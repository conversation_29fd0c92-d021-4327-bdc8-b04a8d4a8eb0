# 新北市違章建築管理系統 - 備份與災難復原解決方案

## 🎯 專案概述

本專案為新北市違章建築管理系統實施了完整的備份與災難復原解決方案，符合政府資訊系統安全規範，達到 RTO (Recovery Time Objective) 4小時、RPO (Recovery Point Objective) 1小時的要求。

### 系統架構
- **主資料庫**：PostgreSQL (localhost:5432/bms) - 37萬筆案件
- **輔助資料庫**：SQL Server (**************:2433/ramsGIS) - GIS資料
- **應用伺服器**：Apache Tomcat 9.0.98
- **網頁應用**：CodeCharge Studio 三層架構

---

## 📁 檔案結構

```
backup_scripts/
├── README.md                          # 本文件
├── postgresql_backup.ps1              # PostgreSQL 備份腳本
├── sqlserver_backup.ps1               # SQL Server 備份腳本
├── application_backup.ps1             # 應用程式備份腳本
├── disaster_recovery.ps1              # 災難復原腳本
├── monitoring_alerting.ps1            # 監控告警腳本
├── recovery_testing.ps1               # 復原測試腳本
├── config/                            # 配置檔案目錄
│   ├── backup_config.json             # 備份配置
│   ├── dr_config.json                 # 災難復原配置
│   └── monitoring_config.json         # 監控配置
├── documentation/                     # 文件目錄
│   ├── BACKUP_DISASTER_RECOVERY_GUIDE.md      # 完整指南
│   ├── EMERGENCY_RESPONSE_PROCEDURES.md       # 緊急應變程序
│   └── RECOVERY_TESTING_SCHEDULE.md           # 復原測試排程
├── modules/                           # PowerShell 模組
│   ├── BackupLogger.psm1              # 日誌模組
│   ├── BackupMetrics.psm1             # 指標模組
│   └── BackupNotification.psm1        # 通知模組
├── logs/                              # 日誌目錄
├── reports/                           # 報告目錄
└── templates/                         # 範本目錄
```

---

## 🚀 快速開始

### 1. 環境準備
```powershell
# 設定執行政策
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 建立必要目錄
New-Item -ItemType Directory -Path "D:\Backups\BMS" -Force
New-Item -ItemType Directory -Path ".\logs" -Force
New-Item -ItemType Directory -Path ".\reports" -Force

# 複製配置檔案
Copy-Item ".\config\backup_config.json.template" ".\config\backup_config.json"
Copy-Item ".\config\dr_config.json.template" ".\config\dr_config.json"
Copy-Item ".\config\monitoring_config.json.template" ".\config\monitoring_config.json"
```

### 2. 配置設定
編輯配置檔案中的資料庫連線資訊、備份路徑、通知設定等。

### 3. 執行備份
```powershell
# PostgreSQL 完整備份
.\postgresql_backup.ps1 -BackupType full -Compress -Verify

# SQL Server 完整備份
.\sqlserver_backup.ps1 -BackupType full -Compress -Verify

# 應用程式完整備份
.\application_backup.ps1 -BackupType full -Compress -StopServices
```

### 4. 監控系統
```powershell
# 啟動監控系統
.\monitoring_alerting.ps1 -Operation monitor -ContinuousMode -IntervalMinutes 5
```

---

## 💾 備份策略

### 備份類型與頻率

#### PostgreSQL 資料庫
- **完整備份**：每日 2:00 AM
- **增量備份**：每6小時 (WAL 歸檔)
- **差異備份**：每12小時

#### SQL Server 資料庫
- **完整備份**：每日 1:00 AM
- **差異備份**：每8小時
- **交易紀錄備份**：每小時

#### 應用程式
- **完整備份**：每週日 00:00
- **增量備份**：每4小時
- **配置備份**：每2小時

### 備份保留政策
| 類型 | 保留期間 | 壓縮 | 跨區域複製 |
|------|----------|------|------------|
| PostgreSQL 完整 | 7天 | ✅ | ✅ |
| PostgreSQL 增量 | 3天 | ✅ | ❌ |
| SQL Server 完整 | 7天 | ✅ | ✅ |
| SQL Server 差異 | 3天 | ✅ | ❌ |
| 應用程式完整 | 14天 | ✅ | ✅ |
| 配置檔案 | 30天 | ✅ | ✅ |

---

## 🚨 災難復原

### 復原目標
- **RTO (Recovery Time Objective)**：4小時
- **RPO (Recovery Point Objective)**：1小時

### 復原程序
```powershell
# 1. 災難評估
.\disaster_recovery.ps1 -Operation assessment

# 2. 執行復原
.\disaster_recovery.ps1 -Operation recovery -RecoveryPoint "2024-07-09_14:00:00"

# 3. 驗證復原
.\disaster_recovery.ps1 -Operation test

# 4. 完成復原
Write-Host "災難復原完成" -ForegroundColor Green
```

### 復原測試
```powershell
# 每日測試
.\recovery_testing.ps1 -TestType database_restore -TestEnvironment test

# 每週測試
.\recovery_testing.ps1 -TestType application_restore -TestEnvironment test

# 每月測試
.\recovery_testing.ps1 -TestType full_recovery -TestEnvironment test
```

---

## 📊 監控與告警

### 監控項目
- 備份作業成功率
- 備份檔案大小變化
- 儲存空間使用量
- 資料庫連線狀態
- 應用服務狀態
- 系統資源使用率

### 告警設定
- **Critical**：備份失敗、服務中斷、磁碟空間不足 < 10%
- **Warning**：備份延遲、磁碟空間不足 < 20%、效能異常
- **Info**：備份完成、測試結果、系統狀態

### 通知方式
- Email 通知
- Slack 通知
- 簡訊通知 (Critical 告警)
- Windows 事件記錄

---

## 🔧 使用說明

### 常用命令

#### 備份操作
```powershell
# PostgreSQL 備份
.\postgresql_backup.ps1 -BackupType full -Compress -Verify -CrossRegionReplicate

# SQL Server 備份
.\sqlserver_backup.ps1 -BackupType differential -Compress -Verify

# 應用程式備份
.\application_backup.ps1 -BackupType incremental -Compress
```

#### 復原操作
```powershell
# 災難評估
.\disaster_recovery.ps1 -Operation assessment

# 執行復原
.\disaster_recovery.ps1 -Operation recovery -RecoveryPoint "2024-07-09_14:00:00"

# 復原測試
.\recovery_testing.ps1 -TestType full_recovery -TestEnvironment test -CleanupAfterTest
```

#### 監控操作
```powershell
# 單次監控
.\monitoring_alerting.ps1 -Operation monitor

# 持續監控
.\monitoring_alerting.ps1 -Operation monitor -ContinuousMode -IntervalMinutes 5

# 產生報告
.\monitoring_alerting.ps1 -Operation report
```

### 腳本參數說明

#### 備份腳本參數
- `-BackupType`: 備份類型 (full, incremental, differential)
- `-Compress`: 壓縮備份檔案
- `-Verify`: 驗證備份完整性
- `-CrossRegionReplicate`: 跨區域複製
- `-StopServices`: 停止服務 (僅應用程式備份)

#### 復原腳本參數
- `-Operation`: 操作類型 (assessment, recovery, failover, test, rollback)
- `-RecoveryPoint`: 復原點時間
- `-TestEnvironment`: 測試環境
- `-DryRun`: 模擬執行
- `-Force`: 強制執行

#### 監控腳本參數
- `-Operation`: 監控操作 (monitor, alert, report, health_check)
- `-ContinuousMode`: 持續監控模式
- `-IntervalMinutes`: 監控間隔 (分鐘)
- `-SendAlerts`: 發送告警

---

## 🔍 故障排除

### 常見問題

#### 備份失敗
```powershell
# 檢查備份日誌
Get-Content ".\logs\postgresql_backup_$(Get-Date -Format 'yyyyMMdd').log" | Select-String "ERROR"

# 檢查磁碟空間
Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "D:" }

# 檢查資料庫連線
Test-NetConnection -ComputerName localhost -Port 5432
```

#### 復原失敗
```powershell
# 檢查復原日誌
Get-Content ".\logs\disaster_recovery_*.log" | Select-String "ERROR"

# 檢查服務狀態
Get-Service "PostgreSQL Database Server 15", "Apache Tomcat 9.0 Tomcat9"

# 檢查復原環境
Test-Path "D:\Recovery\Staging"
```

#### 監控異常
```powershell
# 檢查監控配置
Test-Path ".\config\monitoring_config.json"

# 檢查告警歷史
Get-Content ".\logs\monitoring_*.log" | Select-String "ALERT"

# 重啟監控服務
.\monitoring_alerting.ps1 -Operation monitor -ContinuousMode -IntervalMinutes 5
```

---

## 📋 維護檢查清單

### 每日檢查
- [ ] 檢查備份作業狀態
- [ ] 查看監控告警
- [ ] 確認磁碟空間使用量
- [ ] 檢查系統資源使用率
- [ ] 驗證最新備份檔案

### 每週檢查
- [ ] 執行復原測試
- [ ] 清理過期備份檔案
- [ ] 檢查跨區域複製狀態
- [ ] 更新監控設定
- [ ] 產生週報

### 每月檢查
- [ ] 執行完整災難復原演練
- [ ] 檢查合規性要求
- [ ] 更新緊急聯絡資訊
- [ ] 優化備份策略
- [ ] 產生月報

---

## 🔒 安全考量

### 資料保護
- 備份檔案加密
- 傳輸過程加密
- 存取權限控制
- 敏感資料遮罩

### 存取控制
- 身份驗證
- 授權管理
- 稽核記錄
- 職責分離

### 合規性
- 政府資訊系統安全規範
- 個人資料保護法
- 資訊安全稽核
- 文件保存要求

---

## 📞 支援聯絡

### 緊急聯絡
- **系統管理員**：0912-345-678
- **IT主管**：0923-456-789
- **業務主管**：0934-567-890

### 技術支援
- **資料庫專家**：0945-678-901
- **網路管理員**：0956-789-012
- **應用程式開發**：0967-890-123

---

## 📚 相關文件

### 技術文件
- [系統架構概述](../DOCS/02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫完整指南](../DOCS/02_REFERENCE/DATABASE_COMPLETE_GUIDE.md)
- [業務流程指南](../DOCS/02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md)
- [技術實施指南](../DOCS/02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md)

### 操作手冊
- [備份與災難復原完整指南](./documentation/BACKUP_DISASTER_RECOVERY_GUIDE.md)
- [緊急應變程序手冊](./documentation/EMERGENCY_RESPONSE_PROCEDURES.md)
- [復原測試排程手冊](./documentation/RECOVERY_TESTING_SCHEDULE.md)

---

## 🔄 版本資訊

### 版本 1.0 (2024-07-09)
- 初版發布
- 完整備份與復原功能
- 監控告警系統
- 自動化測試機制
- 完整文件與手冊

### 功能特色
- **全自動化**：備份、復原、監控全程自動化
- **高可靠性**：多重驗證、冗餘保護、錯誤恢復
- **易於管理**：PowerShell 腳本、JSON 配置、Web 介面
- **合規性**：符合政府資訊系統安全規範
- **可擴展性**：模組化設計、支援多種備份目標

---

## 🤝 貢獻指南

### 開發環境
- Windows Server 2019/2022
- PowerShell 5.1+
- PostgreSQL 15
- SQL Server 2019
- Apache Tomcat 9.0.98

### 程式碼規範
- PowerShell 腳本遵循 Microsoft 規範
- JSON 配置檔案使用 UTF-8 編碼
- 函數命名採用 Verb-Noun 格式
- 變數命名使用 camelCase

### 提交流程
1. Fork 專案
2. 建立功能分支
3. 提交變更
4. 測試功能
5. 建立 Pull Request

---

## 📄 授權資訊

本專案為新北市政府違章建築管理系統專用，受到相關法規和政策約束。

**版權所有 © 2024 新北市政府**  
**保留所有權利**

---

**文件版本**：v1.0  
**最後更新**：2024年7月9日  
**文件作者**：系統管理員  
**技術支援**：0912-345-678