# 新北市違章建築管理系統 - API整合規格書

## 一、總體架構

### 1.1 系統定位
本API系統為新北市違章建築管理系統與國土署之間的資料橋接介面，負責將違建案件資料以標準化格式提供給國土署系統。

### 1.2 架構設計原則
- **RESTful設計**：遵循REST架構風格，使用HTTP動詞表達操作意圖
- **JSON格式**：統一使用JSON作為資料交換格式
- **版本控制**：API路徑包含版本號，支援向後相容
- **安全優先**：實施多層次安全機制，確保資料傳輸安全
- **高可用性**：支援批次處理與斷點續傳機制

### 1.3 技術規格
- **協議**：HTTPS (TLS 1.2+)
- **編碼**：UTF-8
- **資料格式**：application/json
- **認證方式**：OAuth 2.0 + API Key
- **時區**：Asia/Taipei (UTC+8)

## 二、API端點規格

### 2.1 基礎URL結構
```
https://api.ntpc.gov.tw/violation/v1
```

### 2.2 核心端點定義

#### 2.2.1 案件查詢端點

##### GET /cases
取得案件列表
```http
GET /violation/v1/cases?status=239&page=1&size=50
Authorization: Bearer {access_token}
X-API-Key: {api_key}
```

**請求參數**
| 參數名稱 | 類型 | 必填 | 說明 | 範例 |
|----------|------|------|------|------|
| status | string | 否 | 案件狀態碼 | 239 |
| case_type | string | 否 | 案件類型(NORMAL/ADVERT/SEWER) | NORMAL |
| start_date | string | 否 | 起始日期(ISO 8601) | 2024-01-01 |
| end_date | string | 否 | 結束日期(ISO 8601) | 2024-12-31 |
| page | integer | 否 | 頁碼(預設1) | 1 |
| size | integer | 否 | 每頁筆數(預設50,最大200) | 50 |

**回應格式**
```json
{
  "status": "success",
  "data": {
    "total": 1234,
    "page": 1,
    "size": 50,
    "items": [
      {
        "case_id": "A113000001",
        "case_no": "113000001",
        "case_type": "NORMAL",
        "current_status": "239",
        "current_stage": "認定已簽准",
        "created_date": "2024-01-15T09:30:00+08:00",
        "updated_date": "2024-03-20T14:25:00+08:00",
        "summary": {
          "address": "新北市板橋區民生路二段123號",
          "violation_type": "頂樓加蓋",
          "area": 45.5,
          "reporter_name": "王小明"
        }
      }
    ]
  },
  "metadata": {
    "request_id": "req_a1b2c3d4e5",
    "timestamp": "2024-03-20T15:30:00+08:00"
  }
}
```

##### GET /cases/{case_no}
取得單一案件詳細資料
```http
GET /violation/v1/cases/113000001
Authorization: Bearer {access_token}
X-API-Key: {api_key}
```

**回應格式**
```json
{
  "status": "success",
  "data": {
    "case_id": "A113000001",
    "case_no": "113000001",
    "case_type": "NORMAL",
    "current_status": "239",
    "current_stage": "認定已簽准",
    "basic_info": {
      "report_date": "2024-01-15",
      "report_source": "民眾檢舉",
      "reporter": {
        "name": "王小明",
        "phone": "0912345678",
        "is_anonymous": false
      },
      "violator": {
        "name": "李大華",
        "id_number": "A12345****",
        "phone": "0987654321",
        "address": "新北市板橋區民生路二段123號"
      }
    },
    "violation_info": {
      "address": "新北市板橋區民生路二段123號",
      "district": "板橋區",
      "location_desc": "頂樓",
      "violation_type": "RC造加蓋",
      "building_type": "住宅",
      "area_occupied": 45.5,
      "height": 3.2,
      "materials": ["鋼筋混凝土", "鐵皮"],
      "usage": "居住使用"
    },
    "inspection_records": [
      {
        "inspection_date": "2024-01-20",
        "inspector": "張巡查",
        "findings": "現場確認有頂樓加蓋情形",
        "photos": [
          {
            "url": "https://api.ntpc.gov.tw/photos/2024/01/20/photo1.jpg",
            "caption": "正面照",
            "taken_date": "2024-01-20T10:30:00+08:00"
          }
        ]
      }
    ],
    "determination_info": {
      "determination_date": "2024-02-15",
      "determination_type": "新違建",
      "legal_basis": "違章建築處理辦法第X條",
      "officer": "陳承辦",
      "approver": "林科長"
    },
    "status_history": [
      {
        "status": "231",
        "stage": "認定辦理中",
        "changed_date": "2024-01-15T09:30:00+08:00",
        "changed_by": "系統"
      },
      {
        "status": "232",
        "stage": "認定陳核中",
        "changed_date": "2024-01-25T14:20:00+08:00",
        "changed_by": "張承辦"
      },
      {
        "status": "239",
        "stage": "認定已簽准",
        "changed_date": "2024-02-15T16:45:00+08:00",
        "changed_by": "林科長"
      }
    ]
  },
  "metadata": {
    "request_id": "req_f6g7h8i9j0",
    "timestamp": "2024-03-20T15:35:00+08:00"
  }
}
```

#### 2.2.2 案件狀態更新端點

##### POST /cases/{case_no}/status
更新案件狀態（國土署回傳處理結果）
```http
POST /violation/v1/cases/113000001/status
Authorization: Bearer {access_token}
X-API-Key: {api_key}
Content-Type: application/json

{
  "new_status": "92c",
  "reason": "資料繕校完成",
  "processed_by": "國土署系統",
  "processed_date": "2024-03-20T16:00:00+08:00",
  "remarks": "資料已成功匯入國土署系統"
}
```

**回應格式**
```json
{
  "status": "success",
  "data": {
    "case_no": "113000001",
    "previous_status": "239",
    "new_status": "92c",
    "updated_date": "2024-03-20T16:00:30+08:00"
  },
  "metadata": {
    "request_id": "req_k1l2m3n4o5",
    "timestamp": "2024-03-20T16:00:30+08:00"
  }
}
```

#### 2.2.3 批次處理端點

##### POST /batch/sync
批次同步案件資料
```http
POST /violation/v1/batch/sync
Authorization: Bearer {access_token}
X-API-Key: {api_key}
Content-Type: application/json

{
  "sync_type": "incremental",
  "filters": {
    "status_in": ["239", "249", "259"],
    "date_range": {
      "start": "2024-03-01",
      "end": "2024-03-20"
    }
  },
  "options": {
    "batch_size": 100,
    "include_photos": true,
    "compress": true
  }
}
```

**回應格式**
```json
{
  "status": "success",
  "data": {
    "batch_id": "batch_20240320_001",
    "total_records": 456,
    "batches": 5,
    "estimated_time": 300,
    "download_url": "https://api.ntpc.gov.tw/batch/download/batch_20240320_001"
  },
  "metadata": {
    "request_id": "req_p6q7r8s9t0",
    "timestamp": "2024-03-20T16:10:00+08:00"
  }
}
```

##### GET /batch/{batch_id}/status
查詢批次處理狀態
```http
GET /violation/v1/batch/batch_20240320_001/status
Authorization: Bearer {access_token}
X-API-Key: {api_key}
```

**回應格式**
```json
{
  "status": "success",
  "data": {
    "batch_id": "batch_20240320_001",
    "status": "processing",
    "progress": {
      "total": 456,
      "processed": 234,
      "success": 230,
      "failed": 4,
      "percentage": 51.3
    },
    "failed_records": [
      {
        "case_no": "113000123",
        "error": "Missing required field: violator.id_number"
      }
    ],
    "estimated_completion": "2024-03-20T16:15:00+08:00"
  },
  "metadata": {
    "request_id": "req_u1v2w3x4y5",
    "timestamp": "2024-03-20T16:12:00+08:00"
  }
}
```

#### 2.2.4 檔案上傳端點

##### POST /files/upload
上傳案件相關檔案（照片、文件）
```http
POST /violation/v1/files/upload
Authorization: Bearer {access_token}
X-API-Key: {api_key}
Content-Type: multipart/form-data

--boundary
Content-Disposition: form-data; name="case_no"

113000001
--boundary
Content-Disposition: form-data; name="file_type"

photo
--boundary
Content-Disposition: form-data; name="file"; filename="photo1.jpg"
Content-Type: image/jpeg

[Binary data]
--boundary--
```

**回應格式**
```json
{
  "status": "success",
  "data": {
    "file_id": "file_a1b2c3d4e5",
    "file_url": "https://api.ntpc.gov.tw/files/file_a1b2c3d4e5",
    "file_type": "photo",
    "file_size": 2457600,
    "mime_type": "image/jpeg",
    "checksum": "sha256:abcdef123456..."
  },
  "metadata": {
    "request_id": "req_z6a7b8c9d0",
    "timestamp": "2024-03-20T16:20:00+08:00"
  }
}
```

## 三、認證與授權機制

### 3.1 OAuth 2.0 認證流程

#### 3.1.1 取得授權碼
```http
GET /oauth/authorize?
  response_type=code&
  client_id={client_id}&
  redirect_uri={redirect_uri}&
  scope=read write&
  state={state}
```

#### 3.1.2 交換Access Token
```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
code={authorization_code}&
client_id={client_id}&
client_secret={client_secret}&
redirect_uri={redirect_uri}
```

**回應格式**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
  "scope": "read write"
}
```

#### 3.1.3 更新Token
```http
POST /oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=refresh_token&
refresh_token={refresh_token}&
client_id={client_id}&
client_secret={client_secret}
```

### 3.2 API Key管理

#### 3.2.1 API Key格式
```
ntpc_live_sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
```

#### 3.2.2 API Key使用方式
- 必須在每個請求的Header中包含：`X-API-Key: {api_key}`
- API Key與OAuth Token同時驗證
- API Key可設定IP白名單限制

### 3.3 權限範圍(Scopes)

| Scope | 說明 | 允許操作 |
|-------|------|----------|
| read | 讀取權限 | GET請求 |
| write | 寫入權限 | POST, PUT, PATCH請求 |
| delete | 刪除權限 | DELETE請求 |
| batch | 批次處理權限 | 批次API存取 |
| admin | 管理權限 | 所有操作 |

## 四、錯誤碼定義與處理策略

### 4.1 HTTP狀態碼

| 狀態碼 | 說明 | 使用場景 |
|--------|------|----------|
| 200 | 成功 | GET請求成功 |
| 201 | 已建立 | POST請求成功建立資源 |
| 204 | 無內容 | DELETE請求成功 |
| 400 | 錯誤的請求 | 參數錯誤、格式錯誤 |
| 401 | 未授權 | Token無效或過期 |
| 403 | 禁止訪問 | 權限不足 |
| 404 | 找不到資源 | 請求的資源不存在 |
| 409 | 衝突 | 資源狀態衝突 |
| 422 | 無法處理 | 請求格式正確但邏輯錯誤 |
| 429 | 請求過多 | 超過速率限制 |
| 500 | 伺服器錯誤 | 系統內部錯誤 |
| 503 | 服務不可用 | 系統維護中 |

### 4.2 錯誤回應格式

```json
{
  "status": "error",
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "參數 'case_no' 格式錯誤",
    "details": {
      "field": "case_no",
      "value": "ABC123",
      "expected": "民國年(3碼) + 流水號(6碼)"
    },
    "suggestion": "請使用正確的案件編號格式，例如：113000001"
  },
  "metadata": {
    "request_id": "req_e1r2r3o4r5",
    "timestamp": "2024-03-20T16:30:00+08:00"
  }
}
```

### 4.3 業務錯誤碼

| 錯誤碼 | 說明 | HTTP狀態碼 | 處理建議 |
|--------|------|------------|----------|
| AUTH_TOKEN_EXPIRED | Token已過期 | 401 | 使用refresh_token更新 |
| AUTH_INVALID_APIKEY | 無效的API Key | 401 | 檢查API Key是否正確 |
| PERMISSION_DENIED | 權限不足 | 403 | 確認帳號權限 |
| CASE_NOT_FOUND | 案件不存在 | 404 | 檢查案件編號 |
| CASE_STATUS_INVALID | 無效的狀態轉換 | 422 | 檢查狀態轉換規則 |
| RATE_LIMIT_EXCEEDED | 超過請求限制 | 429 | 等待後重試 |
| BATCH_SIZE_EXCEEDED | 批次大小超過限制 | 422 | 減少批次大小 |
| FILE_SIZE_EXCEEDED | 檔案大小超過限制 | 422 | 壓縮或分割檔案 |
| INVALID_DATE_RANGE | 無效的日期範圍 | 422 | 調整查詢日期 |
| DUPLICATE_REQUEST | 重複的請求 | 409 | 避免重複提交 |

### 4.4 錯誤處理策略

#### 4.4.1 重試機制
```javascript
const retryConfig = {
  maxRetries: 3,
  retryDelay: 1000, // 毫秒
  retryableErrors: [429, 500, 502, 503, 504],
  backoffMultiplier: 2 // 指數退避
};

async function apiCallWithRetry(request, config = retryConfig) {
  let lastError;
  
  for (let i = 0; i <= config.maxRetries; i++) {
    try {
      const response = await makeRequest(request);
      return response;
    } catch (error) {
      lastError = error;
      
      if (!config.retryableErrors.includes(error.status)) {
        throw error;
      }
      
      if (i < config.maxRetries) {
        const delay = config.retryDelay * Math.pow(config.backoffMultiplier, i);
        await sleep(delay);
      }
    }
  }
  
  throw lastError;
}
```

#### 4.4.2 斷路器模式
```javascript
class CircuitBreaker {
  constructor(threshold = 5, timeout = 60000) {
    this.failureCount = 0;
    this.failureThreshold = threshold;
    this.timeout = timeout;
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.nextAttempt = Date.now();
  }
  
  async call(request) {
    if (this.state === 'OPEN') {
      if (Date.now() < this.nextAttempt) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'HALF_OPEN';
    }
    
    try {
      const response = await makeRequest(request);
      this.onSuccess();
      return response;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  onSuccess() {
    this.failureCount = 0;
    this.state = 'CLOSED';
  }
  
  onFailure() {
    this.failureCount++;
    if (this.failureCount >= this.failureThreshold) {
      this.state = 'OPEN';
      this.nextAttempt = Date.now() + this.timeout;
    }
  }
}
```

## 五、API版本控制策略

### 5.1 版本命名規則
- 使用語意化版本號：`v{major}`
- 目前版本：`v1`
- URL格式：`https://api.ntpc.gov.tw/violation/v1/...`

### 5.2 版本演進策略

#### 5.2.1 向後相容原則
- 新增欄位不影響既有功能
- 不刪除既有欄位，改為標記棄用
- 保持至少兩個主要版本的支援

#### 5.2.2 版本遷移通知
```json
{
  "status": "success",
  "data": { /* ... */ },
  "warnings": [
    {
      "code": "DEPRECATED_VERSION",
      "message": "API v1 將於 2025-12-31 停止支援，請升級至 v2",
      "migration_guide": "https://docs.ntpc.gov.tw/api/migration/v1-to-v2"
    }
  ],
  "metadata": { /* ... */ }
}
```

### 5.3 版本協商機制

#### 5.3.1 Header方式（推薦）
```http
GET /violation/cases/113000001
Accept: application/vnd.ntpc.violation.v1+json
```

#### 5.3.2 URL方式（預設）
```http
GET /violation/v1/cases/113000001
```

## 六、請求頻率限制與流量控制

### 6.1 速率限制規則

| 客戶類型 | 限制規則 | 時間窗口 | 超限處理 |
|----------|----------|----------|----------|
| 標準客戶 | 1000次 | 每小時 | 429錯誤 |
| 進階客戶 | 5000次 | 每小時 | 429錯誤 |
| 企業客戶 | 自訂 | 自訂 | 協商處理 |

### 6.2 限流Header資訊
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 234
X-RateLimit-Reset: 1679385600
X-RateLimit-Reset-After: 3456
```

### 6.3 流量控制實作

#### 6.3.1 令牌桶演算法
```python
import time
from threading import Lock

class TokenBucket:
    def __init__(self, capacity, refill_rate):
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate
        self.last_refill = time.time()
        self.lock = Lock()
    
    def consume(self, tokens=1):
        with self.lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    def _refill(self):
        now = time.time()
        elapsed = now - self.last_refill
        refill_amount = elapsed * self.refill_rate
        
        self.tokens = min(self.capacity, self.tokens + refill_amount)
        self.last_refill = now
```

#### 6.3.2 分散式限流
```python
import redis
import time

class DistributedRateLimiter:
    def __init__(self, redis_client, key_prefix="ratelimit"):
        self.redis = redis_client
        self.key_prefix = key_prefix
    
    def is_allowed(self, identifier, limit, window):
        key = f"{self.key_prefix}:{identifier}"
        current = int(time.time())
        window_start = current - window
        
        pipe = self.redis.pipeline()
        pipe.zremrangebyscore(key, 0, window_start)
        pipe.zadd(key, {str(current): current})
        pipe.zcount(key, window_start, current)
        pipe.expire(key, window + 1)
        
        results = pipe.execute()
        request_count = results[2]
        
        return request_count <= limit
```

## 七、批次處理與分頁機制

### 7.1 分頁參數規範

#### 7.1.1 遊標分頁（推薦用於大資料集）
```http
GET /violation/v1/cases?cursor=eyJpZCI6MTIzNDU2fQ&size=50
```

**回應格式**
```json
{
  "data": {
    "items": [ /* ... */ ],
    "next_cursor": "eyJpZCI6MTIzNTA2fQ",
    "has_more": true
  }
}
```

#### 7.1.2 頁碼分頁（適用於小資料集）
```http
GET /violation/v1/cases?page=2&size=50
```

**回應格式**
```json
{
  "data": {
    "items": [ /* ... */ ],
    "total": 1234,
    "page": 2,
    "size": 50,
    "total_pages": 25
  }
}
```

### 7.2 批次處理規範

#### 7.2.1 批次建立請求
```json
{
  "batch_operations": [
    {
      "operation": "update_status",
      "case_no": "113000001",
      "data": {
        "new_status": "321",
        "reason": "進入排拆階段"
      }
    },
    {
      "operation": "update_status",
      "case_no": "113000002",
      "data": {
        "new_status": "321",
        "reason": "進入排拆階段"
      }
    }
  ],
  "options": {
    "atomic": true,
    "continue_on_error": false
  }
}
```

#### 7.2.2 批次處理限制
- 單次批次最大筆數：1000筆
- 批次處理超時時間：300秒
- 支援斷點續傳機制

### 7.3 資料壓縮

#### 7.3.1 支援的壓縮格式
```http
Accept-Encoding: gzip, deflate, br
Content-Encoding: gzip
```

#### 7.3.2 大量資料下載
```http
GET /violation/v1/export/cases?format=csv&compress=true
Accept: text/csv
Accept-Encoding: gzip
```

## 八、監控與除錯

### 8.1 請求追蹤

#### 8.1.1 追蹤Header
```http
X-Request-ID: req_a1b2c3d4e5
X-Correlation-ID: corr_f6g7h8i9j0
X-Trace-ID: trace_k1l2m3n4o5
```

#### 8.1.2 日誌格式
```json
{
  "timestamp": "2024-03-20T16:40:00+08:00",
  "level": "INFO",
  "request_id": "req_a1b2c3d4e5",
  "method": "GET",
  "path": "/violation/v1/cases/113000001",
  "status": 200,
  "duration_ms": 45,
  "client_ip": "203.145.xxx.xxx",
  "user_agent": "NTPC-Client/1.0"
}
```

### 8.2 健康檢查端點

#### 8.2.1 基本健康檢查
```http
GET /health
```

**回應格式**
```json
{
  "status": "healthy",
  "timestamp": "2024-03-20T16:45:00+08:00",
  "version": "1.2.3",
  "uptime_seconds": 864000
}
```

#### 8.2.2 詳細健康檢查
```http
GET /health/detailed
```

**回應格式**
```json
{
  "status": "healthy",
  "checks": {
    "database": {
      "status": "healthy",
      "response_time_ms": 5
    },
    "cache": {
      "status": "healthy",
      "response_time_ms": 1
    },
    "external_api": {
      "status": "healthy",
      "response_time_ms": 120
    }
  },
  "metrics": {
    "requests_per_minute": 120,
    "average_response_time_ms": 45,
    "error_rate": 0.001
  }
}
```

## 九、安全性考量

### 9.1 資料加密

#### 9.1.1 傳輸層加密
- 強制使用HTTPS (TLS 1.2+)
- 支援的加密套件：
  - TLS_AES_256_GCM_SHA384
  - TLS_CHACHA20_POLY1305_SHA256
  - TLS_AES_128_GCM_SHA256

#### 9.1.2 敏感資料處理
```json
{
  "violator": {
    "name": "李大華",
    "id_number": "A12345****",  // 遮罩處理
    "phone": "0987***321"        // 部分遮罩
  }
}
```

### 9.2 安全Header

```http
Strict-Transport-Security: max-age=31536000; includeSubDomains
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Content-Security-Policy: default-src 'self'
```

### 9.3 IP白名單

#### 9.3.1 設定格式
```json
{
  "allowed_ips": [
    "*************/24",
    "**************",
    "2001:b000::/32"
  ],
  "allowed_domains": [
    "*.nlsc.gov.tw",
    "*.moi.gov.tw"
  ]
}
```

## 十、整合範例

### 10.1 Python整合範例

```python
import requests
import json
from datetime import datetime
import hashlib

class NTPCViolationAPI:
    def __init__(self, base_url, api_key, client_id, client_secret):
        self.base_url = base_url
        self.api_key = api_key
        self.client_id = client_id
        self.client_secret = client_secret
        self.token = None
        self.token_expires = None
    
    def _get_token(self):
        """取得或更新OAuth token"""
        if self.token and self.token_expires > datetime.now():
            return self.token
        
        token_url = f"{self.base_url}/oauth/token"
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'read write'
        }
        
        response = requests.post(token_url, data=data)
        response.raise_for_status()
        
        token_data = response.json()
        self.token = token_data['access_token']
        self.token_expires = datetime.now() + timedelta(seconds=token_data['expires_in'])
        
        return self.token
    
    def _make_request(self, method, endpoint, **kwargs):
        """發送API請求"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {self._get_token()}',
            'X-API-Key': self.api_key,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        response = requests.request(method, url, headers=headers, **kwargs)
        response.raise_for_status()
        
        return response.json()
    
    def get_case(self, case_no):
        """取得單一案件資料"""
        return self._make_request('GET', f'/cases/{case_no}')
    
    def search_cases(self, **filters):
        """搜尋案件"""
        return self._make_request('GET', '/cases', params=filters)
    
    def update_case_status(self, case_no, new_status, reason):
        """更新案件狀態"""
        data = {
            'new_status': new_status,
            'reason': reason,
            'processed_date': datetime.now().isoformat()
        }
        return self._make_request('POST', f'/cases/{case_no}/status', json=data)
    
    def batch_sync(self, status_list, date_range):
        """批次同步案件"""
        data = {
            'sync_type': 'incremental',
            'filters': {
                'status_in': status_list,
                'date_range': date_range
            },
            'options': {
                'batch_size': 100,
                'include_photos': True
            }
        }
        return self._make_request('POST', '/batch/sync', json=data)

# 使用範例
if __name__ == '__main__':
    api = NTPCViolationAPI(
        base_url='https://api.ntpc.gov.tw/violation/v1',
        api_key='ntpc_live_sk_xxx',
        client_id='your_client_id',
        client_secret='your_client_secret'
    )
    
    # 取得單一案件
    case = api.get_case('113000001')
    print(f"案件狀態: {case['data']['current_status']}")
    
    # 搜尋案件
    cases = api.search_cases(
        status='239',
        start_date='2024-03-01',
        end_date='2024-03-20',
        page=1,
        size=50
    )
    print(f"找到 {cases['data']['total']} 筆案件")
    
    # 批次同步
    sync_result = api.batch_sync(
        status_list=['239', '249', '259'],
        date_range={'start': '2024-03-01', 'end': '2024-03-20'}
    )
    print(f"批次ID: {sync_result['data']['batch_id']}")
```

### 10.2 Node.js整合範例

```javascript
const axios = require('axios');
const crypto = require('crypto');

class NTPCViolationAPI {
    constructor(config) {
        this.baseURL = config.baseURL;
        this.apiKey = config.apiKey;
        this.clientId = config.clientId;
        this.clientSecret = config.clientSecret;
        this.token = null;
        this.tokenExpires = null;
    }
    
    async getToken() {
        if (this.token && this.tokenExpires > Date.now()) {
            return this.token;
        }
        
        const tokenResponse = await axios.post(`${this.baseURL}/oauth/token`, {
            grant_type: 'client_credentials',
            client_id: this.clientId,
            client_secret: this.clientSecret,
            scope: 'read write'
        });
        
        this.token = tokenResponse.data.access_token;
        this.tokenExpires = Date.now() + (tokenResponse.data.expires_in * 1000);
        
        return this.token;
    }
    
    async makeRequest(method, endpoint, options = {}) {
        const token = await this.getToken();
        
        const config = {
            method,
            url: `${this.baseURL}${endpoint}`,
            headers: {
                'Authorization': `Bearer ${token}`,
                'X-API-Key': this.apiKey,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            ...options
        };
        
        try {
            const response = await axios(config);
            return response.data;
        } catch (error) {
            if (error.response) {
                throw new Error(`API Error: ${error.response.data.error.message}`);
            }
            throw error;
        }
    }
    
    async getCase(caseNo) {
        return this.makeRequest('GET', `/cases/${caseNo}`);
    }
    
    async searchCases(filters) {
        return this.makeRequest('GET', '/cases', { params: filters });
    }
    
    async updateCaseStatus(caseNo, newStatus, reason) {
        return this.makeRequest('POST', `/cases/${caseNo}/status`, {
            data: {
                new_status: newStatus,
                reason: reason,
                processed_date: new Date().toISOString()
            }
        });
    }
    
    async uploadFile(caseNo, filePath, fileType) {
        const FormData = require('form-data');
        const fs = require('fs');
        
        const form = new FormData();
        form.append('case_no', caseNo);
        form.append('file_type', fileType);
        form.append('file', fs.createReadStream(filePath));
        
        return this.makeRequest('POST', '/files/upload', {
            data: form,
            headers: form.getHeaders()
        });
    }
}

// 使用範例
(async () => {
    const api = new NTPCViolationAPI({
        baseURL: 'https://api.ntpc.gov.tw/violation/v1',
        apiKey: 'ntpc_live_sk_xxx',
        clientId: 'your_client_id',
        clientSecret: 'your_client_secret'
    });
    
    try {
        // 取得案件資料
        const caseData = await api.getCase('113000001');
        console.log('案件狀態:', caseData.data.current_status);
        
        // 搜尋案件
        const searchResult = await api.searchCases({
            status: '239',
            start_date: '2024-03-01',
            end_date: '2024-03-20',
            page: 1,
            size: 50
        });
        console.log(`找到 ${searchResult.data.total} 筆案件`);
        
        // 上傳檔案
        const uploadResult = await api.uploadFile(
            '113000001',
            './photo1.jpg',
            'photo'
        );
        console.log('檔案上傳成功:', uploadResult.data.file_id);
        
    } catch (error) {
        console.error('API錯誤:', error.message);
    }
})();
```

## 十一、測試環境

### 11.1 測試環境資訊
- **Base URL**: `https://api-test.ntpc.gov.tw/violation/v1`
- **測試帳號申請**: https://developer.ntpc.gov.tw/apply
- **API文件**: https://developer.ntpc.gov.tw/docs/violation

### 11.2 測試資料
```json
{
  "test_cases": [
    {
      "case_no": "999000001",
      "description": "測試用一般違建案件",
      "expected_status": "239"
    },
    {
      "case_no": "999000002", 
      "description": "測試用廣告違建案件",
      "expected_status": "349"
    },
    {
      "case_no": "999000003",
      "description": "測試用下水道違建案件",
      "expected_status": "259"
    }
  ]
}
```

## 十二、附錄

### 12.1 狀態碼對照表

| 狀態碼 | 說明 | 業務階段 | 負責單位 |
|--------|------|----------|----------|
| 231 | [一般]認定辦理中 | 認定階段 | 拆除科 |
| 232 | [一般]認定陳核中 | 認定階段 | 拆除科 |
| 234 | [一般]認定送協同作業 | 認定階段 | 拆除科 |
| 239 | [一般]認定已簽准 | 認定階段 | 拆除科 |
| 241 | [廣告物]認定辦理中 | 認定階段 | 廣告科 |
| 249 | [廣告物]認定已簽准 | 認定階段 | 廣告科 |
| 251 | [下水道]認定辦理中 | 認定階段 | 勞安科 |
| 259 | [下水道]認定已簽准 | 認定階段 | 勞安科 |
| 321 | [一般]排拆分案完成 | 排拆階段 | 拆除科 |
| 369 | [一般]排拆已簽准 | 排拆階段 | 拆除科 |
| 342 | [廣告物]認定/排拆陳核中 | 排拆階段 | 廣告科 |
| 349 | [廣告物]認定/排拆已簽准 | 排拆階段 | 廣告科 |
| 440 | [廣告物]結案 | 結案階段 | 廣告科 |
| 450 | [下水道]結案 | 結案階段 | 勞安科 |
| 460 | [一般]結案 | 結案階段 | 拆除科 |
| 92c | 案件資料繕校 | 品質控制 | 系統 |

### 12.2 案件類型對照

| 內部代碼 | API代碼 | 說明 |
|----------|---------|------|
| NORMAL | 01 | 一般違建 |
| ADVERT | 02 | 廣告違建 |
| SEWER | 03 | 下水道違建 |

### 12.3 常見問題

#### Q: 如何處理大量照片上傳？
A: 建議使用以下策略：
1. 壓縮照片至適當大小（建議2MB以內）
2. 使用批次上傳API
3. 實作斷點續傳機制
4. 考慮使用CDN加速

#### Q: API回應時間過長如何處理？
A: 可採取以下措施：
1. 使用分頁減少單次資料量
2. 實作客戶端快取機制
3. 使用非同步處理模式
4. 聯繫技術支援優化查詢

#### Q: 如何確保資料同步的一致性？
A: 系統提供以下機制：
1. 事務性批次處理
2. 冪等性操作設計
3. 資料版本控制
4. 同步狀態追蹤

### 12.4 聯絡資訊

- **技術支援**: <EMAIL>
- **緊急聯絡**: 02-2960-3456 分機 1234
- **開發者社群**: https://developer.ntpc.gov.tw/community
- **問題回報**: https://developer.ntpc.gov.tw/issues

---

**文件版本**: 1.0.0  
**最後更新**: 2024-03-20  
**下次檢視**: 2024-06-20