<%@page pageEncoding="utf-8"%><%@page import="com.ezek.utils.EzekUtils"%>

<%--== Handlers ==--%> <%--im20101_lis Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

private final String CONNECTION_NAME = "DBConn";
private final long MAX_RECORDS_TO_SHOW = 30000L;

private final String[][] SEARCH_FIELDS = {{"", ""}, 
	// More search flag; index 1
	{"moreSearch_state", ""}, 
	// Keyword; index 2
	{"s_keyword", ""}, 
	// 認定通知號碼; index 3-6
	{"s_reg_yy_b", "reg_yy >= 'replace-value'"}, {"s_reg_no_b", "reg_no >= 'replace-value'"}, {"s_reg_yy_e", "reg_yy <= 'replace-value'"}, {"s_reg_no_e", "reg_no <= 'replace-value'"}, 
	// 拆除通知號碼; index 7-10
	{"s_dis_reg_yy_b", "dis_reg_yy >= 'replace-value'"}, {"s_dis_reg_no_b", "dis_reg_no >= 'replace-value'"}, {"s_dis_reg_yy_e", "dis_reg_yy <= 'replace-value'"}, {"s_dis_reg_no_e", "dis_reg_no <= 'replace-value'"}, 
	// 結案通知號碼; index 11-14
	{"s_end_reg_yy_b", "end_reg_yy >= 'replace-value'"}, {"s_end_reg_no_b", "end_reg_no >= 'replace-value'"}, {"s_end_reg_yy_e", "end_reg_yy <= 'replace-value'"}, {"s_end_reg_no_e", "end_reg_no <= 'replace-value'"}, 
	// 違建類型; index 15
	{"s_ib_prcs", "ib_prcs = 'replace-value'"}, 
	// More search parameters
	// 認定承辦姓名; index 16                 拆除優先類組; index 17-18
	{"s_reg_emp", "reg_emp = 'replace-value'"}, {"s_dis_type", "dis_type = 'replace-value'"}, {"s_dis_sort", "dis_sort = 'replace-value'"}, 
	// 拆除方式; index 19
	{"s_b_notice_way", "b_notice_way = 'replace-value'"}, 
	// 違建地點-地址; index 20-30
	{"s_dis_b_addzon", "dis_b_addzon = 'replace-value'"}, {"s_dis_b_add1", "dis_b_add1 LIKE '%replace-value%'"}, {"s_dis_b_add2", "dis_b_add2 LIKE '%replace-value%'"}, {"s_dis_b_add3", "dis_b_add3 LIKE '%replace-value%'"}, {"s_dis_b_add4", "dis_b_add4 LIKE '%replace-value%'"}, {"s_dis_b_add5", "dis_b_add5 = 'replace-value'"}, {"s_dis_b_add6", "dis_b_add6 = 'replace-value'"}, {"s_dis_b_add6_1", "dis_b_add6_1 = 'replace-value'"}, {"s_dis_b_add7", "dis_b_add7 = 'replace-value'"}, {"s_dis_b_add7_1", "dis_b_add7_1 = 'replace-value'"}, {"s_dis_b_add8", "dis_b_add8 = 'replace-value'"}, 
	// 違建地點-地址-其他說明; index 31
	{"s_dis_b_add9", "dis_b_add9 LIKE '%replace-value%'"}, 
	// 違建地點-地號; index 32-35
	{"s_dist", "c.dist = 'replace-value'"}, {"s_section", "c.section = 'replace-value'"}, {"s_road_no1", "c.road_no1 = 'replace-value'"}, {"s_road_no2", "c.road_no2 = 'replace-value'"}, 
	// 認定發文日期; index 36-37
	{"s_reg_date_b", "reg_date >= replace-value"}, {"s_reg_date_e", "reg_date <= replace-value"}, 
	// 拆除發文日期; index 38-39                                                                                            預定拆除日期; index 40-41
	{"s_dis_notice_date_b", "dis_notice_date >= replace-value"}, {"s_dis_notice_date_e", "dis_notice_date <= replace-value"}, {"s_pre_dis_date_b", "pre_dis_date >= replace-value"}, {"s_pre_dis_date_e", "pre_dis_date <= replace-value"}, 
	// 結案發文日期; index 42-43                                                                 拆除完成日期; index 44-45
	{"s_end_date_b", "end_date >= replace-value"}, {"s_end_date_e", "end_date <= replace-value"}, {"s_b_finish_date_b", "b_finish_date >= replace-value"}, {"s_b_finish_date_e", "b_finish_date <= replace-value"}, 
	// 違建高度; index 46-47                                                                                                 違建面積; index 48-49
	{"s_building_height_b", "building_height >= replace-value"}, {"s_building_height_e", "building_height <= replace-value"}, {"s_building_area_b", "building_area >= replace-value"}, {"s_building_area_e", "building_area <= replace-value"}, 
	// 違建類別; index 50                                          違建材料; index 51
	{"s_building_category", "building_category = 'replace-value'"}, {"s_building_kind", "building_kind LIKE '%replace-value%'"}, 
	// 廣告物違建流程; index 52             廣告物型式; index 53
	{"s_ad_typ", "ad_typ = 'replace-value'"}, {"s_ad_kind", "ad_kind = 'replace-value'"}, 
	// 查報單位; index 54                             處理狀態; index 55
	{"s_audnm_code", "audnm_code = 'replace-value'"}, {"s_status", "status = 'replace-value'"}, 
	// 專案名稱; index 56                        舊專案名稱; index 57
	{"s_prj_code", "prj_code = 'replace-value'"}, {"s_prj_code_2", "prj_code = 'replace-value'"}, 
	// 案件來源; index 58
	{"s_case_ori", "case_ori = 'replace-value'"}, 
	// 認定結果; index 59                          公告; index 60                              違建人姓名; index 61
	{"s_reg_rsult", "reg_rsult = 'replace-value'"}, {"s_reg_ann", "reg_ann = 'replace-value'"}, {"s_ib_user", "ib_user LIKE '%replace-value%'"}, 
	// 標案; index 62
	{"s_bid_name", "bid_name = 'replace-value'"}, 
	// 認定單位; index 63                         拆除單位; index 64
	{"s_reg_unit", "reg_unit = 'replace-value'"}, {"s_dis_unit", "dis_unit = 'replace-value'"}, 
	// 勘查紀錄號碼; index 65-66                                                                     建物用途; index 67
	{"s_case_id_b", "a.case_id >= 'replace-value'"}, {"s_case_id_e", "a.case_id <= 'replace-value'"}, {"s_blduse", "blduse LIKE '%replace-value%'"}, 
	// 認定發文登錄日期; index 68-69                                                                                        結案發文登錄日期; index 70-71
	{"s_reg_rec_date_b", "reg_rec_date >= replace-value000000"}, {"s_reg_rec_date_e", "reg_rec_date <= replace-value240000"}, {"s_rsult_rec_time_b", "rsult_rec_time >= replace-value000000"}, {"s_rsult_rec_time_e", "rsult_rec_time <= replace-value240000"}, 
	// 認定登入日期; index 72-73                                                                                                         違規項目; index 74
	{"s_idntfy_rec_time_b", "idntfy_rec_time >= replace-value000000"}, {"s_idntfy_rec_time_e", "idntfy_rec_time <= replace-value240000"}, {"s_ibm_item", "ibm_item = 'replace-value'"}, 
	// 排拆人員; index 75
	{"s_dmltn_emp", "dmltn_emp = 'replace-value'"}, 
	// 結案類型; index 76
	{"s_b_end_item", "(ib_prcs = 'replace-value1' AND b_end_item = 'replace-value2')"},
	//銷案; index  77
	{"is_closed", "is_closed = 'replace-value'"}
};

private String styleDate(String dateStr) {
	String result = "";
	
	if (!StringUtils.isEmpty(dateStr)) {
		result = EzekUtils.formatDate(dateStr, "YYYMMDD", "/");
	}
	
	return result;
}

/**
 * Strip off 鄉、市、鎮、區、村、里、鄰、街、路及「號」之後的字.
 * 
 * @param str: The string to be worked on.
 * 
 * @return: The string without the specific characters.
 **/
private String stripOffSpecificCharacters(String str) {
	String[] SPECIFIC_CHARACTERS = {"鄉", "市", "鎮", "區", "村", "里", "鄰", "街", "路"};
	int specificCharactersLen = SPECIFIC_CHARACTERS.length;
	String result = "";
	
	if (!StringUtils.isEmpty(str)) {
		result = str;
		
		for (int idx = 0; idx < specificCharactersLen; idx++) {
			result = StringUtils.replace(result, SPECIFIC_CHARACTERS[idx], "");
		}
		
		// Process 「號」之後的字
		int posInKeyword = result.indexOf("號");
		if (posInKeyword > -1) {
			result = result.substring(0, posInKeyword);
		}
	}
	
	return result;
}

private boolean isInteger(String str) {
	if (str == null) {
		return false;
	}
	
	int length = str.length();
	if (length == 0) {
		return false;
	}
	
	int i = 0;
	
	if (str.charAt(0) == '-') {
		if (length == 1) {
			return false;
		}
		i = 1;
	}
	
	for (; i < length; i++) {
		char c = str.charAt(i);
		if (c < '0' || c > '9') {
			return false;
		}
	}
	
	return true;
}

private HashMap<String, String> doCustomCode(SessionStorageInterface ssi) {
	// @modifier: Samuel C. Fan
	// @updated: 2021/08/27
	// @issue: 1. 案件關鍵字的地址 use ibmcase.cadd_srch. Yao states:
	//            Need to 忽略鄉、市、鎮、區、村、里、鄰、街、路及「號」之後的字
	//         2. 案件關鍵字的搜尋方式改用精準式; 越多條件越精準.
	// @solution: 1. Alter the code to meet the changes.
	//            2. Rewrite the SQL to meet the changes.
	// 
	
	// The maximum index number always 1 less the array length
	int numOfSearchFields = SEARCH_FIELDS.length - 1;

	
	boolean userKeyinSearch = false;
	
	// 案件關鍵字
	// Step 1: Determine the search type.
	// Step 2: If it is keyword search type, the keywords are parsing by half-character empty space.
	// Step 3: Need to ignore/remove 鄉、市、鎮、區、村、里、鄰、街、路及「號」之後的字.
	// Step 4: Iterate through the search parameters to find if any search in 地號.
	// Step 5: Compose SQL.
	
	boolean keywordSearch = false;
	String[] KEYWORD_FIELDS_ibmcase = {
		// index 0-2
		"reg_yy", "reg_no", "reg_yy || reg_no", 
		// index 3-5
		"dis_reg_yy", "dis_reg_no", "dis_reg_yy || dis_reg_no", 
		// index 6
		"cadd_srch"
	};
	int keywordFieldsLen_ibmcase = KEYWORD_FIELDS_ibmcase.length;
	ArrayList<String> keywordSearchList_ibmcase = new ArrayList<String>();
	
	String[] KEYWORD_FIELDS_ibmcslan = {
		"dist_desc", "section_nm", "road_no1", "road_no2"
	};
	int keywordFieldsLen_ibmcslan = KEYWORD_FIELDS_ibmcslan.length;
	ArrayList<String> keywordSearchList_ibmcslan = new ArrayList<String>();
	
	StringBuffer tmpWhereClause = new StringBuffer();
	String keywordSearchWhereClause_ibmcase = "", keywordSearchWhereClause_ibmcslan = "";
	int idx = 0;
	
	// Step 1 and 2
	String rawKeywords = Utils.convertToString(ssi.getAttribute("SEARCHPARAM_2"));

	if (!StringUtils.isEmpty(rawKeywords)) {
		keywordSearch = true;
		
		String[] keywordArray = rawKeywords.split("\\s+", -1); // Force to split at every character of half-character space
		String keywordWithoutSpecificCharacters = "";
		int startIdx = 0;
		
		for (String keyword : keywordArray) {
			// Step 3: Strip off specific characters
			keywordWithoutSpecificCharacters = stripOffSpecificCharacters(keyword);
			
			if (!StringUtils.isEmpty(keyword) && !StringUtils.isEmpty(keywordWithoutSpecificCharacters)) {
				// When keyword is not a number, then start cadd_srch(idx = 6)
				// Reduce the number of conditions(in where clause) by doing this
				if (!isInteger(keyword)) {
					startIdx = 6;
				}
				
				for (idx = startIdx; idx < keywordFieldsLen_ibmcase; idx++) {
					tmpWhereClause.append(KEYWORD_FIELDS_ibmcase[idx]).append(" LIKE '%").append(keywordWithoutSpecificCharacters).append("%' OR ");
				}
				// Need to remove last 4 characters:" OR "
				keywordSearchList_ibmcase.add("(" + tmpWhereClause.substring(0, tmpWhereClause.length() - 4) + ")");
				
				// Reset for the next usage
				tmpWhereClause.setLength(0);
				
				for (idx = 0; idx < keywordFieldsLen_ibmcslan; idx++) {
					tmpWhereClause.append(KEYWORD_FIELDS_ibmcslan[idx]).append(" LIKE '%").append(keywordWithoutSpecificCharacters).append("%' OR ");
				}
				// Need to remove last 4 characters:" OR "
				keywordSearchList_ibmcslan.add("(" + tmpWhereClause.substring(0, tmpWhereClause.length() - 4) + ")");
				
				// Reset for the next iteration
				tmpWhereClause.setLength(0);
			}
			
			// Reset for the next iteration
			keywordWithoutSpecificCharacters = "";
			startIdx = 0;
		} // End of FOR: (String keyword : keywordArray)
		
		keywordArray = null;
	} // End of IF: (!StringUtils.isEmpty(rawKeywords))

	
	boolean searchInLandNumber = false;
	String ibmcslan_whereStr = "";
	
	String searchParameterValue = "", searchParameterName = "";
	
	// Step 4
	// Search parameter starts from index 3
	for (idx = 3; idx <= numOfSearchFields; idx++) {
		// Get the value from the <SEARCHPARAM_#> session
		searchParameterValue = Utils.convertToString(ssi.getAttribute("SEARCHPARAM_" + idx));
		
		searchParameterName = SEARCH_FIELDS[idx][0];
		
		if (!StringUtils.isEmpty(searchParameterValue) && "s_dist,s_section,s_road_no1,s_road_no2".indexOf(searchParameterName) > -1) {
			searchInLandNumber = true;
			
			if (searchParameterName.equals("s_dist")) {
				ibmcslan_whereStr += "dist = '" + searchParameterValue + "' AND ";
			}
			if (searchParameterName.equals("s_section")) {
				ibmcslan_whereStr += "section = '" + searchParameterValue + "' AND ";
			}
			if (searchParameterName.equals("s_road_no1")) {
				ibmcslan_whereStr += "road_no1 = '" + searchParameterValue + "' AND ";
			}
			if (searchParameterName.equals("s_road_no2")) {
				ibmcslan_whereStr += "road_no2 = '" + searchParameterValue + "' AND ";
			}
		}
	}
	
	// If having search in land number, need to remove last 5 characters:" AND "
	if (searchInLandNumber) {
		ibmcslan_whereStr = ibmcslan_whereStr.substring(0, ibmcslan_whereStr.length() - 5);
	}
	
	// Step 5
	StringBuffer sql = new StringBuffer();
	// For 專案名稱 where clause
	StringBuffer condition_ibmcsprj = new StringBuffer();
	// For 違建人姓名 where clause
	StringBuffer condition_ibmdisnm = new StringBuffer();
	
	sql.append("SELECT reg_yy, reg_no, CASE WHEN reg_yy IS NOT NULL AND reg_no IS NOT NULL THEN reg_yy || reg_no ELSE NULL END AS regnum");
	sql.append(", c.dist AS dist, c.dist_desc AS dist_desc, c.section AS sect, c.section_nm AS section_nm, c.road_no1 AS road_no1, c.road_no2 AS road_no2, CASE WHEN c.road_no1 IS NOT NULL AND c.road_no2 IS NOT NULL THEN c.road_no1 || '－' || c.road_no2 ELSE NULL END AS road_no, (SELECT COUNT(*) FROM ibmcslan WHERE ibmcslan.case_id = a.case_id) AS landnumber_cnt");
	sql.append(", dis_type, dis_sort");
	sql.append(", a.case_id AS case_id");
	sql.append(", a.caddress AS caddress");
	sql.append(", (SELECT string_agg(ibmcode.code_desc, '、' ORDER BY ibmcsprj.prj_code) FROM public.ibmcsprj LEFT JOIN ibmcode ON (ibmcode.code_type = 'PRJNM' AND ibmcode.code_seq = ibmcsprj.prj_code) WHERE ibmcsprj.case_id = a.case_id) AS prj_name");
	sql.append(", pre_dis_date");
	sql.append(", end_reg_yy, end_reg_no, CASE WHEN end_reg_yy IS NOT NULL AND end_reg_no IS NOT NULL THEN end_reg_yy || end_reg_no ELSE NULL END AS end_regnum");
	sql.append(", status, b.code_desc AS status_name");
	sql.append(" FROM ibmcase AS a");
	sql.append(" LEFT JOIN ibmcode AS b ON (b.code_type = 'STA' AND b.code_seq = a.status)");
	sql.append(" LEFT JOIN (SELECT * FROM ibmcslan WHERE land_seq = 1) AS c ON (c.case_id = a.case_id)");
	sql.append(" WHERE 1 = 1");
	if (keywordSearch) {
		int keywordSearchListSize = keywordSearchList_ibmcase.size();
		StringBuffer keywordSearchConditions = new StringBuffer();
		
		for (idx = 0; idx < keywordSearchListSize; idx++) {
			keywordSearchConditions.append("(");
			keywordSearchConditions.append(keywordSearchList_ibmcase.get(idx));
			keywordSearchConditions.append(" OR EXISTS (SELECT 1 FROM ibmcslan WHERE ibmcslan.case_id = a.case_id AND ").append(keywordSearchList_ibmcslan.get(idx)).append(")");
			keywordSearchConditions.append(") AND ");
		}
		
		if (keywordSearchConditions.length() > 0) {
			sql.append(" AND (");
			// Need to remove last 5 characters:" AND "
			sql.append(keywordSearchConditions.substring(0, keywordSearchConditions.length() - 5));
			sql.append(")");
		}
		
		sql.append(" AND is_closed is null");
		
		keywordSearchConditions = null;
		
		userKeyinSearch = true;
	} else {
		if (searchInLandNumber) {
			sql.append(" AND EXISTS (SELECT 1 FROM ibmcslan WHERE ibmcslan.case_id = a.case_id AND ").append(ibmcslan_whereStr).append(")");
			
			userKeyinSearch = true;
		}
		
		// Search parameter starts from index 3
		for (idx = 3; idx <= numOfSearchFields; idx++) {
			searchParameterName = SEARCH_FIELDS[idx][0];
			
			// Skip 地號 search parameters
			if (searchInLandNumber && "s_dist,s_section,s_road_no1,s_road_no2".indexOf(searchParameterName) > -1) {
				continue;
			}
			
			// Get the value from the <SEARCHPARAM_#> session
			searchParameterValue = Utils.convertToString(ssi.getAttribute("SEARCHPARAM_" + idx));

			if (!StringUtils.isEmpty(searchParameterValue)) 
			{
				switch (idx) 
				{
					case 56: // 專案名稱
					case 57: // 舊專案名稱
						condition_ibmcsprj.append(StringUtils.replace(SEARCH_FIELDS[idx][1], "replace-value", searchParameterValue)).append(" OR ");
						break;
					case 61: // 違建人姓名
						condition_ibmdisnm.append(" AND ").append(StringUtils.replace(SEARCH_FIELDS[idx][1], "replace-value", searchParameterValue));
						break;
					case 76: // 結案類型
						String[] searchParameterValueArray = searchParameterValue.split("-");
						String condition_b_end_item = SEARCH_FIELDS[idx][1];
						
						condition_b_end_item = StringUtils.replace(condition_b_end_item, "replace-value1", searchParameterValueArray[0]);
						condition_b_end_item = StringUtils.replace(condition_b_end_item, "replace-value2", searchParameterValueArray[1]);
						
						sql.append(" AND ").append(condition_b_end_item);
						
						searchParameterValueArray = null;
						break;
					case 77:
						if("0".equals(searchParameterValue))
						{
							sql.append(" AND is_closed is null");
						}
						else
						{
							sql.append(" AND ").append(StringUtils.replace(SEARCH_FIELDS[idx][1], "replace-value", searchParameterValue));
						}
						break;
					default:
						sql.append(" AND ").append(StringUtils.replace(SEARCH_FIELDS[idx][1], "replace-value", searchParameterValue));
						break;
				}
				
				userKeyinSearch = true;
			}
		}
	}
	
	// Need to append 專案名稱, 舊專案名稱 search condition if having it
	if (condition_ibmcsprj.length() > 0) {
		sql.append(" AND EXISTS (");
		sql.append("SELECT 1 FROM ibmcsprj WHERE ibmcsprj.case_id = a.case_id AND (").append(condition_ibmcsprj.substring(0, condition_ibmcsprj.lastIndexOf(" OR "))).append(")");
		sql.append(")");
	}
	// Need to append 違建人姓名 search condition if having it
	if (condition_ibmdisnm.length() > 0) {
		sql.append(" AND EXISTS (SELECT 1 FROM ibmdisnm WHERE ibmdisnm.case_id = a.case_id").append(condition_ibmdisnm).append(")");
	}
	
	HashMap<String, String> result = new HashMap<String, String>();
	result.put("sql", sql.toString());
	result.put("userKeyinSearch", (userKeyinSearch ? "YES" : "NO"));
	
	// Release the memory resource immediately
	condition_ibmcsprj = null;
	condition_ibmdisnm = null;
	sql = null;
	
	return result;
}

//Feature checker Head @1-86AA4628
    public class im20101_lisServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//im20101_lis Page Handler Head @1-F9C0B92E
    public class im20101_lisPageHandler implements PageListener {
//End im20101_lis Page Handler Head

//im20101_lis BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End im20101_lis BeforeInitialize Method Head

//im20101_lis BeforeInitialize Method Tail @1-FCB6E20C
        }
//End im20101_lis BeforeInitialize Method Tail

//im20101_lis AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End im20101_lis AfterInitialize Method Head

//Event AfterInitialize Action Validate onTimeout_Synct @362-4B8A4259
          if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
//End Event AfterInitialize Action Validate onTimeout_Synct

//Event AfterInitialize Action Custom Code @49-44795B7A

	// START ==================== set the <PROGRAM_ID> session & clean the <SEARCHPARAM_#> session ==================== START
	String currentProgramId = "im20101";
	
	// Get the program id from the <PROGRAM_ID> session
	String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID"));
	
	// Need to exclude index-0
	int numOfSearchFields = SEARCH_FIELDS.length - 1;
	
	if (!StringUtils.isEmpty(PROGRAM_ID) && !PROGRAM_ID.equals(currentProgramId)) {
		for (int idx = 1; idx <= numOfSearchFields; idx++) {
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + idx, "");
		}
	}
	
	// Write the program id to the <PROGRAM_ID> session
	SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PROGRAM_ID", currentProgramId);
	//  END  ==================== set the <PROGRAM_ID> session & clean the <SEARCHPARAM_#> session ====================  END

//End Event AfterInitialize Action Custom Code

//im20101_lis AfterInitialize Method Tail @1-FCB6E20C
        }
//End im20101_lis AfterInitialize Method Tail

//im20101_lis OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End im20101_lis OnInitializeView Method Head

//im20101_lis OnInitializeView Method Tail @1-FCB6E20C
        }
//End im20101_lis OnInitializeView Method Tail

//im20101_lis BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End im20101_lis BeforeShow Method Head

//im20101_lis BeforeShow Method Tail @1-FCB6E20C
        }
//End im20101_lis BeforeShow Method Tail

//im20101_lis BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End im20101_lis BeforeOutput Method Head

//im20101_lis BeforeOutput Method Tail @1-FCB6E20C
        }
//End im20101_lis BeforeOutput Method Tail

//im20101_lis BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End im20101_lis BeforeUnload Method Head

//im20101_lis BeforeUnload Method Tail @1-FCB6E20C
        }
//End im20101_lis BeforeUnload Method Tail

//im20101_lis onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End im20101_lis onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//im20101_lis onCache Method Tail @1-FCB6E20C
        }
//End im20101_lis onCache Method Tail

//im20101_lis Page Handler Tail @1-FCB6E20C
    }
//End im20101_lis Page Handler Tail

//ibmcase Grid Handler Head @2-C333D668
    public class im20101_lisibmcaseGridHandler implements GridListener, GridDataObjectListener {
//End ibmcase Grid Handler Head

//ibmcase afterInitialize Method Head @2-89E84600
        public void afterInitialize(Event e) {
//End ibmcase afterInitialize Method Head

//ibmcase afterInitialize Method Tail @2-FCB6E20C
        }
//End ibmcase afterInitialize Method Tail

//ibmcase BeforeShow Method Head @2-46046458
        public void beforeShow(Event e) {
//End ibmcase BeforeShow Method Head

//ibmcase BeforeShow Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeShow Method Tail

//ibmcase BeforeShowRow Method Head @2-BDFD38FC
        public void beforeShowRow(Event e) {
//End ibmcase BeforeShowRow Method Head

//Event BeforeShowRow Action Custom Code @233-44795B7A

	// Get the value from the field
	int landnumber_cnt = Utils.convertToLong(e.getGrid().getControl("landnumber_cnt").getValue()).intValue();
	String pre_dis_date = Utils.convertToString(e.getGrid().getControl("pre_dis_date").getValue());
	
	// Set the value to the field
	if (landnumber_cnt > 1) {
		e.getGrid().getControl("landnumber_desc").setValue(" 等 " + landnumber_cnt + " 筆");
	}
	
	e.getGrid().getControl("pre_dis_date").setValue(styleDate(pre_dis_date));
	
	String case_id = Utils.convertToString(e.getGrid().getControl("case_id").getValue());
	
	//案件狀態
	String is_closed = Utils.convertToString(DBTools.dLookUp("is_closed", "ibmcase", " case_id = '"+case_id+"'", CONNECTION_NAME));
	
	String acc_rlt_nm = "";
	if("1".equals(is_closed))
	{
		String type = Utils.convertToString(DBTools.dLookUp("SUBSTRING(code_desc,1,5)", "ibmsts join ibmcode on ibmsts.acc_rlt = ibmcode.code_seq and ibmcode.code_type='RLT'", " case_id = '"+case_id+"'", CONNECTION_NAME));
		acc_rlt_nm = type + "銷案";
	}
	else
	{
		acc_rlt_nm = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmsts join ibmcode on ibmsts.acc_rlt = ibmcode.code_seq and ibmcode.code_type='RLT'", " case_id = '"+case_id+"'", CONNECTION_NAME));
	}
	
	e.getGrid().getControl("status_name").setValue(acc_rlt_nm);

//End Event BeforeShowRow Action Custom Code

//ibmcase BeforeShowRow Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeShowRow Method Tail

//ibmcase BeforeSelect Method Head @2-E5EC9AD3
        public void beforeSelect(Event e) {
//End ibmcase BeforeSelect Method Head

//ibmcase BeforeSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeSelect Method Tail

//ibmcase BeforeBuildSelect Method Head @2-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End ibmcase BeforeBuildSelect Method Head

//ibmcase BeforeBuildSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeBuildSelect Method Tail

//ibmcase BeforeExecuteSelect Method Head @2-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End ibmcase BeforeExecuteSelect Method Head

//Event BeforeExecuteSelect Action Custom Code @277-44795B7A

	HashMap<String, String> customCode = doCustomCode(SessionStorage.getInstance(e.getPage().getRequest()));
	String sql = customCode.get("sql");
	String userKeyinSearch = customCode.get("userKeyinSearch");
	
	if ("YES".equals(userKeyinSearch)) {
		// Filter the count before setting the SQL
		long recordCount = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "(" + sql + ") AS countTbl", "", CONNECTION_NAME)).longValue();
		if (recordCount > MAX_RECORDS_TO_SHOW) {
			// The records are over the limit, do not show any record
			sql += " AND 1 = 2";
		}
	} else {
		// User does not keyin any search parameter, do not show any record
		sql += " AND 1 = 2";
	}
	
	// Execute the new SQL command
	e.getCommand().setSql(sql);

	// Execute the new SQL command to get data count
	e.getCommand().setCountSql("SELECT COUNT(*) FROM (" + sql + ") CNT");
	
	customCode = null;

//End Event BeforeExecuteSelect Action Custom Code

//ibmcase BeforeExecuteSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeExecuteSelect Method Tail

//ibmcase AfterExecuteSelect Method Head @2-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End ibmcase AfterExecuteSelect Method Head

//ibmcase AfterExecuteSelect Method Tail @2-FCB6E20C
        }
//End ibmcase AfterExecuteSelect Method Tail

//ibmcase Grid Handler Tail @2-FCB6E20C
    }
//End ibmcase Grid Handler Tail

//ibmcase_TotalRecords Label Handler Head @4-B5D8C5C7
    public class ibmcaseibmcase_TotalRecordsLabelHandler implements ControlListener {
        public void beforeShow(Event e) {
//End ibmcase_TotalRecords Label Handler Head

//Event BeforeShow Action Retrieve number of records @5-9D59B397
        ((Control) e.getSource()).setValue( ((Grid) e.getParent()).getAmountOfRows());
//End Event BeforeShow Action Retrieve number of records

//ibmcase_TotalRecords Label Handler Tail @4-F5FC18C5
        }
    }
//End ibmcase_TotalRecords Label Handler Tail

//ibmcaseSearch Record Handler Head @26-012C073B
    public class im20101_lisibmcaseSearchRecordHandler implements RecordListener, RecordDataObjectListener {
//End ibmcaseSearch Record Handler Head

//ibmcaseSearch afterInitialize Method Head @26-89E84600
        public void afterInitialize(Event e) {
//End ibmcaseSearch afterInitialize Method Head

//ibmcaseSearch afterInitialize Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch afterInitialize Method Tail

//ibmcaseSearch OnSetDataSource Method Head @26-9B7FBFCF
        public void onSetDataSource(DataObjectEvent e) {
//End ibmcaseSearch OnSetDataSource Method Head

//ibmcaseSearch OnSetDataSource Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch OnSetDataSource Method Tail

//ibmcaseSearch BeforeShow Method Head @26-46046458
        public void beforeShow(Event e) {
//End ibmcaseSearch BeforeShow Method Head

//Event BeforeShow Action Custom Code @55-44795B7A

	String s_regnum_b = "", s_regnum_e = "", s_dis_regnum_b = "", s_dis_regnum_e = "", s_end_regnum_b = "", s_end_regnum_e = "";
	
	// Need to exclude index-0
	int numOfSearchFields = SEARCH_FIELDS.length - 1;
	String searchParameterValue = "";
	
	// Always start from index 1
	for (int idx = 1; idx <= numOfSearchFields; idx++) {
		// Get the value from the <SEARCHPARAM_#> session
		searchParameterValue = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_" + idx));
		
		// Set the value to the field
		if (!StringUtils.isEmpty(searchParameterValue)) {
			e.getRecord().getControl(SEARCH_FIELDS[idx][0]).setValue(searchParameterValue);
			
			// 認定通知號碼
			if (idx == 3 || idx == 4) {
				s_regnum_b += searchParameterValue;
			}
			if (idx == 5 || idx == 6) {
				s_regnum_e += searchParameterValue;
			}
			
			// 拆除通知號碼
			if (idx == 7 || idx == 8) {
				s_dis_regnum_b += searchParameterValue;
			}
			if (idx == 9 || idx == 10) {
				s_dis_regnum_e += searchParameterValue;
			}
			
			// 結案通知號碼
			if (idx == 11 || idx == 12) {
				s_end_regnum_b += searchParameterValue;
			}
			if (idx == 13 || idx == 14) {
				s_end_regnum_e += searchParameterValue;
			}
		}
	}
	
	// 認定號碼
	// Field:s_regnum_b
	e.getRecord().getControl("s_regnum_b").setValue(s_regnum_b);
	// Field:s_regnum_e
	e.getRecord().getControl("s_regnum_e").setValue(s_regnum_e);
	// 拆除時間通知單發文字號
	// Field:s_dis_regnum_b
	e.getRecord().getControl("s_dis_regnum_b").setValue(s_dis_regnum_b);
	// Field:s_dis_regnum_e
	e.getRecord().getControl("s_dis_regnum_e").setValue(s_dis_regnum_e);
	// 結案通知單發文字號
	// Field:s_end_regnum_b
	e.getRecord().getControl("s_end_regnum_b").setValue(s_end_regnum_b);
	// Field:s_end_regnum_e
	e.getRecord().getControl("s_end_regnum_e").setValue(s_end_regnum_e);
	
	// Passin the number to the front end to show the limitation
	e.getRecord().getControl("watch_num").setValue(MAX_RECORDS_TO_SHOW);
	
	String USER_MESSAGE = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserMessage"));
	if (StringUtils.isEmpty(USER_MESSAGE)) {
		e.getRecord().getControl("watchdog").setValue("noSearchParameter");
	} else {
		// Remove <UserMessage> session
		SessionStorage.getInstance(e.getPage().getRequest()).removeAttribute("UserMessage");
		
		if (!"noRecords".equals(USER_MESSAGE)) {
			e.getRecord().getControl("watchdog").setValue(USER_MESSAGE);
		}
	}

//End Event BeforeShow Action Custom Code

//ibmcaseSearch BeforeShow Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeShow Method Tail

//ibmcaseSearch OnValidate Method Head @26-5F430F8E
        public void onValidate(Event e) {
//End ibmcaseSearch OnValidate Method Head

//Event OnValidate Action Custom Code @56-44795B7A

	// Need to exclude index-0
	int numOfSearchFields = SEARCH_FIELDS.length - 1;
	String fieldNameValue = "";
	
	// Always start from index 1
	for (int idx = 1; idx <= numOfSearchFields; idx++) {
		// Get the value from the field
		fieldNameValue = Utils.convertToString(e.getRecord().getControl(SEARCH_FIELDS[idx][0]).getValue());
		
		// Set up the <SEARCHPARAM_#> session
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + idx, fieldNameValue);
	}
	
	// Get the value from the field
	String s_regnum_b = Utils.convertToString(e.getRecord().getControl("s_regnum_b").getValue());
	String s_regnum_e = Utils.convertToString(e.getRecord().getControl("s_regnum_e").getValue());
	String s_dis_regnum_b = Utils.convertToString(e.getRecord().getControl("s_dis_regnum_b").getValue());
	String s_dis_regnum_e = Utils.convertToString(e.getRecord().getControl("s_dis_regnum_e").getValue());
	String s_end_regnum_b = Utils.convertToString(e.getRecord().getControl("s_end_regnum_b").getValue());
	String s_end_regnum_e = Utils.convertToString(e.getRecord().getControl("s_end_regnum_e").getValue());
	String s_case_id_b = Utils.convertToString(e.getRecord().getControl("s_case_id_b").getValue());
	String s_case_id_e = Utils.convertToString(e.getRecord().getControl("s_case_id_e").getValue());
	
	String checkResult = "";
	
	// 認定通知號碼
	if (!StringUtils.isEmpty(s_regnum_b)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_regnum_b").getCaption(), s_regnum_b);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_regnum_b").addError(checkResult);
		}
	}
	if (!StringUtils.isEmpty(s_regnum_e)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_regnum_e").getCaption(), s_regnum_e);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_regnum_e").addError(checkResult);
		}
	}
	// 拆除通知號碼
	if (!StringUtils.isEmpty(s_dis_regnum_b)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_dis_regnum_b").getCaption(), s_dis_regnum_b);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_dis_regnum_b").addError(checkResult);
		}
	}
	if (!StringUtils.isEmpty(s_dis_regnum_e)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_dis_regnum_e").getCaption(), s_dis_regnum_e);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_dis_regnum_e").addError(checkResult);
		}
	}
	// 結案通知號碼
	if (!StringUtils.isEmpty(s_end_regnum_b)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_end_regnum_b").getCaption(), s_end_regnum_b);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_end_regnum_b").addError(checkResult);
		}
	}
	if (!StringUtils.isEmpty(s_end_regnum_e)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_end_regnum_e").getCaption(), s_end_regnum_e);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_end_regnum_e").addError(checkResult);
		}
	}
	// 勘查紀錄號碼
	if (!StringUtils.isEmpty(s_case_id_b)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_case_id_b").getCaption(), s_case_id_b);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_case_id_b").addError(checkResult);
		}
	}
	if (!StringUtils.isEmpty(s_case_id_e)) {
		checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_case_id_e").getCaption(), s_case_id_e);
		if (!StringUtils.isEmpty(checkResult)) {
			e.getRecord().getControl("s_case_id_e").addError(checkResult);
		}
	}
	
	HashMap<String, String> customCode = doCustomCode(SessionStorage.getInstance(e.getPage().getRequest()));
	String sql = customCode.get("sql");
	String userKeyinSearch = customCode.get("userKeyinSearch");	
	
	// Default state
	SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserMessage", "noRecords");
	if ("YES".equals(userKeyinSearch)) {
		// Filter the count before setting the SQL
		long recordCount = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "(" + sql + ") AS countTbl", "", CONNECTION_NAME)).longValue();
		if (recordCount > MAX_RECORDS_TO_SHOW) {
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserMessage", "exceedLimit");
			e.getRecord().addError("符合的案件超過" + EzekUtils.formatNumber("" + MAX_RECORDS_TO_SHOW, 0) + "筆資料，請更明確的輸入要搜尋的條件");
		}
	} else {
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserMessage", "noSearchParameter");
		e.getRecord().addError("目前尚未輸入搜尋條件.");
	}
	
	customCode = null;

//End Event OnValidate Action Custom Code

//ibmcaseSearch OnValidate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch OnValidate Method Tail

//ibmcaseSearch BeforeSelect Method Head @26-E5EC9AD3
        public void beforeSelect(Event e) {
//End ibmcaseSearch BeforeSelect Method Head

//ibmcaseSearch BeforeSelect Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeSelect Method Tail

//ibmcaseSearch BeforeBuildSelect Method Head @26-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End ibmcaseSearch BeforeBuildSelect Method Head

//ibmcaseSearch BeforeBuildSelect Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeBuildSelect Method Tail

//ibmcaseSearch BeforeExecuteSelect Method Head @26-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End ibmcaseSearch BeforeExecuteSelect Method Head

//ibmcaseSearch BeforeExecuteSelect Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeExecuteSelect Method Tail

//ibmcaseSearch AfterExecuteSelect Method Head @26-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End ibmcaseSearch AfterExecuteSelect Method Head

//ibmcaseSearch AfterExecuteSelect Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterExecuteSelect Method Tail

//ibmcaseSearch BeforeInsert Method Head @26-75B62B83
        public void beforeInsert(Event e) {
//End ibmcaseSearch BeforeInsert Method Head

//ibmcaseSearch BeforeInsert Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeInsert Method Tail

//ibmcaseSearch BeforeBuildInsert Method Head @26-FD6471B0
        public void beforeBuildInsert(DataObjectEvent e) {
//End ibmcaseSearch BeforeBuildInsert Method Head

//ibmcaseSearch BeforeBuildInsert Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeBuildInsert Method Tail

//ibmcaseSearch BeforeExecuteInsert Method Head @26-4EB41272
        public void beforeExecuteInsert(DataObjectEvent e) {
//End ibmcaseSearch BeforeExecuteInsert Method Head

//ibmcaseSearch BeforeExecuteInsert Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeExecuteInsert Method Tail

//ibmcaseSearch AfterExecuteInsert Method Head @26-C4572C5E
        public void afterExecuteInsert(DataObjectEvent e) {
//End ibmcaseSearch AfterExecuteInsert Method Head

//ibmcaseSearch AfterExecuteInsert Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterExecuteInsert Method Tail

//ibmcaseSearch AfterInsert Method Head @26-767A9165
        public void afterInsert(Event e) {
//End ibmcaseSearch AfterInsert Method Head

//ibmcaseSearch AfterInsert Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterInsert Method Tail

//ibmcaseSearch BeforeUpdate Method Head @26-33A3CFAC
        public void beforeUpdate(Event e) {
//End ibmcaseSearch BeforeUpdate Method Head

//ibmcaseSearch BeforeUpdate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeUpdate Method Tail

//ibmcaseSearch BeforeBuildUpdate Method Head @26-37688606
        public void beforeBuildUpdate(DataObjectEvent e) {
//End ibmcaseSearch BeforeBuildUpdate Method Head

//ibmcaseSearch BeforeBuildUpdate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeBuildUpdate Method Tail

//ibmcaseSearch BeforeExecuteUpdate Method Head @26-84B8E5C4
        public void beforeExecuteUpdate(DataObjectEvent e) {
//End ibmcaseSearch BeforeExecuteUpdate Method Head

//ibmcaseSearch BeforeExecuteUpdate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeExecuteUpdate Method Tail

//ibmcaseSearch AfterExecuteUpdate Method Head @26-0E5BDBE8
        public void afterExecuteUpdate(DataObjectEvent e) {
//End ibmcaseSearch AfterExecuteUpdate Method Head

//ibmcaseSearch AfterExecuteUpdate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterExecuteUpdate Method Tail

//ibmcaseSearch AfterUpdate Method Head @26-306F754A
        public void afterUpdate(Event e) {
//End ibmcaseSearch AfterUpdate Method Head

//ibmcaseSearch AfterUpdate Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterUpdate Method Tail

//ibmcaseSearch BeforeDelete Method Head @26-752E3118
        public void beforeDelete(Event e) {
//End ibmcaseSearch BeforeDelete Method Head

//ibmcaseSearch BeforeDelete Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeDelete Method Tail

//ibmcaseSearch BeforeBuildDelete Method Head @26-01A46505
        public void beforeBuildDelete(DataObjectEvent e) {
//End ibmcaseSearch BeforeBuildDelete Method Head

//ibmcaseSearch BeforeBuildDelete Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeBuildDelete Method Tail

//ibmcaseSearch BeforeExecuteDelete Method Head @26-B27406C7
        public void beforeExecuteDelete(DataObjectEvent e) {
//End ibmcaseSearch BeforeExecuteDelete Method Head

//ibmcaseSearch BeforeExecuteDelete Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch BeforeExecuteDelete Method Tail

//ibmcaseSearch AfterExecuteDelete Method Head @26-389738EB
        public void afterExecuteDelete(DataObjectEvent e) {
//End ibmcaseSearch AfterExecuteDelete Method Head

//ibmcaseSearch AfterExecuteDelete Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterExecuteDelete Method Tail

//ibmcaseSearch AfterDelete Method Head @26-76E28BFE
        public void afterDelete(Event e) {
//End ibmcaseSearch AfterDelete Method Head

//ibmcaseSearch AfterDelete Method Tail @26-FCB6E20C
        }
//End ibmcaseSearch AfterDelete Method Tail

//ibmcaseSearch Record Handler Tail @26-FCB6E20C
    }
//End ibmcaseSearch Record Handler Tail

//Button_DoSearch Button Handler Head @27-92BA704B
    public class ibmcaseSearchButton_DoSearchButtonHandler implements ButtonListener {
//End Button_DoSearch Button Handler Head

//Button_DoSearch OnClick Method Head @27-A9885EEC
        public void onClick(Event e) {
//End Button_DoSearch OnClick Method Head

//Button_DoSearch OnClick Method Tail @27-FCB6E20C
        }
//End Button_DoSearch OnClick Method Tail

//Button_DoSearch BeforeShow Method Head @27-46046458
        public void beforeShow(Event e) {
//End Button_DoSearch BeforeShow Method Head

//Button_DoSearch BeforeShow Method Tail @27-FCB6E20C
        }
//End Button_DoSearch BeforeShow Method Tail

//Button_DoSearch Button Handler Tail @27-FCB6E20C
    }
//End Button_DoSearch Button Handler Tail

//Button_DoCancel Button Handler Head @51-448B66F6
    public class ibmcaseSearchButton_DoCancelButtonHandler implements ButtonListener {
//End Button_DoCancel Button Handler Head

//Button_DoCancel OnClick Method Head @51-A9885EEC
        public void onClick(Event e) {
//End Button_DoCancel OnClick Method Head

//Event OnClick Action Custom Code @52-44795B7A

	// Need to exclude index-0
	int numOfSearchFields = SEARCH_FIELDS.length - 1;
	
	for (int idx = 1; idx <= numOfSearchFields; idx++) {
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + idx, "");
	}

//End Event OnClick Action Custom Code

//Button_DoCancel OnClick Method Tail @51-FCB6E20C
        }
//End Button_DoCancel OnClick Method Tail

//Button_DoCancel BeforeShow Method Head @51-46046458
        public void beforeShow(Event e) {
//End Button_DoCancel BeforeShow Method Head

//Button_DoCancel BeforeShow Method Tail @51-FCB6E20C
        }
//End Button_DoCancel BeforeShow Method Tail

//Button_DoCancel Button Handler Tail @51-FCB6E20C
    }
//End Button_DoCancel Button Handler Tail

//Button_DoPrint Button Handler Head @187-AC13AA4A
    public class ibmcaseSearchButton_DoPrintButtonHandler implements ButtonListener {
//End Button_DoPrint Button Handler Head

//Button_DoPrint OnClick Method Head @187-A9885EEC
        public void onClick(Event e) {
//End Button_DoPrint OnClick Method Head

//Button_DoPrint OnClick Method Tail @187-FCB6E20C
        }
//End Button_DoPrint OnClick Method Tail

//Button_DoPrint BeforeShow Method Head @187-46046458
        public void beforeShow(Event e) {
//End Button_DoPrint BeforeShow Method Head

//Button_DoPrint BeforeShow Method Tail @187-FCB6E20C
        }
//End Button_DoPrint BeforeShow Method Tail

//Button_DoPrint Button Handler Tail @187-FCB6E20C
    }
//End Button_DoPrint Button Handler Tail

//Button_DoKeywordSearch Button Handler Head @331-5F0D9EFA
    public class ibmcaseSearchButton_DoKeywordSearchButtonHandler implements ButtonListener {
//End Button_DoKeywordSearch Button Handler Head

//Button_DoKeywordSearch OnClick Method Head @331-A9885EEC
        public void onClick(Event e) {
//End Button_DoKeywordSearch OnClick Method Head

//Button_DoKeywordSearch OnClick Method Tail @331-FCB6E20C
        }
//End Button_DoKeywordSearch OnClick Method Tail

//Button_DoKeywordSearch BeforeShow Method Head @331-46046458
        public void beforeShow(Event e) {
//End Button_DoKeywordSearch BeforeShow Method Head

//Button_DoKeywordSearch BeforeShow Method Tail @331-FCB6E20C
        }
//End Button_DoKeywordSearch BeforeShow Method Tail

//Button_DoKeywordSearch Button Handler Tail @331-FCB6E20C
    }
//End Button_DoKeywordSearch Button Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-2BBDC182
    Page im20101_lisModel = (Page)request.getAttribute("im20101_lis_page");
    Page im20101_lisParent = (Page)request.getAttribute("im20101_lisParent");

    if (im20101_lisModel == null) {
        PageController im20101_lisCntr = new PageController(request, response, application, "/im20101_lis.xml" );
        im20101_lisModel = im20101_lisCntr.getPage();
        im20101_lisModel.setRelativePath("./");
        //if (im20101_lisParent != null) {
            //if (!im20101_lisParent.getChild(im20101_lisModel.getName()).isVisible()) return;
        //}
        im20101_lisModel.addPageListener(new im20101_lisPageHandler());
        ((Grid)im20101_lisModel.getChild("ibmcase")).addGridListener(new im20101_lisibmcaseGridHandler());
        ((Label)((Grid)im20101_lisModel.getChild("ibmcase")).getChild("ibmcase_TotalRecords")).addControlListener(new ibmcaseibmcase_TotalRecordsLabelHandler());
        ((Record)im20101_lisModel.getChild("ibmcaseSearch")).addRecordListener(new im20101_lisibmcaseSearchRecordHandler());
        ((Button)((Record)im20101_lisModel.getChild("ibmcaseSearch")).getChild("Button_DoCancel")).addButtonListener(new ibmcaseSearchButton_DoCancelButtonHandler());
        im20101_lisCntr.process();
%>
<%
        if (im20101_lisParent == null) {
            im20101_lisModel.setCookies();
            if (im20101_lisModel.redirect()) return;
        } else {
            im20101_lisModel.redirect();
        }
    }
//End Processing

%>
