# 結案條件分析報告

## Agent並行分析方法說明

本分析採用6個Agent並行處理的系統化方法，每個Agent專責不同的結案分析領域：

### Agent任務分配架構
- **Agent 1**: 結案相關JSP檔案搜尋和分類
- **Agent 2**: 結案狀態碼440/450/460等完整流程分析
- **Agent 3**: 結案條件判定標準和業務規則
- **Agent 4**: 結案審核機制和權限控制
- **Agent 5**: 資料歸檔和保存機制分析
- **Agent 6**: 異常處理和結案撤銷機制

### 並行分析優勢
1. **分析效率**: 多個專業領域同時進行深度分析
2. **專業化分工**: 每個Agent專精特定結案業務域
3. **結果整合**: 各Agent分析結果統一彙整為完整報告

---

## 一、結案相關檔案分類 (Agent 1 分析結果)

### 主要結案檔案清單
```
im40xxx系列 - 結案核心檔案 (44個檔案)
├── im40101系列 - 一般違建排拆通知
├── im40201系列 - 一般違建結案處理 (A/B/C三類)
├── im40301系列 - 結案流程管理
├── im40401系列 - 結案審核機制
├── im40501系列 - 結案特殊處理
├── im40601系列 - 結案權限控制
└── im40701系列 - 結案資料查詢
```

### 關鍵Handler檔案
- `im40101_manHandlers.jsp` - 排拆通知業務邏輯
- `im40201_man_AHandlers.jsp` - 一般違建結案邏輯
- `im40201_man_BHandlers.jsp` - 廣告違建結案邏輯
- `im40201_man_CHandlers.jsp` - 下水道違建結案邏輯
- `im40301_manHandlers.jsp` - 結案流程控制

### 檔案組織架構
每個結案功能模組採用標準三檔案架構：
- `.jsp` - 使用者介面呈現
- `.xml` - 業務規則設定
- `Handlers.jsp` - 核心業務邏輯

---

## 二、結案狀態碼流程分析 (Agent 2 分析結果)

### 結案狀態碼完整映射

#### 辦理中狀態碼
| 狀態碼 | 業務意義 | 違建類型 | 處理階段 |
|--------|----------|----------|----------|
| 441 | 一般違建結案辦理中 | 一般違建 | 結案處理 |
| 451 | 廣告違建結案辦理中 | 廣告違建 | 結案處理 |
| 461 | 下水道違建結案辦理中 | 下水道違建 | 結案處理 |

#### 陳核中狀態碼
| 狀態碼 | 業務意義 | 違建類型 | 處理階段 |
|--------|----------|----------|----------|
| 442 | 一般違建結案陳核中 | 一般違建 | 結案審核 |
| 452 | 廣告違建結案陳核中 | 廣告違建 | 結案審核 |
| 462 | 下水道違建結案陳核中 | 下水道違建 | 結案審核 |
| 447 | 一般違建結案退回承辦 | 一般違建 | 審核退回 |
| 457 | 廣告違建結案退回承辦 | 廣告違建 | 審核退回 |
| 467 | 下水道違建結案退回承辦 | 下水道違建 | 審核退回 |

#### 最終結案狀態碼
| 狀態碼 | 業務意義 | 違建類型 | 完成狀態 |
|--------|----------|----------|----------|
| 440 | 一般違建已結案 | 一般違建 | 案件完結 |
| 450 | 廣告違建已結案 | 廣告違建 | 案件完結 |
| 460 | 下水道違建已結案 | 下水道違建 | 案件完結 |

#### 已簽准狀態碼
| 狀態碼 | 業務意義 | 違建類型 | 特殊狀態 |
|--------|----------|----------|----------|
| 449 | 一般違建已簽准結案 | 一般違建 | 結案核定 |
| 459 | 廣告違建已簽准結案 | 廣告違建 | 結案核定 |
| 469 | 下水道違建已簽准結案 | 下水道違建 | 結案核定 |

#### 其他結案相關狀態碼
| 狀態碼 | 業務意義 | 處理類型 | 備註 |
|--------|----------|----------|------|
| 44d | 一般違建資料繕校 | 品質控制 | 92c機制 |
| 45d | 廣告違建資料繕校 | 品質控制 | 92c機制 |
| 46d | 下水道違建資料繕校 | 品質控制 | 92c機制 |
| 358 | 下水道違建未拆 | 異常處理 | 排拆失敗 |
| 368 | 一般違建未拆 | 異常處理 | 排拆失敗 |
| 348 | 廣告違建未拆 | 異常處理 | 排拆失敗 |
| 23e | 一般違建撤銷認定 | 撤銷處理 | 認定階段撤銷 |
| 24e | 廣告違建撤銷認定 | 撤銷處理 | 認定階段撤銷 |
| 25e | 下水道違建撤銷認定 | 撤銷處理 | 認定階段撤銷 |

### 狀態碼轉換邏輯

#### 正常結案流程
```
排拆完成 → 46x (辦理中) → 46x+1 (陳核中) → 460 (已結案) → 469 (已簽准)
```

#### 存檔機制
根據程式碼分析，結案存檔邏輯如下：
```java
// [存檔] 狀態轉換邏輯
String midChar = current_acc_rlt.substring(1, 2);

if ("6".indexOf(midChar) > -1) { // 拆除科
    acc_rlt = "461"; // 一般違建結案存檔
} else if ("5".indexOf(midChar) > -1) { // 勞安科(下水道)  
    acc_rlt = "451"; // 下水道違建結案存檔
} else if ("4".indexOf(midChar) > -1) { // 廣告科
    acc_rlt = "441"; // 廣告違建結案存檔
}
```

#### 陳核機制
```java
// [陳核] 狀態轉換邏輯 (必須先經過存檔)
if ("APPROVAL".equals(action)) {
    if ("6".indexOf(midChar) > -1) { // 拆除科
        acc_rlt = "462"; // 一般違建結案陳核
    } else if ("5".indexOf(midChar) > -1) { // 勞安科(下水道)
        acc_rlt = "452"; // 下水道違建結案陳核
    } else if ("4".indexOf(midChar) > -1) { // 廣告科
        acc_rlt = "442"; // 廣告違建結案陳核
    }
}
```

---

## 三、結案條件判定標準 (Agent 3 分析結果)

### 一般違建結案條件 (CLOSE_CASE_SITUATIONS)

根據 `im40201_man_AHandlers.jsp` 分析，一般違建有以下7種結案條件：

| 代碼 | 結案條件描述 | 必要資料欄位 | 業務意義 |
|------|-------------|-------------|----------|
| 01 | 依法拆除完畢結案 | b_finish_date | 已完成拆除作業 |
| 02 | 達不堪使用標準結案 | end_chk_date | 自然毀損無需拆除 |
| 03 | 符合處理要點規定結案 | end_chk_date | 符合法規例外條件 |
| 04 | 現場已不存在結案 | end_chk_date | 當事人自行拆除 |
| 05 | 補辦建造執照結案 | licence_yy, licence_word, licence_no | 合法化處理 |
| 06 | 撤銷違建認定結案 | revoke_date, revoke_word, revoke_num | 行政處分撤銷 |
| 07 | 專案解列標準結案 | end_chk_date | 特殊政策處理 |

#### 條件描述範本
```java
private final HashMap<String, String> CLOSE_CASE_SITUATIONS = new HashMap<String, String>() {{
    put("01", "上列違章建築，業經本大隊於[b_finish_date]依法拆除完畢，同意銷案。");
    put("02", "上列違章建築，業經本大隊於[end_chk_date]派員勘查，已達不堪使用標準，同意銷案。");
    put("03", "上列違章建築，業經本大隊於[end_chk_date]派員勘查，已符合「新北市合法建物增設一定規模以下構造物處理要點」之規定，同意銷案。");
    put("04", "上列違章建築，業經本大隊於[end_chk_date]派員勘查，現場已不存在（恢復原狀），同意銷案。");
    put("05", "上列違章建築，已依法申請補辦建造執照手續，並核發[licence_yy || licence_word]字第[licence_no]號建造執照在案，同意銷案。");
    put("06", "上列違章建築，業經本大隊以[revoke_date][revoke_word]字第[revoke_num]號函撤銷違建認定處分，同意銷案。");
    put("07", "上列違章建築，業經本大隊於[end_chk_date]勘查確認符合專案解列標準，全案移拍照建檔列管。");
}};
```

### 廣告違建結案條件

根據 `im40201_man_BHandlers.jsp` 分析，廣告違建有以下5種結案條件：

| 代碼 | 結案條件描述 | 必要資料欄位 | 業務意義 |
|------|-------------|-------------|----------|
| 01 | 依法拆除完畢結案 | b_finish_date | 已完成拆除作業 |
| 02 | 自行改善結案 | b_finish_date, end_way_memo | 業者自行改善 |
| 03 | 現場已不存在結案 | b_finish_date | 當事人自行拆除 |
| 04 | 申請審查許可結案 | licence_yy, licence_word, licence_no, licence_kind | 合法化處理 |
| 05 | 撤銷違建認定結案 | revoke_date, revoke_word, revoke_num | 行政處分撤銷 |

#### 改善類別 (END_WAY_MEMO)
```java
private final HashMap<String, String> END_WAY_MEMO = new HashMap<String, String>() {{
    put("01", "改善至雜項執照規模以下");
    put("02", "自行改善");
    put("03", "標的變異，另行處分");
}};
```

#### 執照類別 (LICENCE_KIND)
```java
private final HashMap<String, String> LICENCE_KIND = new HashMap<String, String>() {{
    put("01", "建造執照");
    put("02", "雜項執照");
    put("03", "許可證");
}};
```

### 下水道違建結案條件

下水道違建結案條件與一般違建相似，但由勞安科負責處理，適用特殊的法規標準。

### 結案條件驗證邏輯

#### 必填欄位驗證
```java
// 排拆通知相關驗證
if (StringUtils.isEmpty(pre_dis_date)) {
    e.getRecord().getControl("pre_dis_date").addError("欄位 " + e.getRecord().getControl("pre_dis_date").getCaption() + " 是必須的.");
}

// 拆除通知單驗證
if (StringUtils.isEmpty(dis_notice_date) || StringUtils.isEmpty(dis_notice_word) || StringUtils.isEmpty(dis_reg_yy) || StringUtils.isEmpty(dis_reg_no)) {
    e.getRecord().addError("欄位 拆除時間通知單 皆必須填寫的.");
}

// 排拆人員驗證
if (StringUtils.isEmpty(dmltn_emp)) {
    e.getRecord().addError("欄位 排拆人員 是必須的.");
}
```

#### 公文字號重複性檢查
```java
long repeatCount_dis_regnum = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "ibmcase", 
    "case_id <> '" + url_case_id + "' AND dis_reg_yy = '" + dis_reg_yy + "' AND dis_reg_no = '" + dis_reg_no + "'", 
    CONNECTION_NAME)).longValue();
if (repeatCount_dis_regnum > 0) {
    e.getRecord().getControl("input_dis_regnum").addError("拆除通知號碼己存在，請重新輸入!!");
}
```

---

## 四、結案審核機制和權限控制 (Agent 4 分析結果)

### 審核權限架構

#### 部門權限對應
| 部門代碼 | 部門名稱 | 違建類型 | 結案權限 |
|----------|----------|----------|----------|
| 021 | 拆除一科 | 一般違建 | 完整結案權限 |
| 022 | 拆除二科 | 一般違建 | 完整結案權限 |
| 031 | 勞安科 | 下水道違建 | 完整結案權限 |
| 041 | 廣告科 | 廣告違建 | 完整結案權限 |

#### 公文字頭對應規則
```java
private String getOfficialDocumentPrefix(String unit_id) {
    String officialDocumentPrefix = "";
    
    if (!StringUtils.isEmpty(unit_id)) {
        if (unit_id.equals("021")) { // 拆除一科
            officialDocumentPrefix = "新北拆拆一";
        } else if (unit_id.equals("022")) { // 拆除二科
            officialDocumentPrefix = "新北拆拆二";
        } else if (unit_id.startsWith("03")) { // 勞安科
            officialDocumentPrefix = "新北拆勞";
        } else if (unit_id.startsWith("04")) { // 廣告科
            officialDocumentPrefix = "新北拆廣";
        }
    }
    
    return officialDocumentPrefix;
}
```

### 審核流程控制

#### 兩階段審核機制
1. **存檔階段**: 承辦人員填寫結案資料並存檔
2. **陳核階段**: 主管審核並核定結案

#### 審核狀態轉換
```java
// 存檔 → 陳核的條件檢查
if ("APPROVAL".equals(action)) {
    // 必須先經過存檔階段
    // 然後進入陳核階段
}
```

#### Session驗證機制
```java
// 登入狀態驗證
if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
    e.getPage().setRedirectString("timeout_err.jsp");

// 使用者資訊擷取
String JOB_TITLE = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));
String USER_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
```

### 權限控制機制

#### 功能權限控制
- 存檔權限：承辦人員可執行
- 陳核權限：主管人員可執行
- 修改權限：依據部門和職等控制

#### 資料修改控制
系統根據當前狀態和使用者權限，動態控制可修改的欄位：
```java
// 狀態特定的欄位顯示控制
String ibmfym_acc_memo_shwsts = "";
if ("364,354,344".indexOf(acc_rlt) > -1) {
    ibmfym_acc_memo_shwsts = "show";
}
```

---

## 五、資料歸檔和保存機制 (Agent 5 分析結果)

### 歸檔表結構

#### 主要歸檔表
1. **ibmsts表** - 案件狀態主檔
2. **ibmfym表** - 案件流程歷程檔
3. **caseopened表** - 案件開啟狀態控制
4. **casesubmitsts表** - 案件提交狀態追蹤

#### 結案時的資料歸檔作業
```java
// 結案歸檔SQL序列
ArrayList<String> sqlCmds = new ArrayList<String>();

// 1. 更新案件狀態
sql = "UPDATE ibmsts SET acc_job = '" + JOB_TITLE + "'";
sql += ", acc_rlt = '" + acc_rlt + "'";
sql += ", acc_date = to_number(to_char(current_date , 'yyyymmdd'), '99999999') - 19110000";
sql += ", acc_time = to_number(to_char(current_timestamp , 'hh24mi'), '9999')";
sql += " WHERE case_id = '" + case_id + "'";
sqlCmds.add(sql);

// 2. 插入流程歷程
sql = "INSERT INTO ibmfym(case_id, acc_job, acc_rlt, acc_date, acc_time, op_user, cr_date, b_notice_emp, b_notice_date)";
sql += " VALUES('" + case_id + "', '" + JOB_TITLE + "', '" + acc_rlt + "', ...)";
sqlCmds.add(sql);

// 3. 清理暫存狀態
String DELETE_OPENED_SQL = " DELETE from public.caseopened WHERE case_id='" + case_id + "' ";
sqlCmds.add(DELETE_OPENED_SQL);

String DELETE_SUBMIT_STS_SQL = " DELETE from public.casesubmitsts WHERE case_id='" + case_id + "' ";
sqlCmds.add(DELETE_SUBMIT_STS_SQL);

// 4. 建立提交狀態紀錄
String INS_SUBMIT_STS_SQL = " INSERT INTO public.casesubmitsts (case_id,acc_rlt) VALUES('" + case_id + "','" + ACC_RLT + "')";
sqlCmds.add(INS_SUBMIT_STS_SQL);
```

### 交易控制機制

#### ACID交易保證
```java
private void executeSqlCmd(ArrayList<String> sqlCmds) {
    DBConnectionManager dbcm = null;
    Connection conn = null;
    PreparedStatement pstmt = null;
    
    try {
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection(CONNECTION_NAME);
        conn.setAutoCommit(false); // 關閉自動提交
        
        for (String sqlCmd : sqlCmds) {
            pstmt = conn.prepareStatement(sqlCmd);
            pstmt.executeUpdate();
            pstmt.close();
        }
        
        conn.commit(); // 全部成功才提交
    } catch (Exception e) {
        // 錯誤時全部回滾
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException sqle) {
                // 處理回滾錯誤
            }
        }
    }
}
```

### 歷程保存策略

#### 完整歷程追蹤
- 每次狀態變更都記錄在ibmfym表
- 保存操作人員、時間、動作類型
- 維護完整的審核軌跡

#### 結案描述文字保存
```java
// 結案類型描述保存到 ibmcase.b_end_item_desc
String b_end_item_desc = composeb_end_item_desc(b_end_item, b_finish_date, end_chk_date, licence_yy, licence_word, licence_no, revoke_date, revoke_word, revoke_num);
b_end_item_desc = StringUtils.isEmpty(b_end_item_desc) ? "NULL" : "'" + b_end_item_desc + "'";

sql = "UPDATE ibmcase SET b_end_item_desc = " + b_end_item_desc;
sql += " WHERE case_id = '" + case_id + "'";
sqlCmds.add(sql);
```

### 備份和復原機制

#### 資料完整性保護
- 結案前完整性檢查
- 關鍵欄位非空驗證
- 外鍵關聯檢查

#### 長期保存規劃
- 定期備份歸檔資料
- 歷史資料壓縮策略
- 法定保存期限管理

---

## 六、異常處理和結案撤銷機制 (Agent 6 分析結果)

### 異常狀況處理

#### 未拆狀態處理
| 狀態碼 | 情況描述 | 處理方式 | 後續動作 |
|--------|----------|----------|----------|
| 368 | 一般違建未拆 | 標記為異常狀態 | 重新安排排拆 |
| 358 | 下水道違建未拆 | 標記為異常狀態 | 重新安排排拆 |
| 348 | 廣告違建未拆 | 標記為異常狀態 | 重新安排排拆 |

#### 退回承辦機制
| 狀態碼 | 情況描述 | 觸發條件 | 處理方式 |
|--------|----------|----------|----------|
| 447 | 一般違建退回承辦 | 主管審核不通過 | 退回重新處理 |
| 457 | 廣告違建退回承辦 | 主管審核不通過 | 退回重新處理 |
| 467 | 下水道違建退回承辦 | 主管審核不通過 | 退回重新處理 |

### 結案撤銷機制

#### 撤銷認定狀態碼
| 狀態碼 | 撤銷類型 | 撤銷階段 | 影響範圍 |
|--------|----------|----------|----------|
| 23e | 一般違建撤銷認定 | 認定階段 | 全案撤銷 |
| 24e | 廣告違建撤銷認定 | 認定階段 | 全案撤銷 |
| 25e | 下水道違建撤銷認定 | 認定階段 | 全案撤銷 |

#### 撤銷處理邏輯
```java
// 撤銷認定的結案條件 (代碼06)
put("06", "上列違章建築，業經本大隊以[revoke_date][revoke_word]字第[revoke_num]號函撤銷違建認定處分，同意銷案。");

// 必要資料檢查
if (!StringUtils.isEmpty(revoke_date) && !StringUtils.isEmpty(revoke_word) && !StringUtils.isEmpty(revoke_num)) {
    // 處理撤銷相關資料
}
```

### 品質控制機制 (92c機制)

#### 資料繕校狀態碼
| 狀態碼 | 控制類型 | 觸發條件 | 處理方式 |
|--------|----------|----------|----------|
| 44d | 一般違建資料繕校 | 資料品質檢查 | 暫停處理進行校正 |
| 45d | 廣告違建資料繕校 | 資料品質檢查 | 暫停處理進行校正 |
| 46d | 下水道違建資料繕校 | 資料品質檢查 | 暫停處理進行校正 |

### 重新開案機制

#### 開案條件評估
- 撤銷認定後可重新開案
- 未拆案件可重新安排
- 退回案件修正後可重新陳核

#### 狀態復原邏輯
```java
// caseopened 和 casesubmitsts 表的狀態管理
String DELETE_OPENED_SQL = " DELETE from public.caseopened WHERE case_id='" + case_id + "' ";
String DELETE_SUBMIT_STS_SQL = " DELETE from public.casesubmitsts WHERE case_id='" + case_id + "' ";

// 重新建立狀態追蹤
String INS_SUBMIT_STS_SQL = " INSERT INTO public.casesubmitsts (case_id,acc_rlt) VALUES('" + case_id + "','" + ACC_RLT + "')";
```

### 錯誤恢復機制

#### 交易回滾保護
- 所有結案操作都有完整的回滾機制
- 錯誤發生時自動恢復到操作前狀態
- 保持資料一致性

#### 系統異常處理
```java
} catch (Exception e) {
    System.err.println("im40201_man_AHandlers ::: executeSqlCmd ::: Exception");
    e.printStackTrace();
    
    // 回滾所有變更
    if (conn != null) {
        try {
            conn.rollback();
        } catch (SQLException sqle) {
            System.err.println("im40201_man_AHandlers ::: executeSqlCmd ::: SQLException");
            sqle.printStackTrace();
        }
    }
}
```

---

## 七、結案條件完整矩陣

### 結案條件總覽表

| 違建類型 | 結案條件代碼 | 條件描述 | 必要欄位 | 狀態碼路徑 |
|----------|-------------|----------|----------|------------|
| 一般違建 | 01 | 依法拆除完畢 | b_finish_date | 461→462→440→449 |
| 一般違建 | 02 | 達不堪使用標準 | end_chk_date | 461→462→440→449 |
| 一般違建 | 03 | 符合處理要點規定 | end_chk_date | 461→462→440→449 |
| 一般違建 | 04 | 現場已不存在 | end_chk_date | 461→462→440→449 |
| 一般違建 | 05 | 補辦建造執照 | licence_yy, licence_word, licence_no | 461→462→440→449 |
| 一般違建 | 06 | 撤銷違建認定 | revoke_date, revoke_word, revoke_num | 461→462→440→449 |
| 一般違建 | 07 | 專案解列標準 | end_chk_date | 461→462→440→449 |
| 廣告違建 | 01 | 依法拆除完畢 | b_finish_date | 451→452→450→459 |
| 廣告違建 | 02 | 自行改善 | b_finish_date, end_way_memo | 451→452→450→459 |
| 廣告違建 | 03 | 現場已不存在 | b_finish_date | 451→452→450→459 |
| 廣告違建 | 04 | 申請審查許可 | licence_yy, licence_word, licence_no, licence_kind | 451→452→450→459 |
| 廣告違建 | 05 | 撤銷違建認定 | revoke_date, revoke_word, revoke_num | 451→452→450→459 |
| 下水道違建 | 01-07 | 同一般違建 | 對應欄位 | 441→442→460→469 |

### 異常處理矩陣

| 異常類型 | 狀態碼 | 處理方式 | 恢復路徑 |
|----------|--------|----------|----------|
| 審核退回 | 447/457/467 | 修正後重新陳核 | 回到4x1狀態 |
| 未拆處理 | 348/358/368 | 重新安排排拆 | 回到3x2狀態 |
| 撤銷認定 | 23e/24e/25e | 全案撤銷重新開始 | 回到初始狀態 |
| 資料繕校 | 44d/45d/46d | 品質校正 | 依92c機制處理 |

---

## 八、總結與建議

### 結案機制特點

1. **完整的三階段流程**: 辦理中 → 陳核中 → 已結案 → 已簽准
2. **細緻的權限控制**: 按部門和職等分級管理
3. **嚴格的資料驗證**: 多層次的欄位檢查機制
4. **完整的異常處理**: 涵蓋各種特殊狀況
5. **可靠的交易保護**: ACID特性確保資料一致性

### 系統優勢

1. **業務完整性**: 涵蓋所有結案情境
2. **資料追蹤性**: 完整的歷程記錄
3. **權限安全性**: 細緻的權限控制
4. **異常處理性**: 完善的錯誤恢復機制

### 改進建議

1. **現代化介面**: 提升使用者體驗
2. **自動化流程**: 減少人工作業
3. **即時通知**: 增加狀態變更通知
4. **行動支援**: 支援行動裝置操作
5. **API介面**: 提供外部系統整合能力

### 技術架構建議

1. **微服務改造**: 將結案邏輯獨立為服務
2. **工作流引擎**: 使用標準工作流程引擎
3. **事件驅動**: 採用事件驅動架構
4. **容器化部署**: 提升部署和維護效率

---

*本報告由【C】Claude Code 業務分析任務組完成*  
*分析時間: 2025年7月5日*  
*Agent並行分析架構: 6個專業Agent同步執行*  
*分析範圍: 新北市違章建築管理系統結案業務完整流程*