# 排拆分案機制分析 (Demolition Case Assignment Mechanism Analysis)

## 概述 (Overview)

本文件詳細分析新北市違章建築管理系統中的排拆分案機制，包含案件從認定完成到排拆分案的完整流程。

## 核心功能位置 (Core Function Location)

### 主要程式檔案
- **主頁面**: `im40301_lis.jsp` - 拆除案件分案作業
- **處理邏輯**: `im40301_lisHandlers.jsp` - 分案業務邏輯
- **配置檔案**: `im40301_lis.xml` - 頁面配置定義

## 狀態碼流程 (Status Code Flow)

### 認定到排拆的狀態轉換
```
認定完成 (239/249/259) → 排拆分案 (321) → 排拆處理 (36x系列)
```

### 關鍵狀態碼
- **239**: 一般認定已簽准
- **249**: 廣告物認定已簽准  
- **259**: 下水道認定已簽准
- **321**: 一般排拆分案完成
- **368**: 一般未拆原因已登錄（待釐清疑義案件回拆除科後）

## 分案作業流程 (Assignment Process Flow)

### 1. 案件篩選條件
```sql
-- im40301_lisHandlers.jsp 第761-765行
newWhereStr += " AND (";
newWhereStr += " (acc_rlt = '239' AND reg_yy IS NOT NULL AND reg_no IS NOT NULL AND reg_date IS NOT NULL)";
newWhereStr += " OR acc_rlt = '321'";
newWhereStr += " OR (acc_rlt = '368' and exists (select * from ibmfym where acc_rlt = '36a' and ibmfym.case_id = a.case_id ))";
newWhereStr += ")";
```

符合以下條件的案件會顯示在分案清單：
1. 認定已簽准（239）且有完整認定通知資料
2. 已經分案過（321）但需要調整
3. 待釐清疑義案件（368）且已處理完畢（36a）

### 2. 分案單位選項

系統提供三個分案選項：
- **拆除一科** (021)
- **拆除二科** (022)
- **暫不分派** (dis_schdl = 'N')

### 3. 分案操作邏輯

#### 前端互動 (Frontend Interaction)
```javascript
// im40301_lis.jsp 第269-302行
function handleCheckboxes(pickId) {
  switch (pickId) {
    case "pick021":  // 全選拆除一科
      $(".dis_unit_hide").val("021");
      $(".class-dis_schdl").val("N");
      break;
    case "pick022":  // 全選拆除二科
      $(".dis_unit_hide").val("022");
      $(".class-dis_schdl").val("N");
      break;
    case "pick":     // 全選暫不分派
      $(".class-dis_schdl").val("Y");
      $(".dis_unit_hide").val("");
      break;
  }
}
```

#### 後端處理 (Backend Processing)
```java
// im40301_lisHandlers.jsp 第895-937行
if (!StringUtils.isEmpty(dis_unit_hide)) {
    String acc_rlt = "321";  // 設定狀態為排拆分案完成
    
    // 插入狀態歷程
    sql = "INSERT INTO ibmfym(case_id, acc_job, acc_rlt, acc_date, acc_time, op_user, cr_date)"; 
    sql += " VALUES('" + case_id + "', '" + JOB_TITLE + "', '" + acc_rlt + "', ...)";
    
    // 更新案件狀態
    sql = "UPDATE ibmsts SET acc_job = '" + JOB_TITLE + "'";
    sql += ", acc_rlt = '" + acc_rlt + "'";
    sql += ", acc_date = ...";
    sql += " WHERE case_id = '" + case_id + "'";
}
```

## 資料表結構 (Database Structure)

### ibmcase 表（案件主表）
- `case_id`: 案件編號
- `dis_unit`: 分派單位（021/022）
- `dis_schdl`: 是否排拆（Y/N，預設Y）
- `reg_yy`: 認定年度
- `reg_no`: 認定號碼
- `reg_date`: 認定日期

### ibmsts 表（案件狀態表）
- `case_id`: 案件編號
- `acc_rlt`: 目前狀態碼
- `acc_job`: 處理職稱
- `acc_date`: 處理日期
- `acc_time`: 處理時間

### ibmfym 表（狀態歷程表）
- `case_id`: 案件編號
- `acc_seq`: 序號
- `acc_rlt`: 狀態碼
- `acc_job`: 處理職稱
- `acc_date`: 處理日期
- `op_user`: 操作人員

## 業務規則 (Business Rules)

### 1. 分案權限控制
```java
// im40301_lisHandlers.jsp 第688-699行
// 已分案的案件僅能由原分案單位更改
if( !StringUtils.isEmpty(dis_unit_db) && !StringUtils.isEmpty(UNIT_ID) && 
    !UNIT_ID.equals(dis_unit_db) && !dis_unit_db.equals(dis_unit_hide) ) {
    if ("021".equals(dis_unit_db)) {
        e.getEditableGrid().getControl("dis_unit_db").addError(
            "本案件已分案至「拆除一科」，如需異動請由拆除一科人員將本案設定為「暫不分派」後再由本科人員分案。"
        );
    }
}
```

### 2. 案件分派狀態篩選
- **待分派**: `dis_unit IS NULL AND COALESCE(dis_schdl, 'Y') = 'Y'`
- **暫不分派**: `COALESCE(dis_schdl, 'Y') = 'N'`
- **全部案件**: 不加條件

### 3. 專案名稱顯示
系統會自動聚合顯示案件相關的所有專案名稱：
```sql
-- im40301_lisHandlers.jsp 第746行
(SELECT string_agg(ibmcode.code_desc, '、' ORDER BY ibmcsprj.prj_code) 
 FROM ibmcsprj 
 LEFT JOIN ibmcode ON (ibmcode.code_type = 'PRJNM' AND ibmcode.code_seq = ibmcsprj.prj_code) 
 WHERE ibmcsprj.case_id = a.case_id) AS prj_name
```

## 特殊功能 (Special Features)

### 1. 退回認定功能
- 提供「退回認定」按鈕，可將案件退回認定階段
- 開啟彈出視窗 `im40301_man.jsp` 進行退回操作

### 2. 案件明細查看
- 點擊認定通知號碼可查看案件完整明細
- 使用 `im_showCaseDetailInPopup()` 函數顯示

### 3. 批次操作
- 支援「全選」功能快速分派多個案件
- 分案時顯示處理中遮罩，避免重複操作

## 系統改進建議 (Improvement Suggestions)

### 1. 安全性增強
- 加入 CSRF Token 防護
- 強化 SQL Injection 防護（使用參數化查詢）

### 2. 使用者體驗
- 加入分案預覽功能
- 提供分案歷程查詢
- 增加批次分案確認對話框

### 3. 效能優化
- 使用分頁減少大量資料載入
- 優化專案名稱聚合查詢

### 4. 業務邏輯
- 考慮加入自動分案規則（依地區、類型等）
- 提供分案負載平衡建議

## 結論 (Conclusion)

排拆分案機制是違建管理系統中的關鍵環節，連接認定階段與排拆執行階段。系統設計考慮了多單位協作、權限控制、狀態追蹤等需求，但在安全性和使用者體驗方面仍有改進空間。