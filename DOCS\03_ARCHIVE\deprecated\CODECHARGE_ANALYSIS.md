# CodeCharge 專案分析報告

本文檔旨在分析現有的 CodeCharge Studio 專案，為後續遷移至原生 JSP/Servlet 架構提供基礎。

---

## 1. 頁面與元件盤點

此部分列出專案中所有的 CodeCharge 頁面模型 (`.xml`) 及其對應的 JSP 檔案 (`.jsp`)，並識別共用元件。

### 1.1 頁面對應表

| PageModel (.xml)  | 對應 JSP (.jsp)     | 描述/主要功能        |
| :---------------- | :------------------ | :------------------- |
| `im20901_lis.xml` | `im20901_lis.jsp`   | 案件列表與搜尋       |
| `im20901_man.xml` | `im20901_man.jsp`   | 案件新增/編輯      |
| `_layout.xml`     | (N/A - Included)  | 共用頁面佈局         |
| `login.xml`       | `login.jsp`         | 使用者登入頁面       |
| `header.xml`      | `header.jsp`        | 共用頁首 (若有獨立模型) |
| `footer.xml`      | `footer.jsp`        | 共用頁尾 (若有獨立模型) |
| *(請繼續列出所有其他頁面)* |                    |                      |

### 1.2 共用元件識別

*   **頁面佈局:**
    *   `_layout.xml`: 定義了主要的頁首 (`<Header>`)、內容 (`<Content>`)、頁尾 (`<Footer>`) 結構，透過 `<Include>` 或 `<IncludePage>` 引入共用 JSP 或子頁面內容。
    *   *其他可能的共用佈局或框架頁面...*
*   **頁首/頁尾/導覽列:**
    *   `header.jsp` (由 `_layout.xml` 引入): 可能包含 Logo、網站標題、使用者資訊、主選單等。
    *   `footer.jsp` (由 `_layout.xml` 引入): 可能包含版權資訊、相關連結等。
    *   *其他共用的導覽元件或選單...*
*   **其他共用 UI 片段:**
    *   *是否有共用的搜尋表單區塊？*
    *   *是否有共用的資料顯示模板？*
    *   *(請識別其他在多個頁面重複使用的 UI 結構或元件)*

---

## 2. 資料存取分析

檢視 `.xml` 檔案，釐清資料來源、查詢邏輯與資料庫連接。

### 2.1 資料庫連線

*   **主要連線:** `DBConn` (在 `im20901_lis.xml` 中使用)
    *   *連線設定細節 (通常在 CodeCharge 的設定檔或 Web Server 的 Datasource 設定中): JDBC URL, Driver Class, User, Password 等。*
*   *其他可能的資料庫連線...*

### 2.2 頁面資料查詢

| PageModel (.xml)  | 元件 (Grid/Record) | 資料標籤 (`<Select>`, `<CountSql>`) | 主要查詢表格           | 查詢描述/用途                     |
| :---------------- | :----------------- | :---------------------------------- | :--------------------- | :------------------------------ |
| `im20901_lis.xml` | `BMSDISOBEY_DIST`  | `<Select>` (查詢列表)             | `ibmlawfee`, `ibmcase` | 查詢案件列表資料                |
| `im20901_lis.xml` | `BMSDISOBEY_DIST`  | `<CountSql>` (計算總數)           | `ibmlawfee`, `ibmcase` | 計算符合條件的案件總筆數          |
| `im20901_lis.xml` | `BMSDISOBEY_DISTSearch` | `<ListBox name="s_edit_dis_b_addzon"> <Select>` | `ibmcode`              | 查詢區域代碼 (for search dropdown) |
| `im20901_lis.xml` | `BMSDISOBEY_DISTSearch` | `<ListBox name="s_payment_type"> <Select>`    | `ibmcode`              | 查詢繳款情形代碼 (for search dropdown) |
| `im20901_man.xml` | *(Record Name)*    | `<Select>` (查詢單筆資料)         | *(Tables)*             | 載入單一案件詳細資料供編輯        |
| `im20901_man.xml` | *(Record Name)*    | *(Insert/Update/Delete SQL - 隱含)* | *(Tables)*             | 處理案件的新增、修改、刪除操作    |
| *(請繼續分析其他頁面)* |                    |                                     |                        |                                 |

**註:** CodeCharge 的 Insert/Update/Delete 操作通常是基於 `<Record>` 元件的設定自動生成，不一定會有明確的 `<InsertSql>` 等標籤，需要檢查 Record 的屬性。

---

## 3. UI 元件映射

將 CodeCharge 元件對應到標準 Web 技術。參考 `COMPONENTS_USAGE.md`。

| CodeCharge 元件      | 標準 HTML/JS 對應                                       | 說明/注意事項                                                                 |
| :------------------- | :------------------------------------------------------ | :---------------------------------------------------------------------------- |
| `<Page>`             | JSP 檔案, HTML `<html>`, `<body>` 結構                 | 頁面容器                                                                      |
| `<Grid>`             | HTML `<table>`, JSTL `<c:forEach>`                      | 資料表格顯示，需手動迭代資料                                                    |
| `<Row>`              | HTML `<tr>`, `<td>`                                     | 表格列與儲存格                                                                |
| `<Label>`            | HTML `<span>`, JSTL/EL (`${bean.property}`)             | 顯示唯讀文字                                                                  |
| `<TextBox>`          | HTML `<input type="text">`                              | 文字輸入欄位                                                                  |
| `<Hidden>`           | HTML `<input type="hidden">`                            | 隱藏欄位                                                                      |
| `<Button>`           | HTML `<button>` 或 `<input type="submit/button">`       | 操作按鈕，需綁定 Form 提交或 JavaScript 事件                                  |
| `<Link>`             | HTML `<a>`                                              | 超連結                                                                        |
| `<ListBox>`          | HTML `<select>`, `<option>` (可能需 JSTL 迭代產生)     | 下拉選單                                                                      |
| `<DatePicker>`       | HTML `<input type="text">` + JavaScript DatePicker Library | 需要引入 JS 函式庫 (如 jQuery UI Datepicker, bootstrap-datepicker, flatpickr) |
| `<Navigator>`        | 自訂分頁 HTML + Servlet/DAO 邏輯                        | 需要後端邏輯配合計算頁數、處理分頁請求                                          |
| `<Sorter>`           | HTML `<a>` (在表頭) + Servlet/DAO 排序邏輯              | 點擊表頭觸發排序，需後端邏輯修改 SQL `ORDER BY` 子句                             |
| `<Record>`           | HTML `<form>`                                           | 資料編輯/搜尋表單容器                                                         |
| `<Include>`          | JSP `<%@ include file="..." %>` 或 `<jsp:include page="..."/>` | 引入其他 JSP 檔案                                                               |
| `<IncludePage>`      | 類似 `<jsp:include>`，用於佈局中的內容區塊               | 引入子頁面內容                                                                |
| *(其他元件...)*     |                                                         |                                                                               |

---

## 4. 業務邏輯定位

找出處理請求、驗證資料、執行操作的程式碼。

*   **事件處理器 (CodeCharge Events):**
    *   檢查 `.xml` 檔案中的 `<Grid>`, `<Record>`, `<Button>` 等元件是否有定義 `OnInitialize`, `OnBeforeShow`, `OnClick`, `OnValidate`, `BeforeInsert`, `AfterUpdate` 等事件。
    *   這些事件對應的 Java 程式碼通常位於 CodeCharge 自動產生的 `.java` 檔案中 (可能與頁面同名或有特定命名規則)。
    *   **範例:** `im20901_lis.java` (如果存在) 可能包含 `BMSDISOBEY_DIST_BeforeShowRow` 事件，用於在顯示每一列之前處理資料。
    *   *(列出找到的關鍵事件及其對應的 Java 方法)*
*   **JSP 中的 Scriptlets:**
    *   檢查 `.jsp` 檔案是否包含 `<% ... %>` 或 `<%= ... %>` Java 程式碼片段。
    *   雖然不推薦，但舊專案可能直接在 JSP 中嵌入業務邏輯或資料處理程式碼。
    *   *(記錄在哪些 JSP 檔案中發現了 Scriptlets 及其用途)*
*   **獨立的 Java 類別:**
    *   專案中是否有名稱看起來像 Service 或 Helper 的自訂 Java 類別被事件處理器或 JSP 呼叫？
    *   *(列出這些輔助類別及其功能)*

---

## 5. 共用功能分析

識別專案範圍的共用機制。

*   **身份驗證 (Authentication):**
    *   登入頁面 (`login.xml`/`login.jsp`) 如何驗證使用者？
    *   是透過 CodeCharge 內建的 Security 功能，還是自訂的事件程式碼？
    *   驗證成功後，使用者資訊 (如 User ID, Roles) 如何儲存？ (通常是 Session)
*   **授權 (Authorization):**
    *   頁面或特定功能是否有限制存取權限？
    *   權限檢查是在 CodeCharge 層級設定 (`restricted="True"`)，還是在事件程式碼中進行？
    *   角色 (Roles) 是如何定義和管理的？
*   **Session 管理:**
    *   Session 何時建立？儲存了哪些重要資訊？
    *   是否有設定 Session Timeout？
*   **錯誤處理:**
    *   應用程式如何處理錯誤 (例如資料庫錯誤、驗證失敗)？
    *   是否有統一的錯誤頁面或錯誤處理機制？
*   **國際化/本地化 (i18n/l10n):**
    *   網站是否支援多語言？
    *   文字資源 (如標籤、訊息) 是如何管理的？ (直接寫在 XML/JSP，還是使用資源檔？)
*   **參數傳遞與狀態管理:**
    *   頁面之間如何傳遞參數 (GET/POST/Session)？ CodeCharge 的 `<LinkParameter>`, `<ExcludeParameter>` 如何使用？

---

**下一步:**

根據此分析結果，可以開始規劃具體的遷移步驟，優先處理共用元件和核心功能，然後逐步遷移各個頁面。 