# 強制抽回後刪除功能 - 完整實現文件

## 📋 實現摘要

**實現日期**: 2025-07-05  
**實現方案**: 方案二 - 強制抽回後刪除  
**目標問題**: 解決協同狀態案件無法刪除的問題  
**解決策略**: 先抽回至可操作狀態，再執行刪除操作  

---

## 🎯 核心業務邏輯

### 問題分析
原系統存在刪除限制機制：
- **UNEDITABLE_ACC_RLT**: "234,244,254,232,252" - 協同中或陳核中案件不可編輯/刪除
- **協同保護**: caseopened 表記錄正在協同處理的案件
- **狀態約束**: 特定狀態碼的案件被系統保護無法直接刪除

### 解決方案
1. **識別可抽回狀態**: 232,252,342,352,362,442,452,462（陳核中狀態）
2. **執行抽回操作**: 轉換至可操作的前置狀態
3. **條件性刪除**: 若抽回至初建狀態(231,241,251)則自動刪除
4. **保護機制**: 維持既有協同狀態(234,244,254)的保護

---

## 📁 檔案修改清單

### 1. case_withdraw.jsp (修改)
**修改位置**: 新增 `getWithdrawTargetStatus()` 函數
```java
// 新增動態狀態對應函數 (第19-31行)
private String getWithdrawTargetStatus(String currentStatus) {
    switch(currentStatus) {
        case "232": return "231"; // 一般認定陳核 → 一般認定初建
        case "252": return "251"; // 下水道認定陳核 → 下水道認定初建
        case "342": return "344"; // 一般排拆陳核 → 一般排拆辦理中
        case "352": return "354"; // 廣告排拆陳核 → 廣告排拆辦理中
        case "362": return "364"; // 下水道排拆陳核 → 下水道排拆辦理中
        case "442": return "441"; // 一般結案陳核 → 一般結案辦理中
        case "452": return "451"; // 廣告結案陳核 → 廣告結案辦理中
        case "462": return "461"; // 下水道結案陳核 → 下水道結案辦理中
        default: return currentStatus;
    }
}
```

### 2. case_withdraw_delete.jsp (新建)
**檔案用途**: 整合抽回+刪除功能的API端點
**回傳格式**: JSON
**參數**: case_id, acc_rlt

### 3. im10101_lisHandlers.jsp (修改)
**修改位置**: 第334-341行 - 按鈕顯示邏輯
```java
// 原邏輯: UNEDITABLE_ACC_RLT = "234,244,254,232,252"
// 修改後: UNEDITABLE_ACC_RLT = "234,244,254"  (移除232,252)

// 新增抽回刪除邏輯
final String WITHDRAWABLE_THEN_DELETABLE = "232,252,342,352,362,442,452,462";

if (WITHDRAWABLE_THEN_DELETABLE.indexOf(ACC_RLT) > -1 && opened == null) {
    e.getGrid().getControl("LinkWithdrawDelete").setVisible(true);
}
```

### 4. im10101_lis.xml (修改)
**修改位置**: 第23-27行 - 新增LinkWithdrawDelete配置
```xml
<Link name="LinkWithdrawDelete" dataType="Text"
      controlSourceType="DataSource" controlSource=""
      hrefType="JavaScript" hrefSource="withdrawAndDelete('{case_id}','{ACC_RLT}')"
      format="" dbFormat="" isHtml="False" preserveParams="None">
</Link>
```

### 5. im10101_lis.jsp (修改)
**修改位置**: 
- 第346行: 抽回刪除按鈕HTML
- 第212-247行: withdrawAndDelete JavaScript函數

```html
<!-- 新增紅色抽回刪除按鈕 -->
<ccs:block name='LinkWithdrawDelete'>
    <a onclick="withdrawAndDelete('<ccs:control name='CASE_ID'/>','<ccs:control name='ACC_RLT'/>');" 
       style="color: red; margin-left: 5px;">抽回刪除</a>
</ccs:block>
```

---

## 🔄 完整業務流程

### Phase 1: 參數驗證與權限檢查
```sql
-- 1.1 檢查協同狀態 (確保沒有人正在協同處理)
SELECT case_id FROM public.caseopened WHERE CASE_ID = '${caseId}';

-- 1.2 驗證狀態碼 (確認是否為可抽回狀態)
-- 允許抽回的狀態: 232,252,342,352,362,442,452,462
```

### Phase 2: 抽回操作
```sql
-- 2.1 更新主要狀態表 (ibmsts)
UPDATE public.ibmsts 
SET acc_rlt = '${withdrawTargetStatus}' 
WHERE case_id = '${caseId}' AND acc_rlt = '${currentStatus}';

-- 2.2 同步更新提交狀態表 (casesubmitsts)
UPDATE public.casesubmitsts 
SET acc_rlt = '${withdrawTargetStatus}', rec_time = CURRENT_TIMESTAMP 
WHERE case_id = '${caseId}';

-- 2.3 清理協同記錄
DELETE FROM public.caseopened WHERE case_id = '${caseId}';

-- 2.4 記錄抽回操作日誌
INSERT INTO public.record (uuid,case_id,rec_type,org_rec,new_rec,empno) 
VALUES('${uuid}','${caseId}','案件抽回','從狀態 ${currentStatus} 抽回至 ${withdrawTargetStatus}','已執行','${empNo}');
```

### Phase 3: 條件性刪除
```sql
-- 3.1 檢查是否為可直接刪除狀態 (231,241,251)
-- 如果 withdrawTargetStatus IN ('231','241','251') 則執行刪除

-- 3.2 刪除主案件記錄
DELETE FROM public.ibmcase WHERE case_id = '${caseId}';

-- 3.3 刪除狀態記錄
DELETE FROM public.ibmsts WHERE case_id = '${caseId}';

-- 3.4 刪除提交狀態記錄
DELETE FROM public.casesubmitsts WHERE case_id = '${caseId}';

-- 3.5 記錄刪除操作日誌
INSERT INTO public.record (uuid,case_id,rec_type,org_rec,new_rec,empno) 
VALUES('${uuid}','${caseId}','案件刪除','抽回後執行刪除','已執行','${empNo}');
```

---

## 📊 狀態轉換矩陣

| 當前狀態 | 狀態說明 | 抽回目標狀態 | 目標說明 | 是否自動刪除 |
|----------|----------|--------------|----------|--------------|
| 232 | 一般認定陳核中 | 231 | 一般認定初建 | ✅ 是 |
| 252 | 下水道認定陳核中 | 251 | 下水道認定初建 | ✅ 是 |
| 342 | 一般排拆陳核中 | 344 | 一般排拆辦理中 | ❌ 否 |
| 352 | 廣告排拆陳核中 | 354 | 廣告排拆辦理中 | ❌ 否 |
| 362 | 下水道排拆陳核中 | 364 | 下水道排拆辦理中 | ❌ 否 |
| 442 | 一般結案陳核中 | 441 | 一般結案辦理中 | ❌ 否 |
| 452 | 廣告結案陳核中 | 451 | 廣告結案辦理中 | ❌ 否 |
| 462 | 下水道結案陳核中 | 461 | 下水道結案辦理中 | ❌ 否 |

---

## 🔐 安全性與權限控制

### 1. 協同狀態保護
```sql
-- 檢查協同狀態 - 防止協同中案件被誤操作
SELECT case_id FROM public.caseopened WHERE CASE_ID = '${caseId}';
```

### 2. 狀態碼驗證
```java
// 只允許特定狀態碼執行抽回操作
String[] WITHDRAWABLE_STATUSES = {"232", "252", "342", "352", "362", "442", "452", "462"};
```

### 3. 原子性操作
- 使用 JDBCConnection 確保資料庫操作原子性
- 錯誤回滾機制
- 完整的異常處理

### 4. 操作日誌
```sql
-- 每次操作都記錄於 public.record 表
INSERT INTO public.record (uuid,case_id,rec_type,org_rec,new_rec,empno) 
VALUES('${uuid}','${caseId}','${operation}','${description}','已執行','${empNo}');
```

---

## 🎨 使用者介面

### 按鈕顯示邏輯
```java
// 顯示條件:
// 1. 案件狀態在 WITHDRAWABLE_THEN_DELETABLE 清單中
// 2. 案件未被協同處理 (opened == null)
if (WITHDRAWABLE_THEN_DELETABLE.indexOf(ACC_RLT) > -1 && opened == null) {
    e.getGrid().getControl("LinkWithdrawDelete").setVisible(true);
}
```

### 確認對話框
```javascript
// 詳細的確認訊息，包含風險提醒
confirm('確定要抽回並刪除此案件嗎？\n\n案件編號：' + case_id + 
        '\n目前狀態：' + acc_rlt + 
        '\n\n注意事項：\n1. 案件將從目前狀態抽回\n2. 如果抽回到初建狀態將直接刪除\n3. 此操作無法復原\n\n確定要繼續嗎？')
```

### 處理中指示器
```javascript
// 顯示處理中訊息避免重複點擊
var loadingMsg = $('<div id="loadingMsg" style="...">處理中，請稍候...</div>');
$('body').append(loadingMsg);
```

---

## 📋 測試案例設計

### 測試案例 1: 認定陳核案件抽回刪除
```sql
-- 準備測試資料
INSERT INTO public.ibmcase (case_id, ...) VALUES ('TEST001', ...);
INSERT INTO public.ibmsts (case_id, acc_rlt) VALUES ('TEST001', '232');

-- 執行抽回刪除
-- 預期結果: 案件被刪除，記錄操作日誌
```

### 測試案例 2: 排拆陳核案件抽回 (不刪除)
```sql
-- 準備測試資料
INSERT INTO public.ibmcase (case_id, ...) VALUES ('TEST002', ...);
INSERT INTO public.ibmsts (case_id, acc_rlt) VALUES ('TEST002', '342');

-- 執行抽回操作
-- 預期結果: 狀態變更為 '344'，案件保留
```

### 測試案例 3: 協同中案件拒絕操作
```sql
-- 準備測試資料
INSERT INTO public.ibmcase (case_id, ...) VALUES ('TEST003', ...);
INSERT INTO public.ibmsts (case_id, acc_rlt) VALUES ('TEST003', '232');
INSERT INTO public.caseopened (case_id, empno) VALUES ('TEST003', 'EMP001');

-- 執行抽回刪除
-- 預期結果: 操作被拒絕，回傳錯誤訊息
```

---

## 🚀 部署檢查清單

### 部署前檢查
- [x] 所有修改檔案已備份
- [x] 資料庫連線設定確認
- [x] 權限表 (ibmcode) 狀態碼定義正確
- [x] 觸發器相容性確認

### 部署後驗證
- [ ] 抽回刪除按鈕正確顯示
- [ ] JavaScript 函數正常執行
- [ ] 狀態轉換邏輯正確
- [ ] 操作日誌正常記錄
- [ ] 錯誤處理機制運作

### 回滾計畫
```bash
# 如需回滾，恢復以下檔案的原始版本:
# 1. case_withdraw.jsp (移除 getWithdrawTargetStatus 函數)
# 2. im10101_lisHandlers.jsp (恢復原 UNEDITABLE_ACC_RLT)
# 3. im10101_lis.xml (移除 LinkWithdrawDelete)
# 4. im10101_lis.jsp (移除 withdrawAndDelete 函數和按鈕)
# 5. 刪除 case_withdraw_delete.jsp
```

---

## 📈 效能影響評估

### 資料庫操作複雜度
- **抽回操作**: 3-4個 UPDATE/DELETE 語句
- **刪除操作**: 3個 DELETE 語句  
- **日誌記錄**: 1-2個 INSERT 語句
- **總計**: 每次操作最多8個SQL語句

### 預期負載
- **操作頻率**: 低頻 (每日 < 50次)
- **併發控制**: 協同狀態檢查防止衝突
- **效能影響**: minimal (短暫鎖定相關記錄)

---

## 📞 支援與維護

### 常見問題
1. **Q**: 為什麼某些案件無法看到抽回刪除按鈕？
   **A**: 確認案件狀態是否在允許清單中，且未被協同處理

2. **Q**: 抽回後案件沒有被刪除？
   **A**: 只有抽回至初建狀態(231,241,251)才會自動刪除

3. **Q**: 操作失敗如何處理？
   **A**: 檢查操作日誌，確認資料庫狀態，必要時聯繫技術支援

### 監控要點
- 檢查 public.record 表的操作日誌
- 監控 caseopened 表的協同狀態
- 追蹤 ibmsts 表的狀態變更

---

**文件版本**: 1.0  
**最後更新**: 2025-07-05  
**作者**: Claude Code - 新北市違章建築管理系統開發團隊  
**審核狀態**: 實現完成，等待功能測試確認  