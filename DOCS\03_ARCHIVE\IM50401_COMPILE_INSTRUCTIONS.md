# IM50401 編譯與部署指引

## 編譯步驟

由於修改了 `IM50401.java`，需要重新編譯並部署。請在有 Java 開發環境的機器上執行以下步驟：

### 1. 進入專案目錄
```bash
cd /mnt/d/apache-tomcat-9.0.98/webapps/src
```

### 2. 編譯 Java 檔案
```bash
javac -cp "WEB-INF/lib/*:WEB-INF/classes" -d WEB-INF/classes WEB-INF/java/com/ezek/report/IM50401.java
```

### 3. 確認編譯成功
檢查是否產生新的 class 檔案：
```bash
ls -la WEB-INF/classes/com/ezek/report/IM50401*.class
```

應該會看到兩個檔案：
- `IM50401.class`
- `IM50401$IM50401_Bean.class`

### 4. 重啟 Tomcat（如需要）
如果應用程式沒有自動重新載入，可能需要重啟 Tomcat：
```bash
# 停止 Tomcat
/mnt/d/apache-tomcat-9.0.98/bin/shutdown.sh

# 啟動 Tomcat
/mnt/d/apache-tomcat-9.0.98/bin/startup.sh
```

## 驗證修改

編譯部署完成後，請執行以下驗證：

1. 登入系統
2. 進入「違章案件認定件數統計」功能（im50401）
3. 輸入時間區間：2025/06/01 - 2025/06/30
4. 點擊「輸出 xlsx」按鈕
5. 檢查 D 類統計數據是否為 254（應排除 1 筆銷案）

## 回滾方案

如果發生問題，可以還原備份檔案：
```bash
# 還原 Java 原始碼
cp WEB-INF/java/com/ezek/report/IM50401.java.backup WEB-INF/java/com/ezek/report/IM50401.java

# 重新編譯
javac -cp "WEB-INF/lib/*:WEB-INF/classes" -d WEB-INF/classes WEB-INF/java/com/ezek/report/IM50401.java
```