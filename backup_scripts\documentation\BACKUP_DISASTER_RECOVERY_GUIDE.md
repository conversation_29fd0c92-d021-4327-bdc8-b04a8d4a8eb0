# 新北市違章建築管理系統 - 備份與災難復原完整指南

## 📋 目錄

1. [系統概述](#系統概述)
2. [備份策略](#備份策略)
3. [災難復原程序](#災難復原程序)
4. [監控與告警](#監控與告警)
5. [操作手冊](#操作手冊)
6. [故障排除](#故障排除)
7. [合規性要求](#合規性要求)
8. [緊急聯絡資訊](#緊急聯絡資訊)

---

## 🎯 系統概述

### 業務關鍵性評估
- **系統類型**：政府違章建築管理系統
- **業務影響**：高度關鍵
- **使用者數量**：100+ 同時使用者
- **資料量**：37萬筆案件 + 100萬筆流程記錄
- **RTO (Recovery Time Objective)**：4小時
- **RPO (Recovery Point Objective)**：1小時

### 技術架構
```
┌─────────────────────────────────────────────────────────────┐
│                    新北市違章建築管理系統                      │
├─────────────────────────────────────────────────────────────┤
│  前端層：Bootstrap 5.3.3 + jQuery 3.7.1                   │
│  應用層：Apache Tomcat 9.0.98 + CodeCharge Studio          │
│  資料層：PostgreSQL (主資料庫) + SQL Server (GIS資料庫)      │
│  基礎設施：Windows Server 2019/2022                        │
└─────────────────────────────────────────────────────────────┘
```

### 關鍵系統組件
1. **主資料庫**：PostgreSQL (localhost:5432/bms)
2. **輔助資料庫**：SQL Server (**************:2433/ramsGIS)
3. **應用伺服器**：Apache Tomcat 9.0.98
4. **網頁應用**：/webapps/src (CodeCharge Studio架構)

---

## 💾 備份策略

### 備份類型與排程

#### PostgreSQL 資料庫備份
```powershell
# 完整備份 (每日 2:00 AM)
.\postgresql_backup.ps1 -BackupType full -Compress -Verify

# 增量備份 (每6小時)
.\postgresql_backup.ps1 -BackupType incremental -Compress

# 差異備份 (每12小時)
.\postgresql_backup.ps1 -BackupType differential -Compress
```

#### SQL Server 資料庫備份
```powershell
# 完整備份 (每日 1:00 AM)
.\sqlserver_backup.ps1 -BackupType full -Compress -Verify

# 差異備份 (每8小時)
.\sqlserver_backup.ps1 -BackupType differential -Compress

# 交易紀錄備份 (每小時)
.\sqlserver_backup.ps1 -BackupType log -Compress
```

#### 應用程式備份
```powershell
# 完整備份 (每週日 00:00)
.\application_backup.ps1 -BackupType full -Compress -StopServices

# 增量備份 (每4小時)
.\application_backup.ps1 -BackupType incremental -Compress

# 配置備份 (每2小時)
.\application_backup.ps1 -BackupType configuration -Compress
```

### 備份保留政策
| 備份類型 | 保留期間 | 儲存位置 |
|---------|----------|----------|
| PostgreSQL 完整 | 7天 | D:\Backups\BMS\full |
| PostgreSQL 增量 | 3天 | D:\Backups\BMS\wal |
| SQL Server 完整 | 7天 | D:\Backups\BMS\sqlserver\full |
| SQL Server 差異 | 3天 | D:\Backups\BMS\sqlserver\differential |
| SQL Server 紀錄 | 2天 | D:\Backups\BMS\sqlserver\log |
| 應用程式完整 | 14天 | D:\Backups\BMS\application\full |
| 應用程式增量 | 7天 | D:\Backups\BMS\application\incremental |
| 配置檔案 | 30天 | D:\Backups\BMS\application\configuration |

### 跨區域複製
```json
{
  "cross_region_replication": {
    "enabled": true,
    "targets": [
      {
        "name": "network_share",
        "type": "network_share",
        "share_path": "\\\\backup-server\\bms-backups",
        "schedule": "daily_after_backup"
      }
    ]
  }
}
```

---

## 🚨 災難復原程序

### 災難評估與決策流程

#### 第一階段：災難評估 (0-15分鐘)
```powershell
# 執行災難評估
.\disaster_recovery.ps1 -Operation assessment

# 評估結果判斷
# - healthy: 正常運作
# - warning: 有警告但可繼續運作
# - critical: 嚴重問題需要介入
# - disaster: 災難狀況需要復原
```

#### 第二階段：復原決策 (15-30分鐘)
1. **評估災難影響範圍**
2. **確認復原點 (Recovery Point)**
3. **通知相關人員**
4. **啟動復原程序**

#### 第三階段：執行復原 (30分鐘-4小時)
```powershell
# 完整災難復原
.\disaster_recovery.ps1 -Operation recovery -RecoveryPoint "2024-01-15_14:00:00"

# 資料庫失效轉移
.\disaster_recovery.ps1 -Operation failover

# 復原測試
.\disaster_recovery.ps1 -Operation test
```

### 復原程序步驟

#### 步驟1：準備復原環境
```powershell
# 初始化復原環境
Initialize-RecoveryEnvironment

# 檢查項目：
# - 磁碟空間 > 100GB
# - 記憶體 > 8GB
# - 網路連線正常
# - 備份檔案完整性
```

#### 步驟2：停止應用服務
```powershell
# 停止關鍵服務
Stop-Service "Apache Tomcat 9.0 Tomcat9"
Stop-Service "PostgreSQL Database Server 15"
Stop-Service "World Wide Web Publishing Service"
```

#### 步驟3：復原資料庫
```powershell
# PostgreSQL 復原
pg_restore -h localhost -p 5432 -U postgres -d bms -c --if-exists backup_file.sql

# SQL Server 復原
RESTORE DATABASE [ramsGIS] 
FROM DISK = 'backup_file.bak'
WITH REPLACE, CHECKSUM, STATS = 10
```

#### 步驟4：復原應用程式
```powershell
# 復原應用程式檔案
Expand-Archive -Path app_backup.zip -DestinationPath D:\apache-tomcat-9.0.98\webapps\src

# 復原配置檔案
Copy-Item config_backup\*.* D:\apache-tomcat-9.0.98\conf\
```

#### 步驟5：啟動服務與驗證
```powershell
# 啟動服務
Start-Service "PostgreSQL Database Server 15"
Start-Service "Apache Tomcat 9.0 Tomcat9"
Start-Service "World Wide Web Publishing Service"

# 驗證復原
Test-Connection -ComputerName localhost -Port 8080
Test-DatabaseConnection -ConnectionString "postgresql://localhost:5432/bms"
```

### 回滾程序
```powershell
# 如果復原失敗，執行回滾
.\disaster_recovery.ps1 -Operation rollback

# 回滾步驟：
# 1. 停止所有服務
# 2. 還原原始配置
# 3. 重新啟動服務
# 4. 驗證系統狀態
```

---

## 📊 監控與告警

### 監控項目

#### 備份監控
```powershell
# 啟動持續監控
.\monitoring_alerting.ps1 -Operation monitor -ContinuousMode -IntervalMinutes 5

# 監控項目：
# - 備份作業成功率
# - 備份檔案大小變化
# - 備份完成時間
# - 儲存空間使用量
```

#### 系統健康監控
- **資料庫連線狀態**
- **應用服務狀態**
- **磁碟空間使用量**
- **記憶體使用率**
- **CPU使用率**
- **網路連線狀態**

### 告警設定

#### 關鍵告警 (Critical)
- 備份作業失敗
- 資料庫連線中斷
- 磁碟空間不足 < 10%
- 服務停止運作

#### 警告告警 (Warning)
- 備份時間超過預期
- 磁碟空間不足 < 20%
- 記憶體使用率 > 80%
- 備份檔案大小異常變化

#### 通知方式
1. **Email通知**：<EMAIL>
2. **Slack通知**：#backup-alerts
3. **簡訊通知**：關鍵告警時
4. **Windows事件記錄**：所有告警

### 告警響應程序

#### 備份失敗告警
```powershell
# 1. 檢查備份日誌
Get-Content ".\logs\postgresql_backup_$(Get-Date -Format 'yyyyMMdd').log" | Select-String "ERROR"

# 2. 重新執行備份
.\postgresql_backup.ps1 -BackupType full -Compress -Verify

# 3. 如果持續失敗，檢查：
# - 磁碟空間
# - 資料庫連線
# - 檔案權限
# - 服務狀態
```

#### 儲存空間不足告警
```powershell
# 1. 檢查磁碟使用量
Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "D:" }

# 2. 清理舊備份
Remove-OldBackups -BackupPath "D:\Backups\BMS" -RetentionDays 7

# 3. 如果仍不足，考慮：
# - 移動備份到其他磁碟
# - 增加儲存容量
# - 調整備份保留政策
```

---

## 📖 操作手冊

### 日常操作檢查清單

#### 每日檢查 (上午8:00)
- [ ] 檢查前一夜備份作業狀態
- [ ] 查看監控告警
- [ ] 確認磁碟空間使用量
- [ ] 檢查應用服務狀態
- [ ] 查看系統資源使用情況

#### 每週檢查 (週一上午)
- [ ] 檢查備份檔案完整性
- [ ] 清理過期的備份檔案
- [ ] 檢查跨區域複製狀態
- [ ] 更新備份統計報告
- [ ] 測試一個備份檔案的還原

#### 每月檢查 (月初)
- [ ] 執行完整災難復原測試
- [ ] 檢查備份策略是否需要調整
- [ ] 更新緊急聯絡資訊
- [ ] 檢查合規性要求
- [ ] 產生月度備份報告

### 緊急操作程序

#### 緊急復原程序 (RTO: 4小時)
```
時間軸：
00:00 - 災難發生
00:15 - 評估完成，決定啟動復原
00:30 - 開始復原作業
02:00 - 資料庫復原完成
03:00 - 應用程式復原完成
03:30 - 服務啟動，開始驗證
04:00 - 系統復原完成，恢復服務
```

#### 緊急聯絡流程
1. **第一時間通知**：系統管理員
2. **30分鐘內通知**：IT主管
3. **1小時內通知**：業務主管
4. **2小時內通知**：高階管理層

---

## 🔧 故障排除

### 常見問題與解決方案

#### 備份失敗問題

**問題1：PostgreSQL 備份失敗**
```
錯誤：pg_dump: error: connection to server at "localhost" (::1), port 5432 failed
解決：
1. 檢查PostgreSQL服務狀態
2. 確認連線參數正確
3. 檢查防火牆設定
4. 驗證使用者權限
```

**問題2：SQL Server 備份失敗**
```
錯誤：Cannot open backup device. Operating system error 5(Access is denied)
解決：
1. 檢查SQL Server服務帳戶權限
2. 確認備份目錄存在且可寫入
3. 檢查防毒軟體是否阻擋
4. 確認磁碟空間足夠
```

**問題3：應用程式備份失敗**
```
錯誤：Cannot access file because it is being used by another process
解決：
1. 停止相關服務
2. 使用Volume Shadow Copy Service
3. 檢查檔案鎖定狀況
4. 調整備份排程時間
```

#### 復原失敗問題

**問題1：資料庫復原失敗**
```
錯誤：Database restore failed with error code 3201
解決：
1. 檢查備份檔案完整性
2. 確認目標資料庫狀態
3. 檢查磁碟空間
4. 驗證使用者權限
```

**問題2：應用程式復原失敗**
```
錯誤：Service failed to start after restore
解決：
1. 檢查配置檔案語法
2. 確認檔案權限設定
3. 檢查相依性服務
4. 查看應用程式日誌
```

### 診斷工具

#### 備份診斷
```powershell
# 檢查備份作業狀態
Get-ChildItem "D:\Backups\BMS" -Recurse | 
  Where-Object { $_.LastWriteTime -gt (Get-Date).AddDays(-1) } |
  Select-Object Name, Length, LastWriteTime

# 檢查備份檔案完整性
.\postgresql_backup.ps1 -BackupType full -Verify

# 產生備份報告
.\monitoring_alerting.ps1 -Operation report
```

#### 系統診斷
```powershell
# 檢查服務狀態
Get-Service "Apache Tomcat 9.0 Tomcat9", "PostgreSQL Database Server 15"

# 檢查系統資源
Get-Counter "\Memory\Available MBytes", "\Processor(_Total)\% Processor Time"

# 檢查磁碟空間
Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
```

---

## 📋 合規性要求

### 政府資訊系統安全規範

#### 備份要求
- **備份頻率**：主要資料每日備份
- **備份保留**：至少保留30天
- **備份驗證**：每週驗證備份完整性
- **異地備份**：重要資料需異地備份

#### 復原要求
- **復原測試**：每季執行復原測試
- **文件記錄**：完整記錄復原程序
- **人員訓練**：定期培訓操作人員
- **變更管理**：建立變更管制程序

### 稽核與報告

#### 月度報告內容
- 備份作業成功率統計
- 儲存空間使用趨勢
- 系統效能指標
- 異常事件記錄
- 改善建議

#### 年度檢查項目
- 災難復原計畫更新
- 備份策略有效性評估
- 人員訓練記錄
- 設備維護記錄
- 合規性符合度評估

---

## 📞 緊急聯絡資訊

### 24小時緊急聯絡

#### 系統管理員
- **姓名**：系統管理員
- **手機**：0912-345-678
- **Email**：<EMAIL>
- **責任**：系統監控、備份管理、初步故障排除

#### IT主管
- **姓名**：IT主管
- **手機**：0923-456-789
- **Email**：<EMAIL>
- **責任**：重大事件決策、資源調配、對外溝通

#### 業務主管
- **姓名**：業務主管
- **手機**：0934-567-890
- **Email**：<EMAIL>
- **責任**：業務影響評估、使用者溝通、服務優先順序決定

### 廠商聯絡資訊

#### 硬體廠商
- **公司**：伺服器廠商
- **聯絡人**：技術支援
- **電話**：02-1234-5678
- **Email**：<EMAIL>

#### 軟體廠商
- **公司**：資料庫廠商
- **聯絡人**：技術支援
- **電話**：02-2345-6789
- **Email**：<EMAIL>

---

## 📚 附錄

### 相關文件
- [系統架構文件](../02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫操作指南](../02_REFERENCE/DATABASE_COMPLETE_GUIDE.md)
- [業務流程文件](../02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md)
- [技術實施指南](../02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md)

### 腳本檔案位置
```
backup_scripts/
├── postgresql_backup.ps1      # PostgreSQL備份腳本
├── sqlserver_backup.ps1       # SQL Server備份腳本
├── application_backup.ps1     # 應用程式備份腳本
├── disaster_recovery.ps1      # 災難復原腳本
├── monitoring_alerting.ps1    # 監控告警腳本
├── recovery_testing.ps1       # 復原測試腳本
└── config/                    # 配置檔案目錄
    ├── backup_config.json     # 備份配置
    ├── dr_config.json         # 災難復原配置
    └── monitoring_config.json # 監控配置
```

### 日誌檔案位置
```
logs/
├── postgresql_backup_YYYYMMDD.log
├── sqlserver_backup_YYYYMMDD.log
├── application_backup_YYYYMMDD.log
├── disaster_recovery_YYYYMMDD.log
├── monitoring_YYYYMMDD.log
└── recovery_testing_YYYYMMDD.log
```

---

**文件版本**：v1.0  
**最後更新**：2024年7月9日  
**文件作者**：系統管理員  
**核准人員**：IT主管  

*本文件包含敏感資訊，請妥善保管並限制存取權限*