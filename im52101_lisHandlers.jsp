<%@page pageEncoding="utf-8"%><%@page import="com.ezek.utils.EzekUtils,java.sql.Connection,java.sql.PreparedStatement,java.sql.SQLException"%>

<%! 

private final String CONNECTION_NAME = "DBConn";
private final long MAX_RECORDS_TO_SHOW = 30000L;

public class im52101_lisServiceChecker implements com.codecharge.feature.IServiceChecker {
    public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
        String attr = "" + request.getParameter("callbackControl");

         
        return false;
    }
}

public class im52101_lisPageHandler implements PageListener {
    public void beforeInitialize(Event e) {
        
    }

    public void afterInitialize(Event e) {
        String currentProgramId = "im60501";
       String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID"));          
        //清除搜尋參數
        if (!StringUtils.isEmpty(PROGRAM_ID) && !PROGRAM_ID.equals(currentProgramId)) {
            for (int i = 1; i <= 15; i++) SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + String.valueOf(i), "");
        }
        //更新使用者使用的模組ID
        SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PROGRAM_ID", currentProgramId);
        //登入狀態驗證
        if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
    }

    public void onInitializeView(Event e) {
    }

    public void beforeShow(Event e) {
    }

    public void beforeOutput(Event e) {
    }

    public void beforeUnload(Event e) {
    }

    public void onCache(CacheEvent e) {
        if (e.getCacheOperation() == ICache.OPERATION_GET) {
        } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
        }
    }
}

public class im52101_lisibmcaseGridHandler implements GridListener, GridDataObjectListener {
    public void afterInitialize(Event e) {
    }

    public void beforeShow(Event e) {
        String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID")); 
        
        String ROLE_ID = Utils.convertToString(DBTools.dLookUp("ROLE_ID", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
        String ROLE_ID_2 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_2", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
        String ROLE_ID_3 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_3", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));		
        
        //判定角色若為系統管理者或隊部長官
        if("sysManager".equals(ROLE_ID) || "sysManager".equals(ROLE_ID_2) || "sysManager".equals(ROLE_ID_3) || "supervisor".equals(ROLE_ID) || "supervisor".equals(ROLE_ID_2) || "supervisor".equals(ROLE_ID_3)){
            e.getGrid().getControl("Link_insert").setVisible(true);
        }
        else {
            e.getGrid().getControl("Link_insert").setVisible(false);
        }

        
    }

    public void beforeShowRow(Event e) {
        ezekTool ezTool = new ezekTool();
        String op_date = Utils.convertToString(e.getGrid().getControl("upload_timestamp").getValue());
        op_date = "1140414202530";
        if( !StringUtils.isEmpty(op_date)){
		  op_date = ezTool.formateDate(op_date);
	    }

         e.getGrid().getControl("upload_timestamp").setValue(op_date); 


         String status = Utils.convertToString(e.getGrid().getControl("status").getValue()).trim();
        //e.getGrid().getControl("Link_Error").setVisible(false);
        
         if(status.equals("NEW_UPLOAD")){
             e.getGrid().getControl("status").setValue("未處理"); 
         }
         else if(status.equals("PENDING")){
            e.getGrid().getControl("status").setValue("待處理"); 
         }
         else if(status.equals("PROCESSING")){
            e.getGrid().getControl("status").setValue("處理中"); 
         }
         else if(status.equals("COMPLETED")){
            e.getGrid().getControl("status").setValue("匯入完成"); 
             
         }
         else if(status.equals("ERROR")){
            e.getGrid().getControl("Link_Error").setVisible(true);
            e.getGrid().getControl("status").setValue("匯入錯誤"); 
         }
          
    }

    public void beforeSelect(Event e) {
    }

    public void beforeBuildSelect(DataObjectEvent e) {
        String newWhereStr = " 1 = 1";
        String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
        System.out.println("in10101_man: error is "  );
        String session_1 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_1"));
        String session_2 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_2"));
       
        String ROLE_ID = Utils.convertToString(DBTools.dLookUp("ROLE_ID", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
        String ROLE_ID_2 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_2", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
        String ROLE_ID_3 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_3", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));		
        
        //判定角色若為系統管理者或隊部長官
        if("sysManager".equals(ROLE_ID) || "sysManager".equals(ROLE_ID_2) || "sysManager".equals(ROLE_ID_3) || "supervisor".equals(ROLE_ID) || "supervisor".equals(ROLE_ID_2) || "supervisor".equals(ROLE_ID_3)){
            newWhereStr = " 1 = 1 AND status <> 'NEW'";
        }
        else{
            newWhereStr = " 1 = 2";
        }

        if (!StringUtils.isEmpty(session_1) && !StringUtils.isEmpty(session_2)) {	
            if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";	
                newWhereStr += "'"+ session_1 +"' <= i.op_date   and  i.op_date <= '"+ session_2 +"' ";
        }else if( !StringUtils.isEmpty(session_1) ){
            if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
                newWhereStr += "'"+ session_1 +"' <= i.op_date  ";
        }else if( !StringUtils.isEmpty(session_2) ){ 
            if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
                newWhereStr += " i.op_date <= '"+ session_2 +"' ";
        }

    
        e.getCommand().setWhere(newWhereStr);
    }

    public void beforeExecuteSelect(DataObjectEvent e) {
    }

    public void afterExecuteSelect(DataObjectEvent e) {
    }
}

public class ibmcaseibmcase_TotalRecordsLabelHandler implements ControlListener {
    public void beforeShow(Event e) {
        ((Control) e.getSource()).setValue( ((Grid) e.getParent()).getAmountOfRows());
    }
}

public class im52101_lisSearchRecordHandler implements RecordListener, RecordDataObjectListener {
    public void afterInitialize(Event e) {
    }

    public void onSetDataSource(DataObjectEvent e) {
    }

    public void beforeShow(Event e) {
        String USER_MESSAGE = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserMessage"));
        if (!StringUtils.isEmpty(USER_MESSAGE)) {
            SessionStorage.getInstance(e.getPage().getRequest()).removeAttribute("UserMessage");
            e.getRecord().getControl("watchdog").setValue(USER_MESSAGE);
        }


        String SEARCHPARAM_1 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_1"));
        String SEARCHPARAM_2 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_2"));
      
        String s_date = "", e_date = "";
        if (!StringUtils.isEmpty(SEARCHPARAM_1)) {
            e.getRecord().getControl("S_DATE").setValue(SEARCHPARAM_1);
            s_date += SEARCHPARAM_1;
        } else {
            e.getRecord().getControl("S_DATE").setValue("");
        }

        if (!StringUtils.isEmpty(SEARCHPARAM_2)) {
            e.getRecord().getControl("E_DATE").setValue(SEARCHPARAM_2);
            e_date += SEARCHPARAM_2;
        } else {
            e.getRecord().getControl("E_DATE").setValue("");
        }
        
        e.getRecord().getControl("S_DATE").setValue(s_date);
        e.getRecord().getControl("E_DATE").setValue(e_date);
    }

    public void onValidate(Event e) {
        
        String searchParam_1 = Utils.convertToString(e.getRecord().getControl("S_DATE").getValue());
        String searchParam_2 = Utils.convertToString(e.getRecord().getControl("E_DATE").getValue());
            
        SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_1", searchParam_1);
        SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_2", searchParam_2);
    }

    public void beforeSelect(Event e) {
    }

    public void beforeBuildSelect(DataObjectEvent e) {
    }

    public void beforeExecuteSelect(DataObjectEvent e) {
    }

    public void afterExecuteSelect(DataObjectEvent e) {
    }

    public void beforeInsert(Event e) {
    }

    public void beforeBuildInsert(DataObjectEvent e) {
    }

    public void beforeExecuteInsert(DataObjectEvent e) {
    }

    public void afterExecuteInsert(DataObjectEvent e) {
    }

    public void afterInsert(Event e) {
    }

    public void beforeUpdate(Event e) {
    }

    public void beforeBuildUpdate(DataObjectEvent e) {
    }

    public void beforeExecuteUpdate(DataObjectEvent e) {
    }

    public void afterExecuteUpdate(DataObjectEvent e) {
    }

    public void afterUpdate(Event e) {
    }

    public void beforeDelete(Event e) {
    }

    public void beforeBuildDelete(DataObjectEvent e) {
    }

    public void beforeExecuteDelete(DataObjectEvent e) {
    }

    public void afterExecuteDelete(DataObjectEvent e) {
    }

    public void afterDelete(Event e) {
    }
}

public class SearchButton_CleanButtonHandler implements ButtonListener {
    public void onClick(Event e) {

    }

    public void beforeShow(Event e) {
    }
}

%> 
<%
    Page im52101_lisModel = (Page)request.getAttribute("im52101_lis_page");
    Page im52101_lisParent = (Page)request.getAttribute("im52101_lisParent");

    if (im52101_lisModel == null) {
        PageController im52101_lisCntr = new PageController(request, response, application, "/im52101_lis.xml" );
        im52101_lisModel = im52101_lisCntr.getPage();
        im52101_lisModel.setRelativePath("./");
        im52101_lisModel.addPageListener(new im52101_lisPageHandler());
        ((Grid)im52101_lisModel.getChild("list")).addGridListener(new im52101_lisibmcaseGridHandler());
        ((Label)((Grid)im52101_lisModel.getChild("list")).getChild("list_TotalRecords")).addControlListener(new ibmcaseibmcase_TotalRecordsLabelHandler());
        //((Record)im52101_lisModel.getChild("Search")).addRecordListener(new im52101_lisSearchRecordHandler());
        //((Button)((Record)im52101_lisModel.getChild("Search")).getChild("Button_Clean")).addButtonListener(new SearchButton_CleanButtonHandler());
        im52101_lisCntr.process();

        if (im52101_lisParent == null) {
            im52101_lisModel.setCookies();
            if (im52101_lisModel.redirect()) return;
        } else {
            im52101_lisModel.redirect();
        }
           
    }
%>

<%!
	public class ezekTool{
		public String formateDate(String dateOld) 
		{
			String date = "";

			if(!StringUtils.isEmpty(dateOld))
			{

				if(dateOld.length() == 13)// yyymmddhhmiss
				{
					date = dateOld.substring(0,3)+"/"+dateOld.substring(3,5)+"/"+dateOld.substring(5,7) + " " + dateOld.substring(7,9) + ":" + dateOld.substring(9,11);
				}
				else if(dateOld.length() == 12) // yymmddhhmiss
				{
					date = dateOld.substring(0,2)+"/"+dateOld.substring(2,4)+"/"+dateOld.substring(4,6) + " " + dateOld.substring(6,8) + ":" + dateOld.substring(8,10);
				}
				else if(dateOld.length() == 14) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,4)+"/"+dateOld.substring(4,6)+"/"+dateOld.substring(6,8) + " " + dateOld.substring(8,10) + ":" + dateOld.substring(10,12);
				}
				else if(dateOld.length() == 6) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,2)+"/"+dateOld.substring(2,4)+"/"+dateOld.substring(4,6);
				}
				else if(dateOld.length() == 7) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,3)+"/"+dateOld.substring(3,5)+"/"+dateOld.substring(5,7);
				}
				else{
					date = "";
				}
			}

			return date;
		}
	}
%>

