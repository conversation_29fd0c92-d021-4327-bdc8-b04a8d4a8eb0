package com.ezek.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.codecharge.util.CCLogger;

/**
 * 安全配置管理器 - 負責管理系統配置，支持環境變數和加密密碼
 * 
 * 主要功能：
 * 1. 優先從環境變數讀取配置
 * 2. 支持配置檔案中的變數替換 ${VAR_NAME:default_value}
 * 3. 敏感資訊加密存儲
 * 4. 執行時配置重載
 * 5. 多環境配置支持
 * 
 * 使用方式：
 * SecureConfigManager config = SecureConfigManager.getInstance();
 * String dbPassword = config.getDecryptedProperty("DBConn.password");
 * 
 * <AUTHOR> Security Team
 * @version 1.0
 * @since 2025-07-09
 */
public class SecureConfigManager {
    
    private static final String CONFIG_FILE_PATH = "/WEB-INF/site.properties";
    private static final String TEMPLATE_FILE_PATH = "/WEB-INF/site.properties.template";
    
    // 變數替換的正規表達式 ${VAR_NAME:default_value}
    private static final Pattern VAR_PATTERN = Pattern.compile("\\$\\{([^:}]+)(?::([^}]*))?\\}");
    
    // 敏感配置項清單
    private static final String[] SENSITIVE_KEYS = {
        "DBConn.password", "DBConn2.password", 
        "security.encryption.key", "ALEncryptionKey"
    };
    
    private static SecureConfigManager instance;
    private final Properties properties;
    private final ConcurrentHashMap<String, String> configCache;
    private final ReadWriteLock lock;
    private final CCLogger logger;
    private final PasswordEncryptor encryptor;
    
    private SecureConfigManager() {
        this.properties = new Properties();
        this.configCache = new ConcurrentHashMap<>();
        this.lock = new ReentrantReadWriteLock();
        this.logger = CCLogger.getInstance();
        this.encryptor = new PasswordEncryptor();
        
        loadConfiguration();
    }
    
    /**
     * 獲取配置管理器單例
     */
    public static SecureConfigManager getInstance() {
        if (instance == null) {
            synchronized (SecureConfigManager.class) {
                if (instance == null) {
                    instance = new SecureConfigManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 載入配置檔案
     */
    private void loadConfiguration() {
        lock.writeLock().lock();
        try {
            properties.clear();
            configCache.clear();
            
            // 1. 載入基本配置檔案
            loadPropertiesFile(CONFIG_FILE_PATH);
            
            // 2. 處理環境變數替換
            processEnvironmentVariables();
            
            // 3. 驗證必要配置
            validateRequiredProperties();
            
            logger.info("SecureConfigManager: 配置載入完成，共載入 " + properties.size() + " 個配置項");
            
        } catch (Exception e) {
            logger.error("SecureConfigManager: 配置載入失敗", e);
            throw new RuntimeException("配置載入失敗", e);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 載入配置檔案
     */
    private void loadPropertiesFile(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            properties.load(fis);
        } catch (IOException e) {
            logger.warn("SecureConfigManager: 無法載入配置檔案 " + filePath + ", 使用預設值");
        }
    }
    
    /**
     * 處理環境變數替換
     * 格式: ${ENV_VAR:default_value}
     */
    private void processEnvironmentVariables() {
        Properties processedProps = new Properties();
        
        for (String key : properties.stringPropertyNames()) {
            String value = properties.getProperty(key);
            String processedValue = processVariableSubstitution(value);
            processedProps.setProperty(key, processedValue);
        }
        
        properties.clear();
        properties.putAll(processedProps);
    }
    
    /**
     * 處理單一值的變數替換
     */
    private String processVariableSubstitution(String value) {
        if (value == null || value.trim().isEmpty()) {
            return value;
        }
        
        Matcher matcher = VAR_PATTERN.matcher(value);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String varName = matcher.group(1);
            String defaultValue = matcher.group(2) != null ? matcher.group(2) : "";
            
            // 1. 優先從環境變數獲取
            String envValue = System.getenv(varName);
            if (envValue != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(envValue));
                continue;
            }
            
            // 2. 從系統屬性獲取
            String sysValue = System.getProperty(varName);
            if (sysValue != null) {
                matcher.appendReplacement(result, Matcher.quoteReplacement(sysValue));
                continue;
            }
            
            // 3. 使用預設值
            matcher.appendReplacement(result, Matcher.quoteReplacement(defaultValue));
        }
        
        matcher.appendTail(result);
        return result.toString();
    }
    
    /**
     * 驗證必要配置項
     */
    private void validateRequiredProperties() {
        String[] requiredKeys = {
            "DBConn.url", "DBConn.user", "DBConn.password",
            "DBConn2.url", "DBConn2.user", "DBConn2.password"
        };
        
        for (String key : requiredKeys) {
            String value = properties.getProperty(key);
            if (value == null || value.trim().isEmpty()) {
                throw new RuntimeException("必要配置項缺失: " + key);
            }
        }
    }
    
    /**
     * 獲取配置項值
     */
    public String getProperty(String key) {
        return getProperty(key, null);
    }
    
    /**
     * 獲取配置項值，支援預設值
     */
    public String getProperty(String key, String defaultValue) {
        lock.readLock().lock();
        try {
            // 1. 先檢查快取
            String cachedValue = configCache.get(key);
            if (cachedValue != null) {
                return cachedValue;
            }
            
            // 2. 從環境變數獲取（優先級最高）
            String envKey = key.replace(".", "_").toUpperCase();
            String envValue = System.getenv(envKey);
            if (envValue != null) {
                configCache.put(key, envValue);
                return envValue;
            }
            
            // 3. 從配置檔案獲取
            String propValue = properties.getProperty(key, defaultValue);
            if (propValue != null) {
                configCache.put(key, propValue);
            }
            
            return propValue;
            
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 獲取加密的配置項值（用於敏感資訊）
     */
    public String getDecryptedProperty(String key) {
        String encryptedValue = getProperty(key);
        if (encryptedValue == null) {
            return null;
        }
        
        // 檢查是否為敏感配置項
        if (isSensitiveProperty(key)) {
            try {
                return encryptor.decrypt(encryptedValue);
            } catch (Exception e) {
                logger.warn("SecureConfigManager: 無法解密配置項 " + key + "，使用原始值");
                return encryptedValue;
            }
        }
        
        return encryptedValue;
    }
    
    /**
     * 檢查是否為敏感配置項
     */
    private boolean isSensitiveProperty(String key) {
        for (String sensitiveKey : SENSITIVE_KEYS) {
            if (key.equals(sensitiveKey)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 獲取整型配置項
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            logger.warn("SecureConfigManager: 配置項 " + key + " 不是有效的整數，使用預設值: " + defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 獲取布林值配置項
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        if (value == null) {
            return defaultValue;
        }
        
        return Boolean.parseBoolean(value);
    }
    
    /**
     * 重新載入配置
     */
    public void reloadConfiguration() {
        logger.info("SecureConfigManager: 開始重新載入配置");
        loadConfiguration();
        logger.info("SecureConfigManager: 配置重新載入完成");
    }
    
    /**
     * 獲取所有配置項名稱（除敏感資訊外）
     */
    public java.util.Set<String> getPropertyNames() {
        lock.readLock().lock();
        try {
            return new java.util.HashSet<>(properties.stringPropertyNames());
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 檢查配置項是否存在
     */
    public boolean hasProperty(String key) {
        return getProperty(key) != null;
    }
    
    /**
     * 獲取當前環境設定
     */
    public String getEnvironment() {
        return getProperty("system.mode", "production");
    }
    
    /**
     * 檢查是否為開發模式
     */
    public boolean isDevelopmentMode() {
        return "development".equals(getEnvironment());
    }
    
    /**
     * 檢查是否為生產模式
     */
    public boolean isProductionMode() {
        return "production".equals(getEnvironment());
    }
    
    /**
     * 獲取資料庫連線配置
     */
    public DatabaseConfig getPrimaryDatabaseConfig() {
        return new DatabaseConfig(
            getProperty("DBConn.url"),
            getProperty("DBConn.user"),
            getDecryptedProperty("DBConn.password"),
            getIntProperty("DBConn.maxconn", 20),
            getIntProperty("DBConn.timeout", 300)
        );
    }
    
    /**
     * 獲取次要資料庫連線配置
     */
    public DatabaseConfig getSecondaryDatabaseConfig() {
        return new DatabaseConfig(
            getProperty("DBConn2.url"),
            getProperty("DBConn2.user"),
            getDecryptedProperty("DBConn2.password"),
            getIntProperty("DBConn2.maxconn", 10),
            getIntProperty("DBConn2.timeout", 300)
        );
    }
    
    /**
     * 資料庫配置物件
     */
    public static class DatabaseConfig {
        private final String url;
        private final String username;
        private final String password;
        private final int maxConnections;
        private final int timeout;
        
        public DatabaseConfig(String url, String username, String password, int maxConnections, int timeout) {
            this.url = url;
            this.username = username;
            this.password = password;
            this.maxConnections = maxConnections;
            this.timeout = timeout;
        }
        
        public String getUrl() { return url; }
        public String getUsername() { return username; }
        public String getPassword() { return password; }
        public int getMaxConnections() { return maxConnections; }
        public int getTimeout() { return timeout; }
    }
}