# IM51001 月報表系統資料撈取參考指南

## 📋 文件資訊
- **建立日期**: 2025-07-22
- **系統版本**: 違章建築管理系統 Legacy版
- **適用範圍**: 月報表資料查詢與分析
- **維護人員**: 系統管理員

## 🎯 系統概述

### 功能定位
IM51001是違章建築管理系統的月報表功能，負責產生各科別的違建統計報表，支援Excel (xlsx)、ODS等格式輸出。

### 架構組成
- **前端介面**: `im51001_lis.jsp` (查詢條件設定)
- **輸出處理**: `im51001_prt.jsp` (檔案產生與下載)
- **核心邏輯**: `IM51001.java` (資料查詢與處理)
- **報表範本**: `im51001.jasper`, `im51001_2.jasper` (JasperReports)

## 🗄️ 核心資料表結構

### ibmmnrp (月報彙整表)
```sql
-- 主要欄位結構
t_yymm        VARCHAR   -- 統計年月 (民國年+月，如'11407'=民國114年7月)
zon_desc      VARCHAR   -- 行政區名稱 (29個新北市行政區)
zon_order     INTEGER   -- 行政區排序
kind          CHAR(1)   -- 違建類型代碼
sum_01 ~ sum_43 NUMERIC -- 43項統計數據欄位

-- 主鍵組合
PRIMARY KEY (t_yymm, zon_desc, kind)
```

### 違建類型代碼 (kind欄位)
| 代碼 | 科別名稱 | 說明 |
|------|----------|------|
| A | 認定科 | 一般違建認定相關統計 |
| B | 拆除科 | 違建拆除作業相關統計 |
| C | 廣告科 | 廣告違建相關統計 |
| D | 勞安科 | 下水道等特定違建統計 |
| E | 總表 | 全科別彙總統計 |
| F | 內控報表 | 內部控制相關統計 |

### Kind='A'(認定科) 統計欄位詳細對應
| 欄位名稱 | 業務含義 | SQL條件邏輯 |
|----------|----------|------------|
| sum_01 | 公所查報案件數 | case_ori = '1' |
| sum_02 | 非公所查報案件數 | case_ori != '1' |
| **sum_03** | **A類違建數** | dis_type = 'A' |
| **sum_04** | **B類違建數** | dis_type = 'B' |
| **sum_05** | **C類違建數** | dis_type = 'C' |
| sum_06 | D類合法案件數 | dis_type = 'D' AND reg_rsult != '02' |
| sum_07 | D類不合法案件數 | dis_type = 'D' AND reg_rsult = '02' |
| sum_08 | 總認定件數 | 全部案件 |

**重要說明**: 
- **ABCD是指違建類型(dis_type)，非科別代碼(kind)**
- A類：一般違建、B類：廣告違建、C類：其他違建、D類：疑似違建(需認定)
- sum_09~sum_43 涵蓋前一年認定本月拆除、本月認定本月拆除、他月認定本月拆除等複雜統計維度

## 📊 資料撈取範例

### 1. 正確的違建類型統計查詢
```sql
-- 查詢認定科(kind='A')的各類違建數量分布
SELECT 
    zon_desc AS 行政區,
    sum_03 AS A類違建數_一般違建,
    sum_04 AS B類違建數_廣告違建, 
    sum_05 AS C類違建數_其他違建,
    sum_06 + sum_07 AS D類違建數_疑似違建,
    sum_08 AS 總認定件數
FROM ibmmnrp 
WHERE t_yymm = '11407'  -- 民國114年7月
  AND kind = 'A'        -- 認定科統計
ORDER BY zon_order;
```

### 2. 客戶需求：行政區|A類總數量|C+D類總數量
```sql
-- 注意：此處的A類、C+D類是指科別，需要查看對應的kind值
-- 如果客戶要求的是：
-- A類總數量 = 認定科統計 (kind='A')
-- C+D類總數量 = 廣告科+勞安科統計 (kind='C'+kind='D')

SELECT 
    zon_desc AS 行政區,
    -- A類總數量：取認定科的總認定件數
    MAX(CASE WHEN kind = 'A' THEN sum_08 ELSE 0 END) AS A類總數量,
    -- C+D類總數量：廣告科+勞安科的總件數
    (MAX(CASE WHEN kind = 'C' THEN sum_08 ELSE 0 END) + 
     MAX(CASE WHEN kind = 'D' THEN sum_08 ELSE 0 END)) AS CD類總數量
FROM ibmmnrp 
WHERE (
    (t_yymm >= '11107' AND t_yymm <= '11112') OR  -- 111年7-12月
    (t_yymm >= '11201' AND t_yymm <= '11206')     -- 112年1-6月
)
  AND kind IN ('A', 'C', 'D')
GROUP BY zon_desc, zon_order
ORDER BY zon_order;
```

### 3. 全科別彙總查詢
```sql
-- 查詢E類(總表)資料
SELECT 
    zon_desc AS 行政區,
    sum_01 AS 總案件數,
    sum_02 AS 總處理數,
    sum_03 AS 總結案數
FROM ibmmnrp 
WHERE t_yymm = '11407'
  AND kind = 'E'
ORDER BY zon_order;
```

### 4. 月份趨勢查詢
```sql
-- 特定行政區的月份趨勢
SELECT 
    t_yymm AS 統計月份,
    kind AS 違建類型,
    sum_03 AS 違建數量
FROM ibmmnrp 
WHERE zon_desc = '板橋區'
  AND t_yymm >= '11101'  -- 民國111年1月起
  AND kind IN ('A', 'C', 'D')
ORDER BY t_yymm, kind;
```

## 🔧 系統參數與配置

### Session參數對應
```java
// im51001_prt.jsp 中的參數讀取
String s_t_yy = (String)session.getAttribute("SEARCHPARAM_1");  // 查詢年度
String s_t_mm = (String)session.getAttribute("SEARCHPARAM_2");  // 查詢月份  
String s_kind = (String)session.getAttribute("SEARCHPARAM_3");  // 科別代碼
String outExt = request.getParameter("o");                      // 輸出格式(EXL/ODS)
```

### 檔案命名規則
```java
// 輸出檔案命名邏輯
String outputFilename = "月報表-" + 科別名稱 + "_" + 時間戳記 + ".xlsx";
// 範例: 月報表-認定科_11407020930.xlsx
```

### 報表範本選擇
```java
if ("E".equals(s_kind)) {
    // 總表使用 im51001.jasper (54個Header欄位, 44個Detail欄位)
} else {
    // 各科別使用 im51001_2.jasper (41個Header欄位, 30個Detail欄位)
}
```

## 📈 資料分析要點

### 1. 時間格式轉換
```sql
-- 民國年轉西元年
SELECT 
    t_yymm,
    (CAST(SUBSTR(t_yymm,1,3) AS INTEGER) + 1911) AS 西元年,
    SUBSTR(t_yymm,4,2) AS 月份
FROM ibmmnrp;
```

### 2. 行政區排序
- 使用 `zon_order` 欄位控制行政區顯示順序
- 板橋區、三重區、新莊區等依照行政重要性排序

### 3. 空值處理
```sql
-- 避免空值影響統計
SELECT 
    zon_desc,
    COALESCE(sum_03, 0) AS 違建數量,
    CASE WHEN sum_03 IS NULL THEN '無資料' ELSE '有資料' END AS 資料狀態
FROM ibmmnrp;
```

### 4. 資料完整性檢查
```sql
-- 檢查特定月份是否有完整的29個行政區資料
SELECT 
    t_yymm,
    COUNT(DISTINCT zon_desc) AS 行政區數量,
    COUNT(*) AS 總記錄數
FROM ibmmnrp 
WHERE t_yymm = '11407'
GROUP BY t_yymm;
```

## 🚨 注意事項與限制

### 1. 資料更新頻率
- 月報表通常在次月初更新
- 最新資料可能有1-2天延遲

### 2. 效能考量
- `ibmmnrp` 表資料量大，建議使用適當的WHERE條件
- 時間範圍查詢建議不超過12個月

### 3. 科別統計邏輯說明
- **kind='A'(認定科)**: 
  - sum_03: A類違建(一般違建)
  - sum_04: B類違建(廣告違建)  
  - sum_05: C類違建(其他違建)
  - sum_08: 總認定件數
- **kind='C'(廣告科)**: 主要統計廣告違建相關數據
- **kind='D'(勞安科)**: 主要統計下水道等特定違建數據
- **kind='E'(總表)**: 為各科別彙總，避免重複計算

### 4. 常見錯誤與澄清
```sql
-- ❌ 錯誤理解：以為ABCD是kind欄位的分類
SELECT SUM(sum_03) FROM ibmmnrp WHERE kind = 'A';  -- 這不是A類違建總數

-- ✅ 正確理解：ABCD是dis_type違建類型，在kind='A'中的分佈
SELECT 
    sum_03 AS A類違建_一般違建,
    sum_04 AS B類違建_廣告違建,  
    sum_05 AS C類違建_其他違建
FROM ibmmnrp WHERE kind = 'A';  -- 認定科的各類違建統計

-- ❌ 錯誤：混淆科別代碼與違建類型
-- A類 ≠ kind='A'，A類是指dis_type='A'

-- ✅ 正確：區分概念
-- kind='A': 認定科的統計數據
-- sum_03: 認定科統計的A類違建(dis_type='A')數量
```

## 📋 快速參考清單

### 常用查詢模板
```sql
-- 1. 最新月份資料
SELECT * FROM ibmmnrp WHERE t_yymm = (SELECT MAX(t_yymm) FROM ibmmnrp);

-- 2. 特定科別年度統計
SELECT zon_desc, SUM(sum_03) FROM ibmmnrp 
WHERE SUBSTR(t_yymm,1,3) = '114' AND kind = 'A' 
GROUP BY zon_desc;

-- 3. 檢查資料完整性
SELECT t_yymm, kind, COUNT(*) FROM ibmmnrp 
WHERE t_yymm >= '11401' 
GROUP BY t_yymm, kind ORDER BY t_yymm, kind;
```

### 相關檔案路徑
```
/im51001_lis.jsp          # 查詢介面
/im51001_prt.jsp          # 輸出處理
/WEB-INF/java/com/ezek/report/IM51001.java      # 核心邏輯
/report/im51001.jasper    # 總表範本
/report/im51001_2.jasper  # 科別範本
```

## 🔗 相關文件
- [系統架構概覽](SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫完整指南](DATABASE_COMPLETE_GUIDE.md) 
- [業務流程指南](BUSINESS_PROCESS_COMPLETE_GUIDE.md)

---
**維護說明**: 本文件應隨系統異動同步更新，特別是資料表結構或統計邏輯變更時。