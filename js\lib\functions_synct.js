/**
 * @author: unknown
 * @created: 09/29/2005
 * @modifier: <PERSON> (<EMAIL>)
 * @updated: 11/30/2006
 *
 * @Comment: (03/23/2006) add function named listboxchain_uni, for GIS usage only.
 *
 *           (09/16/2006) modified listboxchain_uni_export(s) to accommodate the ListBox with no 
 *                        default selection style.
 *
 *           (09/26/2006) modified listboxchain_export(s) to accommodate the ListBox with no 
 *                        default selection style.
 *
 *           (10/25/2006) make listboxchain & listboxchain to accommodate the ListBox with no 
 *                        default selection style more robust.
 *
 *           (11/30/2006) sam的版本與公司共同版本合為一個版本...Tim 
 *
 *           (04/14/2009) add Ajax  Prototype JavaScript framework, version 1.5.0
 *
 *           (01/18/2010) modified listboxchain_export(s) to clear out the original listbox options
 *                        before making the new options.
 *
 *           (##/##/####) <Enter the comment here>
 **/

var isNN = (navigator.appName.indexOf("Netscape") != -1);
var isIE = (navigator.appName.indexOf("Microsoft") != -1);
var IEVersion = (isIE ? getIEVersion() : 0);
var NNVersion = (isNN ? getNNVersion() : 0);

if (window.XMLHttpRequest){
	XmlsReq = new XMLHttpRequest();
}else if (window.ActiveXObject){
	try {
		XmlsReq = new ActiveXObject("Msxml2.XMLHTTP");
	} catch(e){
		XmlsReq = new ActiveXObject("Microsoft.XMLHTTP");
	}
}

// Start listboxchain JScript source code
function listboxchain(FORMNAME, TOLISTBOX, QUERYTABLENAME, WHERESTRING, ORDERBYSTRING, BOUNDFIELD, TEXTFIELD, ONCHANGENAME) {
  str = "";
  str = "function_listboxchain.jsp?FORMNAME=" + FORMNAME + "&TOLISTBOX=" + TOLISTBOX + "&QUERYTABLENAME=" + QUERYTABLENAME + "&WHERESTRING=" + WHERESTRING + "&ORDERBYSTRING=" + ORDERBYSTRING + "&BOUNDFIELD=" + BOUNDFIELD + "&TEXTFIELD=" + TEXTFIELD + "&ONCHANGENAME=" + ONCHANGENAME;
	if (!XmlsReq) return;
	XmlsReq.open('GET',str);
	XmlsReq.send(null);
	XmlsReq.onreadystatechange = function(){
	  if (XmlsReq.readyState == 4 && XmlsReq.status == 200){
		  listboxchain_export(XmlsReq.responseText);
		}
	}  
  //select_obj.startDownload(str, listboxchain_export);
}

function listboxchain_export(s) {
	s1_array = s.split(",");
	
	s_array = new Array();
	
	for (i = 1; i < (s1_array.length - 1); i++) {
		s_array[i-1] = s1_array[i];
	}
	
	// get form name
	formname = s_array[0];
	
	// get ListBox name
	tolistboxname = s_array[1];
	
	// get onChange (function) name
	onchangename = s_array[2];
	
	tolistboxid = new Object();
	
	onchangeobject = new Object();
	
	tolistboxid.control = String("document." + formname + "." + tolistboxname);
	tolistbox = eval(tolistboxid.control);
	
	var listBoxOriginal1stValue = "";
	var listBoxOriginalLength = tolistbox.length;
	
	if (listBoxOriginalLength > 0) {
		listBoxOriginal1stValue = tolistbox.options[0].value;
	}
	
	// ==================== START clearing all Option(s) ====================
	if (listBoxOriginalLength > 0) {
		if (listBoxOriginal1stValue == "") {
			// clear all Options except first item - select values... 
			//for (i = 1; i < tolistbox.length; i++) {
			//	tolistbox.options[i] = new Option();
			//}
			for (i = (tolistbox.length - 1); i > 0; i--) {
				tolistbox.options[i] = null;
			}
		} else {
			// clear all Options
			//for (i = 0; i < tolistbox.length; i++) {
			//	tolistbox.options[i] = new Option();
			//}
			for (i = (tolistbox.length - 1); i >= 0; i--) {
				tolistbox.options[i] = null;
			}
		}
		
		try {
			tolistbox.options.length = 0;
		} catch (e) {
			// do not output anything
		}
	}
	// ====================  END clearing all Option(s)  ====================
	
	// ==================== START resetting Option(s) value(s) ====================
	if (listBoxOriginalLength > 0) {
		
		if (listBoxOriginal1stValue == "") {
			// to clean the previous option(s) occupied space
			tolistbox.length = 1;
			
			// reset Option(s) value(s) from the 2nd option
			for (i = 0; i < ((s_array.length-3)/2); i++) {
				tolistbox.options[i + 1] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
			}
		} else {
			// reset Option(s) value(s) from the 1st option
			for (i = 0; i < ((s_array.length-3)/2); i++) {
				tolistbox.options[i] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
			}
		}
	} else {
		// reset Option(s) value(s) from the 1st option
		for (i = 0; i < ((s_array.length-3)/2); i++) {
			tolistbox.options[i] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
		}
	}
	// ====================  END resetting Option(s) value(s)  ====================
	
	if ((onchangename != undefined) && (onchangename != "undefined") && (onchangename.length > 0)) {
		if (onchangename.indexOf("(") > 0){
			onchangeobject.control = String("window." + onchangename);
		} else {
			onchangeobject.control = String("window." + onchangename + "()");
		}
		
		onchangecontrol = eval(onchangeobject.control);  
	}

}
// End listboxchain JScript source code

// -------------------- START listboxchain_uni JScript source code --------------------
/**
 * Reading user's input to alternate the destinated listbox
 *
 * @param FORMNAME: the HTML form name
 * @param TOLISTBOX: the name of destination of listbox 
 * @param ROAD1NAME: the name of road that would intersect with
 * @param ROADXNUMBER: the total possible number of road would intersect
 **/
function listboxchain_uni(FORMNAME, TOLISTBOX, ROAD1NAME, ROADXNUMBER) {
	str = "";
	str = "function_listboxchain_uni.jsp?";
	str = str + "FORMNAME=" + FORMNAME;
	str = str + "&TOLISTBOX=" + TOLISTBOX;
	str = str + "&ROAD1NAME=" + ROAD1NAME;
	str = str + "&ROADXNUMBER=" + ROADXNUMBER;
	if (!XmlsReq) return;
	XmlsReq.open('GET',str);
	XmlsReq.send(null);
	XmlsReq.onreadystatechange = function(){
	  if (XmlsReq.readyState == 4 && XmlsReq.status == 200){
		  listboxchain_uni_export(XmlsReq.responseText);
		}
	}  

	//select_obj.startDownload(str, listboxchain_uni_export);
}

/**
 * Writing result to destinated listbox
 *
 * @param s: the return string from function_listboxchain_uni
 **/
function listboxchain_uni_export(s) {
	s1_array = s.split(",");
	
	s_array = new Array();
	
	for (i = 1; i < (s1_array.length - 1); i++) {
		s_array[(i - 1)] = s1_array[i];
	}
	
	// get form name
	formname = s_array[0];
	
	// get ListBox name
	tolistboxname = s_array[1];
	
	tolistboxid = new Object();
	
	tolistboxid.control = String("document." + formname + "." + tolistboxname);
	tolistbox = eval(tolistboxid.control);
	
	var listBoxOriginal1stValue = "";
	var listBoxOriginalLength = tolistbox.length;
	
	if (listBoxOriginalLength > 0) {
		listBoxOriginal1stValue = tolistbox.options[0].value;
	}
	
	// ==================== START clearing all Option(s) ====================
	if (listBoxOriginalLength > 0) {
		if (listBoxOriginal1stValue == "") {
			// clear all Options except first item - select values... 
			for (i = 1; i < tolistbox.length; i++) {
				tolistbox.options[i] = new Option();
			}
		} else {
			// clear all Options
			for (i = 0; i < tolistbox.length; i++) {
				tolistbox.options[i] = new Option();
			}
		}
	}
	// ====================  END clearing all Option(s)  ====================
	
	// ==================== START resetting Option(s) value(s) ====================
	if (listBoxOriginalLength > 0) {
		
		if (listBoxOriginal1stValue == "") {
			// to clean the previous option(s) occupied space
			tolistbox.length = 1;
			
			// reset Option(s) value(s) from the 2nd option
			for (i = 0; i < ((s_array.length-3)/2); i++) {
				tolistbox.options[i + 1] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
			}
		} else {
			// reset Option(s) value(s) from the 1st option
			for (i = 0; i < ((s_array.length-3)/2); i++) {
				tolistbox.options[i] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
			}
		}
	} else {
		// reset Option(s) value(s) from the 1st option
		for (i = 0; i < ((s_array.length-3)/2); i++) {
			tolistbox.options[i] = new Option(s_array[(i*2)+4], s_array[(i*2)+3]);
		}
	}
	// ====================  END resetting Option(s) value(s)  ====================
}
// --------------------  END listboxchain_uni JScript source code  --------------------

//listboxchain_adv JScript source code
function listboxchain_adv(FORMNAME, TOLISTBOX, QUERYTABLENAME, WHERESTRING, ORDERBYSTRING, BOUNDFIELD, TEXTFIELD, ONCHANGENAME) {
  str = "";
  str = "function_listboxchain_adv.jsp?FORMNAME=" + FORMNAME + "&TOLISTBOX=" + TOLISTBOX + "&QUERYTABLENAME=" + QUERYTABLENAME + "&WHERESTRING=" + WHERESTRING + "&ORDERBYSTRING=" + ORDERBYSTRING + "&BOUNDFIELD=" + BOUNDFIELD + "&TEXTFIELD=" + TEXTFIELD + "&ONCHANGENAME=" + ONCHANGENAME;
	if (!XmlsReq) return;
	XmlsReq.open('GET',str);
	XmlsReq.send(null);
	XmlsReq.onreadystatechange = function(){
	  if (XmlsReq.readyState == 4 && XmlsReq.status == 200){
		  listboxchain_adv_export(XmlsReq.responseText);
		}
	}  
  //select_obj.startDownload(str, listboxchain_adv_export);
}

function listboxchain_adv_export(s) {
  s1_array = s.split(",");
  s_array = new Array();
  for (i=1; i<s1_array.length-1; i++) {
   s_array[i-1] = s1_array[i];
  }
  formname = s_array[0];
  tolistboxname = s_array[1];
  onchangename  = s_array[2];
  tolistboxid    = new Object();
  onchangeobject = new Object();
  tolistboxid.control = String("document." + formname + "." + tolistboxname);
  tolistbox = eval(tolistboxid.control);
  //clear all Options except first item - select values... 
  for (i=1; i < tolistbox.length; i++) {
   tolistbox.options[i] = new Option();
  }
  tolistbox.length = 1;
  //reset option values from second item 
  for (i=0; i<((s_array.length-3)/2); i++) {
   tolistbox.options[i+1] = new Option(s_array[(i*2)+4],s_array[(i*2)+3]);
  }
  if (onchangename != undefined && onchangename != "undefined" && onchangename.length > 0){
  	if (onchangename.indexOf("(") > 0){
            onchangeobject.control          = String("window." + onchangename);
  	}else{
            onchangeobject.control          = String("window." + onchangename + "()");
  	}
        onchangecontrol = eval(onchangeobject.control);  
  }   
  
}
//End listboxchain_adv JScript source code


//getdbfields JScript source code
function getdbfields(FORMNAME, FIELDNAME, QUERYTABLENAME, WHERESTRING) {
  str = "";
  str = "function_getdbfields.jsp?FORMNAME=" + FORMNAME + "&FIELDNAME=" + FIELDNAME + "&QUERYTABLENAME=" + QUERYTABLENAME+ "&WHERESTRING=" + WHERESTRING ;
	if (!XmlsReq) return;
	XmlsReq.open('GET',str);
	XmlsReq.send(null);
	XmlsReq.onreadystatechange = function(){
	  if (XmlsReq.readyState == 4 && XmlsReq.status == 200){
		  getdbfields_export(XmlsReq.responseText);
		}
	}  
  //select_obj.startDownload(str, getdbfields_export);
}

function getdbfields_export(s) {
  s_array = s.split(",");
  var fieldcount = parseInt(s_array[1]);
  formname = s_array[2];
  totextid = new Object();
  for (i = 1;i <= fieldcount ; i++){
      totextid.control = String("document." + formname + "." + s_array[i + 2]);
      totext = eval(totextid.control);
      //export text
      totext.value = s_array[i + fieldcount + 2];
  }
}
//getdbfieldsv JScript source code
function getdbfieldsv(FORMNAME, FIELDNAME, QUERYTABLENAME, WHERESTRING,ONCHANGENAME) {
  str = "";
  str = "function_getdbfieldsv.jsp?FORMNAME=" + FORMNAME + "&FIELDNAME=" + FIELDNAME + "&QUERYTABLENAME=" + QUERYTABLENAME+ "&WHERESTRING=" + WHERESTRING + "&ONCHANGENAME=" + ONCHANGENAME ;
	if (!XmlsReq) return;
	XmlsReq.open('GET',str);
	XmlsReq.send(null);
	XmlsReq.onreadystatechange = function(){
	  if (XmlsReq.readyState == 4 && XmlsReq.status == 200){
		  getdbfieldsv_export(XmlsReq.responseText);
		}
	}  
  //select_obj.startDownload(str, getdbfieldsv_export);
}

function getdbfieldsv_export(s) {
  s_array = s.split(",");
  var fieldcount = parseInt(s_array[1]);
  var onchangefname = s_array[2];
  formname = s_array[3];
  totextid = new Object();
  for (i = 1;i <= fieldcount ; i++){
      totextid.control = String("document." + formname + "." + s_array[i + 3]);
      totext = eval(totextid.control);
      //export text
      totext.value = s_array[i + fieldcount + 3];
  }
  if (onchangefname != undefined && onchangefname != "undefined" && onchangefname.length > 0){
  	if (onchangefname.indexOf("(") > 0){
            totextid.control          = String("window." + onchangefname);
  	}else{
            totextid.control          = String("window." + onchangefname + "()");
  	}
        totextid = eval(totextid.control);  
  }   
}

//EraseChar JScript source code
/**************************************************
* 函  式: EraseChar                               *
* 說  明: 將字串中的指定字元全部去除              * 
*         例如將yyyy/mm/dd中的'/'去除為yyyymmdd   *
* 參  數: s - 原字串                              *      
*         c - 欲去除字元                          *
* 傳回值: 處理後字串                              *
**************************************************/
function EraseChar(s, c) {
  var pos, val, rtn_str = ""; 
  for (pos = 0; pos < s.length; pos++) {  
    val = s.charAt(pos);
    if (val != c) {
      rtn_str = rtn_str + val;     
    } 
  }
  return rtn_str;
}
//End EraseChar JScript source code

//RSplitChar JScript source code
/**************************************************
* 函  式: RSplitChar                              *
* 說  明: 以指定分隔字元將字串從右側逐一插入分隔  * 
*         例如以'/'字元將yyyymmdd處理為 yyyy/mm/dd*
* 參  數: s - 原字串                              *      
*         c - 插入分隔字元                        *
*         ps - 分隔字元數序列, 以','間隔.         *
*              例如 "2,3" 表於從右數過來2個字元前 *
*              插入分隔字元, 接下來再數3個字元前  *
*              再插入分隔字元.                    *
* 傳回值: 處理後字串                              *
**************************************************/
function RSplitChar(s, c, ps) { 
  var rtn_str = ""; 
  var ps_array = new Array();  
  ps_array = ps.split(',');    
  var remlen = s.length, starinx = 0, currlen = 0;  
  var pos = 0;
  while (pos < ps_array.length) {   	
    currlen = ps_array[pos];    
	starinx = remlen - currlen;    
	if (starinx < 0) {
	  starinx = 0;
	  currlen = remlen;
	}
	if (rtn_str != "" && currlen > 0) rtn_str = c + rtn_str;	
    rtn_str = s.substr(starinx, currlen) + rtn_str;	
	remlen = remlen - currlen;
    pos++;
  }  
  if (remlen > 0) {
    if (rtn_str != "") rtn_str = c + rtn_str;	
    rtn_str = s.substr(0, remlen) + rtn_str;	
  }
  return rtn_str;
}
//End RSplitChar JScript source code

//LSplitChar JScript source code
/**************************************************
* 函  式: LSplitChar                              *
* 說  明: 以指定分隔字元將字串從左側逐一插入分隔  * 
*         例如以'/'字元將yyyymmdd處理為 yyyy/mm/dd*
* 參  數: s - 原字串                              *      
*         c - 插入分隔字元                        *
*         ps - 分隔字元數序列, 以','間隔.         *
*              例如 "2,3" 表於從左數過來2個字元後 *
*              插入分隔字元, 接下來再數3個字元後  *
*              再插入分隔字元.                    *
* 傳回值: 處理後字串                              *
**************************************************/
function LSplitChar(s, c, ps) { 
  var str      = s; 
  var rtn_str  = ""; 
  var temp_str = ""; 
  var ps_array = new Array();  
  ps_array = ps.split(',');    
  var remlen = str.length, starinx = 0, currlen = 0;  
  var pos = 0;
  //alert("s=" + s);
  //alert("str=" + str);
  while (pos < ps_array.length) {   	
    currlen = ps_array[pos];    
	//starinx = remlen - currlen;    
	//if (starinx < 0) {
	//  starinx = 0;
	//  currlen = remlen;
	//}
    temp_str = str.substr(starinx, currlen);	
    str      = str.substr(currlen, str.length);
    //alert("temp_str=" + temp_str);
    //alert("str=" + str);
    //alert("currlen=" + currlen);
    if (temp_str.length == currlen && currlen > 0){
    	 temp_str = temp_str + c;	
    }
    rtn_str = rtn_str + temp_str;
    pos++;
  }  
  if (remlen > 0 && rtn_str == "") {
    rtn_str = str.substr(0, remlen);	
  }else{
    rtn_str = rtn_str + str;	
  }
  return rtn_str;
}
//End LSplitChar JScript source code

//IsDate_AD JScript source code
/**************************************************
* 函  式: IsDate_AD(停用)                         *
* 說  明: 檢查字串是否為合法的西元年(4位數年, 2位 * 
*         數月, 2位數日), 分隔符號可為'.', '-' 或 *
*         '/'. 例如 yyyy/mm/dd 或 yyyy-mm-dd 或   *
*         yyyy.mm.dd                              *
* 參  數: strValue - 要檢查的字串                 *
* 傳回值: true - 合法的西元年                     *
*         false - 不合法的西元年                  *
**************************************************/  
function IsDate_AD(strValue) {
  if (strValue == "") return true;
  var objRegExp = /^\d{4}(\-|\/|\.)\d{2}\1\d{2}$/
  //check to see if in correct format
  if(!objRegExp.test(strValue)) {
    return false; //doesn't match pattern, bad date
  } else{
    var strSeparator = strValue.substring(4,5) //find date separator
    var arrayDate = strValue.split(strSeparator); //split date into month, day, year
    //create a lookup for months not equal to Feb.
    var arrayLookup = { '01' : 31,'03' : 31, '04' : 30,'05' : 31,'06' : 30,'07' : 31,'08' : 31,'09' : 30,'10' : 31,'11' : 30,'12' : 31}
   	var intDay = parseInt(arrayDate[2]);
    //check if month value and day value agree
    if(arrayLookup[arrayDate[1]] != null) {
      if(intDay <= arrayLookup[arrayDate[1]] && intDay != 0)
        return true; //found in lookup table, good date
    }
    //check for February	
    var intMonth = parseInt(arrayDate[1]);
    if (intMonth == 2) { 
       var intYear = parseInt(arrayDate[0]);
       if( ((intYear % 4 == 0 && intDay <= 29) || (intYear % 4 != 0 && intDay <=28)) && intDay !=0) return true; //Feb. had valid number of days
    }
  }
  return false; //any other values, bad date
}
//End IsDate_AD JScript source code

//IsDate JScript source code
/**************************************************
* 函  式: IsDate                                  *
* 說  明: 檢查字串是否為合法的西元日期(分隔符號為 * 
*         '/', 如yyyy/mm/dd)                      * 
* 參  數: strValue - 要檢查的字串                 *
* 傳回值: true - 合法的西元日期                   *
*         false - 不合法的西元日期                *
**************************************************/  
function IsDate(strValue) {
  if (strValue == "") return true;
  var objRegExp = /^\d{1,4}\/\d{1,2}\/\d{1,2}$/  
  if (!objRegExp.test(strValue)) { //格式不正確, 非 1至4個數字 + '/' + 1至2個數字 + '/' + 1至2個數字 的組合
    return false;
  } else {    
    var arrayLookup = { '1':31, '3':31, '4':30, '5':31, '6':30, '7':31, '8':31, '9':30, '10':31, '11':30, '12':31}  //除2月外的月份與日數對照陣列  
    var arrayDate = strValue.split('/'); //分割日期值為年, 月, 日
    var intMonth = eval(arrayDate[1]);
    var intDay = eval(arrayDate[2]);     
    if (intMonth == 2) { //月份為2月, 須判斷年   
      var intYear = eval(arrayDate[0]);
      if( ((intYear % 4 == 0 && intDay <= 29) || (intYear % 4 != 0 && intDay <=28)) && intDay !=0) return true; //Feb. had valid number of days
    } else {      
      if (arrayLookup[""+intMonth] != null) {
        if (intDay != 0 && intDay <= arrayLookup[""+intMonth]) return true; //found in lookup table, good date
      }
    }     
    return false; //any other values, bad date 
  }  
}
//End IsDate JScript source code

//IsMonth JScript source code
/**************************************************
* 函  式: IsMonth                                 *
* 說  明: 檢查字串是否為合法的西元年月(分隔符號為 * 
*         '/', 如yyyy/mm)                         * 
* 參  數: strValue - 要檢查的字串                 *
* 傳回值: true - 合法的西元年月                   *
*         false - 不合法的西元年月                *
**************************************************/  
function IsMonth(strValue) {
  if (strValue == "") return true;
  var objRegExp = /^\d{1,4}\/\d{1,2}$/  
  if (!objRegExp.test(strValue)) { //格式不正確, 非 1至4個數字 + '/' + 1至2個數字
    return false;
  } else {
    var arrayDate = strValue.split('/');   //分割日期值為年, 月
    var intMonth = eval(arrayDate[1]);    
    if (intMonth >= 1 && intMonth <= 12) {
      return true; //good month
    }
    return false; //any other values, bad month 
  }  
}
//End IsMonth JScript source code

//IsCDate JScript source code
/**************************************************
* 函  式: IsCDate                                 *
* 說  明: 檢查字串是否為合法的民國日期(分隔符號為 * 
*         '/', 如yyy/mm/dd)                       * 
* 參  數: strValue - 要檢查的字串                 *
* 傳回值: true - 合法的民國日期                   *
*         false - 不合法的民國日期                *
**************************************************/  
function IsCDate(strValue) {
  if (strValue == "") return true;
  var objRegExp = /^\d{1,3}\/\d{1,2}\/\d{1,2}$/  
  if (!objRegExp.test(strValue)) { //格式不正確, 非 1至3個數字 + '/' + 1至2個數字 + '/' + 1至2個數字 的組合
    return false;
  } else {    
    var arrayLookup = { '1':31, '3':31, '4':30, '5':31, '6':30, '7':31, '8':31, '9':30, '10':31, '11':30, '12':31}  //除2月外的月份與日數對照陣列  
    var arrayDate = strValue.split('/'); //分割日期值為年, 月, 日
    var intMonth = eval(arrayDate[1]);
    var intDay = eval(arrayDate[2]);     
    if (intMonth == 2) { //月份為2月, 須判斷年   
      var intYear = eval(arrayDate[0]) + 1911;
      if( ((intYear % 4 == 0 && intDay <= 29) || (intYear % 4 != 0 && intDay <=28)) && intDay !=0) return true; //Feb. had valid number of days
    } else {      
      if (arrayLookup[""+intMonth] != null) {
        if (intDay != 0 && intDay <= arrayLookup[""+intMonth]) return true; //found in lookup table, good date
      }
    }     
    return false; //any other values, bad date 
  }  
}
//End IsCDate JScript source code

//IsCMonth JScript source code
/**************************************************
* 函  式: IsCMonth                                *
* 說  明: 檢查字串是否為合法的民國年月(分隔符號為 * 
*         '/', 如yyy/mm)                          * 
* 參  數: strValue - 要檢查的字串                 *
* 傳回值: true - 合法的民國年月                   *
*         false - 不合法的民國年月                *
**************************************************/  
function IsCMonth(strValue) {
  if (strValue == "") return true;
  var objRegExp = /^\d{1,3}\/\d{1,2}$/  
  if (!objRegExp.test(strValue)) { //格式不正確, 非 1至3個數字 + '/' + 1至2個數字    
    return false;
  } else {
    var arrayDate = strValue.split('/');   //分割日期值為年, 月
    var intMonth = eval(arrayDate[1]);
    if (intMonth >= 1 && intMonth <= 12) {
      return true; //good month
    }    
    return false; //any other values, bad month 
  }  
}
//End IsCMonth JScript source code

//CommaFormat JScript source code
/**************************************************
* 函  式: CommaFormat                             *
* 說  明: 將數字(具負號, 小數點亦可)插入千位字元  * 
* 參  數: num - 要處理的數字                      *
* 傳回值: 插入千位字元後的數字字串                *
**************************************************/  
function CommaFormat(num) { 
  var size = "";                 //正負號字串
  var n = "" + num;              //小數點前字串  
  var fract = "";                //小數點後(含)字串
  if (n.charAt(0) == '-') {
    size = "-";
    n = n.substring(1);    
  }  
  var scale_pos = n.indexOf('.');
  if (scale_pos > -1) {
    fract = n.substring(scale_pos); 
    n = n.substring(0, scale_pos-1);     
  }  
  var str = "";    
  for (i=1; i<=n.length; i++) {        
    if ((i-1) % 3 == 0) {
     if (i == 1) {
       str = n.charAt(n.length-i) + str;       
     } else {
       str = n.charAt(n.length-i) + ',' + str;
     }  
    } else {
     str = n.charAt(n.length-i) + str; 
    }     
  }   
  return size + str + fract;  
}
//End CommaFormat JScript source code

//fieldcontrol JScript source code
/******************************************************************
* 函  式: fieldcontrol                                            *
* 說  明: text,textarea欄位的長度, 允許字元等輸入控制             * 
* 參  數: checkobject - 要處理的物件(傳入this即可)                *
* 傳回值: 無                                                      *
* 備  註: 1.需配合自訂的fieldlength, fieldtype屬性使用            *
*         2.於onkeypress, onpropertychange, onpaste事件呼叫       *
*******************************************************************/  
function fieldcontrol(checkobject) { 
  var checkvalue = ""; 
  var clipvalue = ""; 
  switch(checkobject.fieldtype) { 
    //* text(文字) 型態
    case "text": if (checkobject.fieldlength != null) {  //需做長度檢查
                   checkvalue = checkobject.value; 
                   checkvalue = checkvalue.replace(/[^\x00-\xff]/g, "^^");                   
                   //** onKeyPress 事件處理程序
                   if (event.type == "keypress") { //輸入single byte charter才被觸發                     
                     if (checkvalue.length + 1 > checkobject.fieldlength) {  //checkvalue為不含新鍵入字元的值
                       with(window.event) {
                         cancelBubble = true;
                         keyCode = 0;
                         returnValue = false;
                       } 
                       alert("超過欄位長度限制, 不允許再輸入字元.");
                     }
                   }
                   //** 因有部份版本的IE, 其onKeyDown事件在onPropertyChange事件之後觸發, 所以此段程式碼不全適用, 註記作廢 **
                   //if (event.type == "keydown") { //英文數字, 中文字皆會觸發  
	           //  if (window.event.keyCode == 229) {
                   //    if (checkvalue.length + 2 > checkobject.fieldlength) {                          
                   //      with(window.event) {
                   //        cancelBubble = true;
                   //        keyCode = 0;
                   //        returnValue = false;
                   //      } 
	           //      alert("超過欄位長度限制, 不允許再輸入字元.");
	           //    }
                   //  }	
                   //}   
                   //** onPaste 事件處理程序                 
                   if (event.type == "paste") { 
	             clipvalue = clipboardData.getData("text");
    	             clipvalue = clipvalue.replace(/[^\x00-\xff]/g, "^^");		
	             if (checkvalue.length + clipvalue.length > checkobject.fieldlength) {
	               event.returnValue = false;          
	               alert("超過欄位長度限制, 貼上動作取消.");		 
	             } 		
                   }
                   //** onPropertyChange 事件處理程序    
                   if (event.type == "propertychange") {                     
                     if (checkvalue.length > checkobject.fieldlength) { //checkvalue為含新鍵入字元的值  
                       //程式為計算textarea型態欄位時游標位置的辨識字元                       
                       var c = "\001";                                                                       
                       //計算目前游標在第幾個字元後面(caret_pos, 一個中文字長度以1計, 換行字元(\r\n)以2計)  
                       var caret_pos = 0;                        
                       var range = document.selection.createRange();                        
                       if (checkobject.type == "textarea") {
                         if (checkobject.value.indexOf(c) > 0) return; //為加入程式辨識用字元所引起onPropertyChange, 則忽略
                         var dul = range.duplicate();
                         dul.moveToElementText(checkobject); 
                         range.text = c;  //此處加入程式辨識用字元會引起onPropertyChange被觸發
                         caret_pos = (dul.text.indexOf(c));
                         caret_pos++;     //額外加上辨識用字元的長度                         
                       } else {
                         var bookmark = range.getBookmark();
	                 caret_pos = bookmark.charCodeAt(2) - 2;                                            
                       } 
                       //計算要移除幾個字元(back_index, 一個中文字長度以1計)                        
                       var back_index = 0;                        
                       var before_str = checkobject.value.substring(0, caret_pos);                       
                       before_str = before_str.replace(/[^\x00-\xff]/g, "^^"); 
                       var check_pos = before_str.length - 1;
                       var excess_len = checkvalue.length - checkobject.fieldlength;
                       while (excess_len > 0) {
                         back_index++;
                         if (before_str.charAt(check_pos) == "^") { //檢查的位置為中文字
                           excess_len = excess_len - 2;
                           check_pos = check_pos - 2;
                         } else {                           
                           excess_len--;
                           check_pos--;
                         }                       
                       }                                              
                       //顯示警告訊息 
                       alert("超過欄位長度限制, 不允許再輸入字元.");  
                       //清除新鍵入超過的字元                       
                       if (before_str.charAt(before_str.length - 1) == "^") {                         
                         var dbc_count = 0;
                         check_pos = checkvalue.indexOf("^^");
                         while (check_pos >= 0) {
                           dbc_count++;                            
                           check_pos = checkvalue.indexOf("^^", check_pos+2);
                         }                                                   
                         if (checkobject.value.length == checkvalue.length - dbc_count) {
                           checkobject.value = checkobject.value.substring(0, caret_pos - back_index) + checkobject.value.substring(caret_pos, checkobject.value.length);
                         } else {    
                           checkobject.value = checkobject.value.substring(0, caret_pos - back_index) + checkobject.value.substring(caret_pos - 1, checkobject.value.length);
                         }
                       } else {                         
                         checkobject.value = checkobject.value.substring(0, caret_pos - back_index) + checkobject.value.substring(caret_pos, checkobject.value.length);
                       }
                     }                 
                   } 
                 }
	         break;
    //* number(數字) 型態
    case "number" : var number_size = 0;    //數字的總位數
                    var number_scale = 0;   //數字的小數位數
                    if (checkobject.fieldlength != null) {
                      if (checkobject.fieldlength.indexOf(".") > -1) {
                        number_size = parseInt((checkobject.fieldlength.split("."))[0]);
                        number_scale = parseInt((checkobject.fieldlength.split("."))[1]);
                      } else {
                        number_size = parseInt((checkobject.fieldlength.split("."))[0]);
                      }
                    }                    
                    //** onKeyPress 事件處理程序 : 檢查為合法的字元
                    if (event.type == "keypress") {
                      if (!(window.event.keyCode >= 48 && window.event.keyCode <= 57)) {	//非鍵入數字0~9
                        if (!(window.event.keyCode == 45)) {                                    //非鍵入負號'-'
                          if (number_scale > 0) {  //值有小數部份額外允許鍵入小數點'.'
                            if (!(window.event.keyCode == 46)) {                                //非鍵入小數點'.'
                              with(window.event) {
                                cancelBubble = true;
                                keyCode = 0;
                                returnValue = false;
                              } 
                            }
                          } else {
                            with(window.event) {
                              cancelBubble = true;
                              keyCode = 0;
                              returnValue = false;
                            } 
                          }
                        }
                      } 
                    }         
                    //** onPaste 事件處理程序 : 檢查欲貼上的內容皆為合法的字元 
                    if (event.type == "paste") {
                      clipvalue = clipboardData.getData("text");                      
                      clipvalue = clipvalue.replace(/[^\x00-\xff]/g, "^^");  
                      var minus_exit = false; 
                      var scale_exit = false;                                         
                      var pos = 0;  
                      while (pos < clipvalue.length) {   	
                        if (clipvalue.charAt(pos) >= '0' && clipvalue.charAt(pos) <= '9') {    //為數字0~9
                          pos++;
                        } else {
                          if (clipvalue.charAt(pos) == '-') {                                  //為負號'-'
                            if (minus_exit) {
                              event.returnValue = false;          
	                      alert("欲貼上內容包含多個負號, 貼上動作取消.");
                              break;
                            } else {
                              minus_exit = true;
                              pos++;
                            }                            
                          } else {
                            if (clipvalue.charAt(pos) == '.') {                               //為小數點'.'                                
                              if (number_scale > 0) {  //值有小數部份額外允許鍵入小數點'.'
                                if (scale_exit) {
                                  event.returnValue = false;          
	                          alert("欲貼上內容包含多個小數點, 貼上動作取消.");
                                  break;
                                } else {
                                  scale_exit = true;
                                  pos++;
                                }
                              } else {
                                event.returnValue = false;          
	                        alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                                break;
                              } 
                            } else {
                              event.returnValue = false;          
	                      alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                              break;
                            }
                          }
                        }
                      }  // end of while
                    }
                    //** onPropertyChange 事件處理程序 : 檢查字串的長度與合理性   
                    if (event.type == "propertychange") {                      
                      //計算目前游標在第幾個字元後面(caret_pos, 一個中文字長度以1計)                     
                      var range = document.selection.createRange();
                      var bookmark = range.getBookmark();
	              var caret_pos = bookmark.charCodeAt(2) - 2;
                      //暫存目前游標後有幾個字元(focus_dis), 作為回復游標位置之用
                      var focus_dis = checkobject.value.length - caret_pos;
                      //游標後字串(after_str)
                      var after_str = checkobject.value.substring(caret_pos, checkobject.value.length);
                      after_str = after_str.replace(/[^\x00-\xff]/g, "^^");
                      //判斷游標後字串小數點位置(after_has_dec, -1表不存在)
                      var after_has_dec = after_str.indexOf(".");   
                      //判斷游標後字串負號位置(after_has_minus, -1表不存在)
                      var after_has_minus = after_str.indexOf("-");                                         
                      //游標前字串(before_str)
                      var before_str = checkobject.value.substring(0, caret_pos);
                      before_str = before_str.replace(/[^\x00-\xff]/g, "^^"); 
                      //檢查游標前字串(before_str)從頭至第幾個字元為合法字元(legality_pos), 小數點位置(before_has_dec, -1表不存在)與負號位置(before_has_minus, -1表不存在)                     
                      var before_has_dec = -1;
                      var before_has_minus = -1;
                      var legality_pos = 0;
                      while (legality_pos < before_str.length) {
                        if (after_has_minus > -1) { //若游標後字串已有負號, 則游標前不允許再輸入任何字元
                          break;
                        } else {   
                          if (after_has_dec > -1) {   //若游標後字串已有小數點
                            if (legality_pos == 0) {       //若正檢查第一個字元, 則字元可為數字0~9或'-'
                              if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                legality_pos++;
                              } else {
                                if (before_str.charAt(legality_pos) == '-') {
                                  before_has_minus = legality_pos;
                                  legality_pos++;
                                } else {                        
                                  break;
                                }
                              }
                            } else {                       //其他位置的字元僅能為數字0~9
                              if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                legality_pos++;
                              } else {
                                break;
                              }
                            }
                          } else {                    //若游標後字串無小數點
                            if (legality_pos == 0) {      //若正檢查第一個字元, 則字元可為數字0~9或'-'
                              if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                legality_pos++;
                              } else {
                                if (before_str.charAt(legality_pos) == '-') {
                                  before_has_minus = legality_pos;
                                  legality_pos++;
                                } else {
                                  break;
                                }
                              }                                    
                            } else {                      //其他位置的字元僅能為數字0~9, '.'則有條件允許
                              if (before_has_dec > -1) {       //檢查過程中已發現小數點, 則後續字元僅能為數字0~9                               
                                if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                  legality_pos++;
                                } else {                              
                                  break;
                                }
                              } else {
                                if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                  legality_pos++;
                                } else {
                                  if (before_str.charAt(legality_pos) == '.') { //檢查過程中第一次發現小數點
                                    before_has_dec = legality_pos;
                                    if (number_scale > 0) {                         //指定格式為具小數部份, ','才為合法字元                                 
                                      legality_pos++;
                                    } else {
                                      break;
                                    } 
                                  } else {
                                    break;
                                  }                                
                                }
                              }                          
                            }
                          }
                        }
                      }  //end of while 
                      //若游標前字串(before_str)含不合法字元, 則重置其為從頭至合法字元(legality_pos)的字串, 並將旗標(before_str_modify)開啟
                      var before_str_modify = false;
                      if (legality_pos != before_str.length) {  
                        before_str_modify = true;                      
                        //檢查總長度是否超過, 若超過則legality_pos需再縮減
                        var temp_length = legality_pos + after_str.length;
                        if (before_has_dec > -1 || after_has_dec > -1) temp_length = temp_length--;      //若有小數點, 長度需減1
                        if (before_has_minus > -1 || after_has_minus > -1) temp_length = temp_length--;  //若有負號, 長度需再減1 
                        if (temp_length > number_size) {
                          legality_pos = number_size - after_str.length;
                          if (before_has_minus > -1) legality_pos++;
                          if (after_has_dec > -1) {
                            legality_pos++;
                          } else {
                            if (before_has_dec > -1 && legality_pos >= before_has_dec + 1) {
                              legality_pos++;
                            } else {
                              before_has_dec = -1;
                            }                    
                          }
                        }
                        //
                        before_str = before_str.substring(0, legality_pos);
                      }
                      //檢查小數位數是否合理
                      if (after_has_dec > -1) {    //CASE 1: 游標後的字串有小數點
                        var temp_length = before_str.length + after_has_dec;
                        if (before_has_minus > -1) temp_length--; 
                        if (number_size > 0 && temp_length > number_size - number_scale) { //字元長度超過
                          alert("超過欄位整數位數限制, 不允許再輸入字元.");
                          var inx = number_size - number_scale - after_has_dec;
                          if (before_has_minus > -1) inx++; 
                          checkobject.value = before_str.substring(0, inx) + after_str;                             
                          //將游標移至原位置
                          var tRNG = checkobject.createTextRange();
                          tRNG.move("character", checkobject.value.length - focus_dis);
                          tRNG.select();
                        } else {
                          if (before_str_modify) {
                            alert("鍵入不允許的字元, 動作取消.");
                            checkobject.value = before_str + after_str;                             
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();
                          }
                        }
                      } else {
                        if (before_has_dec > -1) { //CASE 2: 游標前的字串有小數點   
                          var temp_length = before_has_dec; 
                          if (before_has_minus > -1) temp_length--;
                          if (number_size > 0 && temp_length > number_size - number_scale) { //整數位數超過
                            alert("超過欄位整數位數限制, 不允許再輸入字元.");
                            var inx = number_size - number_scale;
                            if (before_has_minus > -1) inx++; 
                            checkobject.value = before_str.substring(0, inx) + after_str;                               
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();                                                        
                          } else {                              
                            if (before_str.length - 1 - before_has_dec + after_str.length > number_scale) { //小數位數超過指定值
                              alert("超過欄位小數位數限制, 不允許再輸入字元.");
                              var inx = before_has_dec + (number_scale - after_str.length) + 1;                                
                              checkobject.value = before_str.substring(0, inx) + after_str;                                
                              //將游標移至原位置
                              var tRNG = checkobject.createTextRange();
                              tRNG.move("character", checkobject.value.length - focus_dis);
                              tRNG.select();                            
                            } else {
                              if (before_str_modify) {
                                alert("鍵入不允許的字元, 動作取消.");
                                checkobject.value = before_str + after_str;                             
                                //將游標移至原位置
                                var tRNG = checkobject.createTextRange();
                                tRNG.move("character", checkobject.value.length - focus_dis);
                                tRNG.select();
                              }
                            }
                          } 
                        } else {                   //CASE 3: 游標前後的字串皆無小數點
                          var temp_length = before_str.length + after_str.length; 
                          if (before_has_minus > -1) temp_length--;
                          if (number_size > 0 && temp_length > number_size - number_scale) { //字元長度超過
                            alert("超過欄位整數位數限制, 不允許再輸入字元.");
                            var inx = number_size - number_scale - after_str.length;
                            if (before_has_minus > -1) inx++;
                            checkobject.value = before_str.substring(0, inx) + after_str;                               
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();
                          } else {
                            if (before_str_modify) {
                              alert("鍵入不允許的字元, 動作取消.");
                              checkobject.value = before_str + after_str;                             
                              //將游標移至原位置
                              var tRNG = checkobject.createTextRange();
                              tRNG.move("character", checkobject.value.length - focus_dis);
                              tRNG.select();
                            }
                          }                         
                        }                     
                      }
                    } 
	            break;    
    //* comma(含逗號的數字) 型態
    case "comma":   var number_size = 0;    //數字的總位數
                    var number_scale = 0;   //數字的小數位數
                    if (checkobject.fieldlength != null) {
                      if (checkobject.fieldlength.indexOf(".") > -1) {
                        number_size = parseInt((checkobject.fieldlength.split("."))[0]);
                        number_scale = parseInt((checkobject.fieldlength.split("."))[1]);
                      } else {
                        number_size = parseInt((checkobject.fieldlength.split("."))[0]);
                      }
                    }                    
                    //** onKeyPress 事件處理程序 : 檢查為合法的字元
                    if (event.type == "keypress") {
                      if (!(window.event.keyCode >= 48 && window.event.keyCode <= 57)) {	//非鍵入數字0~9
                        if (!(window.event.keyCode == 45)) {                                    //非鍵入負號'-'
                          if (number_scale > 0) {  //值有小數部份額外允許鍵入小數點'.'
                            if (!(window.event.keyCode == 46)) {                                //非鍵入小數點'.'
                              with(window.event) {
                                cancelBubble = true;
                                keyCode = 0;
                                returnValue = false;
                              } 
                            }
                          } else {
                            with(window.event) {
                              cancelBubble = true;
                              keyCode = 0;
                              returnValue = false;
                            } 
                          }
                        }
                      } 
		    }
                    //** onPaste 事件處理程序  : 檢查欲貼上的內容皆為合法的字元                   
		    if (event.type == "paste") {                      
	              clipvalue = clipboardData.getData("text");                      
                      clipvalue = EraseChar(clipvalue, ",");
                      clipvalue = clipvalue.replace(/[^\x00-\xff]/g,"^^");  
                      var minus_exit = false;
                      var scale_exit = false;
                      var pos = 0;
 	              while (pos < clipvalue.length) {   	
                        if (clipvalue.charAt(pos) >= '0' && clipvalue.charAt(pos) <= '9') {    //為數字0~9
                          pos++;
                        } else {
                          if (clipvalue.charAt(pos) == '-') {                                  //為負號'-'
                            if (minus_exit) {
                              event.returnValue = false;          
	                      alert("欲貼上內容包含多個負號, 貼上動作取消.");
                              break;
                            } else {
                              minus_exit = true;
                              pos++;
                            }                            
                          } else {
                            if (clipvalue.charAt(pos) == '.') {                               //為小數點'.'                                
                              if (number_scale > 0) {  //值有小數部份額外允許鍵入小數點'.'
                                if (scale_exit) {
                                  event.returnValue = false;          
	                          alert("欲貼上內容包含多個小數點, 貼上動作取消.");
                                  break;
                                } else {
                                  scale_exit = true;
                                  pos++;
                                }
                              } else {
                                event.returnValue = false;          
	                        alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                                break;
                              } 
                            } else {
                              event.returnValue = false;          
	                      alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                              break;
                            }
                          }
                        }
                      }  // end of while
                    } 
                    //** onPropertyChange 事件處理程序    
                    if (event.type == "propertychange") {                      
                      //計算目前游標在第幾個字元後面(caret_pos, 一個中文字長度以1計)                     
                      var range = document.selection.createRange();
                      var bookmark = range.getBookmark();
	              var caret_pos = bookmark.charCodeAt(2) - 2;
                      //暫存目前游標後有幾個字元(focus_dis), 作為回復游標位置之用
                      var focus_dis = checkobject.value.length - caret_pos;
                      //游標後字串(after_str)
                      var after_str = checkobject.value.substring(caret_pos, checkobject.value.length);
                      after_str = after_str.replace(/[^\x00-\xff]/g, "^^");
                      //判斷游標後字串小數點位置(after_has_dec, -1表不存在)
                      var after_has_dec = after_str.indexOf(".");
                      //判斷游標後字串負號位置(after_has_minus, -1表不存在)
                      var after_has_minus = after_str.indexOf("-");                                         
                      //游標前字串(before_str)
                      var before_str = checkobject.value.substring(0, caret_pos);
                      before_str = before_str.replace(/[^\x00-\xff]/g, "^^"); 
                      //檢查從頭至第幾個字元為合法字元(legality_pos), 小數點位置(before_has_dec, -1表不存在)與負號位置(before_has_minus, -1表不存在)                     
                      var before_has_dec = -1;
                      var before_has_minus = -1;
                      var legality_pos = 0;
                      while (legality_pos < before_str.length) { 
                        if (after_has_minus > -1) { //若游標後字串已有負號, 則游標前不允許再輸入任何字元
                          break;
                        } else {   
                          if (after_has_dec > -1) { //若游標後字串已有小數點
                            if (legality_pos == 0) {    //若正檢查第一個字元, 則字元可為數字0~9, ','或'-'
                              if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == ',') {                          
                                legality_pos++;
                              } else {
                                if (before_str.charAt(legality_pos) == '-') {
                                  before_has_minus = legality_pos;
                                  legality_pos++;
                                } else {
                                  break;
                                }
                              }
                            } else {                    //其他位置的字元僅能為數字0~9, ','
                              if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == ',') {                          
                                legality_pos++;
                              } else {
                                break;
                              }                
                            }
                          } else {                 //若游標後字串無小數點                          
                            if (legality_pos == 0) {    //若正檢查第一個字元, 則字元可為數字0~9, ','或'-'
                              if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == ',') {                          
                                legality_pos++;
                              } else {
                                if (before_str.charAt(legality_pos) == '-') {
                                  before_has_minus = legality_pos;
                                  legality_pos++;
                                } else {
                                  break;
                                }
                              }
                            } else {                    //其他位置的字元僅能為數字0~9或',', 而'.'則有條件允許
                              if (before_has_dec > -1) {   //檢查過程中已發現小數點, 則後續字元僅能為數字0~9
                                if (before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') {                          
                                  legality_pos++;
                                } else {                              
                                  break;
                                }
                              } else {
                                if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == ',') {                          
                                  legality_pos++;
                                } else {
                                  if (before_str.charAt(legality_pos) == '.') { //檢查過程中第一次發現小數點
                                    before_has_dec = legality_pos;
                                    if (number_scale > 0) { //指定格式為具小數部份, ','才為合法字元                                 
                                      legality_pos++;
                                    } else {
                                      break;
                                    } 
                                  } else {
                                    break;
                                  }                                
                                }
                              }               
                            }                                                     
                          }
                        }
                      } //end of while 
                      //若游標前字串(before_str)含不合法字元, 則重置其為從頭至合法字元(legality_pos)的字串, 並將旗標(before_str_modify)開啟
                      var before_str_modify = false;
                      if (legality_pos != before_str.length) { 
                        before_str_modify = true;
                        //檢查總長度是否超過, 若超過則legality_pos需再縮減
                          //計算游標前字串(before_str)從頭至合法字元(legality_pos)的子字串中','的數目(legality_comma)
                        var temp_pos = 0;
                        var legality_comma = 0;
                        while (temp_pos < legality_pos) {
                          if (before_str.charAt(temp_pos) == ',') legality_comma++;                            
                          temp_pos++;
                        }
                        //計算游標後字串(after_str)中','的數目(after_str_comma)
                        temp_pos = 0;
                        var after_str_comma = 0;                          
                        while (temp_pos < after_str.length) {
                          if (after_str.charAt(temp_pos) == ',') after_str_comma++;                            
                          temp_pos++;
                        }                          
                        var temp_length = legality_pos - legality_comma + after_str.length - after_str_comma;
                        if (before_has_dec > -1 || after_has_dec > -1) temp_length = temp_length--;      //若有小數點, 長度需減1
                        if (before_has_minus > -1 || after_has_minus > -1) temp_length = temp_length--;  //若有負號, 長度需再減1 
                        if (temp_length > number_size) {
                          legality_pos = number_size - after_str.length;
                          //游標前字串(before_str)從頭至合法字元(legality_pos)的子字串中有','存在則多取1字元
                          temp_pos = 0;                          
                          while (temp_pos < legality_pos) {
                            if (before_str.charAt(temp_pos) == ',') legality_pos++;
                            temp_pos++;
                          }  
                          //有游標前字串(before_str)中有負號則多取1字元
                          if (before_has_minus > -1) {
                            legality_pos++;
                            while (before_str.charAt(legality_pos-1) == ',') legality_pos++;
                          }
                          //有游標後字串(after_str)中有小數點則多取1字元
                          if (after_has_dec > -1) {
                            legality_pos++;
                            while (before_str.charAt(legality_pos-1) == ',') legality_pos++;
                          } else {
                            if (before_has_dec > -1 && legality_pos >= before_has_dec + 1) {
                              legality_pos++;
                              while (before_str.charAt(legality_pos-1) == ',') legality_pos++;
                            } else {
                              before_has_dec = -1;
                            }                    
                          }
                        }
                        //
                        before_str = before_str.substring(0, legality_pos);
                      }
                      //去除逗號
                      before_str = EraseChar(before_str, ",");                                               
                      after_str = EraseChar(after_str, ",");
                      //檢查小數位數是否合理
                      if (after_has_dec > -1) {   //CASE 1: 游標後的字串有小數點
                        after_has_dec = after_str.indexOf("."); //重新計算小數點在after_str(已去除逗號)的位置
                        var temp_length = before_str.length + after_has_dec;
                        if (before_has_minus > -1) temp_length--;
                        if (number_size > 0 && temp_length > number_size - number_scale) { //字元長度超過
                          alert("超過欄位整數位數限制, 不允許再輸入字元.");
                          var inx = number_size - number_scale - after_has_dec;
                          if (before_has_minus > -1) inx++;       
                          checkvalue = before_str.substring(0, inx) + after_str; 
                          checkobject.value = CommaFormat(checkvalue);
                          //將游標移至原位置
                          var tRNG = checkobject.createTextRange();
                          tRNG.move("character", checkobject.value.length - focus_dis);
                          tRNG.select();
                        } else {
                          if (before_str_modify) {
                            alert("鍵入不允許的字元, 動作取消.");
                            checkobject.value = before_str + after_str; 
                            checkobject.value = CommaFormat(checkvalue);
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();
                          } else {
                            //檢查欄位值是否已是正確的格式, 不是才重新作格式化(CommaFormat), 避免造成onPropertyChange的無窮迴圈
                            var isBad = false; 
                            var dec_right = 0;                             
                            var dec_pos = checkobject.value.indexOf(".");
                            if (dec_pos > -1) dec_right = checkobject.value.length - dec_pos; 
                            for (i = 1; i <= checkobject.value.length; i++) {                                
                              if (i > dec_right) { //只檢查整數位數, 小數點以後不檢查
                                if ((i - dec_right) % 4 == 0) {      //逗號可能出現的位置
                                  if (i == checkobject.value.length) {        //CASE 1:亦為第一個位數, 則僅能為'-'
                                    if (!(checkobject.value.charAt(0) == '-')) {
                                      isBad = true;
                                      break; 
                                    }
                                  } else {
                                    if (i == checkobject.value.length - 1) {  //CASE 2:亦為第二個位數, 則第一個位數需為數字0~9且第二個位數為','
                                      if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') && checkobject.value.charAt(1) == ',')) {
                                        isBad = true;
                                        break;                                          
                                      }   
                                    } else {                                  //CASE 3:其他, 則僅能為','
                                      if (!(checkobject.value.charAt(checkobject.value.length - i) == ',')) {
                                        isBad = true;
                                        break;
                                      }
                                    }
                                  }
                                } else {
                                  if (i == checkobject.value.length) {       //CASE 1:亦為第一個位數, 則需為數字0~9或'-'
                                    if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') || checkobject.value.charAt(0) == '-')) {
                                      isBad = true;
                                      break; 
                                    } 
                                  } else {                                   //CASE 2:其他, 則需為數字0~9
                                    if (!(checkobject.value.charAt(checkobject.value.length - i) >= '0' && checkobject.value.charAt(checkobject.value.length - i) <= '9')) {
                                      isBad = true;
                                      break; 
                                    } 
                                  }
                                }
                              }
                            } //end of for
                            if (isBad) {
                              checkvalue = before_str + after_str; 
                              checkobject.value = CommaFormat(checkvalue);
                              //將游標移至原位置
                              var tRNG = checkobject.createTextRange();
                              tRNG.move("character", checkobject.value.length - focus_dis);
                              tRNG.select();
                            }
                          }
                        } 
                      } else {
                        if (before_has_dec > -1) { //CASE 2: 游標前的字串有小數點
                          before_has_dec = before_str.indexOf("."); //重新計算小數點在before_str(已去除逗號)的位置
                          var temp_length = before_has_dec;
                          if (before_has_minus > -1) temp_length--;
                          if (number_size > 0 && temp_length > number_size - number_scale) { //整數位數超過
                            alert("超過欄位整數位數限制, 不允許再輸入字元.");
                            var inx = number_size - number_scale;
                            if (before_has_minus > -1) inx++;  
                            checkvalue = before_str.substring(0, inx) + after_str; 
                            checkobject.value = CommaFormat(checkvalue);   
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();                                                        
                          } else {                              
                            if (before_str.length - 1 - before_has_dec + after_str.length > number_scale) { //小數位數超過指定值
                              alert("超過欄位小數位數限制, 不允許再輸入字元.");
                              var inx = before_has_dec + (number_scale - after_str.length) + 1;                                
                              checkvalue = before_str.substring(0, inx) + after_str;
                              checkobject.value = CommaFormat(checkvalue);    
                              //將游標移至原位置
                              var tRNG = checkobject.createTextRange();
                              tRNG.move("character", checkobject.value.length - focus_dis);
                              tRNG.select();
                            } else { 
                              if (before_str_modify) {
                                alert("鍵入不允許的字元, 動作取消.");
                                checkobject.value = before_str + after_str; 
                                checkobject.value = CommaFormat(checkvalue);
                                //將游標移至原位置
                                var tRNG = checkobject.createTextRange();
                                tRNG.move("character", checkobject.value.length - focus_dis);
                                tRNG.select();
                              } else {
                                //檢查欄位值是否已是正確的格式, 不是才重新作格式化(CommaFormat), 避免造成onPropertyChange的無窮迴圈
                                var isBad = false; 
                                var dec_right = 0;                             
                                var dec_pos = checkobject.value.indexOf(".");
                                if (dec_pos > -1) dec_right = checkobject.value.length - dec_pos; 
                                for (i = 1; i <= checkobject.value.length; i++) {                                
                                  if (i > dec_right) { //只檢查整數位數, 小數點以後不檢查
                                    if ((i - dec_right) % 4 == 0) {      //逗號可能出現的位置
                                      if (i == checkobject.value.length) {        //CASE 1:亦為第一個位數, 則僅能為'-'
                                        if (!(checkobject.value.charAt(0) == '-')) {
                                          isBad = true;
                                          break; 
                                        }
                                      } else {
                                        if (i == checkobject.value.length - 1) {  //CASE 2:亦為第二個位數, 則第一個位數需為數字0~9且第二個位數為','
                                          if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') && checkobject.value.charAt(1) == ',')) {
                                            isBad = true;
                                            break;                                          
                                          }   
                                        } else {                                  //CASE 3:其他, 則僅能為','
                                          if (!(checkobject.value.charAt(checkobject.value.length - i) == ',')) {
                                            isBad = true;
                                            break;
                                          }
                                        }
                                      }
                                    } else {
                                      if (i == checkobject.value.length) {       //CASE 1:亦為第一個位數, 則需為數字0~9或'-'
                                        if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') || checkobject.value.charAt(0) == '-')) {
                                          isBad = true;
                                          break; 
                                        } 
                                      } else {                                   //CASE 2:其他, 則需為數字0~9
                                        if (!(checkobject.value.charAt(checkobject.value.length - i) >= '0' && checkobject.value.charAt(checkobject.value.length - i) <= '9')) {
                                          isBad = true;
                                          break; 
                                        } 
                                      }
                                    }
                                  }
                                } //end of for
                                if (isBad) {
                                  checkvalue = before_str + after_str; 
                                  checkobject.value = CommaFormat(checkvalue);
                                  //將游標移至原位置
                                  var tRNG = checkobject.createTextRange();
                                  tRNG.move("character", checkobject.value.length - focus_dis);
                                  tRNG.select();
                                }
                              }                     
                            }
                          } 
                        } else {              //CASE 3: 游標前後的字串皆無小數點
                          var temp_length = before_str.length + after_str.length;
                          if (before_has_minus > -1) temp_length--; 
                          if (number_size > 0 && temp_length > number_size - number_scale) { //字元長度超過
                            alert("超過欄位整數位數限制, 不允許再輸入字元.");
                            var inx = number_size - number_scale - after_str.length;
                            if (before_has_minus > -1) inx++;
                            checkvalue = before_str.substring(0, inx) + after_str; 
                            checkobject.value = CommaFormat(checkvalue);
                            //將游標移至原位置
                            var tRNG = checkobject.createTextRange();
                            tRNG.move("character", checkobject.value.length - focus_dis);
                            tRNG.select();
                          } else {
                            if (before_str_modify) {
                              alert("鍵入不允許的字元, 動作取消.");
                              checkobject.value = before_str + after_str; 
                              checkobject.value = CommaFormat(checkvalue);
                              //將游標移至原位置
                              var tRNG = checkobject.createTextRange();
                              tRNG.move("character", checkobject.value.length - focus_dis);
                              tRNG.select();
                            } else {
                              //檢查欄位值是否已是正確的格式, 不是才重新作格式化(CommaFormat), 避免造成onPropertyChange的無窮迴圈
                              var isBad = false; 
                              var dec_right = 0;                             
                              var dec_pos = checkobject.value.indexOf(".");
                              if (dec_pos > -1) dec_right = checkobject.value.length - dec_pos; 
                              for (i = 1; i <= checkobject.value.length; i++) {                                
                                if (i > dec_right) { //只檢查整數位數, 小數點以後不檢查
                                  if ((i - dec_right) % 4 == 0) {      //逗號可能出現的位置
                                    if (i == checkobject.value.length) {        //CASE 1:亦為第一個位數, 則僅能為'-'
                                      if (!(checkobject.value.charAt(0) == '-')) {
                                        isBad = true;
                                        break; 
                                      }
                                    } else {
                                      if (i == checkobject.value.length - 1) {  //CASE 2:亦為第二個位數, 則第一個位數需為數字0~9且第二個位數為','
                                        if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') && checkobject.value.charAt(1) == ',')) {
                                          isBad = true;
                                          break;                                          
                                        }   
                                      } else {                                  //CASE 3:其他, 則僅能為','
                                        if (!(checkobject.value.charAt(checkobject.value.length - i) == ',')) {
                                          isBad = true;
                                          break;
                                        }
                                      }
                                    }
                                  } else {
                                    if (i == checkobject.value.length) {       //CASE 1:亦為第一個位數, 則需為數字0~9或'-'
                                      if (!((checkobject.value.charAt(0) >= '0' && checkobject.value.charAt(0) <= '9') || checkobject.value.charAt(0) == '-')) {
                                        isBad = true;
                                        break; 
                                      } 
                                    } else {                                   //CASE 2:其他, 則需為數字0~9
                                      if (!(checkobject.value.charAt(checkobject.value.length - i) >= '0' && checkobject.value.charAt(checkobject.value.length - i) <= '9')) {
                                        isBad = true;
                                        break; 
                                      } 
                                    }
                                  }
                                }
                              } //end of for
                              if (isBad) {
                                checkvalue = before_str + after_str; 
                                checkobject.value = CommaFormat(checkvalue);
                                //將游標移至原位置
                                var tRNG = checkobject.createTextRange();
                                tRNG.move("character", checkobject.value.length - focus_dis);
                                tRNG.select();
                              }
                            } 
                          }                         
                        }
                      }
                    } 
	            break;    
    //* date(西元年/月/日)型態與cdate(民國年/月/日)型態
    case "date" :
    case "cdate": //** onKeyPress 事件處理程序                  
                  if (event.type == "keypress") {	            
		    if ((window.event.keyCode >= 48 && window.event.keyCode <= 57) || window.event.keyCode == 47) {	//鍵入數字0~9或'/'字元	    
                      if (checkobject.fieldlength != null && checkobject.value.length + 1 > checkobject.fieldlength) {    
                        with(window.event) {
                          cancelBubble = true;
                          keyCode = 0;
                          returnValue = false;
                        } 
                        alert("超過欄位長度限制, 不允許再輸入字元.");
                      }
		    } else { 
		      with(window.event) {
                        cancelBubble = true;
                        keyCode = 0;
                        returnValue = false;
                      }
		    }
		  }
                  //** onPaste 事件處理程序
		  if (event.type == "paste") {
                    checkvalue = checkobject.value;
                    checkvalue = checkvalue.replace(/[^\x00-\xff]/g, "^^");
	            clipvalue = clipboardData.getData("text");
                    clipvalue = clipvalue.replace(/[^\x00-\xff]/g,"^^");                      
	            if (checkobject.fieldlength != null && checkvalue.length + clipvalue.length > checkobject.fieldlength) {
	              event.returnValue = false;          
	              alert("超過欄位長度限制, 貼上動作取消.");
                    } else {
                      var pos = 0;  
                      while (pos < clipvalue.length) {   	
                        if ((clipvalue.charAt(pos) >= '0' && clipvalue.charAt(pos) <= '9') || clipvalue.charAt(pos) == '/') {
                          pos++;
                        } else {
                          event.returnValue = false;          
	                  alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                          break;
                        }
                      }
	            } 		
                  } 
                  //** onPropertyChange 事件處理程序    
                  if (event.type == "propertychange") {
                    //計算目前游標在第幾個字元後面(caret_pos, 一個中文字長度以1計)                     
                    var range = document.selection.createRange();
                    var bookmark = range.getBookmark();
	            var caret_pos = bookmark.charCodeAt(2) - 2;
                    var focus_dis = checkobject.value.length - caret_pos;
                    //計算從頭至第幾個字元為合法字元(legality_pos)
                    var before_str = checkobject.value.substring(0, caret_pos);
                    before_str = before_str.replace(/[^\x00-\xff]/g, "^^");                      
                    var legality_pos = 0;
                    while (legality_pos < before_str.length) {                        
                      if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == '/') {                          
                        legality_pos++;
                      } else {
                        break;
                      }
                    }                      
                    if (legality_pos != before_str.length) { //存在不合法字元(非數字0~9或/字元)則清除 
                      checkvalue = checkobject.value; 
                      checkvalue = checkvalue.replace(/[^\x00-\xff]/g, "^^"); 
                      //顯示警告訊息
                      alert("鍵入不允許的字元, 動作取消."); 
                      //清除新鍵入超過的字元                       
                      if (before_str.charAt(before_str.length - 1) == "^") {                         
                        var dbc_count = 0;
                        var check_pos = checkvalue.indexOf("^^");
                        while (check_pos >= 0) {
                          dbc_count++;                            
                          check_pos = checkvalue.indexOf("^^", check_pos+2);
                        }                        
                        if (checkobject.value.length == checkvalue.length - dbc_count) {
                          checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos, checkobject.value.length);
                        } else { 
                          checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos - 1, checkobject.value.length);   
                        }
                      } else {                         
                        checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos, checkobject.value.length);
                      }
                      //將游標移至原位置
                      var tRNG = checkobject.createTextRange();
                      tRNG.move("character", checkobject.value.length - focus_dis);
                      tRNG.select();
                    } else {
                      if (checkobject.fieldlength != null) {
                        if (checkobject.value.length > parseInt(checkobject.fieldlength)) { //字元長度超過
                          alert("超過欄位長度限制, 不允許再輸入字元.");
                          var inx = parseInt(checkobject.fieldlength) + caret_pos - checkobject.value.length;
                          checkobject.value = checkobject.value.substring(0, inx) + checkobject.value.substring(caret_pos, checkobject.value.length);
                          //將游標移至原位置
                          var tRNG = checkobject.createTextRange();
                          tRNG.move("character", checkobject.value.length - focus_dis);
                          tRNG.select();
                        } else {
                          //智慧型自動加入'/'符號
                          if (caret_pos == checkobject.value.length) { //於欄位最後鍵入值
                            var check_pos = checkobject.value.indexOf("/");
                            if (check_pos < 0) {  //值中無'/'符號
                              if (caret_pos == parseInt(checkobject.fieldlength) - 5) {   //為月的第一碼
                                checkobject.value = checkobject.value.substring(0, checkobject.value.length-1) + "/" + checkobject.value.substring(checkobject.value.length-1, checkobject.value.length);
                                //將游標移至原位置
                                var tRNG = checkobject.createTextRange();
                                tRNG.move("character", checkobject.value.length - focus_dis);
                                tRNG.select();
                              }
                            } else {
                              if (check_pos == caret_pos - 4) { //已有年月間的'/'符號, 且輸入為日的第一碼
                                check_pos = checkobject.value.indexOf("/", check_pos+1);
                                if (check_pos < 0) { //僅有一個'/'符號
                                  checkobject.value = checkobject.value.substring(0, checkobject.value.length-1) + "/" + checkobject.value.substring(checkobject.value.length-1, checkobject.value.length);
                                  //將游標移至原位置
                                  var tRNG = checkobject.createTextRange();
                                  tRNG.move("character", checkobject.value.length - focus_dis);
                                  tRNG.select();
                                }
                              }                              
                            }
                          } 
                        }
                      }    
                    }
                  }                    
	          break;
    //* month(西元年/月)型態與cmonth(民國年/月)型態
    case "month" :    
    case "cmonth": //** onKeyPress 事件處理程序                  
                  if (event.type == "keypress") {	            
		    if ((window.event.keyCode >= 48 && window.event.keyCode <= 57) || window.event.keyCode == 47) {	//鍵入數字0~9或'/'字元	    
                      if (checkobject.fieldlength != null && checkobject.value.length + 1 > checkobject.fieldlength) {    
                        with(window.event) {
                          cancelBubble = true;
                          keyCode = 0;
                          returnValue = false;
                        } 
                        alert("超過欄位長度限制, 不允許再輸入字元.");
                      }
		    } else { 
		      with(window.event) {
                        cancelBubble = true;
                        keyCode = 0;
                        returnValue = false;
                      }
		    }
		  }
                  //** onPaste 事件處理程序
		  if (event.type == "paste") {
                    checkvalue = checkobject.value;
                    checkvalue = checkvalue.replace(/[^\x00-\xff]/g, "^^");
	            clipvalue = clipboardData.getData("text");
                    clipvalue = clipvalue.replace(/[^\x00-\xff]/g,"^^");                      
	            if (checkobject.fieldlength != null && checkvalue.length + clipvalue.length > checkobject.fieldlength) {
	              event.returnValue = false;          
	              alert("超過欄位長度限制, 貼上動作取消.");
                    } else {
                      var pos = 0;  
                      while (pos < clipvalue.length) {   	
                        if ((clipvalue.charAt(pos) >= '0' && clipvalue.charAt(pos) <= '9') || clipvalue.charAt(pos) == '/') {
                          pos++;
                        } else {
                          event.returnValue = false;          
	                  alert("欲貼上內容包含不允許的字元, 貼上動作取消.");
                          break;
                        }
                      }
	            } 		
                  } 
                  //** onPropertyChange 事件處理程序    
                  if (event.type == "propertychange") {
                    //計算目前游標在第幾個字元後面(caret_pos, 一個中文字長度以1計)                     
                    var range = document.selection.createRange();
                    var bookmark = range.getBookmark();
	            var caret_pos = bookmark.charCodeAt(2) - 2;
                    var focus_dis = checkobject.value.length - caret_pos;
                    //計算從頭至第幾個字元為合法字元(legality_pos)
                    var before_str = checkobject.value.substring(0, caret_pos);
                    before_str = before_str.replace(/[^\x00-\xff]/g, "^^");                      
                    var legality_pos = 0;
                    while (legality_pos < before_str.length) {                        
                      if ((before_str.charAt(legality_pos) >= '0' && before_str.charAt(legality_pos) <= '9') || before_str.charAt(legality_pos) == '/') {                          
                        legality_pos++;
                      } else {
                        break;
                      }
                    }                      
                    if (legality_pos != before_str.length) { //存在不合法字元(非數字0~9或/字元)則清除 
                      checkvalue = checkobject.value; 
                      checkvalue = checkvalue.replace(/[^\x00-\xff]/g, "^^"); 
                      //顯示警告訊息
                      alert("鍵入不允許的字元, 動作取消."); 
                      //清除新鍵入超過的字元                       
                      if (before_str.charAt(before_str.length - 1) == "^") {                         
                        var dbc_count = 0;
                        var check_pos = checkvalue.indexOf("^^");
                        while (check_pos >= 0) {
                          dbc_count++;                            
                          check_pos = checkvalue.indexOf("^^", check_pos+2);
                        }                        
                        if (checkobject.value.length == checkvalue.length - dbc_count) {
                          checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos, checkobject.value.length);
                        } else { 
                          checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos - 1, checkobject.value.length);   
                        }
                      } else {                         
                        checkobject.value = checkobject.value.substring(0, legality_pos) + checkobject.value.substring(caret_pos, checkobject.value.length);
                      }
                      //將游標移至原位置
                      var tRNG = checkobject.createTextRange();
                      tRNG.move("character", checkobject.value.length - focus_dis);
                      tRNG.select();
                    } else {
                      if (checkobject.fieldlength != null) {
                        if (checkobject.value.length > parseInt(checkobject.fieldlength)) { //字元長度超過
                          alert("超過欄位長度限制, 不允許再輸入字元.");
                          var inx = parseInt(checkobject.fieldlength) + caret_pos - checkobject.value.length;
                          checkobject.value = checkobject.value.substring(0, inx) + checkobject.value.substring(caret_pos, checkobject.value.length);
                          //將游標移至原位置
                          var tRNG = checkobject.createTextRange();
                          tRNG.move("character", checkobject.value.length - focus_dis);
                          tRNG.select();
                        } else {
                          //智慧型自動加入'/'符號
                          if (caret_pos == checkobject.value.length) { //於欄位最後鍵入值
                            var check_pos = checkobject.value.indexOf("/");
                            if (check_pos < 0) {  //值中無'/'符號
                              if (caret_pos == parseInt(checkobject.fieldlength) - 2) {   //為月的第一碼
                                checkobject.value = checkobject.value.substring(0, checkobject.value.length-1) + "/" + checkobject.value.substring(checkobject.value.length-1, checkobject.value.length);
                                //將游標移至原位置
                                var tRNG = checkobject.createTextRange();
                                tRNG.move("character", checkobject.value.length - focus_dis);
                                tRNG.select();
                              }
                            }
                          } 
                        }
                      }    
                    }
                  }                    
	          break;
  }   
}
//End fieldcontrol JScript source code

function numberMasking(evt)
{
  if (isIE && IEVersion > 4)
  {
    if (window.event.altKey) return false;
    if (window.event.ctrlKey) return false;
    var keycode = window.event.keyCode;
    var inputtype = this.ccsInputMaskType;
    this.value = applyNumberMask(inputtype,keycode, this.value);
    return (window.event.keyCode==13?true:false);
  } else if (isNN && NNVersion<6)
  {
    if (evt.ALT_MASK) return false;
    if (evt.CONTROL_MASK) return false;
    var keycode = evt.which;
    var inputtype = this.ccsInputMaskType;
    this.value = applyNumberMask(inputtype,keycode, this.value);
    
    return (evt.which==13?true:false);
  } else if (isNN && NNVersion==6)
  {
    if (evt.altKey) return false;
    if (evt.ctrlKey) return false;
    var cancelKey = evt.which==13;
    var is_netscape = (navigator.userAgent.toLowerCase().indexOf('netscape') != -1);
    var keycode = evt.which;
    if (is_netscape)
    {
      var inputtype = this.ccsInputMaskType;
      if (keycode >= 32)
        this.value = applyNumberMaskToValue(inputtype,this.value);
    }
    else
    {
      cancelKey = keycode < 32;
      var inputtype = this.ccsInputMaskType;
      if (!cancelKey)
        this.value = applyNumberMask(inputtype,keycode, this.value);
    }
    return cancelKey || evt.which==13;
  } else
  return true;
}

function applyNumberMaskToValue(inputtype,value)
{
  var oldValue = String(value);
  var newValue = "";
  for (var i=0; i<oldValue.length; i++)
  {
    newValue = applyNumberMask(inputtype,oldValue.charCodeAt(i), newValue);
  }
  return newValue;
}

function applyNumberMask(inputtype,keycode, value)
{
  var digit = (keycode >= 48 && keycode <= 57);
  var plus  = (keycode == 43);
  var dash  = (keycode == 45);
  var dot   = (keycode == 46);
  var space = (keycode == 32);
  var numbertemp1 = "";
  var numbertemp2 = "";
  
  var numberlength1 =  0;
  var numberlength2 =  0;
  
  //設定整數位數及小數位數(10,2)
  if (inputtype.length <= 0 || inputtype == "COMMA"){
      numberlength1 = 50;
      numberlength2 = 50;
  }else{	
      numberlength1 = parseFloat((inputtype.split(","))[0]);
      numberlength2 = parseFloat((inputtype.split(","))[1]);
      numberlength1 = numberlength1 - numberlength2;
      numberlength2 = numberlength2 + 1;
  }      
  //數字或.才加入	
  if (digit || dot){
  	  if (digit || numberlength2 > 0){
         value += String.fromCharCode(keycode);
      }
  }
  //判斷有無小數點  
  if (value.indexOf(".") > -1){
      numbertemp1 = EraseChar((value.split("."))[0],",");
      numbertemp2 = "." + (value.split("."))[1];
      if (numbertemp2 == ".."){
         numbertemp2 = ".";
      }
         
 	    if (numbertemp1.length > numberlength1){
 	 	      numbertemp1 = numbertemp1.substring(0,numbertemp1.length -1);
   	  }	

   	  if (numbertemp2.length > numberlength2){
   	 	    numbertemp2 = numbertemp2.substring(0,numbertemp2.length -1);
   	 	}   
   }else{
   	  numbertemp1 = EraseChar(value,",");
      if (numbertemp1.length > numberlength1){
   	      numbertemp1 = numbertemp1.substring(0,numbertemp1.length -1);
      }	
   }

      
   //整數加,號
   if (numbertemp1.length >= 4 && numbertemp1.length <=6){
    	 value = RSplitChar(numbertemp1,",","3") + numbertemp2;
   }else if (numbertemp1.length >= 7  && numbertemp1.length <=9){
     	 value = RSplitChar(numbertemp1,",","3,3") + numbertemp2;
   }else if (numbertemp1.length >= 10 && numbertemp1.length <=12){
     	 value = RSplitChar(numbertemp1,",","3,3,3") + numbertemp2;
   }else if (numbertemp1.length >= 13 && numbertemp1.length <=15){
     	 value = RSplitChar(numbertemp1,",","3,3,3,3") + numbertemp2;
   }else if (numbertemp1.length >= 16 && numbertemp1.length <=18){
     	 value = RSplitChar(numbertemp1,",","3,3,3,3,3") + numbertemp2;
   }else{
    	 value = numbertemp1 + numbertemp2;
   }		
  return value;
}


function inputMasking_Synct(evt)
{
  if (isIE && IEVersion > 4)
  {
    if (window.event.altKey) return false;
    if (window.event.ctrlKey) return false;
    if (typeof(this.ccsInputMask) == "string")
    {
      var mask = this.ccsInputMask;
      var keycode = window.event.keyCode;
      this.value = applyMask_Synct(keycode, mask, this.value);
    }
    return (window.event.keyCode==13?true:false);
  } else if (isNN && NNVersion<6)
  {
    if (evt.ALT_MASK) return false;
    if (evt.CONTROL_MASK) return false;
    if (typeof(this.ccsInputMask) == "string")
    {
      var mask = this.ccsInputMask;
      var keycode = evt.which;
      this.value = applyMask_Synct(keycode, mask, this.value);
    }
    return (evt.which==13?true:false);
  } else if (isNN && NNVersion==6)
  {
    if (evt.altKey) return false;
    if (evt.ctrlKey) return false;
    var cancelKey = evt.which==13;
    var is_netscape = (navigator.userAgent.toLowerCase().indexOf('netscape') != -1);
    if (typeof(this.ccsInputMask) == "string")
    {
      var mask = this.ccsInputMask;
      var keycode = evt.which;
      if (is_netscape)
      {
        if (keycode >= 32)
          this.value = applyMaskToValue_Synct(mask, this.value);
      }
      else
      {
        cancelKey = keycode < 32;
        if (!cancelKey)
          this.value = applyMask_Synct(keycode, mask, this.value);
      }
    }
    return cancelKey || evt.which==13;
  } else
    return true;
}

function applyMaskToValue_Synct(mask, value)
{
  var oldValue = String(value);
  var newValue = "";
  for (var i=0; i<oldValue.length; i++)
  {
    newValue = applyMask_Synct(oldValue.charCodeAt(i), mask, newValue);
  }
  return newValue;
}

function applyMask_Synct(keycode, mask, value)
{
  var digit   = (keycode >= 48 && keycode <= 57);
  var plus    = (keycode == 43);
  var dash    = (keycode == 45);
  var space   = (keycode == 32);
  var uletter = (keycode >= 65 && keycode <= 90);
  var lletter = (keycode >= 97 && keycode <= 122);
  
  var pos = value.length;
  switch(mask.charAt(pos))
  {
    case "0":
      if (digit)
        value += String.fromCharCode(keycode);
      break;
    case "L":
      if (uletter || lletter || digit)
        value += String.fromCharCode(keycode).toUpperCase();
      break;
    case "l":
      if (uletter || lletter || digit)
        value += String.fromCharCode(keycode);
      break;
    default:
      var isMatchMask = (String.fromCharCode(keycode) == mask.charAt(pos));
      while (pos < mask.length && mask.charAt(pos) != "0" && mask.charAt(pos) != "L" && mask.charAt(pos) != "l")
        value += mask.charAt(pos++);
      if (!isMatchMask && pos < mask.length)
        value = applyMask_Synct(keycode, mask, value);
  }  
  return value;
}


/**
 * Check input total length
 * <AUTHOR> C. Fan (<EMAIL>)
 * @version 1.0
 */
// START------------------------- START -------------------------START
// set maxlength   
function setMaxLength(object, length) {

String.prototype.len = function() {
	return this.replace(/[^\x00-\xff]/g, "**").length;
}

	var result = true;
	var controlid = document.selection.createRange().parentElement().id;
	var controlValue = document.selection.createRange().text;
	var tempString = object.value;
	
	var tt = "";
	
	for (var i = 0; i < length; i++) {
		if (tt.len() < length) {
			tt = tempString.substr(0, (i + 1));
		}
		else {
			break;
		}
	}
	
    if (tt.len() > length) {
		tt = tt.substr(0, (tt.length - 1));
	}
	
	object.value = tt;
}  
 
//Check maxlength when paste  
function limitPaste(object, length) {

String.prototype.len = function() {
	return this.replace(/[^\x00-\xff]/g, "**").length;
}

	var tempLength = 0;
	
	if (document.selection) {
		if (document.selection.createRange().parentElement().id == object.id) {
			tempLength = document.selection.createRange().text.len();
		}
	}

	var tempValue = window.clipboardData.getData("Text");
	
	tempLength = object.value.len() + tempValue.len() - tempLength;
 
	if (tempLength > length) {
		tempLength -= length;  
		
		var tt="";  
		
		for (var i = 0; i < tempValue.len() - tempLength; i++) {
			if (tt.len() < (tempValue.len() - tempLength)) {
				tt = tempValue.substr(0, (i + 1));
			}
			else {
				break;
			}
		}  
		
		if (tt.len() <= 0) {
			window.event.returnValue = false;
		}
		else {
			tempValue = tt;
			window.clipboardData.setData("Text", tempValue);
			window.event.returnValue = true;
		}
	}
}

function pressLength(object, length) {
	if (event.srcElement.type == "text" || event.srcElement.type == "textarea" ) {
		setMaxLength(object, length);
	} 
} 
 
function limitLength(object, length) {
	if (event.srcElement.type == "text" || event.srcElement.type == "textarea" ) { 
		limitPaste(object, length);
	} 
}
// END------------------------- END -------------------------END

/**
 * Limit the total number of digit after the decimal place
 * <AUTHOR> C. Fan (<EMAIL>)
 * @version 1.0
 */
// START---------- function decimalPlace(object, length) ----------START
function decimalPlace(object, length) {
	var num = "" + object.value;
	var decimal = num.indexOf(".");
	var wholeNumber = "", fractionNumber = "";
	
	// decimal existed in the number
	if (decimal > -1) {
		// seprarete the whole number and the fraction
		wholeNumber = (String(num).split("."))[0];
		fractionNumber = (String(num).split("."))[1];
		
		// length of decimal place is greater than the limitation length, remove 
		// the excess length of decimal place
		if (fractionNumber.length > length) {
			fractionNumber = "." + fractionNumber.substr(0, length);
		}
		// length of decimal place is within the limitation length
		else{
			fractionNumber = "." + fractionNumber;
		}
	}
	// decimal is not existed in the number
	else {
		wholeNumber = num;
	}	
	
	object.value = wholeNumber + fractionNumber;
}
// END---------- function decimalPlace(object, length) ----------END

//START----------- 網頁資料存取中顯示Progress Bar專用 function ------------START
function showWaitMessage() {
   var x = document.getElementById('waitDiv').style; 
   x.visibility = 'visible';
   return true; 
} 

function closeWaitMessage() {
   var x = getFiledObject('waitDiv'); 
   x.visibility = 'hidden';
   return true; 
} 
//END  ----------- 網頁資料存取中顯示Progress Bar專用 function ------------  END