# =====================================================
# 新北市違章建築管理系統 - 配置檔案模板
# =====================================================
# 使用說明：
# 1. 複製此檔案為 site.properties
# 2. 將 ${變數名:預設值} 替換為實際值
# 3. 敏感資訊請使用環境變數，勿寫入檔案
# =====================================================

# ===== 資料庫配置 =====
# 主要資料庫 (PostgreSQL)
DBConn.name=DBConn
DBConn.url=${DB_PRIMARY_URL:************************************}
DBConn.driver=org.postgresql.Driver
DBConn.user=${DB_PRIMARY_USER:postgres}
DBConn.password=${DB_PRIMARY_PASSWORD}
DBConn.maxconn=${DB_PRIMARY_MAX_CONN:20}
DBConn.timeout=${DB_PRIMARY_TIMEOUT:300}
DBConn.cachesize=100

# 次要資料庫 (SQL Server) - GIS 系統
DBConn2.name=DBConn2
DBConn2.url=${DB_SECONDARY_URL:****************************************************}
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.user=${DB_SECONDARY_USER:gis_user}
DBConn2.password=${DB_SECONDARY_PASSWORD}
DBConn2.maxconn=${DB_SECONDARY_MAX_CONN:10}
DBConn2.timeout=${DB_SECONDARY_TIMEOUT:300}
DBConn2.cachesize=100

# ===== 應用程式設定 =====
serverUrl=${APP_SERVER_URL:http://localhost:8080/bms}
third.party.service.base.url=${THIRD_PARTY_SERVICE_URL:http://localhost/third-party/}
google.maps.service.url=${GOOGLE_MAPS_SERVICE_URL:maps/index}

# ===== 安全性設定 =====
security.type=CCS
security.encryption.key=${ENCRYPTION_KEY}
security.auth.cookie.name=${AUTH_COOKIE_NAME:bmsLogin}
security.auth.cookie.expire=${AUTH_COOKIE_EXPIRE:30}
security.hash.algorithm=${HASH_ALGORITHM:SHA-256}

# ===== 日誌設定 =====
logpriority=${LOG_LEVEL:info}
logfile=${LOG_FILE:logs/bms.log}
logsize=${LOG_MAX_SIZE:10240}

# ===== 本地化設定 =====
language=${SYSTEM_LANGUAGE:zh}
defaultlocale=${SYSTEM_LOCALE:zh_TW}
requestencoding=${REQUEST_ENCODING:UTF-8}
dateformat=${DATE_FORMAT:ShortDate}

# ===== 效能設定 =====
cache.enabled=${CACHE_ENABLED:true}
cache.size=${CACHE_SIZE:1000}
session.timeout=${SESSION_TIMEOUT:30}
upload.max.size=${UPLOAD_MAX_SIZE:10485760}

# ===== 檔案路徑設定 =====
model.folder=${MODEL_FOLDER:}
designs.folder=${DESIGNS_FOLDER:Designs}
upload.folder=${UPLOAD_FOLDER:uploads}
temp.folder=${TEMP_FOLDER:temp}

# ===== 系統設定 =====
system.mode=${SYSTEM_MODE:production}
system.debug=${SYSTEM_DEBUG:false}
system.maintenance=${SYSTEM_MAINTENANCE:false}