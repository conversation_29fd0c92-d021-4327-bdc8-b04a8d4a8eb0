# 違章案件認定件數統計 - 銷案排除修正計畫

## 問題描述
違章案件認定件數統計（im50401）在輸出Excel時，部分統計欄位沒有排除已銷案的案件，導致統計數據不準確。

## 影響範圍
- **報表程式**：IM50401.java
- **受影響欄位**：
  - D20-D24（前一月份統計）
  - D26-D30（前二月份統計）
  - D31-D34（本月份分類合計）

## 修正方案

### 1. 修改SQL查詢條件
在 `IM50401.java` 的 `genDetailSQL()` 方法中，為以下查詢加入銷案排除條件：

#### A. 前一月份統計（D20-D24）
- **第164-168行**（D20）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第169-174行**（D21）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第175-180行**（D22）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第181-186行**（D23）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第187-192行**（D24）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`

#### B. 前二月份統計（D26-D30）
- **第196-200行**（D26）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第201-206行**（D27）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第207-212行**（D28）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第213-218行**（D29）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第219-224行**（D30）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`

#### C. 本月份分類合計（D31-D34）
- **第226-228行**（D31）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第230-232行**（D32）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第234-236行**（D33）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`
- **第238-240行**（D34）：加入 `AND (is_closed IS NULL OR is_closed <> '1')`

### 2. 修正原則
- 所有統計查詢都應該排除已銷案案件
- 使用 `(is_closed IS NULL OR is_closed <> '1')` 條件
- 確保條件加在適當的位置，不影響其他查詢邏輯

### 3. 測試驗證
修正後需要驗證：
1. D類案件數量應為254筆（排除1筆銷案）
2. 所有統計欄位都正確排除銷案案件
3. 報表格式和其他功能正常運作

### 4. 實施步驟
1. 備份原始 `IM50401.java` 檔案
2. 修改SQL查詢條件
3. 編譯Java程式
4. 重新部署至Tomcat
5. 執行測試驗證
6. 確認統計數據正確

### 5. 風險評估
- **低風險**：只是增加過濾條件，不影響程式邏輯
- **影響範圍**：僅影響統計報表的數據準確性
- **回滾方案**：若有問題可立即還原備份檔案

### 6. 長期建議
1. 建立統一的查詢條件常數，避免重複撰寫
2. 考慮建立檢視表（View）統一處理銷案過濾
3. 加強程式碼審查，確保新增功能都考慮銷案邏輯