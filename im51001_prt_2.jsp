<%--JSP Page Init @1-3387B6B9--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im51001_prt_2ServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-E7F4EC60--%>
<%@include file="im51001_prt_2Handlers.jsp"%>
<%
    if (!im51001_prt_2Model.isVisible()) return;
    if (im51001_prt_2Parent != null) {
        if (!im51001_prt_2Parent.getChild(im51001_prt_2Model.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im51001_prt_2Model);
    pageContext.setAttribute("page", im51001_prt_2Model);
    im51001_prt_2Model.fireOnInitializeViewEvent(new Event());
    im51001_prt_2Model.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im51001_prt_2Model;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im51001_prt_2Model.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-B9784B8C--%>
<jsp:useBean id="IM20101_A" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_A" property="reset" value=""/>
	<jsp:setProperty name="IM20101_A" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_B" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_B" property="reset" value=""/>
	<jsp:setProperty name="IM20101_B" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_C" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_C" property="reset" value=""/>
	<jsp:setProperty name="IM20101_C" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_D" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_D" property="reset" value=""/>
	<jsp:setProperty name="IM20101_D" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_E" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_E" property="reset" value=""/>
	<jsp:setProperty name="IM20101_E" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_F" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_F" property="reset" value=""/>
	<jsp:setProperty name="IM20101_F" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM20101_G" class="com.ezek.report.IM20101">
	<jsp:setProperty name="IM20101_G" property="reset" value=""/>
	<jsp:setProperty name="IM20101_G" property="*"/>
</jsp:useBean>

<jsp:useBean id="IM51001_A" class="com.ezek.report.IM51001">
	<jsp:setProperty name="IM51001_A" property="reset" value=""/>
	<jsp:setProperty name="IM51001_A" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM51001_B" class="com.ezek.report.IM51001">
	<jsp:setProperty name="IM51001_B" property="reset" value=""/>
	<jsp:setProperty name="IM51001_B" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM51001_C" class="com.ezek.report.IM51001">
	<jsp:setProperty name="IM51001_C" property="reset" value=""/>
	<jsp:setProperty name="IM51001_C" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM51001_D" class="com.ezek.report.IM51001">
	<jsp:setProperty name="IM51001_D" property="reset" value=""/>
	<jsp:setProperty name="IM51001_D" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM51001_E" class="com.ezek.report.IM51001">
	<jsp:setProperty name="IM51001_E" property="reset" value=""/>
	<jsp:setProperty name="IM51001_E" property="*"/>
</jsp:useBean>
<jsp:useBean id="IM51001_F" class="com.ezek.report.IM51001_F">
	<jsp:setProperty name="IM51001_F" property="reset" value=""/>
	<jsp:setProperty name="IM51001_F" property="*"/>
</jsp:useBean>

<%@ page import="java.io.*,java.net.*,java.nio.file.Files,java.time.LocalDateTime,java.time.format.DateTimeFormatter,org.apache.tools.zip.ZipEntry,org.apache.tools.zip.ZipOutputStream"%>
<%!
private final String CONNECTION_NAME = "DBConn";
private final String DELIMITER = System.getProperty("file.separator");

private final int MAX_COLUMN_SIZE = 60;

private final ArrayList<HashMap<String, String>> REPORT_COLUMNS = new ArrayList<HashMap<String, String>>() {{
	add( new HashMap<String, String>() {{ put("caption", "認定通知號碼"); put("sql", "CASE WHEN reg_yy IS NOT NULL AND reg_no IS NOT NULL THEN TRIM(reg_yy || reg_no) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建類型"); put("sql", "CASE ib_prcs WHEN 'A' THEN '一般違建' WHEN 'B' THEN '廣告違建' WHEN 'C' THEN '下水道違建' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "查報類別"); put("sql", "CASE examine_kind WHEN '01' THEN '勘查紀錄單' WHEN '02' THEN '單位查報' WHEN '03' THEN '專案' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "查報單位"); put("sql", "d.code_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "查報單發文日期"); put("sql", "CASE WHEN audnm_date IS NOT NULL AND LENGTH(audnm_date::text) > 4 THEN SUBSTRING(lpad(audnm_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(audnm_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(audnm_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "查報單發文字號"); put("sql", "audnm_word"); }} );
	add( new HashMap<String, String>() {{ put("caption", "案件來源"); put("sql", "g.code_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "相關文號"); put("sql", "case_ori_num"); }} );
	add( new HashMap<String, String>() {{ put("caption", "認定承辦姓名"); put("sql", "e.empname"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建地址"); put("sql", "caddress"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建地號"); put("sql", "(SELECT string_agg(COALESCE(dist_desc, '') || COALESCE(section_nm, '') || COALESCE(road_no1, '') || '－' || COALESCE(road_no2, ''), '、' ORDER BY land_seq) FROM ibmcslan WHERE ibmcslan.case_id = a.case_id)"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建人姓名"); put("sql", "(SELECT string_agg(ibmdisnm.ib_user, '、' ORDER BY ibmdisnm.case_seq) FROM ibmdisnm WHERE ibmdisnm.case_id = a.case_id)"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建人地址"); put("sql", "(SELECT string_agg(ibmdisnm.usr_add, '、' ORDER BY ibmdisnm.case_seq) FROM ibmdisnm WHERE ibmdisnm.case_id = a.case_id)"); }} );
	add( new HashMap<String, String>() {{ put("caption", "勘查日期"); put("sql", "CASE WHEN rvldate IS NOT NULL AND LENGTH(rvldate::text) > 4 THEN SUBSTRING(lpad(rvldate::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(rvldate::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(rvldate::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建類別代碼"); put("sql", "dis_type"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建組別代碼"); put("sql", "dis_sort"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建類別名稱"); put("sql", "dis_type_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建組別名稱"); put("sql", "dis_sort_item"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建類別"); put("sql", "f.code_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "違建材料"); put("sql", "building_kind"); }} );
	// Need to cast the numeric to text field
	add( new HashMap<String, String>() {{ put("caption", "違建地上層數"); put("sql", "building_coat::text"); }} );
	// Need to cast the numeric to text field
	add( new HashMap<String, String>() {{ put("caption", "違建高度"); put("sql", "building_height::text"); }} );
	// Need to cast the numeric to text field
	add( new HashMap<String, String>() {{ put("caption", "違建面積"); put("sql", "building_area::text"); }} );
	add( new HashMap<String, String>() {{ put("caption", "廣告物違建流程"); put("sql", "CASE ad_typ WHEN 'A' THEN '大型帆布廣告' WHEN 'B' THEN '危險招牌' WHEN 'C' THEN '高風險廣告物' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "廣告物型式"); put("sql", "CASE ad_kind WHEN 'A1' THEN '樹立廣告(屋頂)' WHEN 'A2' THEN '樹立廣告(地面)' WHEN 'B1' THEN '招牌廣告(正面)' WHEN 'B2' THEN '招牌廣告(側懸)' WHEN 'ZZ' THEN '其他' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "認定發文日期"); put("sql", "CASE WHEN reg_date IS NOT NULL AND LENGTH(reg_date::text) > 4 THEN SUBSTRING(lpad(reg_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(reg_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(reg_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "專案名稱"); put("sql", "(SELECT string_agg(ibmcode.code_desc, '、' ORDER BY ibmcsprj.prj_code) FROM ibmcsprj LEFT JOIN ibmcode ON (ibmcode.code_type = 'PRJNM' AND ibmcode.code_seq = ibmcsprj.prj_code) WHERE ibmcsprj.case_id = a.case_id)"); }} );
	add( new HashMap<String, String>() {{ put("caption", "預定拆除日期"); put("sql", "CASE WHEN pre_dis_date IS NOT NULL AND LENGTH(pre_dis_date::text) > 4 THEN SUBSTRING(lpad(pre_dis_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(pre_dis_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(pre_dis_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除時間通知單發文日期"); put("sql", "CASE WHEN dis_notice_date IS NOT NULL AND LENGTH(dis_notice_date::text) > 4 THEN SUBSTRING(lpad(dis_notice_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(dis_notice_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(dis_notice_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除通知號碼"); put("sql", "CASE WHEN dis_reg_yy IS NOT NULL AND dis_reg_no IS NOT NULL THEN dis_reg_yy || dis_reg_no ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除結果"); put("sql", "CASE b_notice_result WHEN '01' THEN '結案' WHEN '02' THEN '未拆' WHEN '03' THEN '併案' WHEN '04' THEN '簽結' WHEN '05' THEN '解列並移拍照建檔列管' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "結案發文登錄日期"); put("sql", "CASE WHEN rsult_rec_time IS NOT NULL AND LENGTH(rsult_rec_time::text) > 10 THEN SUBSTRING(lpad(rsult_rec_time::text, 13, '0'), 1, 3) || '/' || SUBSTRING(lpad(rsult_rec_time::text, 13, '0'), 4, 2) || '/' || SUBSTRING(lpad(rsult_rec_time::text, 13, '0'), 6, 2) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "結案方式"); put("sql", "CASE end_way WHEN '01' THEN '派班執行' WHEN '02' THEN '委外執行' WHEN 'ZZ' THEN '其他' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除方式"); put("sql", "CASE b_notice_way WHEN '01' THEN '強制拆除' WHEN '02' THEN '自行拆除' ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "結案通知單發文日期"); put("sql", "CASE WHEN end_date IS NOT NULL AND LENGTH(end_date::text) > 4 THEN SUBSTRING(lpad(end_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(end_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(end_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "結案通知號碼"); put("sql", "CASE WHEN end_reg_yy IS NOT NULL AND end_reg_no IS NOT NULL THEN end_reg_yy || end_reg_no ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除完成日期"); put("sql", "CASE WHEN b_finish_date IS NOT NULL AND LENGTH(b_finish_date::text) > 4 THEN SUBSTRING(lpad(b_finish_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(b_finish_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(b_finish_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "至拆除現場日期"); put("sql", "CASE WHEN b_notice_date IS NOT NULL AND LENGTH(b_notice_date::text) > 4 THEN SUBSTRING(lpad(b_notice_date::text, 7, '0'), 1, 3) || '/' || SUBSTRING(lpad(b_notice_date::text, 7, '0'), 4, 2) || '/' || SUBSTRING(lpad(b_notice_date::text, 7, '0'), 6) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "結案人員"); put("sql", "h.empname"); }} );
	add( new HashMap<String, String>() {{ put("caption", "標案"); put("sql", "i.code_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "認定發文登錄日期"); put("sql", "CASE WHEN reg_rec_date IS NOT NULL AND LENGTH(reg_rec_date::text) > 10 THEN SUBSTRING(lpad(reg_rec_date::text, 13, '0'), 1, 3) || '/' || SUBSTRING(lpad(reg_rec_date::text, 13, '0'), 4, 2) || '/' || SUBSTRING(lpad(reg_rec_date::text, 13, '0'), 6, 2) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "認定登錄日期"); put("sql", "CASE WHEN idntfy_rec_time IS NOT NULL AND LENGTH(idntfy_rec_time::text) > 10 THEN SUBSTRING(lpad(idntfy_rec_time::text, 13, '0'), 1, 3) || '/' || SUBSTRING(lpad(idntfy_rec_time::text, 13, '0'), 4, 2) || '/' || SUBSTRING(lpad(idntfy_rec_time::text, 13, '0'), 6, 2) ELSE NULL END"); }} );
	add( new HashMap<String, String>() {{ put("caption", "建物用途"); put("sql", "blduse"); }} );
	add( new HashMap<String, String>() {{ put("caption", "拆除人員"); put("sql", "j.empname"); }} );
	add( new HashMap<String, String>() {{ put("caption", "處理狀態"); put("sql", "c.code_desc"); }} );
	add( new HashMap<String, String>() {{ put("caption", "核發執照年度"); put("sql", "LICENCE_YY"); }} );
	add( new HashMap<String, String>() {{ put("caption", "核發執照字軌"); put("sql", "LICENCE_WORD"); }} );
	add( new HashMap<String, String>() {{ put("caption", "核發執照號碼"); put("sql", "LICENCE_NO"); }} );
}};


private boolean isInteger(String str) {
	if (str == null) {
		return false;
	}
	
	int length = str.length();
	if (length == 0) {
		return false;
	}
	
	int i = 0;
	
	if (str.charAt(0) == '-') {
		if (length == 1) {
			return false;
		}
		i = 1;
	}
	
	for (; i < length; i++) {
		char c = str.charAt(i);
		if (c < '0' || c > '9') {
			return false;
		}
	}
	
	return true;
}

	private static void copyFileUsingJava7Files(File source, File dest) throws IOException { 
		if( source.exists() && !dest.exists() ){
			Files.copy(source.toPath(), dest.toPath());
		}
	}

	private static void copyFolder(File source, File dest) throws Exception {
		if (source.isDirectory()) {
			for (File tmp : source.listFiles()) {
				copyFileUsingJava7Files( new File(source, tmp.getName()), new File(dest, tmp.getName()));
			}
		} else {
			copyFileUsingJava7Files(source, dest);
		}
	}

	private static void deleteFiles(File delFile) throws IOException { 
		if( delFile.exists()  ){
			delFile.delete();
		}
	}

	private static void deleteFolder(File delFile) throws Exception {
		if (delFile.isDirectory()) {
			for (File tmp : delFile.listFiles()) {
				deleteFiles( new File(delFile, tmp.getName()));		
			}
			if(delFile.exists()){
				deleteFiles(delFile);
			}
		} else {
			deleteFiles(delFile);
		}
	}
	
	  /**
 	 * Find all file recursively and compress them.
 	 * 
 	 * @param file: file or directory
 	 * @param zos: output stream
 	 * @param filename: file name
 	 **/
	private static void getAllFiles(File file, ZipOutputStream zos, String filename) throws Exception {
		if (file.isDirectory()) {
			for (File tmp : file.listFiles()) {
				getAllFiles(tmp, zos, filename + "/" + tmp.getName());
			}
		} else {
			addToZipFile(file, zos, filename);
		}
	}
    
    /**
 	 * Add the file to the specific zip file.
 	 * 
 	 * @param file: file
 	 * @param zos: output stream
 	 * @param filename: file name
 	 **/
	private static void addToZipFile(File file, ZipOutputStream zos, String filename) throws Exception {
		ZipEntry zipEntry = new ZipEntry(filename);
		
		zos.putNextEntry(zipEntry);	
		
		FileInputStream fis = new FileInputStream(file);
		
		byte[] bytes = new byte[(int)file.length()];
		int length;
		
		// read bytes from the file and write into the zip archive
		while ((length = fis.read(bytes)) >= 0) {
			zos.write(bytes, 0, length);
		}
		
		// close this entry in the zip stream
		zos.closeEntry();
		
		// close the input stream
		fis.close();
		
		bytes = null;
	}
	
	
%>
<%
/**
* 產製並打包月報與月報違建清冊
* @param: (session):UserID 
* @param: (session):SEARCHPARAM_1 : 年度
* @param: (session):SEARCHPARAM_2 : 月份
* @output: zip 檔
*/



//------------------
// STEP_1 產違建清冊
// 
//------------------- 
String USER_ID = (String)session.getAttribute("UserID");
// 年度
String SEARCHPARAM_1 = (String)session.getAttribute("SEARCHPARAM_1");
// 月份
String SEARCHPARAM_2 = (String)session.getAttribute("SEARCHPARAM_2");
String t_yymm = SEARCHPARAM_1 + SEARCHPARAM_2;
	
// Get the absolute file path
String ABSOLUTE_PATH = application.getRealPath("/");

// Find the last character of the absolute file path
String pathLastChar = ABSOLUTE_PATH.substring(ABSOLUTE_PATH.length() - 1);
// Add delimiter to the absolute path if needed
ABSOLUTE_PATH += (pathLastChar.equals(DELIMITER) ? "" : DELIMITER);

String reportPath = ABSOLUTE_PATH + "report" + DELIMITER;
String reportOutputPath = reportPath + "output" + DELIMITER;

String outputFilename = "違章案件清單_" + Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_timestamp, 'YYYYMMDDHH24MISS'), '99999999999999') - 19110000000000", "", "", CONNECTION_NAME));

// Get the value from the URL parameter
String outExt ="EXL"; // 全部產excel
String requestRawStr = "";


String im51001_prt_colum[] = {"認定通知號碼","違建地址","違建類型","違建類別","違建類別代碼","違建類別名稱","違建組別代碼","違建組別名稱"
,"專案名稱","拆除結果","結案方式","拆除方式","結案通知單發文日期","結案發文登錄日期","認定發文日期","認定發文登錄日期","認定登錄日期","處理狀態","核發執照年度","核發執照字軌","核發執照號碼"};
  
// 用中文組出需要的欄位代碼
for(int i = 0 ; i < im51001_prt_colum.length ; i++){
	for(int j = 0 ; j < REPORT_COLUMNS.size() ; j++){
		String caption = REPORT_COLUMNS.get(j).get("caption");
		if(im51001_prt_colum[i].equals(caption) ){
			if( !StringUtils.isEmpty(requestRawStr) )requestRawStr += ",";
			requestRawStr += ""+j;
		}
	}
}
String reportFilename = "";
String[] requestRawStrArray = requestRawStr.split(",");
int requestRawStrArrayLen = requestRawStrArray.length;

String[] allListReportKind = {"A_CHK", "A_PL", "A_DEL", "B_CHK", "B_DEL", "C_CHK", "C_DEL"};
for (int aRK_i = 0; aRK_i < allListReportKind.length ; aRK_i++){

	reportFilename = USER_ID + "im20101_" + allListReportKind[aRK_i];
	if ("ODS".equals(outExt)) {
		reportFilename += ".ods";
		outputFilename += ".ods";
	}else if ("EXL".equals(outExt)) {
	
		reportFilename += ".xlsx";
		outputFilename += ".xlsx";
	}
	

	int idx = 0, reportColumnIndex = 0, sequenceNum = 0;
	String caption = "", sqlColumn = "";
	
	StringBuffer sql_head = new StringBuffer();
	StringBuffer sql_detail = new StringBuffer();
	
	for (idx = 0; idx < requestRawStrArrayLen; idx++) {
		reportColumnIndex = Utils.convertToLong(requestRawStrArray[idx]).intValue();
		caption = REPORT_COLUMNS.get(reportColumnIndex).get("caption");
		sqlColumn = REPORT_COLUMNS.get(reportColumnIndex).get("sql");
		sequenceNum = idx + 1;
		
		sql_head.append(", '").append(caption).append("' AS H").append(sequenceNum);
		// Use fullwidth form of a space instead of halfwidth form of a space to make an empty cell to have border line
		sql_detail.append(", CASE WHEN LENGTH(COALESCE(").append(sqlColumn).append(", '')) = 0 THEN '　' ELSE ").append(sqlColumn).append(" END AS D").append(sequenceNum);
	}
	//requestRawStrArray = null;
	
	// Need to fill up to max column size
	for (idx = requestRawStrArrayLen; idx < MAX_COLUMN_SIZE; idx++) {
		sequenceNum = idx + 1;
		
		sql_head.append(", '' AS H").append(sequenceNum);
		sql_detail.append(", '' AS D").append(sequenceNum);
	}
	
	sql_detail.append(" FROM ibmcase AS a");
	sql_detail.append(" INNER JOIN ibmcode AS c ON (c.code_type = 'STA' AND c.code_seq = a.status)");
	sql_detail.append(" LEFT JOIN ibmcode AS d ON (d.code_type = 'DSTOFF' AND d.code_seq = a.audnm_code)");
	sql_detail.append(" LEFT JOIN ibmuser AS e ON (e.empno = a.reg_emp)");
	sql_detail.append(" LEFT JOIN ibmcode AS f ON (f.code_type = 'BUDCGY' AND f.code_seq = a.building_category)");
	sql_detail.append(" LEFT JOIN ibmcode AS g ON (g.code_type = 'CASORI' AND g.code_seq = a.case_ori)");
	sql_detail.append(" LEFT JOIN ibmuser AS h ON (h.empno = a.rsult_emp)");
	sql_detail.append(" LEFT JOIN ibmcode AS i ON (i.code_type = 'BIDNM' AND i.code_seq = a.bid_name)");
	sql_detail.append(" LEFT JOIN ibmuser AS j ON (j.empno = a.b_notice_emp)");
	sql_detail.append(" WHERE 1 = 1");
	
	
	if("A_CHK".equals(allListReportKind[aRK_i]) ){
		//一般違建- 認定案件
		sql_detail.append(" and ib_prcs = 'A' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < reg_rec_date and reg_rec_date < "+t_yymm+"99999999  " );	
	}else if("A_PL".equals(allListReportKind[aRK_i]) ){
		//一般違建- 拍照列管案件 (修復：排除銷案案件)
		sql_detail.append(" and ib_prcs = 'A' and (is_closed IS NULL OR is_closed <> '1') and ( "+t_yymm+"00000000 < IDNTFY_REC_TIME and IDNTFY_REC_TIME < "+t_yymm+"99999999 and reg_rsult = '02' and dis_type = 'D') " );
	}else if("A_DEL".equals(allListReportKind[aRK_i]) ){
		// 一般違建-拆除
		sql_detail.append(" and ib_prcs = 'A' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < rsult_rec_time and rsult_rec_time < "+t_yymm+"99999999  " );
	}else if("B_CHK".equals(allListReportKind[aRK_i]) ){
		// 廣拆科 - 認定
		sql_detail.append(" and ib_prcs = 'B' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < reg_rec_date and reg_rec_date < "+t_yymm+"99999999  " );
	}else if("B_DEL".equals(allListReportKind[aRK_i]) ){
		// 廣拆科 - 拆除
		sql_detail.append(" and ib_prcs = 'B' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < rsult_rec_time and rsult_rec_time < "+t_yymm+"99999999  " );
	}else if("C_CHK".equals(allListReportKind[aRK_i]) ){
		// 勞安科 - 認定
		sql_detail.append(" and ib_prcs = 'C' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < reg_rec_date and reg_rec_date < "+t_yymm+"99999999  " );
	}else if("C_DEL".equals(allListReportKind[aRK_i]) ){
		// 勞安科 - 拆除
		sql_detail.append(" and ib_prcs = 'C' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < rsult_rec_time and rsult_rec_time < "+t_yymm+"99999999  " );
	}
	
	sql_detail.append(" ORDER BY reg_yy || reg_no");
	
	// Compose the necessary conditions into an array and pass to the bean
	String[] conditionList = new String[2];
	conditionList[0] = "SELECT" + sql_head.substring(1); // Remove the very first comma by using substring
	conditionList[1] = "SELECT" + sql_detail.substring(1); // Remove the very first comma by using substring
	//DEBUG System.out.println("im51001_prt_2 ::: conditionList[0]:" + conditionList[0]);
	//DEBUG System.out.println("im51001_prt_2 ::: conditionList[1]:" + conditionList[1]);
	sql_head = null;
	sql_detail = null;
	
	HashMap<String, Object> reportParameters = new HashMap<String, Object>();
	reportParameters.put("conditionList", conditionList);
	reportParameters.put("outExt", outExt);
	reportParameters.put("outFileName", reportFilename);
	
	

	if("A_CHK".equals(allListReportKind[aRK_i]) ){
		IM20101_A.setAppPath(ABSOLUTE_PATH);
		IM20101_A.setReportPath(reportPath);
		IM20101_A.produceReport(reportParameters);
	}else if("A_PL".equals(allListReportKind[aRK_i]) ){
		IM20101_B.setAppPath(ABSOLUTE_PATH);
		IM20101_B.setReportPath(reportPath);
		IM20101_B.produceReport(reportParameters);
	}else if("A_DEL".equals(allListReportKind[aRK_i]) ){
		IM20101_C.setAppPath(ABSOLUTE_PATH);
		IM20101_C.setReportPath(reportPath);
		IM20101_C.produceReport(reportParameters);
	}else if("B_CHK".equals(allListReportKind[aRK_i]) ){
		IM20101_D.setAppPath(ABSOLUTE_PATH);
		IM20101_D.setReportPath(reportPath);
		IM20101_D.produceReport(reportParameters);
	}else if("B_DEL".equals(allListReportKind[aRK_i]) ){
		IM20101_E.setAppPath(ABSOLUTE_PATH);
		IM20101_E.setReportPath(reportPath);
		IM20101_E.produceReport(reportParameters);
	}else if("C_CHK".equals(allListReportKind[aRK_i]) ){
		IM20101_F.setAppPath(ABSOLUTE_PATH);
		IM20101_F.setReportPath(reportPath);
		IM20101_F.produceReport(reportParameters);
	}else if("C_DEL".equals(allListReportKind[aRK_i]) ){
		IM20101_G.setAppPath(ABSOLUTE_PATH);
		IM20101_G.setReportPath(reportPath);
		IM20101_G.produceReport(reportParameters);
	}
}

//------------------
// STEP_2 產月報表檔案
//------------------- 
String allReportKind[] = {"A", "B", "C", "D", "E", "F"};
for (int aRK_i = 0; aRK_i < allReportKind.length ; aRK_i++){
	String s_kind = allReportKind[aRK_i];
	reportFilename = USER_ID + "im51001" + "_"+s_kind ;
	if ("ODS".equals(outExt)) {
		reportFilename += ".ods";
		outputFilename += ".ods";
	} else if ("EXL".equals(outExt)) {
		reportFilename += ".xlsx";
		outputFilename += ".xlsx";
	}
	
	// Compose the necessary conditions into an array and pass to the bean
	String[] conditionList_month = new String[4];
	conditionList_month[0] = USER_ID;
	conditionList_month[1] = SEARCHPARAM_1;
	conditionList_month[2] = SEARCHPARAM_2;
	conditionList_month[3] = s_kind;
	
	HashMap<String, Object> reportParameters = new HashMap<String, Object>();
	reportParameters.put("conditionList", conditionList_month);
	reportParameters.put("outExt", outExt);
	reportParameters.put("outFileName", reportFilename);
	

	
	if( "A".equals(allReportKind[aRK_i])){
		IM51001_A.setAppPath(ABSOLUTE_PATH);
		IM51001_A.setReportPath(reportPath);
		IM51001_A.produceReport(reportParameters);
	}else if("B".equals(allReportKind[aRK_i])){
		IM51001_B.setAppPath(ABSOLUTE_PATH);
		IM51001_B.setReportPath(reportPath);
		IM51001_B.produceReport(reportParameters);
	}else if("C".equals(allReportKind[aRK_i])){
		IM51001_C.setAppPath(ABSOLUTE_PATH);
		IM51001_C.setReportPath(reportPath);
		IM51001_C.produceReport(reportParameters);
	}else if("D".equals(allReportKind[aRK_i])){
		IM51001_D.setAppPath(ABSOLUTE_PATH);
		IM51001_D.setReportPath(reportPath);
		IM51001_D.produceReport(reportParameters);
	}else if("E".equals(allReportKind[aRK_i])){
		IM51001_E.setAppPath(ABSOLUTE_PATH);
		IM51001_E.setReportPath(reportPath);
		IM51001_E.produceReport(reportParameters);
	}else if("F".equals(allReportKind[aRK_i])){
		IM51001_F.setAppPath(ABSOLUTE_PATH);
		IM51001_F.setReportPath(reportPath);
		IM51001_F.produceReport(reportParameters);
	}
}

//------------------
// STEP_3 移動檔案
// IBMMNRP
//------------------- 

	String zipFileName = USER_ID + "_"+t_yymm + "_repotZip";
	String zipReportPath = reportOutputPath +zipFileName+ "/" ;
	File zipTmpFolder = new File(zipReportPath);
	
	String t_yymm_name = SEARCHPARAM_1 +"年"+ SEARCHPARAM_2+"月";
	zipTmpFolder.mkdirs();
	
	for (int aRK_i = 0; aRK_i < allReportKind.length ; aRK_i++){
		String s_kind = allReportKind[aRK_i];
		reportFilename = USER_ID + "im51001" + "_"+s_kind ;
	
		if ("A".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-認定科" ;
		} else if ("B".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-拆除科";
		} else if ("C".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-廣告科";
		} else if ("D".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-勞安科";
		} else if ("E".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-總表" ;
		} else if ("F".equals(s_kind)) {
			outputFilename = t_yymm_name + "月報表-內控報表" ;
		}
		outputFilename += ".xlsx";
		reportFilename += ".xlsx";
		
		File oldFile = new File(reportOutputPath + reportFilename);
		File newFile = new File(zipReportPath + outputFilename);
		if(newFile.exists()){
		deleteFiles(newFile);
		}
		copyFileUsingJava7Files(oldFile, newFile);
		deleteFiles(oldFile);
	}
	
	
	for (int aRK_i = 0; aRK_i < allListReportKind.length ; aRK_i++){

		reportFilename = USER_ID + "im20101_" + allListReportKind[aRK_i];
		
		if("A_CHK".equals(allListReportKind[aRK_i]) ){
			outputFilename = "認定科_"+t_yymm_name+"認定案件清冊";
		}else if("A_PL".equals(allListReportKind[aRK_i]) ){
			outputFilename = "認定科_"+t_yymm_name+"拍照列管案件清冊" ;
		}else if("A_DEL".equals(allListReportKind[aRK_i]) ){
			outputFilename = "拆除科_"+t_yymm_name+"拆除案件清冊" ;
		}else if("B_CHK".equals(allListReportKind[aRK_i]) ){
			outputFilename = "廣拆科_"+t_yymm_name+"認定案件清冊";
		}else if("B_DEL".equals(allListReportKind[aRK_i]) ){
			outputFilename = "廣拆科_"+t_yymm_name+"拆除案件清冊" ;
		}else if("C_CHK".equals(allListReportKind[aRK_i]) ){
			outputFilename = "勞安科_"+t_yymm_name+"認定案件清冊";
		}else if("C_DEL".equals(allListReportKind[aRK_i]) ){
			outputFilename = "勞安科_"+t_yymm_name+"拆除案件清冊" ;
		}
		outputFilename += ".xlsx";
		reportFilename += ".xlsx";
		
		File oldFile = new File(reportOutputPath + reportFilename);
		File newFile = new File(zipReportPath + outputFilename);
		
		if(newFile.exists()){
		deleteFiles(newFile);
		}
		copyFileUsingJava7Files(oldFile, newFile);
		deleteFiles(oldFile);
		
	}
	
	
//------------------
// STEP_4 打包壓縮檔案
//------------------- 


		try {

				String zipPath = zipReportPath;
    			
    			reportPath = reportOutputPath;
    			
    			if (StringUtils.isEmpty(reportPath)) {
    				System.out.println("im51001_prt_2" + ".compress can not get zipPath path");
    			} else {
    
    	
    				String zipFilePath = "", zipTmpPath = "";
    				
    				File zipFileFolder, targetZipFile,  tmpZipFile;
    				
    				ZipOutputStream zos;
    				
    				
    				
				
					// zip file target path; <NAS path>/年度/index_key-last_modify/
					// 
					zipFilePath = reportOutputPath +  zipFileName;
					
					zipFileFolder = new File(zipFilePath);
					
					// check to see if zip file target path exists
					// if it exists, proceed to check the zip file existen
					// else print the log to stdout
					if (zipFileFolder.exists()) {
						String zipFileName_T = zipFileName+".zip";
						
						//System.err.println(className + " ::" +zipFileFolder.getPath() + zipFileName_T);
						targetZipFile = new File(zipFileFolder, zipFileName_T);
						/*
						if (targetZipFile.exists()) {
							// zip fil exists, do nothing
							targetZipFile.delete();
						} else {
						*/
						if (targetZipFile.exists()) {
							// zip fil exists, do nothing
							targetZipFile.delete();
						}
							// make sure there is at least 1 report file underneath of the zip file target path(<NAS path>/年度/index_key-last_modify/*.*)
							if (zipFileFolder.listFiles().length > 0) {
								// temporary file path for compressing file; 年度/index_key-last_modify-tmp/
								//zipTmpPath = zipPath + CASE_ID.substring(0,3) + SEPARATOR + CASE_ID + SEPARATOR + CASE_ID  + "-tmp/";
								zipTmpPath = reportOutputPath + zipFileName + "-tmp/";
								zipTmpFolder = new File(zipTmpPath);
								
								// create the temporary folder for file compressing purpose
								zipTmpFolder.mkdirs();
								
								tmpZipFile = new File(zipTmpFolder, zipFileName_T);
								
								// assuming the archive's size won't exceeds 4 GByte or there are less than 65535 entries inside the archive
								zos = new ZipOutputStream(new FileOutputStream(tmpZipFile));
								
								// traverse through all the file underneath the zip file target path(<NAS path>/年度/index_key-last_modify/*.*)
								for (File reportFile : zipFileFolder.listFiles()) {
									getAllFiles(reportFile, zos, reportFile.getName());
									//addToZipFile(reportFile, zos, reportFile.getName());
								}
								
								zos.close();
								
								// move compressed file by renaming it
								if (tmpZipFile.renameTo(targetZipFile)) {
									// delete the temporary folder
									zipTmpFolder.delete();
									
									
								} else {
									System.out.println("im51001_prt_2" + ".compress fail to move " + zipFileName_T);
									System.out.println("from " + zipTmpPath);
									System.out.println("to " + zipFilePath);
								}
								
								// release the memory resource immediately
								tmpZipFile = null;
								zipTmpFolder = null;
							} else {
								//System.out.println("im51001_prt_2" + ".compress " + CASE_ID + "-" + caseseq + " have 0 file at " + zipFilePath);
							} // end of ELSE: (zipFileFolder.listFiles().length > 0)
						//} // end of ELSE: (targetZipFile.exists())
					} else {
						System.out.println("im51001_prt_2" + ".compress can not find " + zipFilePath);
					} // end of ELSE: (zipFileFolder.exists())
					
					// release the memory resource immediately
					zipFileFolder = null;
				
    				
    				
    			}


		} catch (Exception eer) {
			System.out.println( "im51001_prt_2 STEP_4 error" + eer.toString());
			eer.printStackTrace();
		} finally {
		
		}
//------------------
// STEP_5 輸出壓縮檔案
//------------------- 

File reportFile = null;
OutputStream outputStream = null;
InputStream inputStream = null;

try {
	//reportFile = new File( reportOutputPath + reportFilename);
	reportFile = new File( (zipReportPath + zipFileName+".zip") );
	outputFilename = t_yymm_name + "各單位月報表與清冊.zip";
	
	if (reportFile.exists()) {
	
		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition","attachment; filename=\"" + URLEncoder.encode(outputFilename, "utf-8") + "\"");
		
		outputStream = response.getOutputStream();
		inputStream = new FileInputStream(reportFile);
		byte[] b = new byte[2048];
		int len;
		while((len = inputStream.read(b)) > 0) {
			outputStream.write(b ,0, len);
		}
		inputStream.close();
	}
} catch (Exception e) {
	out.println("Exception: " + e.toString());
	System.err.println("im20301_prt ::: Exception");
	e.printStackTrace();
} finally {
	// Clean the valur from the <UserMessage> session
	session.removeAttribute("UserMessage");
	
	outputStream.flush();
	outputStream.close();
	
	out.clear();
	out = pageContext.pushBody();
}


%>
<%!

%>
<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="expires" content="-1">
<ccs:meta header="Content-Type"/>
<title>im51001_prt_2</title>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//End CCS script
</script>
</head>
<body>
</body>
</html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-48CBBD4E--%>
<%im51001_prt_2Model.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-A7BD43B0--%>
<%im51001_prt_2Model.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

