package com.ezek.report;

import com.codecharge.db.DBTools;
import com.codecharge.db.DbRow;
import com.codecharge.db.JDBCConnection;
import com.codecharge.db.JDBCConnectionFactory;
import com.codecharge.util.StringUtils;
import com.codecharge.util.Utils;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedList;

public class IM40201 extends JReportAdapter {
	final String PROGREM_ID = "IM40201";
	String CASE_ID = "";
	LinkedList<String> CASE_ID_L = new LinkedList();
	int disnmCnt = 0;
	int disnmCntLast = 0;

	/**
	 * 產生報表（只產生第二聯）
	 * @param mParameters 報表參數
	 */
	public synchronized void produceSecondCopyOnly(HashMap<String, Object> mParameters) {
		// 設定只產生第二聯的參數
		mParameters.put("onlySecondCopy", "Y");
		produceReport(mParameters);
	}

	public synchronized void produceReport(HashMap<String, Object> mParameters) {
		super.produceReport(mParameters);
		JDBCConnection jdbcConn = null;
		DbRow dataRow = null;
		DbRow singleRowData = null;
		Enumeration dataRows = null;
		try {
			setCONNECTION_NAME("DBConn");
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");

			String[] conditionList = (String[]) this.parameters.get("conditionList");
			String case_id = conditionList[0];
			
			// 檢查是否只產生第二聯
			boolean onlySecondCopy = "Y".equals(this.parameters.get("onlySecondCopy"));

			String[] input_case_id = case_id.split(";");
			int pg_num = 0;

			int A4_cnt = 0;
			for (int k = 0; k < input_case_id.length; k++) {
				String pageCnt = Utils.convertToString(
						DBTools.dLookUp("count(A.case_id)", "IBMCASE A left join IBMDISNM B on A.case_id = B.case_id",
								"A.case_id = '" + input_case_id[k] + "'", "DBConn"));
				int pageCnt_int = Integer.parseInt(pageCnt);

				String IB_PRCS = Utils.convertToString(
						DBTools.dLookUp("IB_PRCS", "ibmcase", "case_id = '" + input_case_id[k] + "'", "DBConn"));
				if (onlySecondCopy) {
					// 如果只產生第二聯，每個案件只需要1頁
					A4_cnt += pageCnt_int;
				} else if ("C".equals(IB_PRCS)) {
					A4_cnt += 3 * pageCnt_int;
				} else {
					A4_cnt += 5 * pageCnt_int;
				}
			}
			this.hDataNums = new int[A4_cnt];
			this.dDataNums = new int[A4_cnt];
			this.imgNums = new int[A4_cnt];
			this.hasHeaderF = new boolean[A4_cnt];
			this.hasImageF = new boolean[A4_cnt];

			LinkedList<String> CASE_SEQ_L = new LinkedList();
			LinkedList<String> page_index = new LinkedList();
			for (int ii = 0; ii < input_case_id.length; ii++) {
				this.disnmCnt = 0;

				this.CASE_ID = input_case_id[ii];

				String IB_PRCS = Utils.convertToString(
						DBTools.dLookUp("IB_PRCS", "ibmcase", "case_id = '" + this.CASE_ID + "'", "DBConn"));

				String SQL = "select A.CASE_ID, CASE_SEQ from ibmcase A left join IBMDISNM B on A.CASE_ID = B.CASE_ID where A.case_id = '"
						+ this.CASE_ID + "' order by CASE_SEQ ";
				dataRows = jdbcConn.getRows(SQL);
				if ("C".equals(IB_PRCS)) {
					while ((dataRows != null) && (dataRows.hasMoreElements())) {
						dataRow = (DbRow) dataRows.nextElement();
						String CASE_SEQ = Utils.convertToString(dataRow.get("CASE_SEQ"));

						if (onlySecondCopy) {
							// 只產生第二聯
							CASE_SEQ_L.add(CASE_SEQ);
							this.jrFileNames.add("im40201_4.jasper");
							page_index.add(String.valueOf(1)); // 可以使用任意值，因為會在後面特別處理
							this.CASE_ID_L.add(this.CASE_ID);
						} else {
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							this.jrFileNames.add("im40201_3.jasper");
							this.jrFileNames.add("im40201_4.jasper");
							this.jrFileNames.add("im40201_4.jasper");
							for (int pg_index = 0; pg_index < 3; pg_index++) {
								page_index.add(String.valueOf(pg_index));
								this.CASE_ID_L.add(this.CASE_ID);
							}
						}
						this.disnmCnt += 1;
					}
					if (onlySecondCopy) {
						// 只產生第二聯的設定
						int[] tmp_hDataNums = { 46 };
						int[] tmp_dDataNums = new int[1];
						int[] tmp_imgNums = { 1 };
						boolean[] tmp_hasHeaderF = { true };
						boolean[] tmp_hasImageF = { true };
						for (int i = 0; i < this.disnmCnt; i++) {
							this.hDataNums[(this.disnmCntLast + i)] = tmp_hDataNums[0];
							this.dDataNums[(this.disnmCntLast + i)] = tmp_dDataNums[0];
							this.imgNums[(this.disnmCntLast + i)] = tmp_imgNums[0];
							this.hasHeaderF[(this.disnmCntLast + i)] = tmp_hasHeaderF[0];
							this.hasImageF[(this.disnmCntLast + i)] = tmp_hasImageF[0];
						}
						this.disnmCntLast += this.disnmCnt;
					} else {
						int[] tmp_hDataNums = { 46, 46, 46 };
						int[] tmp_dDataNums = new int[3];
						int[] tmp_imgNums = { 0, 1, 1 };
						boolean[] tmp_hasHeaderF = { true, true, true };
						boolean[] tmp_hasImageF = { false, true, true };
						for (int i = 0; i < this.disnmCnt; i++) {
							for (int j = 0; j < tmp_hDataNums.length; j++) {
								this.hDataNums[(this.disnmCntLast + i * 3 + j)] = tmp_hDataNums[j];
							}
							for (int j = 0; j < tmp_dDataNums.length; j++) {
								this.dDataNums[(this.disnmCntLast + i * 3 + j)] = tmp_dDataNums[j];
							}
							for (int j = 0; j < tmp_imgNums.length; j++) {
								this.imgNums[(this.disnmCntLast + i * 3 + j)] = tmp_imgNums[j];
							}
							for (int j = 0; j < tmp_hasHeaderF.length; j++) {
								this.hasHeaderF[(this.disnmCntLast + i * 3 + j)] = tmp_hasHeaderF[j];
							}
							for (int j = 0; j < tmp_hasImageF.length; j++) {
								this.hasImageF[(this.disnmCntLast + i * 3 + j)] = tmp_hasImageF[j];
							}
						}
						this.disnmCntLast += this.disnmCnt * 3;
					}
				} else {
					String Gow = "im40201_1.jasper";
					String Minsi = "im40201_2.jasper";
					if ("B".equals(IB_PRCS)) {
						Gow = "im40201_5.jasper";
						Minsi = "im40201_6.jasper";
					}
					while ((dataRows != null) && (dataRows.hasMoreElements())) {
						dataRow = (DbRow) dataRows.nextElement();
						String CASE_SEQ = Utils.convertToString(dataRow.get("CASE_SEQ"));
						
						if (onlySecondCopy) {
							// 只產生第二聯
							CASE_SEQ_L.add(CASE_SEQ);
							this.jrFileNames.add(Minsi);
							page_index.add(String.valueOf(1)); // 可以使用任意值，因為會在後面特別處理
							this.CASE_ID_L.add(this.CASE_ID);
						} else {
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							CASE_SEQ_L.add(CASE_SEQ);
							this.jrFileNames.add(Gow);
							this.jrFileNames.add(Minsi);
							this.jrFileNames.add(Minsi);
							this.jrFileNames.add(Minsi);
							this.jrFileNames.add(Minsi);
							for (int pg_index = 0; pg_index < 5; pg_index++) {
								page_index.add(String.valueOf(pg_index));
								this.CASE_ID_L.add(this.CASE_ID);
							}
						}
						this.disnmCnt += 1;
					}
					if (onlySecondCopy) {
						// 只產生第二聯的設定
						int[] tmp_hDataNums = { 46 };
						int[] tmp_dDataNums = new int[1];
						int[] tmp_imgNums = { 1 };
						boolean[] tmp_hasHeaderF = { true };
						boolean[] tmp_hasImageF = { true };
						for (int i = 0; i < this.disnmCnt; i++) {
							this.hDataNums[(this.disnmCntLast + i)] = tmp_hDataNums[0];
							this.dDataNums[(this.disnmCntLast + i)] = tmp_dDataNums[0];
							this.imgNums[(this.disnmCntLast + i)] = tmp_imgNums[0];
							this.hasHeaderF[(this.disnmCntLast + i)] = tmp_hasHeaderF[0];
							this.hasImageF[(this.disnmCntLast + i)] = tmp_hasImageF[0];
						}
						this.disnmCntLast += this.disnmCnt;
					} else {
						int[] tmp_hDataNums = { 46, 46, 46, 46, 46 };
						int[] tmp_dDataNums = new int[5];
						int[] tmp_imgNums = { 0, 1, 1, 1, 1 };
						boolean[] tmp_hasHeaderF = { true, true, true, true, true };
						boolean[] tmp_hasImageF = { false, true, true, true, true };
						for (int i = 0; i < this.disnmCnt; i++) {
							for (int j = 0; j < tmp_hDataNums.length; j++) {
								this.hDataNums[(this.disnmCntLast + i * 5 + j)] = tmp_hDataNums[j];
							}
							for (int j = 0; j < tmp_dDataNums.length; j++) {
								this.dDataNums[(this.disnmCntLast + i * 5 + j)] = tmp_dDataNums[j];
							}
							for (int j = 0; j < tmp_imgNums.length; j++) {
								this.imgNums[(this.disnmCntLast + i * 5 + j)] = tmp_imgNums[j];
							}
							for (int j = 0; j < tmp_hasHeaderF.length; j++) {
								this.hasHeaderF[(this.disnmCntLast + i * 5 + j)] = tmp_hasHeaderF[j];
							}
							for (int j = 0; j < tmp_hasImageF.length; j++) {
								this.hasImageF[(this.disnmCntLast + i * 5 + j)] = tmp_hasImageF[j];
							}
						}
						this.disnmCntLast += this.disnmCnt * 5;
					}
				}
			}
			init();
			for (this.index = 0; this.index < this.jrFileNames.size(); this.index += 1) {
				this.jReportData = ((JReportData) this.jReportDataList.get(this.index));
				pg_num = Integer.parseInt((String) page_index.get(this.index));

				this.CASE_ID = ((String) this.CASE_ID_L.get(this.index));

				String IB_PRCS = "";
				String REG_UNIT = "";
				String DIS_UNIT = "";
				String dis_b_way = "";
				String dis_b_fl = "";
				String dis_b_add9 = "";
				singleRowData = jdbcConn.getOneRow(
						"SELECT IB_PRCS, REG_UNIT, DIS_UNIT,dis_b_way ,dis_b_fl, dis_b_add9 FROM IBMCASE WHERE case_id = '"
								+ this.CASE_ID + "' ");
				if (singleRowData != null) {
					IB_PRCS = Utils.convertToString(singleRowData.get("IB_PRCS"));
					REG_UNIT = Utils.convertToString(singleRowData.get("REG_UNIT"));
					DIS_UNIT = Utils.convertToString(singleRowData.get("DIS_UNIT"));
					dis_b_way = Utils.convertToString(singleRowData.get("dis_b_way"));
					dis_b_fl = Utils.convertToString(singleRowData.get("dis_b_fl"));
					dis_b_add9 = Utils.convertToString(singleRowData.get("dis_b_add9"));
				}
				if (this.jReportData.isHasHeader()) {
					genHeaderSQL2((String) CASE_SEQ_L.get(this.index));

					this.jReportData.genHeaderData();
					if ("C".equals(IB_PRCS)) {
						this.jReportData.getHeaderData().put("H18", "上列違章建築，業經本大隊於　年　月　日依法拆除完畢，同意銷案。");
						this.jReportData.getHeaderData().put("H19", "上列違章建築，已依法申請補辦建造執照手續，並核發　　字　第　號建造執照在案，同意銷案。");
						this.jReportData.getHeaderData().put("H20", "上列違章建築，業經本大隊以　年　月　日　　　　　字第　　號函撤銷違建認定處分，同意銷案。");
						this.jReportData.getHeaderData().put("H21",
								"上列違章建築，業經本大隊會同工程主辦機關及監造(施工)單位共同確認，本案污水下水道用戶接管工程所需施作空間已足夠。");
						this.jReportData.getHeaderData().put("H22",
								"上列違章建築，業經本大隊會同工程主辦機關及監造(施工)單位共同確認，該污水下水道用戶接管工程已完工或完成驗收(計價)程序。");
						this.jReportData.getHeaderData().put("H23", "上列違章建築，業經水利局設施維護單位確認，拆除後已無影響污水下水道用戶排水維護之困難。");
						this.jReportData.getHeaderData().put("H24", "其他");
					} else if ("B".equals(IB_PRCS)) {
						this.jReportData.getHeaderData().put("H18", "上列違規廣告物，業經本大隊於　年　月　日依法拆除完畢，同意銷案。");
						this.jReportData.getHeaderData().put("H19",
								"上列違規廣告物，業經本大隊於　年　月　日派員勘查，已（□改善至雜項執照規模以下□自行改善□標的變異，另行處分），同意銷案。");
						this.jReportData.getHeaderData().put("H20", "上列違規廣告物，業經本大隊於　年　月　日派員勘查，現場已不存在（恢復原狀），同意銷案。");
						this.jReportData.getHeaderData().put("H21",
								"上列違規廣告物，已依法申請審查許可，並核發  字第     號（□建造執照□雜項執照□許可證）在案，同意銷案。");
						this.jReportData.getHeaderData().put("H22", "上列違規廣告物，業經本大隊以　年　月　日　　　　　字第　　號函撤銷違建認定處分，同意銷案。");
					} else {
						this.jReportData.getHeaderData().put("H18", "上列違章建築，業經本大隊於　年　月　日依法拆除完畢，同意銷案。");
						this.jReportData.getHeaderData().put("H19", "上列違章建築，業經本大隊於　年　月　日派員勘查，已達不堪使用標準，同意銷案。");
						this.jReportData.getHeaderData().put("H20",
								"上列違章建築，業經本大隊於　年　月　日派員勘查，已符合「新北市合法建物增設一定規模以下構造物處理要點」之規定，同意銷案。");
						this.jReportData.getHeaderData().put("H21", "上列違章建築，業經本大隊於　年　月　日派員勘查，現場已不存在（恢復原狀），同意銷案。");
						this.jReportData.getHeaderData().put("H22", "上列違章建築，已依法申請補辦建造執照手續，並核發　　字　第　號建造執照在案，同意銷案。");
						this.jReportData.getHeaderData().put("H23", "上列違章建築，業經本大隊以　年　月　日　　　　　字第　　號函撤銷違建認定處分，同意銷案。");
						this.jReportData.getHeaderData().put("H24", "上列違章建築，業經本大隊於　年　月　日勘查確認符合專案解列標準，全案移拍照建檔列管。");
					}
					String all_addr = (String) this.jReportData.getHeaderData().get("H8");

					this.jReportData.getHeaderData().put("H8", all_addr);

					String FLNUM = (String) this.jReportData.getHeaderData().get("H1");
					this.jReportData.getHeaderData().put("H1", "檔　　號：" + FLNUM);

					String FLNYY = (String) this.jReportData.getHeaderData().get("H2");
					this.jReportData.getHeaderData().put("H2", "保存年限：" + FLNYY + "年");

					String dis_b_addzon = (String) this.jReportData.getHeaderData().get("H4");
					String dis_b_addzon_desc = "";
					if (!StringUtils.isEmpty(dis_b_addzon)) {
						if ("C".equals(IB_PRCS)) {
							dis_b_addzon = dis_b_addzon + "公所";
						} else {
							dis_b_addzon = dis_b_addzon + "公所、本府環保局";
						}
					} else if (!"C".equals(IB_PRCS)) {
						dis_b_addzon = "本府環保局";
					}
					this.jReportData.getHeaderData().put("H4", dis_b_addzon);

					String end_date = (String) this.jReportData.getHeaderData().get("H27");
					String end_reg_yy = (String) this.jReportData.getHeaderData().get("H28");
					String end_reg_no = (String) this.jReportData.getHeaderData().get("H29");
					String end_num = "";
					if ((end_reg_yy == null) || ("".equals(end_reg_yy.trim())) || ("null".equals(end_reg_yy))) {
						end_reg_yy = "";
						end_reg_no = "　　　　　";
					}
					if ("C".equals(IB_PRCS)) {
						end_num = "新北拆勞字第" + end_reg_yy + end_reg_no + "號";
					} else if ("A".equals(IB_PRCS)) {
						if ("021".equals(DIS_UNIT)) {
							end_num = end_num + "新北拆拆一字第";
						} else if ("022".equals(DIS_UNIT)) {
							end_num = end_num + "新北拆拆二字第";
						}
						
						end_num =(!StringUtils.isEmpty(end_num) ? end_num : "         ") + (!StringUtils.isEmpty(end_reg_yy) ? end_reg_yy : "   " )+ (!StringUtils.isEmpty(end_reg_no)?end_reg_no:"              ") + "號";
						
									
						
					} else if ("B".equals(IB_PRCS)) {
						if(!StringUtils.isEmpty(end_reg_yy) && !StringUtils.isEmpty(end_reg_no))
						{
							end_num = "新北拆廣字第" + end_reg_yy + end_reg_no + "號";
						}
						else 
						{
							end_num = "新北拆廣字第";
						}
					}
					if (!StringUtils.isEmpty(end_date)) {
						int end_date_length = end_date.length();
						end_date = end_date.substring(0, end_date_length - 4) + "年"
								+ end_date.substring(end_date_length - 4, end_date_length - 2) + "月"
								+ end_date.substring(end_date_length - 2) + "日";
					} else {
						end_date = "   年   月  日";
					}
					end_num = end_date + end_num;
					this.jReportData.getHeaderData().put("H5", end_num);

					String date = (String) this.jReportData.getHeaderData().get("H30");
					String reg_yy = (String) this.jReportData.getHeaderData().get("H31");
					String reg_no = (String) this.jReportData.getHeaderData().get("H32");
					String reg_num = "";
					if ((reg_yy == null) || ("".equals(reg_yy.trim())) || ("null".equals(reg_yy))) {
						reg_yy = "";
						reg_no = "　　　　　";
					}
					if ("C".equals(IB_PRCS)) {
						
						reg_num = "新北拆勞字第" + reg_yy + reg_no + "號";
					} else if ("A".equals(IB_PRCS)) {
						if ("011".equals(REG_UNIT)) {
							reg_num = reg_num + "新北拆認一字第";
						} else if ("012".equals(REG_UNIT)) {
							reg_num = reg_num + "新北拆認二字第";
						}
						reg_num = reg_num + reg_yy + reg_no + "號";
					} else if ("B".equals(IB_PRCS)) {
						reg_num = "新北拆廣字第" + reg_yy + reg_no + "號";
					}
					if (!StringUtils.isEmpty(date)) {
						int date_length = date.length();
						date = date.substring(0, date_length - 4) + "年"
								+ date.substring(date_length - 4, date_length - 2) + "月"
								+ date.substring(date_length - 2) + "日";
					} else {
						date = "   年   月   日";
					}
					this.jReportData.getHeaderData().put("H6", date + reg_num);

					String prjNM = Utils.convertToString(DBTools.dLookUp("B.code_desc",
							"ibmcsprj A left join ibmcode B on A.prj_code = B.code_seq and B.code_type = 'PRJNM'",
							"CASE_ID = '" + this.CASE_ID + "' ", "DBConn"));
					this.jReportData.getHeaderData().put("H26", prjNM);

					String end_way = (String) this.jReportData.getHeaderData().get("H41");
					if (!StringUtils.isEmpty(end_way)) {
						if ("01".equals(end_way)) {
							this.jReportData.getHeaderData().put("H9", "ˇ");
						} else if ("02".equals(end_way)) {
							this.jReportData.getHeaderData().put("H10", "ˇ");
						}
					}
					String b_end_item = (String) this.jReportData.getHeaderData().get("H33");
					String b_finish_date = (String) this.jReportData.getHeaderData().get("H34");
					String end_chk_date = (String) this.jReportData.getHeaderData().get("H35");
					String end_lic_word = (String) this.jReportData.getHeaderData().get("H36");
					String end_lic_num = (String) this.jReportData.getHeaderData().get("H37");
					String revoke_date = (String) this.jReportData.getHeaderData().get("H38");
					String revoke_word = (String) this.jReportData.getHeaderData().get("H39");
					String revoke_num = (String) this.jReportData.getHeaderData().get("H40");
					String end_way_memo = (String) this.jReportData.getHeaderData().get("H42");
					String licence_kind = (String) this.jReportData.getHeaderData().get("H43");
					String licence_word = (String) this.jReportData.getHeaderData().get("H44");
					String licence_no = (String) this.jReportData.getHeaderData().get("H45");
					String licence_yy = (String) this.jReportData.getHeaderData().get("H46");
					String LICENCE_KIND_DESC = "□建造執照□雜項執照□許可證";
					String END_WAY_MEMO_DESC = "□改善至雜項執照規模以下□自行改善□標的變異，另行處分";
					if (!StringUtils.isEmpty(b_finish_date)) {
						int b_finish_date_length = b_finish_date.length();
						b_finish_date = b_finish_date.substring(0, b_finish_date_length - 4) + "年"
								+ b_finish_date.substring(b_finish_date_length - 4, b_finish_date_length - 2) + "月"
								+ b_finish_date.substring(b_finish_date_length - 2) + "日";
					} else {
						b_finish_date = "　年　月　日";
					}
					if (!StringUtils.isEmpty(end_chk_date)) {
						int end_chk_date_length = end_chk_date.length();
						end_chk_date = end_chk_date.substring(0, end_chk_date_length - 4) + "年"
								+ end_chk_date.substring(end_chk_date_length - 4, end_chk_date_length - 2) + "月"
								+ end_chk_date.substring(end_chk_date_length - 2) + "日";
					} else {
						end_chk_date = "　年　月　日";
					}
					if (StringUtils.isEmpty(end_lic_word)) {
						end_lic_word = "　　";
					}
					if (StringUtils.isEmpty(end_lic_num)) {
						end_lic_num = "　　";
					}
					if (StringUtils.isEmpty(licence_yy)) {
						licence_yy = "　　";
					} else {
						licence_yy = licence_yy + "年";
					}
					if (StringUtils.isEmpty(licence_word)) {
						licence_word = "　　";
					}
					if (StringUtils.isEmpty(licence_no)) {
						licence_no = "　　";
					}
					if (!StringUtils.isEmpty(revoke_date)) {
						int revoke_date_length = revoke_date.length();
						revoke_date = revoke_date.substring(0, revoke_date_length - 4) + "年"
								+ revoke_date.substring(revoke_date_length - 4, revoke_date_length - 2) + "月"
								+ revoke_date.substring(revoke_date_length - 2) + "日";
					} else {
						revoke_date = "　年　月　日";
					}
					if (StringUtils.isEmpty(revoke_word)) {
						revoke_word = "　　　　　";
					}
					if (StringUtils.isEmpty(revoke_num)) {
						revoke_num = "　　";
					}
					if (!StringUtils.isEmpty(licence_kind)) {
						if ("01".equals(licence_kind)) {
							LICENCE_KIND_DESC = "■建造執照□雜項執照□許可證";
						} else if ("02".equals(licence_kind)) {
							LICENCE_KIND_DESC = "□建造執照■雜項執照□許可證";
						} else if ("03".equals(licence_kind)) {
							LICENCE_KIND_DESC = "□建造執照□雜項執照■許可證";
						}
					}
					if (!StringUtils.isEmpty(end_way_memo)) {
						if ("01".equals(end_way_memo)) {
							END_WAY_MEMO_DESC = "■改善至雜項執照規模以下□自行改善□標的變異，另行處分";
						} else if ("02".equals(end_way_memo)) {
							END_WAY_MEMO_DESC = "□改善至雜項執照規模以下■自行改善□標的變異，另行處分";
						} else if ("03".equals(end_way_memo)) {
							END_WAY_MEMO_DESC = "□改善至雜項執照規模以下□自行改善■標的變異，另行處分";
						}
					}
					if ("C".equals(IB_PRCS)) {
						if ("01".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H11", "ˇ");
							this.jReportData.getHeaderData().put("H18",
									"上列違章建築，業經本大隊於" + b_finish_date + "依法拆除完畢，同意銷案。");
						} else if ("02".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H12", "ˇ");
							this.jReportData.getHeaderData().put("H19", "上列違章建築，已依法申請補辦建造執照手續，並核發" + licence_yy
									+ licence_word + "字第" + licence_no + "號建造執照在案，同意銷案。");
						} else if ("03".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H13", "ˇ");
							this.jReportData.getHeaderData().put("H20", "上列違章建築，業經本大隊以" + revoke_date + revoke_word
									+ "字第" + revoke_num + "號函撤銷違建認定處分，同意銷案。");
						} else if ("04".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H14", "ˇ");
						} else if ("05".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H15", "ˇ");
						} else if ("06".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H16", "ˇ");
						} else if ("07".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H17", "ˇ");
						}
					} else if ("B".equals(IB_PRCS)) {
						if ("01".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H11", "ˇ");
							this.jReportData.getHeaderData().put("H18",
									"上列違規廣告物，業經本大隊於" + b_finish_date + "依法拆除完畢，同意銷案。");
						} else if ("02".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H12", "ˇ");
							this.jReportData.getHeaderData().put("H19",
									"上列違規廣告物，業經本大隊於" + b_finish_date + "派員勘查，已(" + END_WAY_MEMO_DESC + ")，同意銷案。");
						} else if ("03".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H13", "ˇ");
							this.jReportData.getHeaderData().put("H20",
									"上列違規廣告物，業經本大隊於" + b_finish_date + "派員勘查，現場已不存在（恢復原狀），同意銷案。");
						} else if ("04".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H14", "ˇ");
							this.jReportData.getHeaderData().put("H21", "上列違規廣告物，已依法申請審查許可，並核發" + licence_yy
									+ licence_word + "字第" + licence_no + "號(" + LICENCE_KIND_DESC + ")在案，同意銷案。");
						} else if ("05".equals(b_end_item)) {
							this.jReportData.getHeaderData().put("H15", "ˇ");
							this.jReportData.getHeaderData().put("H22", "上列違規廣告物，業經本大隊以" + revoke_date + revoke_word
									+ "字第" + revoke_num + "號函撤銷違建認定處分，同意銷案。");
						}
					} else if ("01".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H11", "ˇ");
						this.jReportData.getHeaderData().put("H18", "上列違章建築，業經本大隊於" + b_finish_date + "依法拆除完畢，同意銷案。");
					} else if ("02".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H12", "ˇ");
						this.jReportData.getHeaderData().put("H19",
								"上列違章建築，業經本大隊於" + end_chk_date + "派員勘查，已達不堪使用標準，同意銷案。");
					} else if ("03".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H13", "ˇ");
						this.jReportData.getHeaderData().put("H20",
								"上列違章建築，業經本大隊於" + end_chk_date + "派員勘查，已符合「新北市合法建物增設一定規模以下構造物處理要點」之規定，同意銷案。");
					} else if ("04".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H14", "ˇ");
						this.jReportData.getHeaderData().put("H21",
								"上列違章建築，業經本大隊於" + end_chk_date + "派員勘查，現場已不存在（恢復原狀），同意銷案。");
					} else if ("05".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H15", "ˇ");
						this.jReportData.getHeaderData().put("H22", "上列違章建築，已依法申請補辦建造執照手續，並核發" + licence_yy
								+ licence_word + "字第" + licence_no + "號建造執照在案，同意銷案。");
					} else if ("06".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H16", "ˇ");
						this.jReportData.getHeaderData().put("H23",
								"上列違章建築，業經本大隊以" + revoke_date + revoke_word + "字第" + revoke_num + "號函撤銷違建認定處分，同意銷案。");
					} else if ("07".equals(b_end_item)) {
						this.jReportData.getHeaderData().put("H17", "ˇ");
						this.jReportData.getHeaderData().put("H24",
								"上列違章建築，業經本大隊於" + end_chk_date + "勘查確認符合專案解列標準，全案移拍照建檔列管。");
					}
					// === IM52101 專用邏輯開始 ===
					// 如果是只產生第二聯的模式，直接設定第二聯標題
					if (onlySecondCopy) {
						if ("C".equals(IB_PRCS)) {
							this.jReportData.getHeaderData().put("H25", "第二聯（移勞安科）");
						} else {
							this.jReportData.getHeaderData().put("H25", "第二聯（移本府拆除大隊）");
						}
					} else {
						// === IM52101 專用邏輯結束 ===
						// 原本的邏輯保持不變
						if ("C".equals(IB_PRCS)) {
							if (pg_num % 3 == 1) {
								this.jReportData.getHeaderData().put("H25", "第一聯（寄違建人）");
							} else if (pg_num % 3 == 2) {
								this.jReportData.getHeaderData().put("H25", "第二聯（移勞安科）");
							}
						} else if (pg_num % 5 == 1) {
							this.jReportData.getHeaderData().put("H25", "第一聯（郵寄違建人地址）");
						} else if (pg_num % 5 == 2) {
							this.jReportData.getHeaderData().put("H25", "第二聯（移本府拆除大隊）");
						} else if (pg_num % 5 == 3) {
							this.jReportData.getHeaderData().put("H25", "第三聯（郵寄公所）");
						} else if (pg_num % 5 == 4) {
							this.jReportData.getHeaderData().put("H25", "第四聯（寄本府環保局）");
						}
					}
				}
				this.data = new ArrayList();
				this.jReportData.setData(this.data);
				genDetailSQL();
				this.jReportData.genDetailData();
				processDetailData();
				if (this.jReportData.isHasImage()) {
					genImageSQL(pg_num);
					this.jReportData.genImage();
				}
			}
			exportReport();
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(this.CLASS_NAME + ".produceReport Error is " + e);
		} finally {
			if (this.fileOut != null) {
				try {
					this.fileOut.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (jdbcConn != null) {
				jdbcConn.closeConnection();
			}
		}
	}

	protected void genHeaderSQL2(String idx) {
		String case_id = this.CASE_ID;

		String DISNMCnt = Utils
				.convertToString(DBTools.dLookUp("count(*)", "IBMDISNM", "case_id = '" + case_id + "'", "DBConn"));
		String IB_PRCS = Utils
				.convertToString(DBTools.dLookUp("IB_PRCS", "ibmcase", "case_id = '" + case_id + "'", "DBConn"));
		this.sql.setLength(0);

		this.sql.append(" SELECT F.code_desc AS H1, G.code_desc AS H2, B.IB_USER AS H3, H.CODE_DESC AS H4, '' AS H5");

		this.sql.append(" , '' AS H6, B.USR_ADD AS H7, CADDRESS AS H8");
		this.sql.append(" , '' AS H9, '' AS H10");
		this.sql.append(" , '' AS H11, '' AS H12, '' AS H13, '' AS H14, '' AS H15, '' AS H16, '' AS H17");
		this.sql.append(" , '' AS H18, '' AS H19, '' AS H20, '' AS H21, '' AS H22, '' AS H23, '' AS H24");
		this.sql.append(" , '' AS H25");
		this.sql.append(" , '污水下水道用戶排水設備工程' AS H26");
		this.sql.append(" , A.END_DATE AS H27, A.END_REG_YY AS H28, A.END_REG_NO AS H29");
		this.sql.append(" , A.REG_DATE AS H30, A.REG_YY AS H31, A.REG_NO AS H32");
		this.sql.append(" , A.B_END_ITEM AS H33");
		this.sql.append(" , A.B_FINISH_DATE AS H34, A.END_CHK_DATE AS H35");
		this.sql.append(
				" , A.END_LIC_WORD AS H36, A.END_LIC_NUM AS H37, A.REVOKE_DATE AS H38, A.REVOKE_WORD AS H39, A.REVOKE_NUM AS H40, A.END_WAY AS H41");
		this.sql.append(" , A.END_WAY_MEMO AS H42, A.LICENCE_KIND AS H43");
		this.sql.append(" , A.LICENCE_WORD AS H44, A.LICENCE_NO AS H45, A.LICENCE_YY AS H46");

		this.sql.append(" from IBMCASE A left join IBMDISNM B on A.case_id = B.case_id ");
		this.sql.append(" left join IBMCODE C on A.REG_UNIT = C.code_seq and C.code_type = 'IM_UNIT' ");
		this.sql.append(" left join IBMCODE D on A.BUILDING_CATEGORY = D.code_seq and D.code_type = 'BUDCGY' ");
		this.sql.append(" left join IBMCODE E on A.FINISH_STATE = E.code_seq and E.code_type = 'FNHSTT' ");
		this.sql.append(" left join IBMCODE H on A.DIS_B_ADDZON = H.code_seq and H.code_type = 'ZON' ");
		if ("A".equals(IB_PRCS)) {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0203' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0203' ");
		} else if ("B".equals(IB_PRCS)) {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0403' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0403' ");
		} else {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0303' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0303' ");
		}
		if ("0".equals(DISNMCnt)) {
			this.sql.append(" where A.case_id = '" + case_id + "'");
		} else {
			this.sql.append(" where B.case_id = '" + case_id + "' and  B.CASE_SEQ = '" + idx + "' ");
		}
		this.jReportData.setSql(this.sql);
	}

	protected void genDetailSQL() {
		this.sql.setLength(0);
		this.sql.append("select '' d1 ");
		this.jReportData.setSql(this.sql);
	}

	protected void genImageSQL(int pg_num) {
		String picpath = Utils.convertToString(
				DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICPATH' and code_seq = '01'", "DBConn"));
		String case_id = this.CASE_ID;

		String IB_PRCS = Utils
				.convertToString(DBTools.dLookUp("IB_PRCS", "ibmcase", "case_id = '" + case_id + "'", "DBConn"));

		this.sql.setLength(0);

		this.sql.append(" SELECT '" + this.appPath + picpath + "' AS img");
		this.jReportData.setSql(this.sql);
	}

	protected void processDetailData() {
		for (int i = 0; i < this.jReportData.getDetailData().size(); i++) {
			HashMap<String, Object> item = (HashMap) this.jReportData.getDetailData().get(i);
			this.data.add(new IM40201_Bean((String) item.get("D1")));
		}
	}

	public static class IM40201_Bean {
		private String d1;

		public IM40201_Bean(String mD1) {
			setD1(mD1);
		}

		public String getD1() {
			return this.d1;
		}

		public void setD1(String d1) {
			this.d1 = d1;
		}
	}
}
