<%--JSP Page Init @1-81418264--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new MasterPageServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-1F0D87E7--%>
<%@include file="MasterPageHandlers.jsp"%>
<%
    if (!MasterPageModel.isVisible()) return;
    if (MasterPageParent != null) {
        if (!MasterPageParent.getChild(MasterPageModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", MasterPageModel);
    pageContext.setAttribute("page", MasterPageModel);
    MasterPageModel.fireOnInitializeViewEvent(new Event());
    MasterPageModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) MasterPageModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|";
    String includes = (String) request.getAttribute("scriptIncludes");
    request.setAttribute("scriptIncludes", includes + scripts);

    if (!MasterPageModel.isVisible()) return;
    ((ModelAttribute) curPage.getAttribute("pathToCurrentPage")).setValue(pathToRoot + "Designs/Light/");
%>
<%--End Page Body--%>

<%--JSP Page Content @1-7445AF27--%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale = 1.0, maximum-scale = 1.0, user-scalable = no, width = device-width">
    <!--[if lt IE 9]><script src="https://html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->
    <link rel="stylesheet" href="{page:pathToCurrentPage}style.css" media="screen">
    <link rel="stylesheet" href="{page:pathToCurrentPage}jquery-ui.css" media="screen">
    <!--[if lte IE 7]><link rel="stylesheet" href="{page:pathToCurrentPage}style.ie7.css" media="screen" /><![endif]-->
    <link rel="stylesheet" href="{page:pathToCurrentPage}style.responsive.css" media="all">
<link rel="shortcut icon" href="{page:pathToCurrentPage}favicon.ico" type="image/x-icon">
    <script src="{page:pathToCurrentPage}jquery.js"></script>
    <script src="{page:pathToCurrentPage}script.js"></script>
    <script src="{page:pathToCurrentPage}script.responsive.js"></script>
	<link rel="shortcut icon" href="{page:pathToCurrentPage}favicon.ico" type="image/x-icon" />
	<ccs:contentplaceholder name='Head'></ccs:contentplaceholder>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//End CCS script
</script>
</head>
<body>    
<div id="main">
    <div class="sheet clearfix">
<header class="header"><div class="widget"><ccs:contentplaceholder name='HeaderSidebar'></ccs:contentplaceholder></div>
    <div class="shapes">
</div>
<h1 class="headline" data-left="60.79%">
    <a href="#">Headline</a>
</h1>
<h2 class="slogan" data-left="60.79%">Slogan text</h2>
</header>
<nav class="nav">
    <ul class="hmenu"><ccs:contentplaceholder name='Menu'></ccs:contentplaceholder></ul> 
    </nav>
<div class="layout-wrapper">
                <div class="content-layout">
                    <div class="content-layout-row">
                        <div class="layout-cell sidebar1"><ccs:contentplaceholder name='Sidebar1'></ccs:contentplaceholder></div>
                        <div class="layout-cell content"><article class="post article"><div class="postcontent postcontent-0"><ccs:contentplaceholder name='Content'></ccs:contentplaceholder></div></article></div>
                    </div>
                </div>
            </div><footer class="footer"><p>Copyright &copy; 2013. All Rights Reserved.</p></div></footer>
    </div>
</div>
<center><font face="Arial"><small><center><font face="Arial"><small>&#71;&#101;n&#101;&#114;a&#116;ed <!-- CCS -->&#119;it&#104; <!-- CCS -->&#67;od&#101;&#67;&#104;&#97;&#114;&#103;e <!-- CCS -->S&#116;&#117;&#100;&#105;o.</small></font></center></small></font></center></body>
</html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-66D95782--%>
<%MasterPageModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-89AFA97C--%>
<%MasterPageModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

