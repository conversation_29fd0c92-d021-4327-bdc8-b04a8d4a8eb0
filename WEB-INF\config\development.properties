# =====================================================
# 新北市違章建築管理系統 - 開發環境配置
# =====================================================
# 檔案名稱: development.properties
# 環境: 開發環境
# 說明: 本地開發使用的配置，包含測試資料庫連線
# =====================================================

# ===== 環境設定 =====
system.mode=development
system.debug=true
system.maintenance=false

# ===== 資料庫配置 =====
# 主要資料庫 (PostgreSQL) - 本地開發資料庫
DBConn.name=DBConn
DBConn.url=****************************************
DBConn.driver=org.postgresql.Driver
DBConn.user=postgres
DBConn.password=${DB_PRIMARY_PASSWORD:dev_password_123}
DBConn.maxconn=10
DBConn.timeout=300
DBConn.dbType=PostgreSQL

# 次要資料庫 (SQL Server) - 本地測試GIS資料庫
DBConn2.name=DBConn2
DBConn2.url=********************************************************
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.user=dev_user
DBConn2.password=${DB_SECONDARY_PASSWORD:dev_gis_password_123}
DBConn2.maxconn=5
DBConn2.timeout=300
DBConn2.dbType=SQLServer

# ===== 應用程式設定 =====
serverUrl=http://localhost:8080/bms
securedUrl=https://localhost:8443/bms
third.party.service.base.url=http://localhost:8081/third-party/
google.maps.service.url=maps/index

# ===== 安全性設定 =====
security.type=CCS
security.encryption.key=${ENCRYPTION_KEY:DEV_ENCRYPTION_KEY_2025}
security.auth.cookie.name=bmsDevLogin
security.auth.cookie.expire=1440
security.hash.algorithm=SHA-256
ALEncryptionKey=DEV_J46630r7lrS0rYui

# ===== 日誌設定 =====
logpriority=debug
logfile=logs/bms-dev.log
logsize=5120

# ===== 本地化設定 =====
language=zh
defaultLocale=zh_TW
requestEncoding=UTF-8
encoding=UTF-8
defaultDateFormat=yyyy-MM-dd
defaultBooleanFormat=true;false

# ===== 效能設定 =====
cache.enabled=false
cache.size=100
session.timeout=60
upload.max.size=52428800

# ===== 檔案路徑設定 =====
model.folder=
designs.folder=Designs
upload.folder=uploads/dev
temp.folder=temp/dev

# ===== 開發專用設定 =====
useDynamicStyles=true
defaultStyle=Austere
useDynamicDesigns=true
defaultDesign=Light
useI18nFeatures=true
isXHTMLUsed=false
usedWarFile=false
useAmp=true

# ===== 測試與除錯設定 =====
test.data.enabled=true
mock.third.party.services=true
sql.debug.enabled=true
performance.monitoring.enabled=true

# ===== 開發工具設定 =====
hot.reload.enabled=true
auto.refresh.enabled=true
dev.tools.enabled=true
stack.trace.enabled=true