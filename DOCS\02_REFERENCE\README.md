# 📚 新北市違章建築管理系統 - 參考文件索引

本目錄包含新北市違章建築管理系統的所有技術和業務參考文件。系統已穩定運行30年，管理超過37萬筆案件和100萬筆流程記錄。

## 🏗️ 系統概覽

- **系統類型**: 政府違章建築管理系統（Legacy系統）
- **技術架構**: CodeCharge Studio三層架構（JSP+XML+Handlers）
- **資料庫架構**: PostgreSQL + SQL Server雙資料庫系統
- **系統規模**: 371,081筆案件，1,025,000+筆流程記錄
- **運行時間**: 30年以上持續演進

## 📖 核心系統文件

### ⭐ 高優先級文件 (必讀)

#### 1. **[商業流程完整指南](./BUSINESS_PROCESS_COMPLETE_GUIDE.md)**
- **摘要**: 詳細說明違章建築管理的完整生命週期，從案件登記到拆除結案的所有工作流程和決策點
- **關鍵內容**: 三階段處理系統（認定→排拆→結案）、狀態碼系統設計（XYZ格式）、跨部門協同機制、品質控制機制（92c系統）
- **重要性**: 理解系統業務邏輯和工作流程的基礎

#### 2. **[資料庫完整指南](./DATABASE_COMPLETE_GUIDE.md)**
- **摘要**: 涵蓋雙資料庫架構（PostgreSQL + SQL Server）、核心表格結構和91個觸發器系統的技術指南
- **關鍵內容**: 核心表格結構（buildcase、tbflow、ibmcode等）、IBMCODE參數系統（78種代碼類型）、索引策略和查詢優化
- **重要性**: 資料庫操作和系統維護的關鍵參考

#### 3. **[系統架構總覽](./SYSTEM_ARCHITECTURE_OVERVIEW.md)**
- **摘要**: 提供系統技術架構的高層次視圖，包括CodeCharge Studio框架、部署結構和整合點
- **關鍵內容**: 三層架構（JSP + XML + Handlers）、技術堆疊（Tomcat 9.0.98、Bootstrap 5.3.3、jQuery 3.7.1）、模組組織和檔案結構
- **重要性**: 理解系統結構和技術債務的基礎

#### 4. **[技術實施指南](./TECHNICAL_IMPLEMENTATION_GUIDE.md)**
- **摘要**: 開發者實戰指南，涵蓋環境設置、部署程序、編碼標準和最佳實踐
- **關鍵內容**: 開發環境設置、CodeCharge開發模式、API開發標準、前端開發（Bootstrap/jQuery）、安全實施指南
- **重要性**: 系統開發和維護的必備參考

### 📋 功能特定文件

#### 5. **[協同機制完整指南](./COLLABORATION_MECHANISM_COMPLETE_GUIDE.md)**
- **摘要**: 記錄跨部門協同機制，包括協同退回功能的演進過程
- **關鍵內容**: 協同狀態碼（234/244/254）、協同退回機制（235/245/255）、不同角色的權限矩陣
- **重要性**: 理解多使用者案件處理的重要參考

#### 6. **[協同退回實施指南](./COLLABORATION_RETURN_IMPLEMENTATION_GUIDE.md)**
- **摘要**: 協同退回功能的實際實施手冊，包括資料庫變更、程式碼實作和部署程序
- **關鍵內容**: 資料庫架構變更、完整API實作（case_collaboration_return.jsp）、前端UI修改、測試程序
- **重要性**: 特定於協同退回功能實施

#### 7. **[拆除案件分派機制分析](./DEMOLITION_CASE_ASSIGNMENT_MECHANISM_ANALYSIS.md)**
- **摘要**: 分析拆除案件分派機制，詳細說明從案件認定到拆除分派的工作流程
- **關鍵內容**: 跨兩個拆除單位的分派邏輯、權限控制、im40301_lis.jsp中實施的業務規則
- **重要性**: 理解系統行為的業務邏輯文件

### 🔧 環境與配置文件

#### 8. **[環境變數管理計畫](./ENVIRONMENT_VARIABLE_MANAGEMENT_PLAN.md)**
- **摘要**: 替換硬編碼密碼和配置的環境變數管理計畫
- **實施狀態**: 已完成（任務T1.3.2）
- **現代化優先級**: **關鍵** - 解決site.properties中的安全漏洞

#### 9. **[多環境配置設計](./MULTI_ENVIRONMENT_CONFIGURATION_DESIGN.md)**
- **摘要**: 多環境配置管理設計文件（開發/測試/生產）
- **實施狀態**: 已完成（任務T1.3.3）
- **現代化優先級**: **高** - 環境隔離的必要條件

#### 10. **[Git版本控制設置指南](./GIT_VERSION_CONTROL_SETUP_GUIDE.md)**
- **摘要**: 針對遺留系統的Git設置指南，包含詳細的.gitignore配置
- **實施狀態**: 已完成（任務T1.3.1）
- **現代化優先級**: **高** - 程式碼管理和協作的基礎

#### 11. **[文件索引](./DOCUMENTATION_INDEX.md)**
- **摘要**: 所有系統文件的主索引，按類別組織
- **實施狀態**: 活躍維護文件
- **現代化優先級**: **低** - 組織性文件

## 🏛️ FOSSIL系統分析

[FOSSIL_SYSTEM_ANALYSIS](./FOSSIL_SYSTEM_ANALYSIS/)目錄包含對這個「化石系統」的全面分析框架。系統建立在已停產的CodeCharge Studio平台上，透過「數位考古學」方法進行分析和維護。

### 關鍵發現
1. **雙表架構**: 系統使用IBMCASE進行案件管理，IBMFYM進行詳細處理記錄
2. **完整業務流程**: IBMFYM表中存在所有處理階段（05-11），系統有效處理完整工作流程
3. **複製式維護**: 四種不同的複製模式使功能擴展無需修改核心系統
4. **前端補償**: 大量使用JavaScript/jQuery補償後端限制
5. **30年穩定性**: 系統展現出卓越的韌性和持續演進能力

### 主要文件
- [README.md](./FOSSIL_SYSTEM_ANALYSIS/README.md) - 框架概述
- [深度思考分析](./FOSSIL_SYSTEM_ANALYSIS/00_INITIAL_STRATEGY/deep_thinking_analysis.md) - 五維度分析方法
- [分析方法論](./FOSSIL_SYSTEM_ANALYSIS/01_METHODOLOGY/analysis_methodology.md) - 數位考古學原則
- [複製模式分析](./FOSSIL_SYSTEM_ANALYSIS/02_COPY_PATTERNS/copy_pattern_analysis.md) - 系統維護策略
- [風險評估](./FOSSIL_SYSTEM_ANALYSIS/05_RISK_ASSESSMENT/risk_assessment.md) - 安全和技術債務分析

## 📊 狀態碼系統

[STATUS_CODE_SYSTEM](./STATUS_CODE_SYSTEM/)目錄包含違章建築管理系統狀態管理的核心文件。

### 狀態碼結構（ABC格式）
- **第一位(A)**: 業務階段 - 2=認定、3=排拆、4=結案、9=特殊控制
- **第二位(B)**: 部門分工 - 2/6=一般違建、4=廣告違建、5=下水道違建
- **第三位(C)**: 作業狀態 - 1=辦理中、2=陳核中、4=送協同、9=已簽准

### 主要文件
- [編碼規則](./STATUS_CODE_SYSTEM/RLT_STATUS_CODE_ENCODING_RULES.md) - 76個狀態碼的權威參考
- [狀態碼百科](./STATUS_CODE_SYSTEM/STATUS_CODE_ENCYCLOPEDIA.md) - 基於1,025,000+筆記錄的統計分析
- [狀態機圖](./STATUS_CODE_SYSTEM/STATUS_CODE_STATE_MACHINE_CORRECTED.md) - 視覺化狀態轉換
- [轉換矩陣](./STATUS_CODE_SYSTEM/STATUS_CODE_TRANSITION_MATRIX.md) - 所有有效狀態轉換的詳細矩陣

## 🔄 業務流程文件

[flow](./flow/)目錄包含從案件登記到結案的完整業務流程文件。

### 流程階段
1. **[報案登記](./flow/01_report/)** - 案件建立、資料驗證、人員管理
2. **[現場勘查](./flow/02_inspection/)** - 現場檢查、照片管理、GPS驗證
3. **[認定審核](./flow/03_determination/)** - 法律認定、跨部門協同、多層級審批
4. **[認定通知](./flow/04_notification/)** - 通知生成、編號登記、送達追蹤
5. **[拆除通知](./flow/05_demolition_notice/)** - 拆除通知工作流程、案件分派
6. **[拆除執行](./flow/06_demolition/)** - 實體拆除、現場監督、證據收集
7. **[結案處理](./flow/11_closing/)** - 結案條件、兩階段審批、資料歸檔

### 關鍵文件
- [完整業務流程](./flow/COMPLETE_BUSINESS_FLOW.md) - 整合所有61個狀態碼的綜合流程圖
- [狀態轉換圖](./flow/STATE_TRANSITION_DIAGRAMS.md) - 使用Mermaid圖表的視覺化狀態轉換
- [異常處理流程](./flow/EXCEPTION_HANDLING_FLOWS.md) - 錯誤處理模式和最佳實踐
- [自動結案機制分析](./flow/11_closing/AUTO_CLOSING_MECHANISM_ANALYSIS.md) - 揭示所有結案需要人工處理


## 📝 維護注意事項

1. **禁止推測**: 必須實際查詢資料庫或程式碼，不可推測
2. **觸發器防護網**: 91個資料庫觸發器確保資料完整性
3. **複製模式開發**: 使用記錄的四種複製模式安全地添加功能
4. **狀態碼規則**: 嚴格遵循狀態轉換矩陣
5. **品質控制**: 使用92c機制進行資料校正

## 🔗 快速連結

- [CLAUDE.md開發指南](/CLAUDE.md)

---

*最後更新: 2025-07-09*
*文件版本: 1.0*
