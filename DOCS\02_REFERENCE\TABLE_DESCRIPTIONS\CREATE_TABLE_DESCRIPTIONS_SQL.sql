-- =====================================================
-- 新北市違章建築管理系統 - 資料表描述說明表建立腳本
-- =====================================================
-- 建立日期: 2025-01-09
-- 用途: 建立系統資料表描述說明表，供開發維護參考
-- 執行環境: PostgreSQL 15+
-- 執行方式: psql -h localhost -p 5432 -U postgres -d bms -f CREATE_TABLE_DESCRIPTIONS_SQL.sql
-- =====================================================

-- 建立資料表描述說明主表
CREATE TABLE IF NOT EXISTS table_descriptions (
    table_name VARCHAR(50) NOT NULL,           -- 資料表名稱
    table_description TEXT,                    -- 資料表用途說明
    table_type VARCHAR(20) DEFAULT 'business', -- 資料表類型（business/system/support）
    record_count BIGINT DEFAULT 0,             -- 記錄筆數
    main_pages TEXT,                          -- 主要對應頁面
    business_priority INTEGER DEFAULT 3,      -- 業務重要性（1-5）
    growth_rate VARCHAR(20) DEFAULT 'medium', -- 資料成長速度
    maintenance_notes TEXT,                   -- 維護注意事項
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_table_descriptions PRIMARY KEY (table_name)
);

-- 建立欄位描述說明表
CREATE TABLE IF NOT EXISTS field_descriptions (
    table_name VARCHAR(50) NOT NULL,          -- 資料表名稱
    field_name VARCHAR(50) NOT NULL,          -- 欄位名稱
    data_type VARCHAR(50),                    -- 資料型別
    page_description VARCHAR(200),            -- 頁面顯示描述
    document_description VARCHAR(200),        -- 文件記錄描述
    business_importance INTEGER DEFAULT 3,    -- 業務重要性（1-5星）
    field_notes TEXT,                        -- 欄位備註
    is_primary_key BOOLEAN DEFAULT FALSE,    -- 是否為主鍵
    is_foreign_key BOOLEAN DEFAULT FALSE,    -- 是否為外鍵
    foreign_reference VARCHAR(100),          -- 外鍵參照（table.field）
    code_reference VARCHAR(50),              -- 代碼參照（code_type）
    field_length INTEGER,                    -- 欄位長度
    is_nullable BOOLEAN DEFAULT TRUE,        -- 是否可為空
    default_value VARCHAR(100),              -- 預設值
    validation_rules TEXT,                   -- 驗證規則
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_field_descriptions PRIMARY KEY (table_name, field_name),
    CONSTRAINT fk_field_table FOREIGN KEY (table_name) 
        REFERENCES table_descriptions(table_name) ON DELETE CASCADE
);

-- 建立狀態碼描述表
CREATE TABLE IF NOT EXISTS status_descriptions (
    code_type VARCHAR(25) NOT NULL,          -- 代碼類型
    code_value VARCHAR(10) NOT NULL,         -- 代碼值
    code_description VARCHAR(200),           -- 代碼描述
    business_scope VARCHAR(50),              -- 適用範圍
    stage_order INTEGER,                     -- 階段順序
    is_active BOOLEAN DEFAULT TRUE,          -- 是否啟用
    moi_mapping VARCHAR(10),                 -- MOI對應碼
    usage_pages TEXT,                        -- 使用頁面
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_status_descriptions PRIMARY KEY (code_type, code_value)
);

-- 建立頁面對應關係表
CREATE TABLE IF NOT EXISTS page_table_mapping (
    page_name VARCHAR(100) NOT NULL,         -- 頁面名稱
    table_name VARCHAR(50) NOT NULL,         -- 資料表名稱
    operation_type VARCHAR(20),              -- 操作類型（CRUD）
    usage_frequency VARCHAR(20),             -- 使用頻率（高/中/低）
    is_main_table BOOLEAN DEFAULT FALSE,     -- 是否為主要資料表
    page_type VARCHAR(20),                   -- 頁面類型（man/lis/que/prt）
    business_function VARCHAR(100),          -- 業務功能
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_page_table_mapping PRIMARY KEY (page_name, table_name),
    CONSTRAINT fk_mapping_table FOREIGN KEY (table_name) 
        REFERENCES table_descriptions(table_name) ON DELETE CASCADE
);

-- 建立觸發器：自動更新 updated_date
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 為各表建立更新觸發器
DROP TRIGGER IF EXISTS update_table_descriptions_modtime ON table_descriptions;
CREATE TRIGGER update_table_descriptions_modtime
    BEFORE UPDATE ON table_descriptions
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

DROP TRIGGER IF EXISTS update_field_descriptions_modtime ON field_descriptions;
CREATE TRIGGER update_field_descriptions_modtime
    BEFORE UPDATE ON field_descriptions
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

DROP TRIGGER IF EXISTS update_status_descriptions_modtime ON status_descriptions;
CREATE TRIGGER update_status_descriptions_modtime
    BEFORE UPDATE ON status_descriptions
    FOR EACH ROW EXECUTE FUNCTION update_modified_column();

-- 建立索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_field_descriptions_table ON field_descriptions(table_name);
CREATE INDEX IF NOT EXISTS idx_field_descriptions_importance ON field_descriptions(business_importance);
CREATE INDEX IF NOT EXISTS idx_status_descriptions_type ON status_descriptions(code_type);
CREATE INDEX IF NOT EXISTS idx_page_mapping_table ON page_table_mapping(table_name);
CREATE INDEX IF NOT EXISTS idx_page_mapping_type ON page_table_mapping(page_type);

-- =====================================================
-- 插入核心業務表描述資料
-- =====================================================

-- 插入資料表描述
INSERT INTO table_descriptions (table_name, table_description, table_type, record_count, main_pages, business_priority, growth_rate, maintenance_notes) VALUES
('ibmcase', '違章建築案件主檔，系統核心資料表', 'business', 419817, 'im10101_man.jsp,im20201_man.jsp', 5, 'medium', '30年歷史資料，需要定期歸檔'),
('tbflow', '記錄案件處理流程的完整歷程', 'business', 352514, '所有流程相關頁面', 5, 'fast', '每次狀態變更都會新增記錄'),
('ibmcode', '系統參數代碼管理，所有下拉選單的資料來源', 'system', 1751, '所有頁面的下拉選單', 5, 'slow', '78種代碼類型，新舊版本需要整併'),
('ibmuser', '系統使用者帳號管理', 'system', 200, 'im70301_man.jsp,登入頁面', 4, 'slow', '密碼政策需要加強'),
('collaboration_log', '記錄協同作業流程，支援協同退回功能', 'business', 50000, '協同作業相關頁面', 4, 'medium', '235/245/255協同退回為新增功能'),
('ibmlist', '管理案件相關的附件檔案和圖片', 'support', 1000000, '檔案上傳和管理頁面', 3, 'fast', '檔案數量龐大，需要檔案管理策略'),
('ibmdisnm', '記錄違建案件相關人員資訊', 'business', 600000, 'im10101_man.jsp,人員維護頁面', 3, 'medium', '個人資料需要存取控制'),
('ibmfym', '記錄案件各階段的詳細處理資訊', 'business', 400000, 'im50101_man.jsp,im60301_man.jsp', 4, 'medium', '與tbflow功能重疊，建議整併'),
('ibmsts', '記錄案件重要時間節點的狀態', 'business', 400000, '狀態查詢相關頁面', 4, 'medium', '狀態時間戳記記錄'),
('ibmrole', '系統角色權限管理', 'system', 50, '角色管理頁面', 4, 'slow', '權限控制核心表'),
('ibmcslan', '記錄違建案件相關的土地地號資訊', 'business', 300000, '地號維護相關頁面', 3, 'medium', '地政資料介接'),
('ibmcsprj', '記錄案件所屬的專案或計畫', 'business', 100000, '專案管理相關頁面', 3, 'medium', '專案計畫管理'),
('ibmrpli', '既存違建修繕報備管理', 'business', 10000, 'im90201_man.jsp', 3, 'medium', '報備制度相關'),
('status_code_mapping', '內部狀態碼與MOI國土署系統的對應關係', 'system', 100, 'MOI系統相關頁面', 4, 'slow', 'MOI整合關鍵表'),
('ntpc_export_log', '記錄向MOI國土署系統匯出資料的記錄', 'system', 500000, 'MOI匯出管理頁面', 4, 'fast', 'MOI系統匯出記錄');

-- 插入核心欄位描述（ibmcase主要欄位）
INSERT INTO field_descriptions (table_name, field_name, data_type, page_description, document_description, business_importance, field_notes, is_primary_key, is_foreign_key, foreign_reference, code_reference, field_length, is_nullable, validation_rules) VALUES
('ibmcase', 'case_no', 'VARCHAR(10)', '案件編號', '案件編號/勘查紀錄號碼', 5, '主鍵', TRUE, FALSE, NULL, NULL, 10, FALSE, '唯一值'),
('ibmcase', 'caseopened', 'VARCHAR(3)', '當前狀態碼', '處理狀態/流程狀態', 5, '對應RLT代碼', FALSE, FALSE, NULL, 'RLT', 3, FALSE, '必須存在於ibmcode'),
('ibmcase', 's_empno', 'VARCHAR(10)', '承辦人員編號', '承辦人/認定承辦', 4, '對應ibmuser.empno', FALSE, TRUE, 'ibmuser.empno', NULL, 10, TRUE, '必須為有效員工'),
('ibmcase', 'case_con_user', 'VARCHAR(10)', '協同承辦人', '協同承辦人員', 4, '協同作業功能', FALSE, TRUE, 'ibmuser.empno', NULL, 10, TRUE, '協同功能使用'),
('ibmcase', 'reg_yy', 'VARCHAR(3)', '登記年度', '民國年度', 3, '民國年', FALSE, FALSE, NULL, NULL, 3, TRUE, '民國年格式'),
('ibmcase', 'reg_no', 'VARCHAR(6)', '登記號碼', '案件流水號', 3, '年度內編號', FALSE, FALSE, NULL, NULL, 6, TRUE, '數字格式'),
('ibmcase', 'ib_prcs', 'VARCHAR(1)', '違建流程', '案件類型', 4, '1=一般,2=廣告,3=下水道', FALSE, FALSE, NULL, NULL, 1, TRUE, '1-3範圍'),
('ibmcase', 'dis_b_addzon', 'VARCHAR(3)', '違建地點行政區', '郵遞區號', 4, '對應ZON代碼', FALSE, FALSE, NULL, 'ZON', 3, TRUE, '必須為有效區碼'),
('ibmcase', 'building_area', 'NUMERIC', '建物面積', '違章面積', 4, '平方公尺', FALSE, FALSE, NULL, NULL, NULL, TRUE, '正數'),
('ibmcase', 'building_height', 'NUMERIC', '建物高度', '違章高度', 3, '公尺', FALSE, FALSE, NULL, NULL, NULL, TRUE, '正數'),
('ibmcase', 'x_coordinate', 'NUMERIC', '經度座標', 'X座標', 3, 'GIS定位', FALSE, FALSE, NULL, NULL, NULL, TRUE, '座標格式'),
('ibmcase', 'y_coordinate', 'NUMERIC', '緯度座標', 'Y座標', 3, 'GIS定位', FALSE, FALSE, NULL, NULL, NULL, TRUE, '座標格式'),
('ibmcase', 'reg_date', 'NUMERIC', '認定發文日期', '認定日期', 4, 'YYYYMMDD-19110000', FALSE, FALSE, NULL, NULL, NULL, TRUE, '民國年日期格式'),
('ibmcase', 'end_date', 'NUMERIC', '結案日期', '結案發文日期', 4, 'YYYYMMDD-19110000', FALSE, FALSE, NULL, NULL, NULL, TRUE, '民國年日期格式');

-- 插入tbflow欄位描述
INSERT INTO field_descriptions (table_name, field_name, data_type, page_description, document_description, business_importance, field_notes, is_primary_key, is_foreign_key, foreign_reference, code_reference, field_length, is_nullable) VALUES
('tbflow', 'flow_seq', 'BIGINT', '流程序號', '流程記錄主鍵', 5, '自動產生主鍵', TRUE, FALSE, NULL, NULL, NULL, FALSE),
('tbflow', 'case_no', 'VARCHAR(10)', '案件編號', '對應案件編號', 5, '外鍵至ibmcase', FALSE, TRUE, 'ibmcase.case_no', NULL, 10, FALSE),
('tbflow', 'flow_status', 'VARCHAR(3)', '流程狀態', '狀態碼', 5, '對應RLT代碼', FALSE, FALSE, NULL, 'RLT', 3, FALSE),
('tbflow', 'flow_sdate', 'TIMESTAMP', '流程開始時間', '狀態開始時間', 4, '完整時間戳記', FALSE, FALSE, NULL, NULL, NULL, TRUE),
('tbflow', 'flow_edate', 'TIMESTAMP', '流程結束時間', '狀態結束時間', 4, '完整時間戳記', FALSE, FALSE, NULL, NULL, NULL, TRUE),
('tbflow', 'flow_user', 'VARCHAR(10)', '流程處理人', '處理人員', 4, '對應ibmuser.empno', FALSE, TRUE, 'ibmuser.empno', NULL, 10, TRUE),
('tbflow', 'flow_memo', 'TEXT', '流程備註', '處理說明', 3, '處理過程說明', FALSE, FALSE, NULL, NULL, NULL, TRUE);

-- 插入ibmcode欄位描述
INSERT INTO field_descriptions (table_name, field_name, data_type, page_description, document_description, business_importance, field_notes, is_primary_key, is_foreign_key, field_length, is_nullable) VALUES
('ibmcode', 'code_type', 'VARCHAR(25)', '代碼類型', '代碼分類', 5, '主鍵之一', TRUE, FALSE, 25, FALSE),
('ibmcode', 'code_seq', 'VARCHAR(10)', '代碼序號', '代碼值', 5, '主鍵之一', TRUE, FALSE, 10, FALSE),
('ibmcode', 'code_desc', 'VARCHAR(200)', '代碼描述', '代碼說明', 5, '中文描述', FALSE, FALSE, 200, FALSE),
('ibmcode', 'is_del', 'VARCHAR(1)', '是否刪除', '刪除標記', 3, 'Y=刪除,N=正常', FALSE, FALSE, 1, TRUE),
('ibmcode', 'is_open', 'VARCHAR(1)', '是否開放', '啟用標記', 3, 'Y=啟用,N=停用', FALSE, FALSE, 1, TRUE);

-- 插入重要狀態碼描述
INSERT INTO status_descriptions (code_type, code_value, code_description, business_scope, stage_order, is_active, moi_mapping, usage_pages) VALUES
('RLT', '231', '[一般]認定辦理中', '一般違建', 1, TRUE, '02', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '232', '[一般]認定陳核中', '一般違建', 2, TRUE, '02', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '234', '[一般]認定送協同作業', '一般違建', 3, TRUE, '02', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '235', '[一般]認定協同退回', '一般違建', 4, TRUE, '02', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '239', '[一般]認定已簽准', '一般違建', 5, TRUE, '03', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '23b', '[一般]認定協同作業完成', '一般違建', 6, TRUE, '03', 'im10101_man.jsp,im20201_man.jsp'),
('RLT', '241', '[廣告物]認定辦理中', '廣告違建', 1, TRUE, '02', 'im60301_man.jsp,im60502_man.jsp'),
('RLT', '244', '[廣告物]認定送協同作業', '廣告違建', 3, TRUE, '02', 'im60301_man.jsp,im60502_man.jsp'),
('RLT', '245', '[廣告物]認定協同退回', '廣告違建', 4, TRUE, '02', 'im60301_man.jsp,im60502_man.jsp'),
('RLT', '24b', '[廣告物]認定協同作業完成', '廣告違建', 6, TRUE, '03', 'im60301_man.jsp,im60502_man.jsp'),
('RLT', '251', '[下水道]認定辦理中', '下水道違建', 1, TRUE, '02', 'im50101_man.jsp,im50102_man.jsp'),
('RLT', '254', '[下水道]認定送協同作業', '下水道違建', 3, TRUE, '02', 'im50101_man.jsp,im50102_man.jsp'),
('RLT', '255', '[下水道]認定協同退回', '下水道違建', 4, TRUE, '02', 'im50101_man.jsp,im50102_man.jsp'),
('RLT', '25b', '[下水道]認定協同作業完成', '下水道違建', 6, TRUE, '03', 'im50101_man.jsp,im50102_man.jsp'),
('RLT', '460', '[一般]結案', '一般違建', 10, TRUE, '11', 'im60301_man.jsp'),
('RLT', '440', '[廣告物]結案', '廣告違建', 10, TRUE, '11', 'im60301_man.jsp'),
('RLT', '450', '[下水道]結案', '下水道違建', 10, TRUE, '11', 'im60301_man.jsp'),
('RLT', '92c', '案件資料繕校', '所有類型', 99, TRUE, NULL, 'im10101_man.jsp,品質控制');

-- 插入頁面對應關係
INSERT INTO page_table_mapping (page_name, table_name, operation_type, usage_frequency, is_main_table, page_type, business_function) VALUES
('im10101_man.jsp', 'ibmcase', 'CRUD', '高', TRUE, 'man', '勘查記錄表與認定作業'),
('im10101_man.jsp', 'ibmdisnm', 'CRUD', '中', FALSE, 'man', '違建人員資料維護'),
('im10101_man.jsp', 'ibmcslan', 'CRUD', '中', FALSE, 'man', '土地地號資料維護'),
('im20201_man.jsp', 'ibmcase', 'R', '高', TRUE, 'man', '違章案件查詢'),
('im20201_man.jsp', 'ibmcode', 'R', '高', FALSE, 'man', '代碼下拉選單'),
('im50101_man.jsp', 'ibmfym', 'CRUD', '高', TRUE, 'man', '陳核作業'),
('im50101_man.jsp', 'tbflow', 'CRU', '高', FALSE, 'man', '流程記錄'),
('im60301_man.jsp', 'ibmcase', 'U', '高', TRUE, 'man', '排定拆除作業'),
('im70301_man.jsp', 'ibmuser', 'CRUD', '中', TRUE, 'man', '系統使用者資料維護'),
('im90201_man.jsp', 'ibmrpli', 'CRUD', '中', TRUE, 'man', '修繕報備管理');

-- =====================================================
-- 建立查詢視圖
-- =====================================================

-- 完整欄位描述視圖
CREATE OR REPLACE VIEW v_complete_field_descriptions AS
SELECT 
    td.table_name,
    td.table_description,
    td.table_type,
    td.record_count,
    fd.field_name,
    fd.data_type,
    fd.page_description,
    fd.document_description,
    fd.business_importance,
    fd.field_notes,
    fd.is_primary_key,
    fd.is_foreign_key,
    fd.foreign_reference,
    fd.code_reference,
    CASE 
        WHEN fd.business_importance = 5 THEN '⭐⭐⭐⭐⭐'
        WHEN fd.business_importance = 4 THEN '⭐⭐⭐⭐'
        WHEN fd.business_importance = 3 THEN '⭐⭐⭐'
        WHEN fd.business_importance = 2 THEN '⭐⭐'
        ELSE '⭐'
    END AS importance_stars
FROM table_descriptions td
LEFT JOIN field_descriptions fd ON td.table_name = fd.table_name
ORDER BY td.business_priority DESC, td.table_name, fd.business_importance DESC;

-- 狀態碼完整視圖
CREATE OR REPLACE VIEW v_complete_status_codes AS
SELECT 
    sd.code_type,
    sd.code_value,
    sd.code_description,
    sd.business_scope,
    sd.stage_order,
    sd.moi_mapping,
    sd.usage_pages,
    sd.is_active
FROM status_descriptions sd
ORDER BY sd.code_type, sd.stage_order, sd.code_value;

-- 頁面表格對應統計視圖
CREATE OR REPLACE VIEW v_page_table_statistics AS
SELECT 
    ptm.table_name,
    td.table_description,
    COUNT(ptm.page_name) as page_count,
    STRING_AGG(ptm.page_name, ', ' ORDER BY ptm.page_name) as related_pages,
    STRING_AGG(DISTINCT ptm.operation_type, ', ') as operations,
    MAX(CASE WHEN ptm.usage_frequency = '高' THEN 3 
             WHEN ptm.usage_frequency = '中' THEN 2 
             ELSE 1 END) as max_frequency_level
FROM page_table_mapping ptm
JOIN table_descriptions td ON ptm.table_name = td.table_name
GROUP BY ptm.table_name, td.table_description
ORDER BY max_frequency_level DESC, page_count DESC;

-- =====================================================
-- 建立管理功能
-- =====================================================

-- 新增欄位描述的便利函數
CREATE OR REPLACE FUNCTION add_field_description(
    p_table_name VARCHAR(50),
    p_field_name VARCHAR(50),
    p_data_type VARCHAR(50),
    p_page_description VARCHAR(200),
    p_document_description VARCHAR(200),
    p_business_importance INTEGER DEFAULT 3,
    p_field_notes TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
    INSERT INTO field_descriptions (
        table_name, field_name, data_type, page_description, 
        document_description, business_importance, field_notes
    ) VALUES (
        p_table_name, p_field_name, p_data_type, p_page_description,
        p_document_description, p_business_importance, p_field_notes
    )
    ON CONFLICT (table_name, field_name) 
    DO UPDATE SET 
        data_type = EXCLUDED.data_type,
        page_description = EXCLUDED.page_description,
        document_description = EXCLUDED.document_description,
        business_importance = EXCLUDED.business_importance,
        field_notes = EXCLUDED.field_notes,
        updated_date = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 批次更新重要性的函數
CREATE OR REPLACE FUNCTION update_importance_by_pattern(
    p_table_pattern VARCHAR(50),
    p_field_pattern VARCHAR(50),
    p_new_importance INTEGER
) RETURNS INTEGER AS $$
DECLARE
    update_count INTEGER;
BEGIN
    UPDATE field_descriptions 
    SET business_importance = p_new_importance,
        updated_date = CURRENT_TIMESTAMP
    WHERE table_name LIKE p_table_pattern 
      AND field_name LIKE p_field_pattern;
    
    GET DIAGNOSTICS update_count = ROW_COUNT;
    RETURN update_count;
END;
$$ LANGUAGE plpgsql;

-- 產生文件報告的函數
CREATE OR REPLACE FUNCTION generate_table_report(p_table_name VARCHAR(50))
RETURNS TABLE(
    field_name VARCHAR(50),
    data_type VARCHAR(50), 
    page_description VARCHAR(200),
    importance_level VARCHAR(10),
    notes TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fd.field_name,
        fd.data_type,
        fd.page_description,
        CASE 
            WHEN fd.business_importance = 5 THEN '⭐⭐⭐⭐⭐'
            WHEN fd.business_importance = 4 THEN '⭐⭐⭐⭐'
            WHEN fd.business_importance = 3 THEN '⭐⭐⭐'
            WHEN fd.business_importance = 2 THEN '⭐⭐'
            ELSE '⭐'
        END,
        fd.field_notes
    FROM field_descriptions fd
    WHERE fd.table_name = p_table_name
    ORDER BY fd.business_importance DESC, fd.field_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 授權與註解
-- =====================================================

-- 建立註解
COMMENT ON TABLE table_descriptions IS '資料表描述說明主表';
COMMENT ON TABLE field_descriptions IS '欄位描述說明表'; 
COMMENT ON TABLE status_descriptions IS '狀態碼描述表';
COMMENT ON TABLE page_table_mapping IS '頁面對應關係表';

COMMENT ON COLUMN table_descriptions.table_name IS '資料表名稱';
COMMENT ON COLUMN table_descriptions.table_description IS '資料表用途說明';
COMMENT ON COLUMN table_descriptions.business_priority IS '業務重要性（1-5）';

COMMENT ON COLUMN field_descriptions.business_importance IS '業務重要性（1-5星）';
COMMENT ON COLUMN field_descriptions.page_description IS '頁面顯示描述';
COMMENT ON COLUMN field_descriptions.document_description IS '文件記錄描述';

-- 授權給相關角色（根據實際情況調整）
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO developer_role;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO maintenance_role;

-- =====================================================
-- 執行完成訊息
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '===============================================';
    RAISE NOTICE '資料表描述說明表建立完成！';
    RAISE NOTICE '===============================================';
    RAISE NOTICE '已建立以下表格：';
    RAISE NOTICE '- table_descriptions: % 筆資料表描述', (SELECT COUNT(*) FROM table_descriptions);
    RAISE NOTICE '- field_descriptions: % 筆欄位描述', (SELECT COUNT(*) FROM field_descriptions);
    RAISE NOTICE '- status_descriptions: % 筆狀態碼描述', (SELECT COUNT(*) FROM status_descriptions);
    RAISE NOTICE '- page_table_mapping: % 筆頁面對應', (SELECT COUNT(*) FROM page_table_mapping);
    RAISE NOTICE '===============================================';
    RAISE NOTICE '可用的查詢視圖：';
    RAISE NOTICE '- v_complete_field_descriptions';
    RAISE NOTICE '- v_complete_status_codes';
    RAISE NOTICE '- v_page_table_statistics';
    RAISE NOTICE '===============================================';
    RAISE NOTICE '可用的管理函數：';
    RAISE NOTICE '- add_field_description()';
    RAISE NOTICE '- update_importance_by_pattern()';
    RAISE NOTICE '- generate_table_report()';
    RAISE NOTICE '===============================================';
END $$;

-- 查詢範例
-- SELECT * FROM v_complete_field_descriptions WHERE table_name = 'ibmcase';
-- SELECT * FROM v_complete_status_codes WHERE code_type = 'RLT';
-- SELECT * FROM v_page_table_statistics;
-- SELECT * FROM generate_table_report('ibmcase');