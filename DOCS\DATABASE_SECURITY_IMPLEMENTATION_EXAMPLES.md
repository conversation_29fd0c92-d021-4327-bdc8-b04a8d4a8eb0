# 資料庫安全修復系統 - 實施範例和程式碼

## 🚀 實際實施範例

### 1. 立即可用的安全配置

#### 環境變量配置範例
```bash
# .env 檔案
# PostgreSQL 主資料庫
DB_PRIMARY_URL=************************************
DB_PRIMARY_USER=postgres
DB_PRIMARY_PASSWORD=S!@h@202203
DB_PRIMARY_MAX_CONN=80

# SQL Server GIS 資料庫
DB_SECONDARY_URL=*************************************************
DB_SECONDARY_USER=user_rams2
DB_SECONDARY_PASSWORD=$ystemOnlin168
DB_SECONDARY_MAX_CONN=100

# 安全設定
SECURITY_ENCRYPTION_KEY=your-256-bit-encryption-key
SECURITY_AUDIT_ENABLED=true
SECURITY_RECOVERY_AUTO_ENABLED=true
```

#### 安全的資料庫連線管理
```java
/**
 * 安全的資料庫連線管理器
 */
@Component
public class SecureDatabaseConnectionManager {
    
    private final Environment environment;
    private final AuditLogger auditLogger;
    private final Map<String, ComboPooledDataSource> dataSources = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initializeDataSources() {
        try {
            // 主資料庫連線池
            ComboPooledDataSource primaryDS = createSecureDataSource(
                environment.getProperty("DB_PRIMARY_URL"),
                environment.getProperty("DB_PRIMARY_USER"),
                environment.getProperty("DB_PRIMARY_PASSWORD"),
                Integer.parseInt(environment.getProperty("DB_PRIMARY_MAX_CONN", "80"))
            );
            dataSources.put("primary", primaryDS);
            
            // 次要資料庫連線池
            ComboPooledDataSource secondaryDS = createSecureDataSource(
                environment.getProperty("DB_SECONDARY_URL"),
                environment.getProperty("DB_SECONDARY_USER"),
                environment.getProperty("DB_SECONDARY_PASSWORD"),
                Integer.parseInt(environment.getProperty("DB_SECONDARY_MAX_CONN", "100"))
            );
            dataSources.put("secondary", secondaryDS);
            
            auditLogger.logSystemEvent("資料庫連線池初始化完成");
            
        } catch (Exception e) {
            auditLogger.logSystemError("資料庫連線池初始化失敗", e);
            throw new DatabaseInitializationException("無法初始化資料庫連線", e);
        }
    }
    
    private ComboPooledDataSource createSecureDataSource(
            String url, String username, String password, int maxPoolSize) throws PropertyVetoException {
        
        ComboPooledDataSource dataSource = new ComboPooledDataSource();
        
        // 基本配置
        dataSource.setJdbcUrl(url);
        dataSource.setUser(username);
        dataSource.setPassword(password);
        
        // 連線池配置
        dataSource.setInitialPoolSize(5);
        dataSource.setMinPoolSize(5);
        dataSource.setMaxPoolSize(maxPoolSize);
        dataSource.setAcquireIncrement(5);
        
        // 安全配置
        dataSource.setCheckoutTimeout(30000); // 30秒超時
        dataSource.setIdleConnectionTestPeriod(300); // 5分鐘測試閒置連線
        dataSource.setMaxIdleTime(600); // 10分鐘最大閒置時間
        dataSource.setTestConnectionOnCheckout(true);
        dataSource.setTestConnectionOnCheckin(true);
        
        // PostgreSQL 特定配置
        if (url.contains("postgresql")) {
            dataSource.setDriverClass("org.postgresql.Driver");
            // 啟用 SSL 連線
            if (!url.contains("sslmode")) {
                dataSource.setJdbcUrl(url + "?sslmode=require&stringtype=unspecified");
            }
        }
        
        return dataSource;
    }
    
    public Connection getSecureConnection(String poolName) throws SQLException {
        ComboPooledDataSource dataSource = dataSources.get(poolName);
        if (dataSource == null) {
            throw new IllegalArgumentException("未知的連線池名稱: " + poolName);
        }
        
        Connection connection = dataSource.getConnection();
        
        // 設定連線安全屬性
        connection.setAutoCommit(false);
        connection.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
        
        // 記錄連線取得
        auditLogger.logDatabaseAccess(poolName, getConnectionInfo(connection));
        
        return connection;
    }
}
```

### 2. 自動回復點管理系統

#### PostgreSQL 回復點實施
```sql
-- 建立回復點管理表
CREATE TABLE IF NOT EXISTS recovery_points (
    id SERIAL PRIMARY KEY,
    recovery_point_id UUID NOT NULL UNIQUE DEFAULT gen_random_uuid(),
    database_name VARCHAR(255) NOT NULL,
    affected_tables TEXT[] NOT NULL,
    snapshot_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    backup_metadata JSONB NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    operation_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    expiry_date TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL '7 days'),
    recovery_instructions TEXT,
    CONSTRAINT valid_status CHECK (status IN ('ACTIVE', 'USED', 'EXPIRED', 'FAILED'))
);

-- 建立索引
CREATE INDEX idx_recovery_points_operation_id ON recovery_points(operation_id);
CREATE INDEX idx_recovery_points_status ON recovery_points(status);
CREATE INDEX idx_recovery_points_expiry ON recovery_points(expiry_date);

-- 建立自動清理過期回復點的函數
CREATE OR REPLACE FUNCTION cleanup_expired_recovery_points()
RETURNS void AS $$
BEGIN
    -- 標記過期的回復點
    UPDATE recovery_points 
    SET status = 'EXPIRED'
    WHERE expiry_date < CURRENT_TIMESTAMP 
    AND status = 'ACTIVE';
    
    -- 刪除超過30天的過期記錄
    DELETE FROM recovery_points 
    WHERE status = 'EXPIRED' 
    AND expiry_date < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    INSERT INTO system_log(log_time, log_level, message)
    VALUES (CURRENT_TIMESTAMP, 'INFO', 'Recovery points cleanup completed');
END;
$$ LANGUAGE plpgsql;

-- 每日執行清理
SELECT cron.schedule('cleanup-recovery-points', '0 2 * * *', 
    'SELECT cleanup_expired_recovery_points();');
```

#### Java 回復點管理實施
```java
/**
 * PostgreSQL 自動回復點管理器
 */
@Service
public class PostgreSQLRecoveryPointManager implements RecoveryPointManager {
    
    private final JdbcTemplate jdbcTemplate;
    private final AuditLogger auditLogger;
    private final BackupService backupService;
    
    @Override
    @Transactional
    public RecoveryPoint createRecoveryPoint(String operationId, List<String> affectedTables) {
        String recoveryPointId = UUID.randomUUID().toString();
        
        try {
            // 1. 建立資料快照
            Map<String, TableSnapshot> tableSnapshots = createTableSnapshots(affectedTables);
            
            // 2. 建立邏輯備份
            String backupPath = backupService.createLogicalBackup(affectedTables);
            
            // 3. 收集系統狀態
            SystemState systemState = captureSystemState();
            
            // 4. 建立回復點記錄
            RecoveryPointMetadata metadata = RecoveryPointMetadata.builder()
                .tableSnapshots(tableSnapshots)
                .systemState(systemState)
                .backupPath(backupPath)
                .createdAt(Instant.now())
                .build();
            
            String sql = """
                INSERT INTO recovery_points (
                    recovery_point_id, database_name, affected_tables, 
                    backup_metadata, created_by, operation_id, recovery_instructions
                ) VALUES (?, ?, ?, ?::jsonb, ?, ?, ?)
                """;
            
            jdbcTemplate.update(sql,
                recoveryPointId,
                "bms",
                affectedTables.toArray(new String[0]),
                objectMapper.writeValueAsString(metadata),
                getCurrentUser(),
                operationId,
                generateRecoveryInstructions(affectedTables)
            );
            
            RecoveryPoint recoveryPoint = new RecoveryPoint(
                recoveryPointId, operationId, affectedTables, metadata);
            
            auditLogger.logRecoveryPointCreated(recoveryPoint);
            return recoveryPoint;
            
        } catch (Exception e) {
            auditLogger.logRecoveryPointCreationFailed(operationId, affectedTables, e);
            throw new RecoveryPointException("建立回復點失敗", e);
        }
    }
    
    @Override
    @Transactional
    public void restoreFromRecoveryPoint(String recoveryPointId) {
        try {
            // 1. 查詢回復點資訊
            RecoveryPointInfo info = getRecoveryPointInfo(recoveryPointId);
            if (info == null || !"ACTIVE".equals(info.getStatus())) {
                throw new RecoveryPointException("回復點不存在或不可用: " + recoveryPointId);
            }
            
            // 2. 驗證回復點完整性
            validateRecoveryPointIntegrity(info);
            
            // 3. 停用相關觸發器
            TriggerDisableSession triggerSession = disableTriggersForRestore(info.getAffectedTables());
            
            try {
                // 4. 執行資料恢復
                restoreTablesFromSnapshot(info);
                
                // 5. 驗證恢復結果
                validateRestoreResult(info);
                
                // 6. 標記回復點為已使用
                markRecoveryPointUsed(recoveryPointId);
                
                auditLogger.logRecoveryPointRestoreSuccess(recoveryPointId, info);
                
            } finally {
                // 7. 重新啟用觸發器
                enableTriggers(triggerSession);
            }
            
        } catch (Exception e) {
            auditLogger.logRecoveryPointRestoreFailed(recoveryPointId, e);
            throw new RecoveryException("從回復點恢復失敗", e);
        }
    }
    
    private Map<String, TableSnapshot> createTableSnapshots(List<String> tableNames) {
        Map<String, TableSnapshot> snapshots = new HashMap<>();
        
        for (String tableName : tableNames) {
            try {
                // 取得資料表結構資訊
                TableStructure structure = getTableStructure(tableName);
                
                // 計算資料檢查和
                String checksum = calculateTableChecksum(tableName);
                
                // 取得記錄數量
                long recordCount = getTableRecordCount(tableName);
                
                // 建立臨時備份表
                String backupTableName = createTemporaryBackupTable(tableName);
                
                TableSnapshot snapshot = TableSnapshot.builder()
                    .tableName(tableName)
                    .backupTableName(backupTableName)
                    .structure(structure)
                    .checksum(checksum)
                    .recordCount(recordCount)
                    .snapshotTime(Instant.now())
                    .build();
                
                snapshots.put(tableName, snapshot);
                
            } catch (Exception e) {
                throw new SnapshotException("建立資料表快照失敗: " + tableName, e);
            }
        }
        
        return snapshots;
    }
    
    private String createTemporaryBackupTable(String originalTable) {
        String backupTableName = "backup_" + originalTable + "_" + System.currentTimeMillis();
        
        String sql = String.format(
            "CREATE TABLE %s AS SELECT * FROM %s", 
            backupTableName, originalTable
        );
        
        jdbcTemplate.execute(sql);
        return backupTableName;
    }
}
```

### 3. 觸發器安全保護系統

#### 觸發器備份和監控
```sql
-- 建立觸發器備份表
CREATE TABLE IF NOT EXISTS trigger_backups (
    backup_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trigger_name VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    trigger_definition TEXT NOT NULL,
    backup_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    operation_id VARCHAR(255),
    backup_reason TEXT,
    restored BOOLEAN DEFAULT FALSE
);

-- 建立觸發器執行日誌表
CREATE TABLE IF NOT EXISTS trigger_execution_log (
    log_id BIGSERIAL PRIMARY KEY,
    trigger_name VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL,
    operation_type VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    execution_time_ms INTEGER NOT NULL,
    error_message TEXT,
    log_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    session_info JSONB
);

-- 建立觸發器監控函數
CREATE OR REPLACE FUNCTION log_trigger_execution(
    p_trigger_name TEXT,
    p_table_name TEXT,
    p_operation_type TEXT,
    p_start_time TIMESTAMP,
    p_error_message TEXT DEFAULT NULL
) RETURNS void AS $$
DECLARE
    v_execution_time INTEGER;
BEGIN
    v_execution_time := EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - p_start_time)) * 1000;
    
    INSERT INTO trigger_execution_log(
        trigger_name, table_name, operation_type, 
        execution_time_ms, error_message, session_info
    ) VALUES (
        p_trigger_name, p_table_name, p_operation_type,
        v_execution_time, p_error_message,
        jsonb_build_object(
            'user', current_user,
            'client_addr', inet_client_addr(),
            'application_name', current_setting('application_name', true)
        )
    );
END;
$$ LANGUAGE plpgsql;

-- 觸發器性能監控視圖
CREATE OR REPLACE VIEW trigger_performance_summary AS
SELECT 
    trigger_name,
    table_name,
    COUNT(*) as execution_count,
    AVG(execution_time_ms) as avg_execution_time,
    MAX(execution_time_ms) as max_execution_time,
    MIN(execution_time_ms) as min_execution_time,
    COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count,
    (COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)) as error_rate
FROM trigger_execution_log 
WHERE log_timestamp >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
GROUP BY trigger_name, table_name
ORDER BY avg_execution_time DESC;
```

#### Java 觸發器安全管理
```java
/**
 * 觸發器安全管理實施
 */
@Service
public class TriggerSecurityManagerImpl implements TriggerSecurityManager {
    
    private final JdbcTemplate jdbcTemplate;
    private final AuditLogger auditLogger;
    
    @Override
    public TriggerDisableSession safelyDisableTriggers(List<String> tableNames, String operationId) {
        String sessionId = UUID.randomUUID().toString();
        
        try {
            // 1. 查詢所有相關觸發器
            List<TriggerInfo> triggers = findTriggersForTables(tableNames);
            
            // 2. 備份觸發器定義
            Map<String, String> triggerBackups = backupTriggerDefinitions(triggers, operationId);
            
            // 3. 分析觸發器依賴關係
            TriggerDependencyGraph dependencyGraph = analyzeTriggerDependencies(triggers);
            
            // 4. 計算安全的停用順序
            List<String> safeDisableOrder = calculateSafeDisableOrder(dependencyGraph);
            
            // 5. 建立停用會話
            TriggerDisableSession session = new TriggerDisableSession(
                sessionId, operationId, triggerBackups, safeDisableOrder);
            
            // 6. 按順序停用觸發器
            for (String triggerName : safeDisableOrder) {
                disableTriggerSafely(triggerName, session);
            }
            
            auditLogger.logTriggerDisableSession(session);
            return session;
            
        } catch (Exception e) {
            auditLogger.logTriggerDisableSessionFailed(sessionId, operationId, e);
            throw new TriggerSecurityException("安全停用觸發器失敗", e);
        }
    }
    
    private void disableTriggerSafely(String triggerName, TriggerDisableSession session) {
        try {
            // 1. 檢查觸發器狀態
            TriggerStatus status = getTriggerStatus(triggerName);
            if (status == TriggerStatus.DISABLED) {
                session.addWarning("觸發器已經停用: " + triggerName);
                return;
            }
            
            // 2. 記錄停用前狀態
            session.recordTriggerState(triggerName, status);
            
            // 3 執行停用操作
            String sql = "ALTER TABLE " + getTriggerTable(triggerName) + 
                        " DISABLE TRIGGER " + triggerName;
            jdbcTemplate.execute(sql);
            
            // 4. 驗證停用成功
            TriggerStatus newStatus = getTriggerStatus(triggerName);
            if (newStatus != TriggerStatus.DISABLED) {
                throw new TriggerOperationException("觸發器停用驗證失敗: " + triggerName);
            }
            
            // 5. 記錄成功
            session.recordDisableSuccess(triggerName);
            auditLogger.logTriggerDisabled(triggerName, session.getOperationId());
            
        } catch (Exception e) {
            session.recordDisableFailure(triggerName, e);
            auditLogger.logTriggerDisableFailed(triggerName, session.getOperationId(), e);
            throw new TriggerOperationException("停用觸發器失敗: " + triggerName, e);
        }
    }
    
    @Override
    public void enableTriggers(TriggerDisableSession session) {
        try {
            // 反向順序啟用觸發器
            List<String> enableOrder = new ArrayList<>(session.getDisableOrder());
            Collections.reverse(enableOrder);
            
            for (String triggerName : enableOrder) {
                if (session.wasDisabledByUs(triggerName)) {
                    enableTriggerSafely(triggerName, session);
                }
            }
            
            // 驗證所有觸發器都已正確啟用
            validateAllTriggersEnabled(session);
            
            auditLogger.logTriggerEnableSessionComplete(session);
            
        } catch (Exception e) {
            auditLogger.logTriggerEnableSessionFailed(session, e);
            // 嘗試緊急恢復
            attemptEmergencyTriggerRecovery(session);
            throw new TriggerRecoveryException("啟用觸發器失敗，已嘗試緊急恢復", e);
        }
    }
    
    private void enableTriggerSafely(String triggerName, TriggerDisableSession session) {
        try {
            String sql = "ALTER TABLE " + getTriggerTable(triggerName) + 
                        " ENABLE TRIGGER " + triggerName;
            jdbcTemplate.execute(sql);
            
            // 驗證啟用成功
            TriggerStatus status = getTriggerStatus(triggerName);
            if (status != TriggerStatus.ENABLED) {
                throw new TriggerOperationException("觸發器啟用驗證失敗: " + triggerName);
            }
            
            session.recordEnableSuccess(triggerName);
            auditLogger.logTriggerEnabled(triggerName, session.getOperationId());
            
        } catch (Exception e) {
            session.recordEnableFailure(triggerName, e);
            throw new TriggerOperationException("啟用觸發器失敗: " + triggerName, e);
        }
    }
}
```

### 4. 即時風險監控系統

#### 系統資源監控
```java
/**
 * 即時系統監控器
 */
@Component
public class RealTimeSystemMonitor {
    
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(4);
    private final AlertService alertService;
    private final MetricsCollector metricsCollector;
    private final List<MonitoringListener> listeners = new CopyOnWriteArrayList<>();
    
    @PostConstruct
    public void startMonitoring() {
        // 每30秒監控系統資源
        scheduler.scheduleAtFixedRate(this::monitorSystemResources, 0, 30, TimeUnit.SECONDS);
        
        // 每10秒監控資料庫效能
        scheduler.scheduleAtFixedRate(this::monitorDatabasePerformance, 0, 10, TimeUnit.SECONDS);
        
        // 每分鐘監控連線狀況
        scheduler.scheduleAtFixedRate(this::monitorConnections, 0, 60, TimeUnit.SECONDS);
        
        // 每5分鐘監控觸發器效能
        scheduler.scheduleAtFixedRate(this::monitorTriggerPerformance, 0, 300, TimeUnit.SECONDS);
    }
    
    private void monitorSystemResources() {
        try {
            SystemResourceMetrics metrics = metricsCollector.collectSystemMetrics();
            
            // CPU 使用率檢查
            if (metrics.getCpuUsage() > 80) {
                AlertLevel level = metrics.getCpuUsage() > 90 ? AlertLevel.CRITICAL : AlertLevel.HIGH;
                sendAlert(level, "CPU使用率過高", 
                    "當前CPU使用率: " + metrics.getCpuUsage() + "%");
            }
            
            // 記憶體使用率檢查
            if (metrics.getMemoryUsage() > 85) {
                AlertLevel level = metrics.getMemoryUsage() > 95 ? AlertLevel.CRITICAL : AlertLevel.HIGH;
                sendAlert(level, "記憶體使用率過高", 
                    "當前記憶體使用率: " + metrics.getMemoryUsage() + "%");
            }
            
            // 磁碟空間檢查
            if (metrics.getDiskUsage() > 90) {
                sendAlert(AlertLevel.CRITICAL, "磁碟空間不足", 
                    "當前磁碟使用率: " + metrics.getDiskUsage() + "%");
            }
            
            // 通知監聽器
            notifyListeners(new SystemResourceEvent(metrics));
            
        } catch (Exception e) {
            logger.error("系統資源監控失敗", e);
        }
    }
    
    private void monitorDatabasePerformance() {
        try {
            DatabaseMetrics metrics = metricsCollector.collectDatabaseMetrics();
            
            // 查詢響應時間檢查
            if (metrics.getAverageQueryTime() > 5000) { // 5秒
                sendAlert(AlertLevel.HIGH, "資料庫查詢響應緩慢", 
                    "平均查詢時間: " + metrics.getAverageQueryTime() + "ms");
            }
            
            // 活躍連線數檢查
            if (metrics.getActiveConnections() > 70) { // 80個連線池的87.5%
                AlertLevel level = metrics.getActiveConnections() > 75 ? AlertLevel.CRITICAL : AlertLevel.HIGH;
                sendAlert(level, "資料庫連線數過高", 
                    "當前活躍連線數: " + metrics.getActiveConnections());
            }
            
            // 鎖定等待檢查
            if (metrics.getBlockedQueries() > 5) {
                sendAlert(AlertLevel.CRITICAL, "資料庫存在大量鎖定等待", 
                    "被阻塞的查詢數: " + metrics.getBlockedQueries());
            }
            
            // 死鎖檢查
            if (metrics.getDeadlockCount() > 0) {
                sendAlert(AlertLevel.HIGH, "檢測到資料庫死鎖", 
                    "死鎖數量: " + metrics.getDeadlockCount());
            }
            
            notifyListeners(new DatabasePerformanceEvent(metrics));
            
        } catch (Exception e) {
            logger.error("資料庫效能監控失敗", e);
        }
    }
    
    private void monitorTriggerPerformance() {
        try {
            List<TriggerMetrics> triggerMetrics = metricsCollector.collectTriggerMetrics();
            
            for (TriggerMetrics metrics : triggerMetrics) {
                // 執行時間異常檢查
                if (metrics.getAverageExecutionTime() > metrics.getBaselineExecutionTime() * 2.0) {
                    sendAlert(AlertLevel.MEDIUM, "觸發器效能異常", 
                        String.format("觸發器 %s 執行時間異常，平均: %dms (基準: %dms)",
                            metrics.getTriggerName(), 
                            metrics.getAverageExecutionTime(),
                            metrics.getBaselineExecutionTime()));
                }
                
                // 錯誤率檢查
                if (metrics.getErrorRate() > 0.05) { // 5%錯誤率
                    AlertLevel level = metrics.getErrorRate() > 0.1 ? AlertLevel.HIGH : AlertLevel.MEDIUM;
                    sendAlert(level, "觸發器錯誤率過高", 
                        String.format("觸發器 %s 錯誤率: %.2f%%", 
                            metrics.getTriggerName(), metrics.getErrorRate() * 100));
                }
            }
            
            notifyListeners(new TriggerPerformanceEvent(triggerMetrics));
            
        } catch (Exception e) {
            logger.error("觸發器效能監控失敗", e);
        }
    }
}
```

### 5. 緊急自動回復系統

#### 自動回復邏輯
```java
/**
 * 緊急自動回復系統
 */
@Service
public class EmergencyAutoRecoverySystem {
    
    private final RecoveryPointManager recoveryPointManager;
    private final DatabaseHealthChecker healthChecker;
    private final AlertService alertService;
    private final AuditLogger auditLogger;
    
    /**
     * 檢測到緊急情況時觸發自動回復
     */
    public void handleEmergencyRecovery(EmergencyEvent event) {
        String recoveryId = UUID.randomUUID().toString();
        
        try {
            auditLogger.logEmergencyRecoveryStart(recoveryId, event);
            
            // 1. 評估緊急情況嚴重程度
            EmergencySeverity severity = assessEmergencySeverity(event);
            
            // 2. 決定回復策略
            RecoveryStrategy strategy = determineRecoveryStrategy(event, severity);
            
            // 3. 執行自動回復
            switch (strategy) {
                case IMMEDIATE_ROLLBACK:
                    executeImmediateRollback(event, recoveryId);
                    break;
                case SELECTIVE_RESTORE:
                    executeSelectiveRestore(event, recoveryId);
                    break;
                case SYSTEM_SHUTDOWN:
                    executeSystemShutdown(event, recoveryId);
                    break;
                case MANUAL_INTERVENTION:
                    requestManualIntervention(event, recoveryId);
                    break;
            }
            
            // 4. 驗證回復結果
            RecoveryValidationResult validation = validateRecoveryResult(event, recoveryId);
            if (!validation.isSuccessful()) {
                // 回復失敗，升級到手動介入
                requestManualIntervention(event, recoveryId);
            }
            
            auditLogger.logEmergencyRecoveryComplete(recoveryId, strategy, validation);
            
        } catch (Exception e) {
            auditLogger.logEmergencyRecoveryFailed(recoveryId, event, e);
            // 最後手段：通知緊急應變團隊
            alertService.sendEmergencyAlert(
                "自動回復失敗", 
                "緊急情況: " + event.getDescription() + "，自動回復失敗，需要立即人工介入",
                AlertLevel.CRITICAL
            );
        }
    }
    
    private void executeImmediateRollback(EmergencyEvent event, String recoveryId) {
        try {
            // 1. 找到最近的回復點
            String operationId = event.getOperationId();
            RecoveryPoint latestRecoveryPoint = recoveryPointManager.getLatestRecoveryPoint(operationId);
            
            if (latestRecoveryPoint == null) {
                throw new RecoveryException("找不到可用的回復點");
            }
            
            // 2. 停止所有相關操作
            stopAllRelatedOperations(operationId);
            
            // 3. 執行回復
            recoveryPointManager.restoreFromRecoveryPoint(latestRecoveryPoint.getId());
            
            // 4. 驗證系統健康狀態
            DatabaseHealthStatus healthStatus = healthChecker.performHealthCheck();
            if (!healthStatus.isHealthy()) {
                throw new RecoveryException("回復後系統健康檢查失敗");
            }
            
            alertService.sendAlert(AlertLevel.HIGH, "緊急自動回復成功", 
                "系統已從回復點 " + latestRecoveryPoint.getId() + " 成功恢復");
            
        } catch (Exception e) {
            throw new EmergencyRecoveryException("立即回復失敗", e);
        }
    }
    
    private RecoveryStrategy determineRecoveryStrategy(EmergencyEvent event, EmergencySeverity severity) {
        // 根據事件類型和嚴重程度決定策略
        switch (event.getType()) {
            case DATABASE_CORRUPTION:
                return severity == EmergencySeverity.CRITICAL ? 
                    RecoveryStrategy.IMMEDIATE_ROLLBACK : RecoveryStrategy.SELECTIVE_RESTORE;
                    
            case RESOURCE_EXHAUSTION:
                return severity == EmergencySeverity.CRITICAL ? 
                    RecoveryStrategy.SYSTEM_SHUTDOWN : RecoveryStrategy.SELECTIVE_RESTORE;
                    
            case SECURITY_BREACH:
                return RecoveryStrategy.SYSTEM_SHUTDOWN;
                
            case TRIGGER_FAILURE:
                return RecoveryStrategy.SELECTIVE_RESTORE;
                
            case UNKNOWN:
            default:
                return RecoveryStrategy.MANUAL_INTERVENTION;
        }
    }
}
```

## 📊 部署和監控指南

### Docker Compose 部署範例
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: bms
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PRIMARY_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-security.sql:/docker-entrypoint-initdb.d/01-init-security.sql
    ports:
      - "5432:5432"
    command: >
      postgres 
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c log_statement=all
      -c log_min_duration_statement=1000
    
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
      
  app:
    build: .
    environment:
      DB_PRIMARY_URL: ***********************************
      DB_PRIMARY_USER: postgres
      DB_PRIMARY_PASSWORD: ${DB_PRIMARY_PASSWORD}
      REDIS_URL: redis://redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    depends_on:
      - postgres
      - redis
    ports:
      - "8080:8080"
    volumes:
      - app_logs:/app/logs
      - recovery_backups:/app/backups

volumes:
  postgres_data:
  app_logs:
  recovery_backups:
```

### 監控儀表板配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'database-security'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: /actuator/prometheus
    scrape_interval: 10s

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

rule_files:
  - "database_security_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 告警規則
```yaml
# database_security_rules.yml
groups:
  - name: database.security
    rules:
      - alert: DatabaseRecoveryPointCreationFailed
        expr: increase(database_recovery_point_creation_failures_total[5m]) > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "資料庫回復點建立失敗"
          description: "在過去5分鐘內有{{ $value }}次回復點建立失敗"

      - alert: TriggerPerformanceDegraded
        expr: avg_over_time(database_trigger_execution_time_ms[5m]) > 1000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "觸發器效能下降"
          description: "觸發器平均執行時間超過1秒"

      - alert: DatabaseConnectionPoolExhausted
        expr: database_connection_pool_active / database_connection_pool_max > 0.9
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "資料庫連線池接近耗盡"
          description: "連線池使用率: {{ $value | humanizePercentage }}"
```

這個完整的實施指南提供了：

1. **立即可用的程式碼**：可以直接部署的Java實施範例
2. **SQL腳本**：PostgreSQL安全機制的完整實施
3. **監控配置**：Prometheus和Grafana監控設置
4. **部署指南**：Docker Compose完整部署方案
5. **緊急應變**：自動回復和人工介入機制

建議按照文件中的三個階段逐步實施，確保每個階段都經過充分測試後再進入下一階段。