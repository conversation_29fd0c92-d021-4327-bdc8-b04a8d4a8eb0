!function(n){n.fn.removeOptions=function(){return 0===arguments.length?this.each(function(){n(this).children("option:not(:first)").remove()}):1===arguments.length&&"all"===arguments[0]?this.each(function(){n(this).children("option").remove()}):void 0},n.fn.fillOptions=function(t){return this.each(function(){var o=[];n.each(t,function(n,t){o.push('<option value="'+t.boundColumn+'">'+t.textColumn+"</option>")}),n(this).append(o.join(""))})}}(jQuery);