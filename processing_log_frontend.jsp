<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
<html>
<head>
    <meta charset="UTF-8">
    <title>Excel 處理記錄詳情</title>
    
    <%-- Bootstrap CSS --%>
    <link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
    
    <%-- jQuery --%>
    <script src="js/jquery/jquery.js" type="text/javascript"></script>
    
    <%-- Bootstrap JS --%>
    <script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
    
    <%-- Custom Styles --%>
    <style>
        .container { 
            padding: 20px; 
            max-width: 1400px;
        }
        
        .batch-info { 
            background-color: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 25px; 
            border: 1px solid #dee2e6; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .batch-info h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .info-row { 
            margin-bottom: 12px; 
            display: flex;
            align-items: center;
        }
        
        .info-label { 
            font-weight: bold; 
            color: #6c757d; 
            margin-right: 15px;
            min-width: 100px;
        }
        
        .info-value {
            color: #212529;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .stage-stats, .processing-logs {
            margin-bottom: 30px;
        }
        
        .stage-stats .table, .processing-logs .table {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stage-stats .table th {
            background-color: #007bff;
            color: white;
            border: none;
            font-weight: 600;
            text-align: center;
        }
        
        .processing-logs .table th {
            background-color: #28a745;
            color: white;
            border: none;
            font-weight: 600;
            text-align: center;
        }
        
        .table td {
            vertical-align: middle;
        }
        
        .success-row {
            background-color: #d4edda !important;
        }
        
        .warning-row {
            background-color: #fff3cd !important;
        }
        
        .danger-row {
            background-color: #f8d7da !important;
        }
        
        .error-message {
            max-width: 300px;
            word-wrap: break-word;
            font-size: 0.9em;
            max-height: 60px;
            overflow-y: auto;
        }
        
        .view-toggle {
            margin-bottom: 25px;
        }
        
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .error-alert {
            display: none;
        }
        
        .pagination-container {
            text-align: center;
            margin-top: 20px;
        }
        
        .progress-container {
            margin-top: 10px;
        }
        
        
        .section-title {
            color: #495057;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        
        .stats-card {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .stats-card h4 {
            margin: 0;
            font-size: 2em;
        }
        
        .stats-card p {
            margin: 5px 0 0 0;
            color: #6c757d;
        }
    </style>
</head>

<body>
    <div class="container">
        
        <!-- 頁面標題 -->
        <div class="page-header">
            <h1 id="pageTitle">Excel 處理記錄詳情 <small class="loading">載入中...</small></h1>
        </div>
        
        <!-- 錯誤訊息 -->
        <div class="alert alert-danger error-alert" id="errorAlert">
            <strong>錯誤！</strong> <span id="errorMessage"></span>
        </div>
        
        <!-- 批次基本資訊 -->
        <div class="batch-info" id="batchInfo" style="display: none;">
            <h3 class="section-title">批次資訊</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">批次 ID:</span>
                        <span class="info-value" id="importId"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">檔案名稱:</span>
                        <span class="info-value" id="fileName"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">上傳時間:</span>
                        <span class="info-value" id="uploadTime"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">上傳者:</span>
                        <span class="info-value" id="uploader"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-row">
                        <span class="info-label">處理狀態:</span>
                        <span class="status-badge" id="statusBadge"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">總筆數:</span>
                        <span class="info-value" id="totalRows"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">成功處理:</span>
                        <span class="info-value text-success" id="processedRows"></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">錯誤筆數:</span>
                        <span class="info-value text-danger" id="errorRows"></span>
                    </div>
                </div>
            </div>
            
            <!-- 統計卡片 -->
            <div class="row" style="margin-top: 20px;">
                <div class="col-md-3">
                    <div class="stats-card bg-info text-white">
                        <h4 id="totalRowsCard">-</h4>
                        <p>總筆數</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-success text-white">
                        <h4 id="successRowsCard">-</h4>
                        <p>成功處理</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-danger text-white">
                        <h4 id="errorRowsCard">-</h4>
                        <p>錯誤筆數</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-warning text-white">
                        <h4 id="successRateCard">-%</h4>
                        <p>成功率</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 檢視切換按鈕 -->
        <div class="view-toggle">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-primary" id="viewAllBtn" onclick="switchView('all')">
                    全部記錄
                </button>
                <button type="button" class="btn btn-warning" id="viewExceptionsBtn" onclick="switchView('exceptions')">
                    僅顯示異常
                </button>
            </div>
            
            <div class="clearfix"></div>
        </div>
        
        <!-- 處理階段統計 -->
        <div class="stage-stats">
            <h3 class="section-title">處理階段統計</h3>
            <div id="stageStatsContent">
                <div class="loading">載入階段統計中...</div>
            </div>
        </div>
        
        <!-- 處理記錄詳細清單 -->
        <div class="processing-logs">
            <h3 class="section-title">詳細處理記錄</h3>
            <div id="recordsInfo" class="well well-sm" style="display: none;">
                共 <span id="totalRecordsCount">0</span> 筆記錄，
                第 <span id="currentPageInfo">1</span> 頁 / 共 <span id="totalPagesInfo">1</span> 頁
            </div>
            
            <div id="processingLogsContent">
                <div class="loading">載入處理記錄中...</div>
            </div>
            
            <!-- 分頁控制 -->
            <div class="pagination-container" id="paginationContainer" style="display: none;">
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        // 全域變數
        let currentImportId = '';
        let currentViewType = 'all';
        let currentPage = 1;
        let totalPages = 1;
        
        $(document).ready(function() {
            // 取得 URL 參數
            const urlParams = new URLSearchParams(window.location.search);
            currentImportId = urlParams.get('import_id');
            currentViewType = urlParams.get('view') || 'all';
            
            if (!currentImportId) {
                showError('缺少必要的 import_id 參數');
                return;
            }
            
            // 初始化頁面
            initializePage();
        });
        
        function initializePage() {
            // 設定檢視切換按鈕狀態
            updateViewButtons();
            
            // 載入資料
            loadBatchInfo();
            loadStageStats();
            loadProcessingLogs(1);
        }
        
        function updateViewButtons() {
            $('#viewAllBtn, #viewExceptionsBtn').removeClass('active');
            if (currentViewType === 'exceptions') {
                $('#viewExceptionsBtn').addClass('active');
                $('#pageTitle').html('Excel 處理記錄詳情 <span class="label label-warning">僅顯示異常記錄</span>');
            } else {
                $('#viewAllBtn').addClass('active');
                $('#pageTitle').text('Excel 處理記錄詳情');
            }
        }
        
        function loadBatchInfo() {
            $.ajax({
                url: 'api_processing_log.jsp',
                method: 'GET',
                data: {
                    action: 'batch_info',
                    import_id: currentImportId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.result === 'OK') {
                        displayBatchInfo(response.data);
                    } else {
                        showError(response.message || '載入批次資訊失敗');
                    }
                },
                error: function(xhr, status, error) {
                    showError('載入批次資訊時發生錯誤: ' + error);
                }
            });
        }
        
        function displayBatchInfo(data) {
            $('#importId').text(data.import_id || '');
            $('#fileName').text(data.original_file_name || '');
            $('#uploadTime').text(formatDateTime(data.upload_timestamp) || '');
            $('#uploader').text(data.cr_user || '');
            
            // 狀態標籤
            const statusBadge = $('#statusBadge');
            const statusDisplay = data.status_display || data.status;
            statusBadge.text(statusDisplay);
            
            // 根據狀態設定樣式
            statusBadge.removeClass('label-success label-info label-warning label-danger');
            if (data.status === 'COMPLETED') {
                statusBadge.addClass('label-success');
            } else if (data.status === 'PROCESSING') {
                statusBadge.addClass('label-info');
            } else if (data.status === 'FAILED') {
                statusBadge.addClass('label-danger');
            } else {
                statusBadge.addClass('label-warning');
            }
            
            // 統計資料
            const totalRows = data.total_rows_in_excel || 0;
            const processedRows = data.processed_rows_count || 0;
            const errorRows = data.error_rows_count || 0;
            const successRate = totalRows > 0 ? Math.round((processedRows / totalRows) * 100) : 0;
            
            $('#totalRows').text(totalRows);
            $('#processedRows').text(processedRows);
            $('#errorRows').text(errorRows);
            
            // 統計卡片
            $('#totalRowsCard').text(totalRows);
            $('#successRowsCard').text(processedRows);
            $('#errorRowsCard').text(errorRows);
            $('#successRateCard').text(successRate + '%');
            
            $('#batchInfo').show();
        }
        
        function loadStageStats() {
            $.ajax({
                url: 'api_processing_log.jsp',
                method: 'GET',
                data: {
                    action: 'stage_stats',
                    import_id: currentImportId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.result === 'OK') {
                        displayStageStats(response.data);
                    } else {
                        $('#stageStatsContent').html('<div class="alert alert-warning">載入階段統計失敗: ' + (response.message || '未知錯誤') + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#stageStatsContent').html('<div class="alert alert-danger">載入階段統計時發生錯誤: ' + error + '</div>');
                }
            });
        }
        
        function displayStageStats(data) {
            if (!data || data.length === 0) {
                $('#stageStatsContent').html('<div class="alert alert-info"><i class="glyphicon glyphicon-info-sign"></i> 此批次尚未開始處理或無處理記錄。</div>');
                return;
            }
            
            let html = '<table class="table table-striped table-bordered">';
            html += '<thead><tr>';
            html += '<th>處理階段</th>';
            html += '<th>階段說明</th>';
            html += '<th>總筆數</th>';
            html += '<th>成功筆數</th>';
            html += '<th>失敗筆數</th>';
            html += '<th>成功率</th>';
            html += '</tr></thead><tbody>';
            
            data.forEach(function(stage) {
                html += '<tr>';
                html += '<td>' + (stage.processing_stage || '') + '</td>';
                html += '<td>' + (stage.stage_description || '') + '</td>';
                html += '<td>' + (stage.total_records || 0) + '</td>';
                html += '<td class="text-success"><strong>' + (stage.success_records || 0) + '</strong></td>';
                html += '<td class="text-danger"><strong>' + (stage.failed_records || 0) + '</strong></td>';
                html += '<td>' + (stage.success_rate || 0).toFixed(1) + '%</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            $('#stageStatsContent').html(html);
        }
        
        function loadProcessingLogs(page) {
            currentPage = page || 1;
            
            $('#processingLogsContent').html('<div class="loading">載入處理記錄中...</div>');
            
            $.ajax({
                url: 'api_processing_log.jsp',
                method: 'GET',
                data: {
                    action: 'processing_logs',
                    import_id: currentImportId,
                    view: currentViewType,
                    page: currentPage,
                    size: 50
                },
                dataType: 'json',
                success: function(response) {
                    if (response.result === 'OK') {
                        displayProcessingLogs(response.data, response.pagination);
                    } else {
                        $('#processingLogsContent').html('<div class="alert alert-warning">載入處理記錄失敗: ' + (response.message || '未知錯誤') + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#processingLogsContent').html('<div class="alert alert-danger">載入處理記錄時發生錯誤: ' + error + '</div>');
                }
            });
        }
        
        function displayProcessingLogs(data, pagination) {
            if (!data || data.length === 0) {
                let message = currentViewType === 'exceptions' ? 
                    '此批次沒有異常記錄，所有資料都處理成功！' : 
                    '沒有找到符合條件的處理記錄。';
                    
                $('#processingLogsContent').html('<div class="alert alert-info"><i class="glyphicon glyphicon-info-sign"></i> ' + message + '</div>');
                $('#recordsInfo').hide();
                $('#paginationContainer').hide();
                return;
            }
            
            // 更新記錄資訊
            if (pagination) {
                $('#totalRecordsCount').text(pagination.total_records);
                $('#currentPageInfo').text(pagination.current_page);
                $('#totalPagesInfo').text(pagination.total_pages);
                $('#recordsInfo').show();
                
                // 更新分頁
                totalPages = pagination.total_pages;
                updatePagination(pagination);
            }
            
            let html = '<table class="table table-striped table-bordered table-condensed">';
            html += '<thead><tr>';
            html += '<th style="width: 80px;">Excel 行號</th>';
            html += '<th style="width: 150px;">認定號碼</th>';
            html += '<th style="width: 120px;">案件編號</th>';
            html += '<th style="width: 100px;">處理階段</th>';
            html += '<th style="width: 120px;">階段說明</th>';
            html += '<th style="width: 100px;">處理狀態</th>';
            html += '<th>錯誤訊息</th>';
            html += '<th style="width: 140px;">處理時間</th>';
            html += '</tr></thead><tbody>';
            
            data.forEach(function(log) {
                const rowClass = log.status_class ? log.status_class + '-row' : '';
                html += '<tr class="' + rowClass + '">';
                html += '<td class="text-center">' + (log.row_number || '') + '</td>';
                html += '<td>' + (log.reg_num || '') + '</td>';
                html += '<td>' + (log.case_id || '') + '</td>';
                html += '<td>' + (log.processing_stage || '') + '</td>';
                html += '<td>' + (log.stage_description || '') + '</td>';
                html += '<td>' + (log.processing_status || '') + '</td>';
                html += '<td class="error-message">' + (log.error_message || '') + '</td>';
                html += '<td>' + formatDateTime(log.processing_datetime) + '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            $('#processingLogsContent').html(html);
        }
        
        function updatePagination(pagination) {
            if (pagination.total_pages <= 1) {
                $('#paginationContainer').hide();
                return;
            }
            
            let html = '<nav><ul class="pagination">';
            
            // 首頁
            if (pagination.has_prev) {
                html += '<li><a href="javascript:void(0)" onclick="loadProcessingLogs(1)">首頁</a></li>';
                html += '<li><a href="javascript:void(0)" onclick="loadProcessingLogs(' + (pagination.current_page - 1) + ')">上頁</a></li>';
            } else {
                html += '<li class="disabled"><span>首頁</span></li>';
                html += '<li class="disabled"><span>上頁</span></li>';
            }
            
            // 頁碼
            const startPage = Math.max(1, pagination.current_page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                if (i === pagination.current_page) {
                    html += '<li class="active"><span>' + i + '</span></li>';
                } else {
                    html += '<li><a href="javascript:void(0)" onclick="loadProcessingLogs(' + i + ')">' + i + '</a></li>';
                }
            }
            
            // 末頁
            if (pagination.has_next) {
                html += '<li><a href="javascript:void(0)" onclick="loadProcessingLogs(' + (pagination.current_page + 1) + ')">下頁</a></li>';
                html += '<li><a href="javascript:void(0)" onclick="loadProcessingLogs(' + pagination.total_pages + ')">末頁</a></li>';
            } else {
                html += '<li class="disabled"><span>下頁</span></li>';
                html += '<li class="disabled"><span>末頁</span></li>';
            }
            
            html += '</ul></nav>';
            $('#paginationContainer').html(html).show();
        }
        
        function switchView(viewType) {
            if (currentViewType !== viewType) {
                currentViewType = viewType;
                currentPage = 1;
                
                // 更新 URL
                const newUrl = new URL(window.location);
                if (viewType === 'exceptions') {
                    newUrl.searchParams.set('view', 'exceptions');
                } else {
                    newUrl.searchParams.delete('view');
                }
                window.history.pushState({}, '', newUrl);
                
                updateViewButtons();
                loadProcessingLogs(1);
            }
        }
        
        
        function showError(message) {
            $('#errorMessage').text(message);
            $('#errorAlert').show();
            $('.loading').hide();
        }
        
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            
            // 如果是 PostgreSQL 時間戳記格式，取前 19 個字元
            if (dateTimeStr.length > 19) {
                return dateTimeStr.substring(0, 19);
            }
            
            return dateTimeStr;
        }
    </script>
</body>
</html>