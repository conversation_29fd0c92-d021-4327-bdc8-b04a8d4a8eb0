# 新北市違章建築管理系統

## 系統簡介

新北市違章建築管理系統是一個協助管理違章建築案件的政府系統，提供案件管理、流程追蹤、統計分析等功能。系統管理超過37萬筆案件資料，包含100萬筆流程記錄。

## 系統架構

- **應用伺服器**：Apache Tomcat 9.0.98
- **主資料庫**：PostgreSQL (bms)
- **輔助資料庫**：SQL Server (ramsGIS)
- **開發框架**：CodeCharge Studio (Legacy)
- **前端框架**：Bootstrap 5.3.3 + jQuery 3.7.1

## 目錄結構

```
/
├── CLAUDE.md              # Claude Code 開發指南
├── README.md              # 本文件
├── DOCS/                  # 文件目錄
│   ├── 01_ACTIVE/         # 活躍文件
│   ├── 02_REFERENCE/      # 參考文件
│   │   ├── DATABASE/      # 資料庫文件
│   │   ├── SYSTEM/        # 系統架構文件
│   │   └── BUSINESS/      # 業務流程文件
│   ├── 03_ARCHIVE/        # 歸檔文件
│   ├── 04_WEBSERVICE_API/ # WebService API文件
│   └── 違章建築資料拋送至國土署系統/  # MOI系統文件
├── WEB-INF/               # Java 類別與配置
│   ├── classes/           # 編譯後的 Java 類別
│   ├── java/              # Java 原始碼
│   ├── lib/               # Java 函式庫
│   └── web.xml            # Web 應用配置
├── js/                    # JavaScript 檔案
│   ├── lib/               # JavaScript 函式庫
│   ├── modules/           # 模組化 JS
│   └── vendor/            # 第三方函式庫
├── css/                   # 樣式表
├── img/                   # 圖片資源
├── tools/                 # 工具程式
│   ├── scripts/           # 各種腳本
│   │   ├── database/      # 資料庫腳本
│   │   └── deployment/    # 部署腳本
│   └── HealthDiagnostics/ # 健康診斷工具
└── templates/             # HTML 模板
```

## 快速開始

### 環境需求

- Java 11+ (for JSP)
- .NET 8 SDK (for MOI data exporter)
- Docker Desktop
- PostgreSQL 15+
- Git

### 開發環境設置

1. 複製專案到本地
2. 設置資料庫連線（參考 WEB-INF/site.properties）
3. 部署到 Tomcat
4. 存取系統：http://localhost:8080/

### 重要文件

- [Claude Code 開發指南](CLAUDE.md)
- [系統架構總覽](DOCS/02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫完整指南](DOCS/02_REFERENCE/DATABASE_COMPLETE_GUIDE.md)
- [業務流程指南](DOCS/02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md)
- [技術實施指南](DOCS/02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md)

## 核心功能模組

### 案件管理（IM10xxx）
- im10101 - 違章建築案件管理
- im10201 - 案件查詢
- im10301 - 案件統計

### 認定管理（IM20xxx）
- im20101 - 違建認定作業
- im20201 - 認定統計分析
- im20301 - 認定報表

### 拆除管理（IM30xxx）
- im30101 - 拆除排程
- im30201 - 拆除執行管理

### 查詢統計（IM40xxx）
- im40101 - 綜合查詢
- im40201 - 結案報告書
- im40301 - 統計分析

### 報表管理（IM50xxx-IM90xxx）
- 各類統計報表
- 管理報表
- 業務分析報表

## 開發團隊

- 違建管理科 - 系統需求與業務邏輯
- 資訊科 - 系統開發與維護
- 外部廠商 - 技術支援

## 維護與支援

如需技術支援或問題回報，請聯繫：
- 系統管理員
- 資訊科技術窗口

## 授權資訊

本系統為新北市政府內部使用系統，未經授權不得對外公開或使用。

---

最後更新日期：2025-01-11