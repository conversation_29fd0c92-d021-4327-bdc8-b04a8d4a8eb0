package com.ezek.report;

import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 大量資料集查詢分析器
 * 
 * 專門針對37萬筆案件和100萬筆流程記錄的系統進行查詢分析和最佳化建議
 * 
 * 主要功能：
 * 1. 資料庫索引分析與建議
 * 2. 查詢執行計劃分析
 * 3. 分頁查詢最佳化
 * 4. 資料快取策略建議
 * 5. 批次處理最佳化
 * 6. 資料庫分割建議
 */
public class LargeDatasetAnalyzer {
    
    private static final String CONNECTION_NAME = "DBConn";
    private static final int SAMPLE_SIZE = 1000; // 取樣大小
    private static final int ANALYSIS_TIMEOUT = 300000; // 5分鐘超時
    
    // === 資料結構定義 ===
    
    /**
     * 表格分析結果
     */
    public static class TableAnalysis {
        public final String tableName;
        public final long rowCount;
        public final long totalSize; // bytes
        public final double avgRowSize;
        public final List<IndexAnalysis> indexes;
        public final List<String> recommendations;
        public final Map<String, ColumnStats> columnStats;
        
        public TableAnalysis(String tableName, long rowCount, long totalSize) {
            this.tableName = tableName;
            this.rowCount = rowCount;
            this.totalSize = totalSize;
            this.avgRowSize = rowCount > 0 ? (double) totalSize / rowCount : 0;
            this.indexes = new ArrayList<>();
            this.recommendations = new ArrayList<>();
            this.columnStats = new HashMap<>();
        }
        
        public void addRecommendation(String recommendation) {
            recommendations.add(recommendation);
        }
    }
    
    /**
     * 索引分析結果
     */
    public static class IndexAnalysis {
        public final String indexName;
        public final String[] columns;
        public final boolean isUnique;
        public final long size; // bytes
        public final double selectivity; // 選擇性
        public final int usage; // 使用頻率
        public final boolean isRecommendedForRemoval;
        
        public IndexAnalysis(String indexName, String[] columns, boolean isUnique, 
                           long size, double selectivity, int usage) {
            this.indexName = indexName;
            this.columns = columns;
            this.isUnique = isUnique;
            this.size = size;
            this.selectivity = selectivity;
            this.usage = usage;
            this.isRecommendedForRemoval = selectivity > 0.9 && usage < 10; // 低選擇性且少用
        }
    }
    
    /**
     * 欄位統計資訊
     */
    public static class ColumnStats {
        public final String columnName;
        public final String dataType;
        public final long distinctValues;
        public final long nullCount;
        public final double selectivity;
        public final boolean isIndexCandidate;
        public final List<Object> frequentValues;
        
        public ColumnStats(String columnName, String dataType, long distinctValues, 
                          long nullCount, long totalRows) {
            this.columnName = columnName;
            this.dataType = dataType;
            this.distinctValues = distinctValues;
            this.nullCount = nullCount;
            this.selectivity = totalRows > 0 ? (double) distinctValues / totalRows : 0;
            this.isIndexCandidate = selectivity > 0.1 && selectivity < 0.9; // 適中的選擇性
            this.frequentValues = new ArrayList<>();
        }
    }
    
    /**
     * 查詢執行計劃分析
     */
    public static class QueryPlanAnalysis {
        public final String query;
        public final String executionPlan;
        public final long estimatedCost;
        public final long actualExecutionTime;
        public final boolean hasFullTableScan;
        public final boolean hasNestedLoop;
        public final List<String> missingIndexes;
        public final List<String> optimizationSuggestions;
        
        public QueryPlanAnalysis(String query, String executionPlan, long estimatedCost, 
                                long actualExecutionTime) {
            this.query = query;
            this.executionPlan = executionPlan;
            this.estimatedCost = estimatedCost;
            this.actualExecutionTime = actualExecutionTime;
            this.hasFullTableScan = executionPlan.toLowerCase().contains("seq scan");
            this.hasNestedLoop = executionPlan.toLowerCase().contains("nested loop");
            this.missingIndexes = new ArrayList<>();
            this.optimizationSuggestions = new ArrayList<>();
            
            analyzeExecutionPlan();
        }
        
        private void analyzeExecutionPlan() {
            // 分析執行計劃並提供建議
            if (hasFullTableScan) {
                optimizationSuggestions.add("檢測到全表掃描，建議增加適當索引");
            }
            
            if (hasNestedLoop && estimatedCost > 10000) {
                optimizationSuggestions.add("高成本的巢狀迴圈連接，考慮使用 Hash Join 或 Merge Join");
            }
            
            if (actualExecutionTime > 1000) {
                optimizationSuggestions.add("查詢執行時間過長，建議進行查詢重寫或資料分割");
            }
        }
    }
    
    // === 核心分析方法 ===
    
    /**
     * 分析核心業務表格
     */
    public static List<TableAnalysis> analyzeCoreBusinessTables() {
        List<TableAnalysis> results = new ArrayList<>();
        
        // 定義核心表格
        String[] coreTables = {
            "buildcase",    // 37萬筆案件
            "tbflow",       // 100萬筆流程記錄
            "ibmcase",      // 案件詳細資料
            "ibmfym",       // 流程資料
            "ibmsts",       // 狀態資料
            "ibmuser",      // 使用者資料
            "ibmcode"       // 代碼表
        };
        
        for (String tableName : coreTables) {
            TableAnalysis analysis = analyzeTable(tableName);
            if (analysis != null) {
                results.add(analysis);
                generateTableRecommendations(analysis);
            }
        }
        
        return results;
    }
    
    /**
     * 分析單一表格
     */
    public static TableAnalysis analyzeTable(String tableName) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = getConnection();
            
            // 取得表格基本統計
            long rowCount = getTableRowCount(conn, tableName);
            long tableSize = getTableSize(conn, tableName);
            
            TableAnalysis analysis = new TableAnalysis(tableName, rowCount, tableSize);
            
            // 分析欄位統計
            analyzeColumnStatistics(conn, tableName, analysis);
            
            // 分析索引
            analyzeTableIndexes(conn, tableName, analysis);
            
            return analysis;
            
        } catch (Exception e) {
            System.err.println("Error analyzing table " + tableName + ": " + e.getMessage());
            return null;
        } finally {
            closeResources(conn, pstmt, rs);
        }
    }
    
    /**
     * 分析常見查詢的執行計劃
     */
    public static List<QueryPlanAnalysis> analyzeCommonQueryPlans() {
        List<QueryPlanAnalysis> analyses = new ArrayList<>();
        
        // 定義常見的查詢模式
        String[] commonQueries = {
            // 案件查詢
            "SELECT * FROM buildcase WHERE caseopened = '231' AND s_empno = 'EMP001'",
            "SELECT * FROM buildcase WHERE case_no LIKE 'A113%' ORDER BY case_no DESC LIMIT 100",
            
            // 流程查詢
            "SELECT * FROM tbflow WHERE case_no = 'A113000001' ORDER BY flow_sdate",
            "SELECT case_no, COUNT(*) FROM tbflow GROUP BY case_no HAVING COUNT(*) > 5",
            
            // 狀態查詢
            "SELECT b.*, s.acc_rlt FROM buildcase b JOIN ibmsts s ON b.case_no = s.case_id WHERE s.acc_rlt IN ('231', '232', '234')",
            
            // 使用者查詢
            "SELECT empname FROM ibmuser WHERE empno IN (SELECT DISTINCT s_empno FROM buildcase WHERE s_empno IS NOT NULL)",
            
            // 代碼查詢
            "SELECT code_desc FROM ibmcode WHERE code_type = 'RLT' AND code_seq = '231'"
        };
        
        for (String query : commonQueries) {
            QueryPlanAnalysis analysis = analyzeQueryPlan(query);
            if (analysis != null) {
                analyses.add(analysis);
            }
        }
        
        return analyses;
    }
    
    /**
     * 分析單一查詢的執行計劃
     */
    public static QueryPlanAnalysis analyzeQueryPlan(String query) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = getConnection();
            
            // 取得執行計劃
            String explainQuery = "EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) " + query;
            pstmt = conn.prepareStatement(explainQuery);
            
            long startTime = System.currentTimeMillis();
            rs = pstmt.executeQuery();
            long executionTime = System.currentTimeMillis() - startTime;
            
            String executionPlan = "";
            long estimatedCost = 0;
            
            if (rs.next()) {
                executionPlan = rs.getString(1);
                
                // 從 JSON 計劃中提取成本資訊
                estimatedCost = extractCostFromPlan(executionPlan);
            }
            
            return new QueryPlanAnalysis(query, executionPlan, estimatedCost, executionTime);
            
        } catch (Exception e) {
            System.err.println("Error analyzing query plan for: " + query);
            System.err.println("Error: " + e.getMessage());
            return null;
        } finally {
            closeResources(conn, pstmt, rs);
        }
    }
    
    // === 最佳化建議生成 ===
    
    /**
     * 生成表格最佳化建議
     */
    private static void generateTableRecommendations(TableAnalysis analysis) {
        // 大表格建議
        if (analysis.rowCount > 100000) {
            analysis.addRecommendation("大型表格：考慮資料分割或歸檔舊資料");
            
            if (analysis.avgRowSize > 1000) {
                analysis.addRecommendation("平均行大小較大：考慮欄位正規化或壓縮");
            }
        }
        
        // 索引建議
        if (analysis.indexes.isEmpty()) {
            analysis.addRecommendation("缺少索引：建議為常用查詢欄位建立索引");
        } else {
            int unusedIndexes = 0;
            for (IndexAnalysis index : analysis.indexes) {
                if (index.isRecommendedForRemoval) {
                    unusedIndexes++;
                }
            }
            if (unusedIndexes > 0) {
                analysis.addRecommendation(String.format("發現 %d 個低效索引，建議移除以改善寫入效能", unusedIndexes));
            }
        }
        
        // 欄位建議
        for (ColumnStats stats : analysis.columnStats.values()) {
            if (stats.isIndexCandidate) {
                analysis.addRecommendation(String.format("欄位 %s 適合建立索引（選擇性: %.2f）", 
                    stats.columnName, stats.selectivity));
            }
            
            if (stats.nullCount > analysis.rowCount * 0.8) {
                analysis.addRecommendation(String.format("欄位 %s 有大量 NULL 值，考慮重新設計或稀疏索引", 
                    stats.columnName));
            }
        }
    }
    
    /**
     * 生成分頁查詢最佳化建議
     */
    public static List<String> generatePaginationOptimizations() {
        List<String> recommendations = new ArrayList<>();
        
        recommendations.add("使用 OFFSET/LIMIT 的替代方案：");
        recommendations.add("  - 使用 cursor-based pagination（WHERE id > last_seen_id）");
        recommendations.add("  - 建立複合索引支援排序欄位");
        recommendations.add("  - 避免大 OFFSET 值，改用範圍查詢");
        
        recommendations.add("針對案件列表的最佳化：");
        recommendations.add("  - 在 (caseopened, case_no) 建立複合索引");
        recommendations.add("  - 預先計算常用的統計資料");
        recommendations.add("  - 使用 materialized view 快取複雜查詢結果");
        
        return recommendations;
    }
    
    /**
     * 生成快取策略建議
     */
    public static List<String> generateCachingStrategies() {
        List<String> strategies = new ArrayList<>();
        
        strategies.add("應用層快取策略：");
        strategies.add("  - 快取 ibmcode 代碼表資料（變動頻率低）");
        strategies.add("  - 快取使用者資訊和權限");
        strategies.add("  - 實施查詢結果快取（Redis 或 Memcached）");
        
        strategies.add("資料庫層快取最佳化：");
        strategies.add("  - 增加 shared_buffers 設定");
        strategies.add("  - 調整 effective_cache_size");
        strategies.add("  - 使用 pg_stat_statements 監控快取效率");
        
        strategies.add("檔案系統快取：");
        strategies.add("  - 使用 SSD 儲存熱門資料");
        strategies.add("  - 設定適當的檔案系統快取大小");
        
        return strategies;
    }
    
    // === 資料庫分割建議 ===
    
    /**
     * 分析資料分割需求
     */
    public static Map<String, List<String>> analyzePartitioningNeeds() {
        Map<String, List<String>> partitioningRecommendations = new HashMap<>();
        
        // buildcase 表格分割建議
        List<String> buildcasePartitioning = new ArrayList<>();
        buildcasePartitioning.add("依據年份分割：CREATE TABLE buildcase_2023 PARTITION OF buildcase FOR VALUES FROM ('2023-01-01') TO ('2024-01-01')");
        buildcasePartitioning.add("依據案件狀態分割：將已結案和進行中案件分開儲存");
        buildcasePartitioning.add("依據區域分割：根據違建地區進行水平分割");
        partitioningRecommendations.put("buildcase", buildcasePartitioning);
        
        // tbflow 表格分割建議
        List<String> tbflowPartitioning = new ArrayList<>();
        tbflowPartitioning.add("依據時間分割：按月或季分割流程記錄");
        tbflowPartitioning.add("依據流程類型分割：不同業務流程使用不同資料表");
        tbflowPartitioning.add("歸檔策略：將一年前的記錄移至歸檔表");
        partitioningRecommendations.put("tbflow", tbflowPartitioning);
        
        return partitioningRecommendations;
    }
    
    // === 效能監控建議 ===
    
    /**
     * 生成效能監控策略
     */
    public static List<String> generatePerformanceMonitoringStrategies() {
        List<String> strategies = new ArrayList<>();
        
        strategies.add("即時監控指標：");
        strategies.add("  - 查詢執行時間 > 1 秒的慢查詢");
        strategies.add("  - 資料庫連線數使用率");
        strategies.add("  - 磁碟 I/O 使用率");
        strategies.add("  - 記憶體使用率");
        
        strategies.add("定期分析任務：");
        strategies.add("  - 每日執行 VACUUM ANALYZE");
        strategies.add("  - 週期性檢查索引使用率");
        strategies.add("  - 月度資料增長趨勢分析");
        
        strategies.add("警示機制：");
        strategies.add("  - 設定慢查詢警示閾值（> 2秒）");
        strategies.add("  - 磁碟空間不足警示（< 20%）");
        strategies.add("  - 資料庫連線數過高警示（> 80%）");
        
        return strategies;
    }
    
    // === 輔助方法 ===
    
    /**
     * 取得資料庫連接
     */
    private static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(
            "************************************",
            "postgres", 
            "S!@h@202203"
        );
    }
    
    /**
     * 取得表格行數
     */
    private static long getTableRowCount(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT reltuples::BIGINT FROM pg_class WHERE relname = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, tableName.toLowerCase());
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        }
        return 0;
    }
    
    /**
     * 取得表格大小
     */
    private static long getTableSize(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT pg_total_relation_size(?)";
        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, tableName);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        }
        return 0;
    }
    
    /**
     * 分析欄位統計
     */
    private static void analyzeColumnStatistics(Connection conn, String tableName, TableAnalysis analysis) {
        try {
            String sql = "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName.toLowerCase());
                try (ResultSet rs = pstmt.executeQuery()) {
                    while (rs.next()) {
                        String columnName = rs.getString("column_name");
                        String dataType = rs.getString("data_type");
                        
                        // 取得欄位統計
                        ColumnStats stats = analyzeColumn(conn, tableName, columnName, dataType, analysis.rowCount);
                        if (stats != null) {
                            analysis.columnStats.put(columnName, stats);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("Error analyzing column statistics for " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * 分析單一欄位
     */
    private static ColumnStats analyzeColumn(Connection conn, String tableName, String columnName, 
                                           String dataType, long totalRows) {
        try {
            String sql = String.format(
                "SELECT COUNT(DISTINCT %s) as distinct_count, COUNT(*) - COUNT(%s) as null_count FROM %s",
                columnName, columnName, tableName
            );
            
            try (PreparedStatement pstmt = conn.prepareStatement(sql);
                 ResultSet rs = pstmt.executeQuery()) {
                
                if (rs.next()) {
                    long distinctCount = rs.getLong("distinct_count");
                    long nullCount = rs.getLong("null_count");
                    
                    return new ColumnStats(columnName, dataType, distinctCount, nullCount, totalRows);
                }
            }
        } catch (SQLException e) {
            System.err.println("Error analyzing column " + columnName + ": " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 分析表格索引
     */
    private static void analyzeTableIndexes(Connection conn, String tableName, TableAnalysis analysis) {
        try {
            String sql = "SELECT indexname, indexdef FROM pg_indexes WHERE tablename = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName.toLowerCase());
                try (ResultSet rs = pstmt.executeQuery()) {
                    while (rs.next()) {
                        String indexName = rs.getString("indexname");
                        String indexDef = rs.getString("indexdef");
                        
                        // 解析索引資訊
                        IndexAnalysis indexAnalysis = parseIndexDefinition(indexName, indexDef);
                        if (indexAnalysis != null) {
                            analysis.indexes.add(indexAnalysis);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            System.err.println("Error analyzing indexes for " + tableName + ": " + e.getMessage());
        }
    }
    
    /**
     * 解析索引定義
     */
    private static IndexAnalysis parseIndexDefinition(String indexName, String indexDef) {
        // 簡單的索引解析（實際實作需要更複雜的邏輯）
        String[] columns = extractColumnsFromIndexDef(indexDef);
        boolean isUnique = indexDef.contains("UNIQUE");
        
        // 這裡需要查詢實際的索引統計資訊
        long size = 0; // 需要從 pg_relation_size 取得
        double selectivity = 0.5; // 需要從統計資料計算
        int usage = 0; // 需要從 pg_stat_user_indexes 取得
        
        return new IndexAnalysis(indexName, columns, isUnique, size, selectivity, usage);
    }
    
    /**
     * 從索引定義中提取欄位名稱
     */
    private static String[] extractColumnsFromIndexDef(String indexDef) {
        // 簡單的正則表達式提取（實際實作需要更強健）
        int startIndex = indexDef.indexOf("(");
        int endIndex = indexDef.indexOf(")", startIndex);
        
        if (startIndex != -1 && endIndex != -1) {
            String columnsPart = indexDef.substring(startIndex + 1, endIndex);
            return columnsPart.split(",\\s*");
        }
        
        return new String[0];
    }
    
    /**
     * 從執行計劃中提取成本資訊
     */
    private static long extractCostFromPlan(String executionPlan) {
        // 簡單的成本提取（實際需要解析 JSON）
        try {
            if (executionPlan.contains("Total Cost")) {
                // 解析 JSON 格式的執行計劃
                // 這裡需要使用 JSON 解析庫
                return 1000; // 佔位符
            }
        } catch (Exception e) {
            System.err.println("Error extracting cost from plan: " + e.getMessage());
        }
        return 0;
    }
    
    /**
     * 關閉資源
     */
    private static void closeResources(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        if (rs != null) {
            try { rs.close(); } catch (SQLException e) { /* ignore */ }
        }
        if (pstmt != null) {
            try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        }
        if (conn != null) {
            try { conn.close(); } catch (SQLException e) { /* ignore */ }
        }
    }
    
    // === 報告生成 ===
    
    /**
     * 生成完整的分析報告
     */
    public static String generateComprehensiveReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 大量資料集查詢分析報告 ===\n\n");
        report.append("系統規模：37萬筆案件 + 100萬筆流程記錄\n");
        report.append("分析時間：").append(new Date()).append("\n\n");
        
        // 表格分析
        report.append("=== 核心表格分析 ===\n");
        List<TableAnalysis> tableAnalyses = analyzeCoreBusinessTables();
        for (TableAnalysis analysis : tableAnalyses) {
            report.append(String.format(
                "表格：%s\n" +
                "  記錄數：%,d\n" +
                "  大小：%.2f MB\n" +
                "  平均行大小：%.2f bytes\n" +
                "  索引數：%d\n" +
                "  建議：\n",
                analysis.tableName,
                analysis.rowCount,
                analysis.totalSize / (1024.0 * 1024.0),
                analysis.avgRowSize,
                analysis.indexes.size()
            ));
            
            for (String recommendation : analysis.recommendations) {
                report.append("    - ").append(recommendation).append("\n");
            }
            report.append("\n");
        }
        
        // 查詢計劃分析
        report.append("=== 常見查詢效能分析 ===\n");
        List<QueryPlanAnalysis> queryAnalyses = analyzeCommonQueryPlans();
        for (QueryPlanAnalysis analysis : queryAnalyses) {
            if (analysis != null) {
                report.append(String.format(
                    "查詢：%s\n" +
                    "  執行時間：%d ms\n" +
                    "  成本：%d\n" +
                    "  全表掃描：%s\n" +
                    "  最佳化建議：\n",
                    analysis.query.length() > 80 ? analysis.query.substring(0, 80) + "..." : analysis.query,
                    analysis.actualExecutionTime,
                    analysis.estimatedCost,
                    analysis.hasFullTableScan ? "是" : "否"
                ));
                
                for (String suggestion : analysis.optimizationSuggestions) {
                    report.append("    - ").append(suggestion).append("\n");
                }
                report.append("\n");
            }
        }
        
        // 分頁最佳化建議
        report.append("=== 分頁查詢最佳化建議 ===\n");
        for (String recommendation : generatePaginationOptimizations()) {
            report.append(recommendation).append("\n");
        }
        report.append("\n");
        
        // 快取策略建議
        report.append("=== 快取策略建議 ===\n");
        for (String strategy : generateCachingStrategies()) {
            report.append(strategy).append("\n");
        }
        report.append("\n");
        
        // 資料分割建議
        report.append("=== 資料分割建議 ===\n");
        Map<String, List<String>> partitioning = analyzePartitioningNeeds();
        for (Map.Entry<String, List<String>> entry : partitioning.entrySet()) {
            report.append(String.format("表格：%s\n", entry.getKey()));
            for (String suggestion : entry.getValue()) {
                report.append("  - ").append(suggestion).append("\n");
            }
            report.append("\n");
        }
        
        // 效能監控建議
        report.append("=== 效能監控建議 ===\n");
        for (String strategy : generatePerformanceMonitoringStrategies()) {
            report.append(strategy).append("\n");
        }
        
        return report.toString();
    }
    
    // === 公開 API ===
    
    /**
     * 執行完整的大資料集分析
     */
    public static String performFullAnalysis() {
        System.out.println("開始大量資料集分析...");
        
        long startTime = System.currentTimeMillis();
        String report = generateComprehensiveReport();
        long duration = System.currentTimeMillis() - startTime;
        
        System.out.printf("分析完成，耗時 %d 毫秒\n", duration);
        
        return report;
    }
}