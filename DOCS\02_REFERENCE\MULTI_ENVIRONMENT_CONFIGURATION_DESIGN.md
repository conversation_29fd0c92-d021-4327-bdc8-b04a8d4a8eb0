# 多環境配置區隔設計方案

## 📋 文件資訊
- **文件編號**: T1.3.3
- **文件目的**: 設計新北市違章建築管理系統的多環境配置管理方案
- **適用對象**: 開發團隊、DevOps 工程師、系統管理員
- **建立日期**: 2025-07-05
- **版本**: 1.0

---

## 🎯 設計目標

1. **安全性提升**: 移除硬編碼的敏感資訊
2. **環境隔離**: 確保開發、測試、生產環境配置互不干擾
3. **配置管理**: 簡化不同環境的部署流程
4. **可維護性**: 集中管理環境相關配置

---

## 🔍 現況分析

### 當前問題

1. **安全風險**
   - 資料庫密碼硬編碼在 `site.properties`
   - 加密金鑰明文儲存
   - 使用 SA 帳號連接 SQL Server

2. **環境混淆**
   - 單一配置檔案用於所有環境
   - 開發環境設定可能誤用於生產環境
   - 無法快速切換環境配置

3. **維護困難**
   - 每次環境變更需手動修改檔案
   - 容易因人為疏失造成配置錯誤
   - 缺乏配置版本控制

---

## 🏗️ 多環境配置架構設計

### 環境定義

| 環境 | 用途 | 配置檔案 | 環境變數前綴 |
|------|------|----------|--------------|
| dev | 本地開發 | site-dev.properties | DEV_ |
| test | 測試環境 | site-test.properties | TEST_ |
| prod | 生產環境 | site-prod.properties | PROD_ |

### 配置檔案結構

```
src/
├── WEB-INF/
│   ├── site.properties.template    # 配置模板（提交到 Git）
│   ├── config/
│   │   ├── dev/
│   │   │   └── site.properties    # 開發環境配置（不提交）
│   │   ├── test/
│   │   │   └── site.properties    # 測試環境配置（不提交）
│   │   └── prod/
│   │       └── site.properties    # 生產環境配置（不提交）
│   └── lib/
│       └── config-loader.jar      # 配置載入器
```

---

## 📝 配置模板設計

### site.properties.template

```properties
# ===== 資料庫配置 =====
# 主要資料庫 (PostgreSQL)
DBConn.name=DBConn
DBConn.url=${DB_PRIMARY_URL:************************************}
DBConn.driver=org.postgresql.Driver
DBConn.user=${DB_PRIMARY_USER:postgres}
DBConn.password=${DB_PRIMARY_PASSWORD}
DBConn.maxconn=${DB_PRIMARY_MAX_CONN:20}
DBConn.timeout=${DB_PRIMARY_TIMEOUT:300}
DBConn.cachesize=100

# 次要資料庫 (SQL Server)
DBConn2.name=DBConn2
DBConn2.url=${DB_SECONDARY_URL}
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.user=${DB_SECONDARY_USER}
DBConn2.password=${DB_SECONDARY_PASSWORD}
DBConn2.maxconn=${DB_SECONDARY_MAX_CONN:10}
DBConn2.timeout=${DB_SECONDARY_TIMEOUT:300}
DBConn2.cachesize=100

# ===== 應用程式設定 =====
serverUrl=${APP_SERVER_URL:http://localhost:8080/bms}
third.party.service.base.url=${THIRD_PARTY_SERVICE_URL}
google.maps.service.url=${GOOGLE_MAPS_SERVICE_URL:maps/index}

# ===== 安全性設定 =====
security.encryption.key=${ENCRYPTION_KEY}
security.auth.cookie.name=${AUTH_COOKIE_NAME:bmsLogin}
security.auth.cookie.expire=${AUTH_COOKIE_EXPIRE:30}
security.hash.algorithm=${HASH_ALGORITHM:SHA-256}

# ===== 日誌設定 =====
log.level=${LOG_LEVEL:info}
log.file=${LOG_FILE:logs/bms.log}
log.max.size=${LOG_MAX_SIZE:10MB}
log.backup.count=${LOG_BACKUP_COUNT:5}

# ===== 系統設定 =====
system.locale=${SYSTEM_LOCALE:zh_TW}
system.encoding=${SYSTEM_ENCODING:UTF-8}
system.timezone=${SYSTEM_TIMEZONE:Asia/Taipei}
system.date.format=${DATE_FORMAT:yyyy-MM-dd}

# ===== 效能設定 =====
cache.enabled=${CACHE_ENABLED:true}
cache.size=${CACHE_SIZE:1000}
session.timeout=${SESSION_TIMEOUT:30}
upload.max.size=${UPLOAD_MAX_SIZE:10MB}
```

---

## 🚀 環境特定配置

### 開發環境 (dev)

```properties
# config/dev/site.properties
# 開發環境專用設定
DBConn.url=************************************_dev
DBConn.maxconn=10
DBConn.timeout=60

log.level=debug
log.file=logs/bms-dev.log

cache.enabled=false
serverUrl=http://localhost:8080/bms-dev
```

### 測試環境 (test)

```properties
# config/test/site.properties
# 測試環境專用設定
DBConn.url=***************************************************
DBConn.maxconn=50
DBConn.timeout=300

log.level=info
log.file=logs/bms-test.log

cache.enabled=true
serverUrl=https://test-bms.ntpc.gov.tw
```

### 生產環境 (prod)

```properties
# config/prod/site.properties
# 生產環境專用設定
DBConn.url=***********************************************
DBConn.maxconn=100
DBConn.timeout=600

log.level=warn
log.file=/var/log/bms/bms-prod.log

cache.enabled=true
cache.size=5000
serverUrl=https://bms.ntpc.gov.tw
```

---

## 🔧 實作方案

### 1. 環境變數載入器

創建 `EnvironmentConfigLoader.java`:

```java
package com.ntpc.bms.config;

import java.io.*;
import java.util.Properties;

public class EnvironmentConfigLoader {
    private static final String ENV_PROPERTY = "app.env";
    private static final String DEFAULT_ENV = "dev";
    
    public static Properties loadConfiguration() {
        String environment = System.getProperty(ENV_PROPERTY, DEFAULT_ENV);
        Properties props = new Properties();
        
        try {
            // 載入基礎模板
            loadTemplate(props);
            
            // 載入環境特定配置
            loadEnvironmentConfig(props, environment);
            
            // 替換環境變數
            replaceEnvironmentVariables(props);
            
        } catch (IOException e) {
            throw new RuntimeException("無法載入配置檔案", e);
        }
        
        return props;
    }
    
    private static void replaceEnvironmentVariables(Properties props) {
        props.forEach((key, value) -> {
            String strValue = value.toString();
            if (strValue.contains("${")) {
                props.setProperty(key.toString(), 
                    resolveEnvironmentVariables(strValue));
            }
        });
    }
}
```

### 2. Tomcat 環境變數設定

#### Windows (setenv.bat)
```batch
@echo off
rem 環境設定
set APP_ENV=dev

rem 資料庫配置
set DB_PRIMARY_URL=************************************_dev
set DB_PRIMARY_USER=postgres
set DB_PRIMARY_PASSWORD=your_secure_password_here

rem 安全性設定
set ENCRYPTION_KEY=your_encryption_key_here

rem Java 選項
set JAVA_OPTS=-Dapp.env=%APP_ENV% %JAVA_OPTS%
```

#### Linux (setenv.sh)
```bash
#!/bin/sh
# 環境設定
export APP_ENV=prod

# 資料庫配置
export DB_PRIMARY_URL=**********************************************
export DB_PRIMARY_USER=bms_app
export DB_PRIMARY_PASSWORD=${VAULT_DB_PASSWORD}

# 安全性設定
export ENCRYPTION_KEY=${VAULT_ENCRYPTION_KEY}

# Java 選項
export JAVA_OPTS="-Dapp.env=$APP_ENV $JAVA_OPTS"
```

### 3. Web.xml 配置

```xml
<context-param>
    <param-name>configLocation</param-name>
    <param-value>/WEB-INF/config/${app.env}/site.properties</param-value>
</context-param>

<listener>
    <listener-class>com.ntpc.bms.config.ConfigurationListener</listener-class>
</listener>
```

---

## 🔐 敏感資訊管理

### 1. 密碼管理策略

**開發環境**
- 使用預設測試密碼
- 可寫入 `.env` 檔案（不提交）

**測試環境**
- 使用環境變數
- 由 CI/CD 系統注入

**生產環境**
- 使用密碼管理系統（如 HashiCorp Vault）
- 執行時動態取得
- 定期輪換

### 2. 加密金鑰管理

```java
// 金鑰管理範例
public class KeyManager {
    public static String getEncryptionKey() {
        // 優先從密碼管理系統取得
        String key = VaultClient.getSecret("bms/encryption-key");
        
        if (key == null) {
            // 退回到環境變數
            key = System.getenv("ENCRYPTION_KEY");
        }
        
        if (key == null) {
            throw new SecurityException("未設定加密金鑰");
        }
        
        return key;
    }
}
```

---

## 🚀 部署流程

### 1. 開發環境部署

```bash
# 1. 設定環境變數
export APP_ENV=dev
export DB_PRIMARY_PASSWORD=dev_password

# 2. 啟動 Tomcat
./catalina.sh start
```

### 2. 測試環境部署 (CI/CD)

```yaml
# .gitlab-ci.yml 範例
deploy-test:
  stage: deploy
  variables:
    APP_ENV: test
    DB_PRIMARY_URL: $TEST_DB_URL
    DB_PRIMARY_PASSWORD: $TEST_DB_PASSWORD
  script:
    - ./deploy.sh test
```

### 3. 生產環境部署

```bash
# 使用配置管理工具（如 Ansible）
ansible-playbook -i production deploy-bms.yml \
  --extra-vars "env=prod vault_password=$VAULT_PASSWORD"
```

---

## 📊 配置驗證

### 啟動時檢查

```java
public class ConfigurationValidator {
    public static void validate(Properties config) {
        // 必要配置檢查
        checkRequired(config, "DBConn.url", "主要資料庫URL");
        checkRequired(config, "DBConn.password", "主要資料庫密碼");
        
        // 格式驗證
        validateUrl(config.getProperty("DBConn.url"));
        validateUrl(config.getProperty("serverUrl"));
        
        // 連線測試
        testDatabaseConnection(config);
    }
}
```

### 健康檢查端點

```java
@WebServlet("/health/config")
public class ConfigHealthServlet extends HttpServlet {
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) {
        JSONObject status = new JSONObject();
        status.put("environment", System.getProperty("app.env"));
        status.put("dbConnection", testDbConnection());
        status.put("configLoaded", ConfigManager.isLoaded());
        
        resp.setContentType("application/json");
        resp.getWriter().write(status.toString());
    }
}
```

---

## 📋 遷移計畫

### 第一階段：準備工作（1週）
1. 創建配置模板檔案
2. 開發配置載入器
3. 準備環境變數文件

### 第二階段：開發環境測試（1週）
1. 在開發環境實施新配置
2. 測試所有功能
3. 調整配置參數

### 第三階段：測試環境部署（2週）
1. 部署到測試環境
2. 執行完整測試
3. 效能調校

### 第四階段：生產環境遷移（1週）
1. 制定詳細遷移計畫
2. 執行生產環境部署
3. 監控和驗證

---

## ⚠️ 注意事項

1. **向下相容性**
   - 保留原始 `site.properties` 作為備援
   - 逐步遷移，避免大規模變更

2. **安全性考量**
   - 絕不將密碼寫入配置檔案
   - 使用強加密保護敏感資訊
   - 定期審查和輪換密碼

3. **監控和日誌**
   - 記錄配置載入過程
   - 監控配置變更
   - 建立配置審計軌跡

---

## 📞 技術支援

如需協助，請聯繫：
- DevOps 團隊：<EMAIL>
- 系統架構師：<EMAIL>

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*任務編號：T1.3.3*
*完成日期：2025-07-05*