package com.ntpc.violation.diagnostics.cli;

import com.ntpc.violation.diagnostics.core.*;
import com.ntpc.violation.diagnostics.analyzers.*;
import com.ntpc.violation.diagnostics.reporters.*;
import com.ntpc.violation.diagnostics.interfaces.*;

import java.io.*;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.*;

/**
 * 診斷工具命令列介面
 */
public class DiagnosticsCLI {
    
    private static final Logger logger = Logger.getLogger(DiagnosticsCLI.class.getName());
    private static final String VERSION = "1.0.0";
    
    private DiagnosticEngine engine;
    private DiagnosticConfiguration config;
    private CommandLineOptions options;
    
    public static void main(String[] args) {
        DiagnosticsCLI cli = new DiagnosticsCLI();
        
        try {
            cli.parseArguments(args);
            cli.initialize();
            cli.execute();
        } catch (Exception e) {
            System.err.println("診斷工具執行失敗: " + e.getMessage());
            if (cli.options != null && cli.options.isVerbose()) {
                e.printStackTrace();
            }
            System.exit(1);
        }
    }
    
    /**
     * 解析命令列參數
     */
    private void parseArguments(String[] args) {
        options = new CommandLineOptions();
        
        for (int i = 0; i < args.length; i++) {
            String arg = args[i];
            
            switch (arg) {
                case "--help":
                case "-h":
                    printHelp();
                    System.exit(0);
                    break;
                    
                case "--version":
                case "-v":
                    System.out.println("Health Diagnostics Tool v" + VERSION);
                    System.exit(0);
                    break;
                    
                case "--quick-check":
                    options.setMode(DiagnosticEngine.DiagnosticMode.QUICK_CHECK);
                    break;
                    
                case "--full-scan":
                    options.setMode(DiagnosticEngine.DiagnosticMode.FULL_SCAN);
                    break;
                    
                case "--performance-analysis":
                    options.setMode(DiagnosticEngine.DiagnosticMode.PERFORMANCE_ANALYSIS);
                    break;
                    
                case "--security-scan":
                    options.setMode(DiagnosticEngine.DiagnosticMode.SECURITY_SCAN);
                    break;
                    
                case "--output-format":
                    if (i + 1 < args.length) {
                        options.setOutputFormats(args[++i].split(","));
                    }
                    break;
                    
                case "--output-dir":
                    if (i + 1 < args.length) {
                        options.setOutputDir(args[++i]);
                    }
                    break;
                    
                case "--config":
                    if (i + 1 < args.length) {
                        options.setConfigFile(args[++i]);
                    }
                    break;
                    
                case "--silent":
                    options.setSilent(true);
                    break;
                    
                case "--verbose":
                    options.setVerbose(true);
                    break;
                    
                case "--auto-fix":
                    options.setAutoFix(true);
                    break;
                    
                case "--no-color":
                    options.setNoColor(true);
                    break;
                    
                default:
                    System.err.println("未知參數: " + arg);
                    printHelp();
                    System.exit(1);
            }
        }
    }
    
    /**
     * 初始化診斷引擎
     */
    private void initialize() throws Exception {
        // 設定日誌
        configureLogging();
        
        // 載入配置
        config = DiagnosticConfiguration.getInstance();
        if (options.getConfigFile() != null) {
            // 從指定的配置檔案載入
            logger.info("載入配置檔案: " + options.getConfigFile());
        }
        
        // 建立診斷引擎
        engine = new DiagnosticEngine();
        engine.setMode(options.getMode());
        
        // 註冊所有分析器
        registerAnalyzers();
        
        if (!options.isSilent()) {
            printBanner();
            System.out.println("診斷模式: " + options.getMode().getDescription());
            System.out.println("輸出目錄: " + options.getOutputDir());
            System.out.println("輸出格式: " + String.join(", ", options.getOutputFormats()));
            System.out.println();
        }
    }
    
    /**
     * 註冊分析器
     */
    private void registerAnalyzers() {
        // 根據模式註冊相應的分析器
        engine.registerAnalyzer(new SystemScanner());
        engine.registerAnalyzer(new PerformanceAnalyzer());
        
        // 這裡可以動態載入擴充分析器
        loadExtensionAnalyzers();
    }
    
    /**
     * 載入擴充分析器
     */
    private void loadExtensionAnalyzers() {
        Path extensionsDir = Paths.get("lib", "extensions");
        if (Files.exists(extensionsDir)) {
            // 動態載入擴充分析器
            logger.info("載入擴充分析器從: " + extensionsDir);
        }
    }
    
    /**
     * 執行診斷
     */
    private void execute() throws Exception {
        if (!options.isSilent()) {
            System.out.println("開始執行診斷...\n");
        }
        
        long startTime = System.currentTimeMillis();
        
        // 執行診斷
        DiagnosticReport report = engine.execute();
        
        long executionTime = System.currentTimeMillis() - startTime;
        
        if (!options.isSilent()) {
            // 顯示診斷結果摘要
            displaySummary(report);
            
            System.out.println("\n總執行時間: " + formatDuration(executionTime));
        }
        
        // 生成報告
        generateReports(report);
        
        // 執行自動修復
        if (options.isAutoFix() && report.getTotalIssues() > 0) {
            performAutoFix(report);
        }
        
        // 根據結果設定退出碼
        int exitCode = determineExitCode(report);
        
        if (!options.isSilent()) {
            System.out.println("\n診斷完成。");
        }
        
        // 清理資源
        engine.shutdown();
        
        System.exit(exitCode);
    }
    
    /**
     * 顯示診斷摘要
     */
    private void displaySummary(DiagnosticReport report) {
        // 健康狀態
        DiagnosticResult.HealthStatus status = report.getOverallStatus();
        String statusDisplay = formatStatus(status);
        System.out.println("整體健康狀態: " + statusDisplay);
        
        // 問題統計
        if (report.getTotalIssues() > 0) {
            System.out.println("\n發現問題總數: " + report.getTotalIssues());
            
            // 按嚴重性顯示
            System.out.println("\n問題嚴重性分布:");
            for (Map.Entry<DiagnosticIssue.IssueSeverity, Integer> entry : 
                 report.getIssuesBySeverity().entrySet()) {
                System.out.println("  " + formatSeverity(entry.getKey()) + 
                                  ": " + entry.getValue());
            }
            
            // 按類別顯示
            System.out.println("\n問題類別分布:");
            for (Map.Entry<DiagnosticIssue.IssueCategory, Integer> entry : 
                 report.getIssuesByCategory().entrySet()) {
                System.out.println("  " + entry.getKey().getDescription() + 
                                  ": " + entry.getValue());
            }
            
            // 顯示關鍵問題
            List<DiagnosticIssue> criticalIssues = report.getCriticalIssues(5);
            if (!criticalIssues.isEmpty()) {
                System.out.println("\n關鍵問題:");
                for (DiagnosticIssue issue : criticalIssues) {
                    System.out.println("  - [" + issue.getId() + "] " + 
                                      issue.getTitle());
                }
            }
        } else {
            System.out.println("\n未發現任何問題。系統運行正常！");
        }
    }
    
    /**
     * 生成報告
     */
    private void generateReports(DiagnosticReport report) throws Exception {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        Path outputDir = Paths.get(options.getOutputDir());
        Files.createDirectories(outputDir);
        
        for (String format : options.getOutputFormats()) {
            if (!options.isSilent()) {
                System.out.println("\n生成 " + format + " 報告...");
            }
            
            switch (format.toUpperCase()) {
                case "HTML":
                    HtmlReportGenerator htmlGen = new HtmlReportGenerator();
                    Path htmlFile = outputDir.resolve("diagnostic_report_" + timestamp + ".html");
                    htmlGen.generate(report, htmlFile);
                    if (!options.isSilent()) {
                        System.out.println("HTML 報告已生成: " + htmlFile);
                    }
                    break;
                    
                case "PDF":
                    PdfReportGenerator pdfGen = new PdfReportGenerator();
                    Path pdfFile = outputDir.resolve("diagnostic_report_" + timestamp + ".pdf");
                    pdfGen.generate(report, pdfFile);
                    if (!options.isSilent()) {
                        System.out.println("PDF 報告已生成: " + pdfFile);
                    }
                    break;
                    
                case "JSON":
                    JsonReportGenerator jsonGen = new JsonReportGenerator();
                    Path jsonFile = outputDir.resolve("diagnostic_report_" + timestamp + ".json");
                    jsonGen.generate(report, jsonFile);
                    if (!options.isSilent()) {
                        System.out.println("JSON 報告已生成: " + jsonFile);
                    }
                    break;
                    
                case "CSV":
                    CsvReportGenerator csvGen = new CsvReportGenerator();
                    Path csvFile = outputDir.resolve("diagnostic_report_" + timestamp + ".csv");
                    csvGen.generate(report, csvFile);
                    if (!options.isSilent()) {
                        System.out.println("CSV 報告已生成: " + csvFile);
                    }
                    break;
                    
                default:
                    logger.warning("不支援的報告格式: " + format);
            }
        }
    }
    
    /**
     * 執行自動修復
     */
    private void performAutoFix(DiagnosticReport report) {
        if (!options.isSilent()) {
            System.out.println("\n執行自動修復...");
        }
        
        List<DiagnosticIssue> fixableIssues = report.getAutoFixableIssues();
        
        if (fixableIssues.isEmpty()) {
            if (!options.isSilent()) {
                System.out.println("沒有可自動修復的問題。");
            }
            return;
        }
        
        if (!options.isSilent()) {
            System.out.println("發現 " + fixableIssues.size() + " 個可自動修復的問題。");
        }
        
        // 確認修復
        if (!options.isAutoConfirm() && !options.isSilent()) {
            System.out.print("是否執行自動修復？ (y/N): ");
            Scanner scanner = new Scanner(System.in);
            String response = scanner.nextLine().trim().toLowerCase();
            
            if (!response.equals("y") && !response.equals("yes")) {
                System.out.println("取消自動修復。");
                return;
            }
        }
        
        // 執行修復
        List<FixResult> fixResults = engine.autoFix(fixableIssues);
        
        // 顯示修復結果
        if (!options.isSilent()) {
            System.out.println("\n修復結果:");
            int successCount = 0;
            
            for (FixResult result : fixResults) {
                if (result.getStatus() == FixResult.FixStatus.SUCCESS) {
                    successCount++;
                }
                System.out.println("  - " + result.getSummary() + 
                                  " [" + result.getStatus().getDescription() + "]");
            }
            
            System.out.println("\n成功修復: " + successCount + "/" + fixResults.size());
        }
    }
    
    /**
     * 決定退出碼
     */
    private int determineExitCode(DiagnosticReport report) {
        switch (report.getOverallStatus()) {
            case HEALTHY:
                return 0;
            case WARNING:
                return 1;
            case CRITICAL:
                return 2;
            case ERROR:
                return 3;
            default:
                return 4;
        }
    }
    
    /**
     * 格式化狀態顯示
     */
    private String formatStatus(DiagnosticResult.HealthStatus status) {
        if (options.isNoColor()) {
            return status.getDescription();
        }
        
        // ANSI 顏色碼
        switch (status) {
            case HEALTHY:
                return "\033[32m" + status.getDescription() + "\033[0m"; // 綠色
            case WARNING:
                return "\033[33m" + status.getDescription() + "\033[0m"; // 黃色
            case CRITICAL:
                return "\033[31m" + status.getDescription() + "\033[0m"; // 紅色
            case ERROR:
                return "\033[35m" + status.getDescription() + "\033[0m"; // 紫色
            default:
                return status.getDescription();
        }
    }
    
    /**
     * 格式化嚴重性顯示
     */
    private String formatSeverity(DiagnosticIssue.IssueSeverity severity) {
        if (options.isNoColor()) {
            return severity.getDescription();
        }
        
        switch (severity) {
            case LOW:
                return "\033[36m" + severity.getDescription() + "\033[0m"; // 青色
            case MEDIUM:
                return "\033[33m" + severity.getDescription() + "\033[0m"; // 黃色
            case HIGH:
                return "\033[31m" + severity.getDescription() + "\033[0m"; // 紅色
            case CRITICAL:
                return "\033[35m" + severity.getDescription() + "\033[0m"; // 紫色
            default:
                return severity.getDescription();
        }
    }
    
    /**
     * 格式化時間長度
     */
    private String formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d 分 %d 秒", minutes, seconds);
        } else {
            return String.format("%d 秒", seconds);
        }
    }
    
    /**
     * 設定日誌
     */
    private void configureLogging() {
        LogManager.getLogManager().reset();
        
        if (options.isSilent()) {
            Logger.getGlobal().setLevel(Level.OFF);
        } else if (options.isVerbose()) {
            Logger.getGlobal().setLevel(Level.ALL);
            ConsoleHandler consoleHandler = new ConsoleHandler();
            consoleHandler.setLevel(Level.ALL);
            Logger.getGlobal().addHandler(consoleHandler);
        } else {
            Logger.getGlobal().setLevel(Level.INFO);
        }
    }
    
    /**
     * 顯示橫幅
     */
    private void printBanner() {
        System.out.println("╔══════════════════════════════════════════════╗");
        System.out.println("║     Health Diagnostics Tool v" + VERSION + "           ║");
        System.out.println("║     新北市違章建築管理系統診斷工具          ║");
        System.out.println("╚══════════════════════════════════════════════╝");
        System.out.println();
    }
    
    /**
     * 顯示說明
     */
    private void printHelp() {
        System.out.println("使用方式: diagnostics.sh [選項]");
        System.out.println();
        System.out.println("診斷模式:");
        System.out.println("  --quick-check           執行快速健康檢查");
        System.out.println("  --full-scan            執行完整系統診斷（預設）");
        System.out.println("  --performance-analysis  執行效能分析");
        System.out.println("  --security-scan        執行安全掃描");
        System.out.println();
        System.out.println("輸出選項:");
        System.out.println("  --output-format <格式>  指定輸出格式（HTML,PDF,JSON,CSV）");
        System.out.println("  --output-dir <目錄>     指定輸出目錄（預設: ./reports）");
        System.out.println();
        System.out.println("其他選項:");
        System.out.println("  --config <檔案>         指定配置檔案");
        System.out.println("  --auto-fix             自動修復發現的問題");
        System.out.println("  --silent               靜默模式");
        System.out.println("  --verbose              詳細輸出");
        System.out.println("  --no-color             停用彩色輸出");
        System.out.println("  -h, --help             顯示此說明");
        System.out.println("  -v, --version          顯示版本資訊");
        System.out.println();
        System.out.println("範例:");
        System.out.println("  diagnostics.sh --quick-check");
        System.out.println("  diagnostics.sh --full-scan --output-format=HTML,PDF");
        System.out.println("  diagnostics.sh --performance-analysis --auto-fix");
    }
    
    /**
     * 命令列選項類別
     */
    private static class CommandLineOptions {
        private DiagnosticEngine.DiagnosticMode mode = DiagnosticEngine.DiagnosticMode.FULL_SCAN;
        private String[] outputFormats = {"HTML"};
        private String outputDir = "./reports";
        private String configFile = null;
        private boolean silent = false;
        private boolean verbose = false;
        private boolean autoFix = false;
        private boolean autoConfirm = false;
        private boolean noColor = false;
        
        // Getters and Setters
        public DiagnosticEngine.DiagnosticMode getMode() {
            return mode;
        }
        
        public void setMode(DiagnosticEngine.DiagnosticMode mode) {
            this.mode = mode;
        }
        
        public String[] getOutputFormats() {
            return outputFormats;
        }
        
        public void setOutputFormats(String[] outputFormats) {
            this.outputFormats = outputFormats;
        }
        
        public String getOutputDir() {
            return outputDir;
        }
        
        public void setOutputDir(String outputDir) {
            this.outputDir = outputDir;
        }
        
        public String getConfigFile() {
            return configFile;
        }
        
        public void setConfigFile(String configFile) {
            this.configFile = configFile;
        }
        
        public boolean isSilent() {
            return silent;
        }
        
        public void setSilent(boolean silent) {
            this.silent = silent;
        }
        
        public boolean isVerbose() {
            return verbose;
        }
        
        public void setVerbose(boolean verbose) {
            this.verbose = verbose;
        }
        
        public boolean isAutoFix() {
            return autoFix;
        }
        
        public void setAutoFix(boolean autoFix) {
            this.autoFix = autoFix;
        }
        
        public boolean isAutoConfirm() {
            return autoConfirm;
        }
        
        public void setAutoConfirm(boolean autoConfirm) {
            this.autoConfirm = autoConfirm;
        }
        
        public boolean isNoColor() {
            return noColor;
        }
        
        public void setNoColor(boolean noColor) {
            this.noColor = noColor;
        }
    }
}