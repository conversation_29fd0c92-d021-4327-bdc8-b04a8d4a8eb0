.addr_div_2, .addr_div_3{padding-top:6px;}
.imgUrlOutput{ border: 1px solid #555;border-radius: 2px;max-height:400px; max-width: 533px;}
.tittle_left{float:left;text-align:left;}
.tittle_center{float:left;text-align:center;font-size:26px;width:49%;WHITE-SPACE: nowrap;font-weight:700;}
.tittle_right{float:right;text-align:right;}
.bridge{width:25%;WHITE-SPACE: nowrap;margin-top:25px; }
.vertical-container {
 /* height: 300px;*/
  display: -webkit-flex;
  display:         flex;
  -webkit-align-items: center;
          align-items: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.table-bordered>tbody>tr>td{border: 1px solid #a5a5a5;}

.displayNone{display:none;}
.GOVER_NUM{text-align:right;}
.GOVER_WORD{width:96px;margin-left: 20px;text-align:right;}
.textRight{text-align:right;}
#Bottom_td{/*border: 1px solid #fcfcfc;*/text-align:center;padding-top: 10px; padding-bottom: 30px;}
#Top_td{border-top: 0;}
.btn-contain{position: relative;}
.btn-left{position: absolute; left:10px;width: 202px; bottom: 29px;}
.btn-left-bottom{position: absolute; left:-54px;width: 200px;}
.btn-center{position: absolute; width: 200px;left: 45%;}
.btn-right-edit{position: absolute; right:-29px;width: 224px; bottom: 29px;}
.btn-right-insert{position: absolute; right:0px;width: 317px; bottom: 29px;}
.sub-tittle{padding-top: 10px;font-weight:700;}
input{ color: black;}
::placeholder {
  color:#bbb;
    opacity: 1; /* Firefox */
}
:-ms-input-placeholder { /* Internet Explorer 10-11 */
   color: #bbb;
}
::-ms-input-placeholder { /* Microsoft Edge */
   color: #bbb;
}
.addr_textbox{width: 50px;text-align:right;}
.img_add{width:28px;height:28px;cursor:pointer;}
.rlt_div{padding-left: 14px; padding-bottom: 5px;}

.land_top{
	border-top: solid 1px #888;
    margin-top: 15px;
    padding-top: 10px;
	
}

.topbtn-right{position: absolute; right:4px;width: 206px; top:36px;}

.th {
    background-color: #e6e6e6;
}

.nav-tabs > li.active > a{
	background-color: #fcfcfc !important;
}

.nav-tabs > li > a {
    background-color: #c5c5c5;
}

.nav-tabs > li {
    margin-left: 10px;
}

.td-content-required {
	background-color: #fff2f4;
}

.tab-content {
	padding: 10px 14px 30px 14px;
    background-color: #fcfcfc;
	border: 1px solid #ddd;
	border-top: 0px;
}
.tab-content-submit {
	padding: 10px 14px 10px 14px;
    background-color: #fcfcfc;
	border: 1px solid #ddd;
	
}
.td-content {
	/*background-color: #f2f2f2;*/
}

.container {
	/*background-color: #fcfcfc;*/
}