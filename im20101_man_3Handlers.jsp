<%@page pageEncoding="utf-8"%><%@page import="com.ezek.utils.EzekUtils"%>

<%--== Handlers ==--%> <%--im20101_man_3 Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

private final String CONNECTION_NAME = "DBConn";

private String getUnpermittedStructureWords(String code) {
	String unpermittedStructureWords = "";
	
	if (!StringUtils.isEmpty(code)) {
		if (code.equals("A")) {
			unpermittedStructureWords = "一般";
		} else if (code.equals("B")) {
			unpermittedStructureWords = "廣告";
		} else if (code.equals("C")) {
			unpermittedStructureWords = "下水道";
		}
		unpermittedStructureWords += "違建";
	}
	
	return unpermittedStructureWords;
}

private String formatLandNumber(String rawStr) {
	String html = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		String[] rawStrArray = rawStr.split(";");
		int landNumberCount = 0;
		
		for (String landNumber : rawStrArray) {
			if (landNumberCount == 0) {
				html = landNumber;
			}
			
			landNumberCount++;
		}
		
		if (landNumberCount > 1) {
			html += " 等 " + landNumberCount + " 筆";
		}
		
		// Release the memory resource immediately
		rawStrArray = null;
	}
	
	return html;
}

private String getViolatorInfo(String case_id) {
	String result = "無";
	
	JDBCConnection jdbcConn = null;
	Enumeration dataRows = null;
	DbRow dataRow = null;
	
	String sql = "";
	String usr_knd = "", ib_user = "", usr_id = "", usr_add = "";
	String usr_knd_name = "";
	StringBuffer html = null;
	
	try {
		// Obtain a database connection from the pool
		jdbcConn = JDBCConnectionFactory.getJDBCConnection(CONNECTION_NAME);
		
		html = new StringBuffer();
		
		sql = "SELECT usr_knd, ib_user, usr_id, usr_add";
		sql += " FROM ibmdisnm";
		sql += " WHERE case_id = '" + case_id + "'";
		sql += " ORDER BY case_seq";
		
		dataRows = jdbcConn.getRows(sql);
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			usr_knd = Utils.convertToString(dataRow.get("usr_knd"));
			ib_user = Utils.convertToString(dataRow.get("ib_user"));
			usr_id = Utils.convertToString(dataRow.get("usr_id"));
			usr_add = Utils.convertToString(dataRow.get("usr_add"));
			
			if ("1".equals(usr_knd)) {
				usr_knd_name = "自然人";
			} else if ("2".equals(usr_knd)) {
				usr_knd_name = "法人";
			}
			ib_user = (StringUtils.isEmpty(ib_user) ? "" : ib_user);
			usr_id = (StringUtils.isEmpty(usr_id) ? "" : usr_id);
			usr_add = (StringUtils.isEmpty(usr_add) ? "" : usr_add);
			
			// Row 1
			html.append("<tr class=\"Controls\">");
			html.append("<td style=\"vertical-align: middle; padding-right: 0px; border-top: 1px solid #ddd;\"><label>違建人身分</label>&nbsp;</td>");
			html.append("<td colspan=\"5\"><label>").append(usr_knd_name).append("</label></td>");
			html.append("</tr>");
			// Row 2
			html.append("<tr class=\"Controls\">");
			html.append("<td class=\"th_topCntnt col-1\"><label>違建人姓名</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt col-2\"><label>").append(ib_user).append("</label></td>");
			html.append("<td class=\"th_topCntnt col-3\" style=\"WHITE-SPACE: nowrap;\"><label>違建人身分號碼</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt col-4\"><label>").append(usr_id).append("</label></td>");
			html.append("<td class=\"th_topCntnt col-5\" ><label>違建人地址</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt\"><label>").append(usr_add).append("</label></td>");
			html.append("</tr>");
			
			// Reset for the next iteration
			usr_knd_name = "";
		}
	} catch (Exception e) {
		System.err.println("im20101_man_3Handlers ::: getViolatorsInfo ::: Exception");
		e.printStackTrace();
	} finally {
		// Release the database connection back to the pool
		if (jdbcConn != null) {
			jdbcConn.closeConnection();
		}
		
		if (html.length() > 0) {
			result = "<a class=\"link-switch\" id=\"violator_table_switch\" data-toggle=\"collapse\" href=\"#violator_content\" aria-expanded=\"false\">瀏覽</a>";
			result += "<div id=\"violator_content\" class=\"collapse violator-content blend-in-bg\">";
			result += "<table class=\"table violatorTable\" cellspacing=\"0\" cellpadding=\"0\">" + html.toString() + "</table>";
			result += "</div>";
		}
		
		// Release the memory resource immediately
		html = null;
	}
	
	return result;
}

private HashMap<String, String> getFiles(String case_id) {
	HashMap<String, String> result = null;
	
	JDBCConnection jdbcConn = null;
	Enumeration dataRows = null;
	DbRow dataRow = null;
	
	String sql = "";
	String laborlaw_case_id = "", laborlaw_pic_kind = "", laborlaw_pic_seq = "", laborlaw_picname = "", laborlaw_filename = "", laborlaw_showname = "";
	String pic_kind = "", pic_seq = "", picname = "", filename = "", showname = "";
	String extract_picname = "", fileExtention = "";
	boolean isImage = false;
	
	StringBuffer case_photo = null, case_attm = null, case_laborlaw = null, result_photo = null, result_attm = null;
	
	try {
		result = new HashMap<String, String>();
		// 案件基本資料
		result.put("case_photo", "無");
		result.put("case_attm", "無");
		result.put("case_laborlaw", "無");
		// 拆除結果
		result.put("result_photo", "無");
		result.put("result_attm", "無");
		
		case_photo = new StringBuffer();
		case_attm = new StringBuffer();
		case_laborlaw = new StringBuffer();
		result_photo = new StringBuffer();
		result_attm = new StringBuffer();
		
		// Obtain a database connection from the pool
		jdbcConn = JDBCConnectionFactory.getJDBCConnection(CONNECTION_NAME);
		
		sql = "SELECT F.* FROM ibmlaborlaw_case AS C";
		sql += " INNER JOIN ibmlaborlaw_file AS F ON C.ibmlaborlaw_case_id = F.case_id";
		sql += " WHERE C.ibmcase_case_id = '" + case_id + "'";
		
		dataRows = jdbcConn.getRows(sql);
		while (dataRows != null && dataRows.hasMoreElements())
		{
			dataRow = (DbRow)dataRows.nextElement();
			
			laborlaw_case_id = Utils.convertToString(dataRow.get("case_id"));
			laborlaw_pic_kind = Utils.convertToString(dataRow.get("pic_kind"));
			laborlaw_pic_seq = Utils.convertToString(dataRow.get("pic_seq"));
			laborlaw_picname = Utils.convertToString(dataRow.get("picname"));
			laborlaw_filename = Utils.convertToString(dataRow.get("filename"));
			laborlaw_showname = Utils.convertToString(dataRow.get("showname"));
			
			fileExtention = laborlaw_filename.substring(laborlaw_filename.lastIndexOf(".") + 1);
			
			if ("jpg,png,jpeg".indexOf(fileExtention.toLowerCase()) > -1) {
				isImage = true;
			}
			
			case_laborlaw.append(composeHTML_Laborlaw(laborlaw_case_id, laborlaw_pic_kind, laborlaw_pic_seq, isImage, laborlaw_showname));
			
			fileExtention = "";
			isImage = false;
		}
		
		
		sql = "SELECT pic_kind, pic_seq, picname, filename, showname";
		sql += " FROM ibmlist";
		sql += " WHERE systid = 'IBM'";
		sql += " AND case_id = '" + case_id + "'";
		sql += " ORDER BY pic_kind, pic_seq";
		
		dataRows = jdbcConn.getRows(sql);
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			pic_kind = Utils.convertToString(dataRow.get("pic_kind"));
			pic_seq = Utils.convertToString(dataRow.get("pic_seq"));
			picname = Utils.convertToString(dataRow.get("picname"));
			filename = Utils.convertToString(dataRow.get("filename"));
			showname = Utils.convertToString(dataRow.get("showname"));
			
			// Extract the name after "_"
			extract_picname = picname.substring(picname.lastIndexOf("_") + 1);
			// Find the file extention
			fileExtention = filename.substring(filename.lastIndexOf(".") + 1);
			if ("jpg,png,jpeg".indexOf(fileExtention.toLowerCase()) > -1) {
				isImage = true;
			}
			
			if ("SKC,NOW,ILGPIC,STR,GEO,PAT".indexOf(pic_kind) > -1) {
				case_photo.append( composeHTML_II(case_id, pic_kind, pic_seq, isImage, extract_picname) );
			} else if ("ANO,ANO_TG,ANO_SD".indexOf(pic_kind) > -1) {
				case_attm.append( composeHTML(case_id, pic_kind, pic_seq, isImage, showname) );
			} else if (pic_kind.equals("NOW_END")) {
				result_photo.append( composeHTML_II(case_id, pic_kind, pic_seq, isImage, extract_picname) );
			} else if (pic_kind.equals("ANO_END")) {
				result_attm.append( composeHTML(case_id, pic_kind, pic_seq, isImage, showname) );
			}
			
			// Reset for the next iteration
			extract_picname = "";
			fileExtention = "";
			isImage = false;
		}
	} catch (Exception e) {
		System.err.println("im20101_man_3Handlers ::: getFiles ::: Exception");
		e.printStackTrace();
	} finally {
		// Release the database connection back to the pool
		if (jdbcConn != null) {
			jdbcConn.closeConnection();
		}
		
		if (case_photo.length() > 0) {
			result.put("case_photo", "<a class=\"link-switch\" id=\"case_photo_switch\" data-toggle=\"collapse\" href=\"#case_photo_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"case_photo_content\" class=\"collapse ul-file\">" + case_photo.toString() + "</ul>");
		}
		if (case_attm.length() > 0) {
			result.put("case_attm", "<a class=\"link-switch\" data-toggle=\"collapse\" href=\"#case_attm_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"case_attm_content\" class=\"collapse ul-file\">" + case_attm.toString() + "</ul>");
		}
		if (case_laborlaw.length() > 0) {
			result.put("case_laborlaw", "<a class=\"link-switch\" data-toggle=\"collapse\" href=\"#case_laborlaw_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"case_laborlaw_content\" class=\"collapse ul-file\">" + case_laborlaw.toString() + "</ul>");
		}
		if (result_photo.length() > 0) {
			result.put("result_photo", "<a class=\"link-switch\" id=\"result_photo_switch\" data-toggle=\"collapse\" href=\"#result_photo_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"result_photo_content\" class=\"collapse ul-file\">" + result_photo.toString() + "</ul>");
		}
		if (result_attm.length() > 0) {
			result.put("result_attm", "<a class=\"link-switch\" data-toggle=\"collapse\" href=\"#result_attm_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"result_attm_content\" class=\"collapse ul-file\">" + result_attm.toString() + "</ul>");
		}
		
		// Release the memory resource immediately 
		case_photo = null;
		case_attm = null;
		result_photo = null;
		result_attm = null;
	}
	
	return result;
}

private String composeHTML(String case_id, String pic_kind, String pic_seq, boolean isImage, String name) {
	String html = "";
	String divOnclick = "window.open('in10101_getImage.jsp?EXP_NO=" + case_id + "&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis() + "');";
	String imgSrc = "img/PDF.jpg";
	
	if (isImage) {
		divOnclick = "showPopup('" + case_id + "', '" + pic_kind + "', '" + pic_seq + "');";
		imgSrc = "in10101_getImage.jsp?EXP_NO=" + case_id + "&Zzip=Y&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis();
	}
	
	html = "<div class=\"col-xs-6 col-sm-3 col-md-3 col-lg-2 text-center div-frame\">";
	html += "<li>";
	html += "<div class=\"vertical-container ht-200px\" role=\"button\" onclick=\"[onclick]\">";
	html += "<img class=\"img-150x200\" src=\"[src]\" alt=\"" + name + "\">";
	html += "</div>";
	html += "<div><label>" + name + "</label></div>";
	html += "</li>";
	html += "</div>";
	
	html = StringUtils.replace(html, "[onclick]", divOnclick);
	html = StringUtils.replace(html, "[src]", imgSrc);
	
	return html;
}

private String composeHTML_Laborlaw(String case_id, String pic_kind, String pic_seq, boolean isImage, String name) {
	String html = "";
	String divOnclick = "window.open('in20801_getImage.jsp?EXP_NO=" + case_id + "&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis() + "');";
	String imgSrc = "img/PDF.jpg";
	
	if (isImage) {
		divOnclick = "showLaborlawPic('" + pic_seq + "', '" + pic_kind + "', '" + case_id + "');";
		imgSrc = "in20801_getImage.jsp?EXP_NO=" + case_id + "&Zzip=Y&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis();
	}
	
	html = "<div class=\"col-xs-6 col-sm-3 col-md-3 col-lg-2 text-center div-frame\">";
	html += "<li>";
	html += "<div class=\"vertical-container ht-200px\" role=\"button\" onclick=\"[onclick]\">";
	html += "<img class=\"img-150x200\" src=\"[src]\" alt=\"" + name + "\">";
	html += "</div>";
	html += "<div><label>" + name + "</label></div>";
	html += "</li>";
	html += "</div>";
	
	html = StringUtils.replace(html, "[onclick]", divOnclick);
	html = StringUtils.replace(html, "[src]", imgSrc);
	
	return html;
}


private String composeHTML_II(String case_id, String pic_kind, String pic_seq, boolean isImage, String name) {
	String html = "";
	String imgSrc = "img/PDF.jpg";
	
	if (isImage) {
		imgSrc = "in10101_getImage.jsp?EXP_NO=" + case_id + "&Zzip=Y&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis();
	}
	
	html = "<div class=\"col-xs-6 col-sm-3 col-md-3 col-lg-2 text-center div-frame\">";
	html += "<li>";
	html += "<div class=\"vertical-container ht-200px\" role=\"button\" onclick=\"[onclick]\">";
	html += "<img class=\"img-150x200\" data-original=\"[src]\" src=\"[src]\" alt=\"" + name + "\">";
	html += "</div>";
	html += "<div><label>" + name + "</label></div>";
	html += "</li>";
	html += "</div>";
	
	html = StringUtils.replace(html, "[src]", imgSrc);
	
	return html;
}

private String styleDate(String rawStr) {
	String dateStyle = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		dateStyle = EzekUtils.formatDate(rawStr, "YYYMMDD", "/");
	}
	
	return dateStyle;
}

private String styleDateTime(String rawStr) {
	String dateStyle = "", newDate = "", newTime = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		String date = rawStr.substring(0, 7);
		String time = rawStr.substring(7, 13);
		
		newDate = EzekUtils.formatDate(date, "YYYMMDD", "/");
		newTime = formatDateTime(time, "MANDARIN");
		
		dateStyle = newDate + " " + newTime;
	}
	
	return dateStyle;
}

private String formatDateTime(String sourceStr, String delimiterStyle)
{
	String resultStr = "";
	
	String hour = sourceStr.substring(0, 2);
	String minute = sourceStr.substring(2, 4);
	String second = sourceStr.substring(4);
	
	hour = String.valueOf(Integer.parseInt(hour));
	minute = String.valueOf(Integer.parseInt(minute));
	second = String.valueOf(Integer.parseInt(second));
  
	if (delimiterStyle.toUpperCase().equals("MANDARIN")) 
	{
		resultStr = hour + "時" + minute + "分" + second + "秒";
	} 
	else 
	{
		resultStr = hour + delimiterStyle + minute + delimiterStyle + second;
	}
	
	return resultStr;
}



private String styleDateInMandarinHandleEmptyDate(String rawStr) {
	String dateStyle = "　年　月　日";
	
	if (!StringUtils.isEmpty(rawStr)) {
		dateStyle = EzekUtils.formatDate(rawStr, "YYYMMDD", "MANDARIN");
	}
	
	return dateStyle;
}

private String encapsulateWithBrackets(String rawStr) {
	String result = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		result = "[" + rawStr + "]";
	}
	
	return result;
}
private String formateExamine_kind( String rawStr ){
	String result = "";
	
	if ("01".equals(rawStr)) {
		result = "勘查紀錄單";
	}else if ("02".equals(rawStr)){
		result = "單位查報";
	}else if ("03".equals(rawStr)){
		result = "專案";
	}
	
	return result;
}

private String getConfirmResult(String reg_rsult, String reg_ann, String reg_rsult_memo) {
	String result = "";
	
	if (!StringUtils.isEmpty(reg_rsult)) {
		if (reg_rsult.equals("01")) {
			result = "一般違建";
			if ("Y".equals(reg_ann)) {
				result += "(公告)";
			}
		} else if (reg_rsult.equals("02")) {
			result = "拍照建檔列管";
		} else if (reg_rsult.equals("ZZ")) {
			result = "其他";
			if (!StringUtils.isEmpty(reg_rsult_memo)) {
				result += "：" + reg_rsult_memo;
			}
		}
	}
	
	return result;
}

private String composeLicenceDoc(String licence_yy, String licence_word, String licence_no) {
	String TEMPLATE_LICENCE_DOC = "{licence_word}字第{licence_no}號";
	String result = "";
	
	if (!StringUtils.isEmpty(licence_word) && !StringUtils.isEmpty(licence_no)) {
		result = TEMPLATE_LICENCE_DOC;
		
		result = StringUtils.replace(result, "{licence_word}", licence_word);
		result = StringUtils.replace(result, "{licence_no}", licence_no);
		
		if (!StringUtils.isEmpty(licence_yy)) {
			result = licence_yy + "年" + result;
		}
	}
	
	return result;
}

//Feature checker Head @1-ADDA7BA3
 public class im20101_man_3ServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-41C96CEF
  public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
   String attr = "" + request.getParameter("callbackControl");
   if ( "Panel1".equals ( request.getParameter("FormFilter") ) ) {
    request.setAttribute(com.codecharge.Names.CCS_UPDATE_PANEL_FILTER,  "Panel1" );
    return false; 
   }
   return false;
  }
//End feature binding

//Feature checker Tail @1-FCB6E20C
 }
//End Feature checker Tail

//im20101_man_3 Page Handler Head @1-A6DF5372
 public class im20101_man_3PageHandler implements PageListener {
//End im20101_man_3 Page Handler Head

//im20101_man_3 BeforeInitialize Method Head @1-4C73EADA
  public void beforeInitialize(Event e) {
//End im20101_man_3 BeforeInitialize Method Head

//im20101_man_3 BeforeInitialize Method Tail @1-FCB6E20C
  }
//End im20101_man_3 BeforeInitialize Method Tail

//im20101_man_3 AfterInitialize Method Head @1-89E84600
  public void afterInitialize(Event e) {
//End im20101_man_3 AfterInitialize Method Head

//Event AfterInitialize Action Validate onTimeout_Synct @108-4B8A4259
          if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
//End Event AfterInitialize Action Validate onTimeout_Synct

//im20101_man_3 AfterInitialize Method Tail @1-FCB6E20C
  }
//End im20101_man_3 AfterInitialize Method Tail

//im20101_man_3 OnInitializeView Method Head @1-E3C15E0F
  public void onInitializeView(Event e) {
//End im20101_man_3 OnInitializeView Method Head

//im20101_man_3 OnInitializeView Method Tail @1-FCB6E20C
  }
//End im20101_man_3 OnInitializeView Method Tail

//im20101_man_3 BeforeShow Method Head @1-46046458
  public void beforeShow(Event e) {
//End im20101_man_3 BeforeShow Method Head

//im20101_man_3 BeforeShow Method Tail @1-FCB6E20C
  }
//End im20101_man_3 BeforeShow Method Tail

//im20101_man_3 BeforeOutput Method Head @1-BE3571C7
  public void beforeOutput(Event e) {
//End im20101_man_3 BeforeOutput Method Head

//im20101_man_3 BeforeOutput Method Tail @1-FCB6E20C
  }
//End im20101_man_3 BeforeOutput Method Tail

//im20101_man_3 BeforeUnload Method Head @1-1DDBA584
  public void beforeUnload(Event e) {
//End im20101_man_3 BeforeUnload Method Head

//im20101_man_3 BeforeUnload Method Tail @1-FCB6E20C
  }
//End im20101_man_3 BeforeUnload Method Tail

//im20101_man_3 onCache Method Head @1-7A88A4B8
  public void onCache(CacheEvent e) {
//End im20101_man_3 onCache Method Head

//get cachedItem @1-F7EFE9F6
   if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
    /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
   } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
    /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
   }
//End if tail

//im20101_man_3 onCache Method Tail @1-FCB6E20C
  }
//End im20101_man_3 onCache Method Tail

//im20101_man_3 Page Handler Tail @1-FCB6E20C
 }
//End im20101_man_3 Page Handler Tail

//ibmcase4 Record Handler Head @2-5FBB7B5A
 public class im20101_man_3ibmcase4RecordHandler implements RecordListener, RecordDataObjectListener {
//End ibmcase4 Record Handler Head

//ibmcase4 afterInitialize Method Head @2-89E84600
  public void afterInitialize(Event e) {
//End ibmcase4 afterInitialize Method Head

//ibmcase4 afterInitialize Method Tail @2-FCB6E20C
  }
//End ibmcase4 afterInitialize Method Tail

//ibmcase4 OnSetDataSource Method Head @2-9B7FBFCF
  public void onSetDataSource(DataObjectEvent e) {
//End ibmcase4 OnSetDataSource Method Head

//ibmcase4 OnSetDataSource Method Tail @2-FCB6E20C
  }
//End ibmcase4 OnSetDataSource Method Tail

//ibmcase4 BeforeShow Method Head @2-46046458
  public void beforeShow(Event e) {
//End ibmcase4 BeforeShow Method Head

//ibmcase4 Default value @2-F6C32A5F
   e.getComponent().getControl("building_coat_unitLabel").setDefaultValue("層");
   e.getComponent().getControl("building_height_unitLabel").setDefaultValue("公尺");
   e.getComponent().getControl("building_area_unitLabel").setDefaultValue("平方公尺");
//End ibmcase4 Default value

//Event BeforeShow Action Custom Code @116-44795B7A

	// @modifier: Samuel C. Fan
	// @updated: 2021/09/07
	// @issue: PM instructs to have other program link to this page, and need to return back to the 
	//         page. Use <PROGRAM_ID> session to determine which page to return to.
	// @solution: Alter the code according to the instructions.
	// 
	// @modifier: Samuel C. Fan
	// @updated: 2021/09/07 18:30
	// @issue: PM instructs to have another program link to this page, and need to return back to the 
	//         page. Use <PROGRAM_ID> session to determine which page to return to.
	// @solution: Alter the code according to the instructions.
	// 
	// @modifier: Yao
	// @updated: 2021/09/15 18:30
	// @issue: 改文字 新增 examine_kind  formate
	//         
	// @solution:
	// 
	// modifier: Samuel C. Fan
	// @updated: 2021/10/20
	// @issue: 拆除情形 need to include licence_yy.
	// @solution: Alter the code acordingly.
	// 
	
	// Get the program id from the <PROGRAM_ID> session
	String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID"));
	
	// Get the value from the URL parameter
	String url_case_id = e.getPage().getHttpGetParams().getParameter("case_id");
	String url_vwtype = e.getPage().getHttpGetParams().getParameter("vwtype");
	
	// Get the value from the field
	String reg_yy = Utils.convertToString(e.getRecord().getControl("reg_yy").getValue());
	String reg_no = Utils.convertToString(e.getRecord().getControl("reg_no").getValue());
	String reg_date = Utils.convertToString(e.getRecord().getControl("reg_date").getValue());
	String case_ori_num = Utils.convertToString(e.getRecord().getControl("case_ori_num").getValue());
	String rvldate = Utils.convertToString(e.getRecord().getControl("rvldate").getValue());
	String ib_prcs = Utils.convertToString(e.getRecord().getControl("ib_prcs").getValue());
	String dis_type = Utils.convertToString(e.getRecord().getControl("dis_type").getValue());
	String dis_sort = Utils.convertToString(e.getRecord().getControl("dis_sort").getValue());
	String examine_kind = Utils.convertToString(e.getRecord().getControl("examine_kind").getValue());
	String reg_rec_date = Utils.convertToString(e.getRecord().getControl("reg_rec_date").getValue());
	String rsult_rec_time = Utils.convertToString(e.getRecord().getControl("rsult_rec_time").getValue());
	String land_number = Utils.convertToString(e.getRecord().getControl("land_number").getValue());
	String building_coat = Utils.convertToString(e.getRecord().getControl("building_coat").getValue());
	String building_height = Utils.convertToString(e.getRecord().getControl("building_height").getValue());
	String building_area = Utils.convertToString(e.getRecord().getControl("building_area").getValue());
	String audnm_date = Utils.convertToString(e.getRecord().getControl("audnm_date").getValue());
	String x_coordinate = Utils.convertToString(e.getRecord().getControl("x_coordinate").getValue());
	String y_coordinate = Utils.convertToString(e.getRecord().getControl("y_coordinate").getValue());
	String pre_dis_date = Utils.convertToString(e.getRecord().getControl("pre_dis_date").getValue());
	String dis_notice_date = Utils.convertToString(e.getRecord().getControl("dis_notice_date").getValue());
	String dis_reg_yy = Utils.convertToString(e.getRecord().getControl("dis_reg_yy").getValue());
	String dis_reg_no = Utils.convertToString(e.getRecord().getControl("dis_reg_no").getValue());
	String b_notice_date = Utils.convertToString(e.getRecord().getControl("b_notice_date").getValue());
	String reg_rsult = Utils.convertToString(e.getRecord().getControl("reg_rsult").getValue());
	String reg_ann = Utils.convertToString(e.getRecord().getControl("reg_ann").getValue());
	String reg_rsult_memo = Utils.convertToString(e.getRecord().getControl("reg_rsult_memo").getValue());
	String licence_yy = Utils.convertToString(e.getRecord().getControl("licence_yy").getValue());
	String licence_word = Utils.convertToString(e.getRecord().getControl("licence_word").getValue());
	String licence_no = Utils.convertToString(e.getRecord().getControl("licence_no").getValue());
	String end_date = Utils.convertToString(e.getRecord().getControl("end_date").getValue());
	String end_reg_yy = Utils.convertToString(e.getRecord().getControl("end_reg_yy").getValue());
	String end_reg_no = Utils.convertToString(e.getRecord().getControl("end_reg_no").getValue());
	
	JDBCConnection jdbcConn = null;
	DbRow singleRowData = null;
	DbRow dataRow = null;
	Enumeration dataRows = null;
	String SQL = "";
	
	// Start 通知單下載 fields
	long ack_cnt = Utils.convertToLong(e.getRecord().getControl("ack_cnt").getValue()).longValue();
	long plan_cnt = Utils.convertToLong(e.getRecord().getControl("plan_cnt").getValue()).longValue();
	long close_cnt = Utils.convertToLong(e.getRecord().getControl("close_cnt").getValue()).longValue();
	long ack_zip_cnt = Utils.convertToLong(e.getRecord().getControl("ack_zip_cnt").getValue()).longValue();
	if(reg_no.length() < 6)ack_zip_cnt =0;
	// End 通知單下載 fields
	
	String styleFormatStr_case_ori_num = "", styleFormatStr_coordinate = "";
	
	// 案件來源-文號
	if (!StringUtils.isEmpty(case_ori_num)) {
		styleFormatStr_case_ori_num = "(" + case_ori_num + ")";
	}
	// 經緯度座標
	if (!StringUtils.isEmpty(x_coordinate) && !StringUtils.isEmpty(y_coordinate)) {
		styleFormatStr_coordinate = y_coordinate + ", " + x_coordinate;
	}
	
	// Set the visibility of the field
	// 違建地上層數
	if (StringUtils.isEmpty(building_coat)) {
		e.getRecord().getControl("building_coat_unitLabel").setVisible(false);
	}
	// 違建高度
	if (StringUtils.isEmpty(building_height)) {
		e.getRecord().getControl("building_height_unitLabel").setVisible(false);
	}
	// 違建面積
	if (StringUtils.isEmpty(building_area)) {
		e.getRecord().getControl("building_area_unitLabel").setVisible(false);
	}
	
	System.out.println(ack_cnt);
	
	// [][0]: name
	// [][1]: notice count
	// [][2]: target page to call 
	// [][3]: URL parameters
	String[][] PRINT_INFO = {
		{"認定通知單", "" + ack_cnt, "im10101_prt.jsp", "out_ext=PDF&data_need_type=ALL&case_id="}, 
		{"拆除通知單", "" + plan_cnt, "im40101_prt.jsp", "out_ext=PDF&case_id="}, 
		{"結案通知單", "" + close_cnt, "im40201_prt.jsp", "out_ext=PDF&out_null=N&case_id="}, 
		{"空白結案通知單", "1", "im40201_prt.jsp", "out_ext=PDF&PRT_TYPE=EMPTY&case_id="}, 
		{"送達證書", "1", "im40101_prt.jsp", "out_ext=PDF&prt_type=2&case_id="}, 
		{"基本資料列印", "1", "im20102_prt.jsp", "out_ext=PDF&case_id="}
		,{"認定案件打包下載", "" +ack_zip_cnt, "im10501_goZip.jsp", "out_ext=PDF&case_id="}
	};
	int idx = 0, printInfoLen = PRINT_INFO.length, noticeCount = 0;
	String LINK_TEMPLATE = "<a href=\"#gia\" onclick=\"callPrint('[target-page]?[url-param]');\">[ui-word]</a>";
	String print_label = "";
	
	for (idx = 0; idx < printInfoLen; idx++) {
		print_label = "<label>" + PRINT_INFO[idx][0] + "</label>";
		
		noticeCount = Utils.convertToLong(PRINT_INFO[idx][1]).intValue();
		
		if (noticeCount > 0) {
			print_label = LINK_TEMPLATE;
			print_label = StringUtils.replace(print_label, "[target-page]", PRINT_INFO[idx][2]);
			print_label = StringUtils.replace(print_label, "[url-param]", PRINT_INFO[idx][3] + url_case_id);
			print_label = StringUtils.replace(print_label, "[ui-word]", PRINT_INFO[idx][0]);
		}
		
		e.getRecord().getControl("print_label_" + idx).setValue(print_label);
		
		// Reset for the next iteration
		noticeCount = 0;
	}
	
	// Get the uploaded files
	HashMap<String, String> files = getFiles(url_case_id);
	
	// Set the value to the field
	e.getRecord().getControl("reg_date").setValue(styleDate(reg_date));
	e.getRecord().getControl("case_ori_num").setValue(styleFormatStr_case_ori_num);
	e.getRecord().getControl("rvldate").setValue(styleDate(rvldate));
	e.getRecord().getControl("ib_prcs").setValue(getUnpermittedStructureWords(ib_prcs));
	//e.getRecord().getControl("dis_type").setValue(encapsulateWithBrackets(dis_type));
	//e.getRecord().getControl("dis_sort").setValue(encapsulateWithBrackets(dis_sort));
	e.getRecord().getControl("examine_kind").setValue(formateExamine_kind(examine_kind));
	e.getRecord().getControl("reg_rec_date").setValue( styleDate((StringUtils.isEmpty(reg_rec_date) ? "" : reg_rec_date.substring(0, reg_rec_date.length() - 6))) );
	e.getRecord().getControl("rsult_rec_time").setValue( styleDate((StringUtils.isEmpty(rsult_rec_time) ? "" : rsult_rec_time.substring(0, rsult_rec_time.length() - 6))) );
	e.getRecord().getControl("land_number").setValue(formatLandNumber(land_number));
	e.getRecord().getControl("coordinate_view").setValue(styleFormatStr_coordinate);
	e.getRecord().getControl("audnm_date").setValue(styleDate(audnm_date));
	e.getRecord().getControl("view_reg_rsult").setValue(getConfirmResult(reg_rsult, reg_ann, reg_rsult_memo));
	e.getRecord().getControl("case_violator").setValue(getViolatorInfo(url_case_id));
	e.getRecord().getControl("case_photo").setValue(files.get("case_photo"));
	e.getRecord().getControl("case_attm").setValue(files.get("case_attm"));
	e.getRecord().getControl("case_laborlaw").setValue(files.get("case_laborlaw"));
	e.getRecord().getControl("pre_dis_date").setValue(styleDate(pre_dis_date));
	e.getRecord().getControl("dis_notice_date").setValue(styleDate(dis_notice_date));
	e.getRecord().getControl("b_notice_date").setValue(styleDate(b_notice_date));
	e.getRecord().getControl("view_licence").setValue(composeLicenceDoc(licence_yy, licence_word, licence_no));
	e.getRecord().getControl("end_date").setValue(styleDate(end_date));
	e.getRecord().getControl("result_photo").setValue(files.get("result_photo"));
	e.getRecord().getControl("result_attm").setValue(files.get("result_attm"));
	
	// Release the memory resource immediately
	files = null;
	
	String[] BUTTON_NAMES = {"Button_Cancel", "Button_Cancel_2", "Button_Cancel_im20201", "Button_Cancel_im20301"};
	int buttonNamesLen = BUTTON_NAMES.length;
	
	for (idx = 0; idx < buttonNamesLen; idx++) {
		e.getRecord().getButton(BUTTON_NAMES[idx]).setVisible(false);
	}
	
	if ("im20201,im20301".indexOf(PROGRAM_ID) > -1) {
		e.getRecord().getButton("Button_Cancel_" + PROGRAM_ID).setVisible(true);
	} else {
		if (StringUtils.isEmpty(url_vwtype)) {
			e.getRecord().getButton("Button_Cancel").setVisible(true);
		} else {
			e.getRecord().getButton("Button_Cancel_2").setVisible(true);
		}
	}
	
	try
	{
		jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		
		String reb_yy = Utils.convertToString(DBTools.dLookUp("reb_yy", "ibmfirecase", "reb_yy = '"+reg_yy+"'", "DBConn"));
		String reb_no = Utils.convertToString(DBTools.dLookUp("reb_no", "ibmfirecase", "reb_yy = '"+reg_no+"'", "DBConn"));
		String ibmcase_case_id = Utils.convertToString(DBTools.dLookUp("case_id", "ibmcase", "reg_yy || reg_no = '"+(reg_yy + reg_no)+"' ", "DBConn"));
		
		//火災通報
		if(!StringUtils.isEmpty(reb_yy) && !StringUtils.isEmpty(reb_no))
		{
			SQL = "SELECT * FROM ibmfirecase WHERE reb_yy = '"+reb_yy+"' AND reb_no = '"+reb_no+"' ORDER BY notify_date DESC, notify_hour DESC, notify_minute DESC limit 1";
		}
		else
		{
			SQL = "SELECT * FROM ibmfirecase WHERE reg_yy = '"+reg_yy+"' AND reg_no = '"+reg_no+"' ORDER BY notify_date DESC, notify_hour DESC, notify_minute DESC limit 1";
		}
		
		singleRowData = jdbcConn.getOneRow(SQL);
		
		if (singleRowData != null) 
		{
			String notify_date = Utils.convertToString(singleRowData.get("notify_date"));
			String notify_hour = Utils.convertToString(singleRowData.get("notify_hour"));
			String notify_minute = Utils.convertToString(singleRowData.get("notify_minute"));
			String casualty_quantity = StringUtils.isEmpty(Utils.convertToString(singleRowData.get("casualty_quantity"))) ? "0" : Utils.convertToString(singleRowData.get("casualty_quantity"));
			
			String notify_note = "";
			if(!StringUtils.isEmpty(notify_date))
			{
				notify_note = "通報日期: " + notify_date.substring(0, 3) + "年" + notify_date.substring(3, 5) + "月" + notify_date.substring(5, 7) + "日  時間: ";
			}
			
			if(!StringUtils.isEmpty(notify_hour))
			{
				notify_note += notify_hour + "時";
			}
			
			if(!StringUtils.isEmpty(notify_minute))
			{
				notify_note += notify_minute + "分";
			}
			
			e.getRecord().getControl("firecase_note").setValue("<span>"+notify_note+"</span><br/><span>傷亡人數: "+casualty_quantity+"</span>");
		}
		
		//法務收費情形
		SQL = "SELECT * FROM ibmlawfee WHERE reg_yy = '"+reg_yy+"' AND reg_no = '"+reg_no+"' ORDER BY cr_date DESC LIMIT 1";
		singleRowData = jdbcConn.getOneRow(SQL);
		
		String lawfee_note = "";
		if (singleRowData != null) 
		{
			String paymentbook_date = Utils.convertToString(singleRowData.get("paymentbook_date"));
			String transfer_date = Utils.convertToString(singleRowData.get("transfer_date"));
			String payable_amount = Utils.convertToString(singleRowData.get("payable_amount"));
			String payment_type = Utils.convertToString(singleRowData.get("payment_type"));
			String payment_type_note = Utils.convertToString(singleRowData.get("payment_type_note"));
			
			switch(payment_type)
			{
				case "1":
					payment_type = "已繳款";
					break;
				case "2":
					payment_type = "未繳款";
					break;
				case "3":
					payment_type = "分期繳款";
					break;
				case "4":
					payment_type = "其他，說明: " + payment_type_note;
					break;
				default:
					payment_type = "";
					break;
			}
			
			lawfee_note = "<span>繳款書開立日期: " + (StringUtils.isEmpty(paymentbook_date) ? "未填寫" : paymentbook_date.substring(0, 3) + "年" + paymentbook_date.substring(3, 5) + "月" + paymentbook_date.substring(5, 7)) + "日";
			lawfee_note += "<br/>";
			lawfee_note += "移送行政執行日期: " + (StringUtils.isEmpty(transfer_date) ? "未填寫" : transfer_date.substring(0, 3) + "年" + transfer_date.substring(3, 5) + "月" + transfer_date.substring(5, 7)) + "日</span>";
			lawfee_note += "<br/>";
			lawfee_note += "<span>應繳金額: " + (StringUtils.isEmpty(payable_amount) ? "0" : payable_amount) + "元";
			lawfee_note += "<br/>";
			lawfee_note += "繳款情形: " + payment_type + "</span>";
			
			e.getRecord().getControl("lawfee_note").setValue(lawfee_note);
		}
		
		//工輔法專區
		SQL = "SELECT * FROM ibmlaborlaw INNER JOIN ibmlaborlaw_case ON ibmlaborlaw.case_id = ibmlaborlaw_case.ibmlaborlaw_case_id WHERE ibmlaborlaw_case.ibmcase_case_id = '"+ibmcase_case_id+"' ORDER BY ibmlaborlaw.document_date DESC";
		dataRows = jdbcConn.getRows(SQL);
		String laborlaw_note = "";
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow) dataRows.nextElement();
			String document_date = Utils.convertToString(dataRow.get("document_date"));
			String document_no = Utils.convertToString(dataRow.get("document_no"));
			String note = Utils.convertToString(dataRow.get("note"));
			String case_status = Utils.convertToString(dataRow.get("case_status"));
			String case_status_note = Utils.convertToString(dataRow.get("case_status_note"));

			switch (case_status) {
				case "1":
					case_status = "核准";
					break;
				case "2":
					case_status = "改善";
					break;
				case "3":
					case_status = "退件駁回";
					break;
				case "4":
					case_status = "其他，" + case_status_note;
					break;
			}

			laborlaw_note += "<span>來文日期: " + document_date.substring(0, 3) + "年" + document_date.substring(3, 5) + "月" + document_date.substring(5, 7) + "日</span>";
			laborlaw_note += "<br/>";
			laborlaw_note += "<span>來文字號: " + document_no + "</span>";
			laborlaw_note += "<br/>";
			laborlaw_note += "案件狀態: " + case_status;
			laborlaw_note += "<br/>";
			laborlaw_note += "辦理情形: " + note +"<br/>";
			
		}

		e.getRecord().getControl("laborlaw_note").setValue(laborlaw_note);
		
	}
	catch (Exception localException)
	{
		System.err.println("im20101_man3:BeforeShow error is " + localException.toString());		
	}
	finally
	{
		if( jdbcConn != null )jdbcConn.closeConnection();
	}
	

//End Event BeforeShow Action Custom Code

//ibmcase4 BeforeShow Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeShow Method Tail

//ibmcase4 OnValidate Method Head @2-5F430F8E
  public void onValidate(Event e) {
//End ibmcase4 OnValidate Method Head

//ibmcase4 OnValidate Method Tail @2-FCB6E20C
  }
//End ibmcase4 OnValidate Method Tail

//ibmcase4 BeforeSelect Method Head @2-E5EC9AD3
  public void beforeSelect(Event e) {
//End ibmcase4 BeforeSelect Method Head

//ibmcase4 BeforeSelect Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeSelect Method Tail

//ibmcase4 BeforeBuildSelect Method Head @2-3041BA14
  public void beforeBuildSelect(DataObjectEvent e) {
//End ibmcase4 BeforeBuildSelect Method Head

//ibmcase4 Default Values for Select Query (SQL) @2-0C806E53
   try {
    ((SqlParameter)e.getParameter("case_id")).setDefaultValue("");
   } catch(java.text.ParseException ignore) {}
//End ibmcase4 Default Values for Select Query (SQL)

//ibmcase4 BeforeBuildSelect Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeBuildSelect Method Tail

//ibmcase4 BeforeExecuteSelect Method Head @2-8391D9D6
  public void beforeExecuteSelect(DataObjectEvent e) {
//End ibmcase4 BeforeExecuteSelect Method Head

//ibmcase4 BeforeExecuteSelect Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeExecuteSelect Method Tail

//ibmcase4 AfterExecuteSelect Method Head @2-0972E7FA
  public void afterExecuteSelect(DataObjectEvent e) {
//End ibmcase4 AfterExecuteSelect Method Head

//ibmcase4 AfterExecuteSelect Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterExecuteSelect Method Tail

//ibmcase4 BeforeInsert Method Head @2-75B62B83
  public void beforeInsert(Event e) {
//End ibmcase4 BeforeInsert Method Head

//ibmcase4 BeforeInsert Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeInsert Method Tail

//ibmcase4 BeforeBuildInsert Method Head @2-FD6471B0
  public void beforeBuildInsert(DataObjectEvent e) {
//End ibmcase4 BeforeBuildInsert Method Head

//ibmcase4 BeforeBuildInsert Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeBuildInsert Method Tail

//ibmcase4 BeforeExecuteInsert Method Head @2-4EB41272
  public void beforeExecuteInsert(DataObjectEvent e) {
//End ibmcase4 BeforeExecuteInsert Method Head

//ibmcase4 BeforeExecuteInsert Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeExecuteInsert Method Tail

//ibmcase4 AfterExecuteInsert Method Head @2-C4572C5E
  public void afterExecuteInsert(DataObjectEvent e) {
//End ibmcase4 AfterExecuteInsert Method Head

//ibmcase4 AfterExecuteInsert Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterExecuteInsert Method Tail

//ibmcase4 AfterInsert Method Head @2-767A9165
  public void afterInsert(Event e) {
//End ibmcase4 AfterInsert Method Head

//ibmcase4 AfterInsert Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterInsert Method Tail

//ibmcase4 BeforeUpdate Method Head @2-33A3CFAC
  public void beforeUpdate(Event e) {
//End ibmcase4 BeforeUpdate Method Head

//ibmcase4 BeforeUpdate Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeUpdate Method Tail

//ibmcase4 BeforeBuildUpdate Method Head @2-37688606
  public void beforeBuildUpdate(DataObjectEvent e) {
//End ibmcase4 BeforeBuildUpdate Method Head

//ibmcase4 BeforeBuildUpdate Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeBuildUpdate Method Tail

//ibmcase4 BeforeExecuteUpdate Method Head @2-84B8E5C4
  public void beforeExecuteUpdate(DataObjectEvent e) {
//End ibmcase4 BeforeExecuteUpdate Method Head

//ibmcase4 BeforeExecuteUpdate Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeExecuteUpdate Method Tail

//ibmcase4 AfterExecuteUpdate Method Head @2-0E5BDBE8
  public void afterExecuteUpdate(DataObjectEvent e) {
//End ibmcase4 AfterExecuteUpdate Method Head

//ibmcase4 AfterExecuteUpdate Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterExecuteUpdate Method Tail

//ibmcase4 AfterUpdate Method Head @2-306F754A
  public void afterUpdate(Event e) {
//End ibmcase4 AfterUpdate Method Head

//ibmcase4 AfterUpdate Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterUpdate Method Tail

//ibmcase4 BeforeDelete Method Head @2-752E3118
  public void beforeDelete(Event e) {
//End ibmcase4 BeforeDelete Method Head

//ibmcase4 BeforeDelete Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeDelete Method Tail

//ibmcase4 BeforeBuildDelete Method Head @2-01A46505
  public void beforeBuildDelete(DataObjectEvent e) {
//End ibmcase4 BeforeBuildDelete Method Head

//ibmcase4 BeforeBuildDelete Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeBuildDelete Method Tail

//ibmcase4 BeforeExecuteDelete Method Head @2-B27406C7
  public void beforeExecuteDelete(DataObjectEvent e) {
//End ibmcase4 BeforeExecuteDelete Method Head

//ibmcase4 BeforeExecuteDelete Method Tail @2-FCB6E20C
  }
//End ibmcase4 BeforeExecuteDelete Method Tail

//ibmcase4 AfterExecuteDelete Method Head @2-389738EB
  public void afterExecuteDelete(DataObjectEvent e) {
//End ibmcase4 AfterExecuteDelete Method Head

//ibmcase4 AfterExecuteDelete Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterExecuteDelete Method Tail

//ibmcase4 AfterDelete Method Head @2-76E28BFE
  public void afterDelete(Event e) {
//End ibmcase4 AfterDelete Method Head

//ibmcase4 AfterDelete Method Tail @2-FCB6E20C
  }
//End ibmcase4 AfterDelete Method Tail

//ibmcase4 Record Handler Tail @2-FCB6E20C
 }
//End ibmcase4 Record Handler Tail

//Button_Cancel_2 Button Handler Head @257-8C511AFC
 public class ibmcase4Button_Cancel_2ButtonHandler implements ButtonListener {
//End Button_Cancel_2 Button Handler Head

//Button_Cancel_2 OnClick Method Head @257-A9885EEC
  public void onClick(Event e) {
//End Button_Cancel_2 OnClick Method Head

//Button_Cancel_2 OnClick Method Tail @257-FCB6E20C
  }
//End Button_Cancel_2 OnClick Method Tail

//Button_Cancel_2 BeforeShow Method Head @257-46046458
  public void beforeShow(Event e) {
//End Button_Cancel_2 BeforeShow Method Head

//Button_Cancel_2 BeforeShow Method Tail @257-FCB6E20C
  }
//End Button_Cancel_2 BeforeShow Method Tail

//Button_Cancel_2 Button Handler Tail @257-FCB6E20C
 }
//End Button_Cancel_2 Button Handler Tail

//Panel1 Panel Handler Head @272-155E5331
 public class im20101_man_3Panel1PanelHandler implements ControlListener {
//End Panel1 Panel Handler Head

//Panel1 BeforeShow Method Head @272-46046458
  public void beforeShow(Event e) {
//End Panel1 BeforeShow Method Head

//Panel1 BeforeShow Method Tail @272-FCB6E20C
  }
//End Panel1 BeforeShow Method Tail

//Panel1 Panel Handler Tail @272-FCB6E20C
 }

 public class im20101_man_3Panel2PanelHandler implements ControlListener {
	//End Panel1 Panel Handler Head

	//Panel1 BeforeShow Method Head @272-46046458
	  public void beforeShow(Event e) {
	//End Panel1 BeforeShow Method Head

	//Panel1 BeforeShow Method Tail @272-FCB6E20C
	  }
	//End Panel1 BeforeShow Method Tail

	//Panel1 Panel Handler Tail @272-FCB6E20C
	 }
//End Panel1 Panel Handler Tail

//ibmfym Grid Handler Head @184-A6F14B55
 public class im20101_man_3ibmfymGridHandler implements GridListener, GridDataObjectListener {
//End ibmfym Grid Handler Head

//ibmfym afterInitialize Method Head @184-89E84600
  public void afterInitialize(Event e) {
//End ibmfym afterInitialize Method Head

//ibmfym afterInitialize Method Tail @184-FCB6E20C
  }
//End ibmfym afterInitialize Method Tail

//ibmfym BeforeShow Method Head @184-46046458
	public void beforeShow(Event e) {
	}
//End ibmfym BeforeShow Method Tail

//ibmfym BeforeShowRow Method Head @184-BDFD38FC
  public void beforeShowRow(Event e) {
//End ibmfym BeforeShowRow Method Head

//Event BeforeShowRow Action Custom Code @203-44795B7A

	// Get the value from the URL parameter
	String url_case_id = e.getPage().getHttpGetParams().getParameter("case_id");
	// Get the value from the field
	
	String acc_seq = Utils.convertToString(e.getGrid().getControl("acc_seq").getValue());
	String acc_date = Utils.convertToString(e.getGrid().getControl("acc_date").getValue());
	String acc_rlt = Utils.convertToString(e.getGrid().getControl("acc_rlt").getValue());
	String acc_rlt_name = Utils.convertToString(e.getGrid().getControl("acc_rlt_name").getValue());
	
	String lnk_download_html = "";
	
	if("工輔法登錄".equals(acc_rlt_name) == false)
	{
		// Set the visibility of the 排拆通知單
		if ("349,359,369".indexOf(acc_rlt) > -1) {
			lnk_download_html = "<a href=\"#gia\" onclick=\"callPrint('im40101_prt.jsp?out_ext=PDF&case_id=" + url_case_id + "&acc_seq=" + acc_seq + "');\">排拆通知單</a>";
		}
		
		if("92c".indexOf(acc_rlt) > -1)
		{
			// 查詢對應的pic_seq，而非累計次數
			String pic_seq = Utils.convertToString(DBTools.dLookUp("pic_seq", "IBMLIST", "case_id = '"+url_case_id+"' and pic_kind = 'ANO_UPD' and pic_seq = '"+acc_seq+"' ", "DBConn"));
			
			// 如果找不到對應的pic_seq，則使用第一筆ANO_UPD記錄
			if (StringUtils.isEmpty(pic_seq)) {
				pic_seq = Utils.convertToString(DBTools.dLookUp("MIN(pic_seq)", "IBMLIST", "case_id = '"+url_case_id+"' and pic_kind = 'ANO_UPD' ", "DBConn"));
			}
			
			if (!StringUtils.isEmpty(pic_seq)) {
				lnk_download_html = "<a href='javascript:void(0)' onclick=\"window.open('in10101_getImage.jsp?EXP_NO="+url_case_id+"&amp;Img_kind=ANO_UPD&amp;Img_index="+pic_seq+"');\">繕校簽核公文下載</a>";
			}
		}
				
		e.getGrid().getControl("lnk_download").setValue(lnk_download_html);
	}
	
	e.getGrid().getControl("acc_date").setValue(styleDate(acc_date));
	
	
	//dmltn_emp 排拆人員，rsult_emp 結案承辦人員
	//2024/05/03 轉移至 im20101_man_3.xml ibmfym Grid 
	// if("344,354,364".indexOf(acc_rlt) > -1){
	// 	String dmltn_emp = Utils.convertToString(DBTools.dLookUp("dmltn_emp", "ibmcase", "CASE_ID = '"+url_case_id+"'", "DBConn"));
	// 	String principal_name = Utils.convertToString(DBTools.dLookUp("EMPNAME", "IBMUSER", "EMPNO = '"+dmltn_emp+"'", "DBConn"));
		
	// 	e.getGrid().getControl("principal_name").setValue(principal_name);
	// }
	
	// if("441,451,461".indexOf(acc_rlt) > -1){
	// 	String rsult_emp = Utils.convertToString(DBTools.dLookUp("rsult_emp", "ibmcase", "CASE_ID = '"+url_case_id+"'", "DBConn"));
	// 	String principal_name = Utils.convertToString(DBTools.dLookUp("EMPNAME", "IBMUSER", "EMPNO = '"+rsult_emp+"'", "DBConn"));
		
	// 	e.getGrid().getControl("principal_name").setValue(principal_name);
	// }
  }
//End ibmfym BeforeShowRow Method Tail

//ibmfym BeforeSelect Method Head @184-E5EC9AD3
  public void beforeSelect(Event e) {
//End ibmfym BeforeSelect Method Head

//ibmfym BeforeSelect Method Tail @184-FCB6E20C
  }
//End ibmfym BeforeSelect Method Tail

//ibmfym BeforeBuildSelect Method Head @184-3041BA14
  public void beforeBuildSelect(DataObjectEvent e) {
//End ibmfym BeforeBuildSelect Method Head

//ibmfym Default Values for Select Query (SQL) @184-0C806E53
   try {
    ((SqlParameter)e.getParameter("case_id")).setDefaultValue("");
   } catch(java.text.ParseException ignore) {}
//End ibmfym Default Values for Select Query (SQL)

//ibmfym BeforeBuildSelect Method Tail @184-FCB6E20C
  }
//End ibmfym BeforeBuildSelect Method Tail

//ibmfym BeforeExecuteSelect Method Head @184-8391D9D6
  public void beforeExecuteSelect(DataObjectEvent e) {
//End ibmfym BeforeExecuteSelect Method Head

//ibmfym BeforeExecuteSelect Method Tail @184-FCB6E20C
  }
//End ibmfym BeforeExecuteSelect Method Tail

//ibmfym AfterExecuteSelect Method Head @184-0972E7FA
  public void afterExecuteSelect(DataObjectEvent e) {
//End ibmfym AfterExecuteSelect Method Head

//ibmfym AfterExecuteSelect Method Tail @184-FCB6E20C
  }
//End ibmfym AfterExecuteSelect Method Tail

//ibmfym Grid Handler Tail @184-FCB6E20C
 }
//End ibmfym Grid Handler Tail

public class im20101_man_3ibmviolation_landGridHandler implements GridListener, GridDataObjectListener 
{
	public void afterInitialize(Event e) {
	}
	  
	public void beforeShow(Event e) {
	}

	public void beforeShowRow(Event e) {
		String cr_date = Utils.convertToString(e.getGrid().getControl("cr_date").getValue());
		e.getGrid().getControl("cr_date").setValue(styleDateTime(cr_date));
	}

	public void beforeSelect(Event e) {
	}
	
	public void beforeBuildSelect(DataObjectEvent e) {
		try {
		 ((SqlParameter)e.getParameter("case_id")).setDefaultValue("");
		} 
		catch(java.text.ParseException ignore) {
		 
		}
	}
	
	public void beforeExecuteSelect(DataObjectEvent e) {
	}
	
	public void afterExecuteSelect(DataObjectEvent e) {
	}
 }



//ibmfym_TotalRecords Label Handler Head @186-CFF37B48
 public class ibmfymibmfym_TotalRecordsLabelHandler implements ControlListener {
  public void beforeShow(Event e) {
//End ibmfym_TotalRecords Label Handler Head

//Event BeforeShow Action Retrieve number of records @187-9D59B397
  ((Control) e.getSource()).setValue( ((Grid) e.getParent()).getAmountOfRows());
//End Event BeforeShow Action Retrieve number of records

//ibmfym_TotalRecords Label Handler Tail @186-F5FC18C5
  }
 }
//End ibmfym_TotalRecords Label Handler Tail
public class ibmviolation_landibmviolationLand_TotalRecordsLabelHandler implements ControlListener {
  public void beforeShow(Event e) {
//End ibmfym_TotalRecords Label Handler Head

//Event BeforeShow Action Retrieve number of records @187-9D59B397
  ((Control) e.getSource()).setValue( ((Grid) e.getParent()).getAmountOfRows());
//End Event BeforeShow Action Retrieve number of records

//ibmfym_TotalRecords Label Handler Tail @186-F5FC18C5
  }
 }

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-86C15F31
 Page im20101_man_3Model = (Page)request.getAttribute("im20101_man_3_page");
 Page im20101_man_3Parent = (Page)request.getAttribute("im20101_man_3Parent");
 if (im20101_man_3Model == null) {
  PageController im20101_man_3Cntr = new PageController(request, response, application, "/im20101_man_3.xml" );
  im20101_man_3Model = im20101_man_3Cntr.getPage();
  im20101_man_3Model.setRelativePath("./");
  //if (im20101_man_3Parent != null) {
   //if (!im20101_man_3Parent.getChild(im20101_man_3Model.getName()).isVisible()) return;
  //}
  im20101_man_3Model.addPageListener(new im20101_man_3PageHandler());
  ((Record)im20101_man_3Model.getChild("ibmcase4")).addRecordListener(new im20101_man_3ibmcase4RecordHandler());
  ((Panel)im20101_man_3Model.getPanel("Panel1")).addControlListener(new im20101_man_3Panel1PanelHandler());
  ((Panel)im20101_man_3Model.getPanel("Panel2")).addControlListener(new im20101_man_3Panel2PanelHandler());
  ((Grid)im20101_man_3Model.getChild("ibmfym")).addGridListener(new im20101_man_3ibmfymGridHandler());
  ((Grid)im20101_man_3Model.getChild("ibmviolation_land")).addGridListener(new im20101_man_3ibmviolation_landGridHandler());
  ((Label)((Grid)im20101_man_3Model.getChild("ibmfym")).getChild("ibmfym_TotalRecords")).addControlListener(new ibmfymibmfym_TotalRecordsLabelHandler());
  ((Label)((Grid)im20101_man_3Model.getChild("ibmviolation_land")).getChild("ibmviolationLand_TotalRecords")).addControlListener(new ibmviolation_landibmviolationLand_TotalRecordsLabelHandler());
  im20101_man_3Cntr.process();
%>
<%
  if (im20101_man_3Parent == null) {
   im20101_man_3Model.setCookies();
   if (im20101_man_3Model.redirect()) return;
  } else {
   im20101_man_3Model.redirect();
  }
 }
//End Processing

%>
