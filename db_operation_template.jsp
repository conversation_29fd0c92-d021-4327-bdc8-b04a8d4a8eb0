**AI Internal Template: JSP Database Interaction**

**Goal:** Generate robust, secure, and maintainable JSP code for database operations, or explain concepts related to it.

**I. Core Components & Order of Operations (in generated code):**

1.  **Page Directives (`<%@page ... %>`):**
    *   `contentType="text/html; charset=UTF-8"` (or other appropriate)
    *   `pageEncoding="UTF-8"`
    *   `import="java.sql.*"` (Always)
    *   `import="javax.sql.DataSource, javax.naming.InitialContext, javax.naming.NamingException"` (If using JNDI/Connection Pooling)
    *   `import="java.util.*"` (If dealing with collections of data from DB)
    *   Other specific imports as needed.

2.  **Database Connection Strategy (Choose one, prioritize JNDI):**
    *   **A. JNDI DataSource (Preferred for production/robust apps):**
        *   Variables: `Context initContext = null; Context envContext = null; DataSource ds = null; Connection conn = null;`
        *   Lookup:
            ```java
            try {
                initContext = new InitialContext();
                envContext = (Context) initContext.lookup("java:/comp/env");
                ds = (DataSource) envContext.lookup("jdbc/YourDBAlias"); // Alias configured in server (e.g., context.xml)
                conn = ds.getConnection();
                // ... operations ...
            } catch (NamingException e) {
                // Handle JNDI lookup error (log, user message)
            } catch (SQLException e) {
                // Handle connection acquisition error (log, user message)
            } // ... finally block for closing conn (returns to pool) ...
            ```
    *   **B. `DriverManager` (Simpler, for basic examples/dev, less performant):**
        *   Constants (ideally from external config, but for simple JSP example, can be in `<%! ... %>` or scriptlet):
            *   `DB_URL = "************************************"`
            *   `DB_USER = "username"`
            *   `DB_PASSWORD = "password"`
            *   `DB_DRIVER = "com.driver.ClassName"`
        *   Connection:
            ```java
            Connection conn = null;
            try {
                // Class.forName(DB_DRIVER); // Often not needed for JDBC 4.0+ drivers
                conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                // ... operations ...
            } catch (SQLException e) { // Potentially ClassNotFoundException if explicitly loading driver
                // Handle connection error
            } // ... finally block for closing conn ...
            ```

3.  **Statement Preparation & Execution:**
    *   Variables: `PreparedStatement pstmt = null; ResultSet rs = null;`
    *   **`PreparedStatement` (MANDATORY for dynamic queries to prevent SQLi):**
        *   `String sql = "SELECT/INSERT/UPDATE/DELETE ... WHERE column = ? AND another_col = ?";`
        *   `pstmt = conn.prepareStatement(sql);`
        *   Parameter setting: `pstmt.setString(1, value1); pstmt.setInt(2, value2); ...`
    *   Execution:
        *   For DML (INSERT, UPDATE, DELETE): `int rowsAffected = pstmt.executeUpdate();`
        *   For DQL (SELECT): `rs = pstmt.executeQuery();`

4.  **Result Set Processing (if `rs` is used):**
    *   Loop: `while (rs.next()) { ... }`
    *   Data retrieval: `rs.getString("column_name")`, `rs.getInt(columnIndex)`, `rs.getTimestamp()`, etc.
    *   Store data in JavaBeans, Maps, or display directly (depending on context).

5.  **Transaction Management (If multiple DML operations need atomicity):**
    *   `conn.setAutoCommit(false);` (before starting operations)
    *   `conn.commit();` (after all operations succeed)
    *   `conn.rollback();` (in `catch` block if any operation fails)
    *   Ensure `setAutoCommit(true)` is reset in `finally` if changed.

6.  **Error Handling (`try-catch-finally`):**
    *   Main `try` block for all JDBC operations.
    *   `catch (SQLException e)`:
        *   Log `e.getMessage()`, `e.getSQLState()`, `e.getErrorCode()`.
        *   Provide a user-friendly error message.
        *   Rollback transaction if active.
    *   `catch (Exception e)`: General catch-all for unexpected issues (log).
    *   `finally` block: **Crucial for resource cleanup.**

7.  **Resource Cleanup (in `finally` block, LIFO - Last In, First Out):**
    *   `if (rs != null) try { rs.close(); } catch (SQLException e) { /* log or ignore */ }`
    *   `if (pstmt != null) try { pstmt.close(); } catch (SQLException e) { /* log or ignore */ }`
    *   `if (conn != null) try { conn.close(); } catch (SQLException e) { /* log or ignore */ }`
        *   *(Note: For DataSource connections, `conn.close()` typically returns the connection to the pool, not fully closes it.)*

**II. Best Practices to Emphasize to the User:**

*   **Connection Pooling:** Strongly recommend for any real application.
*   **`PreparedStatement`:** Always use for queries with user-supplied or dynamic data to prevent SQL Injection.
*   **Resource Management:** Stress the importance of closing `ResultSet`, `Statement`, and `Connection` in a `finally` block.
*   **Separation of Concerns (MVC/DAO):**
    *   Explain that JSPs are primarily for presentation.
    *   Suggest moving database logic to Servlets and/or Data Access Object (DAO) classes.
    *   This makes code cleaner, more testable, and maintainable.
*   **Error Handling:**
    *   Log detailed errors for developers (server-side logs).
    *   Show generic, user-friendly error messages to the end-user.
*   **Configuration:** Store database connection details externally (e.g., `web.xml` context params, server's `context.xml` for JNDI, properties files) rather than hardcoding in JSPs.
*   **Input Validation:** Validate all incoming data (from forms, URLs) *before* using it in SQL queries, even with `PreparedStatement`.

**III. Output Structure (when generating a full JSP example):**

1.  JSP Page Directives.
2.  HTML head, title, basic CSS (if applicable for a runnable example).
3.  HTML body.
4.  Scriptlet block (`<% ... %>`) containing:
    *   Variable declarations (connection, statement, resultset, data holders).
    *   `try-catch-finally` block.
        *   Connection acquisition.
        *   SQL statement preparation.
        *   Parameter setting.
        *   Execution.
        *   Result processing & storing/displaying data (using expressions `<%= ... %>` or JSTL if appropriate).
        *   Error handling logic.
        *   Resource cleanup in `finally`.
5.  Display of results or status messages in HTML.
