//var temp_date = Date.now();  clickDL
var isFirst = true;
// label_mod  editable_mod->編輯模式
// my_ib_prcs acc_rlt_now prcsTyp
const my_ib_prcs = $("[name='ib_prcs']").val();
const acc_rlt_now = $("[name='acc_rlt_now']").val();
const ibmcasecase_id = $("#ibmcasecase_id").val();
const cnt_349 = $("[name='cnt_349']").val();

const prcsTyp = acc_rlt_now.substring(0, 1);
let CHK_AD_FIRST = false;
window.onload = function () {
    ezekCustomOnload();
};

function ezekCustomOnload() {

    if(my_ib_prcs == "B" && prcsTyp == "3" && cnt_349 == "0"){
		CHK_AD_FIRST = true;
	}
	// 科長還是會往上陳
	if( $("[name='JOB_TITLE']") && "004" == $("[name='JOB_TITLE']").val()){
		//$("#ACC_RLT_UP").hide();
	}
 
	if (prcsTyp == "2" ) {
        $(".stp_2").hide();
        $(".stp_3").hide();
        $("#ttab1").addClass("in active");
        $("#stp_0_li").addClass("active");
    } else if (prcsTyp == "3") {
        $(".stp_3").hide();
        $("#ttab3").addClass("in active");
        $("#stp_2_li").addClass("active");
    } else if (prcsTyp == "4") {
        $("#ttab4").addClass("in active");
        $("#stp_3_li").addClass("active");
    }

    var options = {
        url: "data-original"
    };
    if ($("#case_photo_switch").length) {
        const gallery_case_photo = new Viewer(document.getElementById("case_photo_content"), options);
    }
    if ($("#result_photo_switch").length) {
        const gallery_result_photo = new Viewer(document.getElementById("result_photo_content"), options);
    }

    //拆除優先類組ListBox改變值
    var $DsortListBox = $("#ibmcaseDSORT");
    $DsortListBox.on("change", function () {
        var $thisVal = $(this).find(":selected").val();
        setDsort2Option($thisVal);
    });

    if (isFirst) {
        var edit_dis_type = $("#ibmcaseedit_dis_type").val();
        setDsort2Option(edit_dis_type);
        $("#ibmcaseDSORT").val(edit_dis_type);
    }

    ini_step1_view();
    ini_step2_view();
    ini_step3_view();
    ini_boss_input();
    closeBlockUI();
    ini_zone_code();
} // END  ezekCustomOnload
function ini_boss_input() {
    var _ar = $("#ibmcasebak_acc_rlt");
    if (_ar && _ar.val()) $("input[type=radio][name=ACC_RLT_RD]").filter('[value="' + $("#ibmcasebak_acc_rlt").val() + '"]').prop("checked", true);

    var _am = getUrlParameter('tmp_acc_memo');
    $("#ibmcaseacc_memo").val(_am);


}

function ini_step1_view() {
    // 施工狀態
    $("input[type=radio][name=COMPLITE]").filter('[value="' + $("#ibmcaseedit_finish_state").val() + '"]').prop("checked", true);
    // 是否符合強制收費
    $("input[type=radio][name=PAY]").filter('[value="' + $("#ibmcaseedit_need_pay").val() + '"]').prop("checked", true);
    // 違建建造行為
    $("input[type=radio][name=BUILD_HOBY]").filter('[value="' + $("#ibmcaseedit_building_category").val() + '"]').prop("checked", true);
    // 重建種類 	
    $("input[type=radio][name=REBUILD]").filter('[value="' + $("#ibmcaseedit_rebuild_kind").val() + '"]').prop("checked", true);
	
	// 相對位置
	var BWAY = $('input[name=edit_DIS_B_WAY]').val();
    var _DIS_B_WAY = BWAY.split("、");
	var $IBFORWORD = $('input:checkbox[name=IBFORWORD]');
    $.each(_DIS_B_WAY, function(i, val) {
        $IBFORWORD.filter('[value="' + val + '"]').prop('checked', true);
    });
	
    if ($("#B_FLOOR_ADD")) {
        // 加頂加樓層
        $("#B_FLOOR_ADD").click(function () {
            var BF = $("#ibmcaseB_FLOOR").find(":selected").val();
            if (BF != "") {
                var DIS_B_FL = $("#ibmcaseedit_DIS_B_FL").val();
                var extFloor = DIS_B_FL.indexOf(BF);
                if (extFloor >= 0) {
                    alert("該樓層已存在,無法新增。");
                } else {
                    if (DIS_B_FL != null && DIS_B_FL != "") {
                        DIS_B_FL = DIS_B_FL + "、"
                    }
                    $("#ibmcaseedit_DIS_B_FL").val(DIS_B_FL + BF);
                }
            }
        });
    }

	if(my_ib_prcs == 'B'){
		$("#ad_resul_tr").find("input").prop("disabled", true);
		$("#ibmcaseedit_ad_chk_rslt").prop("disabled", true);
		 // 違建流程一開始因儲存的資料做變動
        var AD_TYP = $('input[name=ad_typ]').val();
        if(AD_TYP == "A"){
            $(".nomal").show();
            $(".danger").hide();
        }else{
        	if(AD_TYP == "B" || AD_TYP == "C"){
        		$(".danger").show();
            	$(".nomal").hide();
        	}
            else{
            	$(".nomal").show();
            	$(".danger").hide();
            }
	    }
		
		//廣告物型式
		var AD_KIND = $('input[name=edit_ad_kind]').val();
			var AD_K = AD_KIND.substring(0, 1);
		var $ADK = $('input:radio[name=ADK]');
		$ADK.filter('[value="' + AD_K + '"]').prop('checked', true);

		//廣告物型式
		var ee = $('input[name=edit_ad_kind]').val();
		var _AD_KIND = ee.split(";");
		var $checkbox = $('input:checkbox[name=AD_CT]');
		$.each(_AD_KIND, function(i, val) {
			$checkbox.filter('[value="' + val + '"]').prop('checked', true);
		});
			
			//廣告物勘查結果-涉及違反法條
			var ACL = $('input[name=edit_ad_chk_law]').val();
			var _AD_CHK_LAW = ACL.split(";");
			
			var $AD_L = $('input:checkbox[name=AD_L]');


		$.each(_AD_CHK_LAW, function(i, val) {

			$AD_L.filter('[value="' + val + '"]').prop('checked', true);
		});
		
		//材質尺寸高度(或縱長)是否為以上
		var edit_ad_height_up = $('input[name=edit_ad_height_up]').val();
		var $checks_ad_h_up = $('input:checkbox[name=AD_H_UP]');
		$checks_ad_h_up.filter('[value="' + edit_ad_height_up + '"]').prop('checked', true);
		
		// 勘查結果-形式  
		var $radio_ADK = $('input:radio[name=ADK]');
		var $checkbox_AD_CT = $('input:checkbox[name=AD_CT]');
        $("[name=AD_CT]").change(function() {
        if($(this).val() == "A1" || $(this).val() == "A2"){
                $radio_ADK.filter('[value="A"]').prop('checked', true);
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $('input[name=edit_ad_kind_memo]').val("");
        }else if($(this).val() == "B1" || $(this).val() == "B2"){
                $radio_ADK.filter('[value="B"]').prop('checked', true);
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
                $('input[name=edit_ad_kind_memo]').val("");
        }
        });

        $("[name=ADK]").change(function() {
                if($(this).val() == "A"){
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $('input[name=edit_ad_kind_memo]').val("");
            }
            else if($(this).val() == "B"){
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
                $('input[name=edit_ad_kind_memo]').val("");
            }
            else{
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
            }
        });
	}

}

function ini_step2_view() {
    // 排拆文號
    var LINKAGE_FIELDS = [
        ["ibmcaseinput_dis_regnum", "ibmcaseview_dis_regnum", "ibmcaseedit_dis_reg_yy", "ibmcaseedit_dis_reg_no"]
    ];
    var linkageFieldsLen = LINKAGE_FIELDS.length,
        linkageField = [];
    var idx = 0,
        initiatorFieldId = "",
        $initiator = {};

    for (idx = 0; idx < linkageFieldsLen; idx++) {
        linkageField = LINKAGE_FIELDS[idx];
        initiatorFieldId = linkageField[0];

        inputsLinked(initiatorFieldId, linkageField[1], linkageField[2], linkageField[3]);

        $initiator = $("#" + initiatorFieldId);
        if ($initiator.val() !== "") {
            $initiator.trigger("input");
        }
    }
    // 拆除時間通知單-發文字
    var $edit_dis_notice_word = $("#ibmcaseedit_dis_notice_word"),
        dis_notice_word = $("#ibmcasedis_notice_word1").val();
    if (dis_notice_word.length > 0) {
        $edit_dis_notice_word.val(dis_notice_word);
    }
}


function ini_step3_view() {
    // 結案方式 radio button
    $("input[type=radio][name=END_WAY]").on("change", function () {
        if (this.value == '02') {} else {
            // Clear 委外執行-listbox
            $(".b_company").val("");
        }
    });
    // 委外執行-listbox
    $(".b_company").on("change", function () {
        // Select 結案方式 = 委外執行
        $("input[type=radio][name=END_WAY][value='02']").prop("checked", true);
    });
    // 警方配合
    $("input[type=radio][name=need_plc]").filter('[value="' + $("#ibmcaseedit_need_plc").val() + '"]').prop("checked", true);
    // Set textboxes of 拆除情形 to disable
    $(".chk_input").prop("disabled", true);
    // 拆除方式
    $("input[type=radio][name=NOTICE_WAY]").filter('[value="' + $("#ibmcaseedit_b_notice_way").val() + '"]').prop("checked", true);
    // 結案方式
    $("input[type=radio][name=END_WAY]").filter('[value="' + $("#ibmcaseedit_end_way").val() + '"]').prop("checked", true);


}

function showPopup(case_id, pic_kind, pic_seq) {
    var url = "in10101_man_5.jsp?";
    url += "EXP_NO=" + case_id;
    url += "&Img_kind=" + pic_kind;
    url += "&Img_index=" + pic_seq

    $.fancybox.open({
        margin: 10,
        padding: 2,
        href: encodeURI(url),
        type: "iframe",
        iframe: {
            scrolling: false,
            preload: true
        },
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        titleShow: false,
        overlayOpacity: 0.3,
        width: 1000,
        height: 688,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function () {}
    });
}

function gogoDownload(_url) {
    var aa = confirm("是否確定下載通知書");
    if (aa && _url) {
        window.open(_url, '_blank');
    }
}

function setRLT(_vv) {
    if (_vv) $("[name='acc_rlt']").val(_vv);
}


function goEditMode() {
    // my_ib_prcs acc_rlt_now prcsTyp

    $(".eidtModeStart").hide();
    // form 的送出
    $("#ibmcaseButton_Update").hide();
    $(".eidtModeBtn").show();
	// 

    $(".acc_rlt_now_" + prcsTyp + " .label_mod_show").hide();
    $(".acc_rlt_now_" + prcsTyp + " .edit_mod_show").show();

	if(CHK_AD_FIRST){
		// 廣拆科 第一次陳核
	
		$(".acc_rlt_now_2 .label_mod_show").hide();
		$(".acc_rlt_now_2 .edit_mod_show").show();
		$("#ad_resul_tr").find("input").prop("disabled", false);
		$("#ibmcaseedit_ad_chk_rslt").prop("disabled", false);
		
		
	}
    if (prcsTyp == "2" || CHK_AD_FIRST) {
        /*
        $(".combobox_QQ").chosen({
        	search_contains: true
        });
        */
        // 地址填寫方式預設
        var dis_b_addmod = $('input[name=edit_DIS_B_ADDMOD]').val();
        if (dis_b_addmod === "L") {
            addrType = 2;
            changeAddrSwitch();
        } else if (dis_b_addmod === "H") {
            addrType = 1;
            changeAddrSwitch();
        }
    } else if (prcsTyp == "3") {

    } else if (prcsTyp == "4") {

    }


}

function editModeSave(_mod) {
    if (_mod == "save") {
        var p = confirm("是否確定修改案件資料?");
        if (p) {
			//
			if($('[name=edit_DIS_B_ADDZON]').find(":selected").val()){
				saveEditData();
			} else{
				alert("[行政區] 欄位不得空白.");
			}
		}
    } else {
        $(".eidtModeStart").show();
        $(".eidtModeBtn").hide();
        $(".label_mod_show").show();
        $(".edit_mod_show").hide();
        $("#ibmcaseButton_Update").show();
		
		ini_step1_view();
    }

}

function saveEditData() {

	submitSet();
	var $radioButtons = $("input[type=radio]");
    var ttSQ = "",
        edit_LANDNM = "",
        edit_PROJECT = "";
	if(CHK_AD_FIRST){
		$(".acc_rlt_now_2 [name^='edit_']").each(function () {
			//console.log($(this).attr("name"));
			var _name = $(this).prop("name"),
				_val = $(this).val();
			if (_name.indexOf("_date") > 0 || _name.indexOf("rvldate") > 0) _val = _val.replace(/\D/g, "");
			if (ttSQ) ttSQ += "!@#"
			ttSQ += _name + "eZeK" + _val
		});	
	}
    $(".acc_rlt_now_" + prcsTyp + " [name^='edit_']").each(function () {
        //console.log($(this).attr("name"));
        var _name = $(this).prop("name"),
            _val = $(this).val();
        if (_name.indexOf("_date") > 0 || _name.indexOf("rvldate") > 0) _val = _val.replace(/\D/g, "");
        if (ttSQ) ttSQ += "!@#"
        ttSQ += _name + "eZeK" + _val

    });
    //console.log(ttSQ);

    var tmp_acc_date = $("#ibmcaseacc_date").val();
    if (tmp_acc_date) tmp_acc_date = tmp_acc_date.replace(/\D/g, "");
    var tmp_acc_memo = $("#ibmcaseacc_memo").val();
    var tmp_choice_acc_rlt = $radioButtons.filter("[name=ACC_RLT_RD]:checked").val();

    var bakVal = "";
    //case_id;tmp_acc_date;tmp_acc_memo;tmp_choice_acc_rlt
    bakVal += "&tmp_acc_date=" + tmp_acc_date;
    bakVal += "&tmp_acc_memo=" + tmp_acc_memo;
    if (tmp_choice_acc_rlt) bakVal += "&tmp_choice_acc_rlt=" + tmp_choice_acc_rlt;
    runBlockUI();
    $.ajax({
        url: encodeURI("im50101_man_saveCase.jsp"),
        type: "POST",
        data: {
            CASE_ID: ibmcasecase_id,
            DDA: ttSQ,
            edit_LANDNM: getLANDNM(),
            edit_PROJECT: getPROJECT_DATA(),
            upd_Mod: "stp" + prcsTyp


        }
    }).done(function (_data) {
        //console.log(_data);
        alert("案件資料編輯成功!");
        window.location = encodeURI("im50101_man_" + my_ib_prcs + ".jsp?case_id=" + ibmcasecase_id + bakVal);
    }).fail(function (_data) {
        		  console.log(_data);
        alert("案件資料編輯失敗!");
    }).always(function () {
        closeBlockUI();
        editModeSave("cancel");
    });

}

function submitSet(){
	
	// 相對位置
	var IBFORWORD = "";
	 $('input[name=IBFORWORD]:checked').each(function(){
		this_val = $(this).attr("value") ;
		if(IBFORWORD) IBFORWORD+= "、";
		IBFORWORD += this_val;
	});
	$('input[name=edit_DIS_B_WAY]').val(IBFORWORD);
	
	
	 //console.log(" save Edit Data");
    var $radioButtons = $("input[type=radio]");
    // 施工狀態
    $("#ibmcaseedit_finish_state").val($radioButtons.filter("[name=COMPLITE]:checked").val());
    // 是否符合強制收費
    $("#ibmcaseedit_need_pay").val($radioButtons.filter("[name=PAY]:checked").val());
    // 違建建造行為
    $("#ibmcaseedit_building_category").val($radioButtons.filter("[name=BUILD_HOBY]:checked").val());
    // 重建種類 	
    $("#ibmcaseedit_rebuild_kind").val($radioButtons.filter("[name=REBUILD]:checked").val());

    var DSORT = $("#ibmcaseDSORT");
    var DSORT2 = $("#ibmcaseDSORT2");

    if (DSORT.find(":selected").val() != "") {
        $('input[name=edit_dis_type]').val(DSORT.val());
        $('input[name=DIS_TYPE_DESC]').val(DSORT.find(":selected").text());
    }

    if (DSORT2.find(":selected").val() != "") {
        $('input[name=edit_dis_sort]').val(DSORT2.val());
        $('input[name=DIS_SORT_ITEM]').val(DSORT2.find(":selected").text());
    }

    // 是否繳費
    var PAY = $('input[name*=PAY]:checked').val();
    $('input[name=edit_need_pay]').val(PAY);


    // 警方配合
    $("#ibmcaseedit_need_plc").val($radioButtons.filter("[name=need_plc]:checked").val());
    // 拆除方式
    $("#ibmcaseedit_b_notice_way").val($radioButtons.filter("[name=NOTICE_WAY]:checked").val());
    // 結案方式
    $("#ibmcaseedit_end_way").val($radioButtons.filter("[name=END_WAY]:checked").val());
	
	
	if(my_ib_prcs == 'B'){
		// 一、型式
		var ADK = $('input[name*=ADK]:checked').val();
        if(ADK != "Z" ) $('input[name=edit_ad_kind_memo]').val(""); 
		 //廣告物型式
		var AD_CT = "", check_site = 0, check_other=0, this_val="";
		$('input[name*=AD_CT]:checked').each(function(){
				this_val = $(this).attr("value") ;
				if(AD_CT) AD_CT+= ";";
				
				AD_CT += this_val;
		});
		var AD_KIND_MEMO =  $('input[name=edit_ad_kind_memo]').val();
		if(AD_KIND_MEMO != ""){
				AD_CT = "Z";
		}
		$('input[name=edit_ad_kind]').val(AD_CT);
		//廣告物勘查結果-涉及違反法條
		var AD_L = "";
		$('input[name*=AD_L]:checked').each(function(){
			this_val = $(this).attr("value") ;
			if(AD_L) AD_L+= ";";
			AD_L += this_val;
		});
		$('input[name=edit_ad_chk_law]').val(AD_L);
		//材質尺寸高度(或縱長)是否為以上
		var AD_H_UP = $('input[name*=AD_H_UP]:checked').val();
		if(!AD_H_UP)AD_H_UP = "N";
		$('input[name=edit_ad_height_up]').val(AD_H_UP);
		
	}
	
}



function getPROJECT_DATA() {
    var CSPRJ = "";

    // 下水道
    if (my_ib_prcs == "C") {
        CSPRJ = $('[name=PROJECT] :selected').val() + "-" + $('[name=PROJECT] :selected').text();
    } else { // 一般 廣拆
          $(".PROJECT_DATA").each(function () {
            var ext_PRJ_ID = $(this).attr("prj_code");
			var ext_PRJ = $(this).attr("prj_nm");
			var ext_PRJ_YY = $(this).attr("prj_yy");
			// 檢查 ext_PRJ_YY 是否為 undefined，根據結果決定是否包含在 ext_Project 中
            var ext_Project = ext_PRJ_ID + "-" + ext_PRJ;

            if (typeof ext_PRJ_YY !== 'undefined') {
                ext_Project += "-" + ext_PRJ_YY;
            }
            if (CSPRJ) CSPRJ += ";";
            CSPRJ += ext_Project;
        });
        //$('input[name=CSPRJ]').val(CSPRJ);
    }
    return CSPRJ;

}

function getLANDNM() {
    var CSLAN = "";
    $(".ZONE_AREA_LIST").each(function () {
        var ext_ZC_V = $(this).attr("dist_desc");
        var ext_ZC = $(this).attr("dist");
        var ext_SN = $(this).attr("section_nm");
        var ext_SN_V = $(this).attr("section");
        var ext_NO1 = $(this).attr("road_no1");
        var ext_NO2 = $(this).attr("road_no2");
        var ext_Land = ext_ZC_V + "-" + ext_SN_V + "-" + ext_SN + "-" + ext_NO1 + "-" + ext_NO2 + "-" + ext_ZC;

        if (CSLAN) CSLAN += ";";
        CSLAN += ext_Land;


    });
    $('input[name=CSLAN]').val(CSLAN);
    return CSLAN;

}


function refleshDate() {
    if (prcsTyp == "2") {

    } else if (prcsTyp == "3") {

    } else if (prcsTyp == "4") {

    }
}

function changeAddrSwitch() {
    if (addrType == 1) {
        $(".byHand").show();
        $("#s_ROAD_chosen").hide();
        $("#s_LANE_chosen").hide();
        $("#s_ALLEY_chosen").hide();
        addrType = 2;
        $("#addrSwitchLink").text("下拉式選單");
    } else {
        $("#s_ROAD_chosen").show();
        $("#s_LANE_chosen").show();
        $("#s_ALLEY_chosen").show();
        $(".byHand").hide();
        addrType = 1;
        $("#addrSwitchLink").text("手填");
    }
}

/**
 * 拆除優先類組ListBox連動拆除優先子類組ListBox.
 **/
function setDsort2Option(_Dsort) {
    // ibmcaseDSORT
    var listbox = $("#ibmcaseDSORT2");

    // 暫時停用Listbox
    listbox.prop("disabled", true).trigger("chosen:updated");
    listbox.removeOptions();

    $.post("function_getData.jsp", {
        boundColumn: "SUBSTRING(CODE_SEQ,2)",
        boundColumnAlias: "BNDCOLUMN",
        textColumn: "CODE_SEQ || ' : ' || CODE_DESC",
        textColumnAlias: "TXTCOLUMN",
        tableName: "IBMCODE",
        whereString: " CODE_TYPE = 'DSORT2' AND CODE_SEQ <> '**' AND SUB_SEQ = '" + _Dsort + "' AND IS_DEL = 'N'",
        orderByString: "ORDER_BY_SEQ"
    }, function (jData) {
        // 利用給予的textColumn及boundColumn建立一個新的Option
        listbox.fillOptions(jData);
    }, "json").always(function () {
        // 將Listbox恢復為正常狀態
        listbox.prop("disabled", false).trigger("chosen:updated");

        if (typeof callback === "function") {
            callback();
        }

        if (isFirst) {
            var dsort2_H = $("#ibmcaseedit_dis_sort").val();
            $("#ibmcaseDSORT2").val(dsort2_H);
            isFirst = false;
        }

    });
}

/**
 * 標案行政區ListBox連動標案名稱ListBox.
 **/
function setIbmNameOption(_bidzon) {

    var listbox = $("#ibmcaseBID_NAME");

    // 暫時停用Listbox
    listbox.prop("disabled", true).trigger("chosen:updated");
    listbox.removeOptions();

    $.post("function_getData.jsp", {
        boundColumn: "CODE_SEQ",
        boundColumnAlias: "BNDCOLUMN",
        textColumn: "CODE_DESC",
        textColumnAlias: "TXTCOLUMN",
        tableName: "IBMCODE",
        whereString: " CODE_TYPE = 'BIDNM' AND CODE_SEQ <> '**' AND CODE_DESC like '%" + _bidzon + "%' AND IS_DEL = 'N'",
        orderByString: "ORDER_BY_SEQ, CODE_DESC"
    }, function (jData) {
        // 利用給予的textColumn及boundColumn建立一個新的Option
        listbox.fillOptions(jData);
    }, "json").always(function () {
        // 將Listbox恢復為正常狀態
        listbox.prop("disabled", false).trigger("chosen:updated");

        if (typeof callback === "function") {
            callback();
        }

    });
}



var ak = "2050600_1676";
// 行政區
var distUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getTownList";
// 地段
var landSecUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getLandSection";
// 地址定位URL
var sCity = "新北市";
// 道路
var roadUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getAddressRoad";
// 巷
var laneUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getAddressLane";
// 弄
var longUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getAddressLong";
// 關鍵字
var hnumUrl = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + ak + "&Cmd=getKeywordLoc";

function ini_zone_code() {

    initAddrListBox();

    var $LANDSECZONE_CODE = $("[name=ZONE_CODE]"),
        $LANDSECSEC_NAME = $("[name=SEC_NAME]");

    // 地號 - 行政區 change 測試用暫時mark掉 之後轉正式機要打開
    $("[name=ZONE_CODE]").change(function () {
        var $thisVal = $(this).find(":selected").text();
        //chang_color($(this));
        $LANDSECSEC_NAME.removeOptions().trigger("chosen:updated");
        if ($thisVal !== "") {
            $LANDSECSEC_NAME.prop("disabled", true);
            $.ajax({
                url: landSecUrl,
                type: "get",
                dataType: "xml",
                data: {
                    town: $thisVal
                }, //{random: Math.floor(Math.random()*1000000)},
                success: function (data) {
                    var jdata = [];
                    $(data).find("SECTION").each(function (i) {
                        jdata.push({
                            boundColumn: $(this).attr("DATA"),
                            textColumn: $(this).text()
                        });
                    });
                    // 地籍定位 - 地段
                    $LANDSECSEC_NAME.fillOptions(jdata);
                },
                error: function (edata) {
                    console.log("error");
                    console.log(edata);
                },
                complete: function () {
                    $LANDSECSEC_NAME.prop("disabled", false).trigger("chosen:updated");
                }
            });
        }
    });
    //------------------------------------------------------------------------
    // init combobox 內容 (地址)
    //------------------------------------------------------------------------
    function chang_color(_dom) {
        var tmpV = _dom.attr("id");
        var $thisVal = _dom.find(":selected").val();
        if ($thisVal != null && $thisVal != "") {
            $("#" + tmpV + "_chosen").find("a").addClass("font_color_black");
        } else {
            $("#" + tmpV + "_chosen").find("a").removeClass("font_color_black");
        }
    }
    /**
     * 輸入地址的 combobox
     *
     */
    var $Panel2DORNUMSearchs_DIST = $("[name=edit_DIS_B_ADDZON]"),
        $Panel2DORNUMSearchs_ROAD = $("[name=s_ROAD]");
    var $Panel2DORNUMSearchs_LANE = $("[name=s_LANE]"),
        $Panel2DORNUMSearchs_ALLEY = $("[name=s_ALLEY]");
    var temp_DIST = $("[name=edit_DIS_B_ADDZON]").find(":selected").text(),
        temp_ROAD = $("[name=edit_DIS_B_ADD2]").val(),
        temp_LANE = $("[name=edit_DIS_B_ADD3]").val(),
        temp_ALLEY = $("[name=edit_DIS_B_ADD4]").val();



    //未輸入前項資料不給挑
    $Panel2DORNUMSearchs_ROAD.removeOptions().trigger("chosen:updated");
    $Panel2DORNUMSearchs_LANE.removeOptions().trigger("chosen:updated");
    $Panel2DORNUMSearchs_ALLEY.removeOptions().trigger("chosen:updated");

    // 行政區-下拉選項變更數值事件
    $("[name=edit_DIS_B_ADDZON]").change(function () {
        //addS1Listener();
        var $thisVal = $(this).find(":selected").text();
        chang_color($(this));
        $("#pac-input").val(""); //清空 google searchBox  
        $Panel2DORNUMSearchs_ROAD.removeOptions().trigger("chosen:updated");
        $Panel2DORNUMSearchs_LANE.removeOptions().trigger("chosen:updated");
        $Panel2DORNUMSearchs_ALLEY.removeOptions().trigger("chosen:updated");
        $("[name=edit_DIS_B_ADD2]").val("");
        $("[name=edit_DIS_B_ADD3]").val("");
        $("[name=edit_DIS_B_ADD4]").val("");

        if ($thisVal !== "") {

            temp_DIST = $thisVal;
            $("[name=edit_DIS_B_ADD_DESC]").val($thisVal);
            // 帶出道路名稱-下拉選項
            $Panel2DORNUMSearchs_ROAD.prop("disabled", true);
            $.ajax({
                url: roadUrl,
                type: "get",
                dataType: "xml",
                data: {
                    town: $thisVal
                },
                success: function (data) {

                    var jdata = [];
                    $(data).find("ROAD").each(function (i) {
                        jdata.push({
                            boundColumn: $(this).text(),
                            textColumn: $(this).text()
                        });
                    });
                    // 行政區下道路給值
                    $Panel2DORNUMSearchs_ROAD.fillOptions(jdata);
                },
                error: function (edata) {},
                complete: function () {
                    $Panel2DORNUMSearchs_ROAD.prop("disabled", false).trigger("chosen:updated");
                }
            });
        }
    });
    // 道路-下拉選項變更數值事件
    $("[name=s_ROAD]").change(function () {
        var $thisVal = $(this).find(":selected").val();
        chang_color($(this));
        $Panel2DORNUMSearchs_LANE.removeOptions().trigger("chosen:updated");
        $Panel2DORNUMSearchs_ALLEY.removeOptions().trigger("chosen:updated");
        $("[name=edit_DIS_B_ADD3]").val("");
        $("[name=edit_DIS_B_ADD4]").val("");

        if ($thisVal !== "") {
            temp_ROAD = $thisVal;
            // 帶出巷-下拉選項
            $Panel2DORNUMSearchs_LANE.prop("disabled", true);
            $.ajax({
                url: laneUrl,
                type: "get",
                dataType: "xml",
                data: {
                    town: temp_DIST,
                    addressRoad: $thisVal
                },
                success: function (data) {

                    var jdata = [];
                    $(data).find("LANE").each(function (i) {
                        jdata.push({
                            boundColumn: $(this).text(),
                            textColumn: $(this).text()
                        });
                    });
                    // 道路下的巷
                    $Panel2DORNUMSearchs_LANE.fillOptions(jdata);
                    $("[name=edit_DIS_B_ADD2]").val($thisVal);
                },
                error: function (edata) {},
                complete: function () {
                    $Panel2DORNUMSearchs_LANE.prop("disabled", false).trigger("chosen:updated");
                }
            });
        }
    });
    // 巷-下拉選項變更數值事件
    $("[name=s_LANE]").change(function () {
        var $thisVal = $(this).find(":selected").val();
        chang_color($(this));
        $Panel2DORNUMSearchs_ALLEY.removeOptions().trigger("chosen:updated");
        $("[name=edit_DIS_B_ADD4]").val("");

        if ($thisVal !== "") {
            temp_LANE = $thisVal;
            $Panel2DORNUMSearchs_ALLEY.prop("disabled", true);
            // 帶出弄-下拉選項
            $.ajax({
                url: longUrl,
                type: "get",
                dataType: "xml",
                data: {
                    town: temp_DIST,
                    addressRoad: temp_ROAD,
                    addressLane: $thisVal
                },
                success: function (data) {

                    var jdata = [];
                    $(data).find("LONG").each(function (i) {
                        jdata.push({
                            boundColumn: $(this).text(),
                            textColumn: $(this).text()
                        });
                    });
                    // 巷下面的弄
                    $Panel2DORNUMSearchs_ALLEY.fillOptions(jdata);
                    $("[name=edit_DIS_B_ADD3]").val($thisVal);
                },
                error: function (edata) {},
                complete: function () {
                    $Panel2DORNUMSearchs_ALLEY.prop("disabled", false).trigger("chosen:updated");
                }
            });
        }
    });

    $("[name=s_ALLEY]").change(function () {
        var $thisVal = $(this).find(":selected").val();
        $("[name=edit_DIS_B_ADD4]").val($thisVal);
    });






    // 加地號
    var landindex = 0;
    $("#ZONE_AREA_ADD").click(function () {

        landindex++;
        var isLandExist = false;
        var tmp_ZC = $("#ibmcaseZONE_CODE").find(":selected").text();
        var tmp_ZC_V = $("#ibmcaseZONE_CODE").find(":selected").val();
        var tmp_SN = $("#ibmcaseSEC_NAME").find(":selected").text();
        var tmp_SN_V = $("#ibmcaseSEC_NAME").find(":selected").val();
        var tmp_NO1 = $("#ibmcaseROAD_NO1").val();
        var tmp_NO2 = $("#ibmcaseROAD_NO2").val();

        tmp_NO1 = addZeroCustom(tmp_NO1, 4);
        tmp_NO2 = addZeroCustom(tmp_NO2, 4);

        if (tmp_ZC_V != "" && tmp_ZC_V && tmp_SN_V != "" && tmp_SN_V) {
            $(".ZONE_AREA_LIST").each(function () {
                var ext_ZC_V = $(this).attr("dist");
                var ext_SN = $(this).attr("section_nm");
                var ext_SN_V = $(this).attr("section");
                var ext_NO1 = $(this).attr("road_no1");
                var ext_NO2 = $(this).attr("road_no2");
                var ext_indx = $(this).attr("landindex");

                landindex = parseInt(ext_indx) + 1;
				console.log("ext_ZC_V::" + ext_ZC_V +"[ext_SN]" + ext_SN +"[ext_SN_V]" +ext_SN_V+ "[ext_NO1]" +ext_NO1+ "[ext_NO2]"+ext_NO2 );
				console.log("tmp_ZC_V::" + tmp_ZC_V +"[tmp_SN]" + tmp_SN +"[tmp_SN_V]" +tmp_SN_V+ "[tmp_NO1]" +tmp_NO1+ "[tmp_NO2]"+tmp_NO2 );
                if (ext_ZC_V === tmp_ZC_V && tmp_SN === ext_SN && tmp_SN_V === ext_SN_V && ext_NO1 === tmp_NO1 && ext_NO2 === tmp_NO2) {
                    isLandExist = true;
                }

            });

            if (isLandExist) {
                alert("該地號已存在,無法新增。");
            } else {
                $("#ZONE_AREA").append("<div class='ZONE_AREA_LIST' landindex='" + landindex + "' dist='" + tmp_ZC_V + "' dist_desc='" + tmp_ZC + "' section='" + tmp_SN_V + "' section_nm='" + tmp_SN + "' road_no1='" + tmp_NO1 + "' road_no2='" + tmp_NO2 + "'>" + tmp_ZC + tmp_SN + tmp_NO1 + "-" + tmp_NO2 + "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='LAND_DELETE' onclick=\"delLan('" + landindex + "');\"></div>");
                //checkCslan(tmp_ZC_V, tmp_SN_V, tmp_NO1, tmp_NO2);
            }
        } else {
            alert("尚未輸入完整地號,無法新增。");
        }
    });
} // END ini_zone_code();





//------------------------------------------------------------------------
// 把半形字串轉成全形
// @return 全形字串
//------------------------------------------------------------------------
String.prototype.halfToFull_2 = function halfToFull() {
    var temp = "";
    for (var i = 0; i < this.toString().length; i++) {
        var charCode = this.toString().charCodeAt(i);
        if (charCode <= 126 && charCode >= 33) {
            charCode += 65248;
        } else if (charCode == 32) { // 半形空白轉全形
            charCode = 12288;
        }
        temp = temp + String.fromCharCode(charCode);
    }
    return temp;
};
//------------------------------------------------------------------------
// 寫入資料至 input 
//------------------------------------------------------------------------
function topFrame(data) {
    var weekHtml = "";
    var weekWeather = data.wList;
    for (var idx in weekWeather) {
        var addrData = weekWeather[idx];

        weekHtml += "<option value='" + addrData.COORD_X + "," + addrData.COORD_Y + "'>" +
            addrData.DIST_DT + addrData.ROAD_DT + addrData.LANE + addrData.ALLEY + addrData.NUM +
            "</option>";
    }
    if (weekHtml == "") {
        weekHtml = " <option style='font-size:1em'>無任何資料</option>";
    }
    $("#addr_select").html(weekHtml);
}

//------------------------------------------------------------------------
// 若行政區有值，初始化路名、巷弄ListBox 
//------------------------------------------------------------------------
function initAddrListBox() {
    var $edit_DIS_B_ADDZON = $("[name=edit_DIS_B_ADDZON]").find(":selected").text();
    var $edit_DIS_B_ADD2 = $("[name=edit_DIS_B_ADD2]").val();
    var $edit_DIS_B_ADD3 = $("[name=edit_DIS_B_ADD3]").val();
    var $edit_DIS_B_ADD4 = $("[name=edit_DIS_B_ADD4]").val();

    if ($edit_DIS_B_ADDZON !== "") {
        // 帶出道路名稱-下拉選項
        $("[name=s_ROAD]").prop("disabled", true);
        $.ajax({
            url: roadUrl,
            type: "get",
            dataType: "xml",
            data: {
                town: $edit_DIS_B_ADDZON
            },
            success: function (data) {

                var jdata = [];
                $(data).find("ROAD").each(function (i) {
                    jdata.push({
                        boundColumn: $(this).text(),
                        textColumn: $(this).text()
                    });
                });
                // 行政區下道路給值
                $("[name=s_ROAD]").fillOptions(jdata);

                if ($edit_DIS_B_ADD2 !== "") {
                    $("[name=s_ROAD]").val($edit_DIS_B_ADD2);
                }
            },
            error: function (edata) {},
            complete: function () {
                $("[name=s_ROAD]").prop("disabled", false).trigger("chosen:updated");
            }
        });
    }

    if ($edit_DIS_B_ADD2 !== "") {
        // 帶出巷-下拉選項
        $("[name=s_LANE]").prop("disabled", true);
        $.ajax({
            url: laneUrl,
            type: "get",
            dataType: "xml",
            data: {
                town: $edit_DIS_B_ADDZON,
                addressRoad: $edit_DIS_B_ADD2
            },
            success: function (data) {
                var jdata = [];
                $(data).find("LANE").each(function (i) {
                    jdata.push({
                        boundColumn: $(this).text(),
                        textColumn: $(this).text()
                    });
                });
                // 道路下的巷
                $("[name=s_LANE]").fillOptions(jdata);

                if ($edit_DIS_B_ADD3 !== "") {
                    $("[name=s_LANE]").val($edit_DIS_B_ADD3);
                }
            },
            error: function (edata) {},
            complete: function () {
                $("[name=s_LANE]").prop("disabled", false).trigger("chosen:updated");
            }
        });
    }

    if ($edit_DIS_B_ADD3 !== "") {
        $("[name=s_ALLEY]").prop("disabled", true);
        // 帶出弄-下拉選項
        $.ajax({
            url: longUrl,
            type: "get",
            dataType: "xml",
            data: {
                town: $edit_DIS_B_ADDZON,
                addressRoad: $edit_DIS_B_ADD2,
                addressLane: $edit_DIS_B_ADD3
            },
            success: function (data) {
                var jdata = [];
                $(data).find("LONG").each(function (i) {
                    jdata.push({
                        boundColumn: $(this).text(),
                        textColumn: $(this).text()
                    });
                });
                // 巷下面的弄
                $("[name=s_ALLEY]").fillOptions(jdata);

                if ($edit_DIS_B_ADD4 !== "") {
                    $("[name=s_ALLEY]").val($edit_DIS_B_ADD4);
                }
            },
            error: function (edata) {},
            complete: function () {
                $("[name=s_ALLEY]").prop("disabled", false).trigger("chosen:updated");
            }
        });
    }
}

//------------------------------------------------------------------------
// 前面補零 空的也補
// 
//------------------------------------------------------------------------

function addZeroCustom(num, len) {
    var num_len = num.length;
    if (num_len < len) {
        for (var i = len - num_len; 0 < i; i--) {
            num = "0" + num;
        }
    }
    return num;
}
//  刪除地號
function delLan(_landindex) {
    $(".addrMessage_lan").remove();
    $("div[landindex=" + _landindex + "]").remove();
}

var getUrlParameter = function getUrlParameter(sParam) {
    var sPageURL = window.location.search.substring(1),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return typeof sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
        }
    }
    return "";
};