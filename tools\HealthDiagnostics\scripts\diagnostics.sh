#!/bin/bash

# Health Diagnostics Tool 啟動腳本
# 新北市違章建築管理系統

# 設定變數
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_HOME="$(dirname "$SCRIPT_DIR")"
LIB_DIR="$APP_HOME/lib"
CONFIG_DIR="$APP_HOME/config"
MAIN_CLASS="com.ntpc.violation.diagnostics.cli.DiagnosticsCLI"

# Java 設定
JAVA_OPTS="-Xms256m -Xmx512m"
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="$JAVA_OPTS -Duser.timezone=Asia/Taipei"
JAVA_OPTS="$JAVA_OPTS -Djava.util.logging.config.file=$CONFIG_DIR/logging.properties"

# 檢查 Java
if [ -z "$JAVA_HOME" ]; then
    JAVA_CMD="java"
else
    JAVA_CMD="$JAVA_HOME/bin/java"
fi

# 檢查 Java 版本
JAVA_VERSION=$("$JAVA_CMD" -version 2>&1 | awk -F '"' '/version/ {print $2}')
JAVA_MAJOR_VERSION=$(echo "$JAVA_VERSION" | cut -d'.' -f1)

if [ "$JAVA_MAJOR_VERSION" -lt 8 ]; then
    echo "錯誤: 需要 Java 8 或以上版本，目前版本為 $JAVA_VERSION"
    exit 1
fi

# 建立 classpath
CLASSPATH="$APP_HOME/src"
CLASSPATH="$CLASSPATH:$CONFIG_DIR"

# 加入所有 JAR 檔
for jar in "$LIB_DIR"/*.jar; do
    if [ -f "$jar" ]; then
        CLASSPATH="$CLASSPATH:$jar"
    fi
done

# 加入擴充 JAR 檔
if [ -d "$LIB_DIR/extensions" ]; then
    for jar in "$LIB_DIR/extensions"/*.jar; do
        if [ -f "$jar" ]; then
            CLASSPATH="$CLASSPATH:$jar"
        fi
    done
fi

# 建立報告目錄
REPORTS_DIR="$APP_HOME/reports"
if [ ! -d "$REPORTS_DIR" ]; then
    mkdir -p "$REPORTS_DIR"
fi

# 日誌目錄
LOG_DIR="$APP_HOME/logs"
if [ ! -d "$LOG_DIR" ]; then
    mkdir -p "$LOG_DIR"
fi

# 設定日誌檔案
LOG_FILE="$LOG_DIR/diagnostics_$(date +%Y%m%d_%H%M%S).log"

# 執行診斷工具
echo "啟動 Health Diagnostics Tool..."
echo "Java 版本: $JAVA_VERSION"
echo "工作目錄: $APP_HOME"
echo "日誌檔案: $LOG_FILE"
echo ""

# 執行 Java 程式
exec "$JAVA_CMD" $JAVA_OPTS \
    -classpath "$CLASSPATH" \
    -Dapp.home="$APP_HOME" \
    -Dlog.file="$LOG_FILE" \
    $MAIN_CLASS "$@" 2>&1 | tee "$LOG_FILE"