package com.ezek.report;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.*;
import java.util.*;

/**
 * 監控管理 Servlet
 * 
 * 提供 Web 介面來管理和查看 N+1 查詢監控結果
 * 
 * 功能：
 * 1. 啟用/停用監控
 * 2. 查看即時監控報告
 * 3. 執行靜態程式碼分析
 * 4. 產生大量資料集分析報告
 * 5. 下載分析結果
 */
public class MonitoringManagementServlet extends HttpServlet {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) pathInfo = "/";
        
        response.setContentType("text/html; charset=UTF-8");
        
        switch (pathInfo) {
            case "/":
                showDashboard(request, response);
                break;
            case "/report":
                showReport(request, response);
                break;
            case "/static-analysis":
                performStaticAnalysis(request, response);
                break;
            case "/dataset-analysis":
                performDatasetAnalysis(request, response);
                break;
            case "/download":
                downloadReport(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String action = request.getParameter("action");
        
        if ("enable_monitoring".equals(action)) {
            NPlusOneQueryDetector.enableMonitoring();
            QueryInterceptor.enableInterceptor();
            MonitoringInjector.enableInjection();
            
            response.getWriter().println("監控已啟用");
            
        } else if ("disable_monitoring".equals(action)) {
            NPlusOneQueryDetector.disableMonitoring();
            QueryInterceptor.disableInterceptor();
            MonitoringInjector.disableInjection();
            
            response.getWriter().println("監控已停用");
            
        } else if ("clear_stats".equals(action)) {
            NPlusOneQueryDetector.clearStats();
            response.getWriter().println("統計資料已清除");
            
        } else {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST);
        }
    }
    
    /**
     * 顯示監控儀表板
     */
    private void showDashboard(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>N+1 查詢監控系統</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".header { background: #f0f0f0; padding: 10px; border-radius: 5px; }");
        out.println(".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }");
        out.println(".button { background: #007cba; color: white; padding: 10px 15px; border: none; border-radius: 3px; cursor: pointer; margin: 5px; }");
        out.println(".button:hover { background: #005a87; }");
        out.println(".warning { color: #d9534f; font-weight: bold; }");
        out.println(".success { color: #5cb85c; font-weight: bold; }");
        out.println("table { border-collapse: collapse; width: 100%; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        
        out.println("<div class='header'>");
        out.println("<h1>新北市違章建築管理系統 - N+1 查詢監控</h1>");
        out.println("<p>CodeCharge Studio 三層架構專用監控系統</p>");
        out.println("</div>");
        
        // 系統狀態
        out.println("<div class='section'>");
        out.println("<h2>系統狀態</h2>");
        out.println("<table>");
        out.println("<tr><th>項目</th><th>狀態</th></tr>");
        out.println("<tr><td>N+1 查詢檢測</td><td><span class='success'>運行中</span></td></tr>");
        out.println("<tr><td>查詢攔截器</td><td><span class='success'>運行中</span></td></tr>");
        out.println("<tr><td>監控注入</td><td><span class='success'>運行中</span></td></tr>");
        out.println("<tr><td>資料庫連接池</td><td>主庫:80, 輔庫:100</td></tr>");
        out.println("<tr><td>系統負載</td><td>案件:37萬筆, 流程:100萬筆</td></tr>");
        out.println("</table>");
        out.println("</div>");
        
        // 控制面板
        out.println("<div class='section'>");
        out.println("<h2>控制面板</h2>");
        out.println("<form method='post' style='display:inline;'>");
        out.println("<input type='hidden' name='action' value='enable_monitoring'>");
        out.println("<button type='submit' class='button'>啟用監控</button>");
        out.println("</form>");
        
        out.println("<form method='post' style='display:inline;'>");
        out.println("<input type='hidden' name='action' value='disable_monitoring'>");
        out.println("<button type='submit' class='button'>停用監控</button>");
        out.println("</form>");
        
        out.println("<form method='post' style='display:inline;'>");
        out.println("<input type='hidden' name='action' value='clear_stats'>");
        out.println("<button type='submit' class='button'>清除統計</button>");
        out.println("</form>");
        out.println("</div>");
        
        // 快速連結
        out.println("<div class='section'>");
        out.println("<h2>功能選單</h2>");
        out.println("<a href='/monitoring/report' class='button'>即時監控報告</a>");
        out.println("<a href='/monitoring/static-analysis' class='button'>靜態程式碼分析</a>");
        out.println("<a href='/monitoring/dataset-analysis' class='button'>大量資料集分析</a>");
        out.println("</div>");
        
        // 最近檢測結果摘要
        out.println("<div class='section'>");
        out.println("<h2>最近檢測結果摘要</h2>");
        
        List<NPlusOneQueryDetector.NPlusOnePattern> patterns = NPlusOneQueryDetector.getDetectedPatterns();
        if (patterns.isEmpty()) {
            out.println("<p class='success'>目前未檢測到 N+1 查詢模式</p>");
        } else {
            out.println("<table>");
            out.println("<tr><th>類型</th><th>位置</th><th>執行次數</th><th>時間</th></tr>");
            
            for (int i = Math.max(0, patterns.size() - 5); i < patterns.size(); i++) {
                NPlusOneQueryDetector.NPlusOnePattern pattern = patterns.get(i);
                out.println("<tr>");
                out.println("<td>" + pattern.patternType + "</td>");
                out.println("<td>" + pattern.location + "</td>");
                out.println("<td class='warning'>" + pattern.executionCount + "</td>");
                out.println("<td>" + pattern.totalTime + "ms</td>");
                out.println("</tr>");
            }
            out.println("</table>");
        }
        out.println("</div>");
        
        out.println("</body>");
        out.println("</html>");
    }
    
    /**
     * 顯示詳細報告
     */
    private void showReport(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        response.setContentType("text/plain; charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        String report = MonitoringInjector.generateFullReport();
        out.println(report);
    }
    
    /**
     * 執行靜態程式碼分析
     */
    private void performStaticAnalysis(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>靜態程式碼分析</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>body { font-family: Arial, sans-serif; margin: 20px; }</style>");
        out.println("</head>");
        out.println("<body>");
        
        out.println("<h1>靜態程式碼分析結果</h1>");
        out.println("<p>分析 CodeCharge Studio 三層架構中的潛在 N+1 查詢問題...</p>");
        
        try {
            // 分析當前專案
            String projectPath = getServletContext().getRealPath("/");
            String analysisResult = MonitoringInjector.analyzeEntireProject(projectPath);
            
            out.println("<pre>");
            out.println(analysisResult);
            out.println("</pre>");
            
        } catch (Exception e) {
            out.println("<p style='color: red;'>分析過程中發生錯誤: " + e.getMessage() + "</p>");
        }
        
        out.println("<p><a href='/monitoring/'>返回主頁</a></p>");
        out.println("</body>");
        out.println("</html>");
    }
    
    /**
     * 執行大量資料集分析
     */
    private void performDatasetAnalysis(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>大量資料集分析</title>");
        out.println("<meta charset='UTF-8'>");
        out.println("<style>body { font-family: Arial, sans-serif; margin: 20px; }</style>");
        out.println("</head>");
        out.println("<body>");
        
        out.println("<h1>大量資料集分析結果</h1>");
        out.println("<p>分析37萬筆案件和100萬筆流程記錄的查詢效能...</p>");
        
        try {
            String analysisResult = LargeDatasetAnalyzer.performFullAnalysis();
            
            out.println("<pre>");
            out.println(analysisResult);
            out.println("</pre>");
            
        } catch (Exception e) {
            out.println("<p style='color: red;'>分析過程中發生錯誤: " + e.getMessage() + "</p>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }
        
        out.println("<p><a href='/monitoring/'>返回主頁</a> | ");
        out.println("<a href='/monitoring/download?type=dataset'>下載完整報告</a></p>");
        out.println("</body>");
        out.println("</html>");
    }
    
    /**
     * 下載報告檔案
     */
    private void downloadReport(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String reportType = request.getParameter("type");
        if (reportType == null) reportType = "full";
        
        response.setContentType("text/plain; charset=UTF-8");
        response.setHeader("Content-Disposition", 
            "attachment; filename=nplus1-report-" + reportType + "-" + System.currentTimeMillis() + ".txt");
        
        PrintWriter out = response.getWriter();
        
        switch (reportType) {
            case "dataset":
                out.println(LargeDatasetAnalyzer.performFullAnalysis());
                break;
            case "static":
                String projectPath = getServletContext().getRealPath("/");
                out.println(MonitoringInjector.analyzeEntireProject(projectPath));
                break;
            default:
                out.println(MonitoringInjector.generateFullReport());
        }
    }
}