.println("產生報表時發生錯誤：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 批量產生第二聯報表的範例
     */
    public static void batchGenerateSecondCopy() {
        IM10101 report = new IM10101();
        
        // 設定基本路徑
        report.setAppPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\");
        report.setReportPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\WEB-INF\\reports\\");
        
        // 批量處理多個案件
        String[] caseIds = {"123001234", "123001235", "123001236"};
        
        for (String caseId : caseIds) {
            HashMap<String, Object> parameters = new HashMap<>();
            
            String[] conditionList = {caseId};
            parameters.put("conditionList", conditionList);
            parameters.put("dataNeedType", "Notice");
            parameters.put("outExt", "PDF");
            parameters.put("outFileName", "IM10101_SecondCopy_" + caseId + "_" + System.currentTimeMillis() + ".pdf");
            parameters.put("ZIP_TAG", "");
            
            try {
                System.out.println("處理案件：" + caseId);
                report.produceSecondCopyOnly(parameters);
                System.out.println("案件 " + caseId + " 第二聯報表產生完成");
            } catch (Exception e) {
                System.err.println("處理案件 " + caseId + " 時發生錯誤：" + e.getMessage());
            }
        }
    }
    
    /**
     * 根據不同的 IB_PRCS 類型產生第二聯報表
     */
    public static void generateByProcessType() {
        IM10101 report = new IM10101();
        
        // 設定基本路徑
        report.setAppPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\");
        report.setReportPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\WEB-INF\\reports\\");
        
        // 不同流程類型的案件範例
        HashMap<String, String> cases = new HashMap<>();
        cases.put("123001234", "A");  // IB_PRCS = 'A' (違章建築認定)
        cases.put("123001235", "B");  // IB_PRCS = 'B' (廣告物)
        cases.put("123001236", "C");  // IB_PRCS = 'C' (違建認定通知書)
        
        for (String caseId : cases.keySet()) {
            String processType = cases.get(caseId);
            
            HashMap<String, Object> parameters = new HashMap<>();
            String[] conditionList = {caseId};
            parameters.put("conditionList", conditionList);
            parameters.put("dataNeedType", "Notice");
            parameters.put("outExt", "PDF");
            parameters.put("outFileName", "IM10101_SecondCopy_Type" + processType + "_" + caseId + ".pdf");
            parameters.put("ZIP_TAG", "");
            
            try {
                System.out.println("處理案件：" + caseId + "（流程類型：" + processType + "）");
                report.produceSecondCopyOnly(parameters);
                
                // 根據不同的流程類型，第二聯的名稱會不同：
                // A類：第二聯（移拆除科）
                // B類：第二聯（移拆除科）
                // C類：第二聯（勞安科）
                System.out.println("案件 " + caseId + " 第二聯報表產生完成");
                
            } catch (Exception e) {
                System.err.println("處理案件 " + caseId + " 時發生錯誤：" + e.getMessage());
            }
        }
    }
}
