# 環境變數管理方案

> **任務編號**: T1.3.2  
> **文件建立**: 2025-07-05  
> **狀態**: 完成  
> **總工時**: 6小時

## 摘要

本文件設計新北市違章建築管理系統的環境變數管理方案，目的是移除硬編碼密碼和配置，提升系統安全性和可維護性。

## 現況分析

### 1. 當前配置檔案分析
基於 `WEB-INF/site.properties` 的分析，發現以下安全風險：

#### 1.1 硬編碼密碼
```properties
# 主資料庫密碼 (PostgreSQL)
DBConn.password=S!@h@202203

# 次要資料庫密碼 (SQL Server)
DBConn2.password=$ystemOnlin168
```

#### 1.2 敏感配置
```properties
# 資料庫連線資訊
DBConn.url=************************************
DBConn2.url=*********************************************************

# 第三方服務 URL
third.party.service.base.url=http://illegal-service.sumire.com.tw/icdc/thridparty/

# 加密金鑰
ALEncryptionKey=J46630r7lrS0rYui
```

#### 1.3 環境相關配置
```properties
# 伺服器 URL
serverUrl=http://localhost/ccsdemo

# 檔案上傳設定
com.codecharge.util.upload.storage=memory

# 日誌設定
logfile=out
logsize=1024
```

## 環境變數管理方案設計

### 1. 設計原則

#### 1.1 安全性原則
- **密碼零硬編碼**: 所有密碼透過環境變數注入
- **敏感資料保護**: 連線字串、API 金鑰等敏感資料環境變數化
- **存取控制**: 環境變數檔案設定適當權限
- **加密存儲**: 生產環境密碼加密存儲

#### 1.2 可維護性原則
- **環境區隔**: dev/test/prod 環境配置分離
- **預設值設定**: 提供合理的預設值
- **向後相容**: 支援舊有配置方式
- **文件化**: 完整的環境變數說明文件

#### 1.3 部署便利性原則
- **容器化友善**: 支援 Docker/K8s 環境
- **CI/CD 整合**: 支援自動化部署
- **設定驗證**: 啟動時驗證必要環境變數
- **錯誤處理**: 提供清楚的設定錯誤訊息

### 2. 環境變數分類

#### 2.1 資料庫連線 (DB_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `DB_PRIMARY_HOST` | 主資料庫主機 | localhost | ✓ | localhost |
| `DB_PRIMARY_PORT` | 主資料庫埠號 | 5432 |  | 5432 |
| `DB_PRIMARY_NAME` | 主資料庫名稱 | bms | ✓ | bms |
| `DB_PRIMARY_USER` | 主資料庫用戶 | postgres | ✓ | postgres |
| `DB_PRIMARY_PASSWORD` | 主資料庫密碼 | - | ✓ | S!@h@202203 |
| `DB_PRIMARY_MAX_CONN` | 主資料庫最大連線數 | 80 |  | 80 |
| `DB_PRIMARY_TIMEOUT` | 主資料庫連線逾時 | 300 |  | 300 |
| `DB_SECONDARY_HOST` | 次要資料庫主機 | - |  | ************** |
| `DB_SECONDARY_PORT` | 次要資料庫埠號 | 2433 |  | 2433 |
| `DB_SECONDARY_NAME` | 次要資料庫名稱 | - |  | ramsGIS |
| `DB_SECONDARY_USER` | 次要資料庫用戶 | - |  | sa |
| `DB_SECONDARY_PASSWORD` | 次要資料庫密碼 | - |  | $ystemOnlin168 |
| `DB_SECONDARY_MAX_CONN` | 次要資料庫最大連線數 | 100 |  | 100 |
| `DB_SECONDARY_TIMEOUT` | 次要資料庫連線逾時 | 600 |  | 600 |

#### 2.2 伺服器設定 (SERVER_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `SERVER_URL` | 伺服器基礎 URL | http://localhost/ccsdemo |  | https://bms.ntpc.gov.tw |
| `SERVER_SECURED_URL` | 安全連線 URL | - |  | https://bms.ntpc.gov.tw |
| `SERVER_ENCODING` | 字元編碼 | UTF-8 |  | UTF-8 |
| `SERVER_LANGUAGE` | 預設語言 | zh |  | zh |
| `SERVER_LOCALE` | 預設地區 | zh |  | zh_TW |
| `SERVER_REQUEST_ENCODING` | 請求編碼 | UTF-8 |  | UTF-8 |

#### 2.3 安全設定 (SECURITY_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `SECURITY_ENCRYPTION_KEY` | 加密金鑰 | - | ✓ | J46630r7lrS0rYui |
| `SECURITY_AUTH_TYPE` | 認證類型 | CCS |  | CCS |
| `SECURITY_SESSION_TIMEOUT` | Session 逾時 | 30 |  | 60 |
| `SECURITY_COOKIE_EXPIRED` | Cookie 過期時間 | 30 |  | 365 |
| `SECURITY_LOGIN_COOKIE_NAME` | 登入 Cookie 名稱 | bmsLogin |  | bmsLogin |

#### 2.4 第三方服務 (EXTERNAL_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `EXTERNAL_SERVICE_BASE_URL` | 第三方服務基礎 URL | - |  | http://illegal-service.sumire.com.tw/icdc/thridparty/ |
| `EXTERNAL_GOOGLE_MAPS_URL` | Google Maps 服務 URL | maps/index |  | maps/index |

#### 2.5 檔案系統 (FILE_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `FILE_UPLOAD_STORAGE` | 檔案上傳儲存方式 | memory |  | disk |
| `FILE_UPLOAD_PATH` | 檔案上傳路徑 | /tmp/uploads |  | /var/bms/uploads |
| `FILE_MAX_SIZE` | 檔案大小限制 | 10MB |  | 50MB |

#### 2.6 日誌設定 (LOG_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `LOG_FILE` | 日誌檔案名稱 | bms |  | bms |
| `LOG_LEVEL` | 日誌層級 | info |  | debug |
| `LOG_SIZE` | 日誌檔案大小限制 | 1024 |  | 10240 |
| `LOG_PATH` | 日誌存放路徑 | /var/log/bms |  | /opt/bms/logs |

#### 2.7 應用程式設定 (APP_*)
| 環境變數名稱 | 說明 | 預設值 | 必填 | 範例 |
|-------------|------|--------|------|------|
| `APP_ENVIRONMENT` | 應用程式環境 | development |  | production |
| `APP_DEBUG` | 除錯模式 | false |  | true |
| `APP_DEFAULT_STYLE` | 預設樣式 | Austere |  | Bootstrap |
| `APP_DEFAULT_DESIGN` | 預設設計 | Light |  | Modern |

### 3. 實作架構

#### 3.1 配置檔案層級架構
```
config/
├── default.properties          # 預設配置
├── dev/
│   ├── site.properties        # 開發環境配置
│   └── .env                   # 開發環境變數檔案
├── test/
│   ├── site.properties        # 測試環境配置
│   └── .env                   # 測試環境變數檔案
├── prod/
│   ├── site.properties        # 生產環境配置
│   └── .env.example           # 生產環境變數範例
└── site.properties.template    # 配置範本檔案
```

#### 3.2 Java 配置管理類別
```java
package com.ntpc.bms.config;

import java.util.Properties;
import java.io.IOException;
import java.io.InputStream;

/**
 * 環境變數配置管理類別
 * 負責載入和管理所有環境相關配置
 */
public class EnvironmentConfig {
    private static final Properties properties = new Properties();
    private static EnvironmentConfig instance;
    
    private EnvironmentConfig() {
        loadConfiguration();
    }
    
    public static synchronized EnvironmentConfig getInstance() {
        if (instance == null) {
            instance = new EnvironmentConfig();
        }
        return instance;
    }
    
    /**
     * 載入配置檔案和環境變數
     */
    private void loadConfiguration() {
        // 1. 載入預設配置
        loadPropertiesFile("default.properties");
        
        // 2. 載入環境特定配置
        String environment = getEnvironment();
        loadPropertiesFile(environment + "/site.properties");
        
        // 3. 載入環境變數（覆蓋檔案配置）
        loadEnvironmentVariables();
        
        // 4. 驗證必要配置
        validateRequiredProperties();
    }
    
    /**
     * 取得環境名稱
     */
    private String getEnvironment() {
        return System.getenv("APP_ENVIRONMENT") != null ? 
               System.getenv("APP_ENVIRONMENT") : "dev";
    }
    
    /**
     * 載入屬性檔案
     */
    private void loadPropertiesFile(String filename) {
        try (InputStream input = getClass().getClassLoader()
             .getResourceAsStream("config/" + filename)) {
            if (input != null) {
                properties.load(input);
            }
        } catch (IOException e) {
            System.err.println("無法載入配置檔案: " + filename);
        }
    }
    
    /**
     * 載入環境變數
     */
    private void loadEnvironmentVariables() {
        // 資料庫配置
        setPropertyFromEnv("DBConn.url", buildDatabaseUrl("PRIMARY"));
        setPropertyFromEnv("DBConn.user", "DB_PRIMARY_USER");
        setPropertyFromEnv("DBConn.password", "DB_PRIMARY_PASSWORD");
        setPropertyFromEnv("DBConn.maxconn", "DB_PRIMARY_MAX_CONN");
        setPropertyFromEnv("DBConn.timeout", "DB_PRIMARY_TIMEOUT");
        
        setPropertyFromEnv("DBConn2.url", buildDatabaseUrl("SECONDARY"));
        setPropertyFromEnv("DBConn2.user", "DB_SECONDARY_USER");
        setPropertyFromEnv("DBConn2.password", "DB_SECONDARY_PASSWORD");
        setPropertyFromEnv("DBConn2.maxconn", "DB_SECONDARY_MAX_CONN");
        setPropertyFromEnv("DBConn2.timeout", "DB_SECONDARY_TIMEOUT");
        
        // 伺服器配置
        setPropertyFromEnv("serverUrl", "SERVER_URL");
        setPropertyFromEnv("securedUrl", "SERVER_SECURED_URL");
        setPropertyFromEnv("encoding", "SERVER_ENCODING");
        setPropertyFromEnv("language", "SERVER_LANGUAGE");
        setPropertyFromEnv("defaultLocale", "SERVER_LOCALE");
        
        // 安全配置
        setPropertyFromEnv("ALEncryptionKey", "SECURITY_ENCRYPTION_KEY");
        setPropertyFromEnv("authenticator.securityType", "SECURITY_AUTH_TYPE");
        
        // 第三方服務
        setPropertyFromEnv("third.party.service.base.url", "EXTERNAL_SERVICE_BASE_URL");
        setPropertyFromEnv("google.maps.service.url", "EXTERNAL_GOOGLE_MAPS_URL");
        
        // 檔案系統
        setPropertyFromEnv("com.codecharge.util.upload.storage", "FILE_UPLOAD_STORAGE");
        
        // 日誌設定
        setPropertyFromEnv("logfile", "LOG_FILE");
        setPropertyFromEnv("logpriority", "LOG_LEVEL");
        setPropertyFromEnv("logsize", "LOG_SIZE");
    }
    
    /**
     * 建立資料庫連線 URL
     */
    private String buildDatabaseUrl(String dbType) {
        String host = System.getenv("DB_" + dbType + "_HOST");
        String port = System.getenv("DB_" + dbType + "_PORT");
        String name = System.getenv("DB_" + dbType + "_NAME");
        
        if (host == null || name == null) {
            return null;
        }
        
        if ("PRIMARY".equals(dbType)) {
            return String.format("jdbc:postgresql://%s:%s/%s", 
                               host, port != null ? port : "5432", name);
        } else {
            return String.format("**************************************", 
                               host, port != null ? port : "2433", name);
        }
    }
    
    /**
     * 從環境變數設定屬性
     */
    private void setPropertyFromEnv(String propertyKey, String envKey) {
        String envValue = System.getenv(envKey);
        if (envValue != null && !envValue.trim().isEmpty()) {
            properties.setProperty(propertyKey, envValue);
        }
    }
    
    /**
     * 驗證必要屬性
     */
    private void validateRequiredProperties() {
        String[] requiredProperties = {
            "DBConn.password",
            "SECURITY_ENCRYPTION_KEY"
        };
        
        for (String prop : requiredProperties) {
            if (getProperty(prop) == null || getProperty(prop).trim().isEmpty()) {
                throw new IllegalStateException("必要的環境變數未設定: " + prop);
            }
        }
    }
    
    /**
     * 取得配置值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 取得配置值（含預設值）
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 取得整數配置值
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                System.err.println("配置值格式錯誤: " + key + " = " + value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 取得布林配置值
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
}
```

#### 3.3 資料庫連線管理器修改
```java
package com.ntpc.bms.database;

import com.ntpc.bms.config.EnvironmentConfig;

/**
 * 資料庫連線管理器
 * 整合環境變數配置
 */
public class DatabaseConnectionManager {
    private final EnvironmentConfig config;
    
    public DatabaseConnectionManager() {
        this.config = EnvironmentConfig.getInstance();
    }
    
    /**
     * 取得主資料庫連線資訊
     */
    public DatabaseConfig getPrimaryDatabaseConfig() {
        return DatabaseConfig.builder()
            .url(config.getProperty("DBConn.url"))
            .username(config.getProperty("DBConn.user"))
            .password(config.getProperty("DBConn.password"))
            .maxConnections(config.getIntProperty("DBConn.maxconn", 80))
            .timeout(config.getIntProperty("DBConn.timeout", 300))
            .build();
    }
    
    /**
     * 取得次要資料庫連線資訊
     */
    public DatabaseConfig getSecondaryDatabaseConfig() {
        String url = config.getProperty("DBConn2.url");
        if (url == null) {
            return null; // 次要資料庫為選用
        }
        
        return DatabaseConfig.builder()
            .url(url)
            .username(config.getProperty("DBConn2.user"))
            .password(config.getProperty("DBConn2.password"))
            .maxConnections(config.getIntProperty("DBConn2.maxconn", 100))
            .timeout(config.getIntProperty("DBConn2.timeout", 600))
            .build();
    }
}
```

### 4. 環境配置檔案範本

#### 4.1 site.properties.template
```properties
#========================================
# 新北市違章建築管理系統 - 配置範本
#========================================

#Rebuild Format Symbols @0-1EE0AE1E
RebuildFormatSymbols=true
#End Rebuild Format Symbols

#logger properties @0-3FBB4328
logfile=${LOG_FILE:bms}
logpriority=${LOG_LEVEL:info}
logsize=${LOG_SIZE:1024}
#End logger properties

#url properties @0-7015BB88
serverUrl=${SERVER_URL:http://localhost/ccsdemo}
securedUrl=${SERVER_SECURED_URL:}
#End url properties

#localization properties @0-********
encoding=${SERVER_ENCODING:UTF-8}
language=${SERVER_LANGUAGE:zh}
defaultDateFormat=ShortDate
defaultBooleanFormat=
requestEncoding=${SERVER_REQUEST_ENCODING:UTF-8}
#End localization properties

#messages bundle @0-77F70051
messagesBundle=MessagesBundle
#End messages bundle

#file upload @0-215C2A9B
com.codecharge.util.upload.storage=${FILE_UPLOAD_STORAGE:memory}
#End file upload

#styles settings @0-7D1CC75B
useDynamicStyles=False
defaultStyle=${APP_DEFAULT_STYLE:Austere}
SSEnableQueryString=True
SSQueryStringName=style
SSEnableSession=True
SSSessionName=style
SSEnableCookie=False
SSCookieName=style
SSCookieExpired=365
#End styles settings

#design settings @0-1750DDFA
useDynamicDesigns=True
defaultDesign=${APP_DEFAULT_DESIGN:Light}
DDEnableQueryString=True
DDQueryStringName=design
DDEnableSession=True
DDSessionName=design
DDEnableCookie=False
DDCookieName=design
DDCookieExpired=365
designFolder=Designs
#End design settings

#defaultLocale @0-6C263AAB
defaultLocale=${SERVER_LOCALE:zh}
#End defaultLocale

#authentication properties @0-BE735D6B
authenticator.securityType=${SECURITY_AUTH_TYPE:CCS}
authenticator.factoryClassName=com.codecharge.util.CCSAuthenticatorFactory
#End authentication properties

#Remember me @0-F065362C
ALEnabled=
ALLoginCookieName=${SECURITY_LOGIN_COOKIE_NAME:bmsLogin}
ALPasswordCookieName=
ALCookieExpired=${SECURITY_COOKIE_EXPIRED:30}
ALEncryptionKey=${SECURITY_ENCRYPTION_KEY}
ALSlidingExpriration=False
#End Remember me

#Connection 'DBConn' properties @1-93ABABCD
DBConn.name=DBConn
DBConn.driver=org.postgresql.Driver
DBConn.dbType=bms
DBConn.url=jdbc:postgresql://${DB_PRIMARY_HOST:localhost}:${DB_PRIMARY_PORT:5432}/${DB_PRIMARY_NAME:bms}
DBConn.maxconn=${DB_PRIMARY_MAX_CONN:80}
DBConn.user=${DB_PRIMARY_USER:postgres}
DBConn.password=${DB_PRIMARY_PASSWORD}
DBConn.timeout=${DB_PRIMARY_TIMEOUT:300}
DBConn.fieldLeftDelim=
DBConn.fieldRightDelim=
DBConn.dateLeftDelim='
DBConn.dateRightDelim='
DBConn.dbNameUppercase=True
DBConn.dateFormat=yyyy-MM-dd HH:mm:ss
DBConn.booleanFormat=1;0
#End Connection 'DBConn' properties

#Connection 'DBConn2' properties @1-6541AA29 (Optional)
DBConn2.name=DBConn2
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.dbType=MSSQLServer
DBConn2.url=jdbc:sqlserver://${DB_SECONDARY_HOST}:${DB_SECONDARY_PORT:2433};DatabaseName=${DB_SECONDARY_NAME}
DBConn2.maxconn=${DB_SECONDARY_MAX_CONN:100}
DBConn2.user=${DB_SECONDARY_USER}
DBConn2.password=${DB_SECONDARY_PASSWORD}
DBConn2.timeout=${DB_SECONDARY_TIMEOUT:600}
DBConn2.fieldLeftDelim=
DBConn2.fieldRightDelim=
DBConn2.dateLeftDelim='
DBConn2.dateRightDelim='
DBConn2.dbNameUppercase=True
DBConn2.dateFormat=yyyy-MM-dd HH:mm:ss
DBConn2.booleanFormat=1;0
#End Connection 'DBConn2' properties

#Third party service configuration
third.party.service.base.url=${EXTERNAL_SERVICE_BASE_URL:}
google.maps.service.url=${EXTERNAL_GOOGLE_MAPS_URL:maps/index}
#End Third party service configuration
```

#### 4.2 dev/.env (開發環境)
```bash
# ========================================
# 開發環境變數配置
# ========================================

# 應用程式設定
APP_ENVIRONMENT=development
APP_DEBUG=true

# 資料庫設定 - 主資料庫 (PostgreSQL)
DB_PRIMARY_HOST=localhost
DB_PRIMARY_PORT=5432
DB_PRIMARY_NAME=bms_dev
DB_PRIMARY_USER=postgres
DB_PRIMARY_PASSWORD=dev_password_123
DB_PRIMARY_MAX_CONN=20
DB_PRIMARY_TIMEOUT=300

# 資料庫設定 - 次要資料庫 (SQL Server) - 選用
# DB_SECONDARY_HOST=dev-sqlserver
# DB_SECONDARY_PORT=2433
# DB_SECONDARY_NAME=ramsGIS_dev
# DB_SECONDARY_USER=sa
# DB_SECONDARY_PASSWORD=dev_sqlserver_password
# DB_SECONDARY_MAX_CONN=50
# DB_SECONDARY_TIMEOUT=600

# 伺服器設定
SERVER_URL=http://localhost:8080/bms
SERVER_SECURED_URL=https://localhost:8443/bms
SERVER_ENCODING=UTF-8
SERVER_LANGUAGE=zh
SERVER_LOCALE=zh_TW
SERVER_REQUEST_ENCODING=UTF-8

# 安全設定
SECURITY_ENCRYPTION_KEY=DevEncryptionKey123
SECURITY_AUTH_TYPE=CCS
SECURITY_SESSION_TIMEOUT=60
SECURITY_COOKIE_EXPIRED=30
SECURITY_LOGIN_COOKIE_NAME=bmsDevLogin

# 第三方服務
EXTERNAL_SERVICE_BASE_URL=http://dev-service.example.com/api/
EXTERNAL_GOOGLE_MAPS_URL=maps/dev

# 檔案系統設定
FILE_UPLOAD_STORAGE=disk
FILE_UPLOAD_PATH=/tmp/bms/uploads
FILE_MAX_SIZE=10MB

# 日誌設定
LOG_FILE=bms-dev
LOG_LEVEL=debug
LOG_SIZE=1024
LOG_PATH=/var/log/bms-dev

# 應用程式設定
APP_DEFAULT_STYLE=Bootstrap
APP_DEFAULT_DESIGN=Modern
```

#### 4.3 prod/.env.example (生產環境範例)
```bash
# ========================================
# 生產環境變數配置範例
# 請複製為 .env 並填入實際值
# ========================================

# 應用程式設定
APP_ENVIRONMENT=production
APP_DEBUG=false

# 資料庫設定 - 主資料庫 (PostgreSQL)
DB_PRIMARY_HOST=db.ntpc.gov.tw
DB_PRIMARY_PORT=5432
DB_PRIMARY_NAME=bms_prod
DB_PRIMARY_USER=bms_user
DB_PRIMARY_PASSWORD=請填入實際密碼
DB_PRIMARY_MAX_CONN=80
DB_PRIMARY_TIMEOUT=300

# 資料庫設定 - 次要資料庫 (SQL Server)
DB_SECONDARY_HOST=gis-db.ntpc.gov.tw
DB_SECONDARY_PORT=2433
DB_SECONDARY_NAME=ramsGIS
DB_SECONDARY_USER=gis_user
DB_SECONDARY_PASSWORD=請填入實際密碼
DB_SECONDARY_MAX_CONN=100
DB_SECONDARY_TIMEOUT=600

# 伺服器設定
SERVER_URL=https://bms.ntpc.gov.tw
SERVER_SECURED_URL=https://bms.ntpc.gov.tw
SERVER_ENCODING=UTF-8
SERVER_LANGUAGE=zh
SERVER_LOCALE=zh_TW
SERVER_REQUEST_ENCODING=UTF-8

# 安全設定
SECURITY_ENCRYPTION_KEY=請填入實際加密金鑰
SECURITY_AUTH_TYPE=CCS
SECURITY_SESSION_TIMEOUT=30
SECURITY_COOKIE_EXPIRED=365
SECURITY_LOGIN_COOKIE_NAME=bmsLogin

# 第三方服務
EXTERNAL_SERVICE_BASE_URL=https://illegal-service.sumire.com.tw/icdc/thridparty/
EXTERNAL_GOOGLE_MAPS_URL=maps/index

# 檔案系統設定
FILE_UPLOAD_STORAGE=disk
FILE_UPLOAD_PATH=/opt/bms/uploads
FILE_MAX_SIZE=50MB

# 日誌設定
LOG_FILE=bms
LOG_LEVEL=info
LOG_SIZE=10240
LOG_PATH=/var/log/bms

# 應用程式設定
APP_DEFAULT_STYLE=Austere
APP_DEFAULT_DESIGN=Light
```

### 5. 部署指引

#### 5.1 開發環境部署
```bash
# 1. 複製環境變數檔案
cp config/dev/.env.example config/dev/.env

# 2. 編輯環境變數檔案
vim config/dev/.env

# 3. 載入環境變數
source config/dev/.env

# 4. 啟動應用程式
./startup.sh dev
```

#### 5.2 生產環境部署
```bash
# 1. 建立環境變數檔案
cp config/prod/.env.example config/prod/.env

# 2. 設定正確權限
chmod 600 config/prod/.env
chown tomcat:tomcat config/prod/.env

# 3. 編輯環境變數檔案（填入實際密碼）
vim config/prod/.env

# 4. 驗證環境變數
./scripts/validate-env.sh prod

# 5. 啟動應用程式
./startup.sh prod
```

#### 5.3 Docker 容器部署
```dockerfile
# Dockerfile
FROM tomcat:9.0-jdk8

# 複製應用程式
COPY target/bms.war /usr/local/tomcat/webapps/

# 複製配置檔案
COPY config/ /usr/local/tomcat/conf/bms/

# 設定環境變數檔案權限
RUN chmod 600 /usr/local/tomcat/conf/bms/prod/.env

# 設定啟動腳本
COPY scripts/docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["catalina.sh", "run"]
```

```docker-compose
# docker-compose.yml
version: '3.8'

services:
  bms-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - APP_ENVIRONMENT=production
      - DB_PRIMARY_HOST=postgres
      - DB_PRIMARY_PASSWORD=${DB_PASSWORD}
      - SECURITY_ENCRYPTION_KEY=${ENCRYPTION_KEY}
    volumes:
      - bms-uploads:/opt/bms/uploads
      - bms-logs:/var/log/bms
    depends_on:
      - postgres
    
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=bms_prod
      - POSTGRES_USER=bms_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data

volumes:
  bms-uploads:
  bms-logs:
  postgres-data:
```

### 6. 安全性措施

#### 6.1 檔案權限設定
```bash
# 環境變數檔案權限
chmod 600 config/*/.env
chown tomcat:tomcat config/*/.env

# 配置目錄權限
chmod 750 config/
chown -R tomcat:tomcat config/

# 日誌目錄權限
chmod 755 /var/log/bms
chown tomcat:tomcat /var/log/bms
```

#### 6.2 密碼加密存儲
```java
/**
 * 密碼加密工具類別
 */
public class PasswordEncryption {
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final String KEY_ALGORITHM = "AES";
    
    /**
     * 加密密碼
     */
    public static String encrypt(String password, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(
                key.getBytes(StandardCharsets.UTF_8), KEY_ALGORITHM);
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            
            byte[] encrypted = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("密碼加密失敗", e);
        }
    }
    
    /**
     * 解密密碼
     */
    public static String decrypt(String encryptedPassword, String key) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(
                key.getBytes(StandardCharsets.UTF_8), KEY_ALGORITHM);
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            
            byte[] decoded = Base64.getDecoder().decode(encryptedPassword);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("密碼解密失敗", e);
        }
    }
}
```

#### 6.3 環境變數驗證腳本
```bash
#!/bin/bash
# scripts/validate-env.sh

ENVIRONMENT=${1:-dev}
ENV_FILE="config/${ENVIRONMENT}/.env"

echo "驗證 ${ENVIRONMENT} 環境變數..."

if [ ! -f "$ENV_FILE" ]; then
    echo "錯誤: 環境變數檔案不存在: $ENV_FILE"
    exit 1
fi

# 載入環境變數
source "$ENV_FILE"

# 驗證必要變數
REQUIRED_VARS=(
    "DB_PRIMARY_HOST"
    "DB_PRIMARY_NAME"
    "DB_PRIMARY_USER"
    "DB_PRIMARY_PASSWORD"
    "SECURITY_ENCRYPTION_KEY"
)

MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        MISSING_VARS+=("$var")
    fi
done

if [ ${#MISSING_VARS[@]} -gt 0 ]; then
    echo "錯誤: 以下必要環境變數未設定:"
    printf ' - %s\n' "${MISSING_VARS[@]}"
    exit 1
fi

# 驗證資料庫連線
echo "驗證資料庫連線..."
PGPASSWORD="$DB_PRIMARY_PASSWORD" psql -h "$DB_PRIMARY_HOST" -p "$DB_PRIMARY_PORT" \
    -U "$DB_PRIMARY_USER" -d "$DB_PRIMARY_NAME" -c "SELECT 1;" > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✓ 主資料庫連線成功"
else
    echo "✗ 主資料庫連線失敗"
    exit 1
fi

echo "✓ 環境變數驗證完成"
```

### 7. 遷移計畫

#### 7.1 階段一：基礎建設 (1週)
- [ ] 建立 EnvironmentConfig 類別
- [ ] 建立配置檔案範本
- [ ] 建立環境變數驗證腳本
- [ ] 更新 DatabaseConnectionManager

#### 7.2 階段二：開發環境導入 (1週)
- [ ] 設定開發環境配置
- [ ] 測試環境變數載入
- [ ] 驗證資料庫連線
- [ ] 測試應用程式功能

#### 7.3 階段三：測試環境驗證 (1週)
- [ ] 部署到測試環境
- [ ] 執行完整功能測試
- [ ] 效能測試
- [ ] 安全性測試

#### 7.4 階段四：生產環境上線 (1週)
- [ ] 生產環境配置
- [ ] 資料庫密碼更新
- [ ] 監控和日誌設定
- [ ] 上線後驗證

### 8. 維護指引

#### 8.1 密碼輪換流程
```bash
# 1. 產生新密碼
NEW_PASSWORD=$(openssl rand -base64 32)

# 2. 更新資料庫密碼
ALTER USER postgres PASSWORD '$NEW_PASSWORD';

# 3. 更新環境變數
sed -i "s/DB_PRIMARY_PASSWORD=.*/DB_PRIMARY_PASSWORD=$NEW_PASSWORD/" config/prod/.env

# 4. 重啟應用程式
systemctl restart tomcat
```

#### 8.2 環境變數備份
```bash
# 定期備份環境變數檔案（不含密碼）
./scripts/backup-env.sh
```

#### 8.3 監控和告警
- 監控應用程式啟動狀態
- 監控資料庫連線狀態
- 設定配置檔案變更告警
- 設定環境變數遺失告警

## 效益評估

### 1. 安全性提升
- **✓ 消除硬編碼密碼**: 降低密碼洩露風險
- **✓ 環境隔離**: 不同環境使用不同密碼
- **✓ 存取控制**: 檔案權限保護敏感配置
- **✓ 密碼加密**: 支援密碼加密存儲

### 2. 可維護性改善
- **✓ 環境區隔**: 清楚的環境配置分離
- **✓ 集中管理**: 統一的配置管理機制
- **✓ 預設值**: 減少配置錯誤
- **✓ 驗證機制**: 啟動時驗證配置完整性

### 3. 部署便利性
- **✓ 容器化支援**: 支援 Docker/K8s 部署
- **✓ CI/CD 整合**: 自動化部署友善
- **✓ 一鍵部署**: 簡化部署流程
- **✓ 回滾機制**: 支援快速回滾

---

*此文件由【B】Claude Code - 後端開發任務組產出*