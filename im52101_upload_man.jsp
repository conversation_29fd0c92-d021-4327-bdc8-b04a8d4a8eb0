<%@page import="jdk.internal.org.objectweb.asm.tree.TryCatchBlockNode"%>
<%@page contentType="application/json; charset=UTF-8"%>
<%@page pageEncoding="utf-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.sql.Connection, java.sql.PreparedStatement, java.sql.ResultSet, java.sql.SQLException"%>
<%@page import="java.io.File,java.io.IOException,java.io.EOFException"%>
<%@page import="com.oreilly.servlet.MultipartRequest, java.nio.file.*"%>
<%@ page import="javax.servlet.*, java.net.URLDecoder" %>
<%    

    
	String SEPARATOR = System.getProperty("file.separator");
	String REAL_PATH = request.getRealPath("/");
	String rootDirectory = REAL_PATH;
	rootDirectory += (REAL_PATH.substring((REAL_PATH.length() - 1), REAL_PATH.length()).equals(SEPARATOR)) ? "" : SEPARATOR;
	
	int MAX_POST_SIZE = 100 * 1024 * 1024;
	
	MultipartRequest mr = null;
	// 暫存路徑  //設定照片上傳暫存路徑
	String tempFileUploadPath = rootDirectory+"img\\im52101_uploadtemp";
	// 上傳照片資料夾名稱
	String PICKIND="";
	// 實體路徑
	String annFileUploadPath="" ;
	
	File tmpDirectory = new File(tempFileUploadPath);
	if (!tmpDirectory.exists()) tmpDirectory.mkdirs();
		
    try {
    	mr = new MultipartRequest(request, tempFileUploadPath , MAX_POST_SIZE, "utf-8");
    }catch (Exception localException){
    }
	
	String fileId = (String)session.getAttribute("id");
	String CR_USER = (String)session.getAttribute("UserID");
	String tempFileName = "";
	
	JDBCConnection jdbcConn = null;
	
	String picPath = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
	annFileUploadPath = picPath + "im52101" + SEPARATOR +"input" + SEPARATOR + fileId + SEPARATOR ;
	
	File annDirectory = new File(annFileUploadPath);
	if (!annDirectory.exists()) 
        annDirectory.mkdirs();
	
	String jsonString ="";
	
	//上傳
	if(mr != null){
		boolean firstIn = false;
		
		Enumeration filesname = mr.getFileNames();
		Enumeration filesdc = mr.getParameterNames(); 
				
		jsonString = "{\"files\":[";
		
		try {
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
			String outFileName="", originalFileName = "",fileExt = "",finalStoredPath = "";
			String outFileNameP1_ORI="";
            long fileSize =0;
			while (filesname != null && filesname.hasMoreElements()){
				
				//int picSeqInt = Integer.parseInt(picSeq);
				String name = (String)filesname.nextElement();
				tempFileName = mr.getFilesystemName(name);//originalFileName
				if (tempFileName == null) {
					continue;
				}
			
				String dataType = tempFileName.substring(tempFileName.lastIndexOf("."));
				fileExt = dataType;
				originalFileName = tempFileName.substring(0,tempFileName.lastIndexOf("."));
				outFileName = originalFileName + dataType;
				outFileNameP1_ORI = originalFileName + dataType;
				finalStoredPath = annFileUploadPath + outFileName;
				
					
				if (!StringUtils.isEmpty(annFileUploadPath + outFileName)){
					File f = new File(annFileUploadPath + outFileName);
					if(f.exists()){
						f.delete();
					}
				}	
				
				// 移動
				File file = new File(tempFileUploadPath + SEPARATOR + tempFileName);
				fileSize = file.length();

				File file2 = new File(annFileUploadPath + outFileNameP1_ORI);
				Files.copy(file.toPath() ,file2.toPath());
				
				 file.renameTo(new java.io.File(annFileUploadPath  + outFileName ));

                String checkSql = "SELECT COUNT(*) FROM public.im52101_excel_imports WHERE import_id = ?";
                PreparedStatement selectStmt = null;
                ResultSet rs = null;
                long count = 0;
                try {
                    selectStmt = jdbcConn.createPreparedStatement(checkSql);
                    selectStmt.setString(1, fileId);
                    rs = selectStmt.executeQuery();
                    if (rs.next()) {
                        count = rs.getLong(1);
                    }
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException e) { /* ignore */ }
                    if (selectStmt != null) try { selectStmt.close(); } catch (SQLException e) { /* ignore */ }
                }

                String upsertSql;
                if (count > 0) {
                     // Consider deleting old file if it's an update and filename changes
                    String oldFilePath = Utils.convertToString(DBTools.dLookUp("stored_file_path", "public.im52101_excel_imports", "import_id = '" + fileId + "'", "DBConn"));
                    if (!StringUtils.isEmpty(oldFilePath) && !oldFilePath.equals(finalStoredPath)) {
                        File oldDbFile = new File(oldFilePath);
                        if(oldDbFile.exists()) oldDbFile.delete();
                    }

                    upsertSql = "UPDATE public.im52101_excel_imports SET " +
                                "original_file_name = ?, stored_file_path = ?, file_extension = ?, " +
                                "file_size = ?, upload_timestamp = NOW(), cr_user = ? " +
                                "WHERE import_id = ?";
                } else {
                    upsertSql = "INSERT INTO public.im52101_excel_imports " +
                                "(original_file_name, stored_file_path, file_extension, file_size, upload_timestamp, cr_user, import_id) " +
                                "VALUES (?, ?, ?, ?, NOW(), ?, ?)";
                }
                
                PreparedStatement stmt = null;
                try {
                    stmt = jdbcConn.createPreparedStatement(upsertSql);
                    stmt.setString(1, originalFileName);
                    stmt.setString(2, finalStoredPath);
                    stmt.setString(3, fileExt);
                    stmt.setLong(4, fileSize); // 直接使用 long 類型
                    stmt.setString(5, CR_USER);
                    stmt.setString(6, fileId);
                    stmt.executeUpdate();
                } finally {
                    if (stmt != null) try { stmt.close(); } catch (SQLException e) { /* ignore */ }
                }

				if(firstIn){
					firstIn = true;
					jsonString += ",";
				}

				jsonString += "{\"name\": \""+originalFileName+"\", ";
				//jsonString += "\"size\": "+fileSizeLong+",";
				jsonString += "\"deleteType\": \"POST\"}";
			}
			jsonString +=  "]}";
		}catch (Exception localException){
			System.err.println("im52101_upload_man : error is " + localException.toString());		
		}finally{
			if( jdbcConn != null )jdbcConn.closeConnection();
			
			out.println(jsonString);
		}
	}

    

%>