# 新北市違章建築管理系統 - 緊急應變程序手冊

## 🚨 緊急應變概述

### 目的
本手冊提供新北市違章建築管理系統發生緊急事件時的標準應變程序，確保系統能在最短時間內恢復正常運作。

### 適用範圍
- 系統完全中斷
- 資料庫損毀
- 應用程式故障
- 硬體設備故障
- 安全事件
- 自然災害

### 緊急應變目標
- **RTO (Recovery Time Objective)**：4小時
- **RPO (Recovery Point Objective)**：1小時
- **業務連續性**：最小化服務中斷時間
- **資料完整性**：確保資料不遺失

---

## 📋 緊急應變分級

### 🔴 第一級 (Critical) - 系統完全中斷
**觸發條件**：
- 系統完全無法存取
- 資料庫完全損毀
- 主要硬體設備故障
- 嚴重安全事件

**應變時間**：立即 (0-15分鐘)

**負責人員**：系統管理員 + IT主管

### 🟡 第二級 (High) - 部分功能異常
**觸發條件**：
- 部分功能無法使用
- 效能嚴重降低
- 非關鍵服務中斷
- 資料不一致

**應變時間**：1小時內開始處理

**負責人員**：系統管理員

### 🟢 第三級 (Medium) - 輕微異常
**觸發條件**：
- 效能輕微降低
- 非關鍵警告
- 預防性維護需求

**應變時間**：4小時內開始處理

**負責人員**：系統管理員

---

## 🎯 第一級緊急應變程序

### 階段1：緊急評估與通報 (0-15分鐘)

#### 1.1 立即行動
```powershell
# 執行緊急評估
cd D:\apache-tomcat-9.0.98\webapps\src\backup_scripts
.\disaster_recovery.ps1 -Operation assessment
```

#### 1.2 通報程序
```
時間軸：
00:00 - 發現問題
00:05 - 通知系統管理員
00:10 - 通知IT主管
00:15 - 決定是否啟動災難復原
```

#### 1.3 評估檢查清單
- [ ] 確認問題範圍和影響
- [ ] 檢查系統基礎設施狀態
- [ ] 評估資料完整性
- [ ] 確認備份可用性
- [ ] 決定復原策略

### 階段2：緊急復原準備 (15-30分鐘)

#### 2.1 復原環境準備
```powershell
# 檢查復原環境
$driveSpace = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "D:" }
$freeSpaceGB = [math]::Round($driveSpace.FreeSpace / 1GB, 2)

if ($freeSpaceGB -lt 50) {
    Write-Host "警告：磁碟空間不足，需要清理" -ForegroundColor Yellow
    # 清理暫存檔案
    Remove-Item "D:\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
}
```

#### 2.2 備份檔案驗證
```powershell
# 驗證最新備份
$backupPath = "D:\Backups\BMS"

# 檢查PostgreSQL備份
$pgBackup = Get-ChildItem "$backupPath\full" -Filter "*.sql*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
if ($pgBackup) {
    Write-Host "PostgreSQL備份：$($pgBackup.Name) ($(($pgBackup.LastWriteTime).ToString('yyyy-MM-dd HH:mm')))" -ForegroundColor Green
} else {
    Write-Host "錯誤：找不到PostgreSQL備份" -ForegroundColor Red
}

# 檢查SQL Server備份
$sqlBackup = Get-ChildItem "$backupPath\sqlserver\full" -Filter "*.bak*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
if ($sqlBackup) {
    Write-Host "SQL Server備份：$($sqlBackup.Name) ($(($sqlBackup.LastWriteTime).ToString('yyyy-MM-dd HH:mm')))" -ForegroundColor Green
} else {
    Write-Host "錯誤：找不到SQL Server備份" -ForegroundColor Red
}
```

#### 2.3 人員動員
```
通知清單：
□ 系統管理員 (0912-345-678)
□ IT主管 (0923-456-789)
□ 業務主管 (0934-567-890)
□ 網管人員 (0945-678-901)
□ 資料庫管理員 (0956-789-012)
```

### 階段3：執行緊急復原 (30分鐘-4小時)

#### 3.1 資料庫復原 (30分鐘-2小時)
```powershell
# PostgreSQL復原
Write-Host "開始PostgreSQL資料庫復原..." -ForegroundColor Yellow

# 停止PostgreSQL服務
Stop-Service "PostgreSQL Database Server 15" -Force

# 備份現有資料庫 (如果可能)
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
if (Test-Path "C:\Program Files\PostgreSQL\15\data") {
    Copy-Item "C:\Program Files\PostgreSQL\15\data" "D:\Recovery\pg_data_backup_$timestamp" -Recurse -Force
}

# 復原資料庫
$env:PGPASSWORD = "S!@h@202203"
$latestBackup = Get-ChildItem "D:\Backups\BMS\full" -Filter "*.sql*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestBackup) {
    # 重新建立資料庫
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d postgres -c "DROP DATABASE IF EXISTS bms;"
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d postgres -c "CREATE DATABASE bms;"
    
    # 復原備份
    & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -f $latestBackup.FullName
    
    Write-Host "PostgreSQL復原完成" -ForegroundColor Green
} else {
    Write-Host "錯誤：無法找到PostgreSQL備份檔案" -ForegroundColor Red
}

# 啟動PostgreSQL服務
Start-Service "PostgreSQL Database Server 15"
```

```powershell
# SQL Server復原
Write-Host "開始SQL Server資料庫復原..." -ForegroundColor Yellow

$connectionString = "Server=**************,2433;Database=master;User ID=sa;Password=`$ystemOnlin168;TrustServerCertificate=True;"
$latestSqlBackup = Get-ChildItem "D:\Backups\BMS\sqlserver\full" -Filter "*.bak*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestSqlBackup) {
    $restoreCommand = @"
    USE master;
    ALTER DATABASE [ramsGIS] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    RESTORE DATABASE [ramsGIS] 
    FROM DISK = N'$($latestSqlBackup.FullName)'
    WITH REPLACE, CHECKSUM, STATS = 10;
    ALTER DATABASE [ramsGIS] SET MULTI_USER;
"@
    
    $connection = New-Object System.Data.SqlClient.SqlConnection
    $connection.ConnectionString = $connectionString
    $connection.Open()
    
    $command = $connection.CreateCommand()
    $command.CommandText = $restoreCommand
    $command.CommandTimeout = 3600
    $command.ExecuteNonQuery()
    
    $connection.Close()
    Write-Host "SQL Server復原完成" -ForegroundColor Green
} else {
    Write-Host "錯誤：無法找到SQL Server備份檔案" -ForegroundColor Red
}
```

#### 3.2 應用程式復原 (2-3小時)
```powershell
# 停止Tomcat服務
Write-Host "停止Tomcat服務..." -ForegroundColor Yellow
Stop-Service "Apache Tomcat 9.0 Tomcat9" -Force

# 備份現有應用程式
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
if (Test-Path "D:\apache-tomcat-9.0.98\webapps\src") {
    Copy-Item "D:\apache-tomcat-9.0.98\webapps\src" "D:\Recovery\app_backup_$timestamp" -Recurse -Force
}

# 復原應用程式
$latestAppBackup = Get-ChildItem "D:\Backups\BMS\application\full" -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestAppBackup) {
    # 清理現有目錄
    Remove-Item "D:\apache-tomcat-9.0.98\webapps\src" -Recurse -Force -ErrorAction SilentlyContinue
    
    # 解壓縮備份
    if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
        & "7z.exe" x $latestAppBackup.FullName -o"D:\apache-tomcat-9.0.98\webapps"
    } else {
        Expand-Archive -Path $latestAppBackup.FullName -DestinationPath "D:\apache-tomcat-9.0.98\webapps"
    }
    
    Write-Host "應用程式復原完成" -ForegroundColor Green
} else {
    Write-Host "錯誤：無法找到應用程式備份檔案" -ForegroundColor Red
}

# 復原配置檔案
$latestConfigBackup = Get-ChildItem "D:\Backups\BMS\application\configuration" -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($latestConfigBackup) {
    $tempPath = "D:\Recovery\config_temp"
    Expand-Archive -Path $latestConfigBackup.FullName -DestinationPath $tempPath -Force
    
    # 復原關鍵配置檔案
    Copy-Item "$tempPath\site.properties" "D:\apache-tomcat-9.0.98\webapps\src\WEB-INF\" -Force
    Copy-Item "$tempPath\web.xml" "D:\apache-tomcat-9.0.98\webapps\src\WEB-INF\" -Force
    Copy-Item "$tempPath\server.xml" "D:\apache-tomcat-9.0.98\conf\" -Force
    
    Remove-Item $tempPath -Recurse -Force
    Write-Host "配置檔案復原完成" -ForegroundColor Green
}
```

#### 3.3 服務啟動與驗證 (3-4小時)
```powershell
# 啟動服務
Write-Host "啟動服務..." -ForegroundColor Yellow

# 啟動PostgreSQL
Start-Service "PostgreSQL Database Server 15"
Start-Sleep -Seconds 30

# 啟動Tomcat
Start-Service "Apache Tomcat 9.0 Tomcat9"
Start-Sleep -Seconds 60

# 驗證服務狀態
$services = @("PostgreSQL Database Server 15", "Apache Tomcat 9.0 Tomcat9")
foreach ($service in $services) {
    $svc = Get-Service $service
    if ($svc.Status -eq "Running") {
        Write-Host "$service 狀態：運行中" -ForegroundColor Green
    } else {
        Write-Host "$service 狀態：$($svc.Status)" -ForegroundColor Red
    }
}

# 驗證資料庫連線
try {
    $env:PGPASSWORD = "S!@h@202203"
    $result = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT COUNT(*) FROM buildcase;" -t
    Write-Host "PostgreSQL連線測試：成功 (案件數：$($result.Trim()))" -ForegroundColor Green
} catch {
    Write-Host "PostgreSQL連線測試：失敗" -ForegroundColor Red
}

# 驗證網頁應用程式
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/" -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "網頁應用程式測試：成功" -ForegroundColor Green
    } else {
        Write-Host "網頁應用程式測試：回應碼 $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "網頁應用程式測試：失敗 - $($_.Exception.Message)" -ForegroundColor Red
}
```

---

## 🟡 第二級緊急應變程序

### 部分功能異常處理

#### 資料庫效能問題
```powershell
# 檢查資料庫連線數
$env:PGPASSWORD = "S!@h@202203"
$connections = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" -t

Write-Host "目前活動連線數：$($connections.Trim())"

# 檢查長時間執行的查詢
$longQueries = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';"

if ($longQueries) {
    Write-Host "發現長時間執行的查詢：" -ForegroundColor Yellow
    Write-Host $longQueries
}
```

#### 應用程式記憶體問題
```powershell
# 檢查Tomcat記憶體使用
$tomcatProcess = Get-Process -Name "java" -ErrorAction SilentlyContinue | Where-Object { $_.MainWindowTitle -like "*Tomcat*" }

if ($tomcatProcess) {
    $memoryUsageMB = [math]::Round($tomcatProcess.WorkingSet64 / 1MB, 2)
    Write-Host "Tomcat記憶體使用：$memoryUsageMB MB" -ForegroundColor $(if ($memoryUsageMB -gt 2048) { "Red" } elseif ($memoryUsageMB -gt 1024) { "Yellow" } else { "Green" })
    
    if ($memoryUsageMB -gt 2048) {
        Write-Host "記憶體使用過高，建議重啟Tomcat服務" -ForegroundColor Red
        
        # 可選：自動重啟
        $restart = Read-Host "是否要重啟Tomcat服務？(y/n)"
        if ($restart -eq "y") {
            Restart-Service "Apache Tomcat 9.0 Tomcat9" -Force
        }
    }
}
```

---

## 🟢 第三級緊急應變程序

### 預防性維護

#### 磁碟空間清理
```powershell
# 清理暫存檔案
Write-Host "清理暫存檔案..." -ForegroundColor Yellow

$tempPaths = @(
    "D:\Temp",
    "D:\apache-tomcat-9.0.98\temp",
    "D:\apache-tomcat-9.0.98\work",
    "D:\apache-tomcat-9.0.98\logs"
)

foreach ($path in $tempPaths) {
    if (Test-Path $path) {
        $beforeSize = (Get-ChildItem $path -Recurse -Force | Measure-Object -Property Length -Sum).Sum
        
        # 清理超過7天的檔案
        Get-ChildItem $path -Recurse -Force | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-7) } | Remove-Item -Force -Recurse
        
        $afterSize = (Get-ChildItem $path -Recurse -Force | Measure-Object -Property Length -Sum).Sum
        $freedSpace = [math]::Round(($beforeSize - $afterSize) / 1MB, 2)
        
        Write-Host "清理 $path : 釋放 $freedSpace MB" -ForegroundColor Green
    }
}
```

#### 日誌輪替
```powershell
# 壓縮舊的日誌檔案
$logPath = "D:\apache-tomcat-9.0.98\logs"
$oldLogs = Get-ChildItem $logPath -Filter "*.log" | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-3) }

foreach ($log in $oldLogs) {
    $zipName = "$($log.BaseName)_$(Get-Date -Format 'yyyyMMdd').zip"
    $zipPath = Join-Path $logPath $zipName
    
    if (!(Test-Path $zipPath)) {
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            & "7z.exe" a $zipPath $log.FullName
            Remove-Item $log.FullName -Force
            Write-Host "壓縮並刪除：$($log.Name)" -ForegroundColor Green
        }
    }
}
```

---

## 📊 應變後檢查清單

### 系統復原驗證
```powershell
# 完整系統驗證腳本
function Test-SystemRecovery {
    $testResults = @()
    
    # 1. 服務狀態檢查
    $services = @("PostgreSQL Database Server 15", "Apache Tomcat 9.0 Tomcat9")
    foreach ($service in $services) {
        $svc = Get-Service $service
        $testResults += @{
            "Test" = "Service Status: $service"
            "Result" = $svc.Status
            "Success" = $svc.Status -eq "Running"
        }
    }
    
    # 2. 資料庫連線檢查
    try {
        $env:PGPASSWORD = "S!@h@202203"
        $result = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT 1;" -t
        $testResults += @{
            "Test" = "PostgreSQL Connection"
            "Result" = "Success"
            "Success" = $true
        }
    } catch {
        $testResults += @{
            "Test" = "PostgreSQL Connection"
            "Result" = "Failed: $($_.Exception.Message)"
            "Success" = $false
        }
    }
    
    # 3. 網頁應用程式檢查
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/" -TimeoutSec 30
        $testResults += @{
            "Test" = "Web Application"
            "Result" = "HTTP $($response.StatusCode)"
            "Success" = $response.StatusCode -eq 200
        }
    } catch {
        $testResults += @{
            "Test" = "Web Application"
            "Result" = "Failed: $($_.Exception.Message)"
            "Success" = $false
        }
    }
    
    # 4. 資料完整性檢查
    try {
        $env:PGPASSWORD = "S!@h@202203"
        $caseCount = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT COUNT(*) FROM buildcase;" -t
        $flowCount = & "C:\Program Files\PostgreSQL\15\bin\psql.exe" -h localhost -p 5432 -U postgres -d bms -c "SELECT COUNT(*) FROM tbflow;" -t
        
        $testResults += @{
            "Test" = "Data Integrity"
            "Result" = "Cases: $($caseCount.Trim()), Flows: $($flowCount.Trim())"
            "Success" = [int]$caseCount.Trim() -gt 0 -and [int]$flowCount.Trim() -gt 0
        }
    } catch {
        $testResults += @{
            "Test" = "Data Integrity"
            "Result" = "Failed: $($_.Exception.Message)"
            "Success" = $false
        }
    }
    
    # 顯示測試結果
    Write-Host "`n=== 系統復原驗證結果 ===" -ForegroundColor Cyan
    foreach ($test in $testResults) {
        $color = if ($test.Success) { "Green" } else { "Red" }
        $status = if ($test.Success) { "PASS" } else { "FAIL" }
        Write-Host "[$status] $($test.Test): $($test.Result)" -ForegroundColor $color
    }
    
    # 整體結果
    $overallSuccess = ($testResults | Where-Object { !$_.Success }).Count -eq 0
    $overallStatus = if ($overallSuccess) { "系統復原成功" } else { "系統復原失敗" }
    $overallColor = if ($overallSuccess) { "Green" } else { "Red" }
    
    Write-Host "`n=== 整體結果：$overallStatus ===" -ForegroundColor $overallColor
    
    return $overallSuccess
}

# 執行系統驗證
$recoverySuccess = Test-SystemRecovery
```

### 事後報告
```powershell
# 產生事後報告
$incidentReport = @{
    "incident_id" = "INC-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    "incident_time" = $incidentStartTime
    "recovery_time" = Get-Date
    "duration_hours" = ((Get-Date) - $incidentStartTime).TotalHours
    "severity" = $incidentSeverity
    "root_cause" = $rootCause
    "recovery_actions" = $recoveryActions
    "lessons_learned" = $lessonsLearned
    "preventive_measures" = $preventiveMeasures
}

$reportJson = $incidentReport | ConvertTo-Json -Depth 3
$reportPath = "D:\Recovery\Reports\incident_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
$reportJson | Out-File -FilePath $reportPath -Encoding UTF8

Write-Host "事後報告已產生：$reportPath" -ForegroundColor Green
```

---

## 📞 緊急聯絡程序

### 通報流程
```
第一階段通報 (0-15分鐘)
│
├─ 系統管理員 (0912-345-678)
│   ├─ 初步問題評估
│   ├─ 確認事件嚴重性
│   └─ 決定是否升級
│
├─ IT主管 (0923-456-789)
│   ├─ 批准緊急應變程序
│   ├─ 調配人力資源
│   └─ 對外溝通協調
│
└─ 業務主管 (0934-567-890)
    ├─ 評估業務影響
    ├─ 通知相關使用者
    └─ 決定服務優先順序
```

### 通報範本

#### 緊急事件通報
```
主旨：【緊急】新北市違章建築管理系統 - 系統中斷通報

事件時間：{事件發生時間}
事件類型：{系統中斷/資料庫故障/應用程式異常/其他}
影響範圍：{全系統/部分功能/特定模組}
預估影響：{使用者數量/業務流程}

初步原因：{初步分析結果}
應變措施：{已採取或計畫採取的措施}
預估復原時間：{預估復原完成時間}

聯絡人：{姓名} {電話} {Email}
下次更新時間：{下次狀況更新時間}
```

#### 復原完成通報
```
主旨：【復原完成】新北市違章建築管理系統 - 服務恢復通報

事件摘要：
- 事件開始時間：{開始時間}
- 事件結束時間：{結束時間}
- 總影響時間：{總時數}

復原結果：
- 系統狀態：正常運作
- 資料完整性：完整
- 功能驗證：通過

後續行動：
- 持續監控24小時
- 事後檢討會議：{會議時間}
- 改善措施實施：{預定完成時間}

聯絡人：{姓名} {電話} {Email}
```

---

## 🔍 預防措施

### 預防性監控
```powershell
# 每日健康檢查腳本
function Daily-HealthCheck {
    $healthReport = @()
    
    # 檢查磁碟空間
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    foreach ($drive in $drives) {
        $freePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        $healthReport += @{
            "Check" = "Disk Space $($drive.DeviceID)"
            "Value" = "$freePercent%"
            "Status" = if ($freePercent -lt 10) { "Critical" } elseif ($freePercent -lt 20) { "Warning" } else { "Normal" }
        }
    }
    
    # 檢查服務狀態
    $services = @("PostgreSQL Database Server 15", "Apache Tomcat 9.0 Tomcat9")
    foreach ($service in $services) {
        $svc = Get-Service $service -ErrorAction SilentlyContinue
        $healthReport += @{
            "Check" = "Service $service"
            "Value" = if ($svc) { $svc.Status } else { "Not Found" }
            "Status" = if ($svc -and $svc.Status -eq "Running") { "Normal" } else { "Critical" }
        }
    }
    
    # 檢查最近備份
    $lastBackup = Get-ChildItem "D:\Backups\BMS\full" -Filter "*.sql*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    if ($lastBackup) {
        $backupAge = ((Get-Date) - $lastBackup.LastWriteTime).TotalHours
        $healthReport += @{
            "Check" = "Last Backup Age"
            "Value" = "$([math]::Round($backupAge, 1)) hours"
            "Status" = if ($backupAge -gt 48) { "Critical" } elseif ($backupAge -gt 24) { "Warning" } else { "Normal" }
        }
    }
    
    # 產生報告
    $criticalIssues = $healthReport | Where-Object { $_.Status -eq "Critical" }
    $warningIssues = $healthReport | Where-Object { $_.Status -eq "Warning" }
    
    if ($criticalIssues.Count -gt 0) {
        Write-Host "發現嚴重問題：" -ForegroundColor Red
        $criticalIssues | ForEach-Object { Write-Host "- $($_.Check): $($_.Value)" -ForegroundColor Red }
    }
    
    if ($warningIssues.Count -gt 0) {
        Write-Host "發現警告問題：" -ForegroundColor Yellow
        $warningIssues | ForEach-Object { Write-Host "- $($_.Check): $($_.Value)" -ForegroundColor Yellow }
    }
    
    if ($criticalIssues.Count -eq 0 -and $warningIssues.Count -eq 0) {
        Write-Host "系統健康狀態：正常" -ForegroundColor Green
    }
}

# 執行每日健康檢查
Daily-HealthCheck
```

### 定期維護作業
```powershell
# 週維護檢查清單
$weeklyMaintenance = @(
    "檢查備份檔案完整性",
    "清理過期日誌檔案", 
    "檢查系統效能指標",
    "更新系統監控設定",
    "驗證災難復原程序"
)

foreach ($task in $weeklyMaintenance) {
    Write-Host "□ $task" -ForegroundColor Yellow
}
```

---

**文件版本**：v1.0  
**最後更新**：2024年7月9日  
**緊急聯絡**：系統管理員 (0912-345-678)  
**24小時支援**：IT主管 (0923-456-789)  

*此為機密文件，僅供授權人員使用*