{"version": 3, "sources": ["canvas-to-blob.js"], "names": ["window", "CanvasPrototype", "HTMLCanvasElement", "prototype", "hasBlobConstructor", "Blob", "Boolean", "e", "hasArrayBufferViewSupport", "Uint8Array", "size", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "dataURIPattern", "dataURLtoBlob", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dataURI", "mediaType", "isBase64", "dataString", "byteString", "arrayBuffer", "intArray", "i", "bb", "matches", "match", "Error", "slice", "length", "decodeURIComponent", "charCodeAt", "type", "append", "getBlob", "toBlob", "mozGetAsFile", "callback", "quality", "self", "this", "setTimeout", "toDataURL", "msToBlob", "define", "amd", "module", "exports"], "mappings": "CAgBC,SAAWA,gBAGV,IAAIC,EACFD,EAAOE,mBAAqBF,EAAOE,kBAAkBC,UACnDC,EACFJ,EAAOK,MACP,WACE,IACE,OAAOC,QAAQ,IAAID,MACnB,MAAOE,GACP,OAAO,GAJX,GAOEC,EACFJ,GACAJ,EAAOS,YACP,WACE,IACE,OAAgD,MAAzC,IAAIJ,KAAK,CAAC,IAAII,WAAW,OAAOC,KACvC,MAAOH,GACP,OAAO,GAJX,GAOEI,EACFX,EAAOW,aACPX,EAAOY,mBACPZ,EAAOa,gBACPb,EAAOc,cACLC,EAAiB,0CACjBC,GACDZ,GAAsBO,IACvBX,EAAOiB,MACPjB,EAAOkB,aACPlB,EAAOS,YACP,SAAUU,GACR,IACEC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEFC,EAAUT,EAAQU,MAAMd,GACxB,IAAKa,EACH,MAAM,IAAIE,MAAM,oBAkBlB,IAfAV,EAAYQ,EAAQ,GAChBA,EAAQ,GACR,cAAgBA,EAAQ,IAAM,qBAClCP,IAAaO,EAAQ,GACrBN,EAAaH,EAAQY,MAAMH,EAAQ,GAAGI,QAGpCT,GAFEF,EAEWJ,KAGAgB,oBAHKX,GAMpBE,EAAc,IAAIN,YAAYK,EAAWS,QACzCP,EAAW,IAAIhB,WAAWe,GACrBE,EAAI,EAAGA,EAAIH,EAAWS,OAAQN,GAAK,EACtCD,EAASC,GAAKH,EAAWW,WAAWR,GAGtC,OAAItB,EACK,IAAIC,KAAK,CAACG,EAA4BiB,EAAWD,GAAc,CACpEW,KAAMf,MAGVO,EAAK,IAAIhB,GACNyB,OAAOZ,GACHG,EAAGU,QAAQjB,KAElBpB,EAAOE,oBAAsBD,EAAgBqC,SAC3CrC,EAAgBsC,aAClBtC,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,WAAW,WACLH,GAAWxC,EAAgB4C,WAAa7B,EAC1CwB,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,KAE5CD,EAASE,EAAKH,aAAa,OAAQJ,OAIhClC,EAAgB4C,WAAa7B,IAClCf,EAAgB6C,SAClB7C,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,WAAW,YAELT,GAAiB,cAATA,GAAyBM,IACnCxC,EAAgB4C,WAChB7B,EAEAwB,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,KAE5CD,EAASE,EAAKI,SAASX,OAK7BlC,EAAgBqC,OAAS,SAAUE,EAAUL,EAAMM,GACjD,IAAIC,EAAOC,KACXC,WAAW,WACTJ,EAASxB,EAAc0B,EAAKG,UAAUV,EAAMM,UAMhC,mBAAXM,QAAyBA,OAAOC,IACzCD,OAAO,WACL,OAAO/B,IAEkB,iBAAXiC,QAAuBA,OAAOC,QAC9CD,OAAOC,QAAUlC,EAEjBhB,EAAOgB,cAAgBA,EA5H1B,CA8HEhB"}