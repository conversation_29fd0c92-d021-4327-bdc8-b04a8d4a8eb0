package com.ezek.report;

import java.util.HashMap;
import javax.servlet.ServletContext;

/**
 * IM10101 整合範例 - 展示如何在現有程式碼中整合「只產生第二聯」功能
 */
public class IM10101Integration {
    
    /**
     * 原有的報表產生方法（完整報表）
     */
    public void generateCompleteReport(ServletContext application, String case_id, String repNM) {
        // 系統路徑參數
        String SEPARATOR = System.getProperty("file.separator");
        String REAL_PATH = application.getRealPath("/");

        // 專案所在位置
        String rootPath = REAL_PATH;
        rootPath += (REAL_PATH.substring(REAL_PATH.length() - 1).equals(SEPARATOR)) ? "" : SEPARATOR;

        HashMap<String, Object> mParameters = new HashMap<String, Object>();
        String[] conditionList = new String[1];
        // report位置
        String reportPath = rootPath;
        reportPath += "report" + SEPARATOR;
        String out_ext = "PDF"; //產出報表類型
        String dataNeedType = "ALL"; //報表類型
        // get the value(s) from the URL parameter(s)
        conditionList[0] = case_id;
        // 報表列印類型
        String TEMPLATE_FILE_NAME = repNM + ".pdf";
        mParameters.put("conditionList", conditionList);
        mParameters.put("outExt", "PDF"); // 下載副檔名
        mParameters.put("dataNeedType", dataNeedType);
        mParameters.put("outFileName", TEMPLATE_FILE_NAME);
        mParameters.put("ZIP_TAG", "ZIP_TAG");

        IM10101 IM10101 = new IM10101();
        IM10101.setAppPath(rootPath);
        IM10101.setReportPath(reportPath);
        IM10101.produceReport(mParameters);
    }
    
    /**
     * 新增：只產生第二聯的方法（方法一 - 使用專用方法）
     */
    public void generateSecondCopyOnly_Method1(ServletContext application, String case_id, String repNM