<%--JSP Page Init @1-68278D97--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new BlockTemplateServiceChecker()).check(request, response, getServletContext())) return;%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-F7DC76BC--%>
<%@include file="BlockTemplateHandlers.jsp"%>
<%
    if (!BlockTemplateModel.isVisible()) return;
    if (BlockTemplateParent != null) {
        if (!BlockTemplateParent.getChild(request.getParameter("controlName")).isVisible()) return;
    }
    pageContext.setAttribute("parent", BlockTemplateModel);
    pageContext.setAttribute("page", BlockTemplateModel);
    BlockTemplateModel.fireOnInitializeViewEvent(new Event());
    BlockTemplateModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) BlockTemplateModel;
    String pathToRoot = request.getAttribute("parentPathToRoot") == null ? "" : (String) request.getAttribute("parentPathToRoot");

    // Include once for client scripts
    String scripts = "|";
    String includes = (String) request.getAttribute("scriptIncludes");
    request.setAttribute("scriptIncludes", includes + scripts);

    if (!BlockTemplateModel.isVisible()) return;
    ((ModelAttribute) curPage.getAttribute("pathToCurrentPage")).setValue(pathToRoot + "Designs/Light/");
%>
<%--End Page Body--%>

<%--JSP Page Content @1-D62B9F65--%>
<div class="block clearfix">
        <div class="blockheader">
            <h3 class="t"><ccs:contentplaceholder name='Title'></ccs:contentplaceholder></h3>
        </div>
        <div class="blockcontent"><ccs:contentplaceholder name='Content'></ccs:contentplaceholder></div>
</div>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-3B3A1E3D--%>
<%BlockTemplateModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-D44CE0C3--%>
<%BlockTemplateModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

