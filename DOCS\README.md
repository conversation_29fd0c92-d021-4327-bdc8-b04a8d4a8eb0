# 新北市違章建築管理系統 - 文件中心

## 📚 文件架構說明

本文件中心採用三層架構組織，確保文件的有效管理與維護：

```
DOCS/
├── 01_ACTIVE/          # 活躍文件（需頻繁更新）
│   └── 違章建築資料拋送至國土署系統/  # 國土署系統整合專案（進行中）
├── 02_REFERENCE/       # 參考文件（穩定版本）
├── 03_ARCHIVE/         # 歸檔文件（舊版本）
├── 04_WEBSERVICE_API/  # WebService API文件
└── 違章建築資料拋送至國土署系統/  # 國土署系統完整實作
```

## 🎯 快速導航

### 核心文件（必讀）

| 文件名稱 | 位置 | 說明 |
|----------|------|------|
| [系統架構總覽](./02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md) | 02_REFERENCE | 完整的系統技術架構說明 |
| [業務流程指南](./02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md) | 02_REFERENCE | 三階段業務流程詳解 |
| [資料庫完整指南](./02_REFERENCE/DATABASE_COMPLETE_GUIDE.md) | 02_REFERENCE | 資料庫架構與操作指南 |
| [技術實施指南](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md) | 02_REFERENCE | 開發規範與最佳實踐 |

### 專題文件

| 類別 | 文件 | 說明 |
|------|------|------|
| **資料表描述** | [資料表描述說明表](./02_REFERENCE/TABLE_DESCRIPTIONS/) | 完整的資料表欄位描述系統 |
| **協同作業** | [協同機制完整指南](./02_REFERENCE/COLLABORATION_MECHANISM_COMPLETE_GUIDE.md) | 協同作業設計與實施 |
| **協同作業** | [協同退回實施手冊](./02_REFERENCE/COLLABORATION_RETURN_IMPLEMENTATION_GUIDE.md) | 協同退回功能實作 |
| **狀態碼系統** | [狀態碼文件](./02_REFERENCE/STATUS_CODE_SYSTEM/) | 狀態碼定義與轉換規則 |
| **深度分析** | [FOSSIL系統分析](./02_REFERENCE/FOSSIL_SYSTEM_ANALYSIS/) | Legacy系統深度剖析 |

### 活躍專案

| 專案名稱 | 說明 | 狀態 |
|----------|------|------|
| [資料表描述說明表](./01_ACTIVE/TABLE_DESCRIPTIONS_PROJECT_COMPLETION.md) | 10-Agent並行建立完整資料表描述系統 | ✅ 已完成 |
| [國土署系統整合](./違章建築資料拋送至國土署系統/) | 違章建築資料拋送至國土署系統 | ✅ 已完成 |

## 📊 系統概況

### 技術架構
- **開發框架**：CodeCharge Studio（三層架構）
- **應用伺服器**：Apache Tomcat 9.0.98
- **資料庫**：PostgreSQL（主）+ SQL Server（GIS）
- **前端技術**：Bootstrap 5.3.3 + jQuery 3.7.1

### 業務規模
- **案件總數**：371,081筆
- **流程記錄**：1,004,853筆
- **狀態碼數**：126種（RLT類型）
- **使用者數**：約500人

### 系統特色
- ✅ 三類違建專業分工（一般/廣告/下水道）
- ✅ 三階段處理流程（認定→排拆→結案）
- ✅ 多部門協同作業機制
- ✅ 完整的證據保全系統
- ✅ 91個觸發器資料防護網

## 🔍 文件分類索引

### 1. 系統架構文件
- `02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md` - 系統架構總覽
- `02_REFERENCE/FOSSIL_SYSTEM_ANALYSIS/` - Legacy系統分析集
  - `DEEP_LAYERED_ANALYSIS.md` - 深層架構分析
  - `ULTIMATE_FOSSIL_ANALYSIS.md` - 終極化石分析
  - `COMPLETE_SYSTEM_ARCHITECTURE.md` - 完整架構文件

### 2. 資料庫文件
- `02_REFERENCE/DATABASE_COMPLETE_GUIDE.md` - 資料庫完整指南
- `02_REFERENCE/TABLE_DESCRIPTIONS/` - 資料表描述說明表系統
  - `TABLE_DESCRIPTIONS_MASTER.md` - 15個核心表完整描述
  - `CREATE_TABLE_DESCRIPTIONS_SQL.sql` - 描述管理表建立腳本
- `02_REFERENCE/FOSSIL_SYSTEM_ANALYSIS/IBMCODE_SYSTEM_PARAMETERS.md` - IBMCODE參數系統
- `02_REFERENCE/FOSSIL_SYSTEM_ANALYSIS/DEEP_SQL_ANALYSIS.md` - SQL深度分析

### 3. 業務流程文件
- `02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md` - 業務流程完整指南
- `02_REFERENCE/flow/` - 各階段流程詳解
  - `01_report/` - 掛號通報流程
  - `02_inspection/` - 現場勘查流程
  - `03_determination/` - 認定審核流程
  - `04_notification/` - 完成通知流程
  - `05_demolition_notice/` - 拆除通知流程
  - `06_demolition/` - 排拆執行流程
  - `11_closing/` - 結案流程

### 4. 狀態碼文件
- `02_REFERENCE/STATUS_CODE_SYSTEM/` - 狀態碼系統文件集
  - `STATUS_CODE_ENCYCLOPEDIA.md` - 狀態碼百科全書
  - `STATUS_CODE_STATE_MACHINE_CORRECTED.md` - 狀態機修正版
  - `RLT_STATUS_CODE_ENCODING_RULES.md` - 編碼規則說明

### 5. 技術實施文件
- `02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md` - 技術實施指南
- `02_REFERENCE/CODECHARGE_ANALYSIS.md` - CodeCharge分析
- `02_REFERENCE/COMPONENTS_USAGE.md` - 元件使用說明

### 6. 國土署系統整合
- `違章建築資料拋送至國土署系統/` - 完整.NET系統實作
  - `NTPC.MOI.DataExporter/` - 資料匯出服務
  - `MOI.MonitoringDashboard/` - 監控儀表板
  - `ViolationDataSync.WebAPI/` - 同步API
  - `DOCS/` - 完整設計與實作文件

### 7. WebService API文件
- `04_WEBSERVICE_API/` - WebService API文件集
  - `01_WebService_Overview.md` - WebService概覽
  - `02_WebService_Stages_Basic.md` - 基礎階段API
  - `03_WebService_Stages_Advanced.md` - 進階階段API
  - `04_WebService_CodeTables.md` - 代碼表API

### 8. 專案管理文件
- `03_ARCHIVE/TASK_TRACKING_DETAIL_V3.md` - 任務追蹤詳情（已歸檔）
- `03_ARCHIVE/2025-07-09_review/` - 本次審查歸檔文件

## 📈 文件維護指南

### 文件更新原則
1. **活躍文件**（01_ACTIVE）：即時更新，反映最新狀態
2. **參考文件**（02_REFERENCE）：版本控制，重大更新時更新
3. **歸檔文件**（03_ARCHIVE）：只讀保存，不再更新

### 文件命名規範
- 使用大寫英文蛇形命名：`DOCUMENT_NAME.md`
- 版本號標記：`DOCUMENT_NAME_V2.md`
- 日期標記：`DOCUMENT_NAME_20240101.md`

### 文件品質要求
- ✅ 結構清晰，包含目錄
- ✅ 內容準確，資訊完整
- ✅ 格式統一，易於閱讀
- ✅ 定期審查，保持更新

## 🚀 快速開始

### 新進人員建議閱讀順序
1. **系統概述**：先閱讀[系統架構總覽](./02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md)
2. **業務理解**：了解[業務流程](./02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md)
3. **技術細節**：研讀[技術實施指南](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md)
4. **深入學習**：探索專題文件

### 開發人員必讀
- [CodeCharge開發規範](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md#codecharge開發規範)
- [資料庫操作指南](./02_REFERENCE/DATABASE_COMPLETE_GUIDE.md#資料庫操作指南)
- [API開發規範](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md#api開發規範)

### 維運人員必讀
- [系統部署指南](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md#系統部署指南)
- [維護與監控](./02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md#維護與監控)
- [緊急處理程序](./02_REFERENCE/DATABASE_COMPLETE_GUIDE.md#緊急處理程序)

## 📞 聯絡資訊

### 文件維護團隊
- 系統架構：架構組
- 業務流程：業務分析組
- 技術實施：開發組
- 資料庫：DBA組

### 問題回報
如發現文件錯誤或有改進建議，請透過以下管道回報：
- 內部系統：提交文件修正單
- Email：<EMAIL>

---

**最後更新時間**：2025-07-09  
**文件版本**：v2.1  
**維護單位**：系統開發組

### 📝 更新記錄
- 2025-01-09：資料表描述說明表專案完成
  - 使用10個Agent並行分析建立完整資料表描述系統
  - 新增TABLE_DESCRIPTIONS目錄至02_REFERENCE/
  - 涵蓋15個核心表、210個欄位的完整描述
  - 建立資料庫管理表、查詢視圖、管理函數
  - 提供完整的使用指南和維護機制
- 2025-07-09：文件審查與重組
  - 歸檔5份過時文件至03_ARCHIVE/2025-07-09_review/
  - 更新國土署系統整合專案狀態（已完成）
  - 新增WebService API文件分類
  - 更新文件索引與結構說明
  - 修正協同退回功能（235/245/255）的實施狀態說明