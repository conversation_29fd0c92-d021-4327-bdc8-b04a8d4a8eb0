# 異常處理標準作業程序 (Exception Handling SOP)

## 📋 文件資訊
- **文件編號**: T2.8.3-SOP
- **文件目的**: 提供開發團隊異常處理的標準作業程序
- **適用對象**: 開發人員、維護人員
- **建立日期**: 2025-07-05

---

## 🎯 快速參考指南

### 異常處理決策樹

```mermaid
graph TD
    A[發生異常] --> B{異常類型?}
    
    B -->|資料庫| C[使用資料庫異常處理模式]
    B -->|驗證| D[使用驗證錯誤處理模式]
    B -->|業務邏輯| E[使用業務異常處理模式]
    B -->|系統| F[使用系統異常處理模式]
    
    C --> G[記錄詳細錯誤]
    D --> H[收集所有錯誤]
    E --> I[顯示友善訊息]
    F --> J[導向錯誤頁面]
    
    G --> K[回滾交易]
    H --> L[一次顯示所有錯誤]
    I --> M[提供操作建議]
    J --> N[記錄並通知管理員]
```

---

## 📝 標準異常處理模式

### 1. 資料庫操作 SOP

```java
// ✅ 正確做法
public boolean updateCase(String caseId, Map<String, Object> data) {
    DBConnectionManager dbcm = null;
    Connection conn = null;
    PreparedStatement pstmt = null;
    boolean success = false;
    
    try {
        // 1. 取得連線
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection(CONNECTION_NAME);
        
        // 2. 開始交易
        conn.setAutoCommit(false);
        
        // 3. 執行更新
        String sql = "UPDATE ibmcase SET ... WHERE case_id = ?";
        pstmt = conn.prepareStatement(sql);
        // 設定參數...
        pstmt.executeUpdate();
        
        // 4. 提交交易
        conn.commit();
        success = true;
        
    } catch (SQLException e) {
        // 5. 記錄錯誤
        System.err.println("updateCase ::: SQLException ::: " + e.getMessage());
        e.printStackTrace();
        
        // 6. 回滾交易
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException re) {
                System.err.println("Rollback failed: " + re.getMessage());
            }
        }
        
    } finally {
        // 7. 清理資源
        try {
            if (pstmt != null) pstmt.close();
            if (conn != null) {
                conn.setAutoCommit(true);
                dbcm.freeConnection(CONNECTION_NAME, conn);
            }
        } catch (Exception e) {
            System.err.println("Resource cleanup failed: " + e.getMessage());
        }
    }
    
    return success;
}
```

### 2. 欄位驗證 SOP

```java
// ✅ 正確做法 - 收集所有錯誤後一次顯示
public void validateForm(CCSEvent e) {
    boolean hasError = false;
    
    // 1. 驗證必填欄位
    if (StringUtils.isEmpty(getValue("case_id"))) {
        e.getRecord().getControl("case_id").addError("案件編號是必須的");
        hasError = true;
    }
    
    // 2. 驗證格式
    String regNum = getValue("reg_num");
    if (!StringUtils.isEmpty(regNum) && !EzekUtils.checkRegNum(regNum)) {
        e.getRecord().getControl("reg_num").addError("認定號碼必須為10碼");
        hasError = true;
    }
    
    // 3. 驗證業務規則
    if (!isValidStatusTransition(currentStatus, newStatus)) {
        e.getRecord().addError("無效的狀態轉換：從 " + currentStatus + " 到 " + newStatus);
        hasError = true;
    }
    
    // 4. 如果有錯誤，停止處理
    if (hasError) {
        return;  // 框架會自動顯示所有錯誤
    }
    
    // 5. 執行業務邏輯
    processBusinessLogic();
}
```

### 3. 檔案處理 SOP

```java
// ✅ 正確做法
public void handleFileUpload(HttpServletRequest request, HttpServletResponse response) {
    try {
        // 1. 檢查上傳目錄
        File uploadDir = new File(UPLOAD_PATH);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // 2. 處理上傳
        MultipartRequest multi = new MultipartRequest(
            request, 
            UPLOAD_PATH, 
            MAX_FILE_SIZE, 
            "UTF-8",
            new DefaultFileRenamePolicy()
        );
        
        // 3. 驗證檔案類型
        Enumeration files = multi.getFileNames();
        while (files.hasMoreElements()) {
            String name = (String) files.nextElement();
            File file = multi.getFile(name);
            
            if (!isValidFileType(file)) {
                file.delete();  // 刪除無效檔案
                throw new Exception("不支援的檔案類型");
            }
        }
        
        // 4. 回應成功
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"success\":true}");
        
    } catch (IOException e) {
        // 5. IO 錯誤處理
        handleUploadError(response, "檔案上傳失敗：" + e.getMessage());
        
    } catch (Exception e) {
        // 6. 其他錯誤處理
        handleUploadError(response, e.getMessage());
    }
}

private void handleUploadError(HttpServletResponse response, String message) {
    try {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"success\":false,\"error\":\"" + message + "\"}");
    } catch (IOException ioe) {
        System.err.println("Error sending error response: " + ioe.getMessage());
    }
}
```

### 4. 會話管理 SOP

```java
// ✅ 正確做法
public void checkSession(CCSevent e) {
    // 1. 檢查會話
    HttpSession session = e.getPage().getRequest().getSession(false);
    
    if (session == null) {
        // 2. 無會話 - 導向登入
        e.getPage().setRedirectString("timeout_err.jsp");
        return;
    }
    
    // 3. 檢查登入狀態
    String loginPass = (String) session.getAttribute("LoginPass");
    if (!"True".equals(loginPass)) {
        // 4. 未登入 - 導向登入
        e.getPage().setRedirectString("timeout_err.jsp");
        return;
    }
    
    // 5. 檢查會話逾時
    int maxInactive = session.getMaxInactiveInterval();
    long lastAccessed = session.getLastAccessedTime();
    long now = System.currentTimeMillis();
    
    if ((now - lastAccessed) > (maxInactive * 1000)) {
        // 6. 已逾時 - 清理並導向
        session.invalidate();
        e.getPage().setRedirectString("timeout_err.jsp");
        return;
    }
    
    // 7. 更新最後存取時間
    session.setAttribute("lastAccessTime", now);
}
```

---

## ⚠️ 常見錯誤與正確做法

### ❌ 錯誤：忽略異常
```java
try {
    // 某些操作
} catch (Exception e) {
    // 什麼都不做 - 這是錯誤的！
}
```

### ✅ 正確：至少記錄錯誤
```java
try {
    // 某些操作
} catch (Exception e) {
    System.err.println("操作失敗：" + e.getMessage());
    e.printStackTrace();
    // 或使用日誌框架
}
```

### ❌ 錯誤：洩露敏感資訊
```java
catch (SQLException e) {
    // 直接顯示 SQL 錯誤給使用者
    errorMessage = "SQL Error: " + e.getMessage();
}
```

### ✅ 正確：顯示友善訊息
```java
catch (SQLException e) {
    // 記錄詳細錯誤供除錯
    System.err.println("SQL Error: " + e.getMessage());
    // 顯示通用訊息給使用者
    errorMessage = "資料處理發生錯誤，請稍後再試";
}
```

### ❌ 錯誤：資源洩漏
```java
Connection conn = getConnection();
PreparedStatement pstmt = conn.prepareStatement(sql);
pstmt.executeUpdate();
// 忘記關閉連線和 statement！
```

### ✅ 正確：確保資源釋放
```java
Connection conn = null;
PreparedStatement pstmt = null;
try {
    conn = getConnection();
    pstmt = conn.prepareStatement(sql);
    pstmt.executeUpdate();
} finally {
    if (pstmt != null) try { pstmt.close(); } catch (Exception e) {}
    if (conn != null) try { conn.close(); } catch (Exception e) {}
}
```

---

## 📊 錯誤訊息對照表

| 情況 | 內部處理 | 使用者看到的訊息 |
|------|---------|----------------|
| 主鍵重複 | 記錄完整 SQL 錯誤 | "資料已存在" |
| 外鍵違反 | 記錄表格和欄位 | "相關資料不完整" |
| 資料過長 | 記錄欄位和長度 | "輸入內容超過長度限制" |
| 格式錯誤 | 記錄期望格式 | "資料格式不正確" |
| 權限不足 | 記錄使用者和操作 | "您沒有執行此操作的權限" |
| 系統錯誤 | 記錄堆疊追蹤 | "系統暫時無法處理，請稍後再試" |

---

## 🔧 偵錯技巧

### 1. 錯誤定位
```java
System.err.println("ClassName ::: MethodName ::: Line " + 
    Thread.currentThread().getStackTrace()[1].getLineNumber() + 
    " ::: " + e.getMessage());
```

### 2. 交易追蹤
```java
System.err.println("Transaction Start: " + System.currentTimeMillis());
// ... 執行操作 ...
System.err.println("Transaction End: " + System.currentTimeMillis());
```

### 3. 參數記錄
```java
System.err.println("Parameters: caseId=" + caseId + 
    ", status=" + status + ", user=" + userId);
```

---

## 📝 檢查清單

### 程式碼審查時
- [ ] 所有可能拋出異常的操作都有 try-catch
- [ ] 資料庫交易有正確的 commit/rollback
- [ ] 資源在 finally 區塊中釋放
- [ ] 錯誤訊息對使用者友善
- [ ] 敏感資訊不會洩露
- [ ] 有適當的錯誤記錄

### 測試時
- [ ] 測試正常流程
- [ ] 測試各種異常情況
- [ ] 驗證錯誤訊息顯示
- [ ] 確認交易回滾正常
- [ ] 檢查資源是否正確釋放

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*配合 EXCEPTION_HANDLING_FLOWS.md 使用*
*完成日期：2025-07-05*