﻿//NOTE: 宣告 -------------------------------------------------------------------
var ezekMap;
var identifyTask, identifyParams , nowCenter, nowZoom;
var nowCenterSyncCount = 0 ;
var loadLayerCnt = 0;
var tiledMap = null,TgosLayer_F2 = null;

const MarkerInfo = { // 1 已結案 , 2 處理中 , 3 查報中 , 共ABCZ:４類
	Marker_1: null,
	Marker_1_NA: null,
	Marker_1_A: null,
	Marker_1_B: null,
	Marker_1_C: null,
	Marker_1_Z: null,
	Marker_2: null,
	Marker_2_NA: null,
	Marker_2_A: null,
	Marker_2_B: null,
	Marker_2_C: null,
	Marker_3: null,
	Marker_3_NA: null,
	Marker_3_A: null,
	Marker_3_B: null,
	Marker_3_C: null,
	Marker_4: null,
	Marker_4_NA: null,
	Marker_4_A: null,
	Marker_4_B: null,
	Marker_4_C: null,
}

const ezekMapInfo = {
	// gtoken: "8jbKtDd4xPTufcLoRYbdMWmsxSyLkUW1kI8k54MG889cp2mjZriyJkbIa14ZPHxU",
	swipeWidget: null,
	LayerArray: [],
	queryResponse: null,
	infoWindowArray: [],
	featureArray: [],
	DMLLayer: null,
	TextLayer: null,
	BaseMap: null,
	BaseLabelMap: null,
	ZoneMap: null,
	isEnableClick: false,
	identifyParams: null,
	cpamigiIndexKey: "",
	UavLayer: null,
	UavLayerStat :"N",
	uavShowLayer: null
};

const ExcisionMapInfo = {
	mapDiv1_1: null,
	mapDiv2_1: null,
	mapDiv2_2: null,
	mapDiv4_1: null,
	mapDiv4_2: null,
	mapDiv4_3: null,
	mapDiv4_4: null,
	separatedBtn2_1: null,
	separatedBtn2_2: null,
	separatedBtn4_1: null,
	separatedBtn4_2: null,
	separatedBtn4_3: null,
	separatedBtn4_4: null,
	swipeIndex_Right: 3, //原本19
	swipeIndex_Left: 0
};
var bcmsLayer = null;
var sys_arcgisMapServiceURL = location.protocol+"//limit.ntpc.gov.tw/arcgis/rest/services/";
// 新北測試 token
//var token = "76yESXrovpWSmiRoKhFX2zjJTWdp9BN0GeAsi58YD-KRgLe-RUw32UO1DQYSoYav"; //禁限建
//var token="rJzVxzRxWwAtW_nmhwMqpNqiWfehMOTiQx8MPL1JUkbrWJRuCz-n8uCADya4EZLD"; //已失效
//var token = "AzS0XMKer0W7BBXaHWd5gICJxZ7ggrXiFZ49OV8i5Qfr0DIdS-u0Pkw3eV0ABo61"; 1110506
//var token = "gwQ-sLeHW7LEBLhRhcVxOT4Xw_RbmpwqaNhNm6h4NdAzRvclvDCJUR2bZsB5Oqwj"; 202305失效
//var token = "447oNhHjoHa57PAsmntmpqhSvdOItoqELmctHXufN0aU0QVW4__vAcc1liVnAsEs";
var token = "17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token="+token, 
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token="+token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer", 
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token="+token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token="+token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";
var bcmsLayerName = "地籍套繪圖";
var MapServerObjArray = [];

/**
 * 初始化所有地圖
 */
function ezek_InitMap() {
	require([
		"esri/map",
		"esri/dijit/Scalebar",
		"esri/symbols/SimpleFillSymbol",
		"esri/symbols/SimpleLineSymbol",
		"esri/dijit/Popup",
		"esri/Color",
		"esri/SpatialReference",
		"dojo/dom-construct",
		"esri/tasks/GeometryService",
		"esri/geometry/webMercatorUtils",
		"esri/layers/KMLLayer",
		"dojo/dom",
		"dojo/domReady!"
	], function (
		Map, Scalebar, SimpleFillSymbol, SimpleLineSymbol, Popup, Color, SpatialReference, domConstruct, GeometryService, webMercatorUtils, KMLLayer ,dom
	) {
		
		// arcGIS Server 與 Web Server位於不同網域, 需額外透過Proxy轉址
		esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";

		//設定座標轉換服務
		//var sys_arcgisMapService = "https://building.tycg.gov.tw/arcgis/rest/services/";
		
		var sys_arcgisMapService = location.protocol+"//limit.ntpc.gov.tw/arcgis/rest/services/";
		gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");
		wgs84SR = new SpatialReference({
			wkid: 4326
		}); //WGS84
		twd97SR = new SpatialReference({
			wkid: 102443
		}); //TWD_97_TM2
		webMercatorSR = new SpatialReference({
			wkid: 102100
		}); //WGS 1984 Web Mercator Projection
		//台灣全圖範圍(102100:WGS 1984 Web Mercator Projection)

		// 初始化基本位置
		initExtent = new esri.geometry.Extent({
			xmax: 354539,
			xmin: 262808,
			ymax: 2797434,
			ymin: 2759021,
			"spatialReference": {
				wkid: 102443,
				latestWkid: 3826
			}
		});
		// nowCenter = initExtent;

		// 初始化 infoWindow
		var popup = new Popup({
			fillSymbol: new SimpleFillSymbol(SimpleFillSymbol.STYLE_SOLID,
				new SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID,
					new Color([255, 0, 0]), 3), new Color([255, 255, 0, 0.25]))
		}, domConstruct.create("div"));

		// 初始化地圖
		ezekMap = new Map("mapDiv", {
			logo: false,
			// extent: initExtent,
			slider: true,
			sliderStyle: "small",
			sliderPosition: "top-left",
			infoWindow: popup,
			spatialReference: {
				wkid: 102443
			}
			// minScale: 5000000000,
			// minZoom: 0,
			// maxZoom: 22
		});

		// 初始化 PictureMarkerSymbol 地圖圖標
		initPictureMarker();
		// 初始化影像比對的地圖
		initExcisionMap();

		// 綁定 life cycle event 事件
		ezekMap.on("load", mapReady);
		dojo.connect(ezekMap, "onLoad", initAllLayer);
		dojo.connect(ezekMap, "onLoad", addAllLayer);
		//dojo.connect(ezekMap, "onLoad", initGroupList);
		dojo.connect(ezekMap, "onLoad", initLocation);
		dojo.connect(ezekMap, "onLoad", initSwipeLayer);
		dojo.connect(ezekMap, "onLoad", initSelectionSts);
		dojo.connect(ezekMap, "onLoad", initSelectionSts2);
		

		var ezekMap_sync = function () {
			//因為extent會抓到Drag前的extent，所以延遲1ms，等移動完畢後再去抓取新的extent
			setTimeout(function () {
				updateNowLocation(ezekMap.extent.getCenter(),ezekMap.getZoom());
			}, 1);
		};
		dojo.connect(ezekMap, "onMouseDragEnd", ezekMap_sync);
		dojo.connect(ezekMap, "onZoomEnd", ezekMap_sync);

		
		ezekMap.on("layers-add-result", function () {
			ezekMapScaleToFullExtent();
		});
		
		//加入經緯度顯示列
		var latlngInfo = '<div id="latlngInfo" style="position: absolute;bottom: 10px;left: 428px;z-index: 50;">&nbsp;</div>';
		$("#mapDiv_root").prepend($(latlngInfo));
		dojo.connect(ezekMap, "onMouseMove", showCoordinates);
		dojo.connect(ezekMap, "onMouseDrag", showCoordinates);
		
		
		tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
			"opacity": 1,
			id: normalTileName2
		});
        ezekMap.addLayer(tiledMap, 0);
		ezekMapInfo.BaseMap = tiledMap;
		ezekMapInfo.BaseLabelMap = tiledMap;
		
		// 1: 1/1000以上 電子地圖
		TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
			"opacity": 1,
			id: normalTileName1
		});
		ezekMap.addLayer(TgosLayer_F2, 1);
		

		// UAV 航拍圖層(於Grouplist , 可開關)
		// var uavShowLayer = new esri.layers.ArcGISDynamicMapServiceLayer(UAVUrl, {
		// 	id: "uavShowLayer"
		// });
		// ezekMap.addLayer(uavShowLayer);
		// ezekMapInfo.uavShowLayer = uavShowLayer;

		//加入比例尺顯示
		var scalebar = new Scalebar({
			map: ezekMap,
			scalebarUnit: "metric"
		});
		esri.config.defaults.map.slider = {
			right: "165px",
			bottom: null,
			width: "200px",
			height: null
		};
		$(".esriScalebar").css("bottom", "50px").css("z-index", "50").css("position", "relative");
		
		// 此處為調整經緯度的排版 (每個 Layer ADD後都會做一次)
		dojo.connect(ezekMap, "onLayerAddResult", function (evt) {
			var maplayerArray = getLayerArray();
			
			if(maplayerArray.length == 74){
				//圖層讀取完畢
				$.unblockUI();
			}
			
			$(".esriScalebar").css("left", "428px");
		});

		// 更改滑鼠座標文字數值 (桃園暫不使用)
		function showCoordinates(evt) {
			//if (typeof evt.mapPoint.x != 'undefined') document.getElementById("coordInfo").innerHTML = "TWD97 (" + evt.mapPoint.x.toFixed(2) + ", " + evt.mapPoint.y.toFixed(2) + ")";
			if (typeof evt.mapPoint.x != 'undefined') {

				var mp = webMercatorUtils.webMercatorToGeographic(evt.mapPoint);
				// console.log(evt.mapPoint);

				// mp.x.toFixed(3) + ", " + mp.y.toFixed(3);
				// dom.byId("latlngInfo").innerHTML = mp.x.toFixed(3) + ", " + mp.y.toFixed(3); // 暫時先隱藏
				// var wgs84Coord = WebMercatorUtils.xyToLngLat(evt.mapPoint.x, evt.mapPoint.y);

				// document.getElementById("latlngInfo").innerHTML = wgs84Coord[0].toFixed(7) + "E, " + wgs84Coord[1].toFixed(6) + "N";
			}
		}
	});
}

function syncMapExtent(_map,time)
{

	require([
		"esri/symbols/PictureMarkerSymbol",
	], function (PictureMarkerSymbol) {

		// console.log("-------------"+_map.id+"  syncMapExtent--------------");
	
		if(nowCenter && nowZoom){


			setTimeout(function() {
				_map.setZoom(nowZoom);
				_map.centerAt(nowCenter);
			}, time);
		}

		// console.log("---------------------------");

	});

	
	
}

function updateNowLocation(_center,_zoom)
{
	// console.log("updateNowLocation----------");

	// console.log("new center : " , _center);
	// console.log("new Zoom : " , _zoom);
	nowCenter = _center;
	nowZoom = _zoom;

	// console.log("---------------------------");
}


/**
 * 初始化影像比對的地圖
 */
function initExcisionMap() {
	// 影像比對-二分格連續模式 初始化地圖物件
	ExcisionMapInfo.mapDiv1_1 = new ExcisionMap("mapDiv1_1");
	//ExcisionMapInfo.mapDiv1_1.extendLayer();
	// 影像比對-二分格鏡射模式 初始化地圖物件
	ExcisionMapInfo.mapDiv2_1 = new ExcisionMap("mapDiv2_1");
	ExcisionMapInfo.mapDiv2_2 = new ExcisionMap("mapDiv2_2");
	// 影像比對-二分格鏡射模式 初始化地圖物件
	ExcisionMapInfo.mapDiv4_1 = new ExcisionMap("mapDiv4_1");
	ExcisionMapInfo.mapDiv4_2 = new ExcisionMap("mapDiv4_2");
	ExcisionMapInfo.mapDiv4_3 = new ExcisionMap("mapDiv4_3");
	ExcisionMapInfo.mapDiv4_4 = new ExcisionMap("mapDiv4_4");

	// 二分隔連續模式callBack - 同步Extent 
	var ExcisionMap1_sync = function () {
		var id = this.id;
		//因為extent會抓到Drag前的extent，所以延遲1ms，等移動完畢後再去抓取新的extent
		setTimeout(function () {
			var extent = ExcisionMapInfo[id].getMap().extent;
			updateNowLocation(extent.getCenter(),ExcisionMapInfo[id].getMap().getZoom());
		}, 1);
	};
	// 二分隔鏡射模式callBack - 同步Extent 
	var ExcisionMap2_sync = function () {
		var id = this.id;
		//因為extent會抓到Drag前的extent，所以延遲1ms，等移動完畢後再去抓取新的extent
		setTimeout(function () {
			var extent = ExcisionMapInfo[id].getMap().extent;
			ExcisionMapInfo.mapDiv2_1.getMap().setExtent(extent);
			ExcisionMapInfo.mapDiv2_2.getMap().setExtent(extent);
			updateNowLocation(extent.getCenter(),ExcisionMapInfo[id].getMap().getZoom());
		}, 1);
	};
	// 四分隔鏡射模式callBack - 同步Extent 
	var ExcisionMap4_sync = function () {
		var id = this.id;
		//因為extent會抓到Drag前的extent，所以延遲1ms，等移動完畢後再去抓取新的extent
		setTimeout(function () {
			var extent = ExcisionMapInfo[id].getMap().extent;
			ExcisionMapInfo.mapDiv4_1.getMap().setExtent(extent);
			ExcisionMapInfo.mapDiv4_2.getMap().setExtent(extent);
			ExcisionMapInfo.mapDiv4_3.getMap().setExtent(extent);
			ExcisionMapInfo.mapDiv4_4.getMap().setExtent(extent);

			updateNowLocation(extent.getCenter(),ExcisionMapInfo[id].getMap().getZoom());
		}, 1);
	};

	// 初始化地圖內容(圖層、Callback)
	initMapContent(ExcisionMapInfo.mapDiv1_1, ExcisionMap1_sync, null, null);
	initMapContent(ExcisionMapInfo.mapDiv2_1, ExcisionMap2_sync, 'separatedBtn2_1', false);
	initMapContent(ExcisionMapInfo.mapDiv2_2, ExcisionMap2_sync, 'separatedBtn2_2', false);
	initMapContent(ExcisionMapInfo.mapDiv4_1, ExcisionMap4_sync, 'separatedBtn4_1', false);
	initMapContent(ExcisionMapInfo.mapDiv4_2, ExcisionMap4_sync, 'separatedBtn4_2', false);
	initMapContent(ExcisionMapInfo.mapDiv4_3, ExcisionMap4_sync, 'separatedBtn4_3', false);
	initMapContent(ExcisionMapInfo.mapDiv4_4, ExcisionMap4_sync, 'separatedBtn4_4', false);

	// 初始化函數
	function initMapContent(InfoObj, callback, BtnId, dropRight) {
		InfoObj.init();
		if (callback !== null) {
			InfoObj.setSyncMapExtent(callback);
		}
		if (BtnId !== null) {
			initSeparatedBtn(BtnId, InfoObj, dropRight);
		}
	}

	function initSeparatedBtn(BtnId, InfoObj, dropRight) {
		var sBtn = new SeparatedBtn(BtnId);
		sBtn.bindingMapID(InfoObj.getID());
		sBtn.bindingLayerArray(InfoObj.getLayerArray());
		if (dropRight) {
			sBtn.setIsMenuDropRight(dropRight);
		}
		sBtn.init();
	}
}



/**
 * 當地圖載入完畢後，後續執行其他元件的初始化
 */
function mapReady() {
	require([
		"esri/layers/LayerDrawingOptions",
		"esri/renderers/UniqueValueRenderer",
		"esri/tasks/IdentifyTask",
		"esri/tasks/IdentifyParameters",
		"esri/layers/ArcGISDynamicMapServiceLayer",
		"esri/layers/GraphicsLayer",
		"esri/symbols/SimpleMarkerSymbol",
		"esri/renderers/SimpleRenderer",
		"esri/lang",
		"dijit/TooltipDialog",
		"dojo/domReady!"
	], function (
		LayerDrawingOptions, UniqueValueRenderer, IdentifyTask, IdentifyParameters, ArcGISDynamicMapServiceLayer,GraphicsLayer,SimpleMarkerSymbol,SimpleRenderer,esriLang,TooltipDialog
	) {
		// 設定工具列顯示的項目
		//ezek_setToolBoxParameter("toolBoxSort", [0, 3, 4, 5, 9, 10, 11, 12, 13, 14, 15]);
		ezek_setToolBoxParameter("toolBoxSort", [0, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]);
		// 初始化工具列
		initEzekArcgisToolBox("mapDiv");
		
		// 1090206 加載 其他 工具
		addOtherTool("mapDiv");

		// 初始化版面的配置
		mapSliderMoveRight();
		ezek_ToolBoxMoveRight();
		$(".esriSimpleSliderIncrementButton").attr("title", "放大");
		$(".esriSimpleSliderDecrementButton").attr("title", "縮小");


	
		ezekMapInfo.DMLLayer = new GraphicsLayer();
		ezekMap.addLayer(ezekMapInfo.DMLLayer);
		addGraphicHover();

		// 初始化渲染樣式 － 根據不同的欄位數值顯示相對應的圖標
		// var layerDrawingOptions = [];
		// var layerDrawingOption = new LayerDrawingOptions();
		var renderer = new UniqueValueRenderer(null, "status", "dis_sort", null, ":");
		renderer.addValue("1:A", MarkerInfo.Marker_1_A);
		renderer.addValue("1:B", MarkerInfo.Marker_1_B);
		renderer.addValue("1:C", MarkerInfo.Marker_1_C);
		renderer.addValue("1:Z", MarkerInfo.Marker_1_Z);
		renderer.addValue("1:", MarkerInfo.Marker_1_NA);
		renderer.addValue("2:A", MarkerInfo.Marker_2_A);
		renderer.addValue("2:B", MarkerInfo.Marker_2_B);
		renderer.addValue("2:C", MarkerInfo.Marker_2_C);
		renderer.addValue("2:", MarkerInfo.Marker_2_NA);
		renderer.addValue("3:A", MarkerInfo.Marker_3_A);
		renderer.addValue("3:B", MarkerInfo.Marker_3_B);
		renderer.addValue("3:C", MarkerInfo.Marker_3_C);
		renderer.addValue("3:", MarkerInfo.Marker_3_NA);
		renderer.addValue("4:A", MarkerInfo.Marker_4_A);
		renderer.addValue("4:B", MarkerInfo.Marker_4_B);
		renderer.addValue("4:C", MarkerInfo.Marker_4_C);
		renderer.addValue("4:", MarkerInfo.Marker_4_NA);
	
		

		//var textLayerDefinitions = [];
		//Mapsefrver的連結，例如：https://IP_Adress/arcgis/rest/services/xxxxxxxx/MapServer
		//var textMapServerURL = location.protocol+"//building.tycg.gov.tw/arcgis/rest/services/oba_tycgisMap/MapServer";
		//var textDynamicLayer = new ArcGISDynamicMapServiceLayer(textMapServerURL);
		//因為一個MapServer可能會有多個圖層，所以這裡設定哪個是要被顯示的
		//textDynamicLayer.setVisibleLayers([2]);
		//此為設定圖層的where條件，跟VisibleLayers一樣需要指定，所以以陣列指定哪個圖層的WHERE條件
		// textLayerDefinitions[0] = "reg_yy='108'";
		// textLayerDefinitions[0] = "reg_yy='" + $('#BDSYear').val() + "'";
		// textLayerDefinitions[0] = " 1 <> 1 ";
		// textLayerDefinitions[0] = " 1 = 1 ";
		//textDynamicLayer.setLayerDefinitions(textLayerDefinitions);
		
		//設定好了之後加入Layer即可 2023/9/7 無介接桃園建管ARCGIS
		//ezekMap.addLayer(textDynamicLayer);
		// ezekMapInfo.TextLayer = textDynamicLayer;

		//in the init function 
		// dojo.connect(ezekMap, "onZoomStart", function (extent, zoomFactor, anchor, level) {
		// 	ezekMapInfo.DMLLayer.setVisibility(false);
		// });

		// dojo.connect(ezekMap, "onZoomEnd", function (extent, zoomFactor, anchor, level) {
		// 	ezekMapInfo.DMLLayer.setVisibility(true); 
		// });

		// 綁定點擊地圖事件，點擊後會去Query違章案件圖層的案件資料	
		ezekMap.on("click", executeIdentifyTask);
		//create identify tasks and setup parameters
		identifyTask = new IdentifyTask( location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/dis_pointMap/MapServer");
		identifyParams = new IdentifyParameters();
		identifyParams.tolerance = 10; //點擊處 方圓5px範圍
		identifyParams.returnGeometry = true;
		identifyParams.layerIds = [0]; //只針對第一個圖層做Query
		identifyParams.layerDefinitions = [];
		//identifyParams.layerDefinitions[0] = "reg_yy = '" + $('#BDSYear').val() + "'";
		identifyParams.layerOption = IdentifyParameters.LAYER_OPTION_ALL;
		identifyParams.width = ezekMap.width;
		identifyParams.height = ezekMap.height;
		ezekMapInfo.identifyParams = identifyParams;


		
		
	});
}

//NOTE:------------------------------------------------------------------------
//NOTE: 新增案件基本資料 Hover
//NOTE:------------------------------------------------------------------------
function addGraphicHover() 
{
	require([
		"esri/lang",
		"dojo/dom-style",
		"dijit/TooltipDialog",
		"dijit/popup",
		"dojo/domReady!"
    ], function(
        esriLang,domStyle,
        TooltipDialog, dijitPopup
    ) {
		var dialog = new TooltipDialog({
			id: "tooltipDialog",
			style: "position: absolute; width: 200px; z-index:100; margin-left:5px; margin-top:5px"
		});
		dialog.startup();

		ezekMapInfo.DMLLayer.enableMouseEvents();
		ezekMapInfo.DMLLayer.on("mouse-out", closeDialog);
		ezekMapInfo.DMLLayer.on("mouse-over", function (evt) {
			var attributes = evt.graphic.attributes;
			if(attributes.reg_yy !== undefined || attributes.reg_no !== undefined || attributes.reg_cg !== undefined)
			{
				var infostr = "";
				var statusArray = ["","認定中","已認定", "已排拆", "已結案", "移送法辦","拍照列管"];
				infostr += "<table id='layerInfoWindow'>";
				infostr += "<tr><th colspan='2' style='text-align:center;'>違章案件基本資料";
				infostr += "</th></tr>";
				infostr += "<tr><td class='infoWindow-td'>案件編號</th><td >" + attributes.reg_yy + "-" + attributes.reg_no + "</td></tr>";
				infostr += "<tr><td class='infoWindow-td'>案件狀態</th><td >" + statusArray[attributes.status.substring(1)] + "</td></tr>";
				infostr += "</table>";
				var content = esriLang.substitute(evt.graphic.attributes, infostr);
				dialog.setContent(content);
				domStyle.set(dialog.domNode, "opacity", 1);
				dijitPopup.open({
					popup: dialog,
					x: evt.pageX+5,
					y: evt.pageY+5
				});
			}
		});

		function closeDialog() {
			dijitPopup.close(dialog);
		}
	});
}

/**
 * 進行地籍套繪圖查詢，查詢該地理位置有無建造執照資料 , 108.10.28
 * @param {event} event
 */
function BcmsMap_I40IdentifyTask(event) 
{	
	return new Promise(function(callback){
		require([
			"esri/tasks/IdentifyTask",
			"esri/tasks/IdentifyParameters",
			"dojo/domReady!"
		], function (
			IdentifyTask, IdentifyParameters
		) {
			//create identify tasks and setup parameters
			var cpamigiParams = new IdentifyParameters();
			cpamigiParams.tolerance = 5; //點擊處 方圓5px範圍
			cpamigiParams.returnGeometry = false;
			cpamigiParams.layerIds = [10]; //只針對第一個圖層做Query
			cpamigiParams.layerDefinitions = [];
			cpamigiParams.layerOption = IdentifyParameters.LAYER_OPTION_ALL;
			cpamigiParams.width = ezekMap.width;
			cpamigiParams.height = ezekMap.height;
			// 點擊事件的位置及範圍
			cpamigiParams.geometry = event.mapPoint;
			cpamigiParams.mapExtent = ezekMap.extent;
			var cpamigiTask = new IdentifyTask("https://limit.ntpc.gov.tw/arcgis/rest/services/bcmsMap_I30/MapServer");
	
			// 執行identifyTask去Query案件資料
			cpamigiTask.execute(cpamigiParams).addCallback(function (response) 
			{
				if(response.length > 0)
				{
					var rltAttr = response[0].feature.attributes;
					var BMPAS = "I30";
					ezekMapInfo.cpamigiIndexKey = rltAttr.index_key + BMPAS;
				}else{
					ezekMapInfo.cpamigiIndexKey = "";
				}
				callback();
			});
		});
	});

	
	
}

function executeIdentifyTask(event) 
{
	if (ezekMapInfo.isEnableClick && !ezek_getIsToolUsing()) 
	{
		// Loading 畫面
		showLoading('讀取中');
		// 進行地籍套繪圖查詢，查詢該地理位置有無建造執照
		BcmsMap_I40IdentifyTask(event).then(function() {
			// 進行案件資訊查詢，並建立InfoWindow
			oba_tycgisMapIdentifyTask(event);
		});
	}
}


/**
 * 點擊地圖事件，去Query違章案件圖層的案件資料，並產出InfoWindow
 * @param {event} event
 */
function oba_tycgisMapIdentifyTask(event) 
{
	require([
		"dojo/_base/array",
		"esri/geometry/webMercatorUtils",
		'dojo/dnd/Moveable',
		"esri/geometry/Point",
		"esri/symbols/PictureMarkerSymbol",
		'dojo/query',
		'dojo/on',
		'dojo/dom-class',
		"esri/tasks/IdentifyTask",
		"esri/tasks/IdentifyParameters",
		"dojo/promise/all",
		"dojo/domReady!"
	], function (
		arrayUtils, WebMercatorUtils, Moveable, Point, PictureMarkerSymbol, query, on, domClass, IdentifyTask, IdentifyParameters, all) {
		// 點擊事件的位置及範圍
		identifyParams.geometry = event.mapPoint;
		identifyParams.mapExtent = ezekMap.extent;

		// 執行identifyTask去Query案件資料
		var deferred = identifyTask  //之後要改回identifyTask
			.execute(identifyParams)//之後要改回identifyParams
			.addCallback(function (response) {
				console.log("response = ",response);
				// 清除前次查詢資料
				ezekMapInfo.infoWindowArray = [];
				ezekMapInfo.featureArray = [];

				// 初始化所需變數
				var graphic = null;
				var wgs84Coord = WebMercatorUtils.xyToLngLat(event.mapPoint.x, event.mapPoint.y);
				var lng = wgs84Coord[0].toFixed(7); // 經度
				var lat = wgs84Coord[1].toFixed(6); // 緯度
				var resLength = 0;
				ezekMapInfo.queryResponse = response; // 預存
				var point = new Point(lng, lat);

				//NOTE: 初始化定位標記icon
				picLocMarker = new PictureMarkerSymbol('img/valveTrace_target.png', 20, 20);
				ezekMapInfo.DMLLayer.add(new esri.Graphic(point, picLocMarker));

				// 計算符合的案件數 , 建立infoWindow時會有index的按鈕需要
				arrayUtils.map(response, function (result, index) 
				{
					var attributes = result.feature.attributes;
					if(ezekMapSearchInfo.resultBmsKey.indexOf(attributes.case_id) !== -1)
					{
						resLength++;
					}
				});

				// 走訪回傳的全部案件資料
				arrayUtils.map(response, function (result, index) 
				{
					console.log("result = ",result);
					var attributes = result.feature.attributes;
					if(ezekMapSearchInfo.resultBmsKey.indexOf(attributes.case_id) !== -1)
					{
						if (resLength > 6) resLength = 6;
						if (ezekMapInfo.infoWindowArray.length < 7) {
							// 取得內容
							var pageIndex = index;
							graphic = result.feature;
							ezekMapInfo.featureArray.push(graphic);
							// 以case_id欄位去取得IBMCASE內的案件資料
							var _bdsdata = getJsonDataWithREG(attributes.case_id);
							// 以REG欄位去取得圖片列表
							var _imgListdata = getCaseImgListWithREG(attributes.case_id);
							// 以案件資料組建InfoWindow的HTML字串);
							var _infostr = createInfoWindowStr(lng, lat, attributes, _bdsdata, _imgListdata, resLength, pageIndex);
							// 將產製好的infoWindow先暫存起來
							if (_infostr.length > 0) {
								ezekMapInfo.infoWindowArray.push(_infostr);
							}
						}
					}
					
					if (response.length - 1 === index) //查詢完畢
					{
						// 顯示InfoWindow 
						if (graphic !== null) {
							// 設定infoWindow內容及顯示
							ezekMap.infoWindow.setTitle("違章建築案件資訊");
							ezekMap.infoWindow.setContent(ezekMapInfo.infoWindowArray[0]);
							ezekMap.infoWindow.show(event.mapPoint);
							ezekMapInfo.DMLLayer.add(graphic);
						}
					}
				});
				
				//如果不是違章案件只顯示多目標連結
				if(response.length == 0){
					
					var infostr = "<tr><th colspan='2' style='text-align:left; padding-left: 8px;'>查無違章案件資料";
					
					if(ezekMapInfo.cpamigiIndexKey.length > 0){
						var year = ezekMapInfo.cpamigiIndexKey.substring(0, 3);
						var kind = ezekMapInfo.cpamigiIndexKey.substring(3, 4);
						var no1 = ezekMapInfo.cpamigiIndexKey.substring(4, 9);
						var user_id = $("#NewRecord1USER_ID").val();
						if(user_id == "bb"){
							user_id = "HCGBM";
						}
						
						//var url = 'https://building-apply.publicwork.ntpc.gov.tw/cpamigi/code/avueCode_I30.jsp?yy=' + year + '&kind=' + kind + '&no1=' + no1 + '&empno=' + user_id + '&iid=' + user_id;
						var url = 'https://building-apply.publicwork.ntpc.gov.tw/ci/login/select/CIH00';
						
						if (ezekMapInfo.cpamigiIndexKey != ""){
							infostr += '<a href="javascript:void(0)" style="float: right; cursor: pointer" onclick="javascript:window.open(\'' + url + '\')">檢視 ' + ezekMapInfo.cpamigiIndexKey + ' 建築物相關資訊</a>';
						}
					}
					infostr += "</th></tr>";
					
					// 將產製好的infoWindow先暫存起來
					if (infostr.length > 0) {
						ezekMapInfo.infoWindowArray.push(infostr);
					}
					
					// 設定infoWindow內容及顯示
					ezekMap.infoWindow.setTitle("違章建案件資訊");
					ezekMap.infoWindow.setContent(ezekMapInfo.infoWindowArray[0]);
					ezekMap.infoWindow.show(event.mapPoint);
				}
				closeLoading();
			});

		// InfoWindow拖曳
		var handle = query(".title", ezekMap.infoWindow.domNode)[0];
		var dnd = new Moveable(ezekMap.infoWindow.domNode, {
			handle: handle
		});
		// when the infoWindow is moved, hide the arrow:
		on(dnd, 'FirstMove', function () {
			// hide pointer and outerpointer (used depending on where the pointer is shown)
			var arrowNode = query(".outerPointer", ezekMap.infoWindow.domNode)[0];
			domClass.add(arrowNode, "hidden");
			var arrowNode = query(".pointer", ezekMap.infoWindow.domNode)[0];
			domClass.add(arrowNode, "hidden");
		}.bind(this));
	});
}

/**
 * 根據掛號號碼搜尋案件資料
 * @param {string} regYY
 * @param {string} regNO
 * @param {string} regCG
 * @returns {Object} 案件資料
 */
function getJsonDataWithREG(caseID) {
	var _response = null;
	var s_data = $.param({
		"s_caseID": caseID
	});

	$.ajax({
		url: 'getBDSJson.jsp',
		type: 'POST',
		cache: false,
		data: s_data,
		async: false,
		dataType: 'json',
		error: function (xhr) {
			console.log('Ajax request 發生錯誤' + xhr.responseText);
		},
		success: function (response) {
			_response = response;
		},
		complete: function (data) {}
	});
	return _response;
}

// https://building.tycg.gov.tw/tycgim/tcy_getCaseImgList.jsp?REG_YY=105&REG_NO=0031247&REG_CG=00
/**
 * 根據掛號號碼搜尋案件圖片列表
 * @param {string} regYY
 * @param {string} regNO
 * @param {string} regCG
 * @returns {Object} 圖片清單
 */
function getCaseImgListWithREG(caseID) {
	var _response = null;
	var s_data = $.param({
		"CASE_ID": caseID,
	});

	$.ajax({
		url: 'tcy_getCaseImgList.jsp',
		type: 'POST',
		cache: false,
		data: s_data,
		async: false,
		dataType: 'json',
		error: function (xhr) {
			console.log('Ajax request 發生錯誤' + xhr.responseText);
		},
		success: function (response) {
			// console.log(response);
			_response = response;
		},
		complete: function (data) {}
	});
	return _response;
}

//------------------------------------------------------------------------
//NOTE:  組建InfoWindow的HTML字串
//------------------------------------------------------------------------

/**
 * 組建InfoWindow的HTML字串
 * @param {*} lng
 * @param {*} lat
 * @param {*} attributes
 * @param {*} bdsdata
 * @param {*} imgListdata
 * @param {*} pagelength
 * @returns {string} InfoWindow HTML文字
 */
function createInfoWindowStr(lng, lat, attributes, bdsdata, imgListdata, pagelength, pageIndex) 
{
	var infostr = "";
	var pageIconsValue = ['looks_one', 'looks_two', 'looks_3', 'looks_4', 'looks_5', 'looks_6'];
	var statusArray = ["","","已認定", "已排拆", "已結案", "移送法辦","拍照列管"];
	//var dis_sortStr = (attributes.dis_sort === 'Z') ? "拍照列管" : attributes.dis_sort + "類";
	infostr += "<div>";
	for (var _index = 0; _index < pagelength; _index++) {
		if (_index === pageIndex) {
			infostr += "<i class='material-icons locationFeature highlight' title='切換案件基本資料' value='" + _index + "' onclick='changeInfoWindowContent(this)' >" + pageIconsValue[_index] + "</i>";
		} else {
			infostr += "<i class='material-icons locationFeature normal' title='切換案件基本資料' value='" + _index + "' onclick='changeInfoWindowContent(this)' >" + pageIconsValue[_index] + "</i>";
		}

	}
	infostr += "</div>";
	//infostr += "<div style='margin-bottom: 5px;'><span>查詢位置：" + lng + "E, " + lat + "N" + "</span></div>";
	infostr += "<table id='layerInfoWindow'>";
	infostr += "<tr><th colspan='2' style='text-align:left; padding-left: 8px;'>基本資料";
	infostr += "<a class='fancybox fancybox.iframe' href='./is10102_lis.jsp?s_CASE_ID=" + attributes.case_id + "'>";
	infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
	infostr += "title='播放影片' value='1'>play_circle_filled</i>";
	infostr += "</a>";
	infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
	infostr += "title='觀看照片' onclick='showPhotoList(this)'>photo</i>";
		
	if(attributes.status == "02" || attributes.status == "03" || attributes.status == "04"){
		
		infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
		infostr += "title='查看認定通知單' onclick='showDistCaseReport(this)' reg_yy='" + attributes.reg_yy + "' reg_no='" + attributes.reg_no + "' case_id='" + attributes.case_id + "' type='type1'>assignment</i>";
		
		if(attributes.status == "03" || attributes.status == "04"){
			infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
			infostr += "title='查看排拆時間通知單' onclick='showDistCaseReport(this)' case_id='" + attributes.case_id + "' type='type2'>pending_actions</i>";
		}
		
		if(attributes.status == "04"){
			infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
			infostr += "title='查看結案通知單' onclick='showDistCaseReport(this)' case_id='" + attributes.case_id + "' type='type3'>description</i>";
		}
	}
	
	if(ezekMapInfo.cpamigiIndexKey.length > 0){
		//infostr += "<i class='material-icons locationFeature' style='color:#FFFFFF; float: right;'";
		//infostr += "title='觀看平面圖說' onclick='javascript:window.open(\"https://building.tycg.gov.tw/cpamigi/code/bdmListAction.do?key="+ezekMapInfo.cpamigiIndexKey+"&empno=c1070921\")'>home</i>";
		var year = ezekMapInfo.cpamigiIndexKey.substring(0, 3);
		var kind = ezekMapInfo.cpamigiIndexKey.substring(3, 4);
		var no1 = ezekMapInfo.cpamigiIndexKey.substring(4, 9);
		var user_id = $("#NewRecord1USER_ID").val();
		if(user_id == "bb"){
			user_id = "HCGBM";
		}
		
		//var url = 'https://building-apply.publicwork.ntpc.gov.tw/cpamigi/code/avueCode_I30.jsp?yy=' + year + '&kind=' + kind + '&no1=' + no1 + '&empno=' + user_id + '&iid=' + user_id;
		var url = "https://building-apply.publicwork.ntpc.gov.tw/ci/login/select/CIH00";
		if (ezekMapInfo.cpamigiIndexKey != ""){
			infostr += '<a href="javascript:void(0)" style="float: right; cursor: pointer" onclick="javascript:window.open(\'' + url + '\')">檢視 ' + ezekMapInfo.cpamigiIndexKey + ' 建築物相關資訊</a>';
		}
	}
	infostr += "</th></tr>";
	
	if(imgListdata.haveNowPic == "Y"){
		infostr += "<tr><td colspan='2'><img width='220' style='border: 1px solid #666666;' src='in10101_getImage.jsp?EXP_NO=" + attributes.case_id + "&Zzip=Y&Img_kind=NOW&Img_index=1'></td></tr>";
	}
	infostr += "<tr><td class='infoWindow-td'>認定號碼</th><td >" + attributes.reg_yy + attributes.reg_no + "</td></tr>";
	infostr += "<tr><td class='infoWindow-td'>案件狀態</th><td >" + statusArray[attributes.status.substring(1)] + "</td></tr>";
	//infostr += "<tr><td class='infoWindow-td'>案件類別</th><td >" + dis_sortStr + "</td></tr>";

	// 照片清單
	infostr += "<tr><div id='imageList' style='display:none;'>";
	// infostr += "<tr><div id='imageList' >";
	$.each(imgListdata.ImgList, function (index, value) {
		var PICNAME = value.PICNAME;
		var PICURL = value.PICURL;
		if (index === 0) {
			infostr += "<img id='triggerClickImg' data-original='" + PICURL + "' alt='" + PICNAME + "'>";
		} else {
			infostr += "<img data-original='" + PICURL + "' alt='" + PICNAME + "'>";
		}
	});
	infostr += "</div></tr>";

	// 案件基本資料列表
	$.each(bdsdata.BDSInfo, function (index, value) {
		var itemKey_ch = value.itemKey_ch;
		var itemValue = value.itemValue;
		if (itemValue !== "") {
			infostr += "<tr><td class='infoWindow-td'>" + itemKey_ch + "</th><td >" + itemValue + "</td></tr>";
		}
	});

	// 拆除記錄列表
	/*$.each(bdsdata.DMLInfo, function (index, value) {
		var DMLInfoData = value;
		infostr += "<tr><th colspan='2' style='text-align:center; padding-left: 20px;'>第" + (index + 1) + "次拆除記錄</th></tr>";
		$.each(DMLInfoData, function (detail_index, detail_value) {
			var itemKey_ch = detail_value.itemKey_ch;
			var itemValue = detail_value.itemValue;
			if (itemValue !== "") {
				infostr += "<tr><td class='infoWindow-td'>" + itemKey_ch + "</th><td >" + itemValue + "</td></tr>";
			}
		});
	});*/
	infostr += "</table>";
	
	return infostr;
}

//------------------------------------------------------------------------
//NOTE:  show
//------------------------------------------------------------------------
function showUAVLayer(element)
{
	var layerDefinitions = [];
	if(  ezekMapInfo.UavLayerStat == "N"){
		ezekMapInfo.UavLayerStat = "Y";
		layerDefinitions[0] = " Name = '"+$(element).attr('ORTHO_NAME')+"'";
	}else{
		ezekMapInfo.UavLayerStat = "N";
		layerDefinitions[0] = " 1 <> 1 ";
	}
	
	ezekMapInfo.UavLayer.setLayerDefinitions(layerDefinitions);
}

//------------------------------------------------------------------------
//NOTE:  切換InfoWindow內容
//------------------------------------------------------------------------
function changeInfoWindowContent(btn) {
	var index = parseInt($(btn).attr('value'));
	ezekMap.infoWindow.setContent(ezekMapInfo.infoWindowArray[index]);
	var viewer = new Viewer(document.getElementById('imageList'));
	addHighlightMarker(ezekMapInfo.featureArray[index], index);
}

//------------------------------------------------------------------------
//NOTE:  顯示照片列表
//------------------------------------------------------------------------
function showPhotoList(btn) {
	if ($('#triggerClickImg').length > 0) { //物件存在代表有照片 
		// 初始化圖片列表
		var viewer = new Viewer(document.getElementById('imageList'),
		{
			url: 'data-original'
		});
		// 顯示照片列表
		$("#triggerClickImg").trigger("click");
	} else {
		// 顯示警示訊息
		swal({
			title: "此案件尚無現場照片!",
			icon: "warning",
			button: "知道了",
		});
	}

}

//------------------------------------------------------------------------
//NOTE:  顯示查報單
//------------------------------------------------------------------------
function showDistCaseReport(btn) {
	showLoading('查詢中');
	
	var case_id = $(btn).attr('case_id');
	var type = $(btn).attr('type');
	
	if( type == "type1" ){
		window.open('im10101_prt.jsp?case_id=' + case_id + '&out_ext=PDF&data_need_type=ALL','_self'); 
	}
	else if( type == "type2" ){
		window.open('im40101_prt.jsp?case_id=' + case_id + '&out_ext=PDF','_self'); 
	}
	else if( type == "type3" ){
		window.open('im40201_prt.jsp?case_id=' + case_id + '&out_ext=PDF&out_null=N','_self'); 
	}
	
	closeLoading();
}

//------------------------------------------------------------------------
//NOTE:  切換InfoWindow內容
//------------------------------------------------------------------------
function addHighlightMarker(graphic, index) {
	require([
		"esri/symbols/PictureMarkerSymbol",
		"esri/geometry/Point"
	], function (PictureMarkerSymbol, Point) {

		var attributes = ezekMapInfo.featureArray[index].attributes;
		var dis_sort = attributes.dis_sort;
		var status = attributes.status;
		var geometry = graphic.geometry;

		graphic.setSymbol(MarkerInfo['Marker_' + status + '_' + dis_sort]);
		//ezekMapInfo.DMLLayer.clear();
		//ezekMapInfo.DMLLayer.add(graphic);
		ezekMap.infoWindow.anchor = "right";
		ezekMap.infoWindow.show(geometry);
	});
}
//https://maps.tycg.gov.tw/tokens/building_tycg_gov_tw_tycgis.js
//------------------------------------------------------------------------
// 初始化圖層
//------------------------------------------------------------------------
function initPictureMarker() {
	require([
		"esri/symbols/PictureMarkerSymbol",
	], function (PictureMarkerSymbol) {
		// 1 已結案 , 2 處理中 , 3 查報中
		MarkerInfo.Marker_1 = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Green.png', 26, 26);
		MarkerInfo.Marker_1_NA = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Green_NA.png', 26, 26);
		MarkerInfo.Marker_1_A = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Green_A.png', 26, 26);
		MarkerInfo.Marker_1_B = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Green_B.png', 26, 26);
		MarkerInfo.Marker_1_C = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Green_C.png', 26, 26);
		MarkerInfo.Marker_1_Z = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Blue_Z.png', 26, 26);
		MarkerInfo.Marker_2 = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Yellow.png', 26, 26);
		MarkerInfo.Marker_2_NA = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Yellow_NA.png', 26, 26);
		MarkerInfo.Marker_2_A = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Yellow_A.png', 26, 26);
		MarkerInfo.Marker_2_B = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Yellow_B.png', 26, 26);
		MarkerInfo.Marker_2_C = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Yellow_C.png', 26, 26);
		MarkerInfo.Marker_3 = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Red.png', 26, 26);
		MarkerInfo.Marker_3_NA = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Red_NA.png', 26, 26);
		MarkerInfo.Marker_3_A = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Red_A.png', 26, 26);
		MarkerInfo.Marker_3_B = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Red_B.png', 26, 26);
		MarkerInfo.Marker_3_C = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Red_C.png', 26, 26);
		MarkerInfo.Marker_4 = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Purple.png', 26, 26);
		MarkerInfo.Marker_4_NA = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Purple_NA.png', 26, 26);
		MarkerInfo.Marker_4_A = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Purple_A.png', 26, 26);
		MarkerInfo.Marker_4_B = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Purple_B.png', 26, 26);
		MarkerInfo.Marker_4_C = new PictureMarkerSymbol(location.protocol+'//building.tycg.gov.tw/tycgim/img/Marker/Purple_C.png', 26, 26);
	});
}

//塗層控制建置
function initAllLayer() {
	
	//建築管理類
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayerWithUrl('https://limit.ntpc.gov.tw/arcgis/rest/services/bcmsMap_I30/MapServer', 'BcmsMap_I30', 0.5, [1,2,3,4,5,6,7,8,9,10,11,12,13]), '地籍套繪圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '建物套繪圖', 0.5, [20]), '建物套繪圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Social', '高風險具安全疑慮建築物', 0.5, [2]), '高風險具安全疑慮建築物'));
	ezekMapInfo.LayerArray.push(createLayerObj(createTileLayer('anno', 'anno', 0.5), '道路註記'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '工業區分布圖', 0.5, [16]), '工業區分布圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '都市計畫範圍', 0.5, [18]), '都市計畫範圍'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '路網數值圖', 0.5, [0,1]), '路網數值圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '國道快速道路', 0.5, [2,3]), '國道快速道路'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '道路', 0.5, [4,5]), '道路'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '鐵路', 0.5, [6,7]), '鐵路'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '高速鐵路', 0.5, [8,9]), '高速鐵路'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '捷運', 0.5, [10,11]), '捷運'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '現有巷道認定', 0.5, [14]), '現有巷道認定'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Traffic', '自然步道', 0.5, [15]), '自然步道'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '一般電信系統管線', 0.5, [0]), '一般電信系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '有線電視系統管線', 0.5, [1]), '有線電視系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '一般供電系統管線', 0.5, [2]), '一般供電系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '給水系統管線', 0.5, [3]), '給水系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '供氣系統管線', 0.5, [4]), '供氣系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '輸油系統管線', 0.5, [5]), '輸油系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '寬頻系統管線', 0.5, [15]), '寬頻系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '交通號誌電信管線', 0.5, [17]), '交通號誌電信管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '共同管道系統管線', 0.5, [19]), '共同管道系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '污水系統孔蓋', 0.5, [21]), '污水系統孔蓋'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '污水系統管線', 0.5, [22]), '污水系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '雨水系統孔蓋', 0.5, [23]), '雨水系統孔蓋'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '雨水系統管線', 0.5, [24]), '雨水系統管線'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '河川', 0.5, [4,5]), '河川'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '土石流潛勢溪流', 0.5, [10]), '土石流潛勢溪流'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '土石流潛勢溪流影響範圍', 0.5, [11]), '土石流潛勢溪流影響範圍'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '自來水水質水量保護區圖', 0.5, [27]), '自來水水質水量保護區圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '國有林區林地分區圖', 0.5, [30,31]), '國有林區林地分區圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '保安林分布概略圖', 0.5, [33,34]), '保安林分布概略圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '林道分布圖', 0.5, [35,36]), '林道分布圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '海岸-近岸海域風浪危害潛勢', 0.5, [51]), '海岸-近岸海域風浪危害潛勢'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '海岸-海岸侵蝕防護區', 0.5, [52]), '海岸-海岸侵蝕防護區'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '海嘯-溢淹水深潛勢', 0.5, [53]), '海嘯-溢淹水深潛勢'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '坡地-大規模崩塌災害潛勢區', 0.5, [54]), '坡地-大規模崩塌災害潛勢區'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '地震-土壤液化潛勢', 0.5, [55]), '地震-土壤液化潛勢'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '地震-山腳斷層', 0.5, [56]), '地震-山腳斷層'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '地震-山腳斷層300m寬影響範圍', 0.5, [57]), '地震-山腳斷層300m寬影響範圍'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '新北市水庫集水區', 0.5, [58]), '新北市水庫集水區'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Environment', '陽明山國家公園範圍', 0.5, [59]), '陽明山國家公園範圍'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '山坡地開發圖', 0.5, [0]), '山坡地開發圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '山坡地範圍圖', 0.5, [17]), '山坡地範圍圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createTileLayer('Land', 'land', 0.5), '地籍圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '重要地標', 0.5, [2]), '重要地標'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '地址門牌', 0.5, [3]), '地址門牌'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '村里界', 0.5, [4,5]), '村里界'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '縣市界', 0.5, [6,7]), '縣市界'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '鄉鎮市區界', 0.5, [8,9]), '鄉鎮市區界'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '街廓', 0.5, [10,11]), '街廓'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '學校', 0.5, [12,13]), '學校'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '公園', 0.5, [14,15]), '公園'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '村里界國土測繪中心', 0.5, [26]), '村里界國土測繪中心'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '消防栓', 0.5, [12]), '消防栓'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('PubPipe', '路燈', 0.5, [13]), '路燈'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Social', '新北市里辦公處資訊', 0.5, [4]), '新北市里辦公處資訊'));
	ezekMapInfo.LayerArray.push(createLayerObj(createTileLayer('topoGraphic', 'topoGraphic', 0.5), '107年(2018)千分之一數值航測地形圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createTileLayer('Satellite', 'Satellite', 0.5), '105年(2016)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2013', 'NTPCWV2_2013', 0.5, [0]), '102年(2013)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2011', 'NTPCWV2_2011', 0.5, [0]), '100年(2011)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2010', 'Formasat2_2010', 0.5, [0]), '99年(2010)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('3857/QuickBird_2009', 'QuickBird_2009', 0.5, [0]), '98年(2009)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createTileLayer('orthophoto', 'orthophoto', 0.5), '97年(2008)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2006', 'Formasat2_2006', 0.5, [0]), '95年(2006)衛星影像圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(tiledMap, '新北市政府電子地圖(比例尺1/500)'));
	ezekMapInfo.LayerArray.push(createLayerObj(TgosLayer_F2, '新北市政府電子地圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Landinfo', '工廠登記', 0.5, [24,25]), '工廠登記'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayer('Social', '防災公園', 0.5, [3]), '防災公園'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_111/MapServer', 'uav_ntpcinMap_111', 0.5, [0]), '111年新北違章空拍正射影圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_110/MapServer', 'uav_ntpcinMap_110', 0.5, [0]), '110年新北違章空拍正射影圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_109/MapServer', 'uav_ntpcinMap_109', 0.5, [0]), '109年新北違章空拍正射影圖'));
	ezekMapInfo.LayerArray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/aerialPhoto/MapServer', 'aerialPhoto', 0.5, [0]), '98年(2009)航拍影像圖'));
}

function createLayerObj(layer, layerName) {
	var layerObject = {
		layer: layer,
		layerName: layerName
	}
	return layerObject;
}

function createTileLayer(ServiceName, ID, opacity) {
	var mapUrl = "https://gis1.ntpc.gov.tw/gis/rest/services/" + ServiceName + "/MapServer?token=" +token;
	var tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapUrl, {
		id: ID
	});
	tiledMap.setOpacity(opacity);
	return tiledMap;
}
function createTileLayerHttps(ServiceName, ID, opacity) {
	
	var mapUrl = "https://maps.tycg.gov.tw/gis/rest/services/" + ServiceName + "/MapServer?token=" + getGtoken();
	var tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapUrl, {
		id: ID
	});
	tiledMap.setOpacity(opacity);
	return tiledMap;
}
function createDynamicLayer(ServiceName, ID, opacity, visibleLayers) {
	var mapUrl2 = "https://gis1.ntpc.gov.tw/gis/rest/services/" + ServiceName + "/MapServer?token=" + token;
	var dynamicMapServiceLayer = new esri.layers.ArcGISDynamicMapServiceLayer(mapUrl2, {
		id: ID
	});
	dynamicMapServiceLayer.setOpacity(opacity);
	if (visibleLayers !== null) {
		dynamicMapServiceLayer.setVisibleLayers(visibleLayers);
	}
	return dynamicMapServiceLayer;
}


function createDynamicLayerWithUrl(url, ID, opacity, visibleLayers) {
	var dynamicMapServiceLayer = new esri.layers.ArcGISDynamicMapServiceLayer(url, {
		id: ID
	});
	dynamicMapServiceLayer.setOpacity(opacity);
	if (visibleLayers !== null) {
		dynamicMapServiceLayer.setVisibleLayers(visibleLayers);
	}
	return dynamicMapServiceLayer;
}

function getLayerArray() {
	return ezekMapInfo.LayerArray;
}

function getGtoken() {
	return gtoken;
}

function showLayer(index) {
	var layerArray = getLayerArray();
	layerArray[index].layer.show();
}

function hideLayer(index) {
	var layerArray = getLayerArray();
	layerArray[index].layer.hide();
}

function showDMLLayer() {
	ezekMapInfo.DMLLayer.show();
}

function hideDMLLayer() {
	ezekMapInfo.DMLLayer.hide();
}

function setLayerOpacity(index, value) {
	var layerArray = getLayerArray();
	layerArray[index].layer.setOpacity(value);
}

function addAllLayer() {
	var layerArray = getLayerArray();
	$.each(layerArray.reverse(), function (index, value) {
		var layer = value.layer;
		ezekMap.addLayer(layer);
		layer.hide();
	});
	layerArray.reverse();
}

function mapClear() {
	//ezekMap.graphics.clear();
	ezekClearGraphic();
	ezekMapInfo.DMLLayer.clear();
}


//NOTE:------------------------------------------------------------------------
//NOTE: 建置影像比對的地圖物件
//NOTE:------------------------------------------------------------------------
var ExcisionMap = function (ID) {
	//宣告變數
	this.id = ID;
	this.map = new esri.Map(ID, {
		logo: false,
		extent: new esri.geometry.Extent({
			"xmin": 279839.38409337704,
			"ymin": 2764934.903522712,
			"xmax": 280690.6826709742,
			"ymax": 2765516.326560558,
			"spatialReference": {
				wkid: 102443,
				latestWkid: 3826
			}
		})
	});
	this.layerArray = [];
	this.syncExtentTime = 1000; //ms

	//宣告初始化方法
	this.initLayerArray = function (layerarray) {
		var _id = this.id;
	
		layerarray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2006', 'Formasat2_2006' + _id, 1, [0]), '95年(2006)衛星影像圖'));
		layerarray.push(createLayerObj(createTileLayer('orthophoto', 'orthophoto' + _id, 1), '97年(2008)衛星影像圖'));
		layerarray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/aerialPhoto/MapServer', 'aerialPhoto' + _id, 1, [0]), '98年(2009)航拍影像圖'));
		layerarray.push(createLayerObj(createDynamicLayer('3857/QuickBird_2009', 'QuickBird_2009' + _id, 1, [0]), '98年(2009)衛星影像圖'));
		layerarray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2010', 'Formasat2_2010' + _id, 1, [0]), '99年(2010)衛星影像圖'));
		layerarray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2011', 'NTPCWV2_2011' + _id, 1, [0]), '100年(2011)衛星影像圖'));
		layerarray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2013', 'NTPCWV2_2013' + _id, 1, [0]), '102年(2013)衛星影像圖'));
		layerarray.push(createLayerObj(createTileLayer('Satellite', 'Satellite' + _id, 1), '105年(2016)衛星影像圖'));
		layerarray.push(createLayerObj(createTileLayer('map', 'map' + _id, 1), '新北電子地圖'));
		
	}
	this.addAllLayer = function (map, layerArray) {
		$.each(layerArray.reverse(), function (index, value) {			
			var layer = value.layer;
			map.addLayer(layer);
			if (index !== 0) { //基本底圖不隱藏
				layer.hide();
			}
		});
		layerArray.reverse();
	};
};
ExcisionMap.prototype.getID = function () {
	return this.id;
};
ExcisionMap.prototype.getMap = function () {
	return this.map;
};
ExcisionMap.prototype.getLayerArray = function () {
	return this.layerArray;
};
ExcisionMap.prototype.showLayer = function (index) {
	this.layerArray[index].layer.show();
};
ExcisionMap.prototype.hideLayer = function (index) {
	this.layerArray[index].layer.hide();
};
ExcisionMap.prototype.setSyncMapExtent = function (_syncMapExtent) {
	this.syncMapExtent = _syncMapExtent;
	dojo.connect(this.map, "onMouseDragEnd", this.syncMapExtent);
	dojo.connect(this.map, "onZoomEnd", this.syncMapExtent);
};
//NOTE:------------------------------------------------------------------------
//NOTE: *ExcisionMap的擴展函數
//NOTE: 掩藏ExcisionMap的所有圖層
//NOTE:------------------------------------------------------------------------
ExcisionMap.prototype.hideAllLayer = function () {
	var _layerArray = this.layerArray;
	$.each(_layerArray, function (index, value) {
		if (index !== _layerArray.length - 1) {
			_layerArray[index].layer.hide();
		}
	});
};
//NOTE:------------------------------------------------------------------------
//NOTE: *ExcisionMap的擴展函數
//NOTE: 初始化ExcisionMap的所有圖層
//NOTE:------------------------------------------------------------------------
ExcisionMap.prototype.extendLayer = function () {
	var _id = this.id;
	var _layerArray = this.layerArray;
	
	//這邊id要記得加底線 讓左右兩邊layer id不一樣 ex: Satellite -> _Satellite
	_layerArray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2006', '_Formasat2_2006' + _id, 1, [0]), '95年(2006)衛星影像圖'));
	_layerArray.push(createLayerObj(createTileLayer('orthophoto', '_orthophoto' + _id, 1), '97年(2008)衛星影像圖'));
	_layerArray.push(createLayerObj(createDynamicLayerWithUrl(location.protocol+'//icdc.ntpc.gov.tw/arcgis/rest/services/aerialPhoto/MapServer', '_aerialPhoto' + _id, 1, [0]), '98年(2009)航拍影像圖'));
	_layerArray.push(createLayerObj(createDynamicLayer('3857/QuickBird_2009', '_QuickBird_2009' + _id, 1, [0]), '98年(2009)衛星影像圖'));
	_layerArray.push(createLayerObj(createDynamicLayer('3857/Formasat2_2010', '_Formasat2_2010' + _id, 1, [0]), '99年(2010)衛星影像圖'));
	_layerArray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2011', '_NTPCWV2_2011' + _id, 1, [0]), '100年(2011)衛星影像圖'));
	_layerArray.push(createLayerObj(createDynamicLayer('3857/NTPCWV2_2013', '_NTPCWV2_2013' + _id, 1, [0]), '102年(2013)衛星影像圖'));
	_layerArray.push(createLayerObj(createTileLayer('Satellite', '_Satellite' + _id, 1), '105年(2016)衛星影像圖'));
	_layerArray.push(createLayerObj(createTileLayer('map', '_map' + _id, 1), '新北電子地圖'));
	
};
ExcisionMap.prototype.init = function () {
	//執行初始化
	this.initLayerArray(this.layerArray);
	if(this.id == "mapDiv1_1"){
		this.extendLayer();
	}
	this.addAllLayer(this.map, this.layerArray);

	dojo.connect(this.map, "onLoad", function (){
		this.syncExtentTime = 1;
	});

	// 設定工具列顯示的項目
	ezek_setToolBoxParameter("toolBoxSort", [9]);
	// 初始化工具列
	//initEzekArcgisToolBox(this.id);
};

//NOTE:------------------------------------------------------------------------
//NOTE: 建置影像比對的切換地圖按鈕
//NOTE:------------------------------------------------------------------------
var SeparatedBtn = function (ID) {
	//宣告變數
	this.id = ID;
	this.btnStr = "";
	this.mapID = "";

	//宣告初始化方法
	this.initBtnStr = function () {
		var _mapID = this.mapID;
		var btnStr = "";
		btnStr += "<div class='btn-group dropup' id='btnGroup2'>";
		btnStr += "<button type='button' class='btn btn-warning dropdown-toggle' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>新北市電子地圖 </button>";
		btnStr += (this.isMenuDropRight) ? "<div class='dropdown-menu dropdown-menu-right'>" : "<div class='dropdown-menu'>";
		$.each(this.layerArray, function (index, value) {
			var _layerName = value.layerName;
			btnStr += "<button class='dropdown-item' type='button' target-map-id='" + _mapID + "' target-index='" + index + "' onclick='separatedBtnOnClick(this)'>" + _layerName + "</button>";
		});
		btnStr += "</div>";
		btnStr += "</div>";
		this.btnStr = btnStr;
	};
	this.initBtnElement = function () {
		$("#" + this.id).append(this.btnStr);
	};
}
SeparatedBtn.prototype.bindingMapID = function (_id) {
	this.mapID = _id;
};
SeparatedBtn.prototype.bindingLayerArray = function (_layerarray) {
	this.layerArray = _layerarray;
};
SeparatedBtn.prototype.setIsMenuDropRight = function (_isRight) {
	this.isMenuDropRight = _isRight;
};
SeparatedBtn.prototype.init = function (_mapInfo) {
	//執行初始化	
	this.initBtnStr();
	this.initBtnElement();
};

function separatedBtnOnClick(_this) {
	var Btn = $(_this);
	var map = getMapFromMapInfo(Btn.attr('target-map-id'));
	var index = Btn.attr('target-index');
	map.hideAllLayer();
	map.showLayer(index);

	var layerName = $(_this).context.innerHTML;
	$(_this).parent().prev().html(layerName);
	$(_this).siblings().css('color', '#212529');
	$(_this).css('color', '#bbbbbb');
}

function getMapFromMapInfo(_mapId) {
	return ExcisionMapInfo[_mapId];
}

function getEzekMapZoomLevel() {
	return ezekMap.getZoom();
}


/**
 * 初始化影像比對－二分格連續模式的地圖與LayerSwipe
 */
function initSwipeLayer() {
	require([
		"esri/dijit/LayerSwipe",
		"dojo/domReady!"
	], function (
		LayerSwipe
	) {
		// 新建一個地圖物件
		var mapID = 'mapDiv1_1';
		var map = getMapFromMapInfo(mapID).getMap();
		// 取得全部圖層陣列
		var layerArr = getMapFromMapInfo(mapID).getLayerArray();
		// 取得左圖層
		var swipeLayer1 = layerArr[ExcisionMapInfo.swipeIndex_Left].layer;
		// 取得右圖層
		var swipeLayer2 = layerArr[ExcisionMapInfo.swipeIndex_Right].layer;
		// 初始化切換圖層按鈕
		initSwipeBtn(mapID, 'separatedBtn1_1', layerArr, false, 'swipeBtnLeftOnClick');
		initSwipeBtn(mapID, 'separatedBtn1_2', layerArr, true, 'swipeBtnRightOnClick');
		// 初始化切換圖層按鈕文字
		$('#separatedBtn1_1 div button.btn.btn-warning.dropdown-toggle').html(layerArr[ExcisionMapInfo.swipeIndex_Left].layerName);
		$('#separatedBtn1_2 div button.btn.btn-warning.dropdown-toggle').html(layerArr[ExcisionMapInfo.swipeIndex_Right].layerName);
		// 顯示左右圖層
		swipeLayer1.show();
		swipeLayer2.show();
		// 宣告LayerSwipe綁定至mapDiv1_1上
		var swipeWidget = new LayerSwipe({
			map: map,
			layers: [swipeLayer1]
		}, "swipeDiv");
		// 初始化
		swipeWidget.startup();
		// 設定拖拉條預設在中間
		$('#swipeDiv div').css('left', '50%');
		swipeWidget.swipe();
		// 暫存
		ezekMapInfo.swipeWidget = swipeWidget;
		// 同步地圖座標
		// dojo.connect(map, "onMouseDragEnd", updatenowCenter);
		// dojo.connect(map, "onZoomEnd", updatenowCenter);
		// function updatenowCenter() {
		// 	updateNowLocation(map.extent);
		// }
	});

	
}

function initSwipeBtn(_mapid, _btnid, _layerArray, _isMenuDropRight, fictionName) {
	var mapID = _mapid;
	var btnID = _btnid;
	var isMenuDropRight = _isMenuDropRight;
	var layerArray = _layerArray;
	var btnStr = "";

	btnStr += "<div class='btn-group dropup' id='btnGroup2'>";
	btnStr += "<button type='button' class='btn btn-warning dropdown-toggle' data-toggle='dropdown' aria-haspopup='true' aria-expanded='false'>新北電子地圖 </button>";
	btnStr += (isMenuDropRight) ? "<div class='dropdown-menu dropdown-menu-right'>" : "<div class='dropdown-menu'>";
	
	var layerCnt = 9; //原本11
	if ( layerArray && layerArray.length > 0) layerCnt = layerArray.length / 2;
	//console.log(layerArray);
	//console.log(layerArray.length);
	$.each(layerArray, function (index, value) {
		// 比對的塗層有新增 index 要改 
		if ((isMenuDropRight && index >= layerCnt) || (!isMenuDropRight && index < layerCnt)) {
			var _layerName = value.layerName;
			btnStr += "<button class='dropdown-item disabled' type='button' target-map-id='" + mapID + "' target-index='" + index + "' onclick='" + fictionName + "(this)' style='color:#212529;'>" + _layerName + "</button>";
		}
	});
	btnStr += "</div>";
	btnStr += "</div>";

	$('#' + btnID).append(btnStr);
}

function swipeBtnLeftOnClick(_this) {
	var Btn = $(_this);
	var mapID = Btn.attr('target-map-id');
	var index = parseInt(Btn.attr('target-index'));
	var layerArr = getMapFromMapInfo(mapID).getLayerArray();
	var index_R = ExcisionMapInfo.swipeIndex_Right;
	var index_L = ExcisionMapInfo.swipeIndex_Left;

	layerArr[index_L].layer.hide();
	layerArr[index].layer.show();
	ezekMapInfo.swipeWidget.layers = [layerArr[index].layer];
	ExcisionMapInfo.swipeIndex_Left = index;

	var layerName = $(_this).context.innerHTML;
	$(_this).parent().prev().html(layerName);
	$(_this).siblings().css('color', '#212529');
	$(_this).css('color', '#bbbbbb');
}

function swipeBtnRightOnClick(_this) {
	var Btn = $(_this);
	var mapID = Btn.attr('target-map-id');
	var index = parseInt(Btn.attr('target-index'));
	var layerArr = getMapFromMapInfo(mapID).getLayerArray();
	var index_R = ExcisionMapInfo.swipeIndex_Right;
	var index_L = ExcisionMapInfo.swipeIndex_Left;

	layerArr[index_R].layer.hide();
	layerArr[index].layer.show();
	ExcisionMapInfo.swipeIndex_Right = index;

	var layerName = $(_this).context.innerHTML;
	$(_this).parent().prev().html(layerName);
	$(_this).siblings().css('color', '#212529');
	$(_this).css('color', '#bbbbbb');
}

function changeBDSLayerContent(_this) {
	// var wheresql = bulidDMLLayerWhereSQL();
	// setDMLLayerDefinitionExpression(wheresql);
	// setDMLLayerClickQueryIdentifyParams(wheresql);
}

function bulidDMLLayerWhereSQL() {
	var BDSYear = $('#BDSYear').val();
	var BDSType_1 = $('#BDSType_1').is(":checked");
	var BDSType_2 = $('#BDSType_2').is(":checked");
	var BDSType_3 = $('#BDSType_3').is(":checked");
	var BDSType_4 = $('#BDSType_4').is(":checked");
	var BDSType_5 = $('#BDSType_5').is(":checked");
	var wheresql = "";

	// 查報中
	if (BDSType_1) {
		wheresql += "(status='1' AND dis_sort<>'Z')";
	}
	// 處理中
	if (BDSType_2) {
		if (wheresql !== "") {
			wheresql += " OR ";
		}
		wheresql += "status='2'";
	}
	// 已拆除
	if (BDSType_3) {
		if (wheresql !== "") {
			wheresql += " OR ";
		}
		wheresql += "status='3'";
	}
	// 移送法辦
	if (BDSType_5) {
		if (wheresql !== "") {
			wheresql += " OR ";
		}
		wheresql += "status='4'";
	}
	// 拍照列管
	if (BDSType_4) {
		if (wheresql !== "") {
			wheresql += " OR ";
		}
		wheresql += "dis_sort='Z'";
	}
	// 建置where整體外框
	if (wheresql !== "") {
		wheresql = "(" + wheresql + ")";
	}
	// 若四種狀態都無勾選，則不顯示任何案件
	if (wheresql === "") {
		wheresql = "1<>1";
	}
	// 案件年份
	if (BDSYear !== "0") {
		if (wheresql !== "") {
			wheresql += " AND ";
		}
		wheresql += "reg_yy='" + BDSYear + "'";
	}

	// wheresql = "reg_yy='" + $('#s_regYY').val() + "' AND reg_no='"+$('#s_regNO').val()+"'";
	wheresql = "";

	return wheresql;
}

function setDMLLayerDefinitionExpression(_wheresql) {
	ezekMapInfo.DMLLayer.setDefinitionExpression(_wheresql);

	// ezekMapInfo.TextLayer.setDefinitionExpression(_wheresql);

	// var textLayerDefinitions = [];
	// 	//Mapsefrver的連結，例如：https://IP_Adress/arcgis/rest/services/xxxxxxxx/MapServer
	// 	var textMapServerURL = "https://building.tycg.gov.tw/arcgis/rest/services/oba_tycgisMap/MapServer/0";
	// 	var textDynamicLayer = new ArcGISDynamicMapServiceLayer(textMapServerURL);
	// 	//因為一個MapServer可能會有多個圖層，所以這裡設定哪個是要被顯示的
	// 	textDynamicLayer.setVisibleLayers([0]);
	// 	//此為設定圖層的where條件，跟VisibleLayers一樣需要指定，所以以陣列指定哪個圖層的WHERE條件
	// 	textLayerDefinitions[0] = "reg_yy='" + $('#BDSYear').val() + "'";


	ezekMapInfo.TextLayer.setLayerDefinitions([_wheresql]);
}

function setDMLLayerClickQueryIdentifyParams(_wheresql) {
	identifyParams.layerDefinitions[0] = _wheresql;
}

function setIsEnableClick(_status) {
	ezekMapInfo.isEnableClick = _status;
}

function map_openDarkMode() {

	require([
		"esri/layers/LayerDrawingOptions",
		"esri/symbols/SimpleFillSymbol",
		"esri/symbols/SimpleLineSymbol",
		"esri/renderers/SimpleRenderer",
		"esri/Color",
		"dojo/domReady!"
	], function (
		LayerDrawingOptions, SimpleFillSymbol, SimpleLineSymbol, SimpleRenderer, Color
	) {
		$("#mapDiv").addClass("darkMode");
		ezekMapInfo.BaseMap.setOpacity(0.45);
		ezekMapInfo.BaseLabelMap.setOpacity(0.1);
		// ezekMapInfo.ZoneMap.setOpacity(1);
		if (ezekMapInfo.ZoneMap === null) {
			//ezekMapInfo.ZoneMap = createDynamicLayer('F/村里界', 'zon2', 1, [1]);
			ezekMapInfo.ZoneMap = createDynamicLayer('Landinfo', '鄉鎮市區界', 1, [8,9]);
			//ezekMapInfo.ZoneMap = createDynamicLayer('TGOS/WMS', 'zon2', 1, [3]);

			// //初始化外框線樣式
			// var LineSfs = new SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID, new Color([255, 255, 0]), 2); 
			// //初始化圖塊樣式
			// var polygonSfs = new SimpleFillSymbol(SimpleFillSymbol.STYLE_SOLID, LineSfs, new Color([255, 255, 0, 1]));
			// //使用LayerDrawingOptions類來設置要渲染的選項
			// var layerDrawingOption = new LayerDrawingOptions();
			// //設定渲染的選項為我們剛剛初始化的圖塊樣式
			// layerDrawingOption.renderer = new SimpleRenderer(polygonSfs);
			// //初始化一個陣列，設定有哪些MapServerLayer ID的圖層套用至此選項
			// var drawingOptionsList = [];
			// drawingOptionsList[0] = layerDrawingOption;
			// drawingOptionsList[1] = layerDrawingOption;
			// drawingOptionsList[2] = layerDrawingOption;
			// drawingOptionsList[3] = layerDrawingOption;
			// drawingOptionsList[4] = layerDrawingOption;
			// //套用設定
			// ezekMapInfo.ZoneMap.setLayerDrawingOptions(drawingOptionsList);

			ezekMap.addLayer(ezekMapInfo.ZoneMap);
		}
	});
}

function map_closeDarkMode() {
	$("#mapDiv").removeClass("darkMode");
	ezekMapInfo.BaseMap.setOpacity(0.5);
	ezekMapInfo.BaseLabelMap.setOpacity(0.5);
	if (ezekMapInfo.ZoneMap) {
		ezekMapRemoveLayer(ezekMapInfo.ZoneMap);
		ezekMapInfo.ZoneMap = null;
	}
	resetSts2();
}

function ezekMapRemoveLayer(layer) {
	ezekMap.removeLayer(layer);
}

function ezekMapScaleToFullExtent() {
	ezekMap.setExtent(initExtent, true);
}

function getEzekMapZoomLevel() {
	return ezekMap.getZoom();
}