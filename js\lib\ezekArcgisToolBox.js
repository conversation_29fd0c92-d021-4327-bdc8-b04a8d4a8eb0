//NOTE:------------------------------------------------------------------------
//NOTE:  來源： 易展數位科技 , 2018/02/08 , By Martin.
//NOTE:  名稱： ArcgisToolBox.js
//NOTE:  敘述： 使用Arcgis時，可透過此JS來快速新增簡易工具列.
//NOTE:  使用方法： (1)在ArcgisMap Onload時呼叫ezek_setToolBoxParameter("toolBoxSort",<int Array>) , 第二個參數的數字陣列代表想出現哪幾種toolbox的子功能;
//NOTE:           (2)在ArcgisMap Onload時呼叫初始化函數initEzekArcgisToolBox() , 且傳入地圖元素的div
//NOTE:	 範例： var ezekMap;  //此變數為ArcgisMap
//NOTE:	       var mapDivId; //此變數為Map元素的id值
//NOTE:	       var toolBoxTypeArray = [0,1,2,3,4,5,6]; //此變數為設定想在toolbox出現的種類
//NOTE:	       ezekMap.on("load", ezek_setToolBoxParameter("toolBoxSort",toolBoxTypeArray));  //使用方法1 
//NOTE:	       ezekMap.on("load", initEzekArcgisToolBox(mapDivId)); //使用方法2
//NOTE:	 Callback函數表：
//NOTE:		   (1) ezek_setToolBoxParameter("didFinishPolyLineCirculate",function(length){ //此為測量距離後會呼叫的函數，並傳入距離測量結果的參數
//NOTE:				   console.log(length);  //印出距離測量結果 , ex: 2018.308公里
//NOTE:		 	   });
//NOTE:	 	   (2) ezek_setToolBoxParameter("didFinishPolyGonCirculate",function(area){ //此為測量面積後會呼叫的函數，並傳入面積測量結果的參數
//NOTE:				   console.log(area);	//印出面積測量結果 , ex: 2018.308平方公里
//NOTE:		 	   });
//NOTE:	 函數表：
//NOTE:  	(1) ezek_setToolBoxParameter  設定基本工具的順序
//NOTE:  	(2) ezek_getIsToolUsing  	   取得目前是否正在使用測量工具
//NOTE:  	(3) initEzekArcgisToolBox     初始化工具列
//NOTE:  	(4) initCirculateTool         初始化所有基本工具
//NOTE:  	(5) changeToolType            切換ToolBox的種類
//NOTE:  	(6) clearToolType             取消所有的toolBox
//NOTE:  	(7) initToolBoxClickEvent     設定ToolBox的HighLight模式
//NOTE:  	(8) openOrCloseToolBox        切換即顯示toolBox的開關
//NOTE:  	(9) changeSrcFromImg          設定img物件圖片切換
//NOTE:  工具列種類(toolBoxTypeArray)：
//NOTE:  	(0) 開關工具列
//NOTE:  	(1) 縮放至全圖
//NOTE:  	(2) 顯示前一圖面
//NOTE:  	(3) 顯示後一圖面
//NOTE:  	(4) 測量距離
//NOTE:  	(5) 測量面積
//NOTE:  	(6) 清除圖面標記
//NOTE:  	(7) 圖層控制 (都市計畫書圖查詢系統使用)
//NOTE:------------------------------------------------------------------------


//NOTE:------------------------------------------------------------------------
//NOTE: 全域變數
//NOTE:------------------------------------------------------------------------
var tb = null; //Toolbox物件
var initMarkerOnClick = null;  //map觸發點擊的物件，測量時新增結果文字時會用到
var outputText = "";  //測量後的結果文字
const toolBoxParameter = { 
	toolboxID:'toolBox',
	isToolUsing: false, //測量工具是否正在使用
	toolBoxSort: [], //基本工具的順序
	didFinishPolyLineCirculate: null, //此變數的值為fuction , 測量 距離 後會觸發的callback
	didFinishPolyGonCirculate: null,   //此變數的值為fuction , 測量 面積 後會觸發的callback
	mapGraphicsClear: null   //此變數的值為fuction , 點擊清除畫面時的callback
};

const layoutXY = {  
	searchDiv_left: "0px", //搜尋選單的位置
	slider_left: "0px", //放大縮小選單的位置
	toolBox_left: "0px", //工具列icon的位置
	toolBox_view_left: "0px",   //工具列選單的位置
	mapTypNav_left :"0px"
};

//NOTE:------------------------------------------------------------------------
//NOTE: 設定基本工具的順序
//NOTE:------------------------------------------------------------------------
function ezek_setToolBoxParameter(key,value){
	toolBoxParameter[key] = value;
}

//NOTE:------------------------------------------------------------------------
//NOTE: 取得目前是否正在使用測量工具
//NOTE:------------------------------------------------------------------------
function ezek_getIsToolUsing(){
	return toolBoxParameter.isToolUsing;
}

//NOTE:------------------------------------------------------------------------
//NOTE: 設定是否正在使用測量工具
//NOTE:------------------------------------------------------------------------
function ezek_setIsToolUsing(isusing){
	toolBoxParameter.isToolUsing = isusing;
}

//NOTE:------------------------------------------------------------------------
//NOTE: 初始化工具列
//NOTE:------------------------------------------------------------------------
function initEzekArcgisToolBox(mapDivId) {
	//NOTE: 所有基本工具
	//******  這裏 理想寫一個可以讓使用者自行新增的方法  *******/
	var tooltypeStr = [
		'<img  src="img/mapToolItem_0_off.png" onclick="changeToolType(1)" title="縮放至全圖" alt="縮放至全圖" class="mapButtonType1">',
		'<img  src="img/mapButton_preScr_off.png" onclick="changeToolType(2)" title="回上一圖面" alt="回上一圖面" class="mapButtonType1">',
		'<img  src="img/mapButton_nextScr_off.png" onclick="changeToolType(3)" title="回下一圖面" alt="回下一圖面" class="mapButtonType1">',
		'<img  src="img/mapToolItem_1_off.png" onclick="changeToolType(4)" title="測量距離" alt="測量距離" class="mapButtonType2" id="mapBtnLine">',
		'<img  src="img/mapToolItem_2_off.png" onclick="changeToolType(5)" title="測量面積" alt="測量面積" class="mapButtonType2" id="mapBtnGon">',
		'<img  src="img/mapToolItem_3_off.png" onclick="changeToolType(8)" title="查詢圖層資訊" alt="查詢圖層資訊" class="mapButtonType2" id="mapBtnInfoWindow">',
		'<img  src="img/mapToolItem_10_off.png" onclick="changeToolType(7)" title="設定圖層顯示與透明度" alt="設定圖層顯示與透明度" class="mapButtonType2" id="mapBtnLayer">',
		'<img  src="img/mapToolItem_9_off.png" onclick="changeToolType(9)" title="列印圖面" alt="列印圖面" class="mapButtonType1" id="mapBtnDownloadMapImg">',
		'<img  src="img/mapToolItem_4_off.png" onclick="changeToolType(6)" title="清除圖面標記" alt="清除圖面標記" class="mapButtonType1">',
		// '<img  style="margin-top:5px;" src="img/mapButton_dark_off.png" onclick="changeToolType(10)" title="切換夜間模式" alt="切換夜間模式" class="mapButtonType3" id="mapBtnSwitchDarkMode">'
		//NOTE: 110/06/10 更改工具列版面
		'<img  src="img/mapToolItem_5_off.png" onclick="changeToolType(17)" title="違章查報案件區域性結案作業" alt="違章查報案件區域性結案作業" class="mapButtonType2" id="mapBtnSwitchDarkMode">',
		'<img  src="img/mapToolItem_6_off.png" onclick="changeToolType(11)" title="案件查詢" alt="案件查詢" class="mapButtonType2" id="mapBtnSwitchDarkMode">',
		'<img  src="img/mapToolItem_7_off.png" onclick="changeToolType(15)" title="區域違建框選查詢" alt="區域違建框選查詢" class="mapButtonType2" id="mapBtnSwitchDarkMode">',
		'<img  src="img/mapToolItem_8_off.png" onclick="changeToolType(16)" title="行政區案件分布分析" alt="行政區案件分布分析" class="mapButtonType2" id="mapBtnSwitchDarkMode">'
	];
		

	
	var toolboxID = (mapDivId==="mapDiv") ? toolBoxParameter.toolboxID : toolBoxParameter.toolboxID+"_"+mapDivId ;
	//NOTE: 取得基本工具的順序
	var toolBoxSort = toolBoxParameter.toolBoxSort;
	//NOTE: 準備加入基本工具的字串
	var toolBoxHtml = '<div id="'+toolboxID+'" style="position: absolute;top: 16px;left: 472px;z-index: 51; transition-duration: 0.7s;">';	
	toolBoxHtml += '<img id="toolsMenu" style="" src="img/mapToolBtn_on.png?1090207" toolBoxid="'+toolboxID+'" onclick="openOrCloseToolBox(this)" title="工具列" alt="工具列">';
	toolBoxHtml += '<div id="'+toolboxID+'_view" style=" width:352px;padding:2px 0px;z-index:-1;display:inline-block;">';	
	
	for(var x=0 ; x<toolBoxSort.length ; x++){
		toolBoxHtml += tooltypeStr[toolBoxSort[x]];
	}
	
	//加入圖層控制清單
	toolBoxHtml += '<div id="layerControl" style="position:absolute; top: 40px; right:-120px; display:inline-block;">';
		//加入MapServer清單
		toolBoxHtml += '<div id="mapServerList" class="shadow_style" style="border-radius: 4px;display:inline-block; background-color: rgba(255,255,255,0.8);vertical-align: top;padding:6px;width: max-content;">'; //inline-block;		
			toolBoxHtml += '<table>';
			toolBoxHtml += '<thead>';
			toolBoxHtml += '<tr>';
			toolBoxHtml += '<td style="text-align:center;"colspan="4"><label>圖層設定</label> <img  style="width:20px;float:right;cursor: pointer;"alt="取消" src="img/DeleteQ.png" class="mapButtonType2"  onclick="changeToolType(7)"></td>';
			toolBoxHtml += '</tr>';
			toolBoxHtml += '</thead>';
			// 加入圖層ul - ul_mapServerlistGroup
			toolBoxHtml += '<tr><td colspan="4"><ul id="ul_mapServerlistGroup" style="width: 490px; height: 380px; overflow: auto; padding-left: 0px"></ul></td></tr>';
			toolBoxHtml += '</table>';
		toolBoxHtml += '</div>';		
	toolBoxHtml += '</div>';


	toolBoxHtml += '</div>'; // _view  END
	toolBoxHtml += '</div>'; // tool END
	$('#'+mapDivId+'_root').prepend($(toolBoxHtml));
	console.log("$('#'+mapDivId+'_root').prepend($(toolBoxHtml));");
	//NOTE: 初始化ToolBox的HighLight模式
	initToolBoxClickEvent();
	//NOTE: 初始化所有基本工具
	initCirculateTool(-1);
	//關圖層透明度
	$("#layerControl").hide();
	
	$("#mapDiv_zoom_slider").css("top","60px");
	$('#mapDiv1_1_zoom_slider').css('top', '60px');
	$('#mapDiv2_1_zoom_slider').css('top', '60px');
	$('#mapDiv2_2_zoom_slider').css('top', '60px');
	$('#mapDiv4_1_zoom_slider').css('top', '60px');
	$('#mapDiv4_2_zoom_slider').css('top', '60px');
	$('#mapDiv4_3_zoom_slider').css('top', '60px');
	$('#mapDiv4_4_zoom_slider').css('top', '60px');
	
	//NOTE: 儲存初始值
	layoutXY.searchDiv_left = $("#searchDiv").css("left");
	layoutXY.slider_left = $("#mapDiv_zoom_slider").css("left");
	layoutXY.toolBox_left = $("#toolBox").css("left");
	layoutXY.toolBox_view_left = $("#toolBox_view").css("left");
	layoutXY.mapTypNav_left = $("#mapTypNav").css("left");
	
	//初始化圖層-因為要在加入圖層控制清單後才做
	initGroupList();
}
//NOTE:------------------------------------------------------------------------
//NOTE: 掛其他功能或工具在 工具列下面
//NOTE:------------------------------------------------------------------------
function addOtherTool(mapDivId){
	
	var toolboxID = (mapDivId==="mapDiv") ? toolBoxParameter.toolboxID : toolBoxParameter.toolboxID+"_"+mapDivId ;
	toolboxID = "sub" + toolboxID;
	var _innerHTML = '';
	// 地圖模式
	var mapTypeStr  = [
		'<img  style="margin-top:5px;" src="img/mapButton_map_off.png" onclick="changeToolType(11)" title="違章地圖" alt="違章地圖" class="mapButtonType1" id="mapBtnSwitchDarkMode">',
		'<img  style="margin-top:5px;" src="img/mapButton_map_off1.png" onclick="changeToolType(12)" title="影像比對-二分割連續模式" alt="影像比對-二分割連續模式" class="mapButtonType1" id="mapBtnSwitchDarkMode">',
		'<img  style="margin-top:5px;" src="img/mapButton_map_off2.png" onclick="changeToolType(13)" title="影像比對-二分割分隔模式" alt="影像比對-二分割分隔模式" class="mapButtonType1" id="mapBtnSwitchDarkMode">',
		'<img  style="margin-top:5px;" src="img/mapButton_map_off4.png" onclick="changeToolType(14)" title="影像比對-四分割分隔模式" alt="影像比對-四分割分隔模式" class="mapButtonType1" id="mapBtnSwitchDarkMode">',
	];
	
	// 功能列
	_innerHTML = '<div id="mapTypNav" style="position: absolute;top: 18px;left: 428px;z-index: 51; transition-duration: 0.7s;">';	
	_innerHTML += '<img id="mapTypsMenu" src="img/scrTypBtn_0.png?1090207" toolBoxid="mapTypNav" onclick="openOrCloseMapTypNav(this)" title="地圖模式" alt="地圖模式">';
	_innerHTML += '<div id="mapTypNav_view" style=" width:106px;padding:2px 0px;z-index:-1;display:none;">';
	
	_innerHTML += '<label style="width: 106px;" class="choiceMap btn btn-custom active" onclick="changeToolType(11)">單一地圖</label><br>';
	_innerHTML += '<label style="width: 106px;" class="choiceMap btn btn-custom" onclick="changeToolType(12)">二分格連續</label><br>';
	_innerHTML += '<label style="width: 106px;" class="choiceMap btn btn-custom" onclick="changeToolType(13)">二分格鏡射</label><br>';
	_innerHTML += '<label style="width: 106px;" class="choiceMap btn btn-custom" onclick="changeToolType(14)">四分格鏡射</label>';
	
	_innerHTML += '</div>'; // mapTypNav_view END
	_innerHTML += '</div>'; // Nav END

	$("#searchDiv").after(_innerHTML);
	
	
	$(".choiceMap").click(function(){

		$(this).addClass("active");
		$(".choiceMap").not(this).removeClass("active");
	
	});
	
	layoutXY.mapTypNav_left = $("#mapTypNav").css("left");
}


//NOTE:------------------------------------------------------------------------
//NOTE: 顯示所有左側Grouplist
//NOTE:------------------------------------------------------------------------
function resetSearchDivLayout(){
	//NOTE: 回復到初始位置
	$(".scalebar_bottom-left.esriScalebar,#latlngInfo").css("left","360px");
	$("#searchDiv").css("left",layoutXY.searchDiv_left);
	$("#mapDiv_zoom_slider").css("left",layoutXY.slider_left);
	$("#toolBox").css("left",layoutXY.toolBox_left);
	$("#toolBox_view").css("left",layoutXY.toolBox_view_left);
	$("#mapTypNav").css("left",layoutXY.mapTypNav_left);
	
	$("#mapDiv_3D").width($(document).width()-$("#searchDiv").width());
	$("#compareDiv").width($(document).width()-$("#searchDiv").width());
}

//NOTE:------------------------------------------------------------------------
//NOTE: 顯示所有左側Grouplist(列印後)
//NOTE:------------------------------------------------------------------------
function resetSearchDivLayoutForPrint(){
	//NOTE: 回復到初始位置
	$(".scalebar_bottom-left.esriScalebar,#latlngInfo").css("left","428px");
	$("#searchDiv").css("left",layoutXY.searchDiv_left);
	$("#mapDiv_zoom_slider").css("left","428px");
	$("#toolBox").css("left",layoutXY.toolBox_left);
	$("#toolBox_view").css("left",layoutXY.toolBox_view_left);
	$("#mapTypNav").css("left",layoutXY.mapTypNav_left);
	
	$("#mapDiv_3D").width($(document).width()-$("#searchDiv").width());
	$("#compareDiv").width($(document).width()-$("#searchDiv").width());
}

//------------------------------------------------------------------------
// 隱藏所有左側Grouplist (含隱藏toolBox)
//------------------------------------------------------------------------
function hideSearchDivLayout() 
{
	$(".scalebar_bottom-left.esriScalebar,#latlngInfo").css("left","20px");
	$("#searchDiv,#mapDiv_zoom_slider,#toolBox").css("left","-408px");
	$(".esriPopup.esriPopupVisible").css("visibility","hidden");
	
	$("#mapTypNav").css("left","-33px");
	
}

//------------------------------------------------------------------------
// 隱藏所有左側Grouplist (不隱藏toolBox)
//------------------------------------------------------------------------
function hideSearchDivLayoutWithoutToolbox() 
{
	$(".scalebar_bottom-left.esriScalebar,#latlngInfo").css("left","20px");
	$("#searchDiv").css("left","-328px");
	
	$("#mapTypNav").css("left","20px");
	
	// $("#searchDiv).css("left","-328px");
	$("#mapDiv_zoom_slider").css("left","20px");
	$("#toolBox").css("left","64px");
	$(".esriPopup.esriPopupVisible").css("visibility","hidden");

	// var height = $(window).height();
	$("#mapDiv_3D").css("width","100%");
	$("#compareDiv").css("width","100%");
}

//NOTE:------------------------------------------------------------------------
//NOTE: 初始化所有基本工具
//NOTE:------------------------------------------------------------------------
function initCirculateTool(toolTypeNumber){
	ezekMap.enableScrollWheelZoom();	
	require([
		"dojo/_base/lang",
		"dojo/_base/array",
		"esri/symbols/SimpleLineSymbol",
		"esri/Color",
		"dojo/number",
		"esri/config",
		"esri/map",
		"esri/graphic",
		"esri/geometry/Geometry",
		"esri/geometry/Extent",
		"esri/tasks/GeometryService",
		"esri/tasks/AreasAndLengthsParameters",
		"esri/tasks/LengthsParameters",
		"esri/toolbars/draw",
		"esri/symbols/SimpleFillSymbol",
		"esri/symbols/PictureMarkerSymbol",
		"esri/symbols/Font",
		"esri/symbols/TextSymbol",
		"esri/tasks/PrintTask",
		"esri/tasks/PrintParameters",
		"esri/tasks/PrintTemplate",
		"esri/geometry/Point"],
	function(lang, array, SimpleLineSymbol, Color, number, esriConfig, Map, Graphic, Geometry, Extent, GeometryService, AreasAndLengthsParameters, LengthsParameters, Draw, SimpleFillSymbol, PictureMarkerSymbol, Font, TextSymbol, PrintTask, PrintParameters,PrintTemplate, Point)
	{
		//NOTE: 基本變數定義區
		// var LineStyle = SimpleLineSymbol.STYLE_DASH;
		var PointColor = new Color([219, 0, 32]); //紅色
		var PointFont = new Font("18px", Font.STYLE_NORMAL, Font.VARIANT_NORMAL, Font.WEIGHT_NORMAL);
		var LinePointData = {
			loc: 0,
			lng: 0,
			spatialReference: null
		};
		//NOTE: identify proxy page to use if the toJson payload to the geometry service is greater than 2000 characters.
		//NOTE: 如果toJson有效載荷大於2000個字符，則要使用的代理頁面。
		esriConfig.defaults.io.proxyUrl = "/proxy/";
		//NOTE: If this null or not available the project and lengths operation will not work.  Otherwise it will do a http post to the proxy.
		//NOTE: 正常來說它會做一個http post代理。 但如果這個為null或不可用的物件和錯誤的長度將會造成無法正常工作。 
		esriConfig.defaults.io.alwaysUseProxy = false;
		//NOTE: 清空目前使用的工具
		if(initMarkerOnClick) initMarkerOnClick.remove();
		//NOTE: 初始化測量工具tb
		tb = new Draw(ezekMap);
		var lengthParams = new esri.tasks.LengthsParameters();
		switch(toolTypeNumber)
		{
			case -1: //不使用任何測量工具
				break;
			case 1: //縮放至全地圖
				// navToolbar.zoomToFullExtent();
				ezekMap.setExtent(initExtent, true);
				break;
			case 2: //縮放至前一個範圍
				navToolbar.zoomToPrevExtent();
				break;
			case 3: //縮放至後一個範圍
				navToolbar.zoomToNextExtent();
				break;
			case 4: //測量距離
				$("#layerControl").hide();
				geometryService = new esri.tasks.GeometryService("https://utility.arcgisonline.com/ArcGIS/rest/services/Geometry/GeometryServer");
				dojo.connect(geometryService, "onLengthsComplete", outputDistance);
				dojo.connect(tb, "onDrawEnd", function(geometry){ polyLineDrawEnd(geometry); });
				initMarkerOnClick = dojo.connect(ezekMap, "onClick", function(geometry){ initMarker(geometry); });
				tb.activate(Draw.POLYLINE);
				ezek_setIsToolUsing(true);
				break;
			case 5: //測量面積
				$("#layerControl").hide();
				//geometryService = new GeometryService("http://mcgbm.taichung.gov.tw/arcgis/rest/services/Utilities/Geometry/GeometryServer");
				//geometryService = new GeometryService("http://building.tycg.gov.tw/arcgis/rest/services/Utilities/Geometry/GeometryServer");
				geometryService = new GeometryService(location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/Utilities/Geometry/GeometryServer");
				
				geometryService.on("areas-and-lengths-complete", outputAreaAndLength);
				tb.on("draw-end", lang.hitch(ezekMap, getAreaAndLength));
				initMarkerOnClick = dojo.connect(ezekMap, "onClick", function(geometry){ initMarker(geometry); });
				tb.activate(Draw.POLYGON);
				ezek_setIsToolUsing(true);
				break;
			case 6: //清除所有圖層標記
				//ezekMap.graphics.clear();
				ezekClearGraphic();
				ezek_setIsToolUsing(true);
				if(toolBoxParameter.mapGraphicsClear){
					toolBoxParameter.mapGraphicsClear();
				}
				break;
			case 7: // 圖層設定
				if($("#layerControl").is(":hidden")){
					$("#layerControl").show();					
					$("#layerControl").css("display", "inline-block");
				}else{
					$("#layerControl").hide();
				}
				break;	
			case 8: //點擊查詢地點，且顯示infoWindow
				if(ezek_getIsToolUsing()){
					ezek_setIsToolUsing(false);
					setIsEnableClick(true);
				}else{
					ezek_setIsToolUsing(true);
					setIsEnableClick(false);
				}
				break;
			case 9: //下載地圖圖片
				/*
				$.blockUI({message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;讀取中, 請稍候...', css:{border:'none', padding:'6px', backgroundColor:'#000', '-webkit-border-radius': '10px', '-moz-border-radius': '10px', opacity: .5, color:'#FFF'}});
				var url = "http://210.69.115.47/arcgis/rest/services/Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task";
				var plate = new PrintTemplate();
				var params = new PrintParameters();
				var printTask = new PrintTask(url);
				plate.format = "jpg";
				plate.preserveScale = false;  //設定true可自定義輸出範圍與比例，若false則輸出與ezekMap相同範圍比例的圖。 true為預設。
				params.map = ezekMap;
				params.template = plate;
				printTask.execute(params, printResult);
				*/
				// $("#searchDiv,#mapDiv_zoom_slider,#toolBox,#toolBox_view").hide(500,function() {
				// 	$(".scalebar_bottom-left.esriScalebar").css("left","50px");
				// });
				// $("#searchDiv,#mapDiv_zoom_slider,#toolBox,#toolBox_view").hide();

				// $(".scalebar_bottom-left.esriScalebar").css("left","50px");
				// $("#searchDiv,#mapDiv_zoom_slider,#toolBox,#toolBox_view").css("left","-400px");
				// $(".esriPopup.esriPopupVisible").css("visibility","hidden");
				$("#layerControl").hide();
				hideSearchDivLayout();
				setTimeout(function(){ //Timeout避免畫面移動時跳出列印，造成卡住
					window.print();
				},500);
				break;
			case 10: //切換夜間模式
				if($("#mapDiv").hasClass("darkMode")){
					$("#mapDiv").removeClass("darkMode");
				}else{
					$("#mapDiv").addClass("darkMode");
				}
				break;
			case 11: //
				$("#mapTypNav_view").hide();
				$("#mapTypsMenu").attr("src", "img/scrTypBtn_0.png?1100610");
				navBarClick('0','searchDiv');
				ezek_setIsToolUsing(false);
				$("#searchDiv_Button").children("i").html("chevron_left");
				identifyParams.layerDefinitions = [];
				break;
			case 12: //
				$("#mapTypNav_view").hide();
				$("#mapTypsMenu").attr("src", "img/scrTypBtn_1.png?1100610");
				navBarClick('1-1','');
				break;
			case 13: //
				$("#mapTypNav_view").hide();
				$("#mapTypsMenu").attr("src", "img/scrTypBtn_2.png?1100610");
				navBarClick('1-2','');
				break;
			case 14: //
				$("#mapTypNav_view").hide();
				$("#mapTypsMenu").attr("src", "img/scrTypBtn_3.png?1100610");
				navBarClick('1-3','');
				break;
			case 15: //
				navBarClick('2-1','stsDiv');
				$("#stsDiv_Button").children("i").html("chevron_left");
				identifyParams.layerDefinitions = [];
				break;
			case 16: //
				navBarClick('2-2','stsDistributedDiv');
				identifyParams.layerDefinitions = [];
				break;
			case 17: //
				navBarClick('3','editDiv');
				$("#editDiv_Button").children("i").html("chevron_left");
				identifyParams.layerDefinitions = [];
				break;
		}
		//新增測量標記
		function initMarker(geometry){
			var pictureMarkerSymbol = new PictureMarkerSymbol('img/pin70101.png', 20, 20);
			pictureMarkerSymbol.setOffset(0, 0);
			ezekAddGraphic(geometry.mapPoint, pictureMarkerSymbol);
		}
		//量測距離 - 結束繪圖
		function polyLineDrawEnd(geometry){
			//繪製線段
			lengthParams.polylines = [geometry];
			lengthParams.lengthUnit = esri.tasks.GeometryService.UNIT_METER;
			lengthParams.geodesic = true;
			geometryService.lengths(lengthParams);
			ezekAddGraphic(geometry, new esri.symbol.SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID, PointColor, 2));
			
			//儲存線段的最後一點，標記文字時會使用到
			if (geometry.paths.length > 0) {
				var loclng = geometry.paths[0][geometry.paths[0].length-1];
				LinePointData.loc = loclng[0];
				LinePointData.lng = loclng[1];
				LinePointData.spatialReference = geometry.spatialReference;
			}
		}
		//量測距離 - 開始計算
		function outputDistance(result){

			//計算距離結果
			var output = "";
			var length = result.lengths[0];
			if(length < 1000){ 
				length = (length).toFixed(2)+'公尺';
			}else{
				length = (length/1000).toFixed(2)+'公里';
			}
			if(toolBoxParameter.didFinishPolyLineCirculate){
				toolBoxParameter.didFinishPolyLineCirculate(length);
			}
			//加上量測結果文字
			var textSymbol = new TextSymbol(length,	PointFont, PointColor);
			textSymbol.setOffset(0,12); //往上移動一點
			var labelPoint = new Point(LinePointData.loc, LinePointData.lng, LinePointData.spatialReference);
			var labelPointGraphic = new Graphic(labelPoint, textSymbol);
			ezekAddGraphic(labelPoint, textSymbol);
			
			clearToolType();
			changeSrcFromImg($("#mapBtnLine"));
			ezek_setIsToolUsing(false);
		}
		//量測面積 - 結束繪圖
		function getAreaAndLength(evtObj) {
			var map = this, geometry = evtObj.geometry;
			var sfs = new SimpleFillSymbol(SimpleFillSymbol.STYLE_SOLID,
				new SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID, PointColor, 2), 
				new Color([0,0,0,0.25])
			);
			//var graphic = map.graphics.add(new esri.Graphic(geometry, sfs));
			ezekAddGraphic(geometry, sfs);
			var areasAndLengthParams = new AreasAndLengthsParameters();
			areasAndLengthParams.lengthUnit = GeometryService.UNIT_FOOT;
			areasAndLengthParams.areaUnit = GeometryService.UNIT_METER;
			areasAndLengthParams.calculationType = "geodesic";
			geometryService.simplify([geometry], function(simplifiedGeometries) {
				areasAndLengthParams.polygons = simplifiedGeometries;
				geometryService.areasAndLengths(areasAndLengthParams);
				geometryService.simplify([geometry], getLabelPoints);
			});
		}
		//量測面積 - 開始計算
		function outputAreaAndLength(evtObj) {  
			var result = evtObj.result;
			var output = "";
			var area = result.areas[0];
			/*
			if(area < 247){ // 0.000247 英畝 = 1 平方公尺
				output = (result.areas[0]/0.000247).toFixed(2)+'平方公尺';
			}else{
				output = (result.areas[0]/247).toFixed(2)+'平方公里';
			}
			*/
			if(area < 1000000){  // 公尺
				output = (result.areas[0]).toFixed(2)+'平方公尺';
			}else{
				output = (result.areas[0]/1000000).toFixed(2)+'平方公里';
			}
			if(toolBoxParameter.didFinishPolyGonCirculate){
				toolBoxParameter.didFinishPolyGonCirculate(output);
			}
			outputText = output;
		}
		//量測面積 - 標記面積文字
		function getLabelPoints(geometries){
			if (geometries[0].rings.length > 0) {
				geometryService.labelPoints(geometries, function(labelPoints) { // callback
					array.forEach(labelPoints, function(labelPoint) {
						var textSymbol = new TextSymbol(outputText, PointFont, PointColor);
						var labelPointGraphic = new Graphic(labelPoint, textSymbol);
						ezekAddGraphic(labelPoint, textSymbol);

						clearToolType();
						changeSrcFromImg($("#mapBtnGon"));
						ezek_setIsToolUsing(false);
					});
				});
			} else {
				alert("測量面積時，至少需要選擇三個量測點。");
			}
		}

		//下載地圖圖片
		function printResult(data){
			$("#mapJpgDownload").attr("href",data.url);
			$("#mapJpgDownload")[0].click();
			$.unblockUI();
		}
	});
}

//NOTE:------------------------------------------------------------------------
//NOTE: 切換ToolBox的種類
//NOTE:------------------------------------------------------------------------
function changeToolType(typeNumber){
	ezek_setIsToolUsing(true);
	tb.deactivate();
	initCirculateTool(typeNumber);
}

//NOTE:------------------------------------------------------------------------
//NOTE: 取消所有的toolBox
//NOTE:------------------------------------------------------------------------
function clearToolType(){
	ezek_setIsToolUsing(true);
	tb.deactivate();
	initCirculateTool(-1);
}

//NOTE:------------------------------------------------------------------------
//NOTE: 設定ToolBox的HighLight模式
//NOTE:------------------------------------------------------------------------
function initToolBoxClickEvent() 
{
	//NOTE: 僅點選一下的功能
	$(".mapButtonType1").mousedown(function() {
		//NOTE: 取消測量距離與測量範圍
		$.each($(".mapButtonType2"), function( index, value ) {
			// if($(value).attr("id") === "mapBtnSwitchDarkMode"){
			// 	return ;
			// }
			$(value).attr("src",$(value).attr("src").replace("_on", "_off"));
		});
		//NOTE: 設定img物件圖片切換
		changeSrcFromImg($(this));
	});
	$(".mapButtonType1").mouseup(function() {
		//NOTE: 設定img物件圖片切換
		changeSrcFromImg($(this));
	});
	//NOTE: 點選後會進行其他動作的按鈕
	$(".mapButtonType2").click(function() {
		//NOTE: 把其他測量的圖示取消
		$(".mapButtonType2").not($(this)).each(function() {			
				var src = $(this).attr('src');					
				src = src.replace("_on", "_off");				
				$(this).attr("src", src);
		});
		//NOTE: 設定img物件圖片切換
		var src = $(this).attr('src');
		if(src.indexOf("_on") != -1){
			src = src.replace("_on", "_off");
			clearToolType();
		}else{
			src = src.replace("_off", "_on");
		}
		$(this).attr("src", src);
	});
	//NOTE: 有開與關功能的按鈕
	$(".mapButtonType3").click(function() {
		//NOTE: 設定img物件圖片切換
		var src = $(this).attr('src');
		if(src.indexOf("_on") != -1){
			src = src.replace("_on", "_off");
			clearToolType();
		}else{
			src = src.replace("_off", "_on");
		}
		$(this).attr("src", src);
	});
}

//NOTE:------------------------------------------------------------------------
//NOTE: 切換即顯示toolBox的開關
//NOTE:------------------------------------------------------------------------
function openOrCloseToolBox(obj){
	var toolboxid = obj.getAttribute('toolboxid');
	if($("#"+toolboxid+"_view").is(":hidden")){
		$("#toolsMenu").attr("src", "img/mapToolBtn_on.png?1100315");
		$("#"+toolboxid+"_view").show();
	}else{
		$("#toolsMenu").attr("src", "img/mapToolBtn_off.png?1100315");
		$("#"+toolboxid+"_view").hide();
	}
}


//NOTE:------------------------------------------------------------------------
//NOTE: 切換即顯示toolBox的開關
//NOTE:------------------------------------------------------------------------
function openOrCloseSubToolBox(obj){
	//console.log(obj);
	var toolboxid = obj.getAttribute('toolboxid');
		console.log(toolboxid);
	if($("#"+toolboxid+"_view").is(":hidden")){
		$("#subToolsMenu").attr("src", "img/mapButton_unexpand.png?1100315");
		$("#"+toolboxid+"_view").show();
	}else{
		$("#subToolsMenu").attr("src", "img/mapButton_list.png?1100315");
		$("#"+toolboxid+"_view").hide();
	}
}

//NOTE:------------------------------------------------------------------------
//NOTE: 切換即顯示MapTypNav的開關
//NOTE:------------------------------------------------------------------------
function openOrCloseMapTypNav(obj){
	if($("#mapTypNav_view").is(":hidden")){
		$("#mapTypNav_view").show();
	}
	else{
		$("#mapTypNav_view").hide();
	}
}

//------------------------------------------------------------------------
// 設定img物件圖片切換
//------------------------------------------------------------------------
function changeSrcFromImg(img) {
	var src = img.attr('src');
	if(src !== undefined)
	{
		if(src.indexOf("_on") != -1){
			src = src.replace("_on", "_off");
		}else{
			src = src.replace("_off", "_on");
		}
		img.attr("src", src);
	}
}

//------------------------------------------------------------------------
// 取得現在Btn的狀態
//------------------------------------------------------------------------
function isToolBtnCilcked(img) {
	var src = img.attr('src');
	if(src.indexOf("_on") != -1){
		return true;
	}else{
		return false;
	}
}

//------------------------------------------------------------------------
// 新增所有地圖Graphic
//------------------------------------------------------------------------
function ezekAddGraphic(point, symbol){
	
	require([
		"esri/graphic"
	],
	function(Graphic)
	{
		for(var i = 0 ; i < 8 ; i++){
			var graphic = new Graphic(point, symbol);
			
			switch (i) {
				case 0:
					//主地圖
					ezekMap.graphics.add(graphic);
					break;
				case 1:
					//二分格連續
					ExcisionMapInfo.mapDiv1_1.getMap().graphics.add(graphic);
					break;
				case 2:
					//二分格鏡射
					ExcisionMapInfo.mapDiv2_1.getMap().graphics.add(graphic);	
					break;
				case 3:
					//二分格鏡射
					ExcisionMapInfo.mapDiv2_2.getMap().graphics.add(graphic);
					break;	
				case 4:
					//四分格鏡射
					ExcisionMapInfo.mapDiv4_1.getMap().graphics.add(graphic);
					break;	
				case 5:
					//四分格鏡射
					ExcisionMapInfo.mapDiv4_2.getMap().graphics.add(graphic);
					break;	
				case 6:
					//四分格鏡射
					ExcisionMapInfo.mapDiv4_3.getMap().graphics.add(graphic);
					break;	
				case 7:
					//四分格鏡射
					ExcisionMapInfo.mapDiv4_4.getMap().graphics.add(graphic);
					break;			
			}
		}
		
	});	
}

//------------------------------------------------------------------------
// 清除所有地圖Graphic
//------------------------------------------------------------------------
function ezekClearGraphic(){
	//主地圖
	ezekMap.graphics.clear();
	//二分格連續
	ExcisionMapInfo.mapDiv1_1.getMap().graphics.clear();
	//二分格鏡射
	ExcisionMapInfo.mapDiv2_1.getMap().graphics.clear();
	ExcisionMapInfo.mapDiv2_2.getMap().graphics.clear();
	//四分格鏡射
	ExcisionMapInfo.mapDiv4_1.getMap().graphics.clear();
	ExcisionMapInfo.mapDiv4_2.getMap().graphics.clear();
	ExcisionMapInfo.mapDiv4_3.getMap().graphics.clear();
	ExcisionMapInfo.mapDiv4_4.getMap().graphics.clear();
}
