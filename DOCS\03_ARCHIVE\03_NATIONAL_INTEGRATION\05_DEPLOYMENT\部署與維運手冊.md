# 違章建築資料拋送至國土署系統 - 部署與維運手冊

## 文件資訊
- **文件版本**：1.0
- **建立日期**：2025-07-08
- **系統名稱**：違章建築資料同步服務
- **維護團隊**：新北市政府工務局資訊組

## 目錄
1. [系統架構概述](#系統架構概述)
2. [部署前檢查清單](#部署前檢查清單)
3. [部署步驟SOP](#部署步驟sop)
4. [系統配置管理](#系統配置管理)
5. [災難復原計畫](#災難復原計畫)
6. [日常維運指南](#日常維運指南)
7. [監控與告警](#監控與告警)
8. [故障排除手冊](#故障排除手冊)
9. [效能調校指南](#效能調校指南)
10. [安全管理規範](#安全管理規範)

## 系統架構概述

### 系統組成
```
┌─────────────────────────────────────────────────────────┐
│                   負載平衡器 (F5/Nginx)                   │
└─────────────────┬───────────────────┬───────────────────┘
                  │                   │
        ┌─────────┴─────────┐ ┌──────┴──────────┐
        │   Tomcat Node 1   │ │  Tomcat Node 2  │
        │  (Primary Server) │ │ (Backup Server)  │
        └─────────┬─────────┘ └──────┬──────────┘
                  │                   │
        ┌─────────┴─────────────────────┴────────┐
        │          PostgreSQL Cluster             │
        │    (Primary + Standby + Arbiter)       │
        └─────────────────────────────────────────┘
```

### 伺服器規格需求

| 角色 | CPU | RAM | 硬碟 | 網路 | OS |
|------|-----|-----|------|------|-----|
| 應用伺服器 | 8 Core | 16GB | 200GB SSD | 1Gbps | Ubuntu 20.04 LTS |
| 資料庫主機 | 16 Core | 32GB | 500GB SSD | 10Gbps | Ubuntu 20.04 LTS |
| 監控伺服器 | 4 Core | 8GB | 100GB SSD | 1Gbps | Ubuntu 20.04 LTS |

### 網路架構
```
Internet
    │
    ▼
防火牆 (允許 443/80)
    │
    ▼
DMZ區域
    ├── Web應用伺服器 (8080/8443)
    └── API Gateway (3000)
    │
內部網路
    ├── PostgreSQL (5432)
    ├── Redis Cache (6379)
    └── 監控系統 (9090)
```

## 部署前檢查清單

### 基礎環境檢查
- [ ] **作業系統更新**
  ```bash
  sudo apt update && sudo apt upgrade -y
  ```

- [ ] **必要軟體安裝**
  ```bash
  # Java 11
  sudo apt install openjdk-11-jdk -y
  java -version
  
  # PostgreSQL Client
  sudo apt install postgresql-client-12 -y
  
  # 監控工具
  sudo apt install htop iotop nethogs -y
  ```

- [ ] **系統參數調整**
  ```bash
  # /etc/sysctl.conf
  net.ipv4.tcp_max_syn_backlog = 65535
  net.core.somaxconn = 65535
  fs.file-max = 1000000
  vm.swappiness = 10
  
  # 套用設定
  sudo sysctl -p
  ```

- [ ] **使用者與權限**
  ```bash
  # 建立應用程式使用者
  sudo useradd -m -s /bin/bash tomcat
  sudo usermod -aG sudo tomcat
  
  # 設定目錄權限
  sudo mkdir -p /opt/tomcat
  sudo chown -R tomcat:tomcat /opt/tomcat
  ```

### 資料庫環境檢查
- [ ] **PostgreSQL安裝確認**
  ```bash
  psql --version
  # 應顯示: psql (PostgreSQL) 12.x
  ```

- [ ] **資料庫連線測試**
  ```bash
  PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -c "SELECT version();"
  ```

- [ ] **資料庫空間檢查**
  ```sql
  -- 檢查資料庫大小
  SELECT pg_database.datname,
         pg_size_pretty(pg_database_size(pg_database.datname)) AS size
  FROM pg_database
  WHERE datname = 'bms';
  
  -- 檢查表格大小
  SELECT schemaname,
         tablename,
         pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
  FROM pg_tables
  WHERE schemaname = 'public'
  ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
  LIMIT 10;
  ```

- [ ] **備份確認**
  ```bash
  # 執行完整備份
  pg_dump -h localhost -p 5432 -U postgres -d bms > bms_backup_$(date +%Y%m%d).sql
  ```

### 網路環境檢查
- [ ] **防火牆規則**
  ```bash
  # 檢查防火牆狀態
  sudo ufw status
  
  # 開放必要端口
  sudo ufw allow 8080/tcp  # Tomcat HTTP
  sudo ufw allow 8443/tcp  # Tomcat HTTPS
  sudo ufw allow 5432/tcp  # PostgreSQL
  ```

- [ ] **DNS解析測試**
  ```bash
  nslookup api.nlsc.gov.tw
  ping -c 4 api.nlsc.gov.tw
  ```

- [ ] **SSL憑證準備**
  ```bash
  # 檢查憑證
  openssl x509 -in /path/to/cert.crt -text -noout
  
  # 驗證憑證鏈
  openssl verify -CAfile /path/to/ca.crt /path/to/cert.crt
  ```

## 部署步驟SOP

### Step 1: 應用程式部署準備

#### 1.1 下載與解壓
```bash
# 切換到部署使用者
sudo su - tomcat

# 下載Tomcat
cd /opt
wget https://downloads.apache.org/tomcat/tomcat-9/v9.0.98/bin/apache-tomcat-9.0.98.tar.gz
tar -xzvf apache-tomcat-9.0.98.tar.gz
mv apache-tomcat-9.0.98 tomcat

# 設定環境變數
echo 'export CATALINA_HOME=/opt/tomcat' >> ~/.bashrc
echo 'export CATALINA_BASE=$CATALINA_HOME' >> ~/.bashrc
echo 'export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64' >> ~/.bashrc
source ~/.bashrc
```

#### 1.2 配置Tomcat
```bash
# 設定記憶體參數
cat > $CATALINA_HOME/bin/setenv.sh << 'EOF'
#!/bin/bash
JAVA_OPTS="-Xms2048m -Xmx4096m -XX:MaxMetaspaceSize=512m"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=$CATALINA_HOME/logs"
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/urandom"
export JAVA_OPTS
EOF

chmod +x $CATALINA_HOME/bin/setenv.sh
```

#### 1.3 部署應用程式
```bash
# 清理舊版本
rm -rf $CATALINA_HOME/webapps/ROOT/*

# 部署新版本
cd $CATALINA_HOME/webapps/ROOT
unzip /tmp/bms-sync-service.war

# 複製相依函式庫
cp /path/to/postgresql-42.2.24.jar $CATALINA_HOME/lib/
cp /path/to/jtds-1.3.1.jar $CATALINA_HOME/lib/
```

### Step 2: 資料庫部署

#### 2.1 建立同步服務資料庫
```sql
-- 連線到PostgreSQL
psql -U postgres

-- 建立資料庫
CREATE DATABASE bms_sync WITH ENCODING 'UTF8' LC_COLLATE 'zh_TW.UTF-8' LC_CTYPE 'zh_TW.UTF-8';

-- 建立使用者
CREATE USER sync_user WITH PASSWORD '*************';
GRANT ALL PRIVILEGES ON DATABASE bms_sync TO sync_user;

-- 切換到新資料庫
\c bms_sync

-- 執行schema建立腳本
\i /path/to/create_schema.sql

-- 建立初始資料
\i /path/to/init_data.sql

-- 驗證
\dt
```

#### 2.2 設定資料庫連線
```xml
<!-- $CATALINA_HOME/conf/context.xml -->
<Context>
    <!-- 主要業務資料庫 -->
    <Resource name="jdbc/bmsDB"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="org.postgresql.Driver"
              url="************************************"
              username="postgres"
              password="${DB_PASSWORD}"
              maxActive="80"
              maxIdle="20"
              minIdle="10"
              maxWait="10000"
              validationQuery="SELECT 1"
              testOnBorrow="true"
              testWhileIdle="true"
              timeBetweenEvictionRunsMillis="30000"
              minEvictableIdleTimeMillis="60000"
              removeAbandoned="true"
              removeAbandonedTimeout="60"
              logAbandoned="true"/>
    
    <!-- 同步服務資料庫 -->
    <Resource name="jdbc/syncDB"
              auth="Container"
              type="javax.sql.DataSource"
              factory="org.apache.tomcat.jdbc.pool.DataSourceFactory"
              driverClassName="org.postgresql.Driver"
              url="*****************************************"
              username="sync_user"
              password="${SYNC_DB_PASSWORD}"
              maxActive="50"
              maxIdle="10"
              minIdle="5"
              maxWait="10000"
              validationQuery="SELECT 1"
              testOnBorrow="true"/>
</Context>
```

### Step 3: Windows Service部署（同步服務）

#### 3.1 安裝.NET服務
```powershell
# 以管理員身份執行PowerShell

# 建立服務目錄
New-Item -ItemType Directory -Path "C:\Services\ViolationSync" -Force

# 複製執行檔
Copy-Item -Path ".\bin\Release\net8.0\publish\*" -Destination "C:\Services\ViolationSync\" -Recurse

# 安裝服務
New-Service -Name "ViolationDataSyncService" `
    -BinaryPathName "C:\Services\ViolationSync\ViolationDataSyncService.exe" `
    -DisplayName "違章建築資料同步服務" `
    -Description "負責將違章建築案件資料同步至國土署系統" `
    -StartupType Automatic

# 設定服務復原選項
sc.exe failure ViolationDataSyncService reset= 86400 actions= restart/60000/restart/60000/restart/60000
```

#### 3.2 配置服務設定
```json
// C:\Services\ViolationSync\appsettings.json
{
  "ServiceConfig": {
    "ServiceName": "ViolationDataSyncService",
    "DisplayName": "違章建築資料同步服務",
    "Description": "負責將違章建築案件資料同步至國土署系統"
  },
  "ConnectionStrings": {
    "PostgreSQL": "Host=localhost;Port=5432;Database=bms_sync;Username=sync_user;Password=*************;Include Error Detail=true"
  },
  "SyncSettings": {
    "BatchSize": 50,
    "IntervalMinutes": 30,
    "MaxConcurrentTasks": 5,
    "TimeoutSeconds": 300,
    "RetryDelayMinutes": 5
  },
  "NationalAPI": {
    "BaseUrl": "https://api.nlsc.gov.tw/violation",
    "ApiKey": "%NATIONAL_API_KEY%",
    "RetryPolicy": {
      "MaxRetryCount": 3,
      "BaseDelaySeconds": 60
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "System": "Warning"
    },
    "EventLog": {
      "SourceName": "ViolationDataSyncService"
    }
  }
}
```

### Step 4: 啟動與驗證

#### 4.1 啟動Tomcat
```bash
# 啟動服務
$CATALINA_HOME/bin/startup.sh

# 監看啟動日誌
tail -f $CATALINA_HOME/logs/catalina.out

# 檢查服務狀態
ps aux | grep tomcat
netstat -tlnp | grep 8080
```

#### 4.2 啟動同步服務
```powershell
# 啟動Windows服務
Start-Service -Name "ViolationDataSyncService"

# 檢查服務狀態
Get-Service -Name "ViolationDataSyncService"

# 查看事件日誌
Get-EventLog -LogName Application -Source "ViolationDataSyncService" -Newest 10
```

#### 4.3 健康檢查
```bash
# 應用程式健康檢查
curl http://localhost:8080/health

# 同步服務健康檢查
curl http://localhost:8081/health

# 資料庫連線檢查
psql -h localhost -p 5432 -U sync_user -d bms_sync -c "SELECT COUNT(*) FROM sync_queue;"
```

### Step 5: 部署後設定

#### 5.1 設定反向代理
```nginx
# /etc/nginx/sites-available/bms-sync
server {
    listen 443 ssl http2;
    server_name sync.bms.ntpc.gov.tw;
    
    ssl_certificate /etc/ssl/certs/bms.crt;
    ssl_certificate_key /etc/ssl/private/bms.key;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 連線逾時設定
        proxy_connect_timeout 300;
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }
    
    location /sync-api {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 5.2 設定日誌輪替
```bash
# /etc/logrotate.d/tomcat
/opt/tomcat/logs/catalina.out {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0644 tomcat tomcat
    sharedscripts
    postrotate
        /bin/kill -USR1 `cat /opt/tomcat/logs/tomcat.pid 2>/dev/null` 2>/dev/null || true
    endscript
}
```

## 系統配置管理

### 環境變數管理
```bash
# /etc/environment
JAVA_HOME="/usr/lib/jvm/java-11-openjdk-amd64"
CATALINA_HOME="/opt/tomcat"
DB_PASSWORD="使用Vault或其他密鑰管理系統"
SYNC_DB_PASSWORD="使用Vault或其他密鑰管理系統"
NATIONAL_API_KEY="使用Vault或其他密鑰管理系統"
```

### 配置檔版本控制
```bash
# 初始化Git倉庫
cd /opt/tomcat/conf
git init
git add .
git commit -m "Initial configuration"

# 建立配置備份
tar -czf /backup/tomcat-conf-$(date +%Y%m%d).tar.gz /opt/tomcat/conf/
```

### 敏感資訊管理
```bash
# 使用HashiCorp Vault
vault kv put secret/bms/database \
    username=postgres \
    password=S!@h@202203

# 在應用程式中讀取
vault kv get -field=password secret/bms/database
```

## 災難復原計畫

### 復原時間目標（RTO/RPO）
- **RTO（Recovery Time Objective）**：4小時
- **RPO（Recovery Point Objective）**：30分鐘

### 備份策略

#### 資料庫備份
```bash
#!/bin/bash
# /opt/scripts/backup_database.sh

# 設定變數
BACKUP_DIR="/backup/postgresql"
DB_NAME="bms"
SYNC_DB_NAME="bms_sync"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=30

# 建立備份目錄
mkdir -p $BACKUP_DIR

# 執行備份
echo "開始備份主資料庫..."
pg_dump -h localhost -U postgres -d $DB_NAME -Fc > $BACKUP_DIR/${DB_NAME}_${DATE}.dump

echo "開始備份同步資料庫..."
pg_dump -h localhost -U postgres -d $SYNC_DB_NAME -Fc > $BACKUP_DIR/${SYNC_DB_NAME}_${DATE}.dump

# 壓縮備份
tar -czf $BACKUP_DIR/backup_${DATE}.tar.gz $BACKUP_DIR/*_${DATE}.dump

# 清理個別檔案
rm -f $BACKUP_DIR/*_${DATE}.dump

# 清理舊備份
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +$RETENTION_DAYS -delete

echo "備份完成: backup_${DATE}.tar.gz"
```

#### 應用程式備份
```bash
#!/bin/bash
# /opt/scripts/backup_application.sh

BACKUP_DIR="/backup/application"
APP_DIR="/opt/tomcat/webapps"
CONF_DIR="/opt/tomcat/conf"
DATE=$(date +%Y%m%d_%H%M%S)

# 建立備份目錄
mkdir -p $BACKUP_DIR

# 備份應用程式
tar -czf $BACKUP_DIR/app_${DATE}.tar.gz -C $APP_DIR .

# 備份配置
tar -czf $BACKUP_DIR/conf_${DATE}.tar.gz -C $CONF_DIR .

# 建立完整備份
tar -czf $BACKUP_DIR/full_backup_${DATE}.tar.gz \
    $BACKUP_DIR/app_${DATE}.tar.gz \
    $BACKUP_DIR/conf_${DATE}.tar.gz

# 清理個別檔案
rm -f $BACKUP_DIR/app_${DATE}.tar.gz
rm -f $BACKUP_DIR/conf_${DATE}.tar.gz
```

### 災難復原程序

#### 場景1：應用程式故障
```bash
# 1. 停止故障服務
$CATALINA_HOME/bin/shutdown.sh

# 2. 備份當前狀態
mv $CATALINA_HOME/webapps/ROOT $CATALINA_HOME/webapps/ROOT.failed

# 3. 復原最近備份
cd $CATALINA_HOME/webapps
tar -xzf /backup/application/app_latest.tar.gz

# 4. 重啟服務
$CATALINA_HOME/bin/startup.sh

# 5. 驗證服務
curl http://localhost:8080/health
```

#### 場景2：資料庫損毀
```bash
# 1. 停止應用服務
$CATALINA_HOME/bin/shutdown.sh
Stop-Service -Name "ViolationDataSyncService"

# 2. 備份損毀資料庫
pg_dump -h localhost -U postgres -d bms > /tmp/bms_corrupted.sql

# 3. 重建資料庫
dropdb -h localhost -U postgres bms
createdb -h localhost -U postgres bms

# 4. 復原備份
pg_restore -h localhost -U postgres -d bms /backup/postgresql/bms_latest.dump

# 5. 驗證資料完整性
psql -h localhost -U postgres -d bms -c "SELECT COUNT(*) FROM buildcase;"

# 6. 重啟服務
$CATALINA_HOME/bin/startup.sh
Start-Service -Name "ViolationDataSyncService"
```

#### 場景3：完整系統復原
```bash
# 1. 準備新環境
# 依照部署步驟準備新伺服器

# 2. 復原資料庫
pg_restore -h localhost -U postgres -C -d postgres /backup/postgresql/bms_latest.dump
pg_restore -h localhost -U postgres -C -d postgres /backup/postgresql/bms_sync_latest.dump

# 3. 復原應用程式
cd /opt/tomcat
tar -xzf /backup/application/full_backup_latest.tar.gz

# 4. 復原配置
cd /opt/tomcat/conf
tar -xzf /backup/conf_latest.tar.gz

# 5. 啟動服務
$CATALINA_HOME/bin/startup.sh
systemctl start postgresql
Start-Service -Name "ViolationDataSyncService"

# 6. 驗證系統
/opt/scripts/health_check_all.sh
```

### 災難演練計畫
- **頻率**：每季一次
- **範圍**：輪流測試不同場景
- **參與人員**：系統管理員、DBA、開發人員
- **演練時間**：非營業時間

## 日常維運指南

### 每日檢查項目

#### 系統健康檢查
```bash
#!/bin/bash
# /opt/scripts/daily_health_check.sh

echo "=== 每日健康檢查報告 $(date) ==="

# 檢查服務狀態
echo "1. 服務狀態檢查"
systemctl status tomcat
systemctl status postgresql

# 檢查磁碟使用
echo "2. 磁碟使用狀況"
df -h | grep -E '^/dev/'

# 檢查記憶體使用
echo "3. 記憶體使用狀況"
free -m

# 檢查資料庫連線
echo "4. 資料庫連線數"
psql -h localhost -U postgres -d bms -c "SELECT count(*) FROM pg_stat_activity;"

# 檢查同步佇列
echo "5. 同步佇列狀態"
psql -h localhost -U sync_user -d bms_sync -c "
SELECT status, COUNT(*) 
FROM sync_queue 
WHERE created_at >= CURRENT_DATE 
GROUP BY status;"

# 檢查錯誤日誌
echo "6. 錯誤日誌檢查"
grep -i error $CATALINA_HOME/logs/catalina.out | tail -20
```

#### 效能指標收集
```sql
-- 資料庫效能指標
SELECT 
    datname,
    numbackends as 連線數,
    xact_commit as 交易提交數,
    xact_rollback as 交易回滾數,
    blks_read as 磁碟讀取,
    blks_hit as 快取命中,
    tup_returned as 回傳筆數,
    tup_fetched as 擷取筆數,
    tup_inserted as 新增筆數,
    tup_updated as 更新筆數,
    tup_deleted as 刪除筆數
FROM pg_stat_database
WHERE datname IN ('bms', 'bms_sync');
```

### 每週維護作業

#### 日誌清理
```bash
#!/bin/bash
# /opt/scripts/weekly_cleanup.sh

# 清理Tomcat日誌
find $CATALINA_HOME/logs -name "*.log" -mtime +7 -delete
find $CATALINA_HOME/logs -name "*.txt" -mtime +7 -delete

# 壓縮舊日誌
find $CATALINA_HOME/logs -name "catalina.out.*" -mtime +1 -exec gzip {} \;

# 清理臨時檔案
find /tmp -name "tomcat*" -mtime +1 -delete
```

#### 資料庫維護
```sql
-- 更新統計資訊
ANALYZE buildcase;
ANALYZE tbflow;
ANALYZE sync_queue;
ANALYZE sync_history;

-- 重建索引（如需要）
REINDEX INDEX CONCURRENTLY idx_buildcase_caseopened;
REINDEX INDEX CONCURRENTLY idx_tbflow_dates;

-- 清理過期資料
DELETE FROM sync_history WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
DELETE FROM audit_log WHERE changed_at < CURRENT_DATE - INTERVAL '180 days';

-- VACUUM清理
VACUUM ANALYZE;
```

### 每月維護作業

#### 安全更新
```bash
# 檢查系統更新
sudo apt update
sudo apt list --upgradable

# 檢查Java更新
java -version

# 檢查Tomcat更新
# 訪問 https://tomcat.apache.org/download-90.cgi
```

#### 效能報告
```bash
#!/bin/bash
# /opt/scripts/monthly_performance_report.sh

REPORT_DIR="/opt/reports"
MONTH=$(date +%Y%m)

mkdir -p $REPORT_DIR

# 產生效能報告
echo "# 月度效能報告 - $MONTH" > $REPORT_DIR/performance_$MONTH.md

# 系統資源使用
echo "## 系統資源使用趨勢" >> $REPORT_DIR/performance_$MONTH.md
sar -u -f /var/log/sysstat/sa$(date +%d) >> $REPORT_DIR/performance_$MONTH.md

# 資料庫效能
echo "## 資料庫效能指標" >> $REPORT_DIR/performance_$MONTH.md
psql -h localhost -U postgres -d bms -c "
SELECT 
    DATE(created_at) as date,
    COUNT(*) as sync_count,
    AVG(execution_time) as avg_time
FROM sync_history
WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY DATE(created_at)
ORDER BY date;" >> $REPORT_DIR/performance_$MONTH.md
```

## 監控與告警

### 監控架構
```
┌─────────────────────────────────────────────────┐
│                 Grafana Dashboard                │
└─────────────────┬───────────────────────────────┘
                  │
┌─────────────────┴───────────────────────────────┐
│                  Prometheus                      │
└────┬──────────┬──────────┬──────────┬──────────┘
     │          │          │          │
┌────┴────┐ ┌──┴────┐ ┌──┴────┐ ┌───┴─────┐
│  Node   │ │Tomcat │ │ PG    │ │ Custom  │
│Exporter │ │  JMX  │ │Export │ │ Metrics │
└─────────┘ └───────┘ └───────┘ └─────────┘
```

### Prometheus配置
```yaml
# /etc/prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
        labels:
          instance: 'bms-app-server'
  
  - job_name: 'tomcat'
    static_configs:
      - targets: ['localhost:8088']
        labels:
          instance: 'tomcat-main'
  
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']
        labels:
          instance: 'postgres-main'
  
  - job_name: 'sync-service'
    static_configs:
      - targets: ['localhost:8081/metrics']
        labels:
          instance: 'sync-service'

rule_files:
  - "alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['localhost:9093']
```

### 告警規則
```yaml
# /etc/prometheus/alerts.yml
groups:
  - name: system_alerts
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg(rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率過高"
          description: "CPU使用率已超過80%，當前值: {{ $value }}%"
      
      # 記憶體使用告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "記憶體使用率過高"
          description: "記憶體使用率已超過85%，當前值: {{ $value }}%"
      
      # 磁碟空間告警
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) * 100 < 20
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁碟空間不足"
          description: "根目錄可用空間低於20%，剩餘: {{ $value }}%"
  
  - name: application_alerts
    rules:
      # Tomcat回應時間
      - alert: SlowResponse
        expr: http_request_duration_seconds{quantile="0.95"} > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "應用程式回應緩慢"
          description: "95%的請求回應時間超過5秒"
      
      # 資料庫連線池
      - alert: DatabaseConnectionPoolExhausted
        expr: tomcat_jdbc_connections_active / tomcat_jdbc_connections_max * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "資料庫連線池即將耗盡"
          description: "連線池使用率: {{ $value }}%"
      
      # 同步失敗率
      - alert: HighSyncFailureRate
        expr: rate(sync_failures_total[5m]) > 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "同步失敗率過高"
          description: "每秒失敗率: {{ $value }}"
```

### 告警通知配置
```yaml
# /etc/alertmanager/alertmanager.yml
global:
  resolve_timeout: 5m
  smtp_from: '<EMAIL>'
  smtp_smarthost: 'smtp.ntpc.gov.tw:587'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'severity']
  group_wait: 10s
  group_interval: 5m
  repeat_interval: 1h
  receiver: 'email-notifications'
  
  routes:
    - match:
        severity: critical
      receiver: 'critical-notifications'
      continue: true
    
    - match:
        severity: warning
      receiver: 'warning-notifications'

receivers:
  - name: 'email-notifications'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: '[BMS告警] {{ .GroupLabels.alertname }}'
  
  - name: 'critical-notifications'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: '[緊急] BMS系統告警 - {{ .GroupLabels.alertname }}'
    webhook_configs:
      - url: 'http://sms-gateway/send'
        send_resolved: true
  
  - name: 'warning-notifications'
    email_configs:
      - to: '<EMAIL>'
```

### 自訂監控指標
```csharp
// 同步服務監控指標
public class MetricsService
{
    private readonly Counter _syncCounter;
    private readonly Histogram _syncDuration;
    private readonly Gauge _queueSize;
    
    public MetricsService()
    {
        _syncCounter = Metrics.CreateCounter(
            "sync_total", 
            "Total number of sync operations",
            new CounterConfiguration 
            { 
                LabelNames = new[] { "status", "operation" } 
            });
        
        _syncDuration = Metrics.CreateHistogram(
            "sync_duration_seconds",
            "Sync operation duration",
            new HistogramConfiguration
            {
                Buckets = Histogram.LinearBuckets(0.1, 0.1, 10)
            });
        
        _queueSize = Metrics.CreateGauge(
            "sync_queue_size",
            "Current sync queue size");
    }
    
    public void RecordSync(string status, string operation, double duration)
    {
        _syncCounter.WithLabels(status, operation).Inc();
        _syncDuration.Observe(duration);
    }
    
    public void UpdateQueueSize(int size)
    {
        _queueSize.Set(size);
    }
}
```

## 故障排除手冊

### 常見問題與解決方案

#### 問題1：Tomcat無法啟動
```bash
# 症狀：執行startup.sh後服務未啟動

# 檢查步驟：
1. 檢查Java環境
   java -version
   echo $JAVA_HOME

2. 檢查端口佔用
   netstat -tlnp | grep 8080
   
3. 檢查日誌
   tail -100 $CATALINA_HOME/logs/catalina.out
   
4. 檢查權限
   ls -la $CATALINA_HOME/bin/
   
# 解決方案：
# 如果是端口被佔用
sudo kill -9 $(lsof -t -i:8080)

# 如果是權限問題
sudo chown -R tomcat:tomcat $CATALINA_HOME
chmod +x $CATALINA_HOME/bin/*.sh

# 如果是記憶體不足
# 調整 setenv.sh 中的 JAVA_OPTS
```

#### 問題2：資料庫連線失敗
```sql
-- 症狀：應用程式報告無法連線到資料庫

-- 檢查步驟：
-- 1. 測試連線
psql -h localhost -p 5432 -U postgres -d bms

-- 2. 檢查服務狀態
systemctl status postgresql

-- 3. 檢查連線數
SELECT count(*) FROM pg_stat_activity;

-- 4. 檢查連線限制
SHOW max_connections;

-- 解決方案：
-- 如果連線數過多
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname = 'bms' 
  AND state = 'idle' 
  AND state_change < CURRENT_TIMESTAMP - INTERVAL '1 hour';

-- 調整連線數限制
-- 編輯 postgresql.conf
-- max_connections = 200
-- 重啟PostgreSQL
```

#### 問題3：同步服務異常
```powershell
# 症狀：同步服務停止運作或同步失敗

# 檢查步驟：
# 1. 檢查服務狀態
Get-Service -Name "ViolationDataSyncService"

# 2. 檢查事件日誌
Get-EventLog -LogName Application -Source "ViolationDataSyncService" -Newest 50

# 3. 檢查同步佇列
psql -h localhost -U sync_user -d bms_sync -c "
SELECT status, COUNT(*), MAX(retry_count) 
FROM sync_queue 
GROUP BY status;"

# 解決方案：
# 重啟服務
Restart-Service -Name "ViolationDataSyncService"

# 清理失敗記錄
psql -h localhost -U sync_user -d bms_sync -c "
UPDATE sync_queue 
SET status = 'pending', retry_count = 0 
WHERE status = 'failed' AND retry_count >= 3;"
```

#### 問題4：API逾時
```bash
# 症狀：國土署API呼叫逾時

# 檢查步驟：
1. 測試網路連通性
   ping api.nlsc.gov.tw
   curl -I https://api.nlsc.gov.tw/violation

2. 檢查DNS解析
   nslookup api.nlsc.gov.tw

3. 檢查防火牆
   sudo iptables -L -n

# 解決方案：
# 調整逾時設定
# 在 appsettings.json 中增加 TimeoutSeconds

# 使用代理伺服器
export https_proxy=http://proxy.ntpc.gov.tw:8080
```

### 緊急處理程序

#### 系統完全失效
1. **立即通知**
   - 通知主管與相關人員
   - 記錄故障開始時間

2. **初步診斷**（5分鐘內）
   ```bash
   # 快速健康檢查
   /opt/scripts/emergency_check.sh
   ```

3. **嘗試快速復原**（15分鐘內）
   ```bash
   # 重啟所有服務
   /opt/scripts/restart_all_services.sh
   ```

4. **切換到備援系統**（30分鐘內）
   - 啟動備援伺服器
   - 更新DNS指向
   - 驗證服務可用性

5. **根本原因分析**
   - 收集所有日誌
   - 分析故障原因
   - 制定修復計畫

#### 資料不一致
1. **停止同步服務**
   ```powershell
   Stop-Service -Name "ViolationDataSyncService"
   ```

2. **檢查資料差異**
   ```sql
   -- 比對本地與遠端資料
   SELECT COUNT(*) FROM sync_queue WHERE status != 'completed';
   ```

3. **執行資料修復**
   ```bash
   # 執行資料一致性檢查
   /opt/scripts/data_consistency_check.sh
   ```

4. **重新同步**
   ```sql
   -- 重置失敗的同步任務
   UPDATE sync_queue 
   SET status = 'pending', retry_count = 0 
   WHERE status = 'failed';
   ```

## 效能調校指南

### JVM調校

#### 記憶體配置優化
```bash
# setenv.sh 優化設定
JAVA_OPTS="-server"
JAVA_OPTS="$JAVA_OPTS -Xms4096m -Xmx4096m"  # 固定堆積大小
JAVA_OPTS="$JAVA_OPTS -XX:MaxMetaspaceSize=512m"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"  # 使用G1垃圾收集器
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=8"
JAVA_OPTS="$JAVA_OPTS -XX:ConcGCThreads=2"
JAVA_OPTS="$JAVA_OPTS -XX:InitiatingHeapOccupancyPercent=70"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=$CATALINA_HOME/logs/heapdump.hprof"
```

#### GC日誌分析
```bash
# 啟用GC日誌
JAVA_OPTS="$JAVA_OPTS -Xlog:gc*:file=$CATALINA_HOME/logs/gc.log:time,tags:filecount=10,filesize=10M"

# 分析GC日誌
java -jar gcviewer.jar $CATALINA_HOME/logs/gc.log
```

### 資料庫調校

#### PostgreSQL參數優化
```ini
# postgresql.conf 優化設定
# 記憶體設定
shared_buffers = 8GB              # 25% of RAM
effective_cache_size = 24GB       # 75% of RAM
maintenance_work_mem = 2GB
work_mem = 50MB

# 連線設定
max_connections = 200
max_prepared_transactions = 100

# 查詢優化
random_page_cost = 1.1           # SSD優化
effective_io_concurrency = 200   # SSD優化
default_statistics_target = 100

# WAL設定
wal_buffers = 16MB
checkpoint_timeout = 15min
checkpoint_completion_target = 0.9
max_wal_size = 4GB
min_wal_size = 1GB

# 平行查詢
max_parallel_workers_per_gather = 4
max_parallel_workers = 8
```

#### 查詢優化
```sql
-- 找出慢查詢
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time
FROM pg_stat_statements
WHERE mean_time > 1000  -- 超過1秒
ORDER BY mean_time DESC
LIMIT 20;

-- 分析查詢計畫
EXPLAIN (ANALYZE, BUFFERS) 
SELECT b.*, t.flow_desc
FROM buildcase b
JOIN tbflow t ON b.case_no = t.case_no
WHERE b.caseopened = '231'
  AND b.create_date >= CURRENT_DATE - INTERVAL '30 days';

-- 建立必要索引
CREATE INDEX CONCURRENTLY idx_buildcase_create_date 
ON buildcase(create_date) 
WHERE caseopened IN ('231', '232', '233');
```

### 網路優化

#### Tomcat連接器調校
```xml
<!-- server.xml -->
<Connector port="8080" protocol="org.apache.coyote.http11.Http11Nio2Protocol"
           connectionTimeout="20000"
           maxThreads="400"
           minSpareThreads="25"
           maxConnections="10000"
           acceptCount="100"
           enableLookups="false"
           compression="on"
           compressionMinSize="2048"
           noCompressionUserAgents="gozilla, traviata"
           compressableMimeType="text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json"
           URIEncoding="UTF-8"/>
```

#### 系統核心參數
```bash
# /etc/sysctl.conf
# 網路優化
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_max_tw_buckets = 2000000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_intvl = 30
net.ipv4.tcp_keepalive_probes = 3

# 檔案系統
fs.file-max = 1000000
fs.nr_open = 1000000

# 套用設定
sysctl -p
```

## 安全管理規範

### 存取控制

#### 系統帳號管理
```bash
# 建立專用帳號
useradd -m -s /bin/bash -c "Tomcat Service Account" tomcat
useradd -m -s /bin/bash -c "Database Admin" dbadmin

# 設定sudo權限
echo "tomcat ALL=(ALL) NOPASSWD: /bin/systemctl restart tomcat" >> /etc/sudoers.d/tomcat
echo "dbadmin ALL=(ALL) NOPASSWD: /bin/systemctl restart postgresql" >> /etc/sudoers.d/dbadmin

# 密碼政策
chage -M 90 -m 7 -W 14 tomcat  # 90天過期，7天才能改，14天前警告
```

#### 檔案權限設定
```bash
# Tomcat目錄權限
chown -R tomcat:tomcat $CATALINA_HOME
chmod 750 $CATALINA_HOME/bin
chmod 640 $CATALINA_HOME/conf/*
chmod 750 $CATALINA_HOME/webapps
chmod 750 $CATALINA_HOME/logs

# 設定檔權限
chmod 600 /opt/config/database.properties
chmod 600 /opt/config/api.keys
```

### 網路安全

#### 防火牆規則
```bash
# UFW設定
ufw default deny incoming
ufw default allow outgoing

# 允許SSH（限制來源）
ufw allow from 10.0.0.0/8 to any port 22

# 允許HTTP/HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# 允許資料庫（僅內部）
ufw allow from 10.0.0.0/8 to any port 5432

# 啟用防火牆
ufw enable
```

#### SSL/TLS配置
```xml
<!-- server.xml HTTPS設定 -->
<Connector port="8443" protocol="org.apache.coyote.http11.Http11NioProtocol"
           maxThreads="150" SSLEnabled="true">
    <SSLHostConfig>
        <Certificate certificateKeystoreFile="/opt/ssl/keystore.jks"
                     certificateKeystorePassword="${SSL_KEYSTORE_PASSWORD}"
                     type="RSA" />
        <Protocols>TLSv1.2,TLSv1.3</Protocols>
        <Ciphers>TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
                 TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
                 TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,
                 TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256</Ciphers>
    </SSLHostConfig>
</Connector>
```

### 稽核與日誌

#### 稽核日誌配置
```xml
<!-- 啟用存取日誌 -->
<Valve className="org.apache.catalina.valves.AccessLogValve" 
       directory="logs"
       prefix="access_log"
       suffix=".txt"
       pattern="%h %l %u %t &quot;%r&quot; %s %b %D %{User-Agent}i"
       rotatable="true"
       renameOnRotate="true"/>
```

#### 安全事件監控
```bash
#!/bin/bash
# /opt/scripts/security_monitor.sh

# 監控可疑登入
echo "=== 可疑登入活動 ==="
grep "Failed password" /var/log/auth.log | tail -20

# 監控檔案變更
echo "=== 設定檔變更 ==="
find $CATALINA_HOME/conf -type f -mtime -1 -ls

# 監控異常連線
echo "=== 異常網路連線 ==="
netstat -an | grep ESTABLISHED | awk '{print $5}' | cut -d: -f1 | sort | uniq -c | sort -n | tail -10
```

### 定期安全檢查

#### 弱點掃描
```bash
# 使用OWASP ZAP進行安全掃描
docker run -t owasp/zap2docker-stable zap-baseline.py \
    -t https://sync.bms.ntpc.gov.tw \
    -r security_scan_$(date +%Y%m%d).html
```

#### 相依套件檢查
```bash
# 檢查Java相依套件
mvn dependency-check:check

# 檢查系統套件
apt list --upgradable | grep -i security
```

## 附錄

### 重要聯絡資訊

| 角色 | 姓名 | 電話 | Email | 備註 |
|------|------|------|-------|------|
| 系統管理員 | 王小明 | 02-2960-XXXX | <EMAIL> | 主要負責人 |
| 資料庫管理員 | 李小華 | 02-2960-YYYY | <EMAIL> | PostgreSQL專家 |
| 開發團隊主管 | 張經理 | 02-2960-ZZZZ | <EMAIL> | 緊急決策 |
| 廠商窗口 | 陳先生 | 0912-XXX-YYY | <EMAIL> | 24小時支援 |

### 相關文件連結
- [系統架構文件](./SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫設計文件](./DATABASE_COMPLETE_GUIDE.md)
- [API規格文件](./API_SPECIFICATION.md)
- [測試計畫書](./測試計畫書.md)

### 版本更新記錄

| 版本 | 日期 | 修改內容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-07-08 | 初版發布 | Claude Code |
| | | | |

---

本手冊為違章建築資料拋送至國土署系統的完整部署與維運指南，涵蓋從部署準備到日常維運的所有面向。請定期更新本文件以反映系統的最新狀態。