var markerPic;
var WGS84_point, XY_point;
var gsvc, wgs84SR, twd97SR, webMercatorSR;
var currentCoordinate;
var addressMarker, graphic;
var showText, graphic_text, containText;
var geocoder;
var ezekMap;									//Google Geocoder
var sys_arcgisMapServiceURL = location.protocol + "//limit.ntpc.gov.tw/arcgis/rest/services/";
// 新北測試 token
//var token="rJzVxzRxWwAtW_nmhwMqpNqiWfehMOTiQx8MPL1JUkbrWJRuCz-n8uCADya4EZLD";
//var token = "AzS0XMKer0W7BBXaHWd5gICJxZ7ggrXiFZ49OV8i5Qfr0DIdS-u0Pkw3eV0ABo61";
//var token = "gwQ-sLeHW7LEBLhRhcVxOT4Xw_RbmpwqaNhNm6h4NdAzRvclvDCJUR2bZsB5Oqwj";202305失效
//var token = "447oNhHjoHa57PAsmntmpqhSvdOItoqELmctHXufN0aU0QVW4__vAcc1liVnAsEs";
var token = "17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token=" + token,
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token=" + token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer",
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token=" + token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token=" + token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";
var orthoimageName = "正射影像圖";
var scopeLayerName = "行政區域圖";
var landLayerName = "地籍圖";
var bcmsLayerName = "地籍套繪圖";
function ezek_InitMap() {
    require([
        "esri/map",                          //  1. Map
        "esri/dijit/Print",                  //  2. Print
        "esri/geometry/Point",               //  3. Point
        "esri/geometry/Polyline",            //  4. Polyline
        "esri/geometry/webMercatorUtils",    //  5. WebMercatorUtils
        "esri/graphic",                      //  6. Graphic
        "esri/Color",                        //  7. Color
        "esri/symbols/PictureMarkerSymbol",  //  8. PictureMarkerSymbol
        "esri/symbols/SimpleMarkerSymbol",   //  9. SimpleMarkerSymbol
        "esri/symbols/SimpleLineSymbol",     // 10. SimpleLineSymbol
        "esri/symbols/TextSymbol",           // 11. TextSymbol
        "esri/symbols/Font",                 // 12. Font
        "esri/toolbars/edit",                // 13. Edit
        "esri/tasks/PrintTemplate",          // 14. PrintTemplate
        "esri/tasks/GeometryService",        // 15. GeometryService
        "esri/SpatialReference",             // 16. SpatialReference
        "esri/dijit/Search",                 // 17. Search
        "dojo/_base/event",                  // 18. event
        "dojo/dom",                          // 19. dom
        "dojo/on",                           // 20. on
        "dojo/parser",                       // 21. parser
        "dojo/domReady!"                     // 22. （無對應參數）
    ], function (
        Map,                 // matches "esri/map"
        Print,               // matches "esri/dijit/Print"
        Point,               // matches "esri/geometry/Point"
        Polyline,            // matches "esri/geometry/Polyline"
        WebMercatorUtils,    // matches "esri/geometry/webMercatorUtils"
        Graphic,             // matches "esri/graphic"
        Color,               // matches "esri/Color"
        PictureMarkerSymbol, // matches "esri/symbols/PictureMarkerSymbol"
        SimpleMarkerSymbol,  // matches "esri/symbols/SimpleMarkerSymbol"
        SimpleLineSymbol,    // matches "esri/symbols/SimpleLineSymbol"
        TextSymbol,          // matches "esri/symbols/TextSymbol"
        Font,                // matches "esri/symbols/Font"
        Edit,                // matches "esri/toolbars/edit"
        PrintTemplate,       // matches "esri/tasks/PrintTemplate"
        GeometryService,     // matches "esri/tasks/GeometryService"
        SpatialReference,    // matches "esri/SpatialReference"
        Search,              // matches "esri/dijit/Search"
        event,               // matches "dojo/_base/event"
        dom,                 // matches "dojo/dom"
        on,                  // matches "dojo/on"
        parser               // matches "dojo/parser"
    ) {
        parser.parse();

        var addrToal = decodeURI(getUrlParameter("ADDR"));
        var WGS84_point = new Point();
        showText = new TextSymbol(" ").setColor(new Color([128, 0, 0])).setAlign(Font.ALIGN_START).setFont(new Font("24pt")).setOffset(0, 60);
        markerPic = new PictureMarkerSymbol('https://icdc.ntpc.gov.tw/ntpcin/img/focus.png?1071030', 30, 30);

        esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";

        //設定座標轉換服務
        var sys_arcgisMapService = location.protocol + "//icdc.ntpc.gov.tw/arcgis/rest/services/";
        gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");

        wgs84SR = new SpatialReference({ wkid: 4326 }); //WGS84
        twd97SR = new SpatialReference({ wkid: 102443 }); //TWD_97_TM2
        webMercatorSR = new SpatialReference({ wkid: 102100 }); //WGS 1984 Web Mercator Projection
        //載入Google Geocoder(地址定位使用)
        geocoder = new google.maps.Geocoder();
        ezekMap = new Map("map_canvas", {
            logo: false,
            slider: true,
            sliderStyle: "small",
            sliderPosition: "top-left",
            spatialReference: {
                wkid: 102443
            },
            minZoom: 3,
            maxZoom: 13 // limit the zoom number; prevent extreme zooming to have no map image

        });

        //var tiledMap = new esri.layers.ArcGISDynamicMapServiceLayer(mapUrl, { id: "baseMap" });


        var tiledMap = null, TgosLayer_F2 = null;
        tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
            "opacity": 1,
            id: normalTileName2
        });
        ezekMap.addLayer(tiledMap, 0);

        // 1: 1/1000以上 電子地圖
        TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
            "opacity": 1,
            id: normalTileName1
        });
        ezekMap.addLayer(TgosLayer_F2, 1);

        var plate = new PrintTemplate();
        // plate.layout = plate.label = "A4 Landscape";
        // plate.layout = "MAP_ONLY";
        // plate.outScale = 1000000; //Zoom 遠近
        plate.showLabels = true;
        plate.format = "jpg";
        plate.label = "~~~Label~~";
        //設定true可自定義輸出範圍與比例，若false則輸出與ezekMap相同範圍比例的圖。 true為預設。
        // plate.preserveScale = false;  
        plate.exportOptions = { //必須是 MAP_ONLY 狀態
            width: 756,
            height: 536,
            dpi: 90
        };



        var printer = new Print({
            map: ezekMap,
            "templates": [plate],
            url: location.protocol + "//icdc.ntpc.gov.tw/arcgis/rest/services/Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task"
        }, dom.byId("printButton"));
        printer.startup();
        printer.on('print-start', function () {
            showLoading();
        });
        //注意  arcgis 版本 3.14會另開示窗
        printer.on('print-complete', function (evtt) {
            const EXP_NO = ($("[name=EXP_NO]").val() || "").trim();
            const postData = {
                imgUrl: evtt.result?.url ?? "",
                EXP_NO,
                Img_type: "PIT",
                Xc: currentCoordinate?.[0] ?? 0,
                Yc: currentCoordinate?.[1] ?? 0,
                random: Math.floor(Math.random() * 1_000_000)
            };

            $.post("in10101_saveImg.jsp", postData)
                .done(function (response) {
                    // 成功收到伺服器回應且 HTTP 狀態碼為 2xx
                    if (response && response.result === "success") {
                        // 伺服器回應 { "result": "success" } ⇒ 執行後續流程
                        getCoordinate_new();
                    } else {
                        // result 非 success，或伺服器結構不同 ⇒ 顯示原始回應內容
                        alert(JSON.stringify(response));
                    }
                })
                .fail(function (jqXHR, textStatus, errorThrown) {
                    console.warn("HTTP", jqXHR.status, "textStatus", textStatus, "errorThrown", errorThrown);
                    let msg = textStatus;
                    try {                         // 後端若仍回 JSON，可嘗試解析
                        const r = JSON.parse(jqXHR.responseText);
                        msg = r.message || msg;
                    } catch (_) { }
                    alert("請求失敗：" + msg);
                })
                .always(closeLoading);        // 無論成功或失敗皆結束載入指示
        });
        //硬改 改文字
        $("#dijit_form_Button_0_label").html("存&nbsp;&nbsp;&nbsp;&nbsp;檔");
        $("#printButton").find(".dijitReset .dijitToggleButtonIconChar").hide();
        $("#printButton").find("input.dijitOffScreen").hide();
        //--------
        // search
        //--------


        on(ezekMap, "load", function () {
            // initialize; store the coordinate before any editing
            currentCoordinate = [$("[name=lng]").val(), $("[name=lat]").val()];
            var ZOOML = $("[name=ZOOML]").val();

            // place the infowindow at the specific position
            ezekMap.infoWindow.anchor = "top";
            ezekMap.infoWindow.offsetY = 10;
            editToolbar = new Edit(ezekMap);

            var int_ZOOML = 10;
            if ($("[name=lng]").val() == "" || $("[name=lat]").val() == "") {
                $("[name=lng]").val("121.43124831920882");
                $("[name=lat]").val("25.00038938975144");
                //25.00038938975144, 121.43124831920882
                int_ZOOML = 3;

            }
            if ($("[name=lng]").val() !== "" && $("[name=lat]").val() !== "") {
                var longitude = parseFloat($("[name=lng]").val()),
                    latitude = parseFloat($("[name=lat]").val());

                WGS84_point_Zoom(longitude, latitude);

            }


            on(ezekMap, "click", function (event) {

                var longitude = event.mapPoint.x,
                    latitude = event.mapPoint.y;

                XY_point.x = longitude;
                XY_point.y = latitude;
                setLngLat(longitude, latitude);
                // add a graphic at the clicked location
                if (graphic != null) ezekMap.graphics.remove(graphic);
                if (graphic_text != null) ezekMap.graphics.remove(graphic_text);
                ezek_removeLocMarker();
                graphic = new Graphic(XY_point, markerPic);
                addressMarker = new Graphic(XY_point, markerPic);
                //ezek_IdentifyQuery( );
                ezekMap.graphics.add(addressMarker);
                ezekMap.graphics.add(graphic);
                setTimeout(function () {
                    editToolbar.activate(Edit.MOVE, graphic);
                    detectGraphicMovement();
                }, 1500);

                centerAndZoom(ezekMap.getZoom());
            });


            //依輸入地址顯示搜尋位置圖標
            $(".searchBox").show();
            document.getElementById("searchTextField").value = addrToal;
            on($("#searchBoxImg"), 'click', function (e) {

                geocoder.geocode({ 'address': document.getElementById("searchTextField").value }, function (results, status) {
                    if (status == google.maps.GeocoderStatus.OK) {
                        var loc = results[0].geometry.location;

                        if (graphic != null) ezekMap.graphics.remove(graphic);
                        ezek_removeLocMarker();
                        WGS84_point_Zoom(loc.lng(), loc.lat());
                    } else {
                        alert("[警告]輸入地址無法定位, 請重新輸入\n\n錯誤碼: " + status);
                    }
                });
            });


        });//END  on(ezekMap, "load", function() 

        //-----
        // zoom 經緯度
        //----
        function WGS84_point_Zoom(_longitude, _latitude) {

            currentCoordinate[0] = parseFloat(_longitude.toFixed(8));
            currentCoordinate[1] = parseFloat(_latitude.toFixed(8));
            $("[name=X_COORDINATE]").val(currentCoordinate[0]);
            $("[name=Y_COORDINATE]").val(currentCoordinate[1]);
            showInfoWindow();
            var int_ZOOML = 10;
            var ZOOML = $("[name=ZOOML]").val();
            if (ZOOML) int_ZOOML = parseFloat(ZOOML);

            WGS84_point.setLongitude(_longitude).setLatitude(_latitude);

            coordToMapSR_geometry(WGS84_point, function (data) {

                XY_point = new Point(data.x, data.y, ezekMap.spatialReference);




                graphic = new Graphic(XY_point, markerPic);
                addressMarker = new Graphic(XY_point, markerPic);
                //var containText = "違建位置座標：" +longitude + " E, "+ latitude + " N";
                /*
                containText =    longitude + " E, "+ latitude + " N"  ;
                showText.setText(containText);
                graphic_text = new Graphic(XY_point, showText);
                ezekMap.graphics.add(graphic_text);
                */
                //if (graphic != null) ezekMap.graphics.remove(graphic);
                //if (graphic_text != null) ezekMap.graphics.remove(graphic_text);
                //ezek_removeLocMarker();

                ezekMap.graphics.add(addressMarker);
                ezekMap.graphics.add(graphic);
                setTimeout(function () {
                    editToolbar.activate(Edit.MOVE, graphic);
                    detectGraphicMovement();
                }, 1500);
                centerAndZoom(int_ZOOML);
            })

        }
        function detectGraphicMovement() {

            editToolbar.on("graphic-first-move", function (evt) {
                ezek_removeLocMarker();
                ezekMap.infoWindow.hide();
            });

            editToolbar.on("graphic-move-stop", function (evt) {
                $.ajaxSettings.async = false;
                var longitude = evt.graphic.geometry.x,
                    latitude = evt.graphic.geometry.y;
                XY_point.x = longitude;
                XY_point.y = latitude;
                setLngLat(longitude, latitude);

                setTimeout(function () {
                    centerAndZoom(ezekMap.getZoom());
                }, 500);

                $.ajaxSettings.async = true;
            });

        }

        //-------
        // 
        //------
        function setLngLat(TMD_97_X, TMD_97_Y) {

            ezek_MapSRtoWGS84(TMD_97_X, TMD_97_Y, function (data) {
                currentCoordinate[0] = parseFloat(data.x.toFixed(8));
                currentCoordinate[1] = parseFloat(data.y.toFixed(8));
                $("[name=X_COORDINATE]").val(currentCoordinate[0]);
                $("[name=Y_COORDINATE]").val(currentCoordinate[1]);
                showInfoWindow();
            });

        }

        //-------
        // 
        //------
        function showInfoWindow() {
            /*
            ezekMap.infoWindow.resize(160, 60);
            ezekMap.infoWindow.setTitle("座標");
            ezekMap.infoWindow.setContent( "經度：" + currentCoordinate[0] + "<br>緯度：" + currentCoordinate[1] );
            ezekMap.infoWindow.show(XY_point, ezekMap.getInfoWindowAnchor(XY_point));
            */

            /*
            if (graphic_text != null) ezekMap.graphics.remove(graphic_text);
            containText =  currentCoordinate[0] + " E, " + currentCoordinate[1] + " N"  ;
            	
            showText.setText(containText);
            graphic_text = new esri.Graphic(XY_point, showText);
            ezekMap.graphics.add(graphic_text);
            */
            containText = "違建位置座標：" + currentCoordinate[0] + " E, " + currentCoordinate[1] + " N";
            $("#showLatLng").text(containText);
        }
        //-------
        // 
        //------
        function centerAndZoom(zoomLevel) {
            ezekMap.centerAndZoom(XY_point, zoomLevel)
                .then(function () {
                    showInfoWindow();
                });

        }
    });
}


//------------------------------------------------------------------------
// 分別清除圖標
//------------------------------------------------------------------------

function ezek_removeLocMarker() {
    if (addressMarker != null) ezekMap.graphics.remove(addressMarker);
    if (graphic_text != null) ezekMap.graphics.remove(graphic_text);

    //if (landNumMarker != null) ezekMap.graphics.remove(landNumMarker);

}
//-------
// 按鈕 存檔 onclick();
//------
function getCoordinate_new() {
    // 經度
    //window.opener.$("[name=lng]").val( currentCoordinate[0] );
    // 緯度
    //window.opener.$("[name=lat]").val( currentCoordinate[1] );

    // close this window
    //window.close();
    var zoomLevel = ezekMap.getZoom();
    parent.fnParent(currentCoordinate[0], currentCoordinate[1], zoomLevel, 'Y');
    distroyView();
}
//-------
// 按鈕 取消 onclick();
//------
function distroyView() {
    parent.$.fancybox.close();
}

//------------------------------------------------------------------------
// 地址定位圖標  wgs84  googleSearchBox
//------------------------------------------------------------------------
function return_geometry_wgs84(loc) {
    var pt;
    require(["esri/geometry/webMercatorUtils", "esri/geometry/Point", "esri/graphic"], function (WebMercatorUtils, Point, Graphic) {
        var twdloc = WebMercatorUtils.lngLatToXY(loc.x, loc.y);
        pt = new Point(twdloc[0], twdloc[1], ezekMap.spatialReference);

    });

    return pt;
}

//------------------------------------------------------------------------
// 將google的底圖使用的座標值(twd97SR)轉為經緯度座標
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function ezek_MapSRtoWGS84(sx, sy, callback) {
    require(["esri/geometry/Point", "esri/tasks/ProjectParameters"], function (Point, ProjectParameters) {
        var inPoint = new Point(sx, sy, twd97SR);
        var prjParams = new ProjectParameters();
        prjParams.geometries = [inPoint];
        prjParams.outSR = wgs84SR;
        gsvc.project(prjParams, function (pt) {
            var outPoint = pt[0];
            this.x = outPoint.x;
            this.y = outPoint.y;
            callback(this);
        }, showErr);
    });
}
//------------------------------------------------------------------------
// 將geometry物件轉為底圖使用的座標系統
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function coordToMapSR_geometry(inGeometry, callback) {
    require(["esri/geometry/Geometry", "esri/tasks/ProjectParameters", "esri/tasks/GeometryService"], function (Geometry, ProjectParameters, GeometryService) {

        var prjParams = new ProjectParameters();
        prjParams.geometries = [inGeometry];
        prjParams.outSR = ezekMap.spatialReference;

        gsvc.project(prjParams, function (et) {
            callback(et[0]);
        }, showErr);
    });
}
//------------------------------------------------------------------------
// 顯示錯誤訊息
//------------------------------------------------------------------------
function showErr(err) {
    alert(JSON.stringify(err));
}
//-----------------------------------------------------------------------------
// 抓取指定URL參數值
//-----------------------------------------------------------------------------
function getUrlParameter(parameterName) {
    parameterName = parameterName.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
    var regexS = "[\\?&]" + parameterName + "=([^&" + "#]*)";
    var regex = new RegExp(regexS);
    var results = regex.exec(window.location.href);
    if (results == null) {
        return "";
    } else {
        return results[1];
    }
}

function showLoading() {
    $.ajaxSettings.async = false;
    $.blockUI({
        message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;存檔中, 請稍候...',
        css: {
            border: 'none',
            padding: '6px',
            backgroundColor: '#000',
            '-webkit-border-radius': '10px',
            '-moz-border-radius': '10px',
            opacity: .5,
            color: '#FFF'
        }
    });
    $.ajaxSettings.async = true;
}

function closeLoading() {
    $.unblockUI();
}

