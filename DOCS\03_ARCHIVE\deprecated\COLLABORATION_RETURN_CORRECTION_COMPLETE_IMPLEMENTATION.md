# 協同退回補正機制 - 完整實施文件

## 📋 文件資訊
- **文件編號**: DEV-2025-001-FINAL
- **文件目的**: 協同退回補正機制的完整實施指南
- **建立日期**: 2025-07-05
- **執行團隊**: 【D】DevOps與技術架構任務組

---

## 🎯 需求說明

### 背景
因同仁有時送協同會有誤線問題，而無退回機制可退回承辦人，需要新增退回功能。

### 解決方案
新增「協同退回補正」狀態及相關退回機制，讓協同承辦人可以將案件退回給原承辦人。

---

## 📊 狀態碼設計

### 新增狀態碼

| 狀態碼 | 描述 | 類型 | 顏色顯示 |
|--------|------|------|----------|
| 235 | [一　般]協同退回補正 | 認定階段 | 紅色 |
| 245 | [廣告物]協同退回補正 | 認定階段 | 紅色 |
| 255 | [下水道]協同退回補正 | 認定階段 | 紅色 |

### 狀態流程
```
234 [一般]認定送協同作業 → 235 [一般]協同退回補正 → 231 [一般]認定辦理中
244 [廣告物]認定送協同作業 → 245 [廣告物]協同退回補正 → 241 [廣告物]認定辦理中
254 [下水道]認定送協同作業 → 255 [下水道]協同退回補正 → 251 [下水道]認定辦理中
```

---

## 💾 資料庫變更

### 1. 新增狀態碼 SQL

```sql
-- 新增協同退回補正狀態碼
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, sts, ib_prcs, prcs_kd, add_user, cr_date, up_date)
VALUES 
('RLT', '235', '[一　般]協同退回補正', 'Y', 'A', '02', 'SYSTEM', CURRENT_DATE, CURRENT_DATE),
('RLT', '245', '[廣告物]協同退回補正', 'Y', 'B', '02', 'SYSTEM', CURRENT_DATE, CURRENT_DATE),
('RLT', '255', '[下水道]協同退回補正', 'Y', 'C', '02', 'SYSTEM', CURRENT_DATE, CURRENT_DATE);

-- 驗證新增結果
SELECT code_type, code_seq, code_desc, sts, ib_prcs 
FROM public.ibmcode 
WHERE code_type = 'RLT' 
AND code_seq IN ('235', '245', '255')
ORDER BY code_seq;
```

### 2. 更新顯示邏輯相關查詢

```sql
-- 檢查現有紅色顯示的狀態碼
SELECT code_seq, code_desc 
FROM public.ibmcode 
WHERE code_type = 'RLT' 
AND code_seq IN ('36a', '34a', '35a', '237', '247', '257', '235', '245', '255')
ORDER BY code_seq;
```

---

## 🔧 程式碼修改

### 1. 新增協同退回API - case_collaboration_return.jsp

```jsp
<%@page contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.sql.*, java.util.UUID, org.json.simple.*"%>

<%!
/**
 * 協同作業退回狀態碼轉換
 * @param currentStatus 當前狀態碼
 * @return 退回後的目標狀態碼（協同退回補正）
 */
private String getCollaborationReturnStatus(String currentStatus) {
    switch(currentStatus) {
        case "234": return "235"; // 一般協同 → 一般協同退回補正
        case "244": return "245"; // 廣告協同 → 廣告協同退回補正
        case "254": return "255"; // 下水道協同 → 下水道協同退回補正
        default: return currentStatus;
    }
}

/**
 * 記錄操作日誌
 */
private void logOperation(String caseId, String operation, String orgStatus, String newStatus, 
                         String reason, String empNo, JDBCConnection conn) throws Exception {
    String uuid = UUID.randomUUID().toString();
    String logSql = "INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, rec_memo, empno, create_date) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
    
    PreparedStatement pstmt = null;
    try {
        pstmt = conn.prepareStatement(logSql);
        pstmt.setString(1, uuid);
        pstmt.setString(2, caseId);
        pstmt.setString(3, operation);
        pstmt.setString(4, orgStatus);
        pstmt.setString(5, newStatus);
        pstmt.setString(6, reason);
        pstmt.setString(7, empNo);
        pstmt.executeUpdate();
    } finally {
        if (pstmt != null) pstmt.close();
    }
}
%>

<%
response.setContentType("application/json");
response.setCharacterEncoding("UTF-8");

// 取得參數
String caseId = request.getParameter("caseId");
String currentStatus = request.getParameter("accRlt");
String empNo = request.getParameter("empNo");
String returnReason = request.getParameter("returnReason");

JSONObject result = new JSONObject();

// 參數驗證
if (caseId == null || currentStatus == null || returnReason == null || returnReason.trim().isEmpty()) {
    result.put("success", false);
    result.put("message", "參數不完整或退回原因為空");
    out.println(result.toString());
    return;
}

// 驗證狀態碼
if (!"234".equals(currentStatus) && !"244".equals(currentStatus) && !"254".equals(currentStatus)) {
    result.put("success", false);
    result.put("message", "非協同作業狀態，無法退回");
    out.println(result.toString());
    return;
}

JDBCConnection conn = null;

try {
    conn = JDBCConnectionFactory.getJDBCConnection("DBConn");
    
    // 開始交易
    conn.setAutoCommit(false);
    
    // 取得目標狀態碼
    String targetStatus = getCollaborationReturnStatus(currentStatus);
    
    // 1. 更新ibmsts當前狀態
    String updateSts = "UPDATE public.ibmsts SET acc_rlt = ?, " +
                      "acc_date = to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000, " +
                      "acc_time = to_number(to_char(current_timestamp, 'hh24mi'), '9999'), " +
                      "acc_job = (SELECT job_title FROM ibmuser WHERE empno = ?) " +
                      "WHERE case_id = ? AND acc_rlt = ?";
    
    PreparedStatement pstmt1 = conn.prepareStatement(updateSts);
    pstmt1.setString(1, targetStatus);
    pstmt1.setString(2, empNo);
    pstmt1.setString(3, caseId);
    pstmt1.setString(4, currentStatus);
    int updated = pstmt1.executeUpdate();
    pstmt1.close();
    
    if (updated == 0) {
        throw new Exception("案件狀態不符，無法退回");
    }
    
    // 2. 新增ibmfym歷程記錄
    String insertFym = "INSERT INTO public.ibmfym (case_id, acc_seq, acc_date, acc_time, " +
                      "acc_job, acc_rlt, acc_memo, op_user, cr_date) " +
                      "VALUES (?, (SELECT COALESCE(MAX(acc_seq), 0) + 1 FROM ibmfym WHERE case_id = ?), " +
                      "to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000, " +
                      "to_number(to_char(current_timestamp, 'hh24mi'), '9999'), " +
                      "(SELECT job_title FROM ibmuser WHERE empno = ?), ?, ?, ?, " +
                      "to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000)";
    
    PreparedStatement pstmt2 = conn.prepareStatement(insertFym);
    pstmt2.setString(1, caseId);
    pstmt2.setString(2, caseId);
    pstmt2.setString(3, empNo);
    pstmt2.setString(4, targetStatus);
    pstmt2.setString(5, "協同退回：" + returnReason);
    pstmt2.setString(6, empNo);
    pstmt2.executeUpdate();
    pstmt2.close();
    
    // 3. 清理協同處理中記錄
    String deleteCaseopened = "DELETE FROM public.caseopened WHERE case_id = ?";
    PreparedStatement pstmt3 = conn.prepareStatement(deleteCaseopened);
    pstmt3.setString(1, caseId);
    pstmt3.executeUpdate();
    pstmt3.close();
    
    // 4. 記錄操作日誌
    logOperation(caseId, "協同退回", currentStatus, targetStatus, returnReason, empNo, conn);
    
    // 提交交易
    conn.commit();
    
    result.put("success", true);
    result.put("message", "案件已成功退回");
    result.put("newStatus", targetStatus);
    
} catch (Exception e) {
    if (conn != null) {
        try {
            conn.rollback();
        } catch (SQLException re) {
            System.err.println("Rollback failed: " + re.getMessage());
        }
    }
    result.put("success", false);
    result.put("message", "退回失敗：" + e.getMessage());
    System.err.println("case_collaboration_return error: " + e.toString());
    e.printStackTrace();
} finally {
    if (conn != null) {
        try {
            conn.setAutoCommit(true);
            conn.closeConnection();
        } catch (Exception e) {
            System.err.println("Connection close failed: " + e.getMessage());
        }
    }
}

out.println(result.toString());
%>
```

### 2. 修改 im10101_lisHandlers.jsp - 新增紅色顯示

找到第236行附近，修改為：

```java
else if("36a".equals(ACC_RLT) || "34a".equals(ACC_RLT) || "35a".equals(ACC_RLT) 
    || "237".equals(ACC_RLT) || "247".equals(ACC_RLT) || "257".equals(ACC_RLT)
    || "235".equals(ACC_RLT) || "245".equals(ACC_RLT) || "255".equals(ACC_RLT)){
    ACC_RLT_HTML = "<label style= 'color:red;'>"+ACC_RLT_NAME+"</label>";    
}
```

### 3. 修改 im10201_man.jsp - 新增退回按鈕

在按鈕區域（約第150行）新增：

```jsp
<!-- 新增退回按鈕 -->
<ccs:control name='show_return_button'>
<ccs:if_value value='Y'>
    <input type="button" id="BMSDISOBEY_DISTButton_Return" 
           class="btn btn-warning" 
           value="退回承辦" 
           onclick="showReturnDialog()">
</ccs:if_value>
</ccs:control>
```

在JavaScript區域新增：

```javascript
function showReturnDialog() {
    var dialogHtml = '<div id="returnDialog" class="modal fade" tabindex="-1">' +
        '<div class="modal-dialog">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<button type="button" class="close" data-dismiss="modal">&times;</button>' +
        '<h4 class="modal-title">退回案件確認</h4>' +
        '</div>' +
        '<div class="modal-body">' +
        '<p>確定要將此案件退回給原承辦人嗎？</p>' +
        '<div class="form-group">' +
        '<label for="returnReason">退回原因：<span class="text-danger">*</span></label>' +
        '<textarea id="returnReason" class="form-control" rows="3" ' +
        'placeholder="請輸入退回原因（必填）" required></textarea>' +
        '</div>' +
        '<p class="text-info">案件將退回至「協同退回補正」狀態</p>' +
        '</div>' +
        '<div class="modal-footer">' +
        '<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>' +
        '<button type="button" class="btn btn-warning" onclick="confirmReturn()">確定退回</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';
    
    // 移除舊的對話框
    $('#returnDialog').remove();
    
    // 新增對話框
    $('body').append(dialogHtml);
    $('#returnDialog').modal('show');
}

function confirmReturn() {
    var returnReason = $("#returnReason").val().trim();
    if (!returnReason) {
        alert("請輸入退回原因");
        return;
    }
    
    var caseId = $("#BMSDISOBEY_DISTcase_id").val();
    var accRlt = $("#BMSDISOBEY_DISTacc_rlt").val();
    var empNo = '<%= SessionStorage.getInstance(request).getAttribute("UserID") %>';
    
    // 顯示處理中
    $('#returnDialog .modal-footer button').prop('disabled', true);
    $('#returnDialog .modal-footer').append('<span class="text-info"> 處理中...</span>');
    
    $.ajax({
        url: 'case_collaboration_return.jsp',
        type: 'POST',
        data: {
            caseId: caseId,
            accRlt: accRlt,
            empNo: empNo,
            returnReason: returnReason
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                alert(response.message);
                // 返回列表頁
                window.location.href = 'im10201_lis.jsp';
            } else {
                alert("退回失敗：" + response.message);
                $('#returnDialog .modal-footer button').prop('disabled', false);
                $('#returnDialog .modal-footer .text-info').remove();
            }
        },
        error: function(xhr, status, error) {
            alert('系統錯誤：' + error);
            $('#returnDialog .modal-footer button').prop('disabled', false);
            $('#returnDialog .modal-footer .text-info').remove();
        }
    });
}
```

### 4. 修改 im10201_manHandlers.jsp - 新增權限控制

在 BeforeShow 事件中新增（約第420行後）：

```java
// 判斷是否顯示退回按鈕
String currentEmpNo = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
String originalEmpNo = Utils.convertToString(e.getRecord().getControl("reg_emp").getValue());
String accRlt = Utils.convertToString(e.getRecord().getControl("acc_rlt").getValue());

// 取得當前登入者的單位
String currentUserUnit = Utils.convertToString(DBTools.dLookUp("UNIT_ID", "ibmuser", 
    "empno = '" + currentEmpNo + "'", CONNECTION_NAME));

// 判斷是否為協同承辦人（非原承辦人且為協同狀態）
boolean isCollaborator = false;
if (("234".equals(accRlt) || "244".equals(accRlt) || "254".equals(accRlt)) 
    && !currentEmpNo.equals(originalEmpNo)) {
    
    // 檢查是否為對應的協同單位
    if ("234".equals(accRlt) && ("011".equals(currentUserUnit) || "012".equals(currentUserUnit))) {
        isCollaborator = true; // 認定科
    } else if ("244".equals(accRlt) && "041".equals(currentUserUnit)) {
        isCollaborator = true; // 廣告科
    } else if ("254".equals(accRlt) && "031".equals(currentUserUnit)) {
        isCollaborator = true; // 勞安科
    }
}

// 設定是否顯示退回按鈕
e.getRecord().getControl("show_return_button").setValue(isCollaborator ? "Y" : "N");
```

---

## 📝 測試指引

### 1. 資料庫測試

```sql
-- 測試狀態碼是否正確新增
SELECT code_type, code_seq, code_desc, sts, ib_prcs 
FROM public.ibmcode 
WHERE code_type = 'RLT' 
AND code_seq IN ('234', '235', '244', '245', '254', '255')
ORDER BY code_seq;

-- 測試案例查詢
SELECT c.case_id, c.reg_emp, s.acc_rlt, i.code_desc
FROM ibmcase c
JOIN ibmsts s ON c.case_id = s.case_id
JOIN ibmcode i ON s.acc_rlt = i.code_seq AND i.code_type = 'RLT'
WHERE s.acc_rlt IN ('234', '244', '254');
```

### 2. 功能測試步驟

1. **準備測試案件**
   - 找一個狀態為234/244/254的案件
   - 確認登入者為協同承辦人（非原承辦人）

2. **測試退回功能**
   - 進入im10201_man.jsp
   - 確認看到「退回承辦」按鈕
   - 點擊按鈕，輸入退回原因
   - 確認案件狀態變更為235/245/255

3. **驗證結果**
   ```sql
   -- 檢查狀態變更
   SELECT case_id, acc_rlt FROM ibmsts WHERE case_id = '測試案號';
   
   -- 檢查歷程記錄
   SELECT case_id, acc_seq, acc_rlt, acc_memo, op_user, acc_date 
   FROM ibmfym 
   WHERE case_id = '測試案號' 
   ORDER BY acc_seq DESC;
   
   -- 檢查操作記錄
   SELECT case_id, rec_type, org_rec, new_rec, rec_memo, empno 
   FROM record 
   WHERE case_id = '測試案號' 
   ORDER BY create_date DESC;
   ```

---

## 🚨 注意事項

1. **權限控制**
   - 只有協同承辦人可以看到退回按鈕
   - 原承辦人不能執行退回操作

2. **狀態限制**
   - 只有234/244/254狀態可以退回
   - 已完成協同（23b/24b/25b）不可退回

3. **資料完整性**
   - 所有操作都在交易中執行
   - 失敗時會自動回滾

4. **日誌記錄**
   - 記錄在ibmfym歷程表
   - 記錄在record操作日誌表

---

## 📋 部署清單

- [ ] 執行資料庫新增狀態碼SQL
- [ ] 新增 case_collaboration_return.jsp
- [ ] 修改 im10101_lisHandlers.jsp（紅色顯示）
- [ ] 修改 im10201_man.jsp（新增按鈕）
- [ ] 修改 im10201_manHandlers.jsp（權限控制）
- [ ] 執行完整測試
- [ ] 確認日誌記錄正常

---

*文件撰寫：【D】Claude Code - DevOps與技術架構任務組*
*完成日期：2025-07-05*