/**
所有功能
1.setMouseLocation()  ==> 叫滑鼠
2.ezek_MapSRtoWGS84   ==> 將google的底圖使用的座標值(twd97SR)轉為經緯度座標
3.
  search 系列
4.
*/
var initExtent ;
//-------------
//initMap 呼叫法
//arcGisMap = new ArcGisMap("ezekMap_canvas");
//arcGisMap.initMap();
//----------
var ArcGisMap = function(mapId) {
	//---------------------------------------------------------
	// 台灣全圖範圍(102100:WGS 1984 Web Mercator Projection)
	//---------------------------------------------------------
	/*
	var initExtent = new esri.geometry.Extent({
		"xmin": 13284188,
		"ymin": 2496492,
		"xmax": 13575261,
		"ymax": 2926985,
		"spatialReference": {"wkid":102100}
	});
*/
	var identifyTask, identifyParams, identifyResult;
	
	ArcGisMap.prototype.initMap = function() {
		bkObj = this;
		 require([
        "esri/map",
		"esri/dijit/Scalebar",
        "esri/InfoTemplate",
		"dojo/parser",
        "esri/layers/ArcGISDynamicMapServiceLayer",
        "esri/symbols/SimpleFillSymbol",
		"agsjs/layers/GoogleMapsLayer",
        "esri/symbols/SimpleLineSymbol",
        "esri/tasks/IdentifyTask",
        "esri/tasks/IdentifyParameters",
        "esri/dijit/Popup",
        "dojo/_base/array",
        "esri/Color",
		"esri/SpatialReference",
        "dojo/dom-construct",		
		"esri/toolbars/navigation",
		"esri/dijit/Search",
		"esri/geometry/Point",
		"esri/geometry/webMercatorUtils",
		"esri/tasks/GeometryService",
        "dojo/domReady!"
      ], function (
        Map, Scalebar,InfoTemplate,parser,  ArcGISDynamicMapServiceLayer, SimpleFillSymbol,GoogleMapsLayer,
        SimpleLineSymbol, IdentifyTask, IdentifyParameters, Popup,
        arrayUtils, Color,SpatialReference, domConstruct ,  Navigation, Search,Point,  WebMercatorUtils,GeometryService
      ) {
		parser.parse();
		
		 initExtent = new esri.geometry.Extent({
			"xmin": 13284188,
			"ymin": 2496492,
			"xmax": 13575261,
			"ymax": 2926985,
			"spatialReference": {"wkid":102100}
		});
		
		
		gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");
		wgs84SR = new SpatialReference({wkid:4326});		//WGS84
		twd97SR = new SpatialReference({wkid:102443});		//TWD_97_TM2
		webMercatorSR = new SpatialReference({wkid:102100});	//WGS 1984 Web Mercator Projection

		ezekMap = new Map("mapDiv", {
			logo:false,
			extent: initExtent,
			slider: true,
			sliderStyle: "small",
			//infoWindow: popup
        });
		
		
		googleLayer = new agsjs.layers.GoogleMapsLayer({
			apiOptions: {
				v: '3.20'
			}
		});
		googleLayer.setMapTypeId(agsjs.layers.GoogleMapsLayer.MAP_TYPE_ROADMAP);
		ezekMap.addLayer(googleLayer, 0);
		//是否套用TGos
		var service = "http://api.tgos.nat.gov.tw/TileAgent/";   //設定圖磚代理服務位址
		var BaseMap = "TGOSMAP_W.aspx";                   //TGOS電子地圖
		//var BaseMap = "NLSCMAP_W.aspx";                //通用版電子地圖
		//var BaseMap = "F2IMAGE_W.aspx";                  //福衛二號影像
		//var BaseMap = "ROADMAP_W.aspx";               //福衛二號混合圖
		//var BaseMap = "HILLSHADE_W.aspx";              //地形暈渲圖
		//var BaseMap = "HILLSHADEMIX_W.aspx";      //地形暈渲混合圖
		var _url = service + BaseMap;               //組合TGOS圖磚服務位址
		LoadScript("javascript/tgos/SGSArcGISServer.js?20160605", function() { //使用LoadScript方式讀取SGSArcGISServer.js
		//	<!--指定TGOS圖磚服務所在的位址來建立圖層-->
			sgsLayer = new SGSTileLayer(_url, "sgsLayer",  0);	
			sgsLayer.setOpacity(0.2);
			ezekMap.addLayer(sgsLayer, 1);//加入圖層
			ezekMap.spatialReference = new esri.SpatialReference({"wkid":102100});
			//initLayer();
			console.log("  ezekMap  LoadScript  ");
			
			
			
			
			//每個 Layer ADD後都會做一次
			dojo.connect(ezekMap,"onLayerAddResult", function(evt) {
				console.log("  ezekMap  onLayerAddResult  ");
				googleLayer.hide();
				var layer = evt;
				ezekMap.spatialReference = new esri.SpatialReference({"wkid":102100});
			});
console.log("  ezekMap LoadScript end  ");

		});
		ezekMap.on("layers-add-result", function() {
				console.log("  ezekMap  ayers-add-result  ");
				 
				googleLayer.hide();
				ezekMap.setExtent(initExtent, true);				
				
			}); 
			
		//地圖載入後套疊監測站圖層
		dojo.connect(ezekMap, "onLoad", function() {
			console.log("  ezekMap  onLoad  2");
		});
		bkObj.addMouse();
			
			
			
			//console.log("mouseQ"  + mouseQ);
			//mouseQ = 1;
		if (mouseLatLng.show){
			
	
			var x = document.getElementById("ezek_latlngInfo");
			var x_style = mouseLatLng.style;
			if ( x_style == "" || x_style == null || x_style == 'undefined'){
				//x.setAttribute("style", "display:block;");
				x.style.display = "block";
			}else{
				//var intStyle = x.getAttribute("style").value ;
				//if (intStyle != null){
					x.setAttribute("style", x_style);
				//}else{
					//x.setAttribute("style", mouseQ);
				//}
			}
			dojo.connect(ezekMap, "onMouseMove", showCoordinates);
			dojo.connect(ezekMap, "onMouseDrag", showCoordinates);
		}	
			
		
		
  		//滑鼠座標
		function showCoordinates(evt) {
			//if (typeof evt.mapPoint.x != 'undefined') document.getElementById("coordInfo").innerHTML = "TWD97 (" + evt.mapPoint.x.toFixed(2) + ", " + evt.mapPoint.y.toFixed(2) + ")";
			if (typeof evt.mapPoint.x != 'undefined') {
				var wgs84Coord = WebMercatorUtils.xyToLngLat(evt.mapPoint.x, evt.mapPoint.y);
				//document.getElementById("ezek_latlngInfo").innerHTML = wgs84Coord[0].toFixed(7) + " E, " +  wgs84Coord[1].toFixed(6) + " N   TWD97 ( " +evt.mapPoint.x + " , " + evt.mapPoint.y +" )";
				document.getElementById("ezek_latlngInfo").innerHTML = wgs84Coord[0].toFixed(7) + " E, " +  wgs84Coord[1].toFixed(6) + " N" ;
			}
		}
	  });
	}

}
function initLayer() {	
	require(["esri/geometry/Extent"],function(Extent) 
	{	
		zonareaLayer = new esri.layers.ArcGISDynamicMapServiceLayer(tccgupMapserver1 , { id: '行政區界' , "opacity": 0.5  });
		ezekMap.addLayers(zonareaLayer);	
	});
}
ArcGisMap.prototype.addMouse = function() { 
	 //加入滑鼠經緯度顯示列  預設 hide();
	var ezek_latlngInfo = '<div id="ezek_latlngInfo" style="position: absolute;bottom: 10px;left: 35px;z-index: 50;">&nbsp;</div>';
	$("#mapDiv_root").prepend($(ezek_latlngInfo));	
	$("#ezek_latlngInfo").hide();
}
//-------
// 功能::滑鼠位置 
// setMouseLocation()
// parameter : String  直接塞 STYLE  記得加分號  EXP:  bottom: 10px;left: 35px;
// 如果沒設定 舊不出現
// 使用  id = "ezek_latlngInfo"
//------
//function setMouseLocation (loc) {	 
ArcGisMap.prototype.setMouseLocation = function(loc) {	 
	
	var x = document.getElementById("ezek_latlngInfo");
	if ( loc == null || loc == 'undefined'){
		//x.setAttribute("style", "display:block;");
		x.style.display = "block";
	}else{
		var intStyle = x.getAttribute("style").value ;
		if (intStyle != null){
			x.setAttribute("style", intStyle + loc);
		}else{
			x.setAttribute("style", loc);
		}
	}
}


//------------------------------------------------------------------------
// 地址定位圖標  TWD97  //門牌定位系統使用  一定在臺中 所以不判斷
//------------------------------------------------------------------------
function return_geometry_twd97(PtX , PtY ) {
	ezek_removeLocMarker();
	var pt;
	pt = new esri.geometry.Point(PtX, PtY, twd97SR);
	//pt = new esri.geometry.Point(PtX, PtY, ezekMap.spatialReference);
	
	
	return pt;
}
//------------------------------------------------------------------------
// 地址定位圖標  wgs84  googleSearchBox
//------------------------------------------------------------------------
function return_geometry_wgs84(loc) {
	
	var pt;
	require(["esri/geometry/webMercatorUtils", "esri/geometry/Point", "esri/graphic"], function(WebMercatorUtils, Point, Graphic) {
		var twdloc = WebMercatorUtils.lngLatToXY(loc.lng(), loc.lat());
		if (loc.lng() >= 119 && loc.lng() <= 123 && loc.lat() >= 21 && loc.lat() <= 26) {  //台灣經緯度範圍內
			pt = new Point(twdloc[0], twdloc[1], ezekMap.spatialReference);
		} else {
			//alert("[警告]輸入地址不在此地圖範圍內, 無法提供定位服務");
			goAlertify("查詢結果","輸入地址不在此地圖範圍內, 無法提供定位服務", "確定" );
		}
	});
	
	return pt;
}
//------------------------------------------------------------------------
// 將google的底圖使用的座標值(twd97SR)轉為經緯度座標
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function ezek_MapSRtoWGS84(sx, sy , callback) {
	require(["esri/geometry/Point", "esri/tasks/ProjectParameters"], function(Point, ProjectParameters) {
		var inPoint = new Point(sx, sy, twd97SR);
		var prjParams = new ProjectParameters();
		prjParams.geometries = [inPoint];
		prjParams.outSR = wgs84SR;
		gsvc.project(prjParams, function (pt) {
			var outPoint = pt[0];
			this.x = outPoint.x;
			this.y = outPoint.y;	
			callback(this);
		}, showErr);
	});
}


