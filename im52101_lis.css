/* im52101_lis.css - Styles for Excel import list page */

/* Table styles */
.table-bordered>tbody>tr>td {
    border: 1px solid #a5a5a5;
}

.table-bordered>thead>tr>th {
    background-color: #e6e6e6;
    border: 1px solid #a5a5a5;
}

/* Search form styles */
.search-container {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

/* Button styles */
.btn-primary {
    background-color: #337ab7;
    border-color: #2e6da4;
}

/* Message styles */
.exceed-limit, .no-search-parameter {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

/* List item styles */
.list-item:hover {
    background-color: #f5f5f5;
}

/* Pagination styles */
.pagination {
    margin: 20px 0;
}

/* Hide elements */
.hide {
    display: none;
}

/* Status indicator styles */
.status-success {
    color: #3c763d;
}

.status-error {
    color: #a94442;
}

.status-pending {
    color: #8a6d3b;
}