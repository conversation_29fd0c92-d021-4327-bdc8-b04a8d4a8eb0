{"backup": {"base_path": "D:\\Backups\\BMS", "retention_days": 30, "compression_level": 9, "verify_backups": true, "parallel_jobs": 4}, "postgresql": {"host": "localhost", "port": "5432", "database": "bms", "username": "postgres", "password": "S!@h@202203", "pg_path": "C:\\Program Files\\PostgreSQL\\15\\bin", "backup_types": {"full": {"enabled": true, "schedule": "0 2 * * *", "retention_days": 7}, "incremental": {"enabled": true, "schedule": "0 */6 * * *", "retention_days": 3}, "differential": {"enabled": true, "schedule": "0 */12 * * *", "retention_days": 5}}}, "sqlserver": {"server": "**************", "port": "2433", "database": "ramsGIS", "username": "sa", "password": "$ystemOnlin168", "backup_types": {"full": {"enabled": true, "schedule": "0 1 * * *", "retention_days": 7}, "differential": {"enabled": true, "schedule": "0 */8 * * *", "retention_days": 3}, "log": {"enabled": true, "schedule": "0 */1 * * *", "retention_days": 2}}}, "application": {"monitored_services": ["Apache Tomcat 9.0 Tomcat9", "PostgreSQL Database Server 15", "World Wide Web Publishing Service"], "backup_paths": [{"name": "tomcat_webapps", "source": "D:\\apache-tomcat-9.0.98\\webapps", "exclude_patterns": [".*\\.tmp$", ".*\\.log$", ".*\\.bak$", "temp/.*", "cache/.*"]}, {"name": "tomcat_conf", "source": "D:\\apache-tomcat-9.0.98\\conf", "exclude_patterns": [".*\\.tmp$"]}, {"name": "tomcat_lib", "source": "D:\\apache-tomcat-9.0.98\\lib", "exclude_patterns": []}], "config_paths": ["D:\\apache-tomcat-9.0.98\\webapps\\src\\WEB-INF\\site.properties", "D:\\apache-tomcat-9.0.98\\webapps\\src\\WEB-INF\\web.xml", "D:\\apache-tomcat-9.0.98\\conf\\server.xml", "D:\\apache-tomcat-9.0.98\\conf\\context.xml", "D:\\apache-tomcat-9.0.98\\conf\\tomcat-users.xml"], "log_paths": [{"name": "tomcat_logs", "path": "D:\\apache-tomcat-9.0.98\\logs", "pattern": "*.log", "max_age_days": 7}, {"name": "application_logs", "path": "D:\\apache-tomcat-9.0.98\\webapps\\src\\logs", "pattern": "*.log", "max_age_days": 7}], "backup_types": {"full": {"enabled": true, "schedule": "0 0 * * 0", "retention_days": 14}, "incremental": {"enabled": true, "schedule": "0 */4 * * *", "retention_days": 7}, "configuration": {"enabled": true, "schedule": "0 */2 * * *", "retention_days": 30}}}, "cross_region_replication": {"enabled": false, "targets": [{"name": "azure_blob_storage", "type": "azure_blob", "enabled": false, "storage_account_name": "bmsbackupstorage", "storage_account_key": "your_azure_storage_key", "container_name": "backups"}, {"name": "aws_s3_storage", "type": "aws_s3", "enabled": false, "access_key": "your_aws_access_key", "secret_key": "your_aws_secret_key", "bucket_name": "bms-backups", "region": "us-west-2"}, {"name": "ftp_server", "type": "ftp", "enabled": false, "server": "ftp.example.com", "username": "backup_user", "password": "backup_password"}, {"name": "network_share", "type": "network_share", "enabled": true, "share_path": "\\\\backup-server\\bms-backups", "username": "backup_user", "password": "backup_password"}]}, "notifications": {"enabled": true, "email": {"enabled": true, "smtp_server": "smtp.example.com", "smtp_port": 587, "use_ssl": true, "username": "<EMAIL>", "password": "email_password", "from_address": "<EMAIL>", "to_address": "<EMAIL>"}, "slack": {"enabled": false, "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "channel": "#backup-alerts"}, "sms": {"enabled": false, "provider": "twi<PERSON>", "account_sid": "your_twilio_account_sid", "auth_token": "your_twilio_auth_token", "from_number": "+**********", "to_number": "+**********"}}, "monitoring": {"enabled": true, "interval_minutes": 5, "health_check_interval_minutes": 15, "log_retention_days": 30}, "security": {"encrypt_backups": true, "encryption_key": "your_encryption_key_here", "secure_transfer": true, "audit_logging": true}, "performance": {"max_concurrent_backups": 3, "backup_timeout_minutes": 120, "compression_threads": 4, "network_bandwidth_limit_mbps": 100}}