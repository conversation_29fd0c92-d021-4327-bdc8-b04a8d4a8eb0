# Health Diagnostics Tool - 獨立健康診斷工具

## 概述
Health Diagnostics Tool 是新北市違章建築管理系統的獨立診斷工具，可在不影響生產環境的情況下執行深度系統診斷。

## 功能特點
- 獨立執行，無需部署到應用程式伺服器
- 支援命令列參數和配置檔案
- 可排程執行定期健康檢查
- 生成多種格式的診斷報告

## 安裝與設定

### 系統需求
- Java 8 或以上版本
- 至少 512MB 記憶體
- 網路連線（用於資料庫診斷）

### 安裝步驟
1. 解壓縮診斷工具包
2. 編輯 `config/diagnostics.properties` 設定檔
3. 執行 `scripts/setup.sh` 初始化

## 使用方式

### 基本命令

#### 快速健康檢查
```bash
./diagnostics.sh --quick-check
```

#### 完整系統診斷
```bash
./diagnostics.sh --full-scan
```

#### 效能分析模式
```bash
./diagnostics.sh --performance-analysis
```

#### 安全掃描模式
```bash
./diagnostics.sh --security-scan
```

### 進階選項

#### 指定輸出格式
```bash
./diagnostics.sh --full-scan --output-format=HTML,PDF,JSON
```

#### 指定輸出目錄
```bash
./diagnostics.sh --full-scan --output-dir=/path/to/reports
```

#### 靜默模式
```bash
./diagnostics.sh --full-scan --silent
```

#### 自動修復模式
```bash
./diagnostics.sh --full-scan --auto-fix
```

### 排程執行

使用 cron 排程定期執行：
```bash
# 每天凌晨 2 點執行完整診斷
0 2 * * * /path/to/diagnostics.sh --full-scan --silent

# 每小時執行快速檢查
0 * * * * /path/to/diagnostics.sh --quick-check --silent
```

## 配置說明

### 主要配置檔案
- `config/diagnostics.properties` - 診斷工具主配置
- `config/database.properties` - 資料庫連線配置
- `config/thresholds.properties` - 診斷閾值配置
- `config/email.properties` - 郵件通知配置

### 配置範例
```properties
# 診斷模式
diagnostics.mode=FULL_SCAN

# 執行緒數
diagnostics.threads=10

# 逾時設定（毫秒）
diagnostics.timeout=300000

# 報告設定
report.format=HTML,PDF
report.output.dir=./reports
report.retention.days=30

# 自動修復
autofix.enabled=true
autofix.confirm=false

# 通知設定
notification.enabled=true
notification.email.to=<EMAIL>
notification.threshold=WARNING
```

## 診斷報告

### 報告內容
- 執行摘要
- 系統健康狀態
- 發現的問題清單
- 效能指標
- 資源使用狀況
- 建議修復方案

### 報告格式
- **HTML**: 互動式網頁報告，包含圖表和詳細資訊
- **PDF**: 可列印的正式報告
- **JSON**: 機器可讀格式，用於整合其他系統
- **CSV**: 資料匯出格式

## 故障排除

### 常見問題

1. **無法連線資料庫**
   - 檢查資料庫配置
   - 確認網路連線
   - 驗證防火牆設定

2. **記憶體不足錯誤**
   - 增加 JVM heap 大小
   - 減少並發執行緒數

3. **權限錯誤**
   - 確保有檔案系統寫入權限
   - 檢查資料庫使用者權限

## 開發與擴充

### 新增自訂分析器
1. 實作 `IDiagnosticAnalyzer` 介面
2. 將編譯後的類別放入 `lib/extensions`
3. 在配置檔案中註冊分析器

### API 整合
診斷工具提供 REST API 用於遠端執行和結果查詢。

## 版本資訊
- 版本：1.0.0
- 建立日期：2025-07-08
- 維護團隊：新北市違章建築管理系統開發團隊

## 授權
本工具為新北市政府專屬軟體，未經授權不得使用或散布。