# 核心業務檔案識別文件

## 文件資訊
- **任務編號**: T1.1.2
- **負責人**: 【A】Claude Code - 全棧開發任務組
- **工時估算**: 6小時
- **建立日期**: 2025-07-05
- **最後更新**: 2025-07-05

## 概述

本文件識別新北市違章建築管理系統中im系列核心業務檔案的功能分類與重要性評估。基於對484個JSP檔案的分析，建立核心檔案清單和依賴關係圖。

## 模組編號結構分析

### IM系列模組編號解析
```
im[XX][YYY]_[role].[ext]
│  │   │     │      │
│  │   │     │      └── 檔案類型 (jsp/xml)
│  │   │     └── 頁面角色 (man/lis/prt等)
│  │   └── 功能子編號 (001-999)
│  └── 業務大類編號 (10-90)
└── 系統前綴 (im=違章管理)
```

### 業務大類編號對應
| 大類編號 | 業務領域 | 檔案數量 | 重要性 | 說明 |
|---------|----------|----------|--------|------|
| **10** | 基礎資料管理 | 27 | 🔴 極高 | 違章建築基本資料、地址管理 |
| **20** | 案件管理核心 | 78 | 🔴 極高 | 掛號、認定、審核主流程 |
| **30** | 檔案管理 | 10 | 🟡 中等 | 文件上傳、影像管理 |
| **40** | 通知管理 | 30 | 🟠 高 | 通知書產生、送達管理 |
| **50** | 認定作業 | 44 | 🔴 極高 | 現場勘查、認定決議 |
| **60** | 拆除作業 | 31 | 🔴 極高 | 排拆執行、現場管控 |
| **70** | 結案作業 | 19 | 🟠 高 | 案件結案、歸檔管理 |
| **90** | 系統管理 | 35 | 🟡 中等 | 用戶管理、參數設定 |

## 核心業務檔案清單

### 🔴 極高重要性檔案 (Tier 1 - Critical)

#### 1. 案件管理核心 (im20xxx)
```
im20101_* (案件掛號)
├── im20101_lis.jsp      - 案件列表查詢
├── im20101_man.jsp      - 案件基本資料管理
├── im20101_man_2.jsp    - 案件詳細資料
├── im20101_man_3.jsp    - 案件狀態控制
├── im20101_man_4.jsp    - 案件流程控制
└── im20101_prt.jsp      - 案件資料列印

im20301_* (案件認定)
├── im20301_lis.jsp      - 認定案件列表
├── im20301_man.jsp      - 認定資料管理
├── im20301_man_2.jsp    - 認定結果輸入
├── im20301_man_3.jsp    - 認定審核作業
└── im20301_man_4.jsp    - 認定完成處理

im20701_* (現場勘查)
├── im20701_lis.jsp      - 勘查案件列表
├── im20701_man.jsp      - 勘查記錄管理
├── im20701_man2.jsp     - 勘查照片管理
└── im20701_man_prt.jsp  - 勘查報告列印
```

#### 2. 基礎資料管理 (im10xxx)
```
im10101_* (違建基本資料)
├── im10101_lis.jsp      - 違建資料列表
├── im10101_man.jsp      - 違建資料管理
├── im10101_man_A.jsp    - 一般違建資料
├── im10101_man_B.jsp    - 廣告物資料
├── im10101_man_C.jsp    - 下水道資料
└── im10101_legend.jsp   - 圖例管理
```

#### 3. 認定作業核心 (im50xxx)
```
im50101_* (認定決議)
├── im50101_lis.jsp      - 認定案件列表
├── im50101_man.jsp      - 認定決議管理
├── im50101_man_A.jsp    - 一般違建認定
├── im50101_man_B.jsp    - 廣告物認定
└── im50101_man_C.jsp    - 下水道認定
```

#### 4. 拆除作業核心 (im60xxx)
```
im60301_* (排拆執行)
├── im60301_lis.jsp      - 排拆案件列表
├── im60301_man.jsp      - 排拆資料管理
├── im60301_man_A.jsp    - 一般違建排拆
├── im60301_man_B.jsp    - 廣告物排拆
└── im60301_man_C.jsp    - 下水道排拆
```

### 🟠 高重要性檔案 (Tier 2 - Important)

#### 5. 通知管理 (im40xxx)
```
im40101_* (通知書管理)
im40201_* (送達管理)
im40301_* (催告管理)
im40401_* (罰鍰管理)
im40501_* (強制執行)
```

#### 6. 結案作業 (im70xxx)
```
im70301_* (案件結案)
im70401_* (結案審核)
im70601_* (歸檔管理)
```

### 🟡 中等重要性檔案 (Tier 3 - Supporting)

#### 7. 檔案管理 (im30xxx)
```
im30101_* (文件上傳)
im30201_* (影像管理)
```

#### 8. 系統管理 (im90xxx)
```
im90101_* (使用者管理)
im90201_* (權限管理)
im90301_* (代碼管理)
im90401_* (系統參數)
im90501_* (操作日誌)
```

## 核心檔案依賴關係分析

### 主要資料表依賴
```mermaid
graph TD
    A[ibmcase 主案件表] --> B[im20101 案件掛號]
    A --> C[im20301 案件認定]
    A --> D[im50101 認定決議]
    A --> E[im60301 排拆執行]
    A --> F[im70301 案件結案]
    
    G[BMSDISOBEY_DIST 違建詳細] --> H[im10101 違建管理]
    H --> B
    
    I[ibmcode 代碼表] --> J[所有模組]
    
    K[ibmuser 使用者] --> L[im90101 使用者管理]
    L --> M[權限控制]
```

### 業務流程依賴
```mermaid
graph LR
    A[im20101 掛號] --> B[im20701 勘查]
    B --> C[im20301 認定]
    C --> D[im50101 決議]
    D --> E[im40101 通知]
    E --> F[im60301 排拆]
    F --> G[im70301 結案]
```

## 功能模組詳細分析

### Tier 1 詳細功能分析

#### im20101 - 案件掛號模組
- **主要功能**: 違章建築案件初始建立與掛號
- **核心檔案**:
  - `im20101_lis.jsp`: 案件查詢列表，支援複雜條件篩選
  - `im20101_man.jsp`: 案件基本資料輸入界面
  - `im20101_man_2.jsp`: 詳細位置資訊輸入
  - `im20101_man_3.jsp`: 案件狀態管理與流程控制
  - `im20101_man_4.jsp`: 特殊處理與例外狀況
- **關鍵業務邏輯**:
  - 案件編號自動產生
  - 地址驗證與標準化
  - 案件類型自動分類 (一般/廣告/下水道)
  - 承辦人員自動分配
- **狀態碼操作**: 231/241/251 (掛號完成)

#### im50101 - 認定決議模組
- **主要功能**: 違章建築認定結果決議與審核
- **核心檔案**:
  - `im50101_lis.jsp`: 待認定案件列表
  - `im50101_man.jsp`: 認定決議主畫面
  - `im50101_man_A.jsp`: 一般違建認定專用
  - `im50101_man_B.jsp`: 廣告物認定專用
  - `im50101_man_C.jsp`: 下水道認定專用
- **關鍵業務邏輯**:
  - 認定標準檢核
  - 法規條文引用
  - 認定結果多級審核
  - 自動狀態轉換
- **狀態碼操作**: 23b/24b/25b (認定完成)

#### im60301 - 排拆執行模組
- **主要功能**: 違章建築拆除執行與監控
- **核心檔案**:
  - `im60301_lis.jsp`: 排拆案件管理
  - `im60301_man.jsp`: 排拆計畫管理
  - `im60301_man_A.jsp`: 一般違建排拆
  - `im60301_man_B.jsp`: 廣告物排拆
  - `im60301_man_C.jsp`: 下水道排拆
- **關鍵業務邏輯**:
  - 排拆通知產生
  - 現場執行記錄
  - 拆除完成確認
  - 後續追蹤管理
- **狀態碼操作**: 349/359/369 (排拆完成)

## 檔案重要性評分標準

### 評分維度
1. **業務關鍵性** (40%)
   - 5分: 主流程核心功能
   - 4分: 重要支援功能
   - 3分: 一般業務功能
   - 2分: 輔助功能
   - 1分: 可選功能

2. **使用頻率** (30%)
   - 5分: 每日必用
   - 4分: 經常使用
   - 3分: 定期使用
   - 2分: 偶爾使用
   - 1分: 很少使用

3. **系統影響範圍** (20%)
   - 5分: 影響整體系統
   - 4分: 影響多個模組
   - 3分: 影響單一模組
   - 2分: 影響部分功能
   - 1分: 獨立功能

4. **維護複雜度** (10%)
   - 5分: 極高複雜度
   - 4分: 高複雜度
   - 3分: 中等複雜度
   - 2分: 低複雜度
   - 1分: 簡單功能

### 核心檔案評分結果

| 檔案模組 | 業務關鍵性 | 使用頻率 | 系統影響 | 維護複雜度 | 總分 | 等級 |
|---------|-----------|----------|----------|-----------|------|------|
| im20101 | 5 | 5 | 5 | 5 | 5.0 | 🔴 |
| im50101 | 5 | 4 | 4 | 5 | 4.6 | 🔴 |
| im60301 | 5 | 4 | 4 | 4 | 4.4 | 🔴 |
| im10101 | 4 | 5 | 4 | 4 | 4.3 | 🔴 |
| im20301 | 4 | 4 | 4 | 4 | 4.0 | 🔴 |
| im40101 | 4 | 3 | 3 | 3 | 3.4 | 🟠 |
| im70301 | 3 | 3 | 3 | 3 | 3.0 | 🟠 |
| im90101 | 2 | 2 | 4 | 3 | 2.6 | 🟡 |

## 檔案間關聯分析

### 強耦合關係組
```
Group 1: 案件生命週期核心
- im20101 → im20701 → im20301 → im50101
- 關聯強度: 極高
- 修改影響: 連鎖反應

Group 2: 拆除執行鏈
- im50101 → im40101 → im60301 → im70301
- 關聯強度: 高
- 修改影響: 中等影響

Group 3: 基礎資料支援
- im10101 → im90301 → im90401
- 關聯強度: 中等
- 修改影響: 有限影響
```

### 介面依賴關係
```
數據流向分析:
im20101_man → im20101_man_2 → im20101_man_3 → im20101_man_4
    ↓              ↓              ↓              ↓
狀態: 建立      →   編輯      →   審核      →   完成

跨模組調用:
im20101 ⟷ im10101 (違建資料)
im50101 ⟷ im40101 (通知產生)
im60301 ⟷ im30101 (檔案管理)
```

## 開發優先序建議

### Phase 1: 核心流程 (週1-2)
1. **im20101** - 案件掛號 (最高優先)
2. **im10101** - 基礎資料
3. **im50101** - 認定決議
4. **im90301** - 代碼管理 (支援)

### Phase 2: 擴展功能 (週3-4)
1. **im20301** - 案件認定
2. **im60301** - 排拆執行
3. **im40101** - 通知管理
4. **im20701** - 現場勘查

### Phase 3: 支援系統 (週5-6)
1. **im70301** - 結案作業
2. **im30101** - 檔案管理
3. **im90101** - 系統管理
4. **其他模組** - 次要功能

## 風險評估與建議

### 高風險檔案
1. **im20101_manHandlers.jsp**: 複雜業務邏輯，修改需謹慎
2. **im50101_man.jsp**: 認定邏輯複雜，影響面廣
3. **im60301_manHandlers.jsp**: 排拆邏輯，涉及外部系統

### 維護建議
1. **優先重構**: Tier 1 檔案的 Handler 邏輯
2. **介面標準化**: 統一各模組的操作介面
3. **依賴解耦**: 減少強耦合關係
4. **單元測試**: 針對核心業務邏輯建立測試

## 產出交付

### 核心檔案清單
- **極高重要性**: 20個模組 (80個檔案)
- **高重要性**: 15個模組 (60個檔案)  
- **中等重要性**: 10個模組 (40個檔案)

### 依賴關係圖
- 模組間依賴關係圖 (Mermaid格式)
- 資料流向分析圖
- 介面調用關係圖

---

**完成狀態**: ✅ 已完成核心業務檔案識別與分類  
**下一步**: 建立 JSP-XML-Handler 三檔案對應表 (T1.1.3)