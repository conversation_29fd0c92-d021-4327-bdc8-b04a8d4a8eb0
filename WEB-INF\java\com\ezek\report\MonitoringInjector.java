package com.ezek.report;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.codecharge.db.JDBCConnection;
import com.codecharge.db.DBConnectionManager;
import com.codecharge.db.JDBCConnectionFactory;

/**
 * 非侵入式監控注入器
 * 
 * 透過 Servlet Filter 和反射機制，在不修改現有程式碼的情況下
 * 注入 N+1 查詢監控功能
 * 
 * 注入策略：
 * 1. Filter 層級：攔截 HTTP 請求，注入監控上下文
 * 2. Connection 層級：替換資料庫連接為代理物件
 * 3. Handler 層級：通過反射注入監控程式碼
 * 4. 統計層級：收集和分析查詢模式
 */
public class MonitoringInjector {
    
    private static final Map<String, RequestMonitoringInfo> activeRequests = new ConcurrentHashMap<>();
    private static boolean injectionEnabled = true;
    
    /**
     * 請求監控資訊
     */
    public static class RequestMonitoringInfo {
        public final String requestId;
        public final String uri;
        public final long startTime;
        public final List<String> executedQueries = Collections.synchronizedList(new ArrayList<>());
        public final Map<String, Integer> queryFrequency = new ConcurrentHashMap<>();
        public int totalQueries = 0;
        
        public RequestMonitoringInfo(String requestId, String uri) {
            this.requestId = requestId;
            this.uri = uri;
            this.startTime = System.currentTimeMillis();
        }
        
        public void recordQuery(String normalizedQuery) {
            executedQueries.add(normalizedQuery);
            queryFrequency.merge(normalizedQuery, 1, Integer::sum);
            totalQueries++;
        }
        
        public List<String> getPotentialNPlusOneQueries() {
            List<String> nPlusOneQueries = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : queryFrequency.entrySet()) {
                if (entry.getValue() >= 3) { // 3次以上視為潛在 N+1
                    nPlusOneQueries.add(String.format("%s (executed %d times)", 
                        entry.getKey(), entry.getValue()));
                }
            }
            return nPlusOneQueries;
        }
        
        public long getDuration() {
            return System.currentTimeMillis() - startTime;
        }
    }
    
    // === Servlet Filter 實作 ===
    
    /**
     * N+1 查詢監控 Filter
     * 
     * 在每個 HTTP 請求的開始和結束時注入監控邏輯
     */
    public static class NPlusOneMonitoringFilter implements Filter {
        
        @Override
        public void init(FilterConfig filterConfig) throws ServletException {
            System.out.println("N+1 Query Monitoring Filter initialized");
            NPlusOneQueryDetector.enableMonitoring();
            QueryInterceptor.enableInterceptor();
        }
        
        @Override
        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
                throws IOException, ServletException {
            
            if (!injectionEnabled) {
                chain.doFilter(request, response);
                return;
            }
            
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            String requestId = generateRequestId(httpRequest);
            String uri = httpRequest.getRequestURI();
            
            // 創建請求監控資訊
            RequestMonitoringInfo monitoringInfo = new RequestMonitoringInfo(requestId, uri);
            activeRequests.put(requestId, monitoringInfo);
            
            // 設定請求屬性，供後續處理使用
            request.setAttribute("MONITORING_REQUEST_ID", requestId);
            request.setAttribute("MONITORING_INFO", monitoringInfo);
            
            try {
                // 注入資料庫連接監控
                injectDatabaseMonitoring();
                
                // 執行請求處理
                chain.doFilter(request, response);
                
            } finally {
                // 請求完成後分析結果
                analyzeRequestQueries(monitoringInfo);
                
                // 清理監控資訊
                activeRequests.remove(requestId);
                QueryInterceptor.clearCurrentContext();
            }
        }
        
        @Override
        public void destroy() {
            System.out.println("N+1 Query Monitoring Filter destroyed");
            NPlusOneQueryDetector.disableMonitoring();
            QueryInterceptor.disableInterceptor();
        }
        
        /**
         * 生成請求 ID
         */
        private String generateRequestId(HttpServletRequest request) {
            return String.format("%s_%d_%s", 
                request.getSession().getId(),
                System.currentTimeMillis(),
                Thread.currentThread().getId()
            );
        }
        
        /**
         * 分析請求中的查詢模式
         */
        private void analyzeRequestQueries(RequestMonitoringInfo info) {
            List<String> nPlusOneQueries = info.getPotentialNPlusOneQueries();
            
            if (!nPlusOneQueries.isEmpty()) {
                System.err.println(String.format(
                    "=== Potential N+1 Queries Detected ===\n" +
                    "Request: %s\n" +
                    "Duration: %d ms\n" +
                    "Total Queries: %d\n" +
                    "Potential N+1 Patterns:\n%s\n" +
                    "=======================================",
                    info.uri,
                    info.getDuration(),
                    info.totalQueries,
                    String.join("\n", nPlusOneQueries)
                ));
            }
        }
    }
    
    // === 資料庫監控注入 ===
    
    /**
     * 注入資料庫連接監控
     */
    private static void injectDatabaseMonitoring() {
        try {
            // 注入 JDBCConnectionFactory 監控
            injectJDBCConnectionFactoryMonitoring();
            
            // 注入 DBConnectionManager 監控
            injectDBConnectionManagerMonitoring();
            
        } catch (Exception e) {
            System.err.println("Failed to inject database monitoring: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 注入 JDBCConnectionFactory 監控
     */
    private static void injectJDBCConnectionFactoryMonitoring() throws Exception {
        Class<?> factoryClass = JDBCConnectionFactory.class;
        
        // 檢查是否已經注入過
        Field[] fields = factoryClass.getDeclaredFields();
        for (Field field : fields) {
            if (field.getName().equals("monitoringInjected")) {
                return; // 已經注入過，跳過
            }
        }
        
        // 使用反射替換 getJDBCConnection 方法的實作
        Method getConnectionMethod = factoryClass.getMethod("getJDBCConnection", String.class);
        
        // 注意：這裡需要使用字節碼操作庫（如 Javassist 或 ASM）來真正實現方法替換
        // 由於複雜性，這裡提供概念性實作
        System.out.println("JDBCConnectionFactory monitoring injection attempted");
    }
    
    /**
     * 注入 DBConnectionManager 監控
     */
    private static void injectDBConnectionManagerMonitoring() throws Exception {
        DBConnectionManager manager = DBConnectionManager.getInstance();
        
        // 透過反射取得連線池
        Field poolField = manager.getClass().getDeclaredField("pools");
        poolField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> pools = (Map<String, Object>) poolField.get(manager);
        
        // 為每個連線池注入監控
        for (String poolName : pools.keySet()) {
            System.out.println("Injecting monitoring for connection pool: " + poolName);
            // 這裡需要進一步的實作來包裝 Connection 物件
        }
    }
    
    // === Handler 監控注入 ===
    
    /**
     * Handler 方法監控裝飾器
     */
    public static class HandlerMethodDecorator {
        
        /**
         * 裝飾 Handler 的關鍵方法
         */
        public static void decorateHandlerMethods(Object handler) {
            if (!injectionEnabled) return;
            
            Class<?> handlerClass = handler.getClass();
            String className = handlerClass.getName();
            
            // 只處理 CodeCharge Handler 類別
            if (!className.contains("Handler")) {
                return;
            }
            
            try {
                // 找到需要監控的方法
                Method[] methods = handlerClass.getDeclaredMethods();
                for (Method method : methods) {
                    String methodName = method.getName();
                    
                    if (shouldMonitorMethod(methodName)) {
                        decorateMethod(handler, method);
                    }
                }
                
            } catch (Exception e) {
                System.err.println("Failed to decorate handler methods: " + e.getMessage());
            }
        }
        
        /**
         * 判斷是否需要監控該方法
         */
        private static boolean shouldMonitorMethod(String methodName) {
            return methodName.equals("beforeShow") ||
                   methodName.equals("afterUpdate") ||
                   methodName.equals("beforeSelect") ||
                   methodName.equals("onValidate");
        }
        
        /**
         * 裝飾單一方法
         */
        private static void decorateMethod(Object handler, Method method) {
            // 這裡需要使用 AOP 框架或字節碼操作來實現方法攔截
            // 概念性實作：記錄方法進入和退出
            System.out.println(String.format("Decorating method: %s.%s", 
                handler.getClass().getSimpleName(), method.getName()));
        }
    }
    
    // === 效能分析工具 ===
    
    /**
     * 查詢效能分析器
     */
    public static class QueryPerformanceAnalyzer {
        
        private static final Map<String, QueryPerformanceStats> performanceStats = new ConcurrentHashMap<>();
        
        public static class QueryPerformanceStats {
            public final String queryPattern;
            public final List<Long> executionTimes = Collections.synchronizedList(new ArrayList<>());
            public final Set<String> callerLocations = Collections.synchronizedSet(new HashSet<>());
            public volatile long totalExecutions = 0;
            public volatile long totalExecutionTime = 0;
            public volatile long maxExecutionTime = 0;
            public volatile long minExecutionTime = Long.MAX_VALUE;
            
            public QueryPerformanceStats(String queryPattern) {
                this.queryPattern = queryPattern;
            }
            
            public synchronized void recordExecution(long executionTime, String callerLocation) {
                totalExecutions++;
                totalExecutionTime += executionTime;
                executionTimes.add(executionTime);
                callerLocations.add(callerLocation);
                
                maxExecutionTime = Math.max(maxExecutionTime, executionTime);
                minExecutionTime = Math.min(minExecutionTime, executionTime);
                
                // 保持最近 100 次記錄
                if (executionTimes.size() > 100) {
                    executionTimes.remove(0);
                }
            }
            
            public double getAverageExecutionTime() {
                return totalExecutions > 0 ? (double) totalExecutionTime / totalExecutions : 0;
            }
            
            public double getExecutionFrequency() {
                return totalExecutions; // 可以根據時間範圍計算頻率
            }
            
            public boolean isPotentialPerformanceIssue() {
                return totalExecutions > 10 && getAverageExecutionTime() > 100; // 平均超過 100ms
            }
        }
        
        /**
         * 記錄查詢效能
         */
        public static void recordQueryPerformance(String sql, long executionTime, String callerLocation) {
            String pattern = normalizeQueryForAnalysis(sql);
            QueryPerformanceStats stats = performanceStats.computeIfAbsent(pattern, QueryPerformanceStats::new);
            stats.recordExecution(executionTime, callerLocation);
            
            // 如果檢測到效能問題，發出警告
            if (stats.isPotentialPerformanceIssue() && stats.totalExecutions % 10 == 0) {
                System.err.println(String.format(
                    "Performance Warning: Query executed %d times with average %.2f ms\n" +
                    "Query: %s\n" +
                    "Locations: %s",
                    stats.totalExecutions,
                    stats.getAverageExecutionTime(),
                    pattern,
                    stats.callerLocations
                ));
            }
        }
        
        /**
         * 產生效能報告
         */
        public static String generatePerformanceReport() {
            StringBuilder report = new StringBuilder();
            report.append("=== Query Performance Report ===\n\n");
            
            List<QueryPerformanceStats> sortedStats = new ArrayList<>(performanceStats.values());
            sortedStats.sort((a, b) -> Double.compare(b.getAverageExecutionTime(), a.getAverageExecutionTime()));
            
            for (QueryPerformanceStats stats : sortedStats) {
                if (stats.totalExecutions > 5) { // 只顯示執行超過 5 次的查詢
                    report.append(String.format(
                        "Query: %s\n" +
                        "  Executions: %d\n" +
                        "  Avg Time: %.2f ms\n" +
                        "  Max Time: %d ms\n" +
                        "  Min Time: %d ms\n" +
                        "  Locations: %s\n\n",
                        stats.queryPattern.substring(0, Math.min(100, stats.queryPattern.length())),
                        stats.totalExecutions,
                        stats.getAverageExecutionTime(),
                        stats.maxExecutionTime,
                        stats.minExecutionTime,
                        stats.callerLocations.size() > 3 ? 
                            stats.callerLocations.stream().limit(3).reduce((a, b) -> a + ", " + b).orElse("") + "..." :
                            stats.callerLocations
                    ));
                }
            }
            
            return report.toString();
        }
        
        private static String normalizeQueryForAnalysis(String sql) {
            if (sql == null) return "UNKNOWN";
            
            return sql.replaceAll("'[^']*'", "'...'")
                      .replaceAll("\\b\\d+\\b", "N")
                      .replaceAll("\\s+", " ")
                      .trim();
        }
    }
    
    // === 靜態分析工具 ===
    
    /**
     * 靜態程式碼分析器
     */
    public static class StaticCodeAnalyzer {
        
        /**
         * 分析專案中的所有檔案
         */
        public static List<String> analyzeProject(String projectPath) {
            List<String> issues = new ArrayList<>();
            
            try {
                // 分析 JSP 檔案
                List<String> jspFiles = findFiles(projectPath, ".jsp");
                for (String jspFile : jspFiles) {
                    String content = readFile(jspFile);
                    issues.addAll(NPlusOneQueryDetector.analyzeJspFile(content, jspFile));
                }
                
                // 分析 Handlers 檔案
                List<String> handlerFiles = findFiles(projectPath, "Handlers.jsp");
                for (String handlerFile : handlerFiles) {
                    String content = readFile(handlerFile);
                    issues.addAll(NPlusOneQueryDetector.analyzeHandlersFile(content, handlerFile));
                }
                
                // 分析 XML 配置檔案
                List<String> xmlFiles = findFiles(projectPath, ".xml");
                for (String xmlFile : xmlFiles) {
                    String content = readFile(xmlFile);
                    issues.addAll(NPlusOneQueryDetector.analyzeXmlFile(content, xmlFile));
                }
                
            } catch (Exception e) {
                issues.add("Error during static analysis: " + e.getMessage());
            }
            
            return issues;
        }
        
        private static List<String> findFiles(String basePath, String extension) {
            List<String> files = new ArrayList<>();
            // 這裡需要實作檔案搜尋邏輯
            // 可以使用 Files.walk() 或類似的 API
            return files;
        }
        
        private static String readFile(String filePath) {
            // 這裡需要實作檔案讀取邏輯
            return "";
        }
    }
    
    // === 公開 API ===
    
    /**
     * 啟用監控注入
     */
    public static void enableInjection() {
        injectionEnabled = true;
        System.out.println("Monitoring injection enabled");
    }
    
    /**
     * 停用監控注入
     */
    public static void disableInjection() {
        injectionEnabled = false;
        activeRequests.clear();
        System.out.println("Monitoring injection disabled");
    }
    
    /**
     * 取得活躍的請求監控資訊
     */
    public static Map<String, RequestMonitoringInfo> getActiveRequests() {
        return new HashMap<>(activeRequests);
    }
    
    /**
     * 產生完整的監控報告
     */
    public static String generateFullReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== N+1 Query Monitoring Full Report ===\n\n");
        
        // N+1 檢測結果
        report.append("=== N+1 Detection Results ===\n");
        report.append(NPlusOneQueryDetector.generateStatsReport());
        report.append("\n");
        
        // 效能分析結果
        report.append("=== Performance Analysis ===\n");
        report.append(QueryPerformanceAnalyzer.generatePerformanceReport());
        report.append("\n");
        
        // 活躍請求統計
        report.append("=== Active Requests ===\n");
        for (RequestMonitoringInfo info : activeRequests.values()) {
            report.append(String.format(
                "Request: %s\n" +
                "  Duration: %d ms\n" +
                "  Total Queries: %d\n" +
                "  Potential N+1: %s\n\n",
                info.uri,
                info.getDuration(),
                info.totalQueries,
                info.getPotentialNPlusOneQueries()
            ));
        }
        
        return report.toString();
    }
    
    /**
     * 執行完整的專案分析
     */
    public static String analyzeEntireProject(String projectPath) {
        StringBuilder report = new StringBuilder();
        
        report.append("=== Complete Project Analysis ===\n\n");
        
        // 靜態分析
        List<String> staticIssues = StaticCodeAnalyzer.analyzeProject(projectPath);
        report.append("=== Static Analysis Issues ===\n");
        for (String issue : staticIssues) {
            report.append(issue).append("\n");
        }
        report.append("\n");
        
        // 執行時分析（如果有的話）
        report.append(generateFullReport());
        
        return report.toString();
    }
}