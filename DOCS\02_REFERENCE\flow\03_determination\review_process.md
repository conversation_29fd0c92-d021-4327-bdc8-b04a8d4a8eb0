# 新北市違章建築管理系統 - 認定審核流程分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.3.1
- **分析範圍**: 認定審核流程分析 (狀態碼234/244/254等)
- **工時**: 5小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署5個專責Agent同時進行：
- Agent 1: 認定審核相關JSP檔案搜尋
- Agent 2: 狀態轉換邏輯分析
- Agent 3: 協同作業機制分析
- Agent 4: 權限控制與簽核機制分析
- Agent 5: 撤銷與異議處理分析

## 1. 認定審核流程概述

### 1.1 業務定義
認定審核是違章建築管理系統的核心環節，負責對現場勘查結果進行法律認定、跨部門協同作業、主管審批，最終確定違建事實與處理方式。

### 1.2 審核目標
- **法律認定** - 依據建築法規確認違建事實
- **多方協同** - 整合相關部門專業意見
- **分級審批** - 落實主管監督與品質控制
- **決策依據** - 為後續處理提供法律基礎

## 2. 認定審核系統檔案架構

### 2.1 核心認定管理模組 (im10101系列)

#### 三類違建認定頁面
```
一般違建認定：
- im10101_man_A.jsp          // 一般違建認定主頁面
- im10101_man_AHandlers.jsp  // 一般違建認定業務邏輯

廣告違建認定：
- im10101_man_B.jsp          // 廣告違建認定主頁面  
- im10101_man_BHandlers.jsp  // 廣告違建認定業務邏輯

下水道違建認定：
- im10101_man_C.jsp          // 下水道違建認定主頁面
- im10101_man_CHandlers.jsp  // 下水道違建認定業務邏輯

共用功能：
- im10101_lis.jsp            // 違建案件清單頁面
- im10101_lisHandlers.jsp    // 案件清單業務邏輯
```

#### 核心認定業務邏輯
**檔案位置**: `im10101_man_AHandlers.jsp` (第1289-1312行)

```java
// 認定陳核與協同作業流程
if("submit".equals(SUBMIT_STATE) || "synergy".equals(SUBMIT_STATE)){
    String acc_rlt;
    
    if("submit".equals(SUBMIT_STATE)){
        acc_rlt = "232";  // 一般違建認定陳核
        operation = "認定陳核";
    }
    else if("synergy".equals(SUBMIT_STATE)){
        acc_rlt = "234";  // 一般違建協同作業
        operation = "協同作業";
    }
    
    // 建立歷程記錄
    String INSERT_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE)"
                      + " VALUES(?, ?, ?, ?, ?, ?, ?)";
    
    // 更新案件狀態
    String UPDATE_SQL = "UPDATE IBMSTS SET ACC_DATE=?, ACC_TIME=?, ACC_JOB=?, ACC_RLT=? WHERE CASE_ID=?";
    
    DBTools.executeUpdate(INSERT_SQL, CASE_ID, currentDate, currentTime, jobTitle, acc_rlt, opUser, currentDate);
    DBTools.executeUpdate(UPDATE_SQL, currentDate, currentTime, jobTitle, acc_rlt, CASE_ID);
}
```

### 2.2 審核流程管理模組 (im20101系列)

#### 審核管理頁面
```
im20101_man.jsp              // 認定審核管理主頁面
im20101_manHandlers.jsp      // 認定審核管理業務邏輯
im20101_man_3.jsp            // 認定審核詳細頁面
im20101_man_3Handlers.jsp    // 認定審核詳細業務邏輯
```

#### 審核決策邏輯
```java
// 審核結果處理
public void processReviewDecision(String caseId, String decision, String reviewer) {
    String newStatus;
    
    switch(decision) {
        case "APPROVE":
            newStatus = determineApprovedStatus(caseId);
            break;
        case "REJECT":
            newStatus = "236"; // 認定退回
            break;
        case "REQUEST_REVISION":
            newStatus = "239"; // 認定補正
            break;
        default:
            throw new IllegalArgumentException("無效的審核決策");
    }
    
    updateCaseStatus(caseId, newStatus, reviewer);
    notifyStakeholders(caseId, decision);
}
```

### 2.3 協同作業模組 (im60301系列)

#### 跨部門協同頁面
```
im60301_man_A.jsp            // 協同作業A類頁面
im60301_man_AHandlers.jsp    // 協同作業A類業務邏輯
im60301_man_B.jsp            // 協同作業B類頁面
im60301_man_BHandlers.jsp    // 協同作業B類業務邏輯
im60301_man_C.jsp            // 協同作業C類頁面
im60301_man_CHandlers.jsp    // 協同作業C類業務邏輯
```

#### 品質控制機制
**檔案位置**: `im60301_manHandlers.jsp` (第199行)

```java
// 資料繕校檢查
String cnt92c = Utils.convertToString(DBTools.dLookUp("COUNT(*)", "IBMFYM", 
    "case_id = '"+case_id+"' and acc_rlt in ('92c', '36c') and acc_seq <= '"+acc_seq+"'", "DBConn"));

if(Integer.parseInt(cnt92c) > 0) {
    // 案件已進入繕校程序
    applyQualityControl(case_id);
}
```

### 2.4 撤銷機制 (case_withdraw.jsp)

#### 撤銷權限控制
**檔案位置**: `case_withdraw.jsp` (第15-25行)

```java
// 可撤銷狀態檢查
if (!"232".equals(accRlt)    // 一般違建認定陳核
    && !"252".equals(accRlt) // 下水道違建認定陳核
    && !"342".equals(accRlt) // 一般違建排拆陳核
    && !"352".equals(accRlt) // 下水道違建排拆陳核
    && !"362".equals(accRlt) // 其他違建排拆陳核
    && !"442".equals(accRlt) // 廣告違建結案陳核
    && !"452".equals(accRlt) // 下水道違建結案陳核
    && !"462".equals(accRlt)) { // 一般違建結案陳核
    
    outJSon.put("result", "-1");
    outJSon.put("message", "此狀態下無法撤銷");
    return;
}

// 執行撤銷邏輯
withdrawCase(caseId, accRlt, currentUser);
```

## 3. 認定審核業務流程

### 3.1 完整審核流程圖

```mermaid
flowchart TD
    A[現場勘查完成] --> B{勘查結果}
    B -->|確認違建| C[認定資料準備]
    B -->|查無違建| D[案件結案]
    
    C --> E[承辦人員認定作業]
    E --> F{處理方式}
    F -->|一般認定| G[認定陳核 232/242/252]
    F -->|需要協同| H[協同作業 234/244/254]
    
    G --> I[主管審核]
    H --> J[跨部門協同]
    J --> K[協同意見整合]
    K --> I
    
    I --> L{審核結果}
    L -->|核准| M[認定完成 23b/24b/25b]
    L -->|退回| N[認定退回 236/246/256]
    L -->|補正| O[認定補正 239/249/259]
    
    N --> E
    O --> E
    M --> P[進入通知階段]
    
    Q[品質控制] --> R[資料繕校 92c]
    R --> S[繕校審核 36c]
    S --> I
```

### 3.2 狀態轉換詳細分析

#### 認定陳核流程 (2x2系列)
```java
// 認定陳核狀態轉換
public String submitForReview(String currentStatus, String violationType) {
    String businessType = currentStatus.substring(0, 2);
    
    switch(businessType) {
        case "23": // 一般違建
            return "232"; // 一般違建認定陳核
        case "24": // 廣告違建  
            return "242"; // 廣告違建認定陳核
        case "25": // 下水道違建
            return "252"; // 下水道違建認定陳核
        default:
            throw new IllegalStateException("無效的業務類型: " + businessType);
    }
}
```

#### 協同作業流程 (2x4系列)
```java
// 協同作業狀態轉換
public String initiateCollaboration(String currentStatus) {
    String businessType = currentStatus.substring(0, 2);
    
    switch(businessType) {
        case "23": // 一般違建
            return "234"; // 一般違建協同作業
        case "24": // 廣告違建
            return "244"; // 廣告違建協同作業  
        case "25": // 下水道違建
            return "254"; // 下水道違建協同作業
        default:
            throw new IllegalStateException("無效的業務類型: " + businessType);
    }
}
```

#### 認定完成流程 (2xb系列)
```java
// 認定完成狀態轉換
public String completeReview(String currentStatus, boolean approved) {
    if (!approved) {
        return handleRejection(currentStatus);
    }
    
    String businessType = currentStatus.substring(0, 2);
    
    switch(businessType) {
        case "23": // 一般違建
            return "23b"; // 一般違建認定完成
        case "24": // 廣告違建
            return "24b"; // 廣告違建認定完成
        case "25": // 下水道違建
            return "25b"; // 下水道違建認定完成
        default:
            throw new IllegalStateException("無效的業務類型: " + businessType);
    }
}
```

### 3.3 特殊狀態處理

#### 撤銷處理流程
```java
// 撤銷邏輯實作
public void withdrawCase(String caseId, String currentStatus, String operator) {
    String targetStatus = determineWithdrawTarget(currentStatus);
    
    // 撤銷目標狀態對應表
    Map<String, String> withdrawTargets = Map.of(
        "232", "231",  // 認定陳核 → 認定準備
        "242", "241",  // 廣告認定陳核 → 廣告認定準備
        "252", "251",  // 下水道認定陳核 → 下水道認定準備
        "342", "341",  // 排拆陳核 → 排拆準備
        "352", "351",  // 下水道排拆陳核 → 下水道排拆準備
        "442", "441",  // 廣告結案陳核 → 廣告結案準備
        "452", "451",  // 下水道結案陳核 → 下水道結案準備
        "462", "461"   // 一般結案陳核 → 一般結案準備
    );
    
    String targetStatus = withdrawTargets.get(currentStatus);
    if (targetStatus != null) {
        updateCaseStatus(caseId, targetStatus, operator);
        logWithdrawOperation(caseId, currentStatus, targetStatus, operator);
    }
}
```

#### 品質控制流程
```java
// 資料繕校機制
public void initiateQualityControl(String caseId, String qcType) {
    String qcStatus;
    
    switch(qcType) {
        case "DATA_VALIDATION":
            qcStatus = "92c"; // 案件資料繕校
            break;
        case "APPROVAL_REVIEW":
            qcStatus = "36c"; // 繕校簽核
            break;
        default:
            throw new IllegalArgumentException("無效的品質控制類型");
    }
    
    // 建立品質控制記錄
    insertQualityControlRecord(caseId, qcStatus, getCurrentUser());
    
    // 暫停案件處理，等待品質控制完成
    suspendCaseProcessing(caseId, "品質控制中");
}
```

## 4. 權限控制與簽核機制

### 4.1 角色權限矩陣

| 操作功能 | 承辦人員 | 股長 | 科長 | 協同單位 | 品管人員 |
|----------|----------|------|------|----------|----------|
| **認定資料輸入** | ✓ | ✓ | ✓ | - | - |
| **認定陳核** | ✓ | - | - | - | - |
| **認定審核** | - | ✓ | ✓ | - | - |
| **協同作業發起** | ✓ | ✓ | - | - | - |
| **協同意見提供** | - | - | - | ✓ | - |
| **案件撤銷** | ✓ | ✓ | ✓ | - | - |
| **資料繕校** | - | - | - | - | ✓ |
| **最終核定** | - | - | ✓ | - | - |

### 4.2 簽核流程層級

#### 一般案件簽核流程
```java
// 簽核層級定義
public class ApprovalLevel {
    public static final int STAFF_LEVEL = 1;      // 承辦人員
    public static final int SUPERVISOR_LEVEL = 2;  // 股長
    public static final int MANAGER_LEVEL = 3;     // 科長
    public static final int DIRECTOR_LEVEL = 4;    // 隊長
    
    public boolean requiresApproval(String caseType, BigDecimal amount, int userLevel) {
        // 一般案件：股長核准即可
        if ("GENERAL".equals(caseType) && userLevel >= SUPERVISOR_LEVEL) {
            return true;
        }
        
        // 重大案件：科長核准
        if ("MAJOR".equals(caseType) && userLevel >= MANAGER_LEVEL) {
            return true;
        }
        
        // 特別案件：隊長核准
        if ("SPECIAL".equals(caseType) && userLevel >= DIRECTOR_LEVEL) {
            return true;
        }
        
        return false;
    }
}
```

#### 協同作業權限控制
```java
// 協同作業權限檢查
public boolean hasCollaborationPermission(String userId, String caseId, String collaborationType) {
    // 檢查使用者是否為指定協同單位人員
    List<String> authorizedDepts = getCollaboratingDepartments(caseId);
    String userDept = getUserDepartment(userId);
    
    if (!authorizedDepts.contains(userDept)) {
        return false;
    }
    
    // 檢查協同作業權限等級
    UserRole role = getUserRole(userId);
    CollaborationRule rule = getCollaborationRule(collaborationType);
    
    return role.getLevel() >= rule.getMinimumLevel();
}
```

### 4.3 電子簽核整合

#### 簽核歷程追蹤
```java
// 簽核歷程記錄
public void recordApprovalHistory(String caseId, ApprovalAction action) {
    ApprovalHistory history = new ApprovalHistory();
    history.setCaseId(caseId);
    history.setActionType(action.getType());
    history.setApprover(action.getApprover());
    history.setApprovalDate(new Date());
    history.setComments(action.getComments());
    history.setDecision(action.getDecision());
    
    // 記錄到IBMFYM表
    String insertSQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE, COMMENTS)"
                     + " VALUES(?, ?, ?, ?, ?, ?, ?, ?)";
    
    DBTools.executeUpdate(insertSQL, 
        history.getCaseId(),
        getCurrentYMD(),
        getCurrentTime(), 
        history.getApprover(),
        action.getResultStatus(),
        history.getApprover(),
        getCurrentYMD(),
        history.getComments()
    );
}
```

## 5. 協同作業機制深度分析

### 5.1 跨部門協同模式

#### 協同作業類型分類
```java
// 協同作業類型定義
public enum CollaborationType {
    TECHNICAL_REVIEW("技術審查", Arrays.asList("建築管理科", "都市計畫科")),
    LEGAL_CONSULTATION("法律諮詢", Arrays.asList("法制科", "訴願科")),
    ENVIRONMENTAL_ASSESSMENT("環境評估", Arrays.asList("環保局", "水利局")),
    SAFETY_INSPECTION("安全檢查", Arrays.asList("勞檢所", "消防局")),
    UTILITY_COORDINATION("公用設施", Arrays.asList("自來水公司", "電力公司", "電信公司"));
    
    private String description;
    private List<String> involvedDepartments;
    
    CollaborationType(String description, List<String> involvedDepartments) {
        this.description = description;
        this.involvedDepartments = involvedDepartments;
    }
}
```

#### 協同作業流程
```java
// 協同作業啟動流程
public void initiateCollaboration(String caseId, CollaborationType type) {
    // 1. 建立協同任務
    CollaborationTask task = new CollaborationTask();
    task.setCaseId(caseId);
    task.setType(type);
    task.setInitiator(getCurrentUser());
    task.setCreationDate(new Date());
    task.setStatus("PENDING");
    
    // 2. 通知相關部門
    for (String dept : type.getInvolvedDepartments()) {
        sendCollaborationRequest(caseId, dept, type);
    }
    
    // 3. 設定案件狀態為協同中
    updateCaseStatus(caseId, getCollaborationStatus(caseId), getCurrentUser());
    
    // 4. 設定協同期限
    setCollaborationDeadline(caseId, calculateDeadline(type));
}
```

### 5.2 協同意見整合

#### 意見收集機制
```java
// 協同意見收集
public class CollaborationOpinion {
    private String caseId;
    private String department;
    private String reviewer;
    private OpinionType opinionType;
    private String content;
    private List<String> attachments;
    private Date submissionDate;
    
    public enum OpinionType {
        APPROVE("同意"),
        CONDITIONAL_APPROVE("有條件同意"),
        OBJECT("反對"),
        NO_OPINION("無意見");
        
        private String description;
        
        OpinionType(String description) {
            this.description = description;
        }
    }
}

// 意見整合邏輯
public CollaborationResult integrateOpinions(String caseId) {
    List<CollaborationOpinion> opinions = getCollaborationOpinions(caseId);
    
    // 分析意見分布
    Map<OpinionType, Long> opinionCounts = opinions.stream()
        .collect(Collectors.groupingBy(
            CollaborationOpinion::getOpinionType,
            Collectors.counting()
        ));
    
    // 決定整合結果
    if (opinionCounts.getOrDefault(OpinionType.OBJECT, 0L) > 0) {
        return new CollaborationResult(CollaborationResult.Status.REJECTED, "存在反對意見");
    } else if (opinionCounts.getOrDefault(OpinionType.CONDITIONAL_APPROVE, 0L) > 0) {
        return new CollaborationResult(CollaborationResult.Status.CONDITIONAL, "存在附帶條件");
    } else {
        return new CollaborationResult(CollaborationResult.Status.APPROVED, "協同意見一致同意");
    }
}
```

## 6. 異常處理與品質控制

### 6.1 異常情況處理

#### 常見異常情況
```java
// 異常情況枚舉
public enum ReviewException {
    INCOMPLETE_DATA("資料不完整", "236"), // 退回狀態
    LEGAL_CONFLICT("法規衝突", "239"),   // 補正狀態
    TECHNICAL_ISSUE("技術問題", "234"),  // 協同狀態
    PERMISSION_DENIED("權限不足", null),
    DEADLINE_EXCEEDED("逾期未辦", "237"); // 撤銷狀態
    
    private String description;
    private String targetStatus;
    
    ReviewException(String description, String targetStatus) {
        this.description = description;
        this.targetStatus = targetStatus;
    }
}

// 異常處理機制
public void handleReviewException(String caseId, ReviewException exception, String handler) {
    // 記錄異常
    logException(caseId, exception, handler);
    
    // 處理異常
    if (exception.getTargetStatus() != null) {
        updateCaseStatus(caseId, exception.getTargetStatus(), handler);
        notifyStakeholders(caseId, exception);
    }
    
    // 建立處理任務
    createRemediationTask(caseId, exception);
}
```

### 6.2 品質控制機制

#### 資料品質檢查
```java
// 資料品質檢查清單
public class DataQualityChecker {
    
    public QualityCheckResult performQualityCheck(String caseId) {
        QualityCheckResult result = new QualityCheckResult();
        
        // 基本資料完整性檢查
        result.addCheck("基本資料", checkBasicDataCompleteness(caseId));
        
        // 勘查資料一致性檢查
        result.addCheck("勘查資料", checkInspectionDataConsistency(caseId));
        
        // 法規適用性檢查
        result.addCheck("法規適用", checkLegalCompliance(caseId));
        
        // 流程完整性檢查
        result.addCheck("流程完整", checkProcessCompleteness(caseId));
        
        // 文件齊備性檢查
        result.addCheck("文件齊備", checkDocumentCompleteness(caseId));
        
        return result;
    }
    
    private boolean checkBasicDataCompleteness(String caseId) {
        // 檢查必要欄位是否完整
        List<String> requiredFields = Arrays.asList(
            "DIS_B_ADDZON", "DIS_B_ADDRD", "REG_EMP", "REG_UNIT", "CHK_DATE"
        );
        
        for (String field : requiredFields) {
            String value = getCaseFieldValue(caseId, field);
            if (StringUtils.isEmpty(value)) {
                return false;
            }
        }
        return true;
    }
}
```

#### 繕校機制實作
```java
// 繕校審核流程
public void initiateProofreadingReview(String caseId, ProofreadingType type) {
    // 建立繕校任務
    ProofreadingTask task = new ProofreadingTask();
    task.setCaseId(caseId);
    task.setType(type);
    task.setAssignee(getQualityControlOfficer());
    task.setDeadline(calculateProofreadingDeadline());
    
    // 設定繕校狀態
    String proofreadingStatus = (type == ProofreadingType.DATA_REVIEW) ? "92c" : "36c";
    updateCaseStatus(caseId, proofreadingStatus, getCurrentUser());
    
    // 通知品管人員
    notifyQualityControlOfficer(task);
    
    // 暫停案件其他處理
    suspendCaseProcessing(caseId, "品質控制審核中");
}
```

## 7. 系統整合與最佳化

### 7.1 外部系統整合

#### 建管系統整合
```java
// 建管系統資料整合
public class BuildingPermitIntegration {
    
    public BuildingPermitInfo queryBuildingPermit(String address, String landNumber) {
        // 查詢建築執照資料
        BuildingPermitQuery query = new BuildingPermitQuery();
        query.setAddress(address);
        query.setLandNumber(landNumber);
        
        BuildingPermitInfo permit = buildingPermitService.query(query);
        
        if (permit != null) {
            // 比對違建與合法建築範圍
            return analyzeBuildingCompliance(permit, getCurrentViolation());
        }
        
        return null;
    }
    
    private ComplianceAnalysis analyzeBuildingCompliance(BuildingPermitInfo permit, ViolationInfo violation) {
        ComplianceAnalysis analysis = new ComplianceAnalysis();
        
        // 比對建築面積
        if (violation.getArea() > permit.getApprovedArea()) {
            analysis.addViolation("超過核准面積");
        }
        
        // 比對使用性質
        if (!permit.getApprovedUsage().contains(violation.getCurrentUsage())) {
            analysis.addViolation("違反使用性質");
        }
        
        // 比對建築高度
        if (violation.getHeight() > permit.getApprovedHeight()) {
            analysis.addViolation("超過核准高度");
        }
        
        return analysis;
    }
}
```

### 7.2 效能最佳化

#### 狀態查詢最佳化
```sql
-- 認定審核案件查詢索引
CREATE INDEX idx_review_cases ON IBMSTS 
(ACC_RLT, ACC_DATE, ACC_JOB) 
WHERE ACC_RLT IN ('232', '234', '235', '236', '237', '239', '23b', 
                  '242', '244', '245', '246', '247', '249', '24b',
                  '252', '254', '255', '256', '257', '259', '25b');

-- 協同作業查詢最佳化
CREATE INDEX idx_collaboration_status ON IBMFYM 
(CASE_ID, ACC_RLT, ACC_DATE) 
WHERE ACC_RLT IN ('234', '244', '254');

-- 品質控制查詢最佳化
CREATE INDEX idx_quality_control ON IBMFYM 
(CASE_ID, ACC_RLT, CR_DATE) 
WHERE ACC_RLT IN ('92c', '36c');
```

#### 批次處理最佳化
```java
// 批次審核處理
public class BatchReviewProcessor {
    
    public BatchProcessResult processBatch(List<String> caseIds, ReviewDecision decision) {
        BatchProcessResult result = new BatchProcessResult();
        
        // 分批處理，避免長時間鎖定
        List<List<String>> batches = partitionCases(caseIds, BATCH_SIZE);
        
        for (List<String> batch : batches) {
            try {
                Connection conn = getConnection();
                conn.setAutoCommit(false);
                
                for (String caseId : batch) {
                    processReviewDecision(caseId, decision);
                    result.addSuccess(caseId);
                }
                
                conn.commit();
            } catch (Exception e) {
                conn.rollback();
                result.addFailure(batch, e.getMessage());
            }
        }
        
        return result;
    }
}
```

## 8. 審核SOP標準作業程序

### 8.1 承辦人員認定SOP

#### 認定作業檢查清單
```markdown
## 承辦人員認定作業標準程序

### 1. 勘查資料檢視 (10分鐘)
- [ ] 確認勘查記錄完整性
- [ ] 檢視現場照片證據
- [ ] 核對測量資料準確性
- [ ] 確認勘查結論合理性

### 2. 法規適用性分析 (20分鐘)
- [ ] 查核建築法相關條文
- [ ] 確認違建認定標準
- [ ] 分析違建程度與類型
- [ ] 評估處理方式建議

### 3. 認定報告撰寫 (30分鐘)
- [ ] 撰寫違建事實認定
- [ ] 說明法律依據
- [ ] 提出處理建議
- [ ] 預估處理期程

### 4. 認定陳核作業 (5分鐘)
- [ ] 確認認定資料正確
- [ ] 執行認定陳核程序
- [ ] 通知主管審核
- [ ] 追蹤審核進度
```

### 8.2 主管審核SOP

#### 審核作業標準流程
```markdown
## 主管審核作業標準程序

### 1. 案件基本資料審查 (10分鐘)
- [ ] 檢查案件來源與類型
- [ ] 確認承辦人員資格
- [ ] 驗證程序完整性
- [ ] 核對時效要求

### 2. 認定內容審核 (20分鐘)
- [ ] 審查事實認定正確性
- [ ] 檢驗法律適用恰當性
- [ ] 評估處理建議合理性
- [ ] 確認證據充分性

### 3. 審核決策制定 (10分鐘)
- [ ] 決定審核結果
- [ ] 撰寫審核意見
- [ ] 設定後續處理方向
- [ ] 確認執行期限

### 4. 審核結果執行 (5分鐘)
- [ ] 更新案件狀態
- [ ] 通知相關人員
- [ ] 安排後續作業
- [ ] 建立追蹤機制
```

### 8.3 協同作業SOP

#### 跨部門協同標準流程
```markdown
## 跨部門協同作業標準程序

### 1. 協同需求評估 (15分鐘)
- [ ] 分析案件複雜度
- [ ] 識別需要協同的專業領域
- [ ] 確定協同作業範圍
- [ ] 評估協同必要性

### 2. 協同作業啟動 (10分鐘)
- [ ] 發送協同作業通知
- [ ] 提供案件相關資料
- [ ] 設定協同作業期限
- [ ] 建立溝通管道

### 3. 協同意見收集 (依協同單位而定)
- [ ] 追蹤各單位回覆進度
- [ ] 澄清疑義與問題
- [ ] 協調意見分歧
- [ ] 整合協同結果

### 4. 協同結果處理 (15分鐘)
- [ ] 彙整協同意見
- [ ] 分析意見一致性
- [ ] 制定綜合處理方案
- [ ] 更新案件處理方向
```

## 9. 監控與報表機制

### 9.1 審核效率監控

#### 審核時效統計
```sql
-- 認定審核時效分析
SELECT 
    CASE 
        WHEN ACC_RLT LIKE '23%' THEN '一般違建'
        WHEN ACC_RLT LIKE '24%' THEN '廣告違建'
        WHEN ACC_RLT LIKE '25%' THEN '下水道違建'
    END AS violation_type,
    ACC_RLT as status_code,
    COUNT(*) as case_count,
    AVG(DATEDIFF(
        (SELECT MIN(ACC_DATE) FROM IBMFYM f2 
         WHERE f2.CASE_ID = f1.CASE_ID AND f2.ACC_RLT LIKE SUBSTRING(f1.ACC_RLT,1,2) + 'b'),
        f1.ACC_DATE
    )) as avg_processing_days
FROM IBMFYM f1
WHERE f1.ACC_RLT IN ('232', '234', '242', '244', '252', '254')
  AND f1.ACC_DATE >= DATE_SUB(CURRENT_DATE, INTERVAL 6 MONTH)
GROUP BY violation_type, ACC_RLT
ORDER BY violation_type, ACC_RLT;
```

#### 審核品質指標
```java
// 審核品質監控
public class ReviewQualityMetrics {
    
    public QualityReport generateQualityReport(DateRange period) {
        QualityReport report = new QualityReport();
        
        // 退回率統計
        double rejectionRate = calculateRejectionRate(period);
        report.setRejectionRate(rejectionRate);
        
        // 補正率統計
        double revisionRate = calculateRevisionRate(period);
        report.setRevisionRate(revisionRate);
        
        // 協同作業比例
        double collaborationRate = calculateCollaborationRate(period);
        report.setCollaborationRate(collaborationRate);
        
        // 撤銷案件比例
        double withdrawalRate = calculateWithdrawalRate(period);
        report.setWithdrawalRate(withdrawalRate);
        
        return report;
    }
    
    private double calculateRejectionRate(DateRange period) {
        int totalCases = countCasesInPeriod(period);
        int rejectedCases = countCasesByStatus(period, Arrays.asList("236", "246", "256"));
        
        return totalCases > 0 ? (double) rejectedCases / totalCases * 100 : 0;
    }
}
```

### 9.2 績效評估機制

#### 承辦人員績效
```java
// 承辦人員績效統計
public class StaffPerformanceEvaluator {
    
    public PerformanceReport evaluateStaff(String empId, DateRange period) {
        PerformanceReport report = new PerformanceReport();
        
        // 處理案件數量
        int processedCases = countProcessedCases(empId, period);
        report.setProcessedCaseCount(processedCases);
        
        // 平均處理時間
        double avgProcessingTime = calculateAvgProcessingTime(empId, period);
        report.setAvgProcessingTime(avgProcessingTime);
        
        // 品質指標
        double qualityScore = calculateQualityScore(empId, period);
        report.setQualityScore(qualityScore);
        
        // 協同配合度
        double collaborationScore = calculateCollaborationScore(empId, period);
        report.setCollaborationScore(collaborationScore);
        
        return report;
    }
}
```

## 總結

### 認定審核流程特點

#### 系統優勢
1. **完整的多級審核** - 承辦→主管→協同的完整審核鏈
2. **靈活的協同機制** - 支援跨部門專業協同作業
3. **嚴格的品質控制** - 資料繕校與品質審核機制
4. **完善的撤銷機制** - 提供錯誤更正與流程回退功能

#### 技術特色
1. **狀態碼精細化管理** - 11個狀態碼涵蓋各種審核情況
2. **權限分級控制** - 基於職級的細緻權限管理
3. **歷程完整記錄** - IBMFYM表完整記錄所有狀態變更
4. **異常處理機制** - 完善的異常情況處理流程

#### 業務價值
1. **法律合規性** - 確保認定程序符合法律要求
2. **決策科學性** - 多方參與提升決策品質
3. **過程可追溯** - 完整的審核歷程記錄
4. **品質可控制** - 多重品質控制關卡

#### 改進建議
1. **數位化簽核** - 導入電子簽核系統
2. **AI輔助審核** - 運用AI技術輔助法規比對
3. **即時協同平台** - 建立即時協同作業平台
4. **智能品質控制** - 自動化品質檢查機制

新北市違章建築管理系統的認定審核流程體現了完整的行政程序與品質管控機制，透過多層級審核、跨部門協同、品質控制等機制，確保違建認定的準確性與合法性。系統具備良好的擴展性和整合能力，為未來數位化轉型奠定了堅實基礎。

---

**文件狀態**: ✅ 已完成  
**下一步**: 執行 T2.4.1 認定完成通知流程分析