# 運行時依賴分析 (Runtime Dependencies Analysis)

## 🎯 分析目標

深入分析系統對CodeCharge Studio運行時環境的依賴關係，識別關鍵類別庫和配置，建立依賴風險評估。

## 🔍 CodeCharge 運行時架構

### 核心依賴結構
```
化石化系統運行時依賴層次：

Application Layer (JSP/Servlet)
    ↓ 依賴
CodeCharge Runtime Layer  
    ↓ 依賴
Java Standard Libraries
    ↓ 依賴  
Application Server (Tomcat 9.0.98)
    ↓ 依賴
JVM & OS
```

### 已識別的關鍵依賴

#### 1. CodeCharge 標籤庫 (CCStags.tld)
**路徑**: `/WEB-INF/CCStags.tld`
**功能**: 提供JSP自訂標籤支援

**關鍵標籤**:
```xml
<tag>
    <name>record</name>
    <tag-class>com.codecharge.tags.RecordTag</tag-class>
</tag>
<tag>
    <name>attribute</name>
    <tag-class>com.codecharge.tags.AttributeTag</tag-class>
</tag>
```

**風險評估**: 🔴 極高風險 - 系統核心依賴

#### 2. CodeCharge 類別庫架構
**已識別的核心套件**:
```
com.codecharge.*
├── components.*         # UI元件
├── util.*              # 工具類別
├── events.*            # 事件處理
├── feature.*           # 功能特性
├── db.*                # 資料庫操作
├── validation.*        # 驗證框架
└── template.*          # 範本引擎
```

#### 3. 第三方依賴庫 (50個JAR檔案)
**核心依賴分析**:
```
activation.jar           - JavaMail支援
ant.jar                 - Apache Ant任務
c3p0-*******.jar        - 連接池管理
postgresql-42.2.18.jar  - PostgreSQL驅動
sqljdbc4.jar            - SQL Server驅動
jasperreports-6.18.0.jar - 報表引擎
commons-*.jar           - Apache Commons工具庫
log4j-*.jar             - 日誌管理
quartz-*.jar            - 排程任務
```

## 📋 依賴類別詳細分析

### 1. 資料庫連接層
**連接池配置** (site.properties):
```properties
# PostgreSQL (主要資料庫)
DBConn.driver=org.postgresql.Driver
DBConn.url=************************************
DBConn.maxconn=80
DBConn.user=postgres
DBConn.password=S!@h@202203  # ⚠️ 硬編碼風險

# SQL Server (次要資料庫)  
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.url=*********************************************************
DBConn2.maxconn=100
DBConn2.user=sa
DBConn2.password=$ystemOnlin168  # ⚠️ 硬編碼風險
```

**C3P0連接池依賴**:
- 版本: ******* (2007年發布，已過時)
- 風險: 安全漏洞、效能問題
- 替代方案: HikariCP、Tomcat JDBC Pool

### 2. 報表生成系統
**JasperReports依賴**:
```
jasperreports-6.18.0.jar  # 相對較新版本
itext-2.1.7.js5.jar       # PDF生成 (版本過舊)
itext-asian-5.2.0.jar     # 中文支援
```

**風險評估**:
- iText 2.1.7 有已知安全漏洞
- 需要升級或替換

### 3. 排程任務系統  
**Quartz Scheduler**:
```
quartz-2.2.1.jar          # 任務排程核心
quartz-jobs-2.2.1.jar     # 預定義任務
quartz.properties          # 配置檔案
```

**配置分析** (web.xml):
```xml
<context-param>
    <param-name>quartz:config-file</param-name>
    <param-value>quartz.properties</param-value>
</context-param>
```

### 4. 日誌管理系統
**Log4j依賴**:
```
log4j-1.2-api-2.17.0.jar   # 相容性API
log4j-api-2.17.0.jar       # 核心API  
log4j-core-2.17.0.jar      # 核心實作
slf4j-*.jar                # 日誌外觀
```

**安全狀態**: ✅ 已升級至2.17.0，修復了Log4Shell漏洞

## 🔧 CodeCharge 運行時行為分析

### 1. 頁面生命週期
```java
// CodeCharge頁面處理流程
Page → Model → Event → Database → Response

典型Handler處理器模式:
1. beforeInitialize()  - 頁面初始化前
2. afterInitialize()   - 頁面初始化後  
3. beforeShow()        - 顯示前處理
4. beforeOutput()      - 輸出前處理
```

### 2. 資料庫操作模式
**DBTools工具類別使用**:
```java
// 常見資料庫查詢模式
String result = DBTools.dLookUp(
    "欄位名稱", 
    "資料表名稱", 
    "WHERE條件", 
    "連接名稱"
);
```

**事務管理**:
- 依賴CodeCharge自動事務管理
- 缺乏明確的事務邊界控制
- 跨庫事務處理複雜

### 3. Session管理
**SessionStorage使用模式**:
```java
// 典型Session操作
SessionStorage.getInstance(request).setAttribute("key", value);
String value = SessionStorage.getInstance(request).getAttribute("key");
```

## 🚨 依賴風險評估

### 極高風險依賴 (🔴)
1. **CodeCharge 核心類別庫**
   - 風險: 無原廠支援，無法更新
   - 影響: 系統核心功能
   - 緩解: 無法替代，只能維護現狀

2. **硬編碼資料庫密碼**
   - 風險: 安全漏洞
   - 影響: 資料安全
   - 緩解: 立即移至環境變數

### 高風險依賴 (🟠)
1. **C3P0 連接池 (*******)**
   - 風險: 版本過舊，有安全漏洞
   - 影響: 資料庫連接穩定性
   - 緩解: 考慮升級或替換

2. **iText 2.1.7**
   - 風險: 已知安全漏洞
   - 影響: PDF生成功能
   - 緩解: 升級至安全版本

### 中等風險依賴 (🟡)
1. **Apache Commons舊版本**
   - 風險: 部分版本有漏洞
   - 影響: 工具函數可靠性
   - 緩解: 逐步升級相容版本

2. **舊版JDBC驅動**
   - 風險: 效能和安全問題
   - 影響: 資料庫連接
   - 緩解: 升級至最新版本

## 🛠️ 依賴管理策略

### 短期措施 (緊急修復)
1. **安全漏洞修復**
   ```bash
   # 檢查已知漏洞
   ./vulnerability_scan.sh
   
   # 移除硬編碼密碼
   cp site.properties site.properties.bak
   # 修改為環境變數引用
   ```

2. **關鍵依賴升級**
   - PostgreSQL驅動: 升級至42.5.x
   - SQL Server驅動: 升級至11.2.x
   - 確保向下相容性

### 中期改善 (風險降低)
1. **連接池替換評估**
   ```java
   // 評估HikariCP替代方案
   // 測試相容性和效能影響
   ```

2. **報表引擎現代化**
   - 評估iText 7.x升級
   - 考慮替代方案 (Apache PDFBox)

### 長期規劃 (架構演進)
1. **依賴隔離策略**
   - 將CodeCharge依賴隔離到特定模組
   - 新功能避免使用CodeCharge API
   - 逐步解耦關鍵功能

2. **微服務改造準備**
   - 識別可獨立的功能模組
   - 建立標準API介面
   - 準備資料遷移策略

## 📊 依賴監控和維護

### 監控指標
```bash
# 記憶體使用監控
jmap -histo:live <pid> | grep com.codecharge

# 類別載入監控  
jstat -class <pid>

# 資料庫連接監控
# 檢查C3P0連接池狀態
```

### 維護檢查清單
- [ ] 定期掃描依賴漏洞
- [ ] 監控記憶體洩漏
- [ ] 檢查連接池健康狀態
- [ ] 日誌異常分析
- [ ] 效能指標追蹤

### 應急預案
1. **CodeCharge類別庫故障**
   - 備份策略: 保留多個版本
   - 恢復程序: 快速回復機制
   - 監控機制: 即時異常偵測

2. **資料庫連接異常**
   - 連接池重啟程序
   - 備用連接配置
   - 故障轉移機制

## 🎯 下一步行動

1. **建立依賴清單** - 完整的JAR檔案分析
2. **漏洞掃描** - 使用OWASP Dependency Check
3. **相容性測試** - 升級關鍵依賴的影響評估
4. **監控機制** - 建立依賴健康度監控

---

**建立日期**: 2025-01-05  
**負責人**: 技術領導者  
**狀態**: 進行中