<%@page contentType="text/html;charset=utf-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*,java.net.*"%>
<%@page import="java.io.File,java.io.IOException"%>
<%@page import="org.apache.commons.io.FileUtils"%>
<%@page import="java.net.URLDecoder"%>
<%@page trimDirectiveWhitespaces="true" %>
<%@page import="java.sql.*, java.io.*, org.apache.commons.codec.binary.Base64, org.json.simple.* , java.nio.file.*"%>
<%@page import="java.awt.*,java.awt.Graphics,java.awt.image.BufferedImage,javax.imageio.ImageIO "%>
<%-- SSL 停用邏輯所需的特定 import --%>
<%@page import="javax.net.ssl.TrustManager"%>
<%@page import="javax.net.ssl.X509TrustManager"%>
<%@page import="java.security.cert.X509Certificate"%>
<%@page import="javax.net.ssl.SSLContext"%>
<%@page import="java.security.SecureRandom"%>
<%@page import="javax.net.ssl.HttpsURLConnection"%>
<%@page import="javax.net.ssl.HostnameVerifier"%>
<%@page import="javax.net.ssl.SSLSession"%>
<%!
    // 這是直接從您提供的 SSLHelper 類別轉換過來的 JSP 宣告版本
    // 為了避免與您可能在 WEB-INF/classes 中部署的同名類別衝突，
    // 這裡的變數和方法名稱可以加上後綴，例如 _jsp。
    // 此處為求直接對應，暫時保留原始名稱，但請注意潛在衝突。

    private static boolean trustAllDisabled_helper = true; // 使用 _helper 後綴以區分

    /**
     * 停用 SSL 憑證驗證 (極不安全，僅供測試)。
     * 此方法修改 JVM 的預設 SSL 行為。
     */
    public static void disableSslVerification_helper() { // 使用 _helper 後綴以區分
        if (trustAllDisabled_helper) {
            return;
        }
        
        // 為了避免在多執行緒環境下 (JSP 被多個請求同時存取時) 的潛在競爭條件，
        // 對於首次設定 trustAllDisabled_helper 的檢查和設定，
        // 嚴謹的作法是加上同步鎖 (synchronized block)。
        // 為求簡化，此處暫未加入同步鎖，但在高併發情況下可能不是執行緒安全的。
        // synchronized (SSLHelperJSPInternalSync.class) {
        //    if (trustAllDisabled_helper) return;
        //    ... (執行以下 try-catch 區塊) ...
        //    trustAllDisabled_helper = true;
        // }

        try {
            // 創建一個信任所有憑證的 TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    // 不進行客戶端憑證檢查
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    // 不進行伺服器憑證檢查
                }
            }};

            // 取得 SSLContext 實例
            // 您提供的範例中使用 "SSL"，現代應用建議使用 "TLS" (例如 "TLSv1.2", "TLSv1.3")
            // 但為保持與您範例一致，此處仍用 "SSL"
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // 創建一個信任所有主機名稱的 HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 信任所有主機名稱
                }
            };
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            trustAllDisabled_helper = true; // 設定旗標，表示已執行過
            System.err.println("JSP (disableSslVerification_helper): 警告：SSL 憑證驗證已禁用！這非常不安全，僅供測試使用！");

        } catch (Exception e) {
            // 異常處理
            System.err.println("JSP (disableSslVerification_helper): 禁用 SSL 驗證時發生錯誤: " + e.toString());
            // 實際應用中可能需要更細緻的錯誤處理或日誌記錄
            // e.printStackTrace(System.err); // 可以印出詳細堆疊追蹤
        }
    }

    // 如果需要同步鎖，可以宣告一個內部類別作為鎖物件
    // private static class SSLHelperJSPInternalSync {}
%>
<%!


	public void movingFile(String src, String newPath) throws IOException {
		java.nio.file.Path fileToMovePath = Paths.get(src);//Files.createFile(Paths.get(src));
		java.nio.file.Path targetPath = Paths.get(newPath);
		Files.move(fileToMovePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
	}
	
	
	public static void setImgText(File imgFile ,String outFileName ,String inText , String imgType) throws Exception {
	    final BufferedImage image = ImageIO.read(imgFile);
		int imgHeight = 0, imgWidth = 0 , textX = 0 , textY = 0;
		int font_size = 26 ,font_width = 0,font_height = 0, padding_bottom = 10;
	    Graphics g = image.getGraphics();
		
		//取得圖片長寬
		
		imgWidth = image.getWidth();
		imgHeight = image.getHeight();
		
/*
		//測試環境有哪些字體可以用	
		final String[] fontList = GraphicsEnvironment.getLocalGraphicsEnvironment().getAvailableFontFamilyNames();
		for(int i=0;i<fontList.length;i++){
			System.err.println("in10101_saveImage: fontList is " +fontList[i]);		
		}
*/
		g.setFont(new Font("微軟正黑體", Font.PLAIN , font_size));
		
        //get文字長度
        FontMetrics fm = g.getFontMetrics();
		font_width = fm.stringWidth(inText);
		font_height = 
		//文字靠右
		textX = imgWidth - font_width - padding_bottom;
		//文字置底
		textY = imgHeight - padding_bottom;
		//g.setColor(new Color(212, 212, 212));
		g.setColor(Color.white);
		if("MAP".equals(imgType) ){
			g.fillRect(textX + padding_bottom, textY - 10, font_width , 20);
		}else{
			g.fillRect(textX, textY - font_size, font_width , 60);
		}
		
		
		
		g.setColor(Color.black);
	    g.drawString(inText, textX, textY);
	    g.dispose();

	    ImageIO.write(image, "jpg", new File(outFileName));
	}
	
	
	
	
%>
<%    

//imgUrl
	String EXP_NO = request.getParameter("EXP_NO");	
	String imgUrl = request.getParameter("imgUrl");	
	String oldBaseUrl = "https://icdc.ntpc.gov.tw";
    String newBaseUrl = "http://172.16.63.52:6080";
    imgUrl = imgUrl.replace(oldBaseUrl, newBaseUrl);
	String Img_index = request.getParameter("Img_index");
	String Img_type = request.getParameter("Img_type");	
	String Y_COORDINATE = request.getParameter("Yc");	
	String X_COORDINATE = request.getParameter("Xc");	
	String fileType = "", filePath = "" ;
	String URL_PATH = request.getRequestURL().toString();
	String REAL_PATH = request.getRealPath("/");
	String SEPARATOR = System.getProperty("file.separator");
	String rootDirectory = REAL_PATH;
	rootDirectory += (REAL_PATH.substring((REAL_PATH.length() - 1), REAL_PATH.length()).equals(SEPARATOR)) ? "" : SEPARATOR;
	String CR_USER = (String)session.getAttribute("UserID");
	String picName = "";
	
	
	// http://114.35.24.194:9452/tycgin/in10101_saveImg.jsp?imgUrl=http://210.69.115.47/arcgis/rest/directories/arcgisoutput/Utilities/PrintingTools_GPServer/_ags_7dcb20b6a6f441aeb3d4c5dbdd7780bd.jpg&EXP_NO=[object%20Object] 
	
	System.err.println(": imgUrl is " + imgUrl + "::EXP_NO is" + EXP_NO);	
	if( !StringUtils.isEmpty(EXP_NO) &&  !StringUtils.isEmpty(imgUrl)){
	
		JDBCConnection jdbcConn = null;
		DbRow singleRowData = null;
		
		String picPath = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
		picPath = picPath + EXP_NO.substring(0,3) + SEPARATOR + EXP_NO + SEPARATOR + "GEO" + SEPARATOR;
		File thePicPath = new File(picPath);			
		FileUtils.forceMkdir(thePicPath);
		
		//儲存路徑
		String theStrDestDir = rootDirectory+"img\\in10101_imgUrlOutput";
		//if (!theStrDestDir.exists()) theStrDestDir.mkdirs();
		File theStockDest = new File(theStrDestDir);			
		FileUtils.forceMkdir(theStockDest);
		try{
			
			//----STEP1 暫存本機
			//下載照片
			URL source = new URL(imgUrl);
			
		
			//in10101_man_3   電子地圖
			//System.out.println("in10101_saveImg.jsp  Img_type " + Img_type + "::X_COORDINATE::" +X_COORDINATE  +  "::Y_COORDINATE::" + Y_COORDINATE);
			 if(!StringUtils.isEmpty(Img_type) && "PIT".equals(Img_type) && !StringUtils.isEmpty(X_COORDINATE) && !StringUtils.isEmpty(Y_COORDINATE) ){
				
				disableSslVerification_helper();
				filePath = theStrDestDir +"\\"+EXP_NO +"_PIT.jpg";
				File destination = new File(filePath);
				FileUtils.copyURLToFile(source, destination);
				
				
				//String outFileName = GUID + "_PIT.jpg";
				String outFileName = EXP_NO + "_PIT.jpg";
				picName = EXP_NO + "_違建位置略圖";
				File f = new File(filePath);
				
				//加文字
				String inText = "違建位置座標：" +X_COORDINATE + "E, "+ Y_COORDINATE + "N";
				//貼圖
				setImgText(f,filePath, inText , "" );
				
				if(f.exists()){		
					//movingFile(filePath, picPath + outFileName);
					
			
					File outf = new File(picPath + outFileName);	
					Files.copy(f.toPath(), outf.toPath(), StandardCopyOption.REPLACE_EXISTING);
					f.delete();					
				}

				//改從這邊 存經緯度DB
				try{
					jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
					
					String UPDATE_SQL = " update IBMCASE ";	
					UPDATE_SQL += " set  X_COORDINATE = "+X_COORDINATE;
					UPDATE_SQL += " , Y_COORDINATE = "+Y_COORDINATE;
					UPDATE_SQL += " where CASE_ID = '" +EXP_NO + "' ";
					jdbcConn.executeUpdate(UPDATE_SQL);
					
					
					String checkDB = Utils.convertToString(DBTools.dLookUp("FILENAME","IBMLIST"," SYSTID = 'IBM' and  CASE_ID = '"+EXP_NO+"' and PIC_KIND ='GEO' and PIC_SEQ = 1", "DBConn"));
					if(StringUtils.isEmpty(checkDB) || "NULL".equals(checkDB) || "null".equals(checkDB) ){
						// 新增照片資料
						String SQL_PIC = "  INSERT INTO IBMLIST(SYSTID, CASE_ID, PIC_KIND, PIC_SEQ, PICNAME, FILENAME, FILEKIND, CR_USER, CR_DATE)";
						SQL_PIC += " VALUES('IBM', '"+EXP_NO+"', 'GEO', '1', '"+picName+"', '"+outFileName+"', 'jpg', '"+CR_USER+"', to_number(to_char(CURRENT_DATE, 'yyyymmdd'),'99999999') - 19110000) ";
						jdbcConn.executeUpdate(SQL_PIC);	
					}
					
					
				}catch (Exception localException){
					System.err.println("in10101_man_3: LngLat is " + localException.toString());		
				}finally{
					if( jdbcConn != null )jdbcConn.closeConnection();
				}
				
				
				
			}
			//in10101_man_2
			else{
				
				//新增照片前先刪除原本照片
				String FILENAME = Utils.convertToString(DBTools.dLookUp("FILENAME","IBMLIST"," SYSTID = 'IBM' and  CASE_ID = '"+EXP_NO+"' and PIC_KIND ='GEO' and PIC_SEQ = 2", "DBConn"));
				
				if (!StringUtils.isEmpty(FILENAME)){
					File f_ori = new File(picPath + FILENAME);
					
					if(f_ori.exists()){
						f_ori.delete();
					}
				}
				
				disableSslVerification_helper();
				filePath = theStrDestDir +"\\"+EXP_NO+".jpg";
				File destination = new File(filePath);
				FileUtils.copyURLToFile(source, destination);
				
				String outFileName = EXP_NO + "_MAP.jpg";
				picName = EXP_NO + "_平面示意圖";
				File f = new File(filePath);
		
				//加文字
				String inText = "                                          ";
				//貼圖
				setImgText(f,filePath, inText , "MAP" );
				
				
				if(f.exists()){		
					//movingFile(filePath, picPath + outFileName);	
					File outf = new File(picPath + outFileName);	
					Files.copy(f.toPath(), outf.toPath(), StandardCopyOption.REPLACE_EXISTING);
					f.delete();					
				}
				
				
				// 塞 BMSDISOBEY_DIST
				try{
					
					String checkDB = Utils.convertToString(DBTools.dLookUp("FILENAME","IBMLIST"," SYSTID = 'IBM' and  CASE_ID = '"+EXP_NO+"' and PIC_KIND ='GEO' and PIC_SEQ = 2", "DBConn"));
					
					if(StringUtils.isEmpty(checkDB)){
						String SQL_PIC = "  INSERT INTO IBMLIST(SYSTID, CASE_ID, PIC_KIND, PIC_SEQ, PICNAME, FILENAME, FILEKIND, CR_USER, CR_DATE)";
						SQL_PIC += " VALUES('IBM', '"+EXP_NO+"', 'GEO', '2', '"+picName+"', '"+outFileName+"', 'jpg', '"+CR_USER+"', to_number(to_char(CURRENT_DATE, 'yyyymmdd'),'99999999') - 19110000) ";
						jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
						jdbcConn.executeUpdate(SQL_PIC); 
					}
					else{
						String SQL_PIC = " UPDATE IBMLIST SET FILENAME = '"+outFileName+"', FILEKIND = 'jpg', CR_USER = '"+CR_USER+"' WHERE SYSTID = 'IBM' and  CASE_ID = '"+EXP_NO+"' and PIC_KIND ='GEO' and PIC_SEQ = 2";
						jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
						jdbcConn.executeUpdate(SQL_PIC); 
					}

				}catch (Exception localException){
					System.err.println("in10101_man_3: Map error is " + localException.toString());		
				}finally{
					if( jdbcConn != null )jdbcConn.closeConnection();
				}
				
				
				
				
			}
	
		}catch(Exception e){
			System.err.println("in10101_saveImg.jsp  err " + e.toString());
		}finally{
			if( jdbcConn != null )jdbcConn.closeConnection();
		}
		
		
	}
	

	

%>
