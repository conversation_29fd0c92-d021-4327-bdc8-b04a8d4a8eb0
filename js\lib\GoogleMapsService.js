function maps_service() {}

maps_service.maps_win = null;
maps_service.url      = null;

maps_service.load = function(googlemapsserviceurl){
	maps_service.url = googlemapsserviceurl; 
}

maps_service.search = function(){ 
	let case_id         = $.trim($("#GMAP_Search_CASE_ID").val());
	let address 		= $.trim($("#GMAP_Search_ADDRESS").val()); 
	if(case_id!=''){ //Get address by case id 
		showLoading();
		$.ajax({
			type: "POST",
			url: "function_getData.jsp",
			data: {
	            boundColumn		: "ibmcase.caddress",
	            boundColumnAlias: "caddress",
	            textColumn		: "ibmcase.cadd_srch",
	            textColumnAlias	: "cadd_srch",
	            tableName		: " ibmcase",
	            whereString		: "ibmcase.case_id = '" + case_id +"'" ,
	            orderByString: "" 
	        }
		}).done(function(o) {
			if(o  && o[0].boundColumn!=undefined){
				$("#GMAP_Search_ADDRESS").val(o[0].boundColumn);//Set address
			} 
		}).always(function( qq ) { 
			address = $.trim($("#GMAP_Search_ADDRESS").val());
			maps_service.open(address); 
			
			closeLoading()
		});		
	} else if(address){
		maps_service.open(address);
	} else{
		maps_service.open('');
	} 
}

maps_service.search2 = function(case_id){
	let address = ''; 
	if(case_id!=''){ //Get address by case id 
		showLoading();
		$.ajax({
			type: "POST",
			url: "function_getData.jsp",
			data: {
	            boundColumn		: "ibmcase.caddress",
	            boundColumnAlias: "caddress",
	            textColumn		: "ibmcase.cadd_srch",
	            textColumnAlias	: "cadd_srch",
	            tableName		: " ibmcase",
	            whereString		: "ibmcase.case_id = '" + case_id +"'" ,
	            orderByString: "" 
	        }
		}).done(function(o) {
			if(o  && o[0].boundColumn!=undefined){
				address = o[0].boundColumn;//Set address
			} 
		}).always(function( qq ) { 
			maps_service.open(address); 
			
			closeLoading()
		});		
	} else if(address){
		maps_service.open(address);
	} else{
		maps_service.open('');
	} 
}

maps_service.open = function(address){
	if(maps_service.maps_win!=null) maps_service.maps_win.close();
	
	let mapservice_url 	= maps_service.url ;	
	
	if(address!=''){
		mapservice_url+="?address="+encodeURIComponent(address);
	}   

	 var width  = 1040;
	 var height = 1040;
	 var left   = screen.width-width;
	 var top    = 100;
	 var params = 'width='+width+', height='+height;
	 params += ', top='+top+', left='+left;
	 params += ', directories=no';
	 params += ', location=yes';
	 params += ', menubar=no';
	 params += ', resizable=no';
	 params += ', scrollbars=yes';
	 params += ', status=no';
	 params += ', toolbar=no';
	 
	if(maps_service.maps_win!=null){
		maps_service.maps_win.close();
	}
	 
	maps_service.maps_win = window.open(mapservice_url , "_blank", params);
	
	if (window.focus) {maps_service.maps_win.focus()}
	
} 


function showLoading() {
    $.ajaxSettings.async = false;
    $.blockUI({
        message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;存檔中, 請稍候...',
        css: {
            border: 'none',
            padding: '6px',
            backgroundColor: '#000',
            '-webkit-border-radius': '10px',
            '-moz-border-radius': '10px',
            opacity: .5,
            color: '#FFF'
        }
    });
    $.ajaxSettings.async = true;
}

function closeLoading() {
    $.unblockUI();
}

