# 協同退回狀態碼設計規格

## 🎯 需求分析

基於E1任務發現的協同作業檔案結構，需要為協同作業新增退回機制。目前協同狀態為234/244/254，協同完成為23b/24b/25b，但缺少協同退回狀態碼。

## 📋 現有協同狀態碼分析

### 一般認定協同狀態 (23x系列)
- `234`: [一般]認定送協同作業  
- `23b`: [一般]認定協同作業完成

### 廣告認定協同狀態 (24x系列)  
- `244`: [廣告物]認定送協同作業
- `24b`: [廣告物]認定協同作業完成

### 下水道認定協同狀態 (25x系列)
- `254`: [下水道]認定送協同作業
- `25b`: [下水道]認定協同作業完成

## 🔧 新增協同退回狀態碼設計

### 編碼規律
基於現有編碼規律，協同相關狀態使用：
- **4**: 送協同作業
- **b**: 協同作業完成  
- **c**: 協同退回 (新增)

### 新增狀態碼規格

| 狀態碼 | 描述 | 業務意義 |
|--------|------|----------|
| `23c` | [一般]認定協同退回 | 一般認定協同作業被退回 |
| `24c` | [廣告物]認定協同退回 | 廣告認定協同作業被退回 |  
| `25c` | [下水道]認定協同退回 | 下水道認定協同作業被退回 |

## 🔄 狀態轉換邏輯

### 協同退回流程
```
234/244/254 (送協同作業) 
       ↓ 
   [協同單位退回]
       ↓
23c/24c/25c (協同退回)
       ↓
   [承辦人重新處理]  
       ↓
231/241/251 (辦理中) 或 234/244/254 (重新送協同)
```

### 權限控制更新
需要更新 `im10101_lisHandlers.jsp` 中的保護機制：
```java
// 原有的協同保護
String UNEDITABLE_ACC_RLT = "234,244,254";

// 更新為包含協同退回狀態
String UNEDITABLE_ACC_RLT = "234,244,254,23c,24c,25c";
```

## 📝 資料庫異動規格

### INSERT SQL語句
```sql
-- 新增一般認定協同退回狀態碼
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, is_del, order_by_seq) 
VALUES ('RLT', '23c', '[一般]認定協同退回', 'N', 233);

-- 新增廣告認定協同退回狀態碼  
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, is_del, order_by_seq)
VALUES ('RLT', '24c', '[廣告物]認定協同退回', 'N', 243);

-- 新增下水道認定協同退回狀態碼
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, is_del, order_by_seq)
VALUES ('RLT', '25c', '[下水道]認定協同退回', 'N', 253);
```

### 排序規則
基於現有的order_by_seq規律：
- 23c：233 (位於234送協同和23b完成之間)
- 24c：243 (位於244送協同和24b完成之間)  
- 25c：253 (位於254送協同和25b完成之間)

## 🔗 整合影響分析

### 需要修改的檔案
1. **`im10101_man_CHandlers.jsp`**: 新增退回邏輯處理
2. **`im10101_man_C.jsp`**: 新增協同退回按鈕UI
3. **`im10101_lisHandlers.jsp`**: 更新狀態保護和顯示邏輯
4. **`case_withdraw.jsp`**: 更新退回目標狀態映射

### 業務流程影響
- **協同單位**: 可以退回案件給原承辦人
- **原承辦人**: 收到退回案件後可重新處理或修正
- **案件列表**: 顯示協同退回狀態，提供適當的操作選項

## ✅ 驗證檢查清單

- [ ] 狀態碼不與現有編碼衝突 ✅ (已確認23c/24c/25c未使用)
- [ ] 編碼規律符合系統慣例 ✅ (遵循4=送協同,b=完成,c=退回)  
- [ ] 狀態轉換邏輯合理 ✅ (協同→退回→重新處理)
- [ ] 權限控制考慮完整 ✅ (已規劃UNEDITABLE_ACC_RLT更新)
- [ ] 資料庫異動語句正確 ✅ (INSERT語句已準備)

---

**文件建立**: 2025-07-05  
**任務編號**: E2  
**狀態**: 設計完成，待實作