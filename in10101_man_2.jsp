<%--JSP Page Init @1-B0ADF600--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new in10101_man_2ServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-EFCBB0FC--%>
<%@include file="in10101_man_2Handlers.jsp"%>
<%
    if (!in10101_man_2Model.isVisible()) return;
    if (in10101_man_2Parent != null) {
        if (!in10101_man_2Parent.getChild(in10101_man_2Model.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", in10101_man_2Model);
    pageContext.setAttribute("page", in10101_man_2Model);
    in10101_man_2Model.fireOnInitializeViewEvent(new Event());
    in10101_man_2Model.fireBeforeShowEvent(new Event());

    Page curPage = (Page) in10101_man_2Model;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!in10101_man_2Model.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-5958DFFD--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>平面示意圖</title>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//Include Common JSFunctions @1-A7198D31
</script>
<script src="ClientI18N.jsp?file=Globalize.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
//End Include Common JSFunctions

//Include User Scripts @1-92024ECE
</script>
<ccs:attribute owner='page' name='scriptIncludes'/>
<script type="text/javascript">
//End Include User Scripts

//Common Script Start @1-8BFA436B
jQuery(function ($) {
    var features = { };
    var actions = { };
    var params = { };
//End Common Script Start

//BMSDISOBEY_DISTButton_DeleteOnClick Event Start @-AEED14B4
    actions["BMSDISOBEY_DISTButton_DeleteOnClick"] = function (eventType, parameters) {
        var result = true;
//End BMSDISOBEY_DISTButton_DeleteOnClick Event Start

//Confirmation Message @7-8243B274
        return confirm('Delete record?');
//End Confirmation Message

//BMSDISOBEY_DISTButton_DeleteOnClick Event End @-A5B9ECB8
        return result;
    };
//End BMSDISOBEY_DISTButton_DeleteOnClick Event End

//Event Binding @1-9B769665
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Delete)').ccsBind(function() {
        this.bind("click", actions["BMSDISOBEY_DISTButton_DeleteOnClick"]);
    });
//End Event Binding

//Plugin Calls @1-BB52EFCA
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Delete)').ccsBind(function() {
        this.bind("click", function(){ $("body").data("disableValidation", true); });
    });
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Cancel)').ccsBind(function() {
        this.bind("click", function(){ $("body").data("disableValidation", true); });
    });
//End Plugin Calls

//Common Script End @1-562554DE
});
//End Common Script End

//End CCS script
</script>
<link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="Themes/jquery.fancybox.css?v=2.1.5" media="screen">
<script src="javascript/jquery-2.1.1.min.js" type="text/javascript"></script>
<script language="JavaScript" src="javascript/jquery.blockUI.min.js" type="text/javascript"></script>
<script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
<script language="JavaScript" src="javascript/jquery.fancybox.js?v=2.1.5" type="text/javascript"></script>
<script>
var temp_date = Date.now();
//var temp_date ="10710301117";

document.writeln('<script language="JavaScript" type="text\/javascript" src="javascript//tgos//Framework.js"><\/script>');
document.writeln('<script language="JavaScript" type="text\/javascript" src="javascript//tgos//AjaxAgent.js"><\/script>');      
document.writeln('<link rel="stylesheet" href="https:////js.arcgis.com//3.24//esri//css//esri.css">');
document.writeln('<script language="JavaScript" type="text\/javascript" src="https:////js.arcgis.com//3.24//"><\/script>');   
            
document.writeln('<link rel="stylesheet" type="text\/css" href="in10101_polygonSearch.css?' + temp_date + '">');
document.writeln('<script language="JavaScript" type="text\/javascript" charset="big5" src="in10101_polygonSearch.js?' + temp_date + '"><\/script>');
    
    
var ezekMap, currentCoordinate = [];
var _lng , _lat;
var gsvc, wgs84SR, twd97SR, webMercatorSR;
var picLocMarker, markerPic, savePoint;
var sys_arcgisMapServiceURL = location.protocol+"//limit.ntpc.gov.tw/arcgis/rest/services/";
// 新北測試 token
//var token="rJzVxzRxWwAtW_nmhwMqpNqiWfehMOTiQx8MPL1JUkbrWJRuCz-n8uCADya4EZLD";
//var token = "AzS0XMKer0W7BBXaHWd5gICJxZ7ggrXiFZ49OV8i5Qfr0DIdS-u0Pkw3eV0ABo61";
//var token = "gwQ-sLeHW7LEBLhRhcVxOT4Xw_RbmpwqaNhNm6h4NdAzRvclvDCJUR2bZsB5Oqwj"; 20230508失效
//var token = "447oNhHjoHa57PAsmntmpqhSvdOItoqELmctHXufN0aU0QVW4__vAcc1liVnAsEs";
var token = "17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token="+token, 
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token="+token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer", 
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token="+token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token="+token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";

function ezek_InitMap() {
        require([
                "esri/map", 
                "esri/dijit/Print",             
                "esri/geometry/Point", 
                "esri/geometry/webMercatorUtils", 
                "esri/graphic", 
                "esri/symbols/PictureMarkerSymbol", 
                "esri/symbols/SimpleMarkerSymbol",
                "esri/toolbars/edit", 
                "esri/dijit/Scalebar",
                'esri/tasks/PrintTemplate',

                        "esri/SpatialReference",
                        "esri/tasks/GeometryService",
                "dojo/_base/event",
                "dojo/dom", 
                "dojo/on", 
                "dojo/parser",
                "dojo/domReady!"
        ], function(Map,Print, Point
                , WebMercatorUtils, Graphic, PictureMarkerSymbol, SimpleMarkerSymbol , Edit,Scalebar,PrintTemplate
               /* , PrintParameters, PrintTask, lang*/
                ,SpatialReference ,GeometryService , event, dom, on, parser) {
               parser.parse();
                                
                        var WGS84_point = new Point();
                        
                        markerPic = new esri.symbol.SimpleMarkerSymbol(esri.symbol.SimpleMarkerSymbol.STYLE_CIRCLE, 12,null, new dojo.Color([255, 0, 0, 1]));
                        esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";
                
                        //設定座標轉換服務
                        var sys_arcgisMapService = location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/";
                        gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");
                        wgs84SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '4326' />);            //WGS84
                        twd97SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102443' />);          //TWD_97_TM2
                        webMercatorSR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102100' />);    //WGS 1984 Web Mercator Projection
                        
                ezekMap = new Map("map_canvas", {
                            logo: false,
                            slider: true,
                            sliderStyle: "small",
                            sliderPosition: "top-left",
                            spatialReference: {
                                    wkid: 102443
                            },
                     zoom: 10,
                     minZoom:3, 
                        maxZoom: 13

                    });
                  

                        //var tiledMap = new esri.layers.ArcGISDynamicMapServiceLayer(mapUrl, { id: "baseMap" });
                        var tiledMap = null,TgosLayer_F2 = null;
                        tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
                                "opacity": 1,
                                id: normalTileName2
                        });
                ezekMap.addLayer(tiledMap, 0);
                        
                        // 1: 1/1000以上 電子地圖
                        TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
                                "opacity": 1,
                                id: normalTileName1
                        });
                        ezekMap.addLayer(TgosLayer_F2, 1);
                        

                var plate = new PrintTemplate();
                // plate.layout = plate.label = "A4 Landscape";
                // plate.layoutu = "MAP_ONLY";
                // plate.outScale = 1000000; //Zoom 遠近
                plate.showLabels = true;
                plate.format = "jpg";
                //設定true可自定義輸出範圍與比例，若false則輸出與ezekMap相同範圍比例的圖。 true為預設。
                // plate.preserveScale = false;  
                
                plate.exportOptions = { //必須是 MAP_ONLY 狀態
                        width: 756,
                        height: 536,
                        dpi: 90
                  };
                 
                  
                var printer = new Print({
                    map: ezekMap,
                    "templates": [plate],
                    // url:  sys_arcgisMapService + "Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task"
                    
                        url: location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/Utilities/PrintingTools/GPServer/Export%20Web%20Map%20Task"
                }, dom.byId("printButton"));
                printer.startup();
                printer.on('print-start',function(){
                        showLoading();    
                });
                //注意  arcgis 版本 3.14會另開示窗
                printer.on('print-complete',function(evt){
                        console.log(evt); 
                        //window.location.assign("ug10101_fileDownload.jsp?imgUrl="+evt.result.url+"&info="+encodeURIComponent(JSON.stringify(info))); 
                       
                        var EXP_NO = $("[name=EXP_NO]").val();
                        //console.log(EXP_NO); 
                        window.location.assign("in10101_saveImg.jsp?imgUrl="+evt.result.url+"&EXP_NO="+EXP_NO ); 
                        closeLoading();
                        closeFancybox();
                        
                });
               
                
                //硬改 改文字
                $("#dijit_form_Button_0_label").html("存&nbsp;&nbsp;&nbsp;&nbsp;檔")  ; 
                 $("#printButton").find(".dijitReset .dijitToggleButtonIconChar").hide();
                 $("#printButton").find("input.dijitOffScreen").hide();
                
                
                on(ezekMap, "load", function() {
                        // initialize; store the coordinate before any editing
                        currentCoordinate = [ parseFloat ($("[name=lng]").val()), parseFloat($("[name=lat]").val())];
                        
                       
                       zoomToCenter( currentCoordinate[0],currentCoordinate[1]  );
                       $("#showLatLng").text("違建位置座標：" + currentCoordinate[0] + " E, " + currentCoordinate[1] + " N");
                       //var point = new Point(currentCoordinate[0],currentCoordinate[1] ,ezekMap.spatialReference );
                       //ezekMap.centerAndZoom(point, 17);
                       //ezekMap.centerAndZoom(point, 0.006);
                      
                       
                       //啟用工具
                                getToolBar();
          
                });
                // //getMaxZoom(), getMinZoom()  , getLevel(), getZoom()
                /*
                ezekMap.on("extent-change", function(evt) {
        
                         console.log(ezekMap.getLevel());
                         console.log(ezekMap.getZoom());
                          console.log(ezekMap.getMaxZoom());
                         console.log(ezekMap.getMinZoom());
                        });
                                */
                 //加入比例尺顯示
                                var scalebar = new Scalebar({
                                        map: ezekMap,
                                        scalebarUnit: "metric"
                                });
                                esri.config.defaults.map.slider = {
                                        right: "165px",
                                        bottom: null,
                                        width: "200px",
                                        height: null
                                };
                                $(".esriScalebar").css("bottom", "50px").css("z-index", "50").css("position", "relative");       
                       
        });//END  require
}//END ezek_InitMap()

 function closeFancybox() {
        parent.$.fancybox.close();     
  }
 

function zoomToCenter(lng,lat) 
{
        require([
                "esri/geometry/Point",
                "esri/symbols/PictureMarkerSymbol"
        ], function(Point,PictureMarkerSymbol) {
                
                var _CallBack_Zoom = function(outputpoint) 
                {
                       // var point = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        savePoint = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        //console.log(outputpoint.x, outputpoint.y);
                        //picLocMarker = new PictureMarkerSymbol('img/searchLocation.png', 20, 20);
                        ezekMap.graphics.add(new esri.Graphic(savePoint, markerPic));
                       
                        ezekMap.centerAndZoom(savePoint, 10); 
                     
                };
                convertLatLng(lat,lng,4326,102443,_CallBack_Zoom);
        });
}

function convertLatLng(_lat,_lng,_incoord,_outcoord,_callback) 
{
        require([
                "esri/tasks/ProjectParameters",
                "esri/symbols/PictureMarkerSymbol"
        ],function(ProjectParameters,PictureMarkerSymbol) { 

                var inlat = _lat;
                var inlon = _lng;
                var incoord = _incoord;
                var outcoord = _outcoord;

                if (isNaN(inlat) || isNaN(inlon)) {
                        //alert("Please enter valid numbers");
                } else {
                        var inSR = new esri.SpatialReference({
                                wkid: incoord
                        });
                        var outSR = new esri.SpatialReference({
                                wkid: outcoord
                        });
                        var geometryService = new esri.tasks.GeometryService("https://utility.arcgisonline.com/ArcGIS/rest/services/Geometry/GeometryServer");
                        var inputpoint = new esri.geometry.Point(inlon, inlat, inSR);
                        var PrjParams = new esri.tasks.ProjectParameters();
                        PrjParams.geometries = [inputpoint];
                        PrjParams.outSR = outSR;

                        geometryService.project(PrjParams, function (outputpoint) {
                                // console.log('Conversion completed. Input SR: ' + incoord + '. Output SR: ' + outcoord);
                                _callback(outputpoint[0]);
                        });
                }
        });
}


        function showLoading(){
                $.ajaxSettings.async = false;                   
                $.blockUI({message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;存檔中, 請稍候...', css:{border:'none', padding:'6px', backgroundColor:'#000', '-webkit-border-radius': '10px', '-moz-border-radius': '10px', opacity: .5, color:'#FFF'}});
                $.ajaxSettings.async = true;
        }

        function closeLoading(){
                $.unblockUI(); 
                //setTimeout( function(){ $.unblockUI(); }, 1500);
        
        }
        
        
   window.onload = function (){
        ezek_InitMap();
        
        }         
            
                
</script>
<style type="text/css">
html body table{
margin:0;
}
.esriSimpleSlider { left: 10px; top: 10px; }
#map_canvas { width: 760px; height: 540px; border: 2px solid #bbb; }

/* make all dijit buttons the same width */
      .dijitButton .dijitButtonNode, #drawingWrapper, #printButton {
        width: 70px;
      }
      .esriPrint {
        padding: 0;
      }
#infoBoxSwitch { left:19px; top:90px; position:absolute; z-index:10; }
#infoBoxSwitch:hover img { opacity: 0.7;}
#infoBoxSwitch2 { left:19px; top:124px; position:absolute; z-index:10; }
#infoBoxSwitch2:hover img { opacity: 0.7;}   
body{font-size: 16px; font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif; }
.divShowLatLng{float:left;margin-top: -6px;font-size:14px;}
</style>
</head>
<body>
<ccs:record name='BMSDISOBEY_DIST'>
<form id="BMSDISOBEY_DIST" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
  <table class="table" style="MARGIN-BOTTOM: 0px" cellspacing="0" cellpadding="0">
    <ccs:error_block>
    <tr id="BMSDISOBEY_DISTErrorBlock" class="Error">
      <td colspan="2"><ccs:error_text/></td>
    </tr>
    </ccs:error_block>
    <tr class="Controls">
      <td style="VERTICAL-ALIGN: top">
        <div>
          <div id="map_div" style="MARGIN: auto">
            <div id="infoBoxSwitch" class="infoBoxSwitch" name="Polygon">
              <img id="infoBoxImg" title="繪製多邊形" class="toolBoxImg" border="0" alt="繪製多邊形" src="img/mapButton_area_off.png">
            </div>
 
            <div id="infoBoxSwitch2" class="infoBoxSwitch" name="clear">
              <img id="infoBoxImg2" title="清除" class="toolBoxImg" border="0" alt="清除" src="img/mapButton_clean_off.png">
            </div>
 
            <div id="map_canvas">
            </div>
          </div>
        </div>
      </td> 
      <td style="VERTICAL-ALIGN: top"><label style="font-weight: 100;">操作說明： </label><br>
        <label style="font-weight: 100;">1.於圖面按著滑鼠左鍵然後移動滑鼠即可平移地圖</label><br>
        <label style="font-weight: 100;">2.於圖面滾動滑鼠滾輪即可縮放地圖</label><br>
        <label style="font-weight: 100;">3.點選左上角繪製多邊形工具按鈕可開始標示違建區域範圍</label><br>
        <label style="font-weight: 100;">4.按存檔按鈕可將目前圖面儲存為平面示意圖</label><br>
      </td> 
      <tr class="Bottom">
        <td style="TEXT-ALIGN: right" colspan="2">
          <div class="divShowLatLng">
            <span id="showLatLng">&nbsp;</span>
          </div>
 
          <div id="printButton" class="btn btn-primary">
          </div>
          <ccs:button name='Button_Insert'><input type="submit" id="BMSDISOBEY_DISTButton_Insert" class="btn btn-primary" style="DISPLAY: none" alt="存&nbsp;&nbsp;&nbsp;&nbsp;檔1" value="存&nbsp;&nbsp;&nbsp;&nbsp;檔1" name="<ccs:control name='Button_Insert' property='name'/>"></ccs:button>
          <ccs:button name='Button_Update'><input type="submit" id="BMSDISOBEY_DISTButton_Update" class="btn btn-primary" style="DISPLAY: none" alt="存&nbsp;&nbsp;&nbsp;&nbsp;檔2" value="存&nbsp;&nbsp;&nbsp;&nbsp;檔2" name="<ccs:control name='Button_Update' property='name'/>"></ccs:button>
          <ccs:button name='Button_Delete'><input type="submit" id="BMSDISOBEY_DISTButton_Delete" class="Button" style="DISPLAY: none" alt="Delete" value="Delete" name="<ccs:control name='Button_Delete' property='name'/>"></ccs:button>
          <ccs:button name='Button_Cancel'><input type="submit" onclick="closeFancybox();" id="BMSDISOBEY_DISTButton_Cancel" class="btn btn-success" alt="取&nbsp;&nbsp;&nbsp;&nbsp;消" value="取&nbsp;&nbsp;&nbsp;&nbsp;消" name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button><input type="hidden" id="BMSDISOBEY_DISTEXP_NO" value="<ccs:control name='EXP_NO'/>" name="<ccs:control name='EXP_NO' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTlng" value="<ccs:control name='lng'/>" name="<ccs:control name='lng' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTlat" value="<ccs:control name='lat'/>" name="<ccs:control name='lat' property='name'/>"></td>
      </tr>
    </table>
  </form>
  </ccs:record><br>
  </body>
  </html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-ACC95954--%>
<%in10101_man_2Model.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-43BFA7AA--%>
<%in10101_man_2Model.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

