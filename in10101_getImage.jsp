<%@page contentType="image/jpeg; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils,java.net.*"%>
<%@page import="java.io.*, org.apache.commons.codec.binary.Base64, java.awt.*, java.awt.image.*,javax.imageio.ImageIO, javax.swing.ImageIcon"%>
<%@page import="java.sql.*, javax.sql.*, java.util.regex.Pattern"%>
<%@page trimDirectiveWhitespaces="true" %>
<%
    // http://building.tycg.gov.tw/tycgin/in10101_getImage.jsp?EXP_NO=107090039&Img_index=1
    String CLASS_NAME = "~~in10101_getImage.jsp~~~";
    // 【安全修復】新增輸入驗證和清理
    String EXP_NO = request.getParameter("EXP_NO");
    String Img_index = request.getParameter("Img_index");
    String Img_kind = StringUtils.isEmpty(request.getParameter("Img_kind")) ? "DRW" : request.getParameter("Img_kind");
    String PROGRAM_ID = (String) request.getSession().getAttribute("PROGRAM_ID");
    String isOri = request.getParameter("isOri");
    
    // 【安全驗證】檢查必要參數和格式
    if (StringUtils.isEmpty(EXP_NO) || !Pattern.matches("^[0-9]{9,12}$", EXP_NO)) {
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid EXP_NO parameter");
        return;
    }
    if (!StringUtils.isEmpty(Img_index) && !Pattern.matches("^[0-9]+$", Img_index)) {
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid Img_index parameter");
        return;
    }
    if (!Pattern.matches("^[A-Z]{2,5}$", Img_kind)) {
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Invalid Img_kind parameter");
        return;
    }
    
    String Zzip = request.getParameter("Zzip");
    
    String CONNECTION_NAME = "DBConn";
    String SEPARATOR = System.getProperty("file.separator");
    String imgDirectory = application.getRealPath("/") + "img" + SEPARATOR;
    out.clear(); 
    out = pageContext.pushBody(); 

    OutputStream o = null;
    FileInputStream fis = null;

    try {
        System.out.println(CLASS_NAME + "~~Starting to process image~~");

        if (!StringUtils.isEmpty(EXP_NO)) {
            System.out.println(CLASS_NAME + "~~EXP_NO is: " + EXP_NO + "~~");

            String picPath = "";
            String picPath_out = "";
            String outFileName = "", PICNAME = "";
            
            String DELIMITER = System.getProperty("file.separator");
            picPath = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
            System.out.println(CLASS_NAME + "~~picPath is: " + picPath + "~~");
            
            // 【安全修復】防止路徑遍歷攻擊
            String safeEXP_NO = EXP_NO.replaceAll("[^0-9]", "");
            String actualDirectory = picPath + safeEXP_NO.substring(0, 3) + DELIMITER + safeEXP_NO + DELIMITER + "GEO" + DELIMITER;
            File f = null;

            if ("MAP".equals(Img_kind)) {
                // 【安全修復】使用參數化查詢防止SQL注入
                DBConnectionManager dbcm = null;
                Connection conn = null;
                PreparedStatement pstmt = null;
                ResultSet rs = null;
                try {
                    dbcm = DBConnectionManager.getInstance();
                    conn = dbcm.getConnection("DBConn");
                    String sql = "SELECT FILENAME FROM IBMLIST WHERE SYSTID = ? AND CASE_ID = ? AND PIC_KIND = ? AND PIC_SEQ = ?";
                    pstmt = conn.prepareStatement(sql);
                    pstmt.setString(1, "IBM");
                    pstmt.setString(2, EXP_NO);
                    pstmt.setString(3, "GEO");
                    pstmt.setInt(4, 2);
                    rs = pstmt.executeQuery();
                    if (rs.next()) {
                        outFileName = rs.getString("FILENAME");
                    }
                } catch (SQLException e) {
                    System.err.println("SQL Error: " + e.getMessage());
                    response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return;
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                    if (pstmt != null) try { pstmt.close(); } catch (SQLException ignored) {}
                    if (conn != null && dbcm != null) try { dbcm.freeConnection("DBConn", conn); } catch (Exception ignored) {}
                }    
                f = new File(actualDirectory + outFileName);
            } else if ("PIT".equals(Img_kind)) {
                outFileName = EXP_NO + "_PIT.jpg";
                f = new File(actualDirectory + EXP_NO + "_PIT.jpg");
            } else {
                // 【安全修復】防止路徑遍歷攻擊  
                String safeImgKind = Img_kind.replaceAll("[^A-Z]", "");
                actualDirectory = picPath + safeEXP_NO.substring(0, 3) + DELIMITER + safeEXP_NO + DELIMITER + safeImgKind + DELIMITER;
                // 【安全修復】使用參數化查詢防止SQL注入
                DBConnectionManager dbcm = null;
                Connection conn = null;
                PreparedStatement pstmt = null;
                ResultSet rs = null;
                try {
                    dbcm = DBConnectionManager.getInstance();
                    conn = dbcm.getConnection("DBConn");
                    String sql = "SELECT FILENAME, PICNAME ||'.' ||FILEKIND as FULLNAME FROM IBMLIST WHERE SYSTID = ? AND CASE_ID = ? AND PIC_KIND = ? AND PIC_SEQ = ?";
                    pstmt = conn.prepareStatement(sql);
                    pstmt.setString(1, "IBM");
                    pstmt.setString(2, EXP_NO);
                    pstmt.setString(3, Img_kind);
                    pstmt.setInt(4, Integer.parseInt(Img_index));
                    rs = pstmt.executeQuery();
                    if (rs.next()) {
                        outFileName = rs.getString("FILENAME");
                        PICNAME = rs.getString("FULLNAME");
                    }
                } catch (SQLException | NumberFormatException e) {
                    System.err.println("SQL Error: " + e.getMessage());
                    response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    return;
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException ignored) {}
                    if (pstmt != null) try { pstmt.close(); } catch (SQLException ignored) {}
                    if (conn != null && dbcm != null) try { dbcm.freeConnection("DBConn", conn); } catch (Exception ignored) {}
                }

                if ("Y".equals(isOri)) {
                    int pathIndex = outFileName.lastIndexOf(".");
                    String outFileName_ORI = "";
                    outFileName_ORI = outFileName.substring(0, pathIndex) + "_ORI" + outFileName.substring(pathIndex);
                    outFileName = outFileName_ORI;
                }
                
                f = new File(actualDirectory + outFileName);
            }

            String fileContentType = "";
            if (outFileName != null) {
                fileContentType = outFileName.substring(outFileName.lastIndexOf(".")).toLowerCase();
                System.out.println(CLASS_NAME + "~~fileContentType is: " + fileContentType + "~~");

                switch (fileContentType) {
                    case ".pdf":
                        response.setContentType("application/pdf");
                        break;
                    case ".jpg":
                        response.setContentType("image/jpeg");
                        break;
                    case ".png":
                        response.setContentType("image/png");
                        break;
                    case ".tif":
                        response.setContentType("image/tiff");
                        break;
                    case ".doc":
                    case ".docx":
                        response.setContentType("application/msword");
                        response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(PICNAME, "UTF-8") + "\"");
                        break;
                    case ".xls":
                    case ".xlsx":
                        response.setContentType("application/vnd.ms-excel");
                        response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(PICNAME, "UTF-8") + "\"");
                        break;
                    default:
                        // response.setContentType( "image/png" );
                        break;
                }
            }

            o = response.getOutputStream();
            if (f.exists()) {
                System.out.println(CLASS_NAME + "~~File exists: " + f.getAbsolutePath() + "~~");

                if ("Y".equals(Zzip)) {
                    // 取得原始照片
                    BufferedImage oriimage = null;

                    // 圖片經過處理過 直接ImageIO.read 會出現一層紅色疊在上面
                    // 改用 Toolkit 處理
                    java.awt.Image image = Toolkit.getDefaultToolkit().getImage(actualDirectory + outFileName);

                    // System.err.println( "~~in10101_getImage~~" + picPath_out + outFileName );

                    if (!(image instanceof BufferedImage)) {
                        // This code ensures that all the pixels in the image are loaded
                        image = new ImageIcon(image).getImage();
                        BufferedImage bimage = new BufferedImage(image.getWidth(null), image.getHeight(null), BufferedImage.TYPE_INT_RGB);
                        Graphics g = bimage.createGraphics();
                        g.drawImage(image, 0, 0, null);
                        g.dispose();
                        image.flush(); // 避免CASH
                        oriimage = bimage;
                    } else {
                        oriimage = (BufferedImage) image;
                    }

                    // 判斷是否有透明圖層
                    if (oriimage.isAlphaPremultiplied()) {
                        // System.err.println(CLASS_NAME + "~~isAlphaPremultiplied~~" );
                    }

                    // 取得原始照片大小
                    boolean isMoreThan1000 = true;
                    boolean isNeedZip = false;
                    int imageW = oriimage.getWidth();
                    int imageH = oriimage.getHeight();
                    int maxWH = 1200;
                    double rate = 0.9;

                    // 重新設定比例
                    while (isMoreThan1000) {
                        if (imageW < maxWH || imageH < maxWH) {
                            isMoreThan1000 = false;
                        } else {
                            isNeedZip = true;
                            imageW = (int) (imageW * rate);
                            imageH = (int) (imageH * rate);
                        }
                    }

                    // 大檔 壓縮後輸出
                    if (isNeedZip) {
                        int img_type = oriimage.getType();
                        if (img_type == 0) { img_type = BufferedImage.TYPE_INT_ARGB; }
                        // 新建要輸出的空白Image，大小格式與輸入來源相同
                        BufferedImage outputImage = new BufferedImage(imageW, imageH, img_type);
                        // 照片轉換成幾何Graphics2D類
                        Graphics2D g2d = outputImage.createGraphics();
                        // 抗鋸齒
                        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                        // 進行繪圖
                        g2d.drawImage(oriimage, 0, 0, imageW, imageH, null);
                        g2d.dispose();
                        // 取得新圖片並輸出
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        ImageIO.write(outputImage, "jpg", baos);
                        byte[] imgData1 = baos.toByteArray();
                        baos.close();

                        o.write(imgData1);
                    } else {
                        fis = new FileInputStream(f);
                        byte[] imgData1 = IOUtils.toByteArray(fis);
                        fis.close();
                        o.write(imgData1);
                    }
                } else {
                    fis = new FileInputStream(f);
                    byte[] imgData1 = IOUtils.toByteArray(fis);
                    fis.close();
                    o.write(imgData1);
                }
            } else {
                System.out.println(CLASS_NAME + "~~File does not exist: " + f.getAbsolutePath() + "~~");

                String noDrawImage_PIC = "";
                if ("MAP".equals(Img_kind)) {
                    noDrawImage_PIC = "NoImage_area.png";
                } else if ("PIT".equals(Img_kind)) {
                    noDrawImage_PIC = "NoImage_loc.png";
                } else {
                    noDrawImage_PIC = "noDrawImage.png";
                }

                File f1 = new File(imgDirectory + noDrawImage_PIC);
                fis = new FileInputStream(f1);
                byte[] imgData1 = IOUtils.toByteArray(fis);
                fis.close();
                o.write(imgData1);
            }
        } else {
            System.out.println(CLASS_NAME + "~~EXP_NO is empty~~");
        }
    } catch (Exception err) {
        System.err.println("in10101_getImage: error is " + err.toString());
    } finally {
        if (o != null) o.flush();
        if (o != null) o.close();
        if (fis != null) fis.close();
    }
%>
