# 新北市違章建築管理系統 - 完整系統架構分析

## 📅 分析日期：2025-07-06

---

## 🏗️ 系統總體架構圖

```
┌─────────────────────────────────────────────────────────────────────┐
│                     新北市違章建築管理系統                              │
│                  (30年化石系統 - Legacy System)                        │
└─────────────────────────────────────────────────────────────────────┘
                                    │
        ┌───────────────────────────┴───────────────────────────┐
        │                                                       │
        ▼                                                       ▼
┌───────────────────┐                                 ┌─────────────────┐
│  Presentation     │                                 │  Mobile Access  │
│     Layer         │                                 │   (Responsive)  │
│                   │                                 └─────────────────┘
│ ┌───────────────┐ │
│ │ JSP Pages     │ │    ┌─────────────────────────────────────────┐
│ │ (484 files)   │ │    │         Application Server             │
│ └───────────────┘ │    │        Apache Tomcat 9.0.98           │
│ ┌───────────────┐ │    │                                       │
│ │ JavaScript    │ │    │ ┌───────────────────────────────────┐ │
│ │ (233 files)   │ │    │ │     CodeCharge Runtime Layer      │ │
│ └───────────────┘ │    │ │    (com.codecharge.* packages)    │ │
└───────────────────┘    │ └───────────────────────────────────┘ │
         │               │ ┌───────────────────────────────────┐ │
         │               │ │      Servlet/Filter Layer         │ │
         ▼               │ │  - ContentSecurityPolicyFilter    │ │
┌───────────────────┐    │ │  - PanelFilter & IncludeFilter   │ │
│  Business Logic   │    │ │  - InitServlet (site.properties) │ │
│     Layer         │    │ └───────────────────────────────────┘ │
│                   │    └─────────────────────────────────────────┘
│ ┌───────────────┐ │                          │
│ │ Handlers.jsp  │ │                          ▼
│ │ (Business)    │ │    ┌─────────────────────────────────────────┐
│ └───────────────┘ │    │          Database Layer                 │
│ ┌───────────────┐ │    │                                       │
│ │ Java Classes  │ │    │ ┌───────────────┐ ┌─────────────────┐ │
│ │ (com.ezek.*)  │ │    │ │  PostgreSQL   │ │   SQL Server    │ │
│ └───────────────┘ │    │ │   (Main DB)   │ │  (GIS System)   │ │
└───────────────────┘    │ │               │ │                 │ │
         │               │ │ localhost:5432│ │ **************  │ │
         │               │ │   /bms        │ │ :2433/ramsGIS   │ │
         ▼               │ └───────────────┘ └─────────────────┘ │
┌───────────────────┐    │         │                   │         │
│   Data Access     │    │         └───────────┬───────┘         │
│     Layer         │    │                     │                 │
│                   │    │              ┌──────▼──────┐          │
│ ┌───────────────┐ │    │              │ Connection  │          │
│ │ C3P0 Pool    │ │◄───┼──────────────│    Pool     │          │
│ │ Manager      │ │    │              │ (C3P0 0.9.1)│          │
│ └───────────────┘ │    │              └─────────────┘          │
└───────────────────┘    └─────────────────────────────────────────┘
```

---

## 🎯 核心技術架構

### 1. **CodeCharge Studio 三層架構**

#### **標準模式 (Three-File Tiered Pattern)**
```
┌─────────────────────────────────────────────────────────┐
│                   CodeCharge 頁面模組                    │
├─────────────────────────────────────────────────────────┤
│ 呈現層:  *_man.jsp / *_lis.jsp                         │
│         - HTML + 自訂標籤 (CCStags)                     │
│         - UI 元件渲染                                   │
├─────────────────────────────────────────────────────────┤
│ 設定層:  *_man.xml / *_lis.xml                         │
│         - 資料來源定義                                  │
│         - 元件配置                                      │
│         - 事件綁定                                      │
├─────────────────────────────────────────────────────────┤
│ 邏輯層:  *_Handlers.jsp                                 │
│         - 業務邏輯處理                                  │
│         - 資料庫 CRUD                                   │
│         - 事件處理器                                    │
└─────────────────────────────────────────────────────────┘
```

### 2. **請求處理生命週期**

```
User Request
    │
    ▼
Apache Tomcat 9.0.98
    │
    ▼
Filter Chain:
    ├── ContentSecurityPolicyFilter (安全防護)
    ├── PanelFilter (面板處理)
    └── IncludeFilter (頁面包含)
    │
    ▼
JSP Processing:
    ├── 1. 解析 XML 設定檔
    ├── 2. 初始化 CodeCharge Bean
    ├── 3. 執行 Handlers 邏輯
    ├── 4. 查詢資料庫
    └── 5. 渲染 HTML 回應
```

### 3. **Web 應用程式配置 (web.xml)**

```xml
核心配置:
- Web Application 2.3 規範 (2001年標準)
- Quartz Scheduler 整合 (排程任務)
- CodeCharge 初始化 Servlet
- 三個主要 Filter (安全、面板、包含)
- 自訂標籤庫 (/ccstags → CCStags.tld)
```

---

## 💾 資料庫架構

### 1. **雙資料庫系統**

#### **主要資料庫 - PostgreSQL**
```
連線配置:
- URL: ************************************
- 使用者: postgres
- 密碼: S!@h@202203 (硬編碼風險)
- 最大連線數: 80
- 連線逾時: 300秒
```

#### **次要資料庫 - SQL Server (GIS系統)**
```
連線配置:
- URL: *********************************************************
- 使用者: sa
- 密碼: $ystemOnlin168 (硬編碼風險)
- 最大連線數: 100
- 連線逾時: 600秒
```

### 2. **核心資料表結構**

```
主要實體關係:
                    ibmcase (主案件表)
                        │
        ┌───────────────┼───────────────┬─────────────┐
        ▼               ▼               ▼             ▼
    ibmcslan        ibmcsprj        ibmdisnm      ibmfym
   (土地資料)       (專案資料)      (關係人)    (處理記錄)
        │               │               │             │
        └───────────────┴───────────────┴─────────────┘
                                │
                    ┌───────────┼───────────┐
                    ▼           ▼           ▼
                ibmlist    ibmsts    ibmlawfee
               (附件)    (狀態)     (法務收費)
```

### 3. **系統參數表 (IBMCODE)**

```
核心代碼類型統計:
- 總代碼類型: 78種
- 總參數數量: 1,500+ 個
- 核心業務代碼: 8種 (RLT, JBTL, STA等)
- 重要業務代碼: 15種
- b_系列分類代碼: 42種
```

---

## 🔧 系統元件架構

### 1. **CodeCharge 標籤庫 (CCStags.tld)**

```xml
核心標籤元件:
├── <ccstags:record> - 資料記錄容器
├── <ccstags:grid> - 資料網格顯示
├── <ccstags:form_*> - 表單相關標籤
├── <ccstags:error_*> - 錯誤處理標籤
├── <ccstags:attribute> - 屬性設定
└── <ccstags:navigator> - 分頁導航
```

### 2. **第三方依賴庫 (50個JAR)**

#### **核心框架**
- CodeCharge Runtime (com.codecharge.*)
- C3P0 連接池 (******* - 需升級)
- Quartz Scheduler (2.2.1)

#### **資料庫驅動**
- PostgreSQL (42.2.18)
- SQL Server (sqljdbc4)
- Oracle (ojdbc6)

#### **報表系統**
- JasperReports (6.18.0)
- iText PDF (2.1.7.js5 - 有安全漏洞)

#### **工具庫**
- Apache Commons 系列
- Log4j2 (2.17.0 - 已修復漏洞)
- JSON Simple (1.1)

### 3. **前端技術棧**

```
前端框架:
├── Bootstrap 5.3.3 (響應式設計)
├── jQuery 3.7.1 (DOM操作)
├── 自訂 JavaScript
│   ├── functions_ezek_im2.js (業務功能)
│   └── ezekArcgisToolBox.js (GIS整合)
└── 第三方插件
    ├── Fancybox (彈窗效果)
    └── DatePicker (日期選擇)
```

---

## 🏛️ 業務流程架構

### 1. **三迷宮系統 (表單分類)**

```
A迷宮 - 一般違章建築系統
├── 案件數: 263,394件 (71%)
├── 複雜度: ⭐⭐⭐⭐⭐
└── 流程階段: 11個完整階段

B迷宮 - 廣告物違規系統  
├── 案件數: 67,052件 (18%)
├── 複雜度: ⭐⭐
└── 流程階段: 4個主要階段

C迷宮 - 特定類型系統
├── 案件數: 85,762件 (23%)
├── 複雜度: ⭐⭐⭐
└── 流程階段: 4個主要階段
```

### 2. **狀態碼體系 (RLT代碼)**

```
三階段業務流程:
├── 認定階段 (2xx系列)
│   ├── 一般: 231-239
│   ├── 廣告: 241-24f
│   └── 下水道: 251-259
├── 排拆階段 (3xx系列)
│   ├── 一般: 362-369
│   ├── 廣告: 342-349
│   └── 下水道: 352-359
└── 結案階段 (4xx系列)
    ├── 一般: 460-469
    ├── 廣告: 440-449
    └── 下水道: 450-459
```

### 3. **雙表協同機制**

```
IBMCASE表 (案件主檔)
├── 371,081個案件
├── 初期處理 (01-04階段)
└── 基本資料管理

IBMFYM表 (處理記錄)
├── 1,004,853筆記錄
├── 深度處理 (05-11階段)
└── 拆除流程管理
```

---

## ⚠️ 架構風險評估

### 1. **技術債務**
- Web Application 2.3 (2001年標準) - 嚴重過時
- CodeCharge Studio 依賴 - 已停止維護
- C3P0 連接池 ******* - 安全漏洞
- iText 2.1.7 - 已知安全問題

### 2. **架構反模式**
- 業務邏輯與表示層緊密耦合
- 大量使用 JSP Scriptlet
- 缺乏現代化的 MVC 分離
- 難以進行單元測試

### 3. **安全風險**
- 資料庫密碼硬編碼在配置檔
- 舊版依賴庫存在漏洞
- 缺乏現代化的安全框架

---

## 🎯 架構優勢

### 1. **系統穩定性**
- 30年穩定運行實績
- 處理百萬級資料能力
- 完整的業務流程覆蓋

### 2. **功能完整性**
- 三類業務完整支援
- 自動化處理機制
- 豐富的報表功能

### 3. **技術適應性**
- 逐步演進的架構
- 向下兼容性良好
- 前端補償技術靈活

---

## 📋 架構總結

新北市違章建築管理系統是一個基於 CodeCharge Studio 開發的大型政府管理系統，擁有30年的運行歷史。系統採用傳統的三層架構，透過 JSP/Servlet 技術實現，並使用 PostgreSQL 作為主要資料庫。

雖然技術棧相對老舊，但系統展現了驚人的穩定性和完整性。透過雙表協同機制（IBMCASE + IBMFYM）實現了複雜的業務流程管理，並且擁有完善的狀態控制體系。

系統的主要挑戰在於技術債務和維護困難，但其業務邏輯的完整性和資料的完整性使其成為不可替代的業務資產。任何現代化改造都必須謹慎進行，以保護現有的業務價值。

---

**文件產出**: Claude Code  
**分析日期**: 2025-07-06  
**版本**: 1.0