# Claude Code 四實例執行 Prompts

## 🚨 重要提醒
**本階段為文件撰寫階段，嚴禁修改任何程式碼和系統配置檔案！**

---

## 【A】Claude Code - 全棧開發任務組

```
請執行 CLAUDE.md 中標註【A】的任務組。

重要規則：
1. 本階段為文件撰寫階段，嚴禁修改任何程式碼和配置檔案
2. 只能讀取檔案進行分析，不得使用 Edit、Write 修改程式碼
3. 所有產出都是文件，存放於 /DOCS 目錄下

你的任務清單：
- T1.1.1: JSP檔案自動掃描腳本 (4小時) - 設計腳本規格文件
- T1.1.2: 核心業務檔案識別 (6小時) - 建立核心檔案清單文件
- T1.1.3: JSP-XML-Handler對應表 (8小時) - 製作對應關係文件
- T1.1.4: 功能分類索引建立 (6小時) - 建立功能分類文件

工作目錄：D:\apache-tomcat-9.0.98\webapps\src\
文件輸出：D:\apache-tomcat-9.0.98\webapps\src\DOCS\

請先讀取 TASK_TRACKING_DETAIL_V3.md 了解任務詳情，然後開始執行標註【A】的任務。
完成任務時請更新 TASK_TRACKING_DETAIL_V3.md 中的任務狀態。
```

---

## 【B】Claude Code - 後端開發任務組

```
請執行 CLAUDE.md 中標註【B】的任務組。

重要規則：
1. 本階段為文件撰寫階段，嚴禁修改任何程式碼和配置檔案
2. 只能讀取資料庫結構進行分析，不得修改資料庫
3. 環境變數方案只需設計文件，不要實際修改 site.properties
4. 所有產出都是文件，存放於 /DOCS 目錄下

你的任務清單：
- T1.2.1: ibmlawfee表結構補充 (4小時) - 查詢並文件化表結構
- T1.2.2: Stored Procedures清單 (4小時) - 列出所有預存程序文件
- T1.2.3: Triggers和約束文件 (4小時) - 記錄觸發器和約束
- T1.2.4: 業務資料字典 (4小時) - 建立欄位業務意義文件
- T1.3.2: 環境變數管理方案 (6小時) - 設計環境變數方案文件

工作目錄：D:\apache-tomcat-9.0.98\webapps\src\
文件輸出：D:\apache-tomcat-9.0.98\webapps\src\DOCS\
資料庫連線：PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms

請先讀取 TASK_TRACKING_DETAIL_V3.md 了解任務詳情，然後開始執行標註【B】的任務。
完成任務時請更新 TASK_TRACKING_DETAIL_V3.md 中的任務狀態。
```

---

## 【C】Claude Code - 業務分析任務組

```
請執行 CLAUDE.md 中標註【C】的任務組。

重要規則：
1. 本階段為文件撰寫階段，嚴禁修改任何程式碼和配置檔案
2. 只能讀取 JSP/XML/Handler 檔案分析業務流程
3. 參考 STATUS_CODE_STATE_MACHINE_CORRECTED.md 理解狀態碼
4. 所有產出都是流程文件，存放於 /DOCS/flow/ 目錄下

你的任務清單：
- T2.1.1: 掛號通報流程分析 (4小時)
- T2.1.2: 查報人員管理分析 (3小時)
- T2.1.3: 查報資料驗證機制 (3小時)
- T2.2.1: 現場勘查流程分析 (4小時)
- T2.3.1: 認定審核流程分析 (5小時)
- T2.4.1: 認定完成通知流程 (4小時)
- T2.5.1: 拆除通知流程分析 (5小時)
- T2.6.1: 排拆執行流程分析 (4小時)
- T2.7.1: 結案條件分析 (5小時)
- T2.8.1: 完整業務流程圖繪製 (8小時)

工作目錄：D:\apache-tomcat-9.0.98\webapps\src\
文件輸出：D:\apache-tomcat-9.0.98\webapps\src\DOCS\flow\

請先讀取 TASK_TRACKING_DETAIL_V3.md 了解任務詳情，然後開始執行標註【C】的任務。
完成任務時請更新 TASK_TRACKING_DETAIL_V3.md 中的任務狀態。
```

---

## 【D】Claude Code - DevOps與技術架構任務組

```
請執行 CLAUDE.md 中標註【D】的任務組。

重要規則：
1. 本階段為文件撰寫階段，嚴禁修改任何程式碼和配置檔案
2. Git 設定只需撰寫設定指南，不要實際執行 git init
3. 環境配置只需設計方案，不要修改實際配置檔
4. 所有產出都是技術文件，存放於 /DOCS 目錄下

你的任務清單：
第一階段：
- T1.3.1: Git版本控制設定 (2小時) - 撰寫 Git 設定指南
- T1.3.3: 多環境配置區隔 (4小時) - 設計環境配置方案

第二階段：
- T2.3.2: 協同作業機制分析 (4小時)
- T2.5.2: 排拆分案機制分析 (4小時)
- T2.7.2: 自動結案機制分析 (4小時)
- T2.8.2: 狀態碼轉換矩陣建立 (6小時)
- T2.8.3: 異常處理流程整理 (4小時)

工作目錄：D:\apache-tomcat-9.0.98\webapps\src\
文件輸出：D:\apache-tomcat-9.0.98\webapps\src\DOCS\

請先讀取 TASK_TRACKING_DETAIL_V3.md 了解任務詳情，然後開始執行標註【D】的任務。
完成任務時請更新 TASK_TRACKING_DETAIL_V3.md 中的任務狀態。
```

---

## 執行注意事項

1. **嚴禁事項**：
   - ❌ 不得修改任何 .jsp、.java、.xml 程式碼檔案
   - ❌ 不得修改 site.properties 或其他配置檔
   - ❌ 不得執行 git init 或任何 git 操作
   - ❌ 不得修改資料庫結構或資料

2. **允許操作**：
   - ✅ 使用 Read 工具讀取檔案內容
   - ✅ 使用 Bash 查詢資料庫結構（只讀）
   - ✅ 使用 Write 建立新的文件檔案（.md）
   - ✅ 使用 Edit 更新 TASK_TRACKING_DETAIL_V3.md 狀態

3. **協作原則**：
   - 各實例獨立工作，避免同時修改相同文件
   - 定期更新任務狀態，方便追蹤進度
   - 文件命名遵循任務編號規範

4. **品質要求**：
   - 文件需包含完整的分析過程
   - 使用 Mermaid 繪製流程圖
   - 提供具體的程式碼參考位置
   - 標註重要發現和建議