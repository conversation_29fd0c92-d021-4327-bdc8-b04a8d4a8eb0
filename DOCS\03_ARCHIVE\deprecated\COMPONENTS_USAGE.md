# 元件使用說明

以下說明在 PageModel (XML) 中常用的元件，包括地址選擇器、驗證、日期選擇器、表格、分頁、按鈕等。

---

## 1. 地址選擇器 (Address Selector)

使用 `<ListBox>` 元件，須設定 `Select` 查詢取得清單：

```xml
<ListBox name="s_edit_dis_b_addzon"
         dataType="Text"
         controlSourceType="DataSource"
         boundColumn="code_seq"
         textColumn="code_desc"
         connection="DBConn"
         controlSource=""
         required="False"
         isHtml="False">
    <Select query="
        SELECT code_seq, code_desc 
        FROM public.ibmcode 
        WHERE code_type='ZON' AND code_seq<>'**'
        {SQL_Where} {SQL_OrderBy}"
        type="raw" orderBy="code_seq">
        <Where>
            <WhereClause>
                <Expression expression="(code_type = 'ZON' AND code_seq &lt;&gt; '**')"/>
            </WhereClause>
        </Where>
    </Select>
</ListBox>
```

---

## 2. 驗證 (Validation)

在輸入元件上設定 `required="True"` 及 `verificationRule` 屬性：

```xml
<TextBox name="s_regnum_b"
         caption="認定通知號碼-起始"
         dataType="Text"
         controlSourceType="DataSource"
         required="True"
         verificationRule="^\d{4}$"
         errorControl="TextError"/>
```

---

## 3. 日期選擇器 (DatePicker)

搭配 `<TextBox>` 使用，指定 `control` 屬性連動：

```xml
<TextBox name="s_S_DATE" caption="起始日期" dataType="Text"/>
<DatePicker name="DatePicker_s_S_DATE"
            control="s_S_DATE"
            style="Styles/Blueprint/Style.css"/>
```

---

## 4. 表格 (Grid)

使用 `<Grid>` 定義表格，內含 `<Row>` 及欄位元件 (`<Hidden>`, `<Label>`, `<Link>`...)，並可設定排序器 `<Sorter>` 及分頁器 `<Navigator>`：

```xml
<Grid name="BMSDISOBEY_DIST"
      connection="DBConn"
      fetchSize="10"
      allowRead="True"
      visible="True">
    <Row>
        <Hidden name="case_id" controlSource="case_id"/>
        <Label name="reg_num"/>
        <Label name="address"/>
        <Link  name="Link_Edit" hrefType="Page" hrefSource="im20901_man.jsp">
            <LinkParameter name="case_id" sourceType="DataField" sourceName="case_id"/>
        </Link>
    </Row>
    <Sorter name="Sorter1" ascColumn="reg_yy, reg_no" descColumn="reg_yy DESC, reg_no DESC" visible="True"/>
    <Navigator name="Navigator" pageSizes="1;5;10;25;50"/>
    <Select query="SELECT ibmlawfee.*, ibmcase.caddress as address,... {SQL_Where} {SQL_OrderBy}" type="raw"/>
    <CountSql query="SELECT COUNT(*) FROM public.ibmlawfee ..." type="raw"/>
</Grid>
```

---

## 5. 分頁 (Pagination)

使用 `<Navigator>`，可設定可選頁面大小 `pageSizes`：

```xml
<Navigator name="Navigator" pageSizes="10;20;50"/>
```

---

## 6. 按鈕 (Button)

使用 `<Button>` 定義操作按鈕，可設定 `operation`, `returnPage`, `doValidate` 等屬性：

```xml
<Button name="Button_DoSearch"
        operation="Search"
        returnPage="im20901_lis.jsp"
        doValidate="True"/>
```

---

## 7. 其他常用元件

- `<Link>`：頁面或檔案連結
- `<Hidden>`：隱藏欄位
- `<TextBox>`：文字輸入欄位
- `<Label>`：顯示文字
- `<Select>` / `<CountSql>`：定義 SQL 查詢
- `<Record>`：搜尋或表單容器
- `<Page>`：頁面根標籤，可加入 `<Header>`/`<Content>`/`<Footer>`

---

## 8. 佈局元件 (_layout.xml)

共用佈局範例：

```xml
<Page name="_layout">
    <Header>
        <Include file="header.jsp"/>
    </Header>
    <Content>
        <IncludePage name="PageContent"/>
    </Content>
    <Footer>
        <Include file="footer.jsp"/>
    </Footer>
</Page>
``` 