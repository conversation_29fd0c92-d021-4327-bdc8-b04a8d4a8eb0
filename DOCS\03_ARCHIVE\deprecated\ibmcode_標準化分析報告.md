# 新北市違章建築管理系統 ibmcode 資料表標準化分析報告

---

## 📋 執行摘要

| 項目 | 內容 |
|------|------|
| **分析對象** | 新北市違章建築管理系統 ibmcode 資料表 |
| **分析日期** | 2024年12月 |
| **code_type 總數** | 81個 |
| **代碼記錄總數** | 2,249筆 |
| **功能分類** | 13大類 |
| **系統性質** | 核心共用代碼資料表 |

---

## 📊 分析結果統計

### 1. 資料表基本結構
```sql
CREATE TABLE ibmcode (
    code_type VARCHAR,    -- 代碼類型（主分類）
    code_seq VARCHAR,     -- 代碼序號（子分類）
    code_desc VARCHAR,    -- 代碼描述
    is_del CHAR(1),       -- 是否刪除（Y/N）
    memo TEXT             -- 備註說明
);
```

### 2. 記錄數分布（前10名）
| 排序 | code_type | 記錄數 | 功能說明 |
|------|-----------|--------|----------|
| 1 | b_caselevel2kind_new | 215 | 案件二級分類（新版） |
| 2 | PRJNM | 110 | 專案名稱 |
| 3 | b_caseproject_new | 104 | 案件專案（新版） |
| 4 | b_saydocword_new | 92 | 公文用語（新版） |
| 5 | b_saydocword | 90 | 公文用語 |
| 6 | RLT | 69 | 處理結果/案件狀態 |
| 7 | b_caseproject | 66 | 案件專案 |
| 8 | DSTOFF | 61 | 查報公所/查報單位 |
| 9 | b_saydepartment_new | 60 | 公文機關（新版） |
| 10 | b_address | 58 | 地址 |

### 3. 功能分類統計
| 分類編號 | 分類名稱 | code_type數量 | 主要用途 |
|----------|----------|---------------|----------|
| 1 | 行政管理類 | 6個 | 區公所、行政區域管理 |
| 2 | 案件分類與管理類 | 18個 | 違建案件處理流程 |
| 3 | 新版案件分類與管理類 | 12個 | 系統升級後的案件管理 |
| 4 | 建築物與材料分類 | 8個 | 建築用途、材料分類 |
| 5 | 地址與位置管理 | 5個 | 地址標準化管理 |
| 6 | 公文系統 | 6個 | 公文用語、機關代碼 |
| 7 | 工程專案類 | 5個 | 專案名稱、費用管理 |
| 8 | 文件管理 | 7個 | 檔號、文件分類 |
| 9 | 管理層級分類 | 4個 | 權限層級管理 |
| 10 | 組織管理 | 4個 | 部門、職務管理 |
| 11 | 系統設定類 | 6個 | 系統參數設定 |
| 12 | 財務管理 | 3個 | 費用、預算分類 |
| 13 | 法規管理 | 1個 | 法規相關代碼 |

---

## 🔍 核心業務代碼分析

### A. 關鍵 code_type 詳細說明

#### 1. DSTOFF - 查報公所/查報單位（★★★重要）
- **記錄數：** 61筆
- **用途：** 新北市各區公所及查報單位代碼
- **重要代碼：**
  - 30: 板橋區公所
  - 31: 土城區公所
  - 32: 中和區公所
  - （涵蓋新北市29個行政區）

#### 2. STA - 違建最近流程狀態（★★★重要）
- **記錄數：** 8筆
- **用途：** 違章建築案件處理狀態追蹤
- **完整代碼清單：**
  - 01: 認定中
  - 02: 已認定
  - 03: 已排拆
  - 04: 已結案
  - 05: 移送法辦
  - 06: 拍照列管
  - 07: 已歸檔

#### 3. BLDUSE - 建築物現況用途（★★★重要）
- **記錄數：** 38筆
- **用途：** 建築物實際使用狀況分類
- **主要分類：**
  - 娛樂場所：視聽歌唱、三溫暖、電子遊戲場
  - 商業設施：百貨公司、市場、店舖、餐廳
  - 住宿設施：旅館、民宿、宿舍、住宅
  - 工業設施：工廠、倉庫、汽車修理場
  - 宗教設施：寺廟、教堂、宗祠、納骨堂
  - 醫療設施：醫院、療養院、護理之家
  - 教育設施：學校、補習班、托兒所

#### 4. CASORI - 案件來源（★★重要）
- **記錄數：** 17筆
- **用途：** 違章建築案件來源分類
- **主要來源：**
  - 1: 公所查報
  - 4: 其他機關
  - 5-11: 各局處（農業局、地政局、經發局等）
  - 14: 自行巡查
  - 15: 空拍機巡查
  - 16: 法院查封案

#### 5. ZON - 行政區代碼（★★★重要）
- **記錄數：** 30筆
- **用途：** 新北市行政區域劃分
- **涵蓋範圍：** 新北市29個行政區（207-253郵遞區號）

---

## 🏗️ 系統架構特點

### 1. 版本管理機制
- **新舊並行：** 43個新版代碼類型（b_前綴）
- **命名規則：** 新版通常加上 `_new` 後綴
- **遷移策略：** 支援平滑系統升級

### 2. 資料管理機制
- **軟刪除：** 使用 `is_del` 欄位管理生命週期
- **歷史保留：** 已刪除代碼仍保留供追溯
- **完整性：** 確保資料一致性和可追蹤性

### 3. 分類架構設計
- **階層式：** 一級分類 → 二級分類 → 專案分類
- **模組化：** 按業務功能劃分代碼類型
- **擴展性：** 支援新增業務需求

---

## 💼 業務價值評估

### 1. 系統支撐能力
- **案件管理：** 支援完整的違建案件處理流程
- **分類標準：** 提供統一的業務分類標準
- **資料整合：** 促進各模組間的資料交換
- **決策支援：** 提供詳細的統計分析基礎

### 2. 維護效益
- **集中管理：** 統一的代碼維護入口
- **標準化：** 減少重複開發和維護成本
- **一致性：** 確保系統間資料一致性
- **可追蹤：** 完整的變更歷史記錄

---

## 📖 使用指南

### 1. 標準查詢語法
```sql
-- 基本查詢
SELECT code_seq, code_desc 
FROM ibmcode 
WHERE code_type = '{代碼類型}' 
  AND is_del = 'N' 
ORDER BY code_seq;

-- 統計查詢
SELECT code_type, COUNT(*) as count
FROM ibmcode 
WHERE is_del = 'N'
GROUP BY code_type
ORDER BY count DESC;
```

### 2. 程式整合範例
```java
// Java 代碼查詢工具類
public class IBMCodeUtil {
    public static List<CodeItem> getCodeList(String codeType) {
        String sql = "SELECT code_seq, code_desc FROM ibmcode " +
                    "WHERE code_type = ? AND is_del = 'N' " +
                    "ORDER BY code_seq";
        return database.query(sql, codeType);
    }
    
    public static String getCodeDesc(String codeType, String codeSeq) {
        String sql = "SELECT code_desc FROM ibmcode " +
                    "WHERE code_type = ? AND code_seq = ? AND is_del = 'N'";
        return database.queryForString(sql, codeType, codeSeq);
    }
}
```

### 3. JSP 頁面使用
```jsp
<!-- 下拉選單動態填充 -->
<select name="status">
    <option value="">請選擇狀態</option>
    <% 
    List<CodeItem> statusList = IBMCodeUtil.getCodeList("STA");
    for(CodeItem item : statusList) {
    %>
    <option value="<%=item.getCodeSeq()%>"><%=item.getCodeDesc()%></option>
    <% } %>
</select>
```

---

## ⚙️ 維護建議

### 1. 效能優化
```sql
-- 建議建立的索引
CREATE INDEX idx_ibmcode_type_del ON ibmcode(code_type, is_del);
CREATE INDEX idx_ibmcode_type_seq ON ibmcode(code_type, code_seq);
CREATE INDEX idx_ibmcode_desc ON ibmcode(code_desc);
```

### 2. 代碼管理規範
- **新增流程：** 需求確認 → 代碼設計 → 系統測試 → 正式發布
- **修改原則：** 優先新增，避免直接修改現有代碼
- **刪除策略：** 使用軟刪除，保留歷史資料
- **審核機制：** 重要代碼變更需經過審核流程

### 3. 系統遷移建議
#### 階段一：評估準備（1-2週）
- 盤點使用舊版代碼的程式模組
- 建立新舊代碼對應關係表
- 制定遷移計畫和時程

#### 階段二：逐步遷移（4-6週）
- 優先處理高頻使用的代碼類型
- 保持新舊版本並行運作
- 建立遷移進度監控機制

#### 階段三：系統整合（2-3週）
- 統一使用新版代碼標準
- 清理不再使用的舊版代碼
- 完成文件更新和使用者培訓

---

## 📈 發展建議

### 1. 短期優化（3-6個月）
- 完善新版代碼系統功能
- 建立代碼使用統計監控
- 優化查詢效能和快取機制

### 2. 中期發展（6-12個月）
- 建立自動化代碼維護工具
- 實施 API 化代碼服務
- 加強與其他系統的整合

### 3. 長期規劃（1-2年）
- 探索 AI 輔助的代碼分類優化
- 建立智慧化的代碼推薦系統
- 發展跨系統的代碼標準化平台

---

## 📝 結論

新北市違章建築管理系統的 `ibmcode` 資料表是一個設計完善、功能全面的核心基礎設施：

### ✅ 優勢特點
1. **完整性**：涵蓋81個業務分類，2,249筆代碼記錄
2. **靈活性**：支援新舊版本並行和平滑升級
3. **穩定性**：軟刪除機制確保資料安全性
4. **擴展性**：模組化設計支援業務發展需求

### 🎯 業務價值
1. **標準化**：統一的代碼標準提升系統一致性
2. **效率化**：集中管理降低維護成本
3. **智能化**：豐富的分類資料支援業務分析
4. **規範化**：完整的代碼體系支援法規遵循

### 🚀 持續發展
此代碼系統為違章建築管理提供了堅實的資料基礎，隨著業務需求的發展和技術的進步，建議持續優化和完善，以更好地支援新北市的違章建築管理工作。

---

## 📎 附件清單

1. **[ibmcode_完整分析報告.md](ibmcode_完整分析報告.md)** - 詳細技術分析
2. **[ibmcode_代碼值對照表.md](ibmcode_代碼值對照表.md)** - 完整代碼對照表
3. **[ibmcode_全部code_type清單.md](ibmcode_全部code_type清單.md)** - 分類清單
4. **[ibmcode_分析總結報告.md](ibmcode_分析總結報告.md)** - 執行總結

---
*報告標準：ISO 27001 資訊安全管理*  
*文件版本：v1.0*  
*最後更新：2024年12月*  
*分析範圍：新北市違章建築管理系統 ibmcode 資料表完整分析* 