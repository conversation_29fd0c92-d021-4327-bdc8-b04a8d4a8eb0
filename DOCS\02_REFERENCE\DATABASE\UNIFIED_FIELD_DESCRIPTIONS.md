# 新北市違章建築管理系統 - 統一欄位描述對照表

## 系統說明
本文件整理並統一來自不同資料來源的欄位描述，以建立標準化的資料表描述說明表。

## 資料來源說明
- **Agent1**：資料庫表結構（實際schema）
- **Agent2**：JSP程式碼引用  
- **Agent3**：JSP頁面中文標籤
- **Agent4**：XML配置檔案描述
- **Agent5**：Handler業務邏輯
- **Agent6**：ibmcode系統代碼
- **Agent7**：buildcase/tbflow分析
- **Agent8**：查詢頁面分析
- **Agent9**：管理頁面分析
- **Agent10**：DOCS文件資料

## 核心資料表統一欄位描述對照表

### 1. IBMCASE/BUILDCASE（案件主表）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| ibmcase | case_id | VARCHAR(20) | 案件編號/勘查紀錄號碼 | 案件編號（主鍵） | ⭐⭐⭐⭐⭐ | 民國年+流水號，系統自動產生 |
| ibmcase | reg_yy | VARCHAR | 認定年度 | 認定通知書年度 | ⭐⭐⭐⭐ | 民國年度，用於文號組成 |
| ibmcase | reg_no | VARCHAR | 認定流水號 | 認定通知書號碼 | ⭐⭐⭐⭐ | 當年度流水號 |
| ibmcase | ib_prcs | VARCHAR(3) | 案件狀態 | 當前狀態碼 | ⭐⭐⭐⭐⭐ | 對應ibmcode.RLT類型 |
| ibmcase | reg_emp | VARCHAR(10) | 承辦人 | 承辦人員編號 | ⭐⭐⭐⭐⭐ | 案件處理責任人 |
| ibmcase | case_con_user | VARCHAR(10) | 協同承辦人 | 協同作業承辦人 | ⭐⭐⭐⭐ | 新增協同退回功能相關 |
| ibmcase | case_con_date | TIMESTAMP | 協同開始時間 | 協同作業開始時間 | ⭐⭐⭐ | 協同機制時間記錄 |
| ibmcase | dis_b_addzon | VARCHAR | 違建物行政區 | 違建地址行政區 | ⭐⭐⭐⭐⭐ | 對應ibmcode.RCD類型 |
| ibmcase | dis_b_add1 | VARCHAR | 地址1 | 違建地址第1段 | ⭐⭐⭐⭐ | 完整地址組成 |
| ibmcase | dis_b_add2 | VARCHAR | 地址2/路段 | 違建地址第2段 | ⭐⭐⭐⭐ | 道路名稱 |
| ibmcase | dis_b_add3 | VARCHAR | 地址3/巷 | 違建地址第3段 | ⭐⭐⭐ | 巷弄資訊 |
| ibmcase | dis_b_add4 | VARCHAR | 地址4/弄 | 違建地址第4段 | ⭐⭐⭐ | 弄的資訊 |
| ibmcase | dis_b_add5 | VARCHAR | 地址5/號 | 違建地址第5段 | ⭐⭐⭐⭐ | 門牌號碼 |
| ibmcase | dis_b_add6 | VARCHAR | 地址6/樓 | 違建地址第6段 | ⭐⭐⭐ | 樓層資訊 |
| ibmcase | dis_b_add7 | VARCHAR | 地址7/補充 | 違建地址第7段 | ⭐⭐ | 補充地址資訊 |
| ibmcase | caddress | VARCHAR | 完整地址 | 組合完整地址 | ⭐⭐⭐⭐⭐ | 地址段落組合結果 |
| ibmcase | rvldate | NUMERIC | 查報日期/勘查日期 | 案件查報日期 | ⭐⭐⭐⭐⭐ | 民國年月日格式 |
| ibmcase | audnm_date | NUMERIC | 認定日期/審核日期 | 認定完成日期 | ⭐⭐⭐⭐⭐ | 民國年月日格式 |
| ibmcase | reg_date | NUMERIC | 發文日期 | 認定通知書發文日期 | ⭐⭐⭐⭐ | 民國年月日格式 |
| ibmcase | chk_case_date | NUMERIC | 勘查日期 | 現場勘查日期 | ⭐⭐⭐⭐⭐ | 民國年月日格式 |
| ibmcase | building_kind | VARCHAR | 建物類型 | 違建物類型 | ⭐⭐⭐⭐ | 對應ibmcode.BTP類型 |
| ibmcase | building_area | NUMERIC | 建物面積 | 違建面積（平方公尺） | ⭐⭐⭐⭐ | 現場測量結果 |
| ibmcase | building_height | NUMERIC | 建物高度 | 違建高度（公尺） | ⭐⭐⭐ | 現場測量結果 |
| ibmcase | usr_knd | VARCHAR | 違建人類型 | 違建人身分類型 | ⭐⭐⭐⭐ | 個人/法人等分類 |
| ibmcase | ib_user | VARCHAR | 違建人姓名 | 違建當事人姓名 | ⭐⭐⭐⭐⭐ | 主要當事人 |
| ibmcase | usr_id | VARCHAR | 違建人身分證 | 違建人身分證字號 | ⭐⭐⭐⭐ | 身分識別 |
| ibmcase | usr_add | VARCHAR | 違建人地址 | 違建人戶籍地址 | ⭐⭐⭐ | 送達地址 |
| ibmcase | x_coordinate | NUMERIC | X座標 | 違建位置X座標 | ⭐⭐⭐⭐ | GIS定位資訊 |
| ibmcase | y_coordinate | NUMERIC | Y座標 | 違建位置Y座標 | ⭐⭐⭐⭐ | GIS定位資訊 |
| ibmcase | reg_unit | VARCHAR | 業務單位 | 主辦業務單位 | ⭐⭐⭐⭐ | 拆除科/廣告科/勞安科 |
| ibmcase | case_ori | VARCHAR | 案件來源 | 案件來源類型 | ⭐⭐⭐⭐ | 對應ibmcode.SRC類型 |
| ibmcase | dis_type | VARCHAR | 違建類型 | 違建類型分類 | ⭐⭐⭐⭐ | 一般/廣告/下水道 |
| ibmcase | status | VARCHAR | 處理狀態 | 案件處理狀態 | ⭐⭐⭐⭐⭐ | 開放/結案等 |
| ibmcase | cr_date | VARCHAR | 建立時間 | 案件建立時間 | ⭐⭐⭐ | 系統建檔時間 |
| ibmcase | op_date | VARCHAR | 更新時間 | 案件更新時間 | ⭐⭐⭐ | 最後異動時間 |

### 2. TBFLOW/IBMFYM（流程記錄表）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| tbflow | case_no | VARCHAR(20) | 案件編號 | 案件編號（外鍵） | ⭐⭐⭐⭐⭐ | 關聯ibmcase.case_id |
| tbflow | case_state | VARCHAR(3) | 狀態碼 | 流程狀態碼 | ⭐⭐⭐⭐⭐ | 對應ibmcode.RLT類型 |
| tbflow | s_empno | VARCHAR(10) | 處理人員 | 流程處理人員 | ⭐⭐⭐⭐⭐ | 承辦人員編號 |
| tbflow | flow_sdate | TIMESTAMP | 開始時間 | 流程開始時間 | ⭐⭐⭐⭐ | 狀態開始時間 |
| tbflow | flow_edate | TIMESTAMP | 結束時間 | 流程結束時間 | ⭐⭐⭐⭐ | 狀態結束時間 |
| tbflow | flow_desc | TEXT | 處理說明 | 流程處理說明 | ⭐⭐⭐ | 業務處理註記 |

### 3. IBMCODE（系統參數表）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| ibmcode | code_type | VARCHAR(10) | 代碼類型 | 參數代碼類型 | ⭐⭐⭐⭐⭐ | 78種系統參數類型 |
| ibmcode | code_seq | VARCHAR(10) | 代碼值 | 參數代碼值 | ⭐⭐⭐⭐⭐ | 具體代碼值 |
| ibmcode | code_desc | VARCHAR(100) | 代碼說明 | 參數代碼說明 | ⭐⭐⭐⭐⭐ | 中文說明 |
| ibmcode | code_enname | VARCHAR(100) | 英文名稱 | 英文說明 | ⭐⭐ | 英文對照 |
| ibmcode | code_expired | VARCHAR(1) | 停用狀態 | 是否停用 | ⭐⭐⭐⭐ | '0'=啟用, '1'=停用 |
| ibmcode | code_order | INTEGER | 排序順序 | 顯示順序 | ⭐⭐⭐ | 選單排序依據 |

### 4. IBMUSER（使用者表）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| ibmuser | user_id | VARCHAR(10) | 使用者編號 | 使用者帳號 | ⭐⭐⭐⭐⭐ | 主鍵 |
| ibmuser | user_name | VARCHAR(50) | 使用者姓名 | 使用者真實姓名 | ⭐⭐⭐⭐⭐ | 顯示名稱 |
| ibmuser | unit_id | VARCHAR(10) | 單位編號 | 所屬單位編號 | ⭐⭐⭐⭐⭐ | 組織架構 |
| ibmuser | job_title | VARCHAR(50) | 職務 | 職務名稱 | ⭐⭐⭐⭐ | 權限依據 |
| ibmuser | area_code | VARCHAR(10) | 負責區域 | 負責行政區 | ⭐⭐⭐⭐ | 分案依據 |
| ibmuser | is_active | VARCHAR(1) | 啟用狀態 | 帳號啟用狀態 | ⭐⭐⭐⭐ | 'Y'=啟用, 'N'=停用 |

### 5. COLLABORATION_LOG（協同作業記錄）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| collaboration_log | case_no | VARCHAR(20) | 案件編號 | 協同案件編號 | ⭐⭐⭐⭐⭐ | 關聯ibmcase.case_id |
| collaboration_log | collab_type | VARCHAR(10) | 協同類型 | 協同作業類型 | ⭐⭐⭐⭐ | 開始/退回/完成 |
| collaboration_log | from_user | VARCHAR(10) | 發起人 | 協同發起人 | ⭐⭐⭐⭐ | 原承辦人 |
| collaboration_log | to_user | VARCHAR(10) | 協同人 | 協同承辦人 | ⭐⭐⭐⭐ | 協同處理人 |
| collaboration_log | start_date | TIMESTAMP | 開始時間 | 協同開始時間 | ⭐⭐⭐⭐ | 協同發起時間 |
| collaboration_log | end_date | TIMESTAMP | 結束時間 | 協同結束時間 | ⭐⭐⭐ | 協同完成時間 |
| collaboration_log | return_reason | TEXT | 退回原因 | 協同退回原因 | ⭐⭐⭐ | 235/245/255功能 |
| collaboration_log | result_desc | TEXT | 處理結果 | 協同處理結果 | ⭐⭐⭐ | 協同完成說明 |

### 6. IBMLIST（檔案清單）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| ibmlist | case_no | VARCHAR(20) | 案件編號 | 所屬案件編號 | ⭐⭐⭐⭐⭐ | 關聯ibmcase.case_id |
| ibmlist | file_type | VARCHAR(10) | 檔案類型 | 檔案分類 | ⭐⭐⭐⭐ | 照片/文件/圖檔 |
| ibmlist | file_name | VARCHAR(200) | 檔案名稱 | 檔案名稱 | ⭐⭐⭐⭐ | 原始檔名 |
| ibmlist | file_path | VARCHAR(500) | 檔案路徑 | 檔案存放路徑 | ⭐⭐⭐⭐ | 系統存放位置 |
| ibmlist | file_size | BIGINT | 檔案大小 | 檔案大小（bytes） | ⭐⭐ | 儲存空間統計 |
| ibmlist | upload_date | TIMESTAMP | 上傳時間 | 檔案上傳時間 | ⭐⭐⭐ | 上傳時間戳記 |
| ibmlist | upload_user | VARCHAR(10) | 上傳人員 | 檔案上傳人員 | ⭐⭐⭐ | 上傳人員編號 |

### 7. IBMDISNM（違建人資料）

| 表名 | 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|------|--------|----------|----------|----------|--------|------|
| ibmdisnm | case_no | VARCHAR(20) | 案件編號 | 所屬案件編號 | ⭐⭐⭐⭐⭐ | 關聯ibmcase.case_id |
| ibmdisnm | person_type | VARCHAR(10) | 當事人類型 | 違建人類型 | ⭐⭐⭐⭐ | 主要/共同違建人 |
| ibmdisnm | person_name | VARCHAR(100) | 當事人姓名 | 違建人姓名 | ⭐⭐⭐⭐⭐ | 真實姓名 |
| ibmdisnm | person_id | VARCHAR(20) | 身分證字號 | 身分證字號 | ⭐⭐⭐⭐ | 身分識別 |
| ibmdisnm | person_address | VARCHAR(200) | 戶籍地址 | 戶籍地址 | ⭐⭐⭐⭐ | 送達地址 |
| ibmdisnm | person_phone | VARCHAR(20) | 聯絡電話 | 聯絡電話 | ⭐⭐⭐ | 聯絡方式 |
| ibmdisnm | relation_type | VARCHAR(10) | 關係類型 | 與違建物關係 | ⭐⭐⭐ | 所有人/使用人 |

## 重要狀態碼對照表

### 協同退回功能（新增）
| 狀態碼 | 中文描述 | 業務意義 | 重要性 |
|--------|----------|----------|--------|
| 235 | [一般]認定協同退回 | 協同作業退回原承辦人 | ⭐⭐⭐⭐⭐ |
| 245 | [廣告物]認定協同退回 | 協同作業退回原承辦人 | ⭐⭐⭐⭐⭐ |
| 255 | [下水道]認定協同退回 | 協同作業退回原承辦人 | ⭐⭐⭐⭐⭐ |

### 協同完成狀態
| 狀態碼 | 中文描述 | 業務意義 | 重要性 |
|--------|----------|----------|--------|
| 23b | [一般]認定協同作業完成 | 協同作業完成 | ⭐⭐⭐⭐ |
| 24b | [廣告物]認定協同作業完成 | 協同作業完成 | ⭐⭐⭐⭐ |
| 25b | [下水道]認定協同作業完成 | 協同作業完成 | ⭐⭐⭐⭐ |

### 品質控制機制
| 狀態碼 | 中文描述 | 業務意義 | 重要性 |
|--------|----------|----------|--------|
| 92c | 案件資料繕校 | 資料品質控制 | ⭐⭐⭐⭐ |

## 系統參數代碼類型

### 核心業務參數
| 代碼類型 | 說明 | 參數數量 | 重要性 |
|----------|------|----------|--------|
| RLT | 狀態碼定義 | 126 | ⭐⭐⭐⭐⭐ |
| BTP | 建物類型 | 45 | ⭐⭐⭐⭐⭐ |
| RCD | 行政區代碼 | 29 | ⭐⭐⭐⭐⭐ |
| IFT | 檢查項目 | 38 | ⭐⭐⭐⭐ |
| USG | 使用分區 | 22 | ⭐⭐⭐⭐ |
| SRC | 查報來源 | 15 | ⭐⭐⭐⭐ |

## 地址欄位分段說明

### 地址組合邏輯
```
完整地址 = dis_b_addzon + dis_b_add1 + dis_b_add2 + dis_b_add3 + dis_b_add4 + dis_b_add5 + dis_b_add6 + dis_b_add7 + dis_b_add8 + dis_b_add9
```

### 地址段位說明
1. **dis_b_addzon**: 行政區（新北市XX區）
2. **dis_b_add1**: 里別或大地標
3. **dis_b_add2**: 路段名稱
4. **dis_b_add3**: 巷
5. **dis_b_add4**: 弄
6. **dis_b_add5**: 號碼
7. **dis_b_add6**: 樓層
8. **dis_b_add7**: 補充說明
9. **dis_b_add8**: 其他資訊
10. **dis_b_add9**: 備註

## MOI系統相關欄位

### 國土署資料匯出對應
本系統的核心欄位需要轉換為MOI系統格式：
- `case_no` → MOI案件編號
- `ib_prcs` → MOI處理階段
- `caddress` → MOI違建地址
- `ib_user` → MOI違建人資訊
- `building_area` → MOI違建面積

## 使用說明

### 欄位重要性等級
- ⭐⭐⭐⭐⭐: 系統核心欄位，絕對不可遺失
- ⭐⭐⭐⭐: 重要業務欄位，影響主要功能
- ⭐⭐⭐: 一般業務欄位，影響部分功能
- ⭐⭐: 輔助欄位，提供附加資訊
- ⭐: 備註欄位，可選填

### 命名規範統一
- 所有案件編號統一使用 `case_no` 或 `case_id`
- 所有人員編號統一使用 `*_empno` 或 `*_user`
- 所有日期欄位統一使用 `*_date`
- 所有地址欄位統一使用 `*_add*` 格式

### 協同機制欄位
新增的協同退回功能（235/245/255）相關欄位：
- `case_con_user`: 協同承辦人
- `case_con_date`: 協同開始時間
- `return_reason`: 退回原因

## 更新記錄
- 2025-07-09: 建立統一欄位描述對照表
- 包含10個Agent來源的完整資訊整合
- 涵蓋7個核心資料表的欄位說明
- 新增協同退回功能相關欄位

## 注意事項
1. 本對照表以使用者實際看到的頁面描述為主
2. 如有不同描述則用"/"分隔顯示
3. 所有欄位都經過實際資料庫驗證
4. 協同退回功能（235/245/255）為新增功能，需特別注意
5. MOI系統相關欄位需要特殊格式轉換