<!--Page model head @1-B6E37234-->
<Page name="im52101_upload" restricted="False" included="False"	masterPage="" accessDeniedPage=".jsp" convertRule="Absolute" onlySslAccess="False">
<!--End Page model head-->

<!--Record fileupload model @2-806421C0-->
    <Record name="fileupload" connection="DBConn" restricted="False" masterID="" detailForm="" returnPage="" convertRule="Relative" preserveParams="GET" 
            allowInsert="False" allowUpdate="False" allowDelete="False" allowRead="False" visible="True">
        <Button name="Button_Cancel" operation="Cancel"
                returnPage="" convertRule="Relative"
                defaultButton="False" doValidate="False" order="1">
        </Button>
        <Hidden name="fileId" dataType="Text"
                controlSourceType="DataSource" controlSource="case_id"
                required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
        </Hidden>
        <Hidden name="fileType" dataType="Text"
                controlSourceType="DataSource" controlSource=""
                required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
        </Hidden>
        <Label name="uploadTitle" dataType="Text"
                controlSourceType="DataSource" controlSource=""
                isHtml="False" format="" dbFormat="">
        </Label>
    </Record>
<!--End Record fileupload model-->

<!--Page model tail @1-2BAFA7FA-->
</Page>
<!--End Page model tail-->

