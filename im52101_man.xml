<!--Page model head @1-IM52101HEAD-->
<Page name="im52101_man" restricted="False" included="False" masterPage="" accessDeniedPage=".jsp" convertRule="Absolute" onlySslAccess="False">
<!--End Page model head-->

<!--Record im52101ExcelImport model @2-IM52101REC-->
    <Record name="ibmcase" connection="DBConn" restricted="False" masterID="" detailForm="" returnPage="im52101_man.jsp" convertRule="Relative" preserveParams="GET" 
            allowInsert="False" allowUpdate="False" allowDelete="False" allowRead="False" visible="True"> <!-- Setting allowUpdate to False as JS will handle save -->
     <Button name="Button_Update" operation="Cancel"
                returnPage="" convertRule="Relative"
                defaultButton="False" doValidate="False" order="1">
        </Button>
        <Button name="Button_Save" operation="Nothing" 
                returnPage="" convertRule="Relative"
                defaultButton="False" doValidate="False" order="1">
        </Button>
        <Button name="Button_Cancel" operation="Cancel"
                returnPage="im52101_lis.jsp" convertRule="Relative"
                defaultButton="False" doValidate="False" order="2">
        </Button>
        <Hidden name="import_id" dataType="Text"
                controlSourceType="DataSource" controlSource=""
                required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
        </Hidden>
        <TextArea name="acc_memo" dataType="Text"
                isHtml="" controlSourceType="DataSource" controlSource="acc_memo" 
                required="" unique="" format="" dbFormat=""
                verificationRule="" errorControl="">
        </TextArea>                
        <Insert>
        </Insert>
        <Update>
            <!-- This block would be used if CCS handles the update. 
                 For AJAX save, this might not be directly used but kept for reference.
                 If im52101_man_save.jsp directly updates DB, this SQL is illustrative.
            <SQLParameters>
                <SQLParameter name="import_id" parameterType="URL" dataType="Text" value="import_id" />
                <SQLParameter name="acc_memo" parameterType="Control" dataType="Text" value="acc_memo" />
            </SQLParameters>
            <SQLStatement>
                UPDATE public.im52101_excel_imports SET
                acc_memo = {acc_memo},
                memo_update_timestamp = CURRENT_TIMESTAMP
                WHERE import_id = {import_id}
            </SQLStatement>
            -->
        </Update>
        <Delete>
        </Delete>
    </Record>
<!--End Record im52101ExcelImport model-->

<!--Page model tail @1-IM52101TAIL-->
</Page>
<!--End Page model tail--> 