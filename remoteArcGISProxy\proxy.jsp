<%@page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="java.io.*,java.net.*,java.util.*,javax.servlet.http.*"%>

<%
/**
 * ArcGIS Server 代理服務
 * 用於解決跨域問題和Token管理
 */
response.setHeader("Access-Control-Allow-Origin", "*");
response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");

try {
    String targetUrl = request.getParameter("url");
    String method = request.getMethod();
    
    if (targetUrl == null || targetUrl.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        out.println("{\"error\":\"Missing url parameter\"}");
        return;
    }
    
    // 驗證允許的主機
    if (!isAllowedHost(targetUrl)) {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        out.println("{\"error\":\"Host not allowed\"}");
        return;
    }
    
    // 建立連線
    URL url = new URL(targetUrl);
    HttpURLConnection conn = (HttpURLConnection) url.openConnection();
    
    // 設定請求方法
    conn.setRequestMethod(method);
    conn.setConnectTimeout(30000);
    conn.setReadTimeout(30000);
    
    // 複製請求標頭
    Enumeration<String> headerNames = request.getHeaderNames();
    while (headerNames.hasMoreElements()) {
        String headerName = headerNames.nextElement();
        if (!headerName.toLowerCase().startsWith("host") && 
            !headerName.toLowerCase().startsWith("connection")) {
            conn.setRequestProperty(headerName, request.getHeader(headerName));
        }
    }
    
    // 如果是POST請求，複製請求內容
    if ("POST".equalsIgnoreCase(method)) {
        conn.setDoOutput(true);
        try (InputStream requestStream = request.getInputStream();
             OutputStream outputStream = conn.getOutputStream()) {
            copyStream(requestStream, outputStream);
        }
    }
    
    // 取得回應
    int responseCode = conn.getResponseCode();
    response.setStatus(responseCode);
    
    // 複製回應標頭
    for (Map.Entry<String, List<String>> header : conn.getHeaderFields().entrySet()) {
        String headerName = header.getKey();
        if (headerName != null && !headerName.toLowerCase().equals("transfer-encoding")) {
            for (String headerValue : header.getValue()) {
                response.addHeader(headerName, headerValue);
            }
        }
    }
    
    // 複製回應內容
    InputStream responseStream = (responseCode < 400) ? 
        conn.getInputStream() : conn.getErrorStream();
        
    if (responseStream != null) {
        try (OutputStream outputStream = response.getOutputStream()) {
            copyStream(responseStream, outputStream);
        }
    }
    
} catch (Exception e) {
    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    out.println("{\"error\":\"Proxy error: " + e.getMessage() + "\"}");
}

%>

<%!
// 複製串流的輔助方法
void copyStream(InputStream input, OutputStream output) throws IOException {
    byte[] buffer = new byte[4096];
    int bytesRead;
    while ((bytesRead = input.read(buffer)) != -1) {
        output.write(buffer, 0, bytesRead);
    }
}

// 檢查是否為允許的主機
boolean isAllowedHost(String urlStr) {
    try {
        URL url = new URL(urlStr);
        String host = url.getHost().toLowerCase();
        
        // 允許的主機清單
        String[] allowedHosts = {
            "gis1.ntpc.gov.tw",
            "icdc.ntpc.gov.tw",
            "limit.ntpc.gov.tw",
            "localhost",
            "127.0.0.1"
        };
        
        for (String allowedHost : allowedHosts) {
            if (host.equals(allowedHost) || host.endsWith("." + allowedHost)) {
                return true;
            }
        }
        
        return false;
    } catch (Exception e) {
        return false;
    }
}
%>