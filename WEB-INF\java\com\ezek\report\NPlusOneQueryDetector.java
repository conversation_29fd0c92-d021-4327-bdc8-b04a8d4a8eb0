package com.ezek.report;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * N+1 查詢檢測器
 * 
 * 專門針對 CodeCharge Studio 三層架構的 N+1 查詢模式檢測
 * 支援靜態程式碼分析和動態執行時監控
 * 
 * 架構特徵：
 * - JSP：表現層，混合 HTML 和 Java 程式碼
 * - XML：配置層，定義資料綁定和查詢
 * - Handlers：邏輯層，包含業務邏輯和資料庫操作
 * 
 * 常見 N+1 模式：
 * 1. jdbcConn.getRows() 在迴圈中重複呼叫
 * 2. DBTools.dLookUp() 在資料處理過程中多次調用
 * 3. XML 配置的 ListBox/Select 查詢產生的連鎖查詢
 * 4. Record Handler 中的 BeforeShow/AfterUpdate 事件觸發多次資料庫查詢
 */
public class NPlusOneQueryDetector {
    
    // === 核心監控資料結構 ===
    
    /** 查詢執行統計 */
    private static final Map<String, QueryStats> queryStatsMap = new ConcurrentHashMap<>();
    
    /** 執行堆疊追蹤 */
    private static final ThreadLocal<QueryCallStack> callStackThreadLocal = new ThreadLocal<>();
    
    /** N+1 模式檢測結果 */
    private static final List<NPlusOnePattern> detectedPatterns = Collections.synchronizedList(new ArrayList<>());
    
    /** 監控開關 */
    private static volatile boolean monitoringEnabled = true;
    
    /** 查詢相似度閾值 */
    private static final double SIMILARITY_THRESHOLD = 0.8;
    
    /** N+1 檢測閾值 */
    private static final int N_PLUS_ONE_THRESHOLD = 5;
    
    // === 資料結構定義 ===
    
    /**
     * 查詢統計資料
     */
    public static class QueryStats {
        private final AtomicLong executionCount = new AtomicLong(0);
        private final AtomicLong totalExecutionTime = new AtomicLong(0);
        private final Set<String> callerMethods = Collections.synchronizedSet(new HashSet<>());
        private final List<Long> executionTimes = Collections.synchronizedList(new ArrayList<>());
        private final String normalizedQuery;
        private volatile long firstExecutionTime;
        private volatile long lastExecutionTime;
        
        public QueryStats(String normalizedQuery) {
            this.normalizedQuery = normalizedQuery;
            this.firstExecutionTime = System.currentTimeMillis();
        }
        
        public void recordExecution(long executionTime, String callerMethod) {
            executionCount.incrementAndGet();
            totalExecutionTime.addAndGet(executionTime);
            executionTimes.add(executionTime);
            callerMethods.add(callerMethod);
            lastExecutionTime = System.currentTimeMillis();
            
            // 保持最近100次執行記錄
            if (executionTimes.size() > 100) {
                executionTimes.remove(0);
            }
        }
        
        public double getAverageExecutionTime() {
            long count = executionCount.get();
            return count > 0 ? (double) totalExecutionTime.get() / count : 0;
        }
        
        public boolean isPotentialNPlusOne() {
            return executionCount.get() >= N_PLUS_ONE_THRESHOLD && 
                   (lastExecutionTime - firstExecutionTime) < 10000; // 10秒內多次執行
        }
        
        // Getters
        public long getExecutionCount() { return executionCount.get(); }
        public long getTotalExecutionTime() { return totalExecutionTime.get(); }
        public Set<String> getCallerMethods() { return new HashSet<>(callerMethods); }
        public String getNormalizedQuery() { return normalizedQuery; }
        public List<Long> getExecutionTimes() { return new ArrayList<>(executionTimes); }
    }
    
    /**
     * 查詢調用堆疊
     */
    public static class QueryCallStack {
        private final List<CallFrame> frames = new ArrayList<>();
        private final long startTime = System.currentTimeMillis();
        
        public void pushFrame(String method, String query) {
            frames.add(new CallFrame(method, query, System.currentTimeMillis()));
        }
        
        public CallFrame popFrame() {
            return frames.isEmpty() ? null : frames.remove(frames.size() - 1);
        }
        
        public List<CallFrame> getFrames() {
            return new ArrayList<>(frames);
        }
        
        public boolean hasRepeatedQueries() {
            Map<String, Integer> queryCount = new HashMap<>();
            for (CallFrame frame : frames) {
                String normalized = normalizeQuery(frame.query);
                queryCount.put(normalized, queryCount.getOrDefault(normalized, 0) + 1);
            }
            return queryCount.values().stream().anyMatch(count -> count >= N_PLUS_ONE_THRESHOLD);
        }
        
        public static class CallFrame {
            public final String method;
            public final String query;
            public final long timestamp;
            
            public CallFrame(String method, String query, long timestamp) {
                this.method = method;
                this.query = query;
                this.timestamp = timestamp;
            }
        }
    }
    
    /**
     * N+1 模式檢測結果
     */
    public static class NPlusOnePattern {
        public final String patternType;
        public final String location;
        public final String normalizedQuery;
        public final int executionCount;
        public final long totalTime;
        public final List<String> stackTrace;
        public final String recommendation;
        public final long detectedAt;
        
        public NPlusOnePattern(String patternType, String location, String normalizedQuery, 
                              int executionCount, long totalTime, List<String> stackTrace, 
                              String recommendation) {
            this.patternType = patternType;
            this.location = location;
            this.normalizedQuery = normalizedQuery;
            this.executionCount = executionCount;
            this.totalTime = totalTime;
            this.stackTrace = new ArrayList<>(stackTrace);
            this.recommendation = recommendation;
            this.detectedAt = System.currentTimeMillis();
        }
        
        @Override
        public String toString() {
            return String.format(
                "N+1 Pattern Detected:\n" +
                "  Type: %s\n" +
                "  Location: %s\n" +
                "  Query: %s\n" +
                "  Executions: %d\n" +
                "  Total Time: %d ms\n" +
                "  Recommendation: %s\n",
                patternType, location, normalizedQuery, executionCount, totalTime, recommendation
            );
        }
    }
    
    // === 核心監控方法 ===
    
    /**
     * 記錄 JDBC 查詢執行
     * 攔截 jdbcConn.getRows() 和類似方法
     */
    public static void recordQueryExecution(String sql, long executionTime, String callerMethod) {
        if (!monitoringEnabled) return;
        
        try {
            String normalized = normalizeQuery(sql);
            QueryStats stats = queryStatsMap.computeIfAbsent(normalized, QueryStats::new);
            stats.recordExecution(executionTime, callerMethod);
            
            // 更新調用堆疊
            QueryCallStack callStack = getOrCreateCallStack();
            callStack.pushFrame(callerMethod, sql);
            
            // 檢測 N+1 模式
            detectNPlusOnePattern(stats, callStack, callerMethod);
            
        } catch (Exception e) {
            System.err.println("Error recording query execution: " + e.getMessage());
        }
    }
    
    /**
     * 記錄 DBTools.dLookUp 執行
     */
    public static void recordLookupExecution(String table, String condition, long executionTime, String callerMethod) {
        if (!monitoringEnabled) return;
        
        String syntheticSql = String.format("SELECT * FROM %s WHERE %s", table, condition);
        recordQueryExecution(syntheticSql, executionTime, callerMethod);
    }
    
    /**
     * 檢測 N+1 查詢模式
     */
    private static void detectNPlusOnePattern(QueryStats stats, QueryCallStack callStack, String callerMethod) {
        if (stats.isPotentialNPlusOne()) {
            // 檢查是否在迴圈中執行
            if (isInLoop(callStack) || isRepeatedInHandlers(callerMethod)) {
                String patternType = determinePatternType(callerMethod, stats.getNormalizedQuery());
                String recommendation = generateRecommendation(patternType, stats.getNormalizedQuery());
                
                NPlusOnePattern pattern = new NPlusOnePattern(
                    patternType,
                    callerMethod,
                    stats.getNormalizedQuery(),
                    (int) stats.getExecutionCount(),
                    stats.getTotalExecutionTime(),
                    getCurrentStackTrace(),
                    recommendation
                );
                
                detectedPatterns.add(pattern);
                logNPlusOneDetection(pattern);
            }
        }
    }
    
    // === 靜態程式碼分析方法 ===
    
    /**
     * 分析 JSP 檔案中的 N+1 模式
     */
    public static List<String> analyzeJspFile(String jspContent, String fileName) {
        List<String> issues = new ArrayList<>();
        
        // 模式1：for/while 迴圈中的資料庫查詢
        Pattern loopDbPattern = Pattern.compile(
            "(?i)(for|while)\\s*\\([^}]*\\{[^}]*" +
            "(jdbcConn\\.getRows|DBTools\\.dLookUp|getConnection)[^}]*\\}",
            Pattern.DOTALL
        );
        
        Matcher matcher = loopDbPattern.matcher(jspContent);
        while (matcher.find()) {
            issues.add(String.format("%s: 在迴圈中發現資料庫查詢 - %s", 
                fileName, matcher.group().substring(0, Math.min(100, matcher.group().length()))));
        }
        
        // 模式2：JSTL forEach 中的查詢
        Pattern forEachDbPattern = Pattern.compile(
            "(?i)<c:forEach[^>]*>[^<]*" +
            "(jdbcConn\\.getRows|DBTools\\.dLookUp)[^<]*</c:forEach>",
            Pattern.DOTALL
        );
        
        matcher = forEachDbPattern.matcher(jspContent);
        while (matcher.find()) {
            issues.add(String.format("%s: 在 JSTL forEach 中發現資料庫查詢", fileName));
        }
        
        // 模式3：重複的 dLookUp 調用
        Pattern repeatedLookupPattern = Pattern.compile("(DBTools\\.dLookUp[^;]*;)", Pattern.DOTALL);
        Map<String, Integer> lookupCounts = new HashMap<>();
        
        matcher = repeatedLookupPattern.matcher(jspContent);
        while (matcher.find()) {
            String lookup = normalizeQuery(matcher.group());
            lookupCounts.put(lookup, lookupCounts.getOrDefault(lookup, 0) + 1);
        }
        
        for (Map.Entry<String, Integer> entry : lookupCounts.entrySet()) {
            if (entry.getValue() > 3) {
                issues.add(String.format("%s: 重複的 dLookUp 調用 (%d 次) - %s", 
                    fileName, entry.getValue(), entry.getKey().substring(0, Math.min(80, entry.getKey().length()))));
            }
        }
        
        return issues;
    }
    
    /**
     * 分析 Handlers 檔案中的 N+1 模式
     */
    public static List<String> analyzeHandlersFile(String handlersContent, String fileName) {
        List<String> issues = new ArrayList<>();
        
        // 模式1：BeforeShow/AfterUpdate 方法中的多次查詢
        Pattern eventHandlerPattern = Pattern.compile(
            "(?i)(beforeShow|afterUpdate|beforeSelect)\\s*\\([^{]*\\{([^}]*\\{[^}]*\\})*[^}]*" +
            "(jdbcConn\\.getRows|DBTools\\.dLookUp)[^}]*\\}",
            Pattern.DOTALL
        );
        
        Matcher matcher = eventHandlerPattern.matcher(handlersContent);
        while (matcher.find()) {
            String method = matcher.group(1);
            String content = matcher.group();
            
            // 計算查詢次數
            int queryCount = countDatabaseCalls(content);
            if (queryCount > 3) {
                issues.add(String.format("%s: %s 方法中發現多次資料庫查詢 (%d 次)", 
                    fileName, method, queryCount));
            }
        }
        
        // 模式2：while hasMoreElements 迴圈中的查詢
        Pattern whileLoopPattern = Pattern.compile(
            "(?i)while\\s*\\([^)]*hasMoreElements[^)]*\\)\\s*\\{[^}]*" +
            "(jdbcConn\\.getRows|DBTools\\.dLookUp)[^}]*\\}",
            Pattern.DOTALL
        );
        
        matcher = whileLoopPattern.matcher(handlersContent);
        while (matcher.find()) {
            issues.add(String.format("%s: while hasMoreElements 迴圈中發現資料庫查詢", fileName));
        }
        
        return issues;
    }
    
    /**
     * 分析 XML 配置檔案中的 N+1 模式
     */
    public static List<String> analyzeXmlFile(String xmlContent, String fileName) {
        List<String> issues = new ArrayList<>();
        
        // 模式1：ListBox 的 Select 查詢可能導致 N+1
        Pattern listBoxPattern = Pattern.compile(
            "(?i)<ListBox[^>]*>.*?<Select[^>]*query=\"([^\"]*)\"|<Select[^>]*>([^<]*)</Select>.*?</ListBox>",
            Pattern.DOTALL
        );
        
        Matcher matcher = listBoxPattern.matcher(xmlContent);
        while (matcher.find()) {
            String query = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            if (query != null && !query.trim().isEmpty()) {
                // 檢查是否可能在迴圈中被調用
                if (query.contains("WHERE") && !query.toUpperCase().contains("IN (")) {
                    issues.add(String.format("%s: ListBox 查詢可能導致 N+1 問題 - %s", fileName, query.trim()));
                }
            }
        }
        
        // 模式2：Record 的 Select 查詢
        Pattern recordSelectPattern = Pattern.compile(
            "(?i)<Record[^>]*>.*?<Select[^>]*query=\"([^\"]*)\"|<Select[^>]*>([^<]*)</Select>.*?</Record>",
            Pattern.DOTALL
        );
        
        matcher = recordSelectPattern.matcher(xmlContent);
        while (matcher.find()) {
            String query = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            if (query != null && containsSingleRowPattern(query)) {
                issues.add(String.format("%s: Record 查詢使用單筆查詢模式，可能在列表中造成 N+1", fileName));
            }
        }
        
        return issues;
    }
    
    // === 輔助方法 ===
    
    /**
     * 正規化 SQL 查詢，移除參數值但保留結構
     */
    private static String normalizeQuery(String sql) {
        if (sql == null) return "";
        
        return sql.replaceAll("'[^']*'", "'?'")              // 字串常數
                  .replaceAll("\\b\\d+\\b", "?")              // 數字常數  
                  .replaceAll("\\s+", " ")                    // 多餘空白
                  .trim()
                  .toUpperCase();
    }
    
    /**
     * 取得或建立當前執行緒的調用堆疊
     */
    private static QueryCallStack getOrCreateCallStack() {
        QueryCallStack callStack = callStackThreadLocal.get();
        if (callStack == null) {
            callStack = new QueryCallStack();
            callStackThreadLocal.set(callStack);
        }
        return callStack;
    }
    
    /**
     * 檢查是否在迴圈中執行
     */
    private static boolean isInLoop(QueryCallStack callStack) {
        List<QueryCallStack.CallFrame> frames = callStack.getFrames();
        if (frames.size() < 3) return false;
        
        // 檢查最近的調用是否有相似的查詢
        String lastQuery = normalizeQuery(frames.get(frames.size() - 1).query);
        for (int i = frames.size() - 2; i >= Math.max(0, frames.size() - 10); i--) {
            if (calculateSimilarity(lastQuery, normalizeQuery(frames.get(i).query)) > SIMILARITY_THRESHOLD) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 檢查是否在 Handlers 中重複執行
     */
    private static boolean isRepeatedInHandlers(String callerMethod) {
        return callerMethod != null && 
               (callerMethod.contains("Handler") || 
                callerMethod.contains("beforeShow") || 
                callerMethod.contains("afterUpdate"));
    }
    
    /**
     * 決定模式類型
     */
    private static String determinePatternType(String callerMethod, String query) {
        if (callerMethod.contains("beforeShow")) return "BEFORE_SHOW_N_PLUS_ONE";
        if (callerMethod.contains("afterUpdate")) return "AFTER_UPDATE_N_PLUS_ONE";
        if (query.contains("dLookUp")) return "DLOOKUP_N_PLUS_ONE";
        if (query.contains("getRows")) return "GETROWS_N_PLUS_ONE";
        return "UNKNOWN_N_PLUS_ONE";
    }
    
    /**
     * 產生改善建議
     */
    private static String generateRecommendation(String patternType, String query) {
        switch (patternType) {
            case "BEFORE_SHOW_N_PLUS_ONE":
                return "建議在 beforeShow 中使用批次查詢，避免在每個 Record 載入時個別查詢";
            case "AFTER_UPDATE_N_PLUS_ONE":
                return "建議合併 afterUpdate 中的多次資料庫操作為單一事務";
            case "DLOOKUP_N_PLUS_ONE":
                return "建議使用 JOIN 查詢或快取機制取代重複的 dLookUp 調用";
            case "GETROWS_N_PLUS_ONE":
                return "建議使用 IN 條件或 JOIN 查詢取代迴圈中的 getRows 調用";
            default:
                return "建議檢視查詢邏輯，考慮使用批次處理或快取機制";
        }
    }
    
    /**
     * 取得當前堆疊追蹤
     */
    private static List<String> getCurrentStackTrace() {
        List<String> stackTrace = new ArrayList<>();
        StackTraceElement[] elements = Thread.currentThread().getStackTrace();
        
        for (StackTraceElement element : elements) {
            if (element.getClassName().startsWith("com.ezek") || 
                element.getClassName().contains("Handler")) {
                stackTrace.add(element.toString());
            }
            if (stackTrace.size() >= 10) break;
        }
        
        return stackTrace;
    }
    
    /**
     * 記錄 N+1 檢測結果
     */
    private static void logNPlusOneDetection(NPlusOnePattern pattern) {
        System.err.println("=== N+1 Query Pattern Detected ===");
        System.err.println(pattern);
        System.err.println("Stack Trace:");
        for (String trace : pattern.stackTrace) {
            System.err.println("  " + trace);
        }
        System.err.println("=====================================");
    }
    
    /**
     * 計算查詢相似度
     */
    private static double calculateSimilarity(String query1, String query2) {
        if (query1.equals(query2)) return 1.0;
        
        // 簡單的相似度計算：基於編輯距離
        int maxLen = Math.max(query1.length(), query2.length());
        if (maxLen == 0) return 1.0;
        
        int editDistance = editDistance(query1, query2);
        return 1.0 - (double) editDistance / maxLen;
    }
    
    /**
     * 計算編輯距離
     */
    private static int editDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) {
            for (int j = 0; j <= n; j++) {
                if (i == 0) {
                    dp[i][j] = j;
                } else if (j == 0) {
                    dp[i][j] = i;
                } else if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i][j - 1], dp[i - 1][j]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 計算程式碼中的資料庫調用次數
     */
    private static int countDatabaseCalls(String content) {
        int count = 0;
        count += countOccurrences(content, "jdbcConn.getRows");
        count += countOccurrences(content, "DBTools.dLookUp");
        count += countOccurrences(content, "getConnection");
        return count;
    }
    
    /**
     * 計算字串出現次數
     */
    private static int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
    
    /**
     * 檢查是否包含單筆查詢模式
     */
    private static boolean containsSingleRowPattern(String query) {
        String upperQuery = query.toUpperCase();
        return upperQuery.contains("WHERE") && 
               upperQuery.contains("=") && 
               !upperQuery.contains("IN (") &&
               !upperQuery.contains("LIMIT") &&
               !upperQuery.contains("TOP ");
    }
    
    // === 公開 API ===
    
    /**
     * 啟用監控
     */
    public static void enableMonitoring() {
        monitoringEnabled = true;
        System.out.println("N+1 Query Detection enabled");
    }
    
    /**
     * 停用監控
     */
    public static void disableMonitoring() {
        monitoringEnabled = false;
        System.out.println("N+1 Query Detection disabled");
    }
    
    /**
     * 清除統計資料
     */
    public static void clearStats() {
        queryStatsMap.clear();
        detectedPatterns.clear();
        callStackThreadLocal.remove();
        System.out.println("N+1 Query Detection stats cleared");
    }
    
    /**
     * 取得查詢統計報告
     */
    public static String generateStatsReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== N+1 Query Detection Report ===\n\n");
        
        report.append("Query Statistics:\n");
        for (Map.Entry<String, QueryStats> entry : queryStatsMap.entrySet()) {
            QueryStats stats = entry.getValue();
            report.append(String.format(
                "Query: %s\n" +
                "  Executions: %d\n" +
                "  Average Time: %.2f ms\n" +
                "  Total Time: %d ms\n" +
                "  Callers: %s\n\n",
                entry.getKey().substring(0, Math.min(80, entry.getKey().length())),
                stats.getExecutionCount(),
                stats.getAverageExecutionTime(),
                stats.getTotalExecutionTime(),
                stats.getCallerMethods()
            ));
        }
        
        report.append("\nDetected N+1 Patterns:\n");
        for (NPlusOnePattern pattern : detectedPatterns) {
            report.append(pattern.toString()).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 取得所有檢測到的模式
     */
    public static List<NPlusOnePattern> getDetectedPatterns() {
        return new ArrayList<>(detectedPatterns);
    }
    
    /**
     * 取得查詢統計
     */
    public static Map<String, QueryStats> getQueryStats() {
        return new HashMap<>(queryStatsMap);
    }
}