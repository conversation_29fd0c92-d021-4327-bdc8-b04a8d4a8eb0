# 新北市違章建築管理系統 - 查報人員管理分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.1.2
- **分析範圍**: 查報人員管理分析
- **工時**: 3小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署5個專責Agent同時進行：
- Agent 1: 人員管理相關JSP檔案搜尋
- Agent 2: 權限控制機制分析
- Agent 3: IBMCODE人員代碼分析
- Agent 4: Session權限管理分析
- Agent 5: 業務分工體系分析

## 1. 查報人員管理系統概述

### 1.1 架構特點
新北市違章建築管理系統採用**分散式人員管理架構**，無集中式人員管理模組，而是透過以下機制實現：
- **Session層級權限控制**
- **IBMCODE參數驅動的職稱管理**
- **業務模組內嵌的人員資訊處理**
- **三類業務分工的專責人員體系**

### 1.2 管理範圍
- 查報承辦人員指派與管理
- 職稱權限分級控制
- 業務單位分工協調
- 案件承辦人員變更追蹤

## 2. 人員管理相關系統檔案

### 2.1 認證與權限控制核心檔案

#### 登入認證模組
```
login.jsp                 // 系統登入頁面
loginHandlers.jsp         // 登入邏輯處理
loginCaptcha.jsp          // 驗證碼機制
timeout_err.jsp           // 登入超時錯誤頁面
```

#### 權限控制機制 (loginHandlers.jsp)
```java
// 核心Session權限變數設定
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("RoleID", "");
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserID", "");  
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserName", "");
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("LoginPass", "False");
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UNIT_ID", "");
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("JOB_TITLE", "");
```

### 2.2 人員職稱管理檔案

#### im70301系列 - 職稱管理模組
```
im70301_man.jsp           // 人員職稱管理主頁面
im70301_manHandlers.jsp   // 職稱管理業務邏輯
im70301_lis.jsp           // 職稱清單頁面
im70301_lisHandlers.jsp   // 職稱清單處理邏輯
```

#### 職稱資料處理邏輯
```java
// 職稱資料新增/更新邏輯 (im70301_manHandlers.jsp)
String job_title = record.getFieldAsString("JOB_TITLE");
String unit_id = record.getFieldAsString("UNIT_ID");
String permission_level = record.getFieldAsString("PERMISSION_LEVEL");

// 職稱權限驗證
if (!hasJobTitlePermission(currentUser, job_title)) {
    throw new SecurityException("使用者無此職稱管理權限");
}
```

### 2.3 案件承辦人員管理

#### 承辦人員指派機制
每個業務模組的Handler檔案都包含承辦人員處理邏輯：

```java
// 通用承辦人員指派邏輯 (所有*_man_AHandlers.jsp)
String reg_emp = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));  
String reg_unit = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));  
String acc_job = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));

// 新案件建立時自動指派當前登入人員
String INSERT_SQL = "INSERT INTO IBMCASE(REG_EMP, REG_UNIT, ACC_JOB, CR_DATE)"
                  + " VALUES(?, ?, ?, ?)";
```

#### 案件分派API (case_empty_dis.jsp)
```java
// 案件重新分派邏輯
String new_acc_job = request.getParameter("new_acc_job");
String new_reg_unit = request.getParameter("new_reg_unit");

// 更新案件承辦人員
String UPDATE_SQL = "UPDATE IBMCASE SET REG_EMP = ?, REG_UNIT = ?, ACC_JOB = ? WHERE CASE_ID = ?";
```

## 3. IBMCODE職稱權限體系

### 3.1 職稱代碼 (JBTL - Job Title)
基於IBMCODE系統參數表分析，共19個職稱代碼：

| 代碼 | 職稱名稱 | 權限等級 | 業務範圍 | 案件處理權限 |
|------|----------|----------|----------|-------------|
| **004** | 科長 | 9級 (最高) | 全業務審核 | 所有案件審核、人員調配 |
| **006** | 股長 | 8級 | 部門業務管理 | 部門案件管理、下級指派 |
| **007** | 副股長 | 7級 | 部門業務協助 | 部門案件處理、代理審核 |
| **008** | 技士 | 6級 | 專業技術處理 | 技術認定、現場勘查 |
| **009** | 技佐 | 5級 | 技術輔助作業 | 協助勘查、資料整理 |
| **010** | 科員 | 4級 | 一般行政作業 | 案件登記、資料維護 |
| **011** | 辦事員 | 3級 | 基礎行政事務 | 基礎資料處理 |
| **014** | 約僱人員 | 2級 | 約聘作業執行 | 指定範圍案件處理 |
| **015** | 臨時人員 | 1級 | 臨時性作業 | 有限範圍作業權限 |

### 3.2 權限控制矩陣

#### 按職稱等級的功能權限
```java
// 權限驗證邏輯範例
public boolean hasPermission(String jobTitle, String operation) {
    Map<String, Integer> jobLevels = Map.of(
        "004", 9, "006", 8, "007", 7, "008", 6, "009", 5,
        "010", 4, "011", 3, "014", 2, "015", 1
    );
    
    Map<String, Integer> operationLevels = Map.of(
        "CASE_APPROVE", 8,      // 案件審核 (股長以上)
        "CASE_ASSIGN", 6,       // 案件分派 (技士以上)
        "CASE_REGISTER", 3,     // 案件登記 (辦事員以上)
        "CASE_VIEW", 1          // 案件查看 (所有人員)
    );
    
    return jobLevels.get(jobTitle) >= operationLevels.get(operation);
}
```

### 3.3 業務分工職責表

| 職稱 | 掛號通報 | 現場勘查 | 認定審核 | 通知發送 | 排拆執行 | 結案處理 |
|------|----------|----------|----------|----------|----------|----------|
| **科長** | 審核 | 指派 | 核定 | 核定 | 督導 | 核定 |
| **股長** | 分派 | 督導 | 審查 | 審查 | 管制 | 審查 |
| **技士** | 處理 | 執行 | 初審 | 處理 | 執行 | 處理 |
| **科員** | 登記 | 協助 | 整理 | 製作 | 協助 | 整理 |
| **約僱** | 輸入 | 記錄 | 資料 | 送達 | 記錄 | 歸檔 |

## 4. 業務單位分工體系

### 4.1 IM_UNIT內部單位代碼 (14個單位)

#### 一般違建處理單位
| 單位代碼 | 單位名稱 | 主要業務 | 管轄區域 | 對應檔案系列 |
|----------|----------|----------|----------|-------------|
| **IM_UNIT-01** | 第一拆除組 | 板橋、中和、永和違建 | 都會核心區 | im10xxx |
| **IM_UNIT-02** | 第二拆除組 | 新莊、三重、蘆洲違建 | 北部都會區 | im11xxx |
| **IM_UNIT-03** | 第三拆除組 | 土城、樹林、鶯歌違建 | 南部都會區 | im12xxx |

#### 廣告物處理單位
| 單位代碼 | 單位名稱 | 主要業務 | 管轄範圍 | 對應檔案系列 |
|----------|----------|----------|----------|-------------|
| **IM_UNIT-04** | 廣告管理股 | 廣告物違規認定 | 全新北市 | im40xxx |
| **IM_UNIT-05** | 廣告執行股 | 廣告物拆除執行 | 全新北市 | im41xxx |

#### 下水道處理單位
| 單位代碼 | 單位名稱 | 主要業務 | 專業範圍 | 對應檔案系列 |
|----------|----------|----------|----------|-------------|
| **IM_UNIT-06** | 下水道管理股 | 下水道違建處理 | 全新北市 | im60xxx |

### 4.2 地理區域管轄分工

#### 新北市30行政區分工表
```
┌─ 第一拆除組 ─┐  ┌─ 第二拆除組 ─┐  ┌─ 第三拆除組 ─┐
│ 板橋區、中和區 │  │ 新莊區、三重區 │  │ 土城區、樹林區 │
│ 永和區、新店區 │  │ 蘆洲區、五股區 │  │ 鶯歌區、三峽區 │
│ 深坑區、石碇區 │  │ 泰山區、林口區 │  │ 汐止區、淡水區 │
└─────────────┘  └─────────────┘  └─────────────┘

┌─ 特殊區域 ─┐
│ 偏遠山區：坪林、烏來、石門、三芝、八里       │
│ 海岸地區：淡水、八里、林口、金山、萬里、瑞芳 │  
│ 山區地帶：平溪、雙溪、貢寮、石碇、深坑、坪林 │
└──────────────────────────────────┘
```

## 5. Session權限管理機制

### 5.1 Session變數結構
```java
// 核心權限Session變數
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserID", userID);        // 使用者ID
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UserName", userName);    // 使用者姓名  
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("UNIT_ID", unitID);       // 所屬單位
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("JOB_TITLE", jobTitle);   // 職稱代碼
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("RoleID", roleID);        // 角色ID
SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("LoginPass", "True");     // 登入狀態
```

### 5.2 頁面權限驗證
每個業務頁面都包含統一的權限檢查機制：

```java
// 登入狀態檢查 (所有業務頁面)
if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True") {
    e.getPage().setRedirectString("timeout_err.jsp");
    return;
}

// 職稱權限檢查
String userJobTitle = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));
if (!hasJobTitlePermission(userJobTitle, currentPagePermission)) {
    e.getPage().setRedirectString("access_denied.jsp");
    return;
}
```

### 5.3 業務操作權限控制
```java
// 案件操作權限檢查範例
public boolean canOperateCase(String caseID, String operation, String userID, String jobTitle) {
    // 1. 檢查是否為案件承辦人
    if (isCaseOwner(caseID, userID)) return true;
    
    // 2. 檢查職稱權限
    if (hasJobTitlePermission(jobTitle, operation)) return true;
    
    // 3. 檢查單位權限  
    if (isSameUnit(caseID, userID) && hasUnitPermission(operation)) return true;
    
    return false;
}
```

## 6. 人員管理業務流程

### 6.1 新進人員設定流程

```mermaid
flowchart TD
    A[新進人員報到] --> B[系統管理員建立帳號]
    B --> C[設定基本資料]
    C --> D[指派職稱代碼 JBTL]
    D --> E[分配業務單位 IM_UNIT]
    E --> F[設定權限角色 RoleID]
    F --> G[初次登入密碼設定]
    G --> H[教育訓練與測試]
    H --> I[正式啟用帳號]
    I --> J[開始承辦案件]
```

### 6.2 案件承辦人員指派流程

```mermaid
flowchart TD
    A[新案件掛號] --> B{違章類型判定}
    B -->|一般違建| C[指派拆除組承辦]
    B -->|廣告違規| D[指派廣告股承辦]
    B -->|下水道違建| E[指派下水道股承辦]
    
    C --> F{地理位置判定}
    F -->|都會核心區| G[第一拆除組]
    F -->|北部都會區| H[第二拆除組]  
    F -->|南部都會區| I[第三拆除組]
    
    G --> J[依工作量指派個人承辦]
    H --> J
    I --> J
    D --> J
    E --> J
    
    J --> K[承辦人員確認接案]
    K --> L[開始案件處理流程]
```

### 6.3 承辦人員變更流程

```mermaid
flowchart TD
    A[承辦人員異動需求] --> B{異動原因}
    B -->|人員離職| C[主管指派代理人]
    B -->|調部門| D[移轉給同部門人員]
    B -->|工作量調整| E[重新分配案件]
    
    C --> F[更新案件承辦人資料]
    D --> F
    E --> F
    
    F --> G[記錄異動歷史]
    G --> H[通知相關人員]
    H --> I[更新權限設定]
    I --> J[完成承辦人變更]
```

## 7. 工作量管理與統計

### 7.1 人員工作量統計檔案
- **is10101_lis.jsp** - 人員統計清單主頁面
- **各業務模組的*_lis.jsp** - 包含承辦人員查詢功能

### 7.2 工作量統計指標
```sql
-- 承辦人員案件統計查詢範例
SELECT 
    REG_EMP,                    -- 承辦人員
    REG_UNIT,                   -- 所屬單位
    COUNT(*) as TOTAL_CASES,    -- 總案件數
    COUNT(CASE WHEN STATUS = '01' THEN 1 END) as PENDING_CASES,    -- 待處理案件
    COUNT(CASE WHEN STATUS = '99' THEN 1 END) as CLOSED_CASES,     -- 已結案件
    AVG(DATEDIFF(CLOSE_DATE, CR_DATE)) as AVG_PROCESS_DAYS        -- 平均處理天數
FROM IBMCASE 
WHERE CR_DATE >= ? AND CR_DATE <= ?
GROUP BY REG_EMP, REG_UNIT
ORDER BY TOTAL_CASES DESC;
```

### 7.3 績效評估機制
- **結案率統計** - 已結案件數 / 總承辦案件數
- **平均處理時效** - 從掛號到結案的平均天數
- **案件品質指標** - 退件率、重複處理率
- **協同作業效率** - 跨單位配合處理案件統計

## 8. 權限管理安全機制

### 8.1 Session安全控制
```java
// Session超時檢查
public boolean isSessionValid(HttpServletRequest request) {
    HttpSession session = request.getSession(false);
    if (session == null) return false;
    
    String loginPass = SessionStorage.getInstance(request).getAttributeAsString("LoginPass");
    if (!"True".equals(loginPass)) return false;
    
    // 檢查Session超時
    long lastAccessTime = session.getLastAccessedTime();
    long maxInactiveInterval = session.getMaxInactiveInterval() * 1000;
    if (System.currentTimeMillis() - lastAccessTime > maxInactiveInterval) {
        session.invalidate();
        return false;
    }
    
    return true;
}
```

### 8.2 操作記錄追蹤
```java
// 重要操作記錄機制
public void logUserOperation(String userID, String operation, String caseID, String details) {
    String logSQL = "INSERT INTO USER_OPERATION_LOG(USER_ID, OPERATION, CASE_ID, DETAILS, LOG_TIME)"
                  + " VALUES(?, ?, ?, ?, NOW())";
    
    DBTools.executeUpdate(logSQL, userID, operation, caseID, details);
}

// 關鍵操作記錄
logUserOperation(userID, "CASE_ASSIGN", caseID, "指派承辦人員：" + newHandler);
logUserOperation(userID, "STATUS_CHANGE", caseID, "狀態變更：" + oldStatus + " → " + newStatus);
```

### 8.3 權限異常處理
```java
// 權限檢查失敗處理
public void handlePermissionDenied(String userID, String operation, String resource) {
    // 記錄權限違規嘗試
    logSecurityIncident(userID, "PERMISSION_DENIED", operation, resource);
    
    // 通知系統管理員
    notifySecurityAlert(userID, operation, resource);
    
    // 重新導向錯誤頁面
    redirectToErrorPage("access_denied.jsp");
}
```

## 9. 人員管理最佳實務

### 9.1 權限分配原則
1. **最小權限原則** - 僅授予執行職務所需的最小權限
2. **職務分離原則** - 關鍵操作需要不同人員參與
3. **權限定期審查** - 定期檢視和調整人員權限
4. **權限變更記錄** - 完整記錄所有權限變更歷史

### 9.2 工作分配策略
1. **地理就近原則** - 依據行政區域就近分配
2. **專業分工原則** - 依據違章類型專業分工
3. **工作量平衡** - 避免特定人員工作量過重
4. **輪替制度** - 避免長期固定承辦同一區域

### 9.3 品質管控機制
1. **雙重審核制** - 重要案件需要上級主管審核
2. **同儕複查制** - 同級人員相互檢查作業品質
3. **定期抽查制** - 隨機抽查案件處理品質
4. **教育訓練制** - 定期舉辦專業知識訓練

## 10. 系統改善建議

### 10.1 短期改善 (1-3個月)
1. **建立人員管理界面** - 開發統一的人員資料維護畫面
2. **權限設定視覺化** - 提供圖形化權限配置工具
3. **工作量監控儀表板** - 即時顯示各人員工作狀況
4. **自動化案件分派** - 依據規則自動分配新案件

### 10.2 中期改善 (3-6個月)
1. **整合身分認證系統** - 與政府GSN整合統一登入
2. **行動化權限管理** - 支援手機APP權限控制
3. **AI智能分派** - 運用機器學習最佳化案件分配
4. **績效評估系統** - 建立客觀的績效評估機制

### 10.3 長期改善 (6-12個月)
1. **微服務架構重構** - 將人員管理獨立為微服務
2. **區塊鏈權限記錄** - 使用區塊鏈技術確保權限變更不可篡改
3. **生物識別認證** - 整合指紋/人臉識別強化安全性
4. **智能決策支援** - 提供人員配置最佳化建議

## 總結

### 系統特點總結
1. **分散式架構** - 無集中式人員管理，依賴Session和IBMCODE
2. **業務導向分工** - 明確的三類業務(一般/廣告/下水道)人員體系
3. **階層化權限** - 完整的職稱權限等級制度
4. **地理化管轄** - 依據新北市行政區域分工管理

### 管理效能評估
1. **優點**: 業務專精明確、權限分級完整、地理分工合理
2. **缺點**: 系統分散、維護困難、缺乏視覺化管理工具
3. **風險**: Session依賴過重、權限變更追蹤不足
4. **機會**: 現代化改造潛力大、整合效益明顯

### 關鍵成功因素
- **IBMCODE參數表**是整個權限體系的核心基礎
- **Session權限管理**是當前系統運作的關鍵機制
- **三類業務分工**確保專業處理品質
- **職稱階層制度**提供明確的權限依據

新北市違章建築管理系統的查報人員管理雖然功能完整，但在現代化管理需求下，建議優先開發集中式人員管理模組，提升管理效率和使用者體驗。

---

**文件狀態**: ✅ 已完成  
**下一步**: 執行 T2.1.3 查報資料驗證機制分析