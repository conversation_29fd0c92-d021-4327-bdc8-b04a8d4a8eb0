#!/bin/bash

# =====================================================
# 新北市違章建築管理系統 - 安全配置設定腳本
# =====================================================
# 檔案名稱: setup-secure-config.sh
# 用途: 設定生產環境的安全配置
# 使用方式: ./setup-secure-config.sh [environment]
# 範例: ./setup-secure-config.sh production
# =====================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEBINF_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$WEBINF_DIR/config"

# 預設環境
ENVIRONMENT=${1:-production}

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查環境
check_environment() {
    log_info "檢查環境: $ENVIRONMENT"
    
    if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
        log_error "無效的環境名稱: $ENVIRONMENT"
        log_error "有效的環境: development, staging, production"
        exit 1
    fi
    
    log_success "環境檢查通過"
}

# 檢查必要的工具
check_dependencies() {
    log_info "檢查必要工具..."
    
    local tools=("openssl" "keytool" "java")
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    log_success "工具檢查通過"
}

# 生成加密金鑰
generate_encryption_key() {
    log_info "生成加密金鑰..."
    
    local key_file="$CONFIG_DIR/encryption.key"
    
    # 生成256位元隨機金鑰
    openssl rand -base64 32 > "$key_file"
    chmod 600 "$key_file"
    
    log_success "加密金鑰已生成: $key_file"
    log_warning "請妥善保管此金鑰檔案，遺失將導致無法解密敏感資料"
}

# 設定環境變數範本
setup_environment_template() {
    log_info "建立環境變數範本..."
    
    local env_file="$CONFIG_DIR/${ENVIRONMENT}.env"
    
    cat > "$env_file" << EOF
# =====================================================
# 新北市違章建築管理系統 - ${ENVIRONMENT} 環境變數
# =====================================================
# 使用方式: source ${ENVIRONMENT}.env
# =====================================================

# ===== 資料庫配置 =====
export DB_PRIMARY_URL="************************************"
export DB_PRIMARY_USER="postgres"
export DB_PRIMARY_PASSWORD="YOUR_PRIMARY_DB_PASSWORD"
export DB_PRIMARY_MAX_CONN="80"
export DB_PRIMARY_TIMEOUT="300"

export DB_SECONDARY_URL="jdbc:sqlserver://**************:2433;DatabaseName=ramsGIS"
export DB_SECONDARY_USER="user_rams2"
export DB_SECONDARY_PASSWORD="YOUR_SECONDARY_DB_PASSWORD"
export DB_SECONDARY_MAX_CONN="100"
export DB_SECONDARY_TIMEOUT="300"

# ===== 應用程式設定 =====
export APP_SERVER_URL="http://localhost:8080/bms"
export APP_SECURED_URL="https://localhost:8443/bms"
export THIRD_PARTY_SERVICE_URL="http://illegal-service.sumire.com.tw/icdc/thridparty/"
export GOOGLE_MAPS_SERVICE_URL="maps/index"

# ===== 安全性設定 =====
export ENCRYPTION_KEY="\$(cat $CONFIG_DIR/encryption.key)"
export AL_ENCRYPTION_KEY="YOUR_AL_ENCRYPTION_KEY"
export AUTH_COOKIE_NAME="bmsLogin"
export AUTH_COOKIE_EXPIRE="30"
export HASH_ALGORITHM="SHA-256"

# ===== 系統設定 =====
export SYSTEM_MODE="$ENVIRONMENT"
export SYSTEM_DEBUG="false"
export SYSTEM_MAINTENANCE="false"

# ===== 日誌設定 =====
export LOG_LEVEL="info"
export LOG_FILE="logs/bms-${ENVIRONMENT}.log"
export LOG_MAX_SIZE="10240"

# ===== 效能設定 =====
export CACHE_ENABLED="true"
export CACHE_SIZE="1000"
export SESSION_TIMEOUT="30"
export UPLOAD_MAX_SIZE="10485760"

# ===== 監控設定 =====
export HEALTH_CHECK_ENABLED="true"
export METRICS_ENABLED="true"
export AUDIT_LOGGING_ENABLED="true"
export ERROR_NOTIFICATION_ENABLED="true"
EOF

    chmod 600 "$env_file"
    log_success "環境變數範本已建立: $env_file"
}

# 設定配置檔案
setup_config_file() {
    log_info "設定配置檔案..."
    
    local source_config="$CONFIG_DIR/${ENVIRONMENT}.properties"
    local target_config="$WEBINF_DIR/site.properties"
    
    if [[ ! -f "$source_config" ]]; then
        log_error "找不到配置檔案: $source_config"
        exit 1
    fi
    
    # 備份現有配置
    if [[ -f "$target_config" ]]; then
        cp "$target_config" "$target_config.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已備份現有配置檔案"
    fi
    
    # 複製新配置
    cp "$source_config" "$target_config"
    log_success "配置檔案已更新: $target_config"
}

# 設定密碼加密
setup_password_encryption() {
    log_info "設定密碼加密..."
    
    local java_class="com.ezek.config.PasswordEncryptor"
    local classpath="$WEBINF_DIR/classes:$WEBINF_DIR/lib/*"
    
    # 編譯Java類別
    find "$WEBINF_DIR/java" -name "*.java" -exec javac -cp "$classpath" {} \;
    
    # 建立加密工具
    cat > "$CONFIG_DIR/encrypt-password.sh" << 'EOF'
#!/bin/bash
# 密碼加密工具
WEBINF_DIR="$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
CLASSPATH="$WEBINF_DIR/classes:$WEBINF_DIR/lib/*"

if [[ $# -eq 0 ]]; then
    echo "使用方式: $0 <明文密碼>"
    exit 1
fi

java -cp "$CLASSPATH" -Djava.awt.headless=true com.ezek.config.PasswordEncryptor "$1"
EOF
    
    chmod +x "$CONFIG_DIR/encrypt-password.sh"
    log_success "密碼加密工具已建立: $CONFIG_DIR/encrypt-password.sh"
}

# 設定系統服務
setup_system_service() {
    log_info "設定系統服務..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        # 建立systemd服務檔案
        local service_file="/tmp/bms-secure-config.service"
        
        cat > "$service_file" << EOF
[Unit]
Description=BMS Secure Configuration Service
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/bash $CONFIG_DIR/load-environment.sh
RemainAfterExit=yes
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
        
        log_info "系統服務檔案已建立: $service_file"
        log_warning "請手動將此檔案複製到 /etc/systemd/system/ 並啟用服務"
    fi
}

# 設定權限
setup_permissions() {
    log_info "設定檔案權限..."
    
    # 設定配置目錄權限
    chmod 750 "$CONFIG_DIR"
    
    # 設定敏感檔案權限
    find "$CONFIG_DIR" -name "*.env" -exec chmod 600 {} \;
    find "$CONFIG_DIR" -name "*.key" -exec chmod 600 {} \;
    find "$CONFIG_DIR" -name "*.properties" -exec chmod 640 {} \;
    
    # 設定腳本權限
    find "$CONFIG_DIR" -name "*.sh" -exec chmod 750 {} \;
    
    log_success "檔案權限設定完成"
}

# 驗證配置
validate_configuration() {
    log_info "驗證配置..."
    
    local config_file="$WEBINF_DIR/site.properties"
    
    # 檢查配置檔案是否存在
    if [[ ! -f "$config_file" ]]; then
        log_error "配置檔案不存在: $config_file"
        return 1
    fi
    
    # 檢查必要的配置項
    local required_keys=(
        "DBConn.url"
        "DBConn.user"
        "DBConn.password"
        "DBConn2.url"
        "DBConn2.user"
        "DBConn2.password"
    )
    
    for key in "${required_keys[@]}"; do
        if ! grep -q "^$key=" "$config_file"; then
            log_error "缺少必要配置項: $key"
            return 1
        fi
    done
    
    log_success "配置驗證通過"
}

# 顯示後續步驟
show_next_steps() {
    log_info "設定完成！後續步驟："
    echo ""
    echo "1. 編輯環境變數檔案："
    echo "   vi $CONFIG_DIR/${ENVIRONMENT}.env"
    echo ""
    echo "2. 設定實際的資料庫密碼："
    echo "   export DB_PRIMARY_PASSWORD='your_actual_password'"
    echo "   export DB_SECONDARY_PASSWORD='your_actual_password'"
    echo ""
    echo "3. 載入環境變數："
    echo "   source $CONFIG_DIR/${ENVIRONMENT}.env"
    echo ""
    echo "4. 重新啟動應用程式伺服器"
    echo ""
    echo "5. 驗證配置："
    echo "   $CONFIG_DIR/validate-config.sh"
    echo ""
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        echo "生產環境額外步驟："
        echo "- 將加密金鑰檔案移至安全位置"
        echo "- 設定適當的檔案權限"
        echo "- 啟用系統服務"
        echo "- 設定監控和備份"
    fi
}

# 主函數
main() {
    log_info "開始設定安全配置 - 環境: $ENVIRONMENT"
    
    check_environment
    check_dependencies
    
    mkdir -p "$CONFIG_DIR"
    
    generate_encryption_key
    setup_environment_template
    setup_config_file
    setup_password_encryption
    setup_system_service
    setup_permissions
    
    if validate_configuration; then
        log_success "安全配置設定完成！"
        show_next_steps
    else
        log_error "配置驗證失敗，請檢查設定"
        exit 1
    fi
}

# 執行主函數
main "$@"