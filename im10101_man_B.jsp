<%--JSP Page Init @1-5948C552--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im10101_man_BServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-AEC63AC4--%>
<%@include file="im10101_man_BHandlers.jsp"%>
<%
    if (!im10101_man_BModel.isVisible()) return;
    if (im10101_man_BParent != null) {
        if (!im10101_man_BParent.getChild(im10101_man_BModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im10101_man_BModel);
    pageContext.setAttribute("page", im10101_man_BModel);
    im10101_man_BModel.fireOnInitializeViewEvent(new Event());
    im10101_man_BModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im10101_man_BModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im10101_man_BModel.isVisible()) return;
%>
<!-- 202210 ICDC Project Modify -- begin-->
<%@include file="google_map_include.jsp"%> 
<!-- 202210 ICDC Project Modify -- end-->
<%--End Page Body--%>

<%--JSP Page Content @1-7C359F97--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>im10101_man_B</title>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//Include Common JSFunctions @1-C04D94A2
</script>
<script src="ClientI18N.jsp?file=Functions.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script src="ClientI18N.jsp?file=DatePicker.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script src="ClientI18N.jsp?file=Globalize.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
//End Include Common JSFunctions

//Include User Scripts @1-92024ECE
</script>
<ccs:attribute owner='page' name='scriptIncludes'/>
<script type="text/javascript">
//End Include User Scripts
$(document).ready(function() {
  var case_id = $("#BMSDISOBEY_DISTCASE_ID").val();

  // 檢查 case_id 是否為有效數字
  if (isNaN(parseInt(case_id.substring(0, 3)))) {
    // 轉型失敗或沒有值，顯示第一種狀況
    $("input[name='IBPRSS'][value='C']").parent().hide(); 
  } else {
    // case_id 為有效數字，進行判斷
    if (parseInt(case_id.substring(0, 3)) < 114) {
      // case_id 前三碼小於 114，顯示第一種狀況
      $("input[name='IBPRSS'][value='C']").parent().hide(); 

      // 新增邏輯：case_id 前三碼小於 114，顯示對應的文字
      $("input[name='EXP_STATE'][value='B']").parent().html("<input type='radio' value='B' name='EXP_STATE'>&nbsp;上列違規廣告物經勘查，係屬傾頹廢朽危險廣告物，已影響公共安全，應予拆除。（建築法第八十一條、行政執行法第三十六條）。<br>");
    } else {
      // case_id 前三碼大於等於 114，顯示第二種狀況
      $("input[name='IBPRSS'][value='B']").parent().hide(); 

      // 新增邏輯：case_id 前三碼大於等於 114，顯示對應的文字
      $("input[name='EXP_STATE'][value='B']").parent().html("<input type='radio' value='C' name='EXP_STATE'>&nbsp;上列違規廣告物經勘查，係屬高風險廣告物，已影響公共安全，應予拆除。（建築法第八十一條、行政執行法第三十六條）。<br>");
    }
  }
});
//Common Script Start @1-8BFA436B
jQuery(function ($) {
    var features = { };
    var actions = { };
    var params = { };

    actions["BMSDISOBEY_DISTButton_DeleteOnClick"] = function (eventType, parameters) {
        var result = true;

        return confirm('是否確認刪除?');

        return result;
    };

	actions["BMSDISOBEY_DISTButton_UpdateOnClick"] = function (eventType, parameters) {
		var result = true;
          
		var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
		var dis_b_addzon = $("#BMSDISOBEY_DISTDIS_B_ADDZON").find(":selected").val();
		var dis_b_add2 = $("#BMSDISOBEY_DISTDIS_B_ADD2").val();
		var dis_b_add3 = $("#BMSDISOBEY_DISTDIS_B_ADD3").val();
		var dis_b_add4 = $("#BMSDISOBEY_DISTDIS_B_ADD4").val();
		var dis_b_add5 = $("#BMSDISOBEY_DISTDIS_B_ADD5").val();
		var dis_b_add6 = $("#BMSDISOBEY_DISTDIS_B_ADD6").val();
		var dis_b_add6_1 = $("#BMSDISOBEY_DISTDIS_B_ADD6_1").val();
		
     	if((dis_b_addzon != '' && dis_b_addzon != "undefined") && (dis_b_add2 != '' && dis_b_add2 != "undefined")){
  
     		open_blockUI();
     		
     		var xhr = new XMLHttpRequest();
     		var url = "im10101_man_submitCheckAddr.jsp";
     		xhr.open('post', url, false);
     		xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded; charset=UTF-8');
     		
     		var data = new URLSearchParams();
     		data.append('DIS_B_ADDZON', dis_b_addzon);
     		data.append('DIS_B_ADD2', dis_b_add2);
     		data.append('DIS_B_ADD3', dis_b_add3);
     		data.append('DIS_B_ADD4', dis_b_add4);
     		data.append('DIS_B_ADD5', dis_b_add5);
     		data.append('DIS_B_ADD6', dis_b_add6);
     		data.append('DIS_B_ADD6_1', dis_b_add6_1);
     		data.append('CASE_ID', case_id);
     		data.append('random', Math.floor(Math.random() * 1000000));
     		
     		xhr.send(data);
     		
     		var result = JSON.parse(xhr.responseText);
     		
     		close_blockUI();
     		
     		if(result != 0){
     			if (!confirm("地址於系統中有重複!是否繼續作業")) {
 		        	return false;
 		        }
     		}
     		
     	}
     	else
     	{
     		open_blockUI();
     		
           	$(".ZONE_AREA_LIST").each(function()
 			{
				var dist = $(this).attr("dist");
 				var section_nm = $(this).attr("section_nm");
  	            var section = $(this).attr("section");
  	            var road_no1 = $(this).attr("road_no1");
  	            var road_no2 = $(this).attr("road_no2");
  	            
 				if((dist && dist != "undefined") || (section && section != "undefined") || (road_no1 && road_no1 != "undefined") || (road_no2 && road_no2 != "undefined"))
 				{
 					var xhr = new XMLHttpRequest();
 		     		var url = "im10101_man_submitCheckCslan.jsp";
 		     		xhr.open('post', url, false);
 		     		xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded; charset=UTF-8');
 					
 		     		var data = new URLSearchParams();
 		     		data.append('DIST', dist);
 		     		data.append('SECTION', section);
 		     		data.append('ROAD_NO1', road_no1);
 		     		data.append('ROAD_NO2', road_no2);
 		     		data.append('CASE_ID', case_id);
 		     		data.append('random', Math.floor(Math.random() * 1000000));
 		     		
 		     		xhr.send(data);
 		     		
 		     		var result = JSON.parse(xhr.responseText);
 		     		
 		     		close_blockUI();
 		     		
 		     		if(result != 0){
 		     			if (!confirm("地號於系統中有重複!是否繼續作業")) {
 		 		        	return false;
 		 		        }
 		     		}
 				}
 			});
     	}
		
		return result;
    };

//BMSDISOBEY_DISTOnLoad Event Start @-671EC62D
    actions["BMSDISOBEY_DISTOnLoad"] = function (eventType, parameters) {
        var result = true;
//End BMSDISOBEY_DISTOnLoad Event Start

//Custom Code @37-2A29BDB7

var field_RVLDATE = document.forms["BMSDISOBEY_DIST"].elements["RVLDATE"];
if(field_RVLDATE) addSlash(field_RVLDATE);
var field_REG_DATE = document.forms["BMSDISOBEY_DIST"].elements["REG_DATE"];
if(field_REG_DATE) addSlash(field_REG_DATE);
var field_AUDNM_DATE = document.forms["BMSDISOBEY_DIST"].elements["AUDNM_DATE"];
if(field_AUDNM_DATE) addSlash(field_AUDNM_DATE);
 

  /*if (document.forms["BMSDISOBEY_DIST"].elements["output_file"]!= null && document.forms["BMSDISOBEY_DIST"].elements["output_file"].value.length > 0)  {
      var fileName = document.forms["BMSDISOBEY_DIST"].elements["output_file"].value;
      document.forms["BMSDISOBEY_DIST"].elements["output_file"].value = "";
      window.location.href = "fileDownload_tycgin.jsp?FILENAME=" + fileName;
  }*/
  
  if (document.forms["BMSDISOBEY_DIST"].elements["PRINT_STATE"]!= null && document.forms["BMSDISOBEY_DIST"].elements["PRINT_STATE"].value.length > 0)  {
      var prtSts = document.forms["BMSDISOBEY_DIST"].elements["PRINT_STATE"].value;
      document.forms["BMSDISOBEY_DIST"].elements["PRINT_STATE"].value = "";
      
      if(prtSts == "gogodownload"){
        var r = confirm("是否下載認定通知書進行預覽 ?");
        if (r == true) {
                gogodownload();
        }
      }
      else if(prtSts == "gogodownload2"){
        var t = confirm("是否下載勘查紀錄表進行預覽 ?");
        if (t == true) {
                gogodownload2();
        }
      }
  }
  
  //先存檔再編輯圖片
  if (document.forms["BMSDISOBEY_DIST"].elements["editPicUrl"]!= null && document.forms["BMSDISOBEY_DIST"].elements["editPicUrl"].value.length > 0)  {
      var editPicUrl = document.forms["BMSDISOBEY_DIST"].elements["editPicUrl"].value;
      var pic_kind = document.forms["BMSDISOBEY_DIST"].elements["editPicParameter"].value;
      
      document.forms["BMSDISOBEY_DIST"].elements["editPicUrl"].value = "";
      document.forms["BMSDISOBEY_DIST"].elements["editPicParameter"].value = "";
      
      //進入編輯圖片畫面
	  $('#BMSDISOBEY_DISTedtImgIdx').val(pic_kind);
	  window.location.href = editPicUrl;
  }else if(document.forms["BMSDISOBEY_DIST"].elements["currentScroll"]!= null && document.forms["BMSDISOBEY_DIST"].elements["currentScroll"].value.length > 0){
		var target_top = document.forms["BMSDISOBEY_DIST"].elements["currentScroll"].value;
  		
  		document.forms["BMSDISOBEY_DIST"].elements["currentScroll"].value = "";	
  		setTimeout(function() {
	  		//console.log("target_top = ",target_top);
	  		//scroll到進去編輯圖片前的位置
	  		$("html, body").scrollTop(target_top);
  		}, 1000);
  }
  
  //根據案件狀態判斷是否顯示[刪除], [送協同作業], [送排拆作業]
  var acc_rlt = document.forms["BMSDISOBEY_DIST"].elements["ACC_RLT"].value;
  if (acc_rlt === "344") {
    $("#BMSDISOBEY_DISTButton_Delete, #BMSDISOBEY_DISTButton_Synergy, #BMSDISOBEY_DISTButton_Submit").css("visibility", "hidden");
  }
  
  // 案件歷程頁籤
  var ibmfym_347 = $("#BMSDISOBEY_DISTibmfym_347").val();
  if (+ibmfym_347 === 0) {
    $(".caseProgress").hide();
  }
  
  //新增時不顯示複製貼上，新增存檔(畫面資料留用)按鈕
  var action_mod = $("#BMSDISOBEY_DISTACTION_MOD").val();
  var canPasteCase = $("#BMSDISOBEY_DISTcanPasteCase").val();
  
  if(action_mod == "insert"){
  	//不顯示複製貼上
  	$("#pasteBtn").hide();
  	$("#pasteMark").hide();
  	$("#copyBtn").hide();
  	//因為新增按鈕所以透過class更改css樣式
  	//$(".btn-right-edit").attr("class","btn-right-insert");
  	//隱藏存檔(畫面資料留用)按鈕
  	$("#BMSDISOBEY_DISTButton_SaveInsert").hide();
  }
  else{
  	//隱藏存檔(畫面資料留用)按鈕
  	$("#BMSDISOBEY_DISTButton_SaveInsert").hide();
  	//複製貼上功能-貼上按鈕管控
	if(canPasteCase == "Y"){
		$("#pasteBtn").show();
		$("#pasteMark").show();		
	}
	else{
		$("#pasteBtn").hide();
		$("#pasteMark").hide();
	}
  }
  
  	//認定號碼輸入
	var LINKAGE_FIELDS = [
		// 認定號碼
		["BMSDISOBEY_DISTREG_NUM", "BMSDISOBEY_DISTVIEWREG_NUM", "BMSDISOBEY_DISTREG_YY", "BMSDISOBEY_DISTREG_NO"]
	];
	    
	var linkageFieldsLen = LINKAGE_FIELDS.length, linkageField = [];
	var idx = 0, initiatorFieldId = "", $initiator = {};
	
	for (idx = 0; idx < linkageFieldsLen; idx++) {
		linkageField = LINKAGE_FIELDS[idx];
		initiatorFieldId = linkageField[0];
		
		inputsLinked(initiatorFieldId, linkageField[1], linkageField[2], linkageField[3]);
		
		$initiator = $("#" + initiatorFieldId);
		
		if ($initiator.val() !== "") {
			var composeRegNumResult = {};
			
			composeRegNumResult = composeRegistrationNumber($initiator.val());
			if (composeRegNumResult.complete) {
				$("#BMSDISOBEY_DISTVIEWREG_NUM").removeClass("regnum-incomplete");
			} else {
				if (!$("#BMSDISOBEY_DISTVIEWREG_NUM").hasClass("regnum-incomplete")) {
					$("#BMSDISOBEY_DISTVIEWREG_NUM").addClass("regnum-incomplete");
				}
			}
			$("#BMSDISOBEY_DISTVIEWREG_NUM").text(composeRegNumResult.val); 
		}
	}    
        
    let value = $('#BMSDISOBEY_DISTCASE_ID').val();
    if (value && (isNaN(parseInt(value.substring(0,3))) || parseInt(value.substring(0,3)) < 114)) {
    $('.BMSDISOBEY_DISTFINISH_STATE_BLOCK').show();   
    }
    else
    {
    $('.BMSDISOBEY_DISTFINISH_STATE_BLOCK').hide();
    }
     	customWindowOnload();   
		return result;
    };

    actions["BMSDISOBEY_DISTOnSubmit"] = function (eventType, parameters) {
        var result = true;

		// 去斜線
		if(document.forms["BMSDISOBEY_DIST"].RVLDATE ){
		  var RVLDATE = document.forms["BMSDISOBEY_DIST"].RVLDATE.value;
		  document.forms["BMSDISOBEY_DIST"].RVLDATE.value =  RVLDATE.replace(/\D/g, "") ; 
		}
		
		if(document.forms["BMSDISOBEY_DIST"].REG_DATE ){
		  var REG_DATE = document.forms["BMSDISOBEY_DIST"].REG_DATE.value;
		  document.forms["BMSDISOBEY_DIST"].REG_DATE.value =  REG_DATE.replace(/\D/g, "") ; 
		}
		if(document.forms["BMSDISOBEY_DIST"].AUDNM_DATE ){
		  var AUDNM_DATE = document.forms["BMSDISOBEY_DIST"].AUDNM_DATE.value;
		  document.forms["BMSDISOBEY_DIST"].AUDNM_DATE.value =  AUDNM_DATE.replace(/\D/g, "") ; 
		}
      
        //radioBox (違建現況 值) EXP_STATE  SHOW_EXP_STATE
        var IBPRSS = $('input[name*=IBPRSS]:checked').val();
        $('input[name=AD_TYP]').val(IBPRSS);
        
        //違建相對位置
        var IBFORWORD = "";
        $('input[name=IBFORWORD]:checked').each(function(){
                this_val = $(this).attr("value") ;
                if(IBFORWORD) IBFORWORD+= "、";
                
                IBFORWORD += this_val;
        });
        $('input[name=DIS_B_WAY]').val(IBFORWORD);
        
        var ADK = $('input[name*=ADK]:checked').val();
        if(ADK != "Z" ) $('input[name=AD_KIND_MEMO]').val(""); 
        $('input[name=AD_KIND_TYPE]').val(ADK);
        //廣告物型式
        var AD_CT = "", check_site = 0, check_other=0, this_val="";
        $('input[name*=AD_CT]:checked').each(function(){
                this_val = $(this).attr("value") ;
                if(AD_CT) AD_CT+= ";";
                
                AD_CT += this_val;
        });
        var AD_KIND_MEMO =  $('input[name=AD_KIND_MEMO]').val();
        if(AD_KIND_MEMO != ""){
                AD_CT = "Z";
        }
        $('input[name=AD_KIND]').val(AD_CT);
        //廣告物勘查結果-涉及違反法條
        var AD_L = "";
        $('input[name*=AD_L]:checked').each(function(){
                this_val = $(this).attr("value") ;
                if(AD_L) AD_L+= ";";
                
                AD_L += this_val;
        });
        $('input[name=AD_CHK_LAW]').val(AD_L);
        
        //違建完成程度
       var COMPLITE = $('input[name*=COMPLITE]:checked').val();
       $('input[name=FINISH_STATE]').val(COMPLITE);
       
       //專案名稱是否顯示
       var PRJ_SHOW = $('input[name*=PRJ_SHOW]:checked').val();
       $('input[name=PRJSHOW]').val(PRJ_SHOW);
       
       //違規項目
       var EXP_STATE = $('input[name*=EXP_STATE]:checked').val();
       if(EXP_STATE != "Z" ) $('input[name=IBM_ITEM_MEMO]').val(""); 
       $('input[name=IBM_ITEM]').val(EXP_STATE);
       

       // 地號加碼數
       var ROAD_NO1 = document.forms["BMSDISOBEY_DIST"].elements["ROAD_NO1"];
                if(ROAD_NO1) addZero_2(ROAD_NO1);
       var ROAD_NO2 = document.forms["BMSDISOBEY_DIST"].elements["ROAD_NO2"];
                if(ROAD_NO2) addZero_2(ROAD_NO2);

       // 建築材料 判斷是否其他
        var BUILDING_KIND = document.forms["BMSDISOBEY_DIST"].elements["BUILDING_KIND"];
        var BUILDING_KIND_DESC = document.forms["BMSDISOBEY_DIST"].elements["BUILDING_KIND_DESC"];
        if (BUILDING_KIND && BUILDING_KIND.value !="99")BUILDING_KIND_DESC.value = "";
        

        // 違建類別及組別名稱
        var DSORT = $("#BMSDISOBEY_DISTDSORT");
        var DSORT2 = $("#BMSDISOBEY_DISTDSORT2");
        
        if(DSORT.find(":selected").val() != ""){
                $('input[name=DIS_TYPE_DESC]').val(DSORT.find(":selected").text());
        }
        
        if(DSORT2.find(":selected").val() != ""){
                $('input[name=DIS_SORT_ITEM]').val(DSORT2.find(":selected").text());
        }
        
      	//專案收費項目
        var FEE = $("#BMSDISOBEY_DISTPRJFEE");
        if(FEE.find(":selected").val() != ""){
        	
            var FEEDESC = FEE.find(":selected").text();
    		$('input[name=PRJFEE_DESC]').val(FEEDESC);
		}
        
        // 地號
        var CSLAN = "";
        $(".ZONE_AREA_LIST").each(function(){
            var ext_ZC_V = $(this).attr("dist_desc");
            var ext_ZC = $(this).attr("dist");
            var ext_SN = $(this).attr("section_nm");
            var ext_SN_V = $(this).attr("section");
            var ext_NO1 = $(this).attr("road_no1");
            var ext_NO2 = $(this).attr("road_no2");
            var ext_Land = ext_ZC_V + "-" + ext_SN_V + "-" + ext_SN + "-" + ext_NO1 + "-" + ext_NO2 + "-" + ext_ZC;
            
            if(CSLAN)CSLAN += ";";
            CSLAN += ext_Land;
            
            $('input[name=CSLAN]').val(CSLAN);
        });
        

        // 專案名稱
        var CSPRJ = "";
        $(".PROJECT_DATA").each(function(){
            var ext_PRJ = $(this).attr("prj_nm");
            var ext_PRJ_ID = $(this).attr("prj_code");
            var ext_PRJFEE = $(this).attr('prjfee');
            var ext_PRJFEE_DESC = $(this).attr('prjfee_desc');
            var ext_Project = "";
            
            if(ext_PRJFEE == 0)
           	{
            	ext_Project = ext_PRJ_ID + "-" + ext_PRJ
           	}
            else
           	{
            	ext_Project = ext_PRJ_ID + "-" + ext_PRJ + "-" + ext_PRJFEE + "-" + ext_PRJFEE_DESC;
           	}
            
            if(CSPRJ)CSPRJ += ";";
            CSPRJ += ext_Project;
            
            $('input[name=CSPRJ]').val(CSPRJ);
            
        });    
        
        //違建人
        var chk = setIbmdisnm();

        if(chk){
                alert("違建人「姓名欄位」必須填寫");
                return false;
        }
        
        //地址填寫方式
        if(addrType == 1){
                $("#BMSDISOBEY_DISTDIS_B_ADDMOD").val("L");
        }
        else if(addrType == 2){
                $("#BMSDISOBEY_DISTDIS_B_ADDMOD").val("H");
        }
        
        //材質尺寸高度(或縱長)是否為以上
       var AD_H_UP = $('input[name*=AD_H_UP]:checked').val();
       if(!AD_H_UP)AD_H_UP = "N";
       $('input[name=AD_HEIGHT_UP]').val(AD_H_UP);

		return result;
    };

//Event Binding @1-E2880456
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Delete)').ccsBind(function() {
        this.bind("click", actions["BMSDISOBEY_DISTButton_DeleteOnClick"]);
    });
    $('*:ccsControl(BMSDISOBEY_DIST)').ccsBind(function() {
        this.each(function(){ actions["BMSDISOBEY_DISTOnLoad"].call(this); });
    });
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Update)').ccsBind(function() {
        this.bind("click", actions["BMSDISOBEY_DISTButton_UpdateOnClick"]);
    });
    $('*:ccsControl(BMSDISOBEY_DIST)').ccsBind(function() {
        this.bind("submit", actions["BMSDISOBEY_DISTOnSubmit"]);
    });
//End Event Binding

//Plugin Calls @1-B62A2315
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Delete)').ccsBind(function() {
        this.bind("click", function(){ $("body").data("disableValidation", true); });
    });
    $('*:ccsControl(BMSDISOBEY_DIST, Button_Cancel)').ccsBind(function() {
        this.bind("click", function(){ $("body").data("disableValidation", true); });
    });
    BMSDISOBEY_DIST_DatePicker_GOVER_DATE1 = new Object(); 
    BMSDISOBEY_DIST_DatePicker_GOVER_DATE1.format           = "yyy/mm/dd";
    BMSDISOBEY_DIST_DatePicker_GOVER_DATE1.style            = "Styles/Blueprint/Style.css";
    BMSDISOBEY_DIST_DatePicker_GOVER_DATE1.relativePathPart = "";
    BMSDISOBEY_DIST_DatePicker_GOVER_DATE1.themeVersion     = "3.0";
    
    
    
    BMSDISOBEY_DIST_DatePicker_EXP_DATE1 = new Object(); 
    BMSDISOBEY_DIST_DatePicker_EXP_DATE1.format           = "yyy/mm/dd";
    BMSDISOBEY_DIST_DatePicker_EXP_DATE1.style            = "Styles/Blueprint/Style.css";
    BMSDISOBEY_DIST_DatePicker_EXP_DATE1.relativePathPart = "";
    BMSDISOBEY_DIST_DatePicker_EXP_DATE1.themeVersion     = "3.0";
    
    
    BMSDISOBEY_DIST_DatePicker_REG_DATE = new Object(); 
    BMSDISOBEY_DIST_DatePicker_REG_DATE.format           = "yyy/mm/dd";
    BMSDISOBEY_DIST_DatePicker_REG_DATE.style            = "Styles/Blueprint/Style.css";
    BMSDISOBEY_DIST_DatePicker_REG_DATE.relativePathPart = "";
    BMSDISOBEY_DIST_DatePicker_REG_DATE.themeVersion     = "3.0";
//End Plugin Calls

//Common Script End @1-562554DE

// 查詢顯示便利貼
selPostIt();
dragElement(document.getElementById("post_it_div"));

});
//End Common Script End

//End CCS script
</script>
<style>
.Description{
    position: absolute;
    top: 0;right: 0;
    z-index :100;
        margin-right: 5px;
        margin-top: 15px;
        border: #85ABE4 1px solid;
        height: 52%;
        background-color: white;        
}

#desTitle{
    height: 40px;
    border-top: #85ABE4 1px solid;
    border-right: #222 1px solid;
    border-left: #85ABE4 1px solid;
    border-bottom: none;
    background: #0090C7;
}
</style>
<script language="JavaScript" src="functions_synct.js" type="text/javascript" charset="utf-8"></script>
<!-- bootstrap -->
<link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/4.7.0/css/font-awesome.css">
<link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="javascript/ezek/chosen/chosen.min.css">
<link rel="stylesheet" type="text/css" href="javascript/ezek/fancybox/jquery.fancybox.css" media="screen">
<script language="JavaScript" src="javascript/viewer/viewer.min.js" type="text/javascript"></script>
<script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
<!-- mousewheel 不能再 arcgis 之後 因為都有用到dojo 會mutipledefine-->
<script language="JavaScript" src="javascript/ezek/fancybox/jquery.mousewheel.pack.js" type="text/javascript"></script>
<script src="javascript/jquery-1.11.2.min.js" type="text/javascript"></script>
<!-- fancybox 不能再 jquery 之前 open() 會掛掉-->
<script language="JavaScript" src="javascript/ezek/fancybox/jquery.fancybox.pack.js" type="text/javascript"></script>
<script language="JavaScript" src="javascript/ezek/chosen/chosen.jquery.min.js" type="text/javascript"></script>
<script src="javascript/ezek/jquery.ezek.min.js"></script>
<script src="javascript/jquery.blockUI.min.js"></script>
<link rel="stylesheet" type="text/css" href="in_recordgridstyle.css?11006250951">
<script src="functions_synct_im.js?1100624" type="text/javascript"></script>
<script src="post_it_drag.js" type="text/javascript"></script>
<script language="JavaScript" type="text/javascript">
var temp_date = Date.now();
//var temp_date = '11005140001';
document.writeln('<link rel="stylesheet" type="text\/css" href="in_recordgridstyle.css?'+temp_date+'">');
document.writeln('<link rel="stylesheet" type="text\/css" href="im10101_man_2.css?'+temp_date+'">');
document.writeln('<script  src="im10101_addr.js?'+temp_date+'"><\/script>');

//var viewer;
//var options = {url: 'data-original'};
var ezekMap, currentCoordinate = [],  markerPic;
var _expNo , _lng , _lat ;
var isFirst = true;
var isDesOpen = false;
var addrType = 1;
//------------------------------
// 給 man call get URL
//------------------------------
function fnParent(lng, lat, zoomLevel, checkImage) {

    $("[name=checkImage]").val(checkImage);
    if (lng && lat) {
        _lng.val(lng);
        _lat.val(lat); 
        currentCoordinate[0] = lng;
        currentCoordinate[1] = lat;
        $("#updatelng").text(lng);
        $("#updatelat").text(lat);
    	$("#coord_mark").show();
    }
    $("input[name=zoom_level]").val(zoomLevel);

}


//------------------------------
// call fancyBox_1   重設經緯度
//------------------------------
function popupSetLngLat() {
    var video_W = $("body").width(),
        video_H = $("body").height();
    var more_H = 30; //扣除margin
    var max_W = (video_H - more_H) * 16 / 9;
    var _EXP_NO = $("#EXP_NO").text();
    var ZOOML = $("input[name=zoom_level]").val();
    if (video_W > max_W) video_W = max_W;

        //var addrTotal = getAddrTotal();
    $.fancybox.open({
        margin: 10,
        padding: 2,
        //centerOnScroll: 'yes',
        href: 'in10101_man_3.jsp?&EXP_NO= ' + _EXP_NO + '&lat=' + _lat.val() + '&lng=' + _lng.val() + '&ZOOML=' + ZOOML+ '&ADDR='+encodeURI(getAddrTotal()),
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },

        iframe: {
            scrolling: 'auto',
            //scrolling : 'no',
            preload: true
        },

        //title : '違建位置',
        titleShow: false,
        titlePosition: 'outside',
        overlayOpacity: 0.3,
        width: 1000,
        //scrolling:'no',
        //scrolling : 'auto',
        height: 688, //(video_H - more_H),
        minWidth: 688,
        //overflow: scroll,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function() {
            //reset 經緯度
            setTimeout(function() {
                setImg(_EXP_NO);
            }, 1500);
        }

    });
}
//------------------------------
// call fancyBox_2   繪圖
//------------------------------
function popupAddWindow() {
    var video_W = $("body").width(),
        video_H = $("body").height();
    var more_H = 30; //扣除margin
    var max_W = (video_H - more_H) * 16 / 9;
    if (video_W > max_W) video_W = max_W;
    var _EXP_NO = $("#EXP_NO").text();
    var ZOOML = $("input[name=zoom_level]").val();
    $.fancybox.open({
        margin: 10,
        padding: 2,
        centerOnScroll: 'yes',
        href: 'in10101_man_2.jsp?EXP_NO=' + _EXP_NO + '&lng=' + _lng.val() + '&lat=' + _lat.val() + '&ZOOML=' + ZOOML,
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        iframe: {
            scrolling: 'auto',
            //scrolling : false,
            // scrolling : 'no',
            preload: true
        },
        // title : '平面示意圖',
        titleShow: false,
        //titlePosition : 'outside',
        overlayOpacity: 0.3,
        width: 1000,
        //scrolling:'yes',
        //height: (video_H - more_H),
        //overflow: scroll,
        height: 688,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function() {
            setTimeout(function() {
                setImg(_EXP_NO);
            }, 1500);
        }
    });
}




//------------------------------
// call upload 上傳
//------------------------------
function addPic ( _EXP_NO, _picType ){
//window.location.href = 'in10101_upload.jsp?EXP_NO=' + _EXP_NO + '&picType=' + _pic_count;
        //var _EXP_NO = $("#EXP_NO").text();
    if (_EXP_NO) _EXP_NO = _EXP_NO.trim();
    $.fancybox.open({
        margin: 10,
        padding: 2,
        //centerOnScroll: 'yes',
        href: 'in10101_upload.jsp?EXP_NO=' + _EXP_NO + '&picType=' + _picType,
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        iframe: {

            scrolling: false,
            preload: true
        },
       
        titleShow: false,
        overlayOpacity: 0.3,
        width: 1000,
        height: 688,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function() {
                        refleshPic(_EXP_NO, _picType);
        }
    });

}

//------------------------------
// call upload 上傳
//------------------------------
function addNowPic ( _CASE_ID, _picType, _picSeq ){

    if (_CASE_ID) _CASE_ID = _CASE_ID.trim();

	if(_picSeq == "N"){
		alert("照片目前已經存在兩張，無法進行上傳");
	}
	else{
	    $.fancybox.open({
	        margin: 10,
	        padding: 2,
	        //centerOnScroll: 'yes',
	        href: 'in10101_uploadForNow.jsp?CASE_ID=' + _CASE_ID + '&picType=' + _picType + '&picSeq=' + _picSeq,
	        type: 'iframe',
	        helpers: {
	            overlay: {
	                closeClick: false
	            }
	        },
	        iframe: {
	
	            scrolling: false,
	            preload: true
	        },
	       
	        titleShow: false,
	        overlayOpacity: 0.3,
	        width: 1000,
	        height: 688,
	        minWidth: 688,
	        hideOnContentClick: false,
	        closeBtn: false,
	        afterClose: function() {
	                        refleshNowPic(_CASE_ID, _picType);
	        }
	    });
	}
}

//------------------------------
// call Legend 說明圖示
//------------------------------
function openLegend (){
    $.fancybox.open({
        margin: 10,
        padding: 2,
        //centerOnScroll: 'yes',
        href: 'im10101_legend.jsp?ib_prcs=B',
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        iframe: {

            scrolling: false,
            preload: true
        },
       
        titleShow: false,
        overlayOpacity: 0.3,
        width: 1100,
        height: 550,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: true,
        fitToView: false,
		autoSize: false,
        afterClose: function() {
                        //refleshPic(_EXP_NO, _picType);
        }
    });

}

//------------------------------
// reflesh pic
//------------------------------
function refleshPic(_EXP_NO, _picType ){
            $.ajax({
                                type: "POST",
                                url: "in10101_refleshPic_v2.jsp",
                                data: {  
                                   CASE_ID: _EXP_NO,
                                   picType: _picType,                     
                                   random:Math.floor(Math.random()*1000000)
                                }
                        }).done(function(o) {
                                //show_ADpic
                                
                                var $divId;
                                if(_picType == "nowPic"){
                                        $divId = $("#show_NOWpic2") ;
                                }else if( _picType == "partPic" ) {
                                        $divId = $("#show_PATpic") ; 
                                }else if( _picType == "adPic" ){
                                        $divId = $("#show_ADpic") ;  
                                }
                                //console.log("~refleshPic~");
                           
                                        $divId.html(" ");
                                        setTimeout(function() {$divId.html(o); }, 500);
                                
                                
                        }).always(function( qq ) { 
                                 //closeLoading()
                        });
}

//------------------------------
// reflesh now pic
//------------------------------
function refleshNowPic(_EXP_NO, _picType ){
            $.ajax({
                                type: "POST",
                                url: "in10101_refleshNowPic.jsp",
                                data: {  
                                   CASE_ID: _EXP_NO,
                                   picType: _picType,                     
                                   random:Math.floor(Math.random()*1000000)
                                }
                        }).done(function(o) {
                                //show_ADpic
                                
                                var $divId;
                                if( _picType == "nowPic" ) {
                                        $divId = $("#show_NOWpic") ;
                                }else if( _picType == "partPic" ) {
                                        $divId = $("#show_PATpic") ; 
                                }
                                //console.log("~refleshNowPic~");
                           
                         $divId.html(" ");
                         setTimeout(function() {$divId.html(o); }, 500);
                     
                        }).always(function( qq ) { 
                                 //closeLoading()
                        });
}

//------------------------------
// call delete
//------------------------------
function del_ADpic(_EXP_NO,_filename ){
        var r = confirm("是否確認刪除");
        if (r == true) {
            $.ajax({
                                type: "POST",
                                url: "in10101_delete_man.jsp",
                                data: {  
                                   EXP_NO: _EXP_NO,
                                   filename: _filename,
                                   _method: 'DELETE',
                                   random:Math.floor(Math.random()*1000000)
                                }
                        }).done(function(o) {

                                var n =_filename.substring(0, _filename.lastIndexOf("."));
                                $("#"+n+"").remove();
                                //window.location.href = 'in10101_man.jsp?EXP_NO='+EXP_NO+'&time='+Date.now();
                                
                        }).always(function( qq ) { 
                                 //closeLoading()
                        });
        } else {
            //txt = "You pressed Cancel!";
        }
        
}
//------------------------------
// show 照片
//------------------------------
function showPicQ(Img_index, Img_kind, _case_id) {
    
    if (_case_id) _case_id = _case_id.trim();
    $.fancybox.open({
        margin: 10,
        padding: 2,
        //centerOnScroll: 'yes',
        href: 'in10101_man_5.jsp?EXP_NO=' + _case_id + '&Img_index=' + Img_index + '&Img_kind=' +Img_kind ,
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        iframe: {

            scrolling: false,
            preload: true
        },
        // title : '平面示意圖',
        titleShow: false,
        overlayOpacity: 0.3,
        width: 1000,
        height: 688,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function() {

        }
    });
}

//------------------------------
// 顯示畫的圖片
//------------------------------
function setImg(_EXP_NO) {
    var temp_date = Date.now();
    if (_EXP_NO) _EXP_NO = _EXP_NO.trim();

//console.log(" setImg " + _EXP_NO);
    // var image_url = "img/in10101_imgUrlOutput/"+_EXP_NO+".jpg?"+temp_date;
    var _scr = "in10101_getImage.jsp?EXP_NO=" + _EXP_NO + "&Img_index=0&Img_kind=MAP&time=" + temp_date;
    //var _img = '<img id= "drawImg" class= "imgUrlOutput" onload="resetDiv();"  alt="img" src = "'+_scr+'">'
    //_scr = "img/NoImage_area.png?1100203";
    var _img = '<img id= "drawImg" class= "imgUrlOutput"   alt="img" src = "' + _scr + '">'
    $("#show_draw").html(_img);


_scr = "in10101_getImage.jsp?EXP_NO=" + _EXP_NO + "&Img_index=0&Img_kind=PIT&time=" + temp_date;
//_scr = "img/NoImage_loc.png?1100203";
    _img = '<img class= "imgUrlOutput"  alt="img" src = "' + _scr + '">'
    $("#map_canvas").html(_img);
}



//重製 照片
function resetPic() {
/*
    $(".all_pic").each(function() {
        var old_src = $(this).attr("src");

        $(this).attr("src", (old_src + Date.now()))
        //$(this).attr("data-original", (old_src + Date.now()))

    });
    var old_html = $("#show_pic").html();

    $("#show_pic").html(" ");
    setTimeout(function() {
        $("#show_pic").html(old_html);
    }, 500);

*/
}


function customWindowOnload() {
    //window.onload = function (){

    _expNo = $("[name=EXP_NO]");
    _lng = $("[name=X_COORDINATE]");
    _lat = $("[name=Y_COORDINATE]");
    //viewer

    'use strict';
    if (document.getElementById('viewImages')) {
        viewer = new Viewer(document.getElementById('viewImages'), {
            toolbar: 0
        });
    }


    //document.getElementById('showImage').onclick = function() {viewer.show();}
    //setLocation

    $("#setLocation").click(function() {
        popupSetLngLat();
    });

 // 加材料
    $("#BUILDING_KIND_ADD").click(function() {
        var BK_V =  $("#BMSDISOBEY_DISTBUILDING_KIND").find(":selected").val();
        if(BK_V != ""){
        var _BB =  $("#BMSDISOBEY_DISTBUILDING_KIND_SHOW").val();
        if (_BB != null && _BB != ""){
        _BB = _BB+"、"
        }
         $("#BMSDISOBEY_DISTBUILDING_KIND_SHOW").val(_BB + BK_V);
        
        }
        
    });

	//清除材料欄位
	$("#BUILDING_KIND_DEL").click(function() {
		var b = confirm("是否要清除[建造材質]欄位?");
        if (b == true) {
			$("#BMSDISOBEY_DISTBUILDING_KIND_SHOW").val("");
		}
	});

// 加地號
var landindex = 0;
$("#ZONE_AREA_ADD").click(function() {

        landindex++;
        var isLandExist = false;
        var tmp_ZC =  $("#BMSDISOBEY_DISTZONE_CODE").find(":selected").text();
        var tmp_ZC_V =  $("#BMSDISOBEY_DISTZONE_CODE").find(":selected").val();
        var tmp_SN =  $("#BMSDISOBEY_DISTSEC_NAME").find(":selected").text();
        var tmp_SN_V =  $("#BMSDISOBEY_DISTSEC_NAME").find(":selected").val();
        var tmp_NO1 =  $("#BMSDISOBEY_DISTROAD_NO1").val();
        var tmp_NO2 =  $("#BMSDISOBEY_DISTROAD_NO2").val();
        
      tmp_NO1 = addZeroCustom(tmp_NO1, 4);
      tmp_NO2 = addZeroCustom(tmp_NO2, 4);
      
      if(tmp_ZC_V != "" && tmp_ZC_V && tmp_SN_V != "" && tmp_SN_V){
	        $(".ZONE_AREA_LIST").each(function(){
	                var ext_ZC_V = $(this).attr("dist");
	                var ext_SN = $(this).attr("section_nm");
	                var ext_SN_V = $(this).attr("section");
	                var ext_NO1 = $(this).attr("road_no1");
	                var ext_NO2 = $(this).attr("road_no2");
	                var ext_indx = $(this).attr("landindex");
	                
	                landindex = parseInt(ext_indx) + 1; 
	                
	                if(ext_ZC_V === tmp_ZC_V && tmp_SN === ext_SN && tmp_SN_V === ext_SN_V && ext_NO1 === tmp_NO1 && ext_NO2 === tmp_NO2){
	                        isLandExist = true;
	                }
	                
	            });
	            
	            if(isLandExist){
	                alert("該地號已存在,無法新增。");
	            }
	            else{
	                $("#ZONE_AREA").append( "<div class='ZONE_AREA_LIST' landindex='"+landindex+"' dist='"+tmp_ZC_V+"' dist_desc='"+tmp_ZC+"' section='"+tmp_SN_V+"' section_nm='"+tmp_SN+"' road_no1='"+tmp_NO1+"' road_no2='"+tmp_NO2+"'>" + tmp_ZC + tmp_SN + tmp_NO1 + "-" +tmp_NO2 + "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='LAND_DELETE' onclick=\"delLan('"+landindex+"');\"></div>");
	            	checkCslan(tmp_ZC_V, tmp_SN_V, tmp_NO1, tmp_NO2);
	            }
    	}
    	else{
         	alert("尚未輸入完整地號,無法新增。");
         }
    });
// 
      // 違建程序
      $("[name=IBPRSS]").change(function() {
              if($(this).val() == "A"){
                      $(".nomal").show();
                      $(".danger").hide();
              }else if($(this).val() == "B"){
                      $(".danger").show();
                        $('input[name="AD_L"]').each(function() {
                        if ($(this).val() === "B") {
                          $(this).next('.danger').html('建築法&nbsp;81&nbsp;條：等相關規定');
                        }
                          });
                      $(".nomal").hide();
              }
              else if($(this).val() == "C"){
                      $(".danger").show();
                      $(".nomal").hide();

                       $('input[name="AD_L"]').each(function() {
                        if ($(this).val() === "B") {
                          $(this).next('.danger').html('建築法&nbsp;81&nbsp;條：直轄市、縣(市) (局)主管建築機關對頹類或朽壞而有危害公共安全之建築物，應通知所有人或占有人停止使用，並限期命所有人拆除；逾期未拆者，得強制拆除之。前項建築物所有人住址不明無法通知者，得逕予公告強制拆除。');
                        }
                      });
              }
              else{
                	$(".nomal").show();
            	    $(".danger").hide();
              }
      });
        // 違建流程一開始因儲存的資料做變動
        var AD_TYP = $('input[name=AD_TYP]').val();
        if(AD_TYP == "A"){
            $(".nomal").show();
            $(".danger").hide();
        }else{
        	if(AD_TYP == "B"){
        		$(".danger").show();
             $('input[name="AD_L"]').each(function() {
            if ($(this).val() === "B") {
              $(this).next('.danger').html('建築法&nbsp;81&nbsp;條：等相關規定');
            }
          });
            	$(".nomal").hide();
        	}
          else if(AD_TYP == "C"){
            $(".danger").show();
            $('input[name="AD_L"]').each(function() {
            if ($(this).val() === "B") {
              $(this).next('.danger').html('建築法 81 條：直轄市、縣(市) (局)主管建築機關對頹類或朽壞而有危害公共安全之建築物，應通知所有人或占有人停止使用，並限期命所有人拆除；逾期未拆者，得強制拆除之。前項建築物所有人住址不明無法通知者，得逕予公告強制拆除。');
            }
          });
              $(".nomal").hide();
          }
            else{
            	$(".nomal").show();
            	$(".danger").hide();
            }
	    }

        //相對位置說明按鈕
        $("#disBtn").click(function() {
                openLegend();
        });

    //drawPic    
    $("#drawPic").click(function() {
        if (_lng.val() && _lat.val()) {
            var t_lng = parseFloat(_lng.val()),
                t_lat = parseFloat(_lat.val());
            if (t_lng > 122.00456 || t_lng < 120.983438 || t_lat > 25.29965 || t_lat < 24.586478) {

                alert("「違建位置」不在新北市範圍內,請重新挑選。");
            } else {
                popupAddWindow();
            }

        } else {
            alert("請先選擇「違建位置」");
        }
    });
	
	//drawUpload    
    $("#drawUpload").click(function() {
        if (_lng.val() && _lat.val()) {
            var t_lng = parseFloat(_lng.val()),
                t_lat = parseFloat(_lat.val());
            if (t_lng > 122.00456 || t_lng < 120.983438 || t_lat > 25.29965 || t_lat < 24.586478) {

                alert("「違建位置」不在新北市範圍內,請重新挑選。");
            } else {
                popupAddWindowForUpload();
            }

        } else {
            alert("請先選擇「違建位置」");
        }
    });

    // 點欄位觸發日期picker
    $("#CP_TESTSearchs_S_DATE_div1").on("click", function() {
        $("#BMSDISOBEY_DISTDatePicker_GOVER_DATE1_Image").trigger("click");
    });

    //checkBox 事件(違建現況) EXP_STATE
    $("[name=EXP_STATE]").on("click", function() {
        var EXP_STATE = $(this).val();
        if (EXP_STATE != "Z") $('input[name=IBM_ITEM_MEMO]').val("");
    });

    $("[name=EXP_PROSTS]").on("click", function() {


        if (!$(this).prop("checked")) {
            if ($(this).val() == "02") {
                $('input[name=EXP_PROSTS_SITE]').val("");

            } else if ($(this).val() == "99") {

                $('input[name=EXP_PROSTS_DESC]').val("");

            }
        }
    });

    initCheckBox();
    //combobox
    $(".combobox_QQ").chosen({
        search_contains: true
    });

    setImg($("#EXP_NO").text());
//resetPic();
//編輯照片後回來  新增的照片會不見
window.setTimeout(function() {
        if($('#BMSDISOBEY_DISTedtImgIdx').val() === ""){
        }
        else{
                        var pickind = $('#BMSDISOBEY_DISTedtImgIdx').val();
                        if(pickind === "NOW"){
                                pickind = "nowPic";
                        }
                        else if (pickind === "PAT"){
                                pickind = "partPic";
                        }
                        else if (pickind === "ANO"){
                                pickind = "adPic";
                        }
                //refleshPic($("#EXP_NO").text().trim(), pickind );
                $('#BMSDISOBEY_DISTedtImgIdx').val("");
        }
}, 2000);


    ini_zone_code();
        bind_ZONE_CODE();

        //拆除優先類組ListBox改變值
    var $DsortListBox = $("#BMSDISOBEY_DISTDSORT");
    $DsortListBox.on("change", function() {
        var $thisVal = $(this).find(":selected").val();
        setDsort2Option($thisVal);
    });
                
    if(isFirst){
        var dsort_H = $("#BMSDISOBEY_DISTDSORT_H").val();
        setDsort2Option(dsort_H);
        $("#BMSDISOBEY_DISTDSORT").val(dsort_H);
    }
         
  		//專案收費項目
		var $PrjectListBox = $("#BMSDISOBEY_DISTPROJECT");
		$PrjectListBox.on('change', function(){
			var $value = $(this).find(':selected').val();
			setprjfeeoption($value);
		});

        // 加專案
        $("#PROJECT_ADD").click(function() {
                
        var isProjectExist = false;
        var tmp_PRJ =  $("#BMSDISOBEY_DISTPROJECT").find(":selected").text();
        var tmp_PRJ_ID = $("#BMSDISOBEY_DISTPROJECT").find(":selected").val();
        var tmp_PRJFEE_DESC = $("#BMSDISOBEY_DISTPRJFEE").find(":selected").text();
        var tmp_PRJFEE_ID = $("#BMSDISOBEY_DISTPRJFEE").find(":selected").val();
        
        if(tmp_PRJ_ID != "" && tmp_PRJ_ID)
        {
        	var PRJFEE_DESC = '';
	     	if(tmp_PRJFEE_ID == '0')
           	{
	     		PRJFEE_DESC = '';
           	}
	     	else
     		{
	     		PRJFEE_DESC = ', 收費項目: ' + tmp_PRJFEE_DESC;
     		}
        	
        	$(".PROJECT_DATA").each(function(){
				var ext_PRJ = $(this).attr("prj_nm");
	            var ext_PRJ_ID = $(this).attr("prj_code");
	            var ext_PRJFEE_DESC = $(this).attr("prjfee");
                var ext_PRJFEE_ID = $(this).attr("prjfee_desc");
	            
	            if(ext_PRJ === tmp_PRJ && tmp_PRJ_ID === ext_PRJ_ID)
	            {
	                isProjectExist = true;
	            }
	            
	        });
	            
	        if(isProjectExist){
	            alert("該專案已存在,無法新增。");
	        }
	        else{
	            $("#PROJECT_LIST").append( "<div class='PROJECT_DATA' prj_code='"+tmp_PRJ_ID+"' prj_nm='"+tmp_PRJ+"' prjfee='"+tmp_PRJFEE_ID+"' prjfee_desc='"+tmp_PRJFEE_DESC+"'>" + tmp_PRJ + PRJFEE_DESC + "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='PROJECT_DELETE' onclick=\"delProject('"+tmp_PRJ_ID+"');\"></div>");
	        }
	    }
	    else{
	    	alert("尚未選擇專案,無法新增。");
	    }    
        
    });

    // 勘查結果-形式  
    var $radio_ADK = $('input:radio[name=ADK]');
    var $checkbox_AD_CT = $('input:checkbox[name=AD_CT]');
        $("[name=AD_CT]").change(function() {
        if($(this).val() == "A1" || $(this).val() == "A2"){
                $radio_ADK.filter('[value="A"]').prop('checked', true);
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $('input[name=AD_KIND_MEMO]').val("");
        }else if($(this).val() == "B1" || $(this).val() == "B2"){
                $radio_ADK.filter('[value="B"]').prop('checked', true);
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
                $('input[name=AD_KIND_MEMO]').val("");
        }
        });

        $("[name=ADK]").change(function() {
                if($(this).val() == "A"){
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $('input[name=AD_KIND_MEMO]').val("");
            }
            else if($(this).val() == "B"){
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
                $('input[name=AD_KIND_MEMO]').val("");
            }
            else{
                $checkbox_AD_CT.filter('[value="B1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="B2"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A1"]').prop('checked', false);
                $checkbox_AD_CT.filter('[value="A2"]').prop('checked', false);
            }
        });

        // 地址填寫方式預設
        var dis_b_addmod = $('input[name=DIS_B_ADDMOD]').val();
        if(dis_b_addmod === "L"){
                addrType = 2;
                changeAddrSwitch();
        }
        else if(dis_b_addmod === "H"){
                addrType = 1;
                changeAddrSwitch();
        }
		
		//加稽查結論
		$("#CHK_RSLT_ADD").click(function() {
	        var CHK_RLT =  $("#BMSDISOBEY_DISTCHK_RSLT").find(":selected").val();
	        if(CHK_RLT != ""){
	        var AD_CHK_RLT =  $("#BMSDISOBEY_DISTAD_CHK_RSLT").val();
	        if (AD_CHK_RLT != null && AD_CHK_RLT != ""){
	        	AD_CHK_RLT = AD_CHK_RLT+"、"
	        }
	         $("#BMSDISOBEY_DISTAD_CHK_RSLT").val(AD_CHK_RLT + CHK_RLT);
	        
	        }
	        
	    });

	//隨著畫面滾動判斷回到頂端及移至底部的按鈕是否出現
	$(window).scroll(function() {
		
		//最後一頁scrollTop=body-window，50是預留空間
     	var last= $(".container").height() - $(window).height() - 50;
		
		if( $(this).scrollTop() >= last ){
			$("#goBottomBtn").hide();
		}
		else if ( $(this).scrollTop() == 0){
			$("#goTopBtn").hide();
		}
		else {
			$("#goBottomBtn").show();
			$("#goTopBtn").show();	
		}
	}).scroll();

	$("#goTopBtn").click(function() {
		//scroll到最上面
	  	$("html, body").scrollTop(0);
	  	$("#goBottomBtn").show();
	}); 

	$("#goBottomBtn").click(function() {
		//scroll到最下面
		var last= $(".container").height() - $(window).height();
		
	  	$("html, body").scrollTop(last);
	  	$("#goTopBtn").show();
	}); 

	//經緯度逗號顯示與否
	var lng_text = $("#updatelng").text();
	if(lng_text){
		$("#coord_mark").show();
	}
	else{
		$("#coord_mark").hide();
	}
		
} //END  window.onload

//  刪除專案
function delProject(_prj_id) {
     $("div[prj_code="+_prj_id+"]").remove();
}

//  刪除地號
function delLan(_landindex) {
	$(".addrMessage_lan").remove();
    $("div[landindex="+_landindex+"]").remove();
}


//  編輯照片

function editeMode(_EXP_NO, _pic_count, _pic_kind) {
	var target_top = $("html, body").scrollTop(); 
	$('[name=currentScroll]').val(target_top);
	$('[name=editPicParameter]').val(_EXP_NO + "-" + _pic_count + "-" + _pic_kind);
    $("#BMSDISOBEY_DISTButton_EditPic").trigger("click");
}


function initCheckBox() {

    //違建流程 
    var AD_TYP = $('input[name=AD_TYP]').val();
    var $IBPRSS = $('input:radio[name=IBPRSS]');
    $IBPRSS.filter('[value="' + AD_TYP + '"]').prop('checked', true);

    //違建相對位置
    var BWAY = $('input[name=DIS_B_WAY]').val();
    var _DIS_B_WAY = BWAY.split("、");
   
    $.each(_DIS_B_WAY, function(i, val) {
 
        var $IBFORWORD = $('input:checkbox[name=IBFORWORD]');


        $IBFORWORD.filter('[value="' + val + '"]').prop('checked', true);
    });
        
        //廣告物型式
    var AD_KIND = $('input[name=AD_KIND]').val();
        var AD_K = AD_KIND.substring(0, 1);
    var $ADK = $('input:radio[name=ADK]');
    $ADK.filter('[value="' + AD_K + '"]').prop('checked', true);

    //廣告物型式
    var ee = $('input[name=AD_KIND]').val();
    var _AD_KIND = ee.split(";");

    var $checkbox = $('input:checkbox[name=AD_CT]');


    $.each(_AD_KIND, function(i, val) {

        $checkbox.filter('[value="' + val + '"]').prop('checked', true);
    });
        
        //廣告物勘查結果-涉及違反法條
        var ACL = $('input[name=AD_CHK_LAW]').val();
        var _AD_CHK_LAW = ACL.split(";");
        
        var $AD_L = $('input:checkbox[name=AD_L]');


    $.each(_AD_CHK_LAW, function(i, val) {

        $AD_L.filter('[value="' + val + '"]').prop('checked', true);
    });

        //施工狀態
        //114 年不在使用，勘查號碼前三碼大於114的案件，施工狀態不可選擇
        let value = $('#BMSDISOBEY_DISTCASE_ID').val();
        if (value && (isNaN(parseInt(value.substring(0,3))) || parseInt(value.substring(0,3)) < 114)) {
            var finish_state = $('input[name=FINISH_STATE]').val();
            var $radios_finish_state = $('input:radio[name=COMPLITE]');
            $radios_finish_state.filter('[value="' + finish_state + '"]').prop('checked', true);
        }

    //專案名稱顯示與否
    var prjshow = $('input[name=PRJSHOW]').val();
    var $radios_prjshow = $('input:radio[name=PRJ_SHOW]');
    $radios_prjshow.filter('[value="' + prjshow + '"]').prop('checked', true);
        
    //違規項目
    var ibm_item = $('input[name=IBM_ITEM]').val();
    var $radios_ibm_item = $('input:radio[name=EXP_STATE]');
    $radios_ibm_item.filter('[value="' + ibm_item + '"]').prop('checked', true);  

	//材質尺寸高度(或縱長)是否為以上
    var ad_height_up = $('input[name=AD_HEIGHT_UP]').val();
    var $checks_ad_h_up = $('input:checkbox[name=AD_H_UP]');
    $checks_ad_h_up.filter('[value="' + ad_height_up + '"]').prop('checked', true);
}

function addSlash(obj) {
    if (obj.value.length > 0 && obj.value.indexOf("/") == -1) {
        var padding0Str = "";
        for (var loop = 0; loop < (7 - obj.value.length); loop++) padding0Str += "0";
        obj.value = padding0Str + obj.value;
        obj.value = RSplitChar(obj.value, "/", "2,2");
    }
}

function addZero_2(obj) {
    if (obj && obj.value.length > 0) {
        var padding0Str = "";
        for (var loop = 0; loop < (4 - obj.value.length); loop++) padding0Str += "0";
        obj.value = padding0Str + obj.value;
        //obj.value = RSplitChar(obj.value, "/", "2,2");
    }
}



//----
// 綁定LISTBOX
//----
function bind_ZONE_CODE() {

 

    //綁 建造材質
    $("[name=BUILDING_KIND]").change(function() {
        var $thisVal = $(this).find(":selected").val();
        if ($thisVal == "99") {
            //
            $("#BMSDISOBEY_DISTBUILDING_KIND_DESC").show();
        } else {
            $("#BMSDISOBEY_DISTBUILDING_KIND_DESC").hide();
        }
    });
    //init 建造材質
    if ($("#BMSDISOBEY_DISTBUILDING_KIND_DESC")) {
        if ($("[name=BUILDING_KIND]").val() == "99") $("#BMSDISOBEY_DISTBUILDING_KIND_DESC").show();
    }
}

//----
// 設定狀態
//----
function setState(sta) {
        $('[name=SUBMIT_STATE]').val(sta);
}



function getAddrTotal(){
        var ALL_ADDR = "";
        var CODE_DESC = $("#BMSDISOBEY_DISTDIS_B_ADDZON_chosen").find(".chosen-single").text();
        //var CODE_DESC = $("[name=DISTDIS_B_ADDZON]").find(":selected").text();
        var DIS_B_ADD1 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD1"]);
        var DIS_B_ADD2 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD2"]);
        var DIS_B_ADD3 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD3"]);
        var DIS_B_ADD4 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD4"]);
        var DIS_B_ADD5 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD5"]);
        var DIS_B_ADD6 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD6"]);
        var DIS_B_ADD6_1 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD6_1"]);
        var DIS_B_ADD7 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD7"]);
        var DIS_B_ADD7_1 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD7_1"]);
        var DIS_B_ADD8 = getStringValue(document.forms["BMSDISOBEY_DIST"].elements["DIS_B_ADD8"]);
        
        if( CODE_DESC.indexOf("請選擇行政區") == -1 ){ 
                ALL_ADDR += CODE_DESC.trim() ;
        }else{
                ALL_ADDR += "新北市" ;
        }
//if()
        if (DIS_B_ADD1) ALL_ADDR += DIS_B_ADD1;
        if (DIS_B_ADD2) ALL_ADDR += DIS_B_ADD2;
        if (DIS_B_ADD3) ALL_ADDR += DIS_B_ADD3;
        if (DIS_B_ADD4) ALL_ADDR += DIS_B_ADD4;
        
        if ( DIS_B_ADD5 || DIS_B_ADD6 || DIS_B_ADD6_1 ){                
                 ALL_ADDR += DIS_B_ADD5 ;
                 if (DIS_B_ADD6) ALL_ADDR += "-" + DIS_B_ADD6 ;
                 ALL_ADDR += "號";
                 if (DIS_B_ADD6_1) ALL_ADDR += "之" +DIS_B_ADD6_1;       
        }
        
        if (DIS_B_ADD7 || DIS_B_ADD7_1 ){       
                ALL_ADDR += DIS_B_ADD7 + "樓";                           
                if (DIS_B_ADD7_1) ALL_ADDR += "之" + DIS_B_ADD7_1;
        }
        
        if (DIS_B_ADD8) ALL_ADDR += "，"+DIS_B_ADD8 + "室";
        

        return ALL_ADDR;
}       
function getStringValue(obj){
        var result ="";
        if(obj){
                result = obj.value.trim();
        }
        return result;
}               
                        
         //------------------------------------------------------------------------
        // 前面補零 空的也補
        // 
        //------------------------------------------------------------------------
        
        function addZeroCustom(num, len){
                var num_len = num.length;       
                        if(num_len < len){
                                for (var i = len- num_len  ; 0 < i ; i--){
                                        num = "0" + num;
                                }
                        }
                return num;     
        }                    

function gogodownload(){
        var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
        window.open('im10101_prt.jsp?case_id='+case_id+'&out_ext=PDF&data_need_type=Notice','_self'); 
}

function gogodownload2(){
        var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
        window.open('im10101_prt.jsp?case_id='+case_id+'&out_ext=PDF&data_need_type=SurveySheet','_self'); 
}

//專案收費項目
function setprjfeeoption(val){
	var listbox = $('#BMSDISOBEY_DISTPRJFEE');
	
	// 暫時停用Listbox
    listbox.prop("disabled", true).trigger("chosen:updated");
    listbox.removeOptions();
    
    $.post('function_getData.jsp', 
	{
    	boundColumn: "CODE_SEQ", 
        boundColumnAlias: "BNDCOLUMN", 
        textColumn: "CODE_DESC", 
        textColumnAlias: "TXTCOLUMN",
       	tableName: "IBMCODE", 
           whereString: " CODE_TYPE = 'PRJFEE' AND is_del = 'N' AND TRIM('0000' || SUB_SEQ) = '"+val+"'", 
           orderByString: "CODE_SEQ"
   	}, function(data){
   		console.log(data);
   		// 利用給予的textColumn及boundColumn建立一個新的Option
   		listbox.fillOptions(data);
 	}, 'json'
 	).always(function () {
 		// 將Listbox恢復為正常狀態
        listbox.prop("disabled", false).trigger("chosen:updated");
 		
        if (typeof callback === "function") 
        {
            callback();
    	}
 	});
}

/**
 * 拆除優先類組ListBox連動拆除優先子類組ListBox.
 **/
function setDsort2Option( _Dsort ) {

        var listbox = $("#BMSDISOBEY_DISTDSORT2");

        // 暫時停用Listbox
        listbox.prop("disabled", true).trigger("chosen:updated");
        listbox.removeOptions();
                
        $.post("function_getData.jsp", 
        {
            boundColumn: "SUBSTRING(CODE_SEQ,2)", 
            boundColumnAlias: "BNDCOLUMN", 
            textColumn: "CODE_SEQ || ' : ' || CODE_DESC", 
            textColumnAlias: "TXTCOLUMN", 
            tableName: "IBMCODE", 
            whereString: " CODE_TYPE = 'DSORT2' AND CODE_SEQ <> '**' AND SUB_SEQ = '"+_Dsort+"' AND IS_DEL = 'N'", 
            orderByString: "ORDER_BY_SEQ"
        }, function(jData) {
            // 利用給予的textColumn及boundColumn建立一個新的Option
            listbox.fillOptions(jData);
        }, "json"
        ).always(function() {
            // 將Listbox恢復為正常狀態
            listbox.prop("disabled", false).trigger("chosen:updated");

            if (typeof callback === "function") {
                callback();
            }
            
            if(isFirst){
                var dsort2_H = $("#BMSDISOBEY_DISTDSORT2_H").val();
                $("#BMSDISOBEY_DISTDSORT2").val(dsort2_H);
                isFirst = false;
            }
            
        });
}

// -------------------------
// 格示化 違建人資料
// ID_KIND, ID_SEX, ID_NUM, DIS_U_NAME, DIS_U_ADDR
// -------------------------
function setIbmdisnm(){
    var TTdata = "" , _sp = "!@#", _subsp = "#@!" , first = 0, chk=0, chk4kind = 0;
    $(".IBMDISNM").each(function (){
            if( first){
             TTdata += _sp;
            }else{
            first = 1;
            }
            var ibmdisnmAll = $(this);
            var ID_KIND = ibmdisnmAll.find("[name^='ID_KIND']:checked").val();
            var ID_SEX = ibmdisnmAll.find("[name^='ID_SEX']:checked").val();
            var ID_NUM = ibmdisnmAll.find("[name='ID_NUM']").val();
            var DIS_U_NAME = ibmdisnmAll.find("[name='DIS_U_NAME']").val();
            var DIS_U_ADDR = ibmdisnmAll.find("[name='DIS_U_ADDR']").val();
//                      console.log(" 1. " + ID_KIND + " 2. " + ID_SEX+ " 3. " + ID_NUM+ " 4. " + DIS_U_NAME+ " 5. " + DIS_U_ADDR);DIS_U_ADDR
            if(ID_KIND && ID_KIND != "undefined" ){
                    TTdata += ID_KIND ;
                    chk4kind++;
            }
            TTdata += _subsp;
            if(ID_SEX && ID_SEX != "undefined" ){
                    TTdata += ID_SEX ;
                    chk4kind++;
            }
            TTdata += _subsp;
            if(ID_NUM && ID_NUM != "undefined" ){
                    TTdata += ID_NUM ;
                    chk4kind++;
            }
            TTdata += _subsp;
            if(DIS_U_ADDR && DIS_U_ADDR != "undefined" ){
                    TTdata += DIS_U_ADDR ;
                    chk4kind++;
            }
            TTdata += _subsp;
            if(DIS_U_NAME && DIS_U_NAME != "undefined" ){
                    TTdata += DIS_U_NAME;
            }else{
            // 除了姓名以外的欄位有值 再檢查姓名
             if(chk4kind)chk++; 
            
            }
            chk4kind = 0;
           
    });

        $("#BMSDISOBEY_DISTdisnm_all").val( TTdata );
                return chk;
}

function changeAddrSwitch(){
        if(addrType == 1){
            $(".byHand").show();
            $("#s_ROAD_chosen").hide();
                        $("#s_LANE_chosen").hide();
                        $("#s_ALLEY_chosen").hide();
                addrType = 2;
                $("#addrSwitchLink").text("(改採選單模式)");
    }else{
                        $("#s_ROAD_chosen").show();
                        $("#s_LANE_chosen").show();
                        $("#s_ALLEY_chosen").show();
            $(".byHand").hide();
                addrType = 1;
                $("#addrSwitchLink").text("(改採文字模式)");
    }
}

function checkAddr(){
	var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
	var dis_b_addzon = $("#BMSDISOBEY_DISTDIS_B_ADDZON").find(":selected").val();
	var dis_b_add2 = $("#BMSDISOBEY_DISTDIS_B_ADD2").val();
	var dis_b_add3 = $("#BMSDISOBEY_DISTDIS_B_ADD3").val();
	var dis_b_add4 = $("#BMSDISOBEY_DISTDIS_B_ADD4").val();
	var dis_b_add5 = $("#BMSDISOBEY_DISTDIS_B_ADD5").val();
	var dis_b_add6 = $("#BMSDISOBEY_DISTDIS_B_ADD6").val();
	var dis_b_add6_1 = $("#BMSDISOBEY_DISTDIS_B_ADD6_1").val();
	var resultString = "";

	if((dis_b_addzon && dis_b_addzon != "undefined") || (dis_b_add2 && dis_b_add2 != "undefined") || (dis_b_add3 && dis_b_add3 != "undefined") || (dis_b_add4 && dis_b_add4 != "undefined")){
		
		open_blockUI();
		
		$.ajax({
            type: "POST",
            url: "im10101_man_checkAddr.jsp",
            data: {              	
               DIS_B_ADDZON: dis_b_addzon,
               DIS_B_ADD2: dis_b_add2,    
			   DIS_B_ADD3: dis_b_add3,    
			   DIS_B_ADD4: dis_b_add4,
			   DIS_B_ADD5: dis_b_add5,
			   DIS_B_ADD6: dis_b_add6,
			   DIS_B_ADD6_1: dis_b_add6_1,
			   CASE_ID: case_id,          
               random:Math.floor(Math.random()*1000000)
            }
        }).done(function(o) {

			//判斷資料庫是否存在
			if(o){
				var _result = o.trim();
				
				if( _result == "0"){
					$(".addrMessage").remove();
					resultString = "<div class='addrMessage' style='color: green;'>此地址查無其他違章案件</div>";
					$(".addr_div").before(resultString);
				}
				else{
					$(".addrMessage").remove();
					resultString = "<div class='addrMessage' style='color: red;'>此地址已存在"+_result+"筆其他違章案件</div>";
					$(".addr_div").before(resultString);
				}

			}

        }).fail(function( pp ) { 
			alert("檢查地址失敗!" );
		}).always(function( qq ) {
			close_blockUI();
        });
	}
	else{
		alert("地址尚未輸入，無法進行檢查");
	}
}

//檢查地號是否存在資料庫
function checkCslan(_dist, _section, _road_no1, _road_no2){
	var resultString = "";
	var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
	
	if((_dist && _dist != "undefined") || (_section && _section != "undefined") || (_road_no1 && _road_no1 != "undefined") || (_road_no2 && _road_no2 != "undefined")){
		open_blockUI();
		
		$.ajax({
            type: "POST",
            url: "im10101_man_checkCslan.jsp",
            data: {              	
               DIST: _dist,
               SECTION: _section,    
			   ROAD_NO1: _road_no1,    
			   ROAD_NO2: _road_no2,
			   CASE_ID: case_id,          
               random:Math.floor(Math.random()*1000000)
            }
        }).done(function(o) {

			//判斷資料庫是否存在
			if(o){
				var _result = o.trim();
				
				if( _result == "0"){
					$(".addrMessage_lan").remove();
					resultString = "<div class='addrMessage_lan' style='color: green; padding-top: 10px;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此地號查無其他違章案件</div>";
					$(".cslan_title").before(resultString);
				}
				else{
					$(".addrMessage_lan").remove();
					resultString = "<div class='addrMessage_lan' style='color: red; padding-top: 10px;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;此地號已存在"+_result+"筆其他違章案件</div>";
					$(".cslan_title").before(resultString);
				}

			}

        }).fail(function( pp ) { 
			alert("檢查地號失敗!" );
		}).always(function( qq ) {
			close_blockUI();
        });
	}
}

function copyCase(){
	var case_id =  $("#BMSDISOBEY_DISTCASE_ID").val();
	var rvldate = "";
	if(document.forms["BMSDISOBEY_DIST"].RVLDATE ){
	    var RVLDATE_ori = document.forms["BMSDISOBEY_DIST"].RVLDATE.value;
	    rvldate =  RVLDATE_ori.replace(/\D/g, "") ; 
	}
	var dis_type = $("#BMSDISOBEY_DISTDSORT").find(":selected").val();
	var dis_sort = $("#BMSDISOBEY_DISTDSORT2").find(":selected").val();
	var building_kind = $("#BMSDISOBEY_DISTBUILDING_KIND_SHOW").val();
	var finish_state = $('input[name*=COMPLITE]:checked').val();
	var ibm_item = $('input[name*=EXP_STATE]:checked').val();

	var CSPRJ = "";
    $(".PROJECT_DATA").each(function(){
        var ext_PRJ = $(this).attr("prj_nm");
        var ext_PRJ_ID = $(this).attr("prj_code");
        
        var ext_Project = ext_PRJ_ID + "-" + ext_PRJ;
        
        if(CSPRJ)CSPRJ += ";";
        CSPRJ += ext_Project;
    });    

	var ad_name = $("#BMSDISOBEY_DISTAD_NAME").val();
	var ad_typ = $('input[name*=IBPRSS]:checked').val();
	var dis_b_addzon = $("#BMSDISOBEY_DISTDIS_B_ADDZON").find(":selected").val();
	var dis_b_add_desc = $("#BMSDISOBEY_DISTDIS_B_ADD_DESC").val();
	var dis_b_add1 = $("#BMSDISOBEY_DISTDIS_B_ADD1").val();
	var dis_b_add2 = $("#BMSDISOBEY_DISTDIS_B_ADD2").val();
	var dis_b_add3 = $("#BMSDISOBEY_DISTDIS_B_ADD3").val();
	var dis_b_add4 = $("#BMSDISOBEY_DISTDIS_B_ADD4").val();
	var dis_b_add5 = $("#BMSDISOBEY_DISTDIS_B_ADD5").val();
	var dis_b_add6 = $("#BMSDISOBEY_DISTDIS_B_ADD6").val();
	var dis_b_add6_1 = $("#BMSDISOBEY_DISTDIS_B_ADD6_1").val();
	var dis_b_add7 = $("#BMSDISOBEY_DISTDIS_B_ADD7").val();
	var dis_b_add7_1 = $("#BMSDISOBEY_DISTDIS_B_ADD7_1").val();
	var dis_b_add8 = $("#BMSDISOBEY_DISTDIS_B_ADD8").val();
	var dis_b_add9 = $("#BMSDISOBEY_DISTDIS_B_ADD9").val();
	
	var dis_b_way = "";
    $('input[name=IBFORWORD]:checked').each(function(){
            this_val = $(this).attr("value") ;
            if(dis_b_way) dis_b_way+= "、";
            
            dis_b_way += this_val;
    });

	var dis_b_addmod = $("#BMSDISOBEY_DISTDIS_B_ADDMOD").val();

	var CSLAN = "";
    $(".ZONE_AREA_LIST").each(function(){
        var ext_ZC_V = $(this).attr("dist_desc");
        var ext_ZC = $(this).attr("dist");
        var ext_SN = $(this).attr("section_nm");
        var ext_SN_V = $(this).attr("section");
        var ext_NO1 = $(this).attr("road_no1");
        var ext_NO2 = $(this).attr("road_no2");
        var ext_Land = ext_ZC + "-" + ext_ZC_V + "-" + ext_SN_V + "-" + ext_SN + "-" + ext_NO1 + "-" + ext_NO2;
        
        if(CSLAN)CSLAN += ";";
        CSLAN += ext_Land;
    });

	var AD_KIND = "";
    $('input[name*=AD_CT]:checked').each(function(){
            this_val = $(this).attr("value") ;
            if(AD_KIND) AD_KIND+= ";";
            
            AD_KIND += this_val;
    });
    var AD_KIND_MEMO =  $('input[name=AD_KIND_MEMO]').val();
    if(AD_KIND_MEMO != ""){
    	AD_KIND = "Z";
    }
    
    var building_height = $("#BMSDISOBEY_DISTBUILDING_HEIGHT").val();
	var AD_H_UP = $('input[name*=AD_H_UP]:checked').val();
    if(!AD_H_UP)AD_H_UP = "N";

	var ad_chk_law = "";
    $('input[name*=AD_L]:checked').each(function(){
        this_val = $(this).attr("value") ;
        if(ad_chk_law) ad_chk_law+= ";";
        
        ad_chk_law += this_val;
    });

	var ad_chk_law_mm = $("#BMSDISOBEY_DISTAD_CHK_LAW_MM").val();
	var ad_chk_rslt =  $("#BMSDISOBEY_DISTAD_CHK_RSLT").val();
	var usr_knd = $('input[name*=ID_KIND1]:checked').val();
	var usr_sex = $('input[name*=ID_SEX1]:checked').val();
	var usr_id = $("#BMSDISOBEY_DISTID_NUM").val();
	var ib_user = $("input[name=DIS_U_NAME]").val();
	var usr_add = $("input[name=DIS_U_ADDR]").val();
	var ibm_item_memo = $("#BMSDISOBEY_DISTIBM_ITEM_MEMO").val();
	var ad_deadline = $("#BMSDISOBEY_DISTAD_DEADLINE").val();
	var case_ori = $("#BMSDISOBEY_DISTCASE_ORI").find(":selected").val();

	open_blockUI();
	
	$.ajax({
        type: "POST",
        url: "im10101_man_copyCase.jsp",
        data: {              	
           CASE_ID: case_id,    
		   RVLDATE: rvldate,    
		   DIS_TYPE: dis_type,
		   DIS_SORT: dis_sort,
		   BUILDING_KIND: building_kind,
		   FINISH_STATE: finish_state,  
		   IBM_ITEM: ibm_item, 
		   PRJ_LIST: CSPRJ,   
		   AD_NAME: ad_name, 
		   AD_TYP: ad_typ,
		   DIS_B_ADDZON: dis_b_addzon,
		   DIS_B_ADD_DESC: dis_b_add_desc,
		   DIS_B_ADD1: dis_b_add1,
		   DIS_B_ADD2: dis_b_add2,
		   DIS_B_ADD3: dis_b_add3,
		   DIS_B_ADD4: dis_b_add4,
		   DIS_B_ADD5: dis_b_add5,
		   DIS_B_ADD6: dis_b_add6,
		   DIS_B_ADD6_1: dis_b_add6_1,
		   DIS_B_ADD7: dis_b_add7,
		   DIS_B_ADD7_1: dis_b_add7_1,
		   DIS_B_ADD8: dis_b_add8,
		   DIS_B_ADD9: dis_b_add9,
		   DIS_B_WAY: dis_b_way,
		   DIS_B_ADDMOD: dis_b_addmod,
		   LAN_LIST: CSLAN,
		   AD_KIND: AD_KIND,
		   AD_KIND_MEMO: AD_KIND_MEMO,
		   BUILDING_HEIGHT: building_height,
		   AD_HEIGHT_UP: AD_H_UP,
		   AD_CHK_LAW: ad_chk_law,
		   AD_CHK_LAW_MM: ad_chk_law_mm,
		   AD_CHK_RSLT: ad_chk_rslt,
		   USR_KND: usr_knd,
		   USR_SEX: usr_sex,
		   USR_ID: usr_id,
		   IB_USER: ib_user,
		   USR_ADD: usr_add,
		   IBM_ITEM_MEMO: ibm_item_memo,
		   AD_DEADLINE: ad_deadline,
		   CASE_ORI: case_ori,        
           random:Math.floor(Math.random()*1000000)
        }
    }).done(function(o) {
		$("#pasteBtn").show();
		$("#pasteMark").show();
		alert("案件資料複製成功!" );
    }).fail(function( pp ) { 
		alert("案件資料複製失敗!" );
	}).always(function( qq ) {
		close_blockUI();
    });
}

function pasteCase(){
	
	var RVLDATE = $("#BMSDISOBEY_DISTRVLDATE");
	var DIS_TYPE = $("#BMSDISOBEY_DISTDSORT");
	var DIS_SORT = $("#BMSDISOBEY_DISTDSORT2");
	var BUILDING_KIND = $("#BMSDISOBEY_DISTBUILDING_KIND_SHOW");
	var FINISH_STATE = $('input:radio[name=COMPLITE]');
	var IBM_ITEM = $('input:radio[name=EXP_STATE]');
    var PROJECT_LIST = $("#PROJECT_LIST");
	var AD_NAME = $("#BMSDISOBEY_DISTAD_NAME");
	var AD_TYP = $('input:radio[name=IBPRSS]');
	var DIS_B_ADDZON = $("#BMSDISOBEY_DISTDIS_B_ADDZON");
	var DIS_B_ADD_DESC = $("#BMSDISOBEY_DISTDIS_B_ADD_DESC");
	var DIS_B_ADD1 = $("#BMSDISOBEY_DISTDIS_B_ADD1");
	var DIS_B_ADD2 = $("#BMSDISOBEY_DISTDIS_B_ADD2");
	var DIS_B_ADD3 = $("#BMSDISOBEY_DISTDIS_B_ADD3");
	var DIS_B_ADD4 = $("#BMSDISOBEY_DISTDIS_B_ADD4");
	var DIS_B_ADD5 = $("#BMSDISOBEY_DISTDIS_B_ADD5");
	var DIS_B_ADD6 = $("#BMSDISOBEY_DISTDIS_B_ADD6");
	var DIS_B_ADD6_1 = $("#BMSDISOBEY_DISTDIS_B_ADD6_1");
	var DIS_B_ADD7 = $("#BMSDISOBEY_DISTDIS_B_ADD7");
	var DIS_B_ADD7_1 = $("#BMSDISOBEY_DISTDIS_B_ADD7_1");
	var DIS_B_ADD8 = $("#BMSDISOBEY_DISTDIS_B_ADD8");
	var DIS_B_ADD9 = $("#BMSDISOBEY_DISTDIS_B_ADD9");
	var DIS_B_ADDMOD = $("#BMSDISOBEY_DISTDIS_B_ADDMOD");
	var ZONE_AREA = $("#ZONE_AREA");
	var AD_KIND_MEMO =  $('input[name=AD_KIND_MEMO]');
	var BUILDING_HEIGHT = $("#BMSDISOBEY_DISTBUILDING_HEIGHT");
	var $checks_ad_h_up = $('input:checkbox[name=AD_H_UP]');
	var AD_CHK_LAW_MM = $("#BMSDISOBEY_DISTAD_CHK_LAW_MM");
	var AD_CHK_RSLT =  $("#BMSDISOBEY_DISTAD_CHK_RSLT");
	var USR_ID = $("#BMSDISOBEY_DISTID_NUM");
	var IB_USER = $("input[name=DIS_U_NAME]");
	var USR_ADD = $("input[name=DIS_U_ADDR]");
	var IBM_ITEM_MEMO = $("#BMSDISOBEY_DISTIBM_ITEM_MEMO");
	var AD_DEADLINE = $("#BMSDISOBEY_DISTAD_DEADLINE");
	var CASE_ORI = $("#BMSDISOBEY_DISTCASE_ORI");

	open_blockUI();
	
	$.ajax({
        type: "POST",
        url: "im10101_man_pasteCase.jsp",
        data: {              	
           IB_PRCS: "B", 
           random:Math.floor(Math.random()*1000000)
        }
    }).done(function(jData) {
		jData = JSON.parse(jData);
		
        RVLDATE.val(RSplitChar(jData.RVLDATE, "/", "2,2"));
        
        //因應Listbox聯動
        DIS_TYPE.val(jData.DIS_TYPE);
        $("#BMSDISOBEY_DISTDSORT2_H").val(jData.DIS_SORT);
        isFirst = true;
        setDsort2Option(jData.DIS_TYPE);
        
        BUILDING_KIND.val(jData.BUILDING_KIND);
        FINISH_STATE.filter('[value="' + jData.FINISH_STATE + '"]').prop('checked', true);
        IBM_ITEM.filter('[value="' + jData.IBM_ITEM + '"]').prop('checked', true);
        PROJECT_LIST.html(jData.PRJLIST);
        
        AD_NAME.val(jData.AD_NAME);
    	  AD_TYP.filter('[value="' + jData.AD_TYP + '"]').prop('checked', true);
        DIS_B_ADDZON.val(jData.DIS_B_ADDZON).trigger("chosen:updated");
        DIS_B_ADD_DESC.val(jData.DIS_B_ADD_DESC);
        DIS_B_ADD1.val(jData.DIS_B_ADD1);
        DIS_B_ADD2.val(jData.DIS_B_ADD2);
        DIS_B_ADD3.val(jData.DIS_B_ADD3);
        DIS_B_ADD4.val(jData.DIS_B_ADD4);
        DIS_B_ADD5.val(jData.DIS_B_ADD5);
        DIS_B_ADD6.val(jData.DIS_B_ADD6);
        DIS_B_ADD6_1.val(jData.DIS_B_ADD6_1);
        DIS_B_ADD7.val(jData.DIS_B_ADD7);
        DIS_B_ADD7_1.val(jData.DIS_B_ADD7_1);
        DIS_B_ADD8.val(jData.DIS_B_ADD8);
        DIS_B_ADD9.val(jData.DIS_B_ADD9);
        DIS_B_ADDMOD.val(jData.DIS_B_ADDMOD);
        
        initAddrListBox();
        if(jData.DIS_B_ADDMOD === "L"){
            addrType = 2;
            changeAddrSwitch();
	    }
	    else if(jData.DIS_B_ADDMOD === "H"){
            addrType = 1;
            changeAddrSwitch();
	    }
        
        var BWAY = jData.DIS_B_WAY;
    	var _DIS_B_WAY = BWAY.split("、");
   
	    $.each(_DIS_B_WAY, function(i, val) {
	 
	        var $IBFORWORD = $('input:checkbox[name=IBFORWORD]');
	
	
	        $IBFORWORD.filter('[value="' + val + '"]').prop('checked', true);
	    });
	    
	    ZONE_AREA.html(jData.LAN_LIST);
	    
	    //廣告物型式
	    var AD_K = jData.AD_KIND.substring(0, 1);
	    var $ADK = $('input:radio[name=ADK]');
	    $ADK.filter('[value="' + AD_K + '"]').prop('checked', true);
	
	    //廣告物型式
	    var _AD_KIND = jData.AD_KIND.split(";");
	
	    var $checkbox = $('input:checkbox[name=AD_CT]');
	
	
	    $.each(_AD_KIND, function(i, val) {
	        $checkbox.filter('[value="' + val + '"]').prop('checked', true);
	    });
	    
	    AD_KIND_MEMO.val(jData.AD_KIND_MEMO);
	    BUILDING_HEIGHT.val(jData.BUILDING_HEIGHT);
	    $checks_ad_h_up.filter('[value="' + jData.AD_HEIGHT_UP + '"]').prop('checked', true);
	    
	    //廣告物勘查結果-涉及違反法條
        var ACL = jData.AD_CHK_LAW;
        var _AD_CHK_LAW = ACL.split(";");
        
        var $AD_L = $('input:checkbox[name=AD_L]');

	    $.each(_AD_CHK_LAW, function(i, val) {
	
	        $AD_L.filter('[value="' + val + '"]').prop('checked', true);
	    });
        
        AD_CHK_LAW_MM.val(jData.AD_CHK_LAW_MM);
        AD_CHK_RSLT.val(jData.AD_CHK_RSLT);
        
        var USR_KND = $('input:radio[name*=ID_KIND]');
	    USR_KND.filter('[value="' + jData.USR_KND + '"]').prop('checked', true);
	    
	    var USR_SEX = $('input:radio[name*=ID_SEX]');
	    USR_SEX.filter('[value="' + jData.USR_SEX + '"]').prop('checked', true);
	    
	    USR_ID.val(jData.USR_ID);
	    IB_USER.val(jData.IB_USER);
	    USR_ADD.val(jData.USR_ADD);
	    IBM_ITEM_MEMO.val(jData.IBM_ITEM_MEMO);
	    AD_DEADLINE.val(jData.AD_DEADLINE);
	    CASE_ORI.val(jData.CASE_ORI);
    
      if(jData.AD_TYP == "A"){
                $(".nomal").show();
                $(".danger").hide();
      }
      else{
        if(jData.AD_TYP == "B"){
          $(".danger").show();
            $('input[name="AD_L"]').each(function() {
          if ($(this).val() === "B") {
            $(this).next('.danger').html('建築法&nbsp;81&nbsp;條：等相關規定');
          }
        });
            $(".nomal").hide();
        }
        else if(jData.AD_TYP == "C"){
          $(".danger").show();
          $('input[name="AD_L"]').each(function() {
          if ($(this).val() === "B") {
            $(this).next('.danger').html('建築法 81 條：直轄市、縣(市) (局)主管建築機關對頹類或朽壞而有危害公共安全之建築物，應通知所有人或占有人停止使用，並限期命所有人拆除；逾期未拆者，得強制拆除之。前項建築物所有人住址不明無法通知者，得逕予公告強制拆除。');
          }
        });
            $(".nomal").hide();
        }
          else{
            $(".nomal").show();
            $(".danger").hide();
          }
    }
		alert("案件資料貼上成功!" );
    }).fail(function( pp ) { 
		alert("案件資料貼上失敗!" );
	}).always(function( qq ) {
		close_blockUI();
    });
}

function open_blockUI(){
	$.ajaxSettings.async = false;
	$.blockUI({message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;頁面載入中, 請稍候...', css:{border:'none', padding:'6px', backgroundColor:'#000', '-webkit-border-radius': '10px', '-moz-border-radius': '10px', opacity: .5, color:'#FFF'}});
	$.ajaxSettings.async = true;
}

function close_blockUI(){
	$.unblockUI();
}

function popupAddWindowForUpload(){
	
	var _CASE_ID = $("#EXP_NO").text();
	
	$.fancybox.open({
        margin: 10,
        padding: 2,
        //centerOnScroll: 'yes',
        href: 'in10101_uploadForGeo.jsp?CASE_ID=' + _CASE_ID + '&picType=GEO&picSeq=2',
        type: 'iframe',
        helpers: {
            overlay: {
                closeClick: false
            }
        },
        iframe: {

            scrolling: false,
            preload: true
        },
       
        titleShow: false,
        overlayOpacity: 0.3,
        width: 1000,
        height: 688,
        minWidth: 688,
        hideOnContentClick: false,
        closeBtn: false,
        afterClose: function() {
        	setTimeout(function() {
                setImg(_CASE_ID);
            }, 1500);
        }
    });
}

//便利貼 開始
var showEditPostIt = false;
function editPostIt() {
	$('#postit_edit').show();
	$('#editPostIt').hide();
	$('#hideEditPostIt').show();
	$('.delPostIt').show();
	showEditPostIt = true;
}
function hideEditPostIt() {
	$('#postit_edit').hide();
	$('#editPostIt').show();
	$('#hideEditPostIt').hide();
	$('.delPostIt').hide();
	showEditPostIt = false;
}

function delPostIt(uuid, newRec) {
	var reg=new RegExp("<br/>","g");
	if (!confirm('確定要刪除這則訊息嗎：\r\n'+newRec.replace(reg,"\r\n"))) {
		return;
	}
	$.ajax({
        type: 'POST',
        url: 'post_it_delete.jsp',
        data: {
        	uuid: uuid,
        	empNo: '<%= session.getAttribute("UserID") %>'
        },
        async: false,
        dataType: 'json',
        success: function(data) {
        	if (data.result == '') {
        		selPostIt();
        	}
        },
        error: function() {
            
        }
    });
}

function selPostIt() {
	var case_id = $('input[name=CASE_ID]').val();
	$('#postit_show').html('');
	$.ajax({
        type: 'POST',
        url: 'post_it_select.jsp',
        data: {
        	caseId: case_id
        },
        async: false,
        dataType: 'json',
        success: function(data) {
        	var result = data.result;
        	var dataList = data.dataList;
        	for (var i = 0; i < dataList.length; i++) {
        		var obj = dataList[i];
				$('#postit_show').append('<br><div>'+obj.newRec+'<br>'+obj.empno+'&nbsp;'+obj.recTime+'<img src="img/DeleteQ.png" class= "img_add delPostIt" onclick="delPostIt(\''+obj.uuid+'\',\''+ obj.newRec.replace(/"/g, '&quot;')+'\');" >'+'</div>');
				$('#postit_input').val('');
			}
        	if (showEditPostIt) {
        		editPostIt();
        	} else {
        		hideEditPostIt()
        	}
        },
        error: function() {
            
        }
    });
}

function addPostIt() {
	var postit_input = $('#postit_input').val().replace(/\n/g,"<br/>");
	var case_id = $('input[name=CASE_ID]').val();
	if (postit_input.length == 0) {
		alert('請輸入便利貼內容！');
		return;
	}
	$.ajax({
        type: 'POST',
        url: 'post_it_insert.jsp',
        data: {
        	caseId: case_id,
        	newRec: postit_input,
        	empNo: '<%= session.getAttribute("UserID") %>'
        },
        async: false,
        dataType: 'json',
        success: function(data) {
        	var result = data.result;
        	var uuid = data.uuid;
        	var newRec = data.newRec;
        	var recTime = data.recTime;
        	if (result == '') {
        		//$('#postit_show').append('<br><div>'+newRec+'&nbsp;'+recTime+'<img src="img/DeleteQ.png" class= "img_add delPostIt" onclick="delPostIt(\''+uuid+'\');" >'+'</div>');
        		selPostIt();
        	}
        },
        error: function() {
            
        }
    }).done(function(o) {

    }).fail(function( qq ) { 
         
    }).always(function( qq ) { 

    });
}
//便利貼 結束

</script>
<!-- 202210 ICDC Project Modify -- begin-->
<script type="text/javascript">
$(document).ready(function(){ 
	maps_service.load('<%=googlemapsserviceurl%>');
	$("#google-map-link").click(function(){
		let address = getAddrTotal(); 
		maps_service.open(address);
	});
	img_service.init_events();

}); 
</script>
<!-- 202210 ICDC Project Modify -- end-->
<style type="text/css">

.danger{
display:none;}
</style>
<!-- 202210 ICDC Project Modify -- begin-->
<script language="JavaScript" src="GoogleMapsService.js?v=<%= (new java.util.Date()).getTime()%>" type="text/javascript" charset="utf-8"></script>
<script language="JavaScript" src="ImageService.js?v=<%= (new java.util.Date()).getTime()%>" type="text/javascript" charset="utf-8"></script>
<!-- 202210 ICDC Project Modify -- end--> 
</head>
<body>
<div class="container">
  <div class="up_titte">
    <span class="tittle_span">&nbsp;&nbsp;&nbsp;</span> <span class="tittle_label">&nbsp;違章認定作業</span> 
  </div>
 
  <ccs:record name='BMSDISOBEY_DIST'>
  <form id="BMSDISOBEY_DIST" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
    <table class="table" cellspacing="0" cellpadding="0">
      <ccs:error_block>
      <tr id="BMSDISOBEY_DISTErrorBlock" class="Error">
        <td colspan="4"><span style="FONT-WEIGHT: 700"><ccs:error_text/></span></td> 
      </tr>
 </ccs:error_block>
      <!-- yao 1 star-->
<a href="javascript:void(0)" id="goTopBtn" style="position:fixed;bottom:50px;right:5px;" title="回到頂端"><img src="img/page_up.png"></a> <a href="javascript:void(0)" id="goBottomBtn" style="position:fixed;bottom:5px;right:5px;" title="移至底部"><img src="img/page_down.png?1100830"></a> 
      <div class="btn-contain">
            <div id="post_it_div" style="padding:5px; left;position: absolute; right: 250px; width: 250px;border: solid;border-color: green;background-color: rgba(255,255,0,0.8);">
	            <div id="post_it_divheader" style="background-color: #fce703;">便利貼&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
	            	<a onclick="editPostIt();" id="editPostIt">編輯</a>
	            	<a onclick="hideEditPostIt();" id="hideEditPostIt" style="display: none;">取消編輯</a>
	            </div>
		        <div id="postit_edit" style="display: none;">
		            <textarea id="postit_input" oninput="if(value.length>50)value=value.slice(0,50)" rows="3" cols="20"></textarea>
		            <img class="img_add" src="img/Add2.png" onclick="addPostIt();">
		        </div>
		        <div id="postit_show" style="font-size: 14px;"></div>
            </div>
        <div class="topbtn-right">
<a style="font-size: 16px;" id="copyBtn" onclick="copyCase();">特定欄位複製</a><span id="pasteMark">&nbsp;/&nbsp;</span><a style="font-size: 16px;" id="pasteBtn" onclick="pasteCase();">特定欄位貼上</a> <input type="hidden" name="<ccs:control name='canPasteCase' property='name'/>" value="<ccs:control name='canPasteCase'/>" id="BMSDISOBEY_DISTcanPasteCase">
        </div>
 
      </div>
 
      <tr>
        <td style="border-top: 0 !important;">
          <div>
            <ul class="nav nav-tabs">
              <li class="active"><a href="#ttab1" data-toggle="tab">勘查紀錄表</a> 
              <li><a href="#ttab2" data-toggle="tab">認定通知書</a> 
              <li class="caseProgress"><a href="#ttab3" data-toggle="tab">案件歷程</a> </li>
 
            </ul>
 
          </div>
 
          <div class="tab-content">
            <table id="ttab1" class="tab-pane fade in active table table-bordered" style="WIDTH: 100%">
              <!-- yao 1 end -->
              <tr class="Controls" style="DISPLAY: none">
                <td style="PADDING-BOTTOM: 0px" colspan="4">
                  <div style="WIDTH: 100%">
                    <div class="tittle_left bridge">
                      查報單號：<span id="EXP_NO"><ccs:control name='EXP_NO'/></span> 
                    </div>
 
                    <div class="tittle_center">
                      <span><ccs:control name='UserJob'/>勘查紀錄表</span><br>
                    </div>
 
                    <div class="tittle_right bridge">
                      <ccs:control name='EXP_DATE_SHOW'/> <input type="hidden" id="BMSDISOBEY_DISTSUBMIT_STATE" value="<ccs:control name='SUBMIT_STATE'/>" name="<ccs:control name='SUBMIT_STATE' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTCASE_ID" value="<ccs:control name='CASE_ID'/>" name="<ccs:control name='CASE_ID' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTACC_RLT" value="<ccs:control name='ACC_RLT'/>" name="<ccs:control name='ACC_RLT' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTPRINT_STATE" value="<ccs:control name='PRINT_STATE'/>" name="<ccs:control name='PRINT_STATE' property='name'/>"><input type="hidden" name="<ccs:control name='editPicParameter' property='name'/>" value="<ccs:control name='editPicParameter'/>" id="BMSDISOBEY_DISTeditPicParameter"><input type="hidden" name="<ccs:control name='editPicUrl' property='name'/>" value="<ccs:control name='editPicUrl'/>" id="BMSDISOBEY_DISTeditPicUrl"><input type="hidden" name="<ccs:control name='currentScroll' property='name'/>" value="<ccs:control name='currentScroll'/>" id="BMSDISOBEY_DISTcurrentScroll"><input type="hidden" name="<ccs:control name='ACTION_MOD' property='name'/>" value="<ccs:control name='ACTION_MOD'/>" id="BMSDISOBEY_DISTACTION_MOD"><input type="hidden" name="<ccs:control name='ibmfym_347' property='name'/>" value="<ccs:control name='ibmfym_347'/>" id="BMSDISOBEY_DISTibmfym_347">
                    </div>
 
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th" style="WIDTH: 7%">
                <label>&nbsp;勘查紀錄號碼&nbsp;</label>
                <input class="td-content-required" id="BMSDISOBEY_DISTedtImgIdx" type="hidden" value="<ccs:control name='edtImgIdx'/>" name="<ccs:control name='edtImgIdx' property='name'/>">
                </td> 
                <td class="td-content" style="WIDTH: 25%; vertical-align: middle;"><label><ccs:control name='CASE_ID_MEMO'/></label></td> 
                <td class="th requiredFild" style="WIDTH: 7%"><label>*&nbsp;違建流程&nbsp;</label></td> 
                <td class="td-content-required" style="WIDTH: 35%">
                  <label><input type="radio" checked value="A" name="IBPRSS">&nbsp;大型帆布招牌廣告</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <label><input type="radio" value="B" name="IBPRSS">&nbsp;危險廢棄招牌</label>
                  <label><input type="radio" value="C" name="IBPRSS">&nbsp;高風險廣告物</label>
                  <input type="hidden" id="BMSDISOBEY_DISTAD_TYP" value="<ccs:control name='AD_TYP'/>" name="<ccs:control name='AD_TYP' property='name'/>">
                </td> 
                <td class="th" style="WIDTH: 15%;"><label>&nbsp;承辦人&nbsp;</label></td> 
                <td style="vertical-align: middle;"><label><ccs:control name='REG_EMP'/></label></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;勘查日期&nbsp;</label></td> 
                <td class="td-content-required" colspan="5"><input type="text" id="BMSDISOBEY_DISTRVLDATE" size="9" value="<ccs:control name='RVLDATE'/>" name="<ccs:control name='RVLDATE' property='name'/>">
                  <ccs:datepicker name='DatePicker_EXP_DATE1'><a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');" id="BMSDISOBEY_DISTDatePicker_EXP_DATE1"><img id="BMSDISOBEY_DISTDatePicker_EXP_DATE1_Image" border="0" alt="Show Date Picker" src="Styles/Blueprint/Images/DatePicker.gif"></a></ccs:datepicker></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;廣告名稱及內容&nbsp;</label></td> 
                <td class="td-content-required" colspan="5"><input type="text" id="BMSDISOBEY_DISTAD_NAME" maxlength="100" size="100" value="<ccs:control name='AD_NAME'/>" name="<ccs:control name='AD_NAME' property='name'/>"></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label for="BMSDISOBEY_DISTDIS_B_ADD1">*&nbsp;座落地點&nbsp;</label></td> 
                <td class="td-content-required" colspan="5">
                  <div class="addr_div">
                    <label>地址：</label> 
                    <select id="BMSDISOBEY_DISTDIS_B_ADDZON" class="combobox_QQ" style="WIDTH: 146px" name="<ccs:control name='DIS_B_ADDZON' property='name'/>">
                      <option selected value="">(請選擇行政區)</option>
 <ccs:control name='DIS_B_ADDZON' property='options'/> 
                    </select>
 <input type="hidden" id="BMSDISOBEY_DISTDIS_B_ADD_DESC" value="<ccs:control name='DIS_B_ADD_DESC'/>" name="<ccs:control name='DIS_B_ADD_DESC' property='name'/>">&nbsp;&nbsp;<input type="text" id="BMSDISOBEY_DISTDIS_B_ADD1" class="byHand" maxlength="20" value="<ccs:control name='DIS_B_ADD1'/>" name="<ccs:control name='DIS_B_ADD1' property='name'/>" size="20" placeholder="村里鄰">&nbsp;&nbsp;<input type="text" id="BMSDISOBEY_DISTDIS_B_ADD2" class="byHand" maxlength="20" value="<ccs:control name='DIS_B_ADD2'/>" name="<ccs:control name='DIS_B_ADD2' property='name'/>" size="20" placeholder="路(街)名">
                    <select id="s_ROAD" class="combobox_QQ" style="WIDTH: 150px" name="s_ROAD">
                      <option selected value="">路(街)名</option>
 
                    </select>
 <a onclick="changeAddrSwitch()" id="addrSwitchLink" style="vertical-align:super; CURSOR: pointer;font-size:8px;">(改採文字模式)</a>&nbsp;&nbsp;<label style="width: 106px; font-size: 16px;" class="btn btn-danger" onclick="checkAddr();">地址檢查</label><input type="hidden" id="BMSDISOBEY_DISTDIS_B_ADDMOD" value="<ccs:control name='DIS_B_ADDMOD'/>" name="<ccs:control name='DIS_B_ADDMOD' property='name'/>">
                  </div>
 
                  <div class="addr_div_2">
                    <label style="visibility: hidden;">地址：</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD3" class="byHand" maxlength="20" size="5" value="<ccs:control name='DIS_B_ADD3'/>" name="<ccs:control name='DIS_B_ADD3' property='name'/>" placeholder="巷">
                    <select id="s_LANE" class="combobox_QQ" style="WIDTH: 90px" name="s_LANE">
                      <option selected value="">巷</option>
 
                    </select>
 <label>&nbsp;&nbsp;</label> <input type="text" id="BMSDISOBEY_DISTDIS_B_ADD4" class="byHand" maxlength="20" size="4" value="<ccs:control name='DIS_B_ADD4'/>" name="<ccs:control name='DIS_B_ADD4' property='name'/>" placeholder="弄">
                    <select id="s_ALLEY" class="combobox_QQ" style="WIDTH: 80px" name="s_ALLEY">
                      <option selected value="">弄</option>
 
                    </select>
 
                  </div>
 
                  <div class="addr_div_2">
                    <label style="visibility: hidden;">地址：</label><label>&nbsp;</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD5" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD5'/>" name="<ccs:control name='DIS_B_ADD5' property='name'/>">－<input type="text" id="BMSDISOBEY_DISTDIS_B_ADD6" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD6'/>" name="<ccs:control name='DIS_B_ADD6' property='name'/>"><label>&nbsp;號之&nbsp;</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD6_1" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD6_1'/>" name="<ccs:control name='DIS_B_ADD6_1' property='name'/>"><label>&nbsp;&nbsp;</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD7" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD7'/>" name="<ccs:control name='DIS_B_ADD7' property='name'/>"><label>&nbsp;樓之&nbsp;</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD7_1" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD7_1'/>" name="<ccs:control name='DIS_B_ADD7_1' property='name'/>"><label>&nbsp;，&nbsp;</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD8" class="addr_textbox" maxlength="4" size="4" value="<ccs:control name='DIS_B_ADD8'/>" name="<ccs:control name='DIS_B_ADD8' property='name'/>"><label>&nbsp;室</label> 
                  </div>
 
                  <div class="addr_div_2">
                    <label>相對位置<a href="#" id="disBtn" style="vertical-align:super; font-size:6px;">(註)</a>：</label> <label><input type="checkbox" value="前" name="IBFORWORD">&nbsp;前</label>&nbsp;&nbsp;&nbsp;<label><input type="checkbox" value="後" name="IBFORWORD">&nbsp;後</label>&nbsp;&nbsp;&nbsp;<label><input type="checkbox" value="左" name="IBFORWORD">&nbsp;左</label>&nbsp;&nbsp;&nbsp;<label><input type="checkbox" value="右" name="IBFORWORD">&nbsp;右</label>&nbsp;&nbsp;&nbsp;<label><input type="checkbox" value="頂" name="IBFORWORD">&nbsp;頂</label><input type="hidden" id="BMSDISOBEY_DISTDIS_B_WAY" value="<ccs:control name='DIS_B_WAY'/>" name="<ccs:control name='DIS_B_WAY' property='name'/>"><br>
                    <label>其他說明：</label><input type="text" id="BMSDISOBEY_DISTDIS_B_ADD9" style="MARGIN-TOP: 6px" maxlength="150" size="50" value="<ccs:control name='DIS_B_ADD9'/>" name="<ccs:control name='DIS_B_ADD9' property='name'/>">
                  </div>
 
                  <div class="addr_div_3 land_top">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                    <div class="list-block">
                      <select id="BMSDISOBEY_DISTZONE_CODE" class="combobox_QQ" style="WIDTH: 146px" name="<ccs:control name='ZONE_CODE' property='name'/>">
                        <option selected value="">(請選擇行政區)</option>
 <ccs:control name='ZONE_CODE' property='options'/> 
                      </select>
 &nbsp; 
                      <select id="BMSDISOBEY_DISTSEC_NAME" class="combobox_QQ" style="WIDTH: 210px" name="<ccs:control name='SEC_NAME' property='name'/>">
                        <option selected value="">(請選擇地段)</option>
 <ccs:control name='SEC_NAME' property='options'/> 
                      </select>
 <input type="hidden" id="BMSDISOBEY_DISTSECTION" value="<ccs:control name='SECTION'/>" name="<ccs:control name='SECTION' property='name'/>">&nbsp;<input type="text" id="BMSDISOBEY_DISTROAD_NO1" maxlength="4" size="4" value="<ccs:control name='ROAD_NO1'/>" name="<ccs:control name='ROAD_NO1' property='name'/>" placeholder="母號">&nbsp;－&nbsp;<input type="text" id="BMSDISOBEY_DISTROAD_NO2" maxlength="4" size="4" value="<ccs:control name='ROAD_NO2'/>" name="<ccs:control name='ROAD_NO2' property='name'/>" placeholder="子號">&nbsp;<img id="ZONE_AREA_ADD" class="img_add" src="img/Add2.png"><input type="hidden" id="BMSDISOBEY_DISTCSLAN" value="<ccs:control name='CSLAN'/>" name="<ccs:control name='CSLAN' property='name'/>">
                    </div>
 <br>
                    <label class="cslan_title" style="vertical-align: top;">地號：</label> 
                    <div id="ZONE_AREA" style="display: inline-block;">
                      <ccs:control name='ZONE_AREA_LIST'/> 
                    </div>
 
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;勘查結果&nbsp;</label></td> 
                <td class="td-content-required" colspan="5"><label>一、型式</label> 
                  <div class="rlt_div">
                    <label><input type="radio" value="A" name="ADK">&nbsp;樹立廣告</label> <label><input type="checkbox" value="A1" name="AD_CT">&nbsp;屋頂</label> <label><input type="checkbox" value="A2" name="AD_CT">&nbsp;地面</label> <br>
                    <label><input type="radio" value="B" name="ADK">&nbsp;招牌廣告</label> <label><input type="checkbox" value="B1" name="AD_CT">&nbsp;正面</label> <label><input type="checkbox" value="B2" name="AD_CT">&nbsp;側懸</label> <br>
                    <label><input type="radio" value="Z" name="ADK">&nbsp;其他：</label><input type="text" id="BMSDISOBEY_DISTAD_KIND_MEMO" size="15" value="<ccs:control name='AD_KIND_MEMO'/>" name="<ccs:control name='AD_KIND_MEMO' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTAD_KIND" value="<ccs:control name='AD_KIND'/>" name="<ccs:control name='AD_KIND' property='name'/>"><input type="hidden" name="<ccs:control name='AD_KIND_TYPE' property='name'/>" value="<ccs:control name='AD_KIND_TYPE'/>" id="BMSDISOBEY_DISTAD_KIND_TYPE">
                  </div>
 <label>二、材質尺寸</label> 
                  <div class="rlt_div">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                    <div class="list-block">
                      <select id="BMSDISOBEY_DISTBUILDING_KIND" class="combobox_QQ" style="WIDTH: 167px" name="<ccs:control name='BUILDING_KIND' property='name'/>">
                        <option selected value="">(請選擇建造材質)</option>
 <ccs:control name='BUILDING_KIND' property='options'/> 
                      </select>
 &nbsp;<img id="BUILDING_KIND_ADD" class="img_add" src="img/Add2.png">
                    </div>
 &nbsp;<input type="text" id="BMSDISOBEY_DISTBUILDING_KIND_DESC" style="WIDTH: 600px; DISPLAY: none" maxlength="100" value="<ccs:control name='BUILDING_KIND_DESC'/>" name="<ccs:control name='BUILDING_KIND_DESC' property='name'/>"><br>
                    <label>建造材質：&nbsp;</label><input type="text" id="BMSDISOBEY_DISTBUILDING_KIND_SHOW" size="50" style="MARGIN-TOP: 6px" value="<ccs:control name='BUILDING_KIND_SHOW'/>" name="<ccs:control name='BUILDING_KIND_SHOW' property='name'/>"><img id="BUILDING_KIND_DEL" class="img_add" src="img/fieldRefreshButton.png" style="margin-bottom: 3px !important;"><br>
                    <input type="hidden" id="BMSDISOBEY_DISTBUILDING_COAT" value="<ccs:control name='BUILDING_COAT'/>" name="<ccs:control name='BUILDING_COAT' property='name'/>"><label>高度(或縱長)：約&nbsp;</label><input type="text" id="BMSDISOBEY_DISTBUILDING_HEIGHT" class="textRight" style="MARGIN-TOP: 6px" maxlength="12" size="12" value="<ccs:control name='BUILDING_HEIGHT'/>" name="<ccs:control name='BUILDING_HEIGHT' property='name'/>">&nbsp;公尺&nbsp;<label><input type="checkbox" value="Y" name="AD_H_UP">&nbsp;以上</label><input type="hidden" id="BMSDISOBEY_DISTBUILDING_AREA" value="<ccs:control name='BUILDING_AREA'/>" name="<ccs:control name='BUILDING_AREA' property='name'/>"><input type="hidden" name="<ccs:control name='AD_HEIGHT_UP' property='name'/>" value="<ccs:control name='AD_HEIGHT_UP'/>" id="BMSDISOBEY_DISTAD_HEIGHT_UP">
                    <div style="MARGIN-TOP: 5px; DISPLAY: none">
                      <label style="vertical-align: top;">備註</label> <textarea id="BMSDISOBEY_DISTBUILDING_MEMO" style="WIDTH: 90%" rows="3" name="<ccs:control name='BUILDING_MEMO' property='name'/>" maxlength="400"><ccs:control name='BUILDING_MEMO'/></textarea>
                    </div>
 
                  </div>
 <label>三、涉及違反法條</label> 
                  <div class="rlt_div">
                    <label><input type="checkbox" value="A" name="AD_L">&nbsp; <span class="danger">行政執行法&nbsp;36&nbsp;條：行政機關為阻止犯罪、危害之發生或避免急迫危險，而有即時處置之必要時，得為即時強制。即時強制方法如下：一、對於人之管束。二、對於物之扣留、使用、處置或限制其使用。三、對於住宅、建築物或其他處所之進入。四、其他依法定職權所為之必要處置。</span> <span class="nomal">上列違規廣告經勘查，係屬實質違建，依法不得補辦建築執照手續，應予拆除（建築法第二十五條、建築法第八十六條第一款、違章建築處理辦法第五條）。</span></label><br>
                    <label><input type="checkbox" value="B" name="AD_L">&nbsp; <span class="danger">建築法81條等相關規定</span> <span class="nomal">上列違章建築經勘查，係屬程序違建，請於三十日內至新北市政府工務局補行申請建築執照，若申請執照不合規定或逾期未補辦申領執照手續者，依法拆除（建築法第三十條、第二十五條、第八十六條第一款、違章建築處理辦法第五條）。</span></label><br>
                    <label><input type="checkbox" value="Z" name="AD_L">&nbsp;其他：</label><input type="text" id="BMSDISOBEY_DISTAD_CHK_LAW_MM" value="<ccs:control name='AD_CHK_LAW_MM'/>" name="<ccs:control name='AD_CHK_LAW_MM' property='name'/>" size="20"><input type="hidden" id="BMSDISOBEY_DISTAD_CHK_LAW" value="<ccs:control name='AD_CHK_LAW'/>" name="<ccs:control name='AD_CHK_LAW' property='name'/>">
                  </div>
 <label>四、稽查結論</label> 
                  <div class="rlt_div">
                    <div class="list-block">
                      <select id="BMSDISOBEY_DISTCHK_RSLT" class="combobox_QQ" style="WIDTH: 400px;" name="<ccs:control name='CHK_RSLT' property='name'/>">
                        <option selected value="">(請選擇稽查結論)</option>
 <ccs:control name='CHK_RSLT' property='options'/> 
                      </select>
 &nbsp;<img id="CHK_RSLT_ADD" class="img_add" src="img/Add2.png">
                    </div>
 <textarea id="BMSDISOBEY_DISTAD_CHK_RSLT" style="WIDTH: 90%; margin-top: 5px;" rows="3" name="<ccs:control name='AD_CHK_RSLT' property='name'/>"><ccs:control name='AD_CHK_RSLT'/></textarea>
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls BMSDISOBEY_DISTFINISH_STATE_BLOCK">
                <td class="th requiredFild"><label>*&nbsp;施工狀態&nbsp;</label></td> 
                <td class="td-content-required" colspan="5"><label><input type="radio" value="1" name="COMPLITE">&nbsp;施工中</label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label><input type="radio" value="2" name="COMPLITE">&nbsp;建造完成</label><input type="hidden" id="BMSDISOBEY_DISTFINISH_STATE" value="<ccs:control name='FINISH_STATE'/>" name="<ccs:control name='FINISH_STATE' property='name'/>"></td> 
              </tr>
 
              <tr class="Controls" style="DISPLAY: none">
                <td colspan="4"><input type="hidden" id="BMSDISOBEY_DISTSHOW_EXP_STATE" value="<ccs:control name='SHOW_EXP_STATE'/>" name="<ccs:control name='SHOW_EXP_STATE' property='name'/>"><label><input type="radio" value="01" name="EXP_STATE">&nbsp;上列違章建築經勘查，係屬實質違建，依法不得補辦建造執照手續，應予拆除（建築法第二十五條、建築法第八十六條第一款、違章建築處理辦法第五條）。</label><br>
                  <input type="hidden" id="BMSDISOBEY_DISTSHOW_EXP_PROSTS" value="<ccs:control name='SHOW_EXP_PROSTS'/>" name="<ccs:control name='SHOW_EXP_PROSTS' property='name'/>"><label><input type="checkbox" value="01" name="EXP_PROSTS">&nbsp;施工中違建(A0)</label><br>
                  <label><input type="checkbox" value="02" name="EXP_PROSTS">&nbsp;供不特定對象使用，具高危險性及出入人員眾多之場所(B0)：場所為</label>&nbsp;<input type="text" id="BMSDISOBEY_DISTEXP_PROSTS_SITE" maxlength="50" size="10" value="<ccs:control name='EXP_PROSTS_SITE'/>" name="<ccs:control name='EXP_PROSTS_SITE' property='name'/>"><br>
                  <label><input type="checkbox" value="03" name="EXP_PROSTS">&nbsp;占用防火巷或防火間隔(B2)</label><br>
                  <label><input type="checkbox" value="04" name="EXP_PROSTS">&nbsp;屋頂平台之違建(B1)</label><br>
                  <label><input type="checkbox" value="05" name="EXP_PROSTS">&nbsp;一般性案件(C)</label><br>
                  <label><input type="checkbox" value="06" name="EXP_PROSTS">&nbsp;違規廣告物</label><br>
                  <label><input type="checkbox" value="99" name="EXP_PROSTS">&nbsp;其他：</label><input type="text" id="BMSDISOBEY_DISTEXP_PROSTS_DESC" maxlength="100" size="50" value="<ccs:control name='EXP_PROSTS_DESC'/>" name="<ccs:control name='EXP_PROSTS_DESC' property='name'/>"></td> 
              </tr>
 
              <tr class="Controls">
                <td id="loc_label" style="WIDTH: 50%; TEXT-ALIGN: center" colspan="3" class="th"><label class="requiredFild">*&nbsp;違建位置圖</label></td> 
                <td style="TEXT-ALIGN: center" colspan="3" class="th"><label>違建平面示意圖</label></td> 
              </tr>
 
              <tr class="Controls">
                <td class="td-content-required" style="VERTICAL-ALIGN: middle" colspan="3">
                  <div id="left_pic" class="show_2pic">
                    <input type="hidden" id="BMSDISOBEY_DISTcheckImage" value="<ccs:control name='checkImage'/>" name="<ccs:control name='checkImage' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTzoom_level" value="<ccs:control name='zoom_level'/>" name="<ccs:control name='zoom_level' property='name'/>">
                    <div id="map_canvas" style="TEXT-ALIGN: center">
                    </div>
 
                    <div id="setLocation" style="CURSOR: pointer; TEXT-ALIGN: center">
<a href="#">標示</a> 
                    </div>
 
                  </div>
 </td> 
                <td style="VERTICAL-ALIGN: middle" colspan="3">
                  <div id="right_pic" class="show_2pic">
                    <div id="show_draw" style="TEXT-ALIGN: center">
                    </div>
 
                    <div id="drawPic" style="CURSOR: pointer; TEXT-ALIGN: center; width: 49%; display: inline-block;">
<a href="#">繪製</a> 
                    </div>
                    
                    <div id="drawUpload" style="CURSOR: pointer; TEXT-ALIGN: center; width: 49%; display:inline-block;">
 <a href="#">自選圖片</a> 
                  	</div>
 
                  </div>
 </td> 
              </tr>
            
	            <!-- 202210 ICDC Project Modify -- begin --> 
	            <tr class="Controls">
	              <td class="th"><label>Google 地圖&nbsp;</label></td> 
	              <td colspan="5">
	              	<label style="width: 126px; font-size: 16px;margin-right:10px;" class="btn btn-danger" id="google-map-link">Google 地圖</label>
	              </td> 
	            </tr>     
	            <!--202210 ICDC Project Modify -- end-->
             
              <tr class="Controls">
                <td class="th"><label>違建位置座標&nbsp;</label></td> 
                <td colspan="5"><label id="updatelng"><ccs:control name='lng'/></label><span id="coord_mark">, </span><label id="updatelat"><ccs:control name='lat'/></label></td> 
              </tr>
 
              <tr class="Controls">
                <td rowspan="2" class="th"><label class="requiredFild">*&nbsp;現勘照片&nbsp;</label></td> 
                <td colspan="5" class="td-content" style="padding: 0px;">
                  <div id="show_NOWpic" class="td-content-required" style="height: 266px; width: 50%; float: left; border-right: 1px solid #a5a5a5; padding: 8px;">
                    <ccs:control name='show_NOWimg'/> 
                  </div>
 
                  <div id="show_PATpic" class="td-content" style="height: 266px; width: 50%; float: right; padding: 8px;">
                    <ccs:control name='show_PATimg'/> 
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls">
                <td id="show_NOWpic2" colspan="5" class="td-content" style="height: 239px;"><ccs:control name='show_NOWimg2'/></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th"><label>附件&nbsp;</label><br>
<a src="#" class="pointer" style="font-size: 16px;float: right;" onclick="addPic('<ccs:control name='case_id_ano'/>','adPic');">上傳&nbsp;</a></td> 
                <td id="show_ADpic" colspan="5" style="height: 239px;"><ccs:control name='show_ADimg'/></td> 
              </tr>
 
              <!-- yao 2 star-->
            </table>
 
            <table id="ttab2" class="tab-pane fade table table-bordered" style="WIDTH: 100%">
              <!-- yao 2 end-->
              <tr class="Controls">
                <td class="th"><label>認定號碼&nbsp;</label></td> 
                <td class="td-content"><input type="text" name="<ccs:control name='REG_NUM' property='name'/>" value="<ccs:control name='REG_NUM'/>" id="BMSDISOBEY_DISTREG_NUM" maxlength="10" size="10">號<input type="hidden" name="<ccs:control name='REG_YY' property='name'/>" value="<ccs:control name='REG_YY'/>" id="BMSDISOBEY_DISTREG_YY"><input type="hidden" name="<ccs:control name='REG_NO' property='name'/>" value="<ccs:control name='REG_NO'/>" id="BMSDISOBEY_DISTREG_NO"><span class="regnum-viewer"><label class="regnum-incomplete" id="BMSDISOBEY_DISTVIEWREG_NUM"></label></span></td> 
                <td class="th"><label>認定發文日期&nbsp;</label></td> 
                <td class="td-content">
                  <div id="CP_TESTSearchs_S_DATE_div1" style="CURSOR: pointer; POSITION: relative; DISPLAY: inline-block">
                    <input type="text" id="BMSDISOBEY_DISTREG_DATE" size="9" value="<ccs:control name='REG_DATE'/>" name="<ccs:control name='REG_DATE' property='name'/>">
                    <div id="CP_TESTSearchs_S_DATE_div2" style="RIGHT: 0px; POSITION: absolute; LEFT: 0px; TOP: 0px; BOTTOM: 0px">
                    </div>
 
                  </div>
 
                  <ccs:datepicker name='DatePicker_REG_DATE'><a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');" id="BMSDISOBEY_DISTDatePicker_REG_DATE"><img id="BMSDISOBEY_DISTDatePicker_GOVER_DATE1_Image" border="0" alt="Show Date Picker" src="Styles/Blueprint/Images/DatePicker.gif"></a></ccs:datepicker>&nbsp;</td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild">*&nbsp;違建人&nbsp;</td> 
                <td class="td-content-required" colspan="3">
                  <div style="MARGIN-TOP: 5px">
                    <ccs:control name='ANO_TG'/> <ccs:control name='show_ibmdisnm'/> <input type="hidden" id="BMSDISOBEY_DISTdisnm_all" value="<ccs:control name='disnm_all'/>" name="<ccs:control name='disnm_all' property='name'/>">
                  </div>
 
                  <div>
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls" style="DISPLAY: none">
                <td class="th requiredFild"><label>*&nbsp;違建建造行為&nbsp;</label></td> 
                <td class="td-content-required" colspan="3"><label><input type="radio" checked value="01" name="BUILD_HOBY">&nbsp;新建</label> <label><input type="radio" value="02" name="BUILD_HOBY">&nbsp;增建</label> <label><input type="radio" value="03" name="BUILD_HOBY">&nbsp;改建</label> <label><input type="radio" value="04" name="BUILD_HOBY">&nbsp;修建</label> </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;拆除優先類組&nbsp;</label></td> 
                <td class="td-content-required" colspan="3">
                  <select id="BMSDISOBEY_DISTDSORT" name="<ccs:control name='DSORT' property='name'/>">
                    <option selected value="">請選擇</option>
 <ccs:control name='DSORT' property='options'/> 
                  </select>
 &nbsp;&nbsp; 
                  <select id="BMSDISOBEY_DISTDSORT2" name="<ccs:control name='DSORT2' property='name'/>">
                    <option selected value="">請選擇</option>
 <ccs:control name='DSORT2' property='options'/> 
                  </select>
 <input type="hidden" id="BMSDISOBEY_DISTDSORT_H" value="<ccs:control name='DSORT_H'/>" name="<ccs:control name='DSORT_H' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTDSORT2_H" value="<ccs:control name='DSORT2_H'/>" name="<ccs:control name='DSORT2_H' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTDIS_TYPE_DESC" value="<ccs:control name='DIS_TYPE_DESC'/>" name="<ccs:control name='DIS_TYPE_DESC' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTDIS_SORT_ITEM" value="<ccs:control name='DIS_SORT_ITEM'/>" name="<ccs:control name='DIS_SORT_ITEM' property='name'/>"></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th"><label>專案名稱&nbsp;</label></td> 
                <td colspan="3">
                	<label><input type="radio" checked value="Y" name="PRJ_SHOW">&nbsp;顯示</label> 
                	<label><input type="radio" value="N" name="PRJ_SHOW">&nbsp;不顯示</label>
                	<input type="hidden" id="BMSDISOBEY_DISTPRJSHOW" value="<ccs:control name='PRJSHOW'/>" name="<ccs:control name='PRJSHOW' property='name'/>">
                  <div style="MARGIN-TOP: 5px">
                    <select id="BMSDISOBEY_DISTPROJECT" name="<ccs:control name='PROJECT' property='name'/>">
                      <option selected value="">請選擇</option>
					  <ccs:control name='PROJECT' property='options'/> 
                    </select>&nbsp;
                    <label>收費項目</label>
                    <select name="<ccs:control name='PRJFEE' property='name'/>" id="BMSDISOBEY_DISTPRJFEE">
						<option selected value="0">請選擇收費項目</option>
						<ccs:control name='PRJFEE' property='options'/> 
                    </select>
                    <img id="PROJECT_ADD" class="img_add" src="img/Add2.png"><input type="hidden" id="BMSDISOBEY_DISTCSPRJ" value="<ccs:control name='CSPRJ'/>" name="<ccs:control name='CSPRJ' property='name'/>">
                  </div>
 					
                  <div id="PROJECT_LIST" style="PADDING-TOP: 10px; PADDING-LEFT: 50px">
                    <ccs:control name='PRJLIST'/> 
                  </div>
 </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;違規項目&nbsp;</label></td> 
                <td class="td-content-required" colspan="3">
                  <label><input type="radio" value="A" name="EXP_STATE">&nbsp;上列違規廣告物經勘查，未依法申請雜項執照，且屬實質違建或已完工之程序違建，應予拆除（建築法第二十五條、建築法第八十六條第一款、違章建築處理辦法第五條）。</label><br>
                  <label><input type="radio" value="B" name="EXP_STATE">&nbsp;上列違規廣告物經勘查，係屬傾頹廢朽危險廣告物，已影響公共安全，應予拆除。（建築法第八十一條、行政執行法第三十六條）。</label><br>
                  <label><input type="radio" value="Z" name="EXP_STATE">&nbsp;其他：</label>
                  <input type="text" id="BMSDISOBEY_DISTIBM_ITEM_MEMO" maxlength="100" size="50" value="<ccs:control name='IBM_ITEM_MEMO'/>" name="<ccs:control name='IBM_ITEM_MEMO' property='name'/>">
                  <input type="hidden" id="BMSDISOBEY_DISTIBM_ITEM" value="<ccs:control name='IBM_ITEM'/>" name="<ccs:control name='IBM_ITEM' property='name'/>"><br>
                </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;改善期限&nbsp;</label></td> 
                <td class="td-content-required" colspan="3"><input maxlength="3" size="3" type="text" id="BMSDISOBEY_DISTAD_DEADLINE" value="<ccs:control name='AD_DEADLINE'/>" name="<ccs:control name='AD_DEADLINE' property='name'/>">&nbsp;日</td> 
              </tr>
 
              <tr class="Controls">
                <td class="th requiredFild"><label>*&nbsp;案件來源&nbsp;</label></td> 
                <td class="td-content-required" colspan="3">
                  <select id="BMSDISOBEY_DISTCASE_ORI" name="<ccs:control name='CASE_ORI' property='name'/>">
                    <option selected value="">請選擇</option>
 <ccs:control name='CASE_ORI' property='options'/> 
                  </select>
 </td> 
              </tr>
              <tr class="Controls">
              	<td class="th"><label>&nbsp;火災通報案件&nbsp;</label></td> 
              	<td colspan="3" class="td-content">
					<ccs:control name='IBMFIRECASE'/>
 				</td>
              </tr>
              <tr class="Controls">
                <td class="th"><label>&nbsp;相關字號&nbsp;</label></td> 
                <td colspan="3"><input type="text" id="BMSDISOBEY_DISTCASE_ORI_NUM" value="<ccs:control name='CASE_ORI_NUM'/>" name="<ccs:control name='CASE_ORI_NUM' property='name'/>"></td> 
              </tr>
 
              <tr class="Controls" style="DISPLAY: none">
                <td class="th"><label>*&nbsp;認定結果&nbsp;</label></td> 
                <td colspan="3"><label><input type="radio" value="01" name="IBRESULT">&nbsp;違章建築&nbsp;</label> <label><input type="radio" value="02" name="IBRESULT">&nbsp;檢還公所&nbsp;</label> <label><input type="radio" value="03" name="IBRESULT">&nbsp;其他(仍需認定)&nbsp;</label><label><input type="radio" value="04" name="IBRESULT">&nbsp;拍照建檔列管</label> </td> 
              </tr>
 
              <tr class="Controls" style="DISPLAY: none">
                <td class="th"><label>*&nbsp;所屬性質&nbsp;</label></td> 
                <td colspan="3"><label><input type="radio" value="01" name="IBTYP">&nbsp;一般違建</label> <label><input type="radio" value="02" name="IBTYP">&nbsp;違規廣告物</label> <label><input type="radio" value="03" name="IBTYP">&nbsp;檳榔攤</label><label><input type="radio" value="04" name="IBTYP">&nbsp;下水道違建</label><label><input type="radio" value="05" name="IBTYP">&nbsp;拍照建檔列管</label></td> 
              </tr>
 
              <tr class="Controls">
                <td class="th"><label>&nbsp;查報公所&nbsp;</label></td> 
                <td colspan="3">
                  <select id="BMSDISOBEY_DISTAUDNM_CODE" name="<ccs:control name='AUDNM_CODE' property='name'/>">
                    <option selected value="">請選擇</option>
 <ccs:control name='AUDNM_CODE' property='options'/> 
                  </select>
 </td> 
              </tr>
 
              <tr class="Controls">
                <td class="th"><label>&nbsp;查報單位發文日期字號&nbsp;</label></td> 
                <td colspan="3">
                  <div id="CP_TESTSearchs_S_DATE_div1" style="CURSOR: pointer; POSITION: relative; DISPLAY: inline-block">
                    <input type="text" id="BMSDISOBEY_DISTAUDNM_DATE" size="9" value="<ccs:control name='AUDNM_DATE'/>" name="<ccs:control name='AUDNM_DATE' property='name'/>">
                    <div id="CP_TESTSearchs_S_DATE_div2" style="RIGHT: 0px; POSITION: absolute; LEFT: 0px; TOP: 0px; BOTTOM: 0px">
                    </div>
 
                  </div>
 
                  <ccs:datepicker name='DatePicker_GOVER_DATE1'><a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');" id="BMSDISOBEY_DISTDatePicker_GOVER_DATE1"><img id="BMSDISOBEY_DISTDatePicker_GOVER_DATE1_Image" border="0" alt="Show Date Picker" src="Styles/Blueprint/Images/DatePicker.gif"></a></ccs:datepicker>&nbsp;<input type="text" id="BMSDISOBEY_DISTAUDNM_WORD" class="AUDNM_WORD" maxlength="20" size="12" value="<ccs:control name='AUDNM_WORD'/>" name="<ccs:control name='AUDNM_WORD' property='name'/>"><label>&nbsp;字第&nbsp;</label><input type="text" id="BMSDISOBEY_DISTAUDNM_NUM" class="AUDNM_NUM" maxlength="12" size="10" value="<ccs:control name='AUDNM_NUM'/>" name="<ccs:control name='AUDNM_NUM' property='name'/>"><label>&nbsp;號</label></td> 
              </tr>
              <tr class="Controls">
              	<td class="th">
              		<label>違反土管紀錄(公文請</label>
                	<br/>
                	<label>上傳至認定附件區)</label>&nbsp;
               	</td>
              	<td colspan="3" class="td-content">
                  <div style="MARGIN-TOP: 5px">
                    <textarea id="BMSDISOBEY_DISTVIOLATION_LAND" style="WIDTH: 100%" rows="5" name="<ccs:control name='VIOLATION_LAND' property='name'/>" maxlength="400"><ccs:control name='VIOLATION_LAND'/></textarea>
                  </div>
				</td> 
              </tr>
 
              <!-- yao 1-1 star -->
            </table>
 
            <table id="ttab3" class="tab-pane fade table" style="WIDTH: 100%" cellspacing="0" cellpadding="0">
              <ccs:control name='FYMGRID'/> 
            </table>
 
          </div>
 </td> 
      </tr>
 
      <div>
      </div>
 
      <!-- yao 1-1 end -->
      <tr class="Bottom">
        <td id="Bottom_td" colspan="4"><input type="hidden" id="BMSDISOBEY_DISToutput_file" value="<ccs:control name='output_file'/>" name="<ccs:control name='output_file' property='name'/>">
          <div class="btn-contain">
            <div class="btn-center">
              <ccs:button name='Button_Synergy'><input type="submit" onclick="setState('synergy');" id="BMSDISOBEY_DISTButton_Synergy" class="btn btn-warning" alt="送協同作業" value="送協同作業" name="<ccs:control name='Button_Synergy' property='name'/>"></ccs:button>
              <ccs:button name='Button_Submit'><input type="submit" onclick="setState('submit');" id="BMSDISOBEY_DISTButton_Submit" class="btn btn-warning" alt="送排拆作業" value="送排拆作業" name="<ccs:control name='Button_Submit' property='name'/>"></ccs:button>
            </div>
 
            <div class="btn-left">
              <ccs:button name='Button_Print1'><input type="submit" onclick="setState('prt2');" id="BMSDISOBEY_DISTButton_Print1" class="btn btn-primary" alt="勘查紀錄表下載預覽" value="勘查紀錄表下載預覽" name="<ccs:control name='Button_Print1' property='name'/>"></ccs:button>
              <ccs:button name='Button_Print2'><input type="submit" onclick="setState('prt1');" id="BMSDISOBEY_DISTButton_Print2" class="btn btn-primary" alt="認定通知書下載預覽" value="認定通知書下載預覽" name="<ccs:control name='Button_Print2' property='name'/>"></ccs:button>
            </div>
 
            <div class="btn-right-edit">
              <ccs:button name='Button_EditPic'><input type="submit" onclick="setState('editPic');" id="BMSDISOBEY_DISTButton_EditPic" class="btn btn-primary" style="DISPLAY: none" alt="存&nbsp;&nbsp;&nbsp;&nbsp;檔" value="存&nbsp;&nbsp;&nbsp;&nbsp;檔" name="<ccs:control name='Button_EditPic' property='name'/>"></ccs:button>
              <ccs:button name='Button_SaveInsert'><input name="<ccs:control name='Button_SaveInsert' property='name'/>" type="submit" onclick="setState('saveInsert');" value="存檔(畫面資料留用)" alt="存檔(畫面資料留用)" id="BMSDISOBEY_DISTButton_SaveInsert" class="btn btn-primary"></ccs:button>
              <ccs:button name='Button_Update'><input type="submit" onclick="setState('save');" id="BMSDISOBEY_DISTButton_Update" class="btn btn-primary" alt="存&nbsp;&nbsp;&nbsp;&nbsp;檔" value="存&nbsp;&nbsp;&nbsp;&nbsp;檔" name="<ccs:control name='Button_Update' property='name'/>"></ccs:button>
              <ccs:button name='Button_Delete'><input type="submit" onclick="setState('delete');" id="BMSDISOBEY_DISTButton_Delete" class="btn btn-danger" alt="刪&nbsp;&nbsp;&nbsp;&nbsp;除" value="刪&nbsp;&nbsp;&nbsp;&nbsp;除" name="<ccs:control name='Button_Delete' property='name'/>"></ccs:button><input type="hidden" id="BMSDISOBEY_DISTOP_USER" value="<ccs:control name='OP_USER'/>" name="<ccs:control name='OP_USER' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTX_COORDINATE" value="<ccs:control name='X_COORDINATE'/>" name="<ccs:control name='X_COORDINATE' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTY_COORDINATE" value="<ccs:control name='Y_COORDINATE'/>" name="<ccs:control name='Y_COORDINATE' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTcurrent_ym" value="<ccs:control name='current_ym'/>" name="<ccs:control name='current_ym' property='name'/>">
            </div>
 
            <div class="btn-left-bottom">
              <ccs:button name='Button_Cancel'><input type="submit" id="BMSDISOBEY_DISTButton_Cancel" class="btn btn-success" alt="回上頁" value="回上頁" name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button>
            </div>
 
          </div>
 </td> 
        <div>
        </div>
 
        <div>
        </div>
 
      </tr>
 
      <tr>
        <div class="Description" style="DISPLAY: none">
          <div id="desTitle">
            <label style="CURSOR: pointer;position: absolute;right: 5px;"><em id="desClose" class="fa fa-close" style="FONT-SIZE: 30px; PADDING-LEFT: 230px; MARGIN-TOP: 3px"></em></label> 
          </div>
 <img src="img/relativePositioin1.jpg"><img src="img/relativePositioin2.jpg"><img src="img/relativePositioin3.jpg">
        </div>
 
      </tr>
 
    </table>
 </td>
</tr>
</table>
</form>
</ccs:record>
</div>
</body>
</html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-B253112F--%>
<%im10101_man_BModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-5D25EFD1--%>
<%im10101_man_BModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

