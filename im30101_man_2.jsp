<%--JSP Page Init @1-E31E6653--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im30101_man_2ServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-02C7A6C3--%>
<%@include file="im30101_man_2Handlers.jsp"%>
<%
    if (!im30101_man_2Model.isVisible()) return;
    if (im30101_man_2Parent != null) {
        if (!im30101_man_2Parent.getChild(im30101_man_2Model.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im30101_man_2Model);
    pageContext.setAttribute("page", im30101_man_2Model);
    im30101_man_2Model.fireOnInitializeViewEvent(new Event());
    im30101_man_2Model.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im30101_man_2Model;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im30101_man_2Model.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-54F025EF--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>空拍正射影像圖查詢</title>
<link rel="stylesheet" type="text/css" href="javascript/bootstrap3.4.1/css/bootstrap.min.css">
<link rel="stylesheet" href="//js.arcgis.com/3.24/esri/css/esri.css">

<script src="javascript/jquery-2.1.1.min.js" type="text/javascript"></script>
<script language="JavaScript" src="javascript/jquery.blockUI.min.js" type="text/javascript"></script>
<script src="javascript/ezek/tgos/Framework.js" type="text/javascript"></script>
<script src="javascript/ezek/tgos/AjaxAgent.js" type="text/javascript"></script>
<script src="javascript/bootstrap3.4.1/js/bootstrap.min.js"></script>
<script language="JavaScript" src="javascript/jquery.fancybox.js?v=2.1.5" type="text/javascript"></script>
<script src="//js.arcgis.com/3.24/"></script>

<script>
var uav_tycgisMap, dom_id,prdct_yy ;
var tiledMap = null,TgosLayer_F2 = null;
var sys_arcgisMapServiceURL = location.protocol+"//limit.ntpc.gov.tw/arcgis/rest/services/";
//var token = "gwQ-sLeHW7LEBLhRhcVxOT4Xw_RbmpwqaNhNm6h4NdAzRvclvDCJUR2bZsB5Oqwj"; 失效
//var token = "447oNhHjoHa57PAsmntmpqhSvdOItoqELmctHXufN0aU0QVW4__vAcc1liVnAsEs";
var token = "17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token="+token, 
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token="+token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer", 
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token="+token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token="+token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";

   window.onload = function (){
   
	   dom_id = $("[name='dom_id']").val().trim();   
	   prdct_yy = $("[name='prdct_yy']").val().trim();   
	   uav_tycgisMap = "https://icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_"+prdct_yy+"/MapServer" ; 
	   ezek_InitMap();  
   }  
  
   
var temp_date = Date.now();
  
    
var ezekMap, currentCoordinate = [];
var _lng , _lat;
var gsvc, wgs84SR, twd97SR, webMercatorSR;
var  markerPic, savePoint, landNumMarker, areaMarker;
var UAVMap ;
function ezek_InitMap() {
        require([
                 "esri/map", 
                "esri/dijit/Print",             
                "esri/geometry/Point", 
                "esri/geometry/webMercatorUtils", 
                "esri/graphic", 
                "esri/symbols/PictureMarkerSymbol", 
                "esri/symbols/SimpleMarkerSymbol",
                "esri/toolbars/edit", 
                "esri/dijit/Scalebar",
                'esri/tasks/PrintTemplate',
                "esri/SpatialReference",
                "esri/tasks/GeometryService",
                "dojo/_base/event",
                "dojo/dom", 
                "dojo/on", 
                "dojo/parser",
                "dojo/domReady!"
        ], function(Map,Print, Point
                , WebMercatorUtils, Graphic, PictureMarkerSymbol, SimpleMarkerSymbol , Edit,Scalebar,PrintTemplate
               /* , PrintParameters, PrintTask, lang*/
                ,SpatialReference ,GeometryService , event, dom, on, parser) {
               parser.parse();
                                
                        var WGS84_point = new Point();
                        //markerPic = new PictureMarkerSymbol('http://building.tycg.gov.tw/tycgim/img/focus.png?1071030', 30, 30);
                        markerPic = new esri.symbol.SimpleMarkerSymbol(esri.symbol.SimpleMarkerSymbol.STYLE_CIRCLE, 12,null, new dojo.Color([255, 0, 0, 1]));
                        areaMarker = new esri.symbol.SimpleFillSymbol(esri.symbol.SimpleFillSymbol.STYLE_SOLID, new esri.symbol.SimpleLineSymbol(esri.symbol.SimpleLineSymbol.STYLE_SOLID, new dojo.Color([255,0,0,0.8]), 3),new dojo.Color([125,125,125,0.35]));
                        esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";
                
                        //設定座標轉換服務
                        
                        var sys_arcgisMapService = location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/";
                        
                        gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");
                        wgs84SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '4326' />);            //WGS84
                        twd97SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102443' />);          //TWD_97_TM2
                        webMercatorSR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102100' />);    //WGS 1984 Web Mercator Projection
                        
                ezekMap = new Map("map_canvas", {
                            logo: false,
                            slider: true,
                            sliderStyle: "small",
                            sliderPosition: "top-left",
                            spatialReference: {
                                    wkid: 102443
                            },
                     zoom: 10,
                     minZoom:2, 
                        maxZoom: 13

                    });
                  

           
                        
                        tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
							"opacity": 1,
							id: normalTileName2
						});
				        ezekMap.addLayer(tiledMap, 0);
					
						
						// 1: 1/1000以上 電子地圖
						TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
							"opacity": 1,
							id: normalTileName1
						});
						ezekMap.addLayer(TgosLayer_F2, 1);
                        
                      
                        UAVMap = new esri.layers.ArcGISDynamicMapServiceLayer(uav_tycgisMap , { id: "UAVMap" });
                        //tempMap.setVisibleLayers([0,1]);
                        ezekMap.addLayer(UAVMap, 2);
                  

   

                on(ezekMap, "load", function() {
                      //REG_YY
                      var _name = dom_id + ".tif";
                      zoomToLayer_UAV(_name )
                     // console.log(_name);
                });
               
                 //加入比例尺顯示
                                var scalebar = new Scalebar({
                                        map: ezekMap,
                                        scalebarUnit: "metric"
                                });
                                esri.config.defaults.map.slider = {
                                        right: "165px",
                                        bottom: null,
                                        width: "200px",
                                        height: null
                                };
                                $(".esriScalebar").css("bottom", "50px").css("z-index", "50").css("position", "relative");       
                       
        });//END  require
}//END ezek_InitMap()


 //------------------------------------------------------------------------
        // 縮放置圖層
        //------------------------------------------------------------------------
        function zoomToLayer_UAV(_name ) {
        	
      //console.log( "~~zoomToLayer_UAV~~~" );  	
        	var layerDefinitions = [];
		layerDefinitions[0] = "Name = '"+_name+"'";

        UAVMap.setLayerDefinitions(layerDefinitions);
        
        
               // ezekMap.graphics.clear();
            var query = new esri.tasks.Query();
            var queryTask =  new esri.tasks.QueryTask(uav_tycgisMap+"/0");
            query.returnGeometry = true;
            query.where = "Name='" +_name + "' "; 
        queryTask.execute(query, addrQueryResults);
        
   
        }
        //------------------------------------------------------------------------
        // 縮放置圖層 結果
        //------------------------------------------------------------------------
        function addrQueryResults(featureSet) {
   
                ezekMap.graphics.clear();
                //啟動單次查詢
                
                if(featureSet.features.length == 0){
                        //alertify.alert('查詢結果', '查無此地號', function(){ }).set('labels', '確認');
                        //goAlertify( '查詢結果', '查無此地號','確認' );
                        //showSearchErr(2 ,"&nbsp;&nbsp;&nbsp;&nbsp;查無此地號");
                }else{
                        dojo.forEach(featureSet.features, function(feature) {
                                
                                landNumMarker = feature;
                                //landNumMarker.setSymbol(areaMarker);
                                //
                                if (feature.geometry.getExtent().spatialReference.wkid == null || feature.geometry.getExtent().spatialReference.wkid != ezekMap.spatialReference.wkid) {
                                        landNumMarker.geometry.setSpatialReference(twd97SR);
                                        coordToMapSR_geometry(landNumMarker.geometry, function(outGeometry) {

                                                                                         
                                                //landNumMarker.setGeometry(outGeometry);
                                                //ezekMap.graphics.add(landNumMarker);
                                                //地圖zoom至該feature
                                                ezekMap.setExtent(landNumMarker.geometry.getExtent(), true);
                                        });
                                } else {
                                        ezekMap.graphics.add(landNumMarker);
                                        //地圖zoom至該feature
                                        ezekMap.setExtent(landNumMarker.geometry.getExtent(), true);
                                        
                                }       
                        });
                        
                }
                //hideLoading();
        }

function zoomToCenter(lng,lat) 
{
        require([
                "esri/geometry/Point",
                "esri/symbols/PictureMarkerSymbol"
        ], function(Point,PictureMarkerSymbol) {
                
                var _CallBack_Zoom = function(outputpoint) 
                {
                       // var point = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        savePoint = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        //console.log(outputpoint.x, outputpoint.y);
                        //picLocMarker = new PictureMarkerSymbol('img/searchLocation.png', 20, 20);
                        ezekMap.graphics.add(new esri.Graphic(savePoint, markerPic));
                       
                        ezekMap.centerAndZoom(savePoint, 10); 
                     
                };
                convertLatLng(lat,lng,4326,102443,_CallBack_Zoom);
        });
}
//------------------------------------------------------------------------
// 將geometry物件轉為底圖使用的座標系統
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function coordToMapSR_geometry(inGeometry, callback) {
        require(["esri/geometry/Geometry", "esri/tasks/ProjectParameters", "esri/tasks/GeometryService"], function(Geometry, ProjectParameters, GeometryService) {
                var prjParams = new ProjectParameters();
                prjParams.geometries = [inGeometry];
                prjParams.outSR = ezekMap.spatialReference;
                gsvc.project(prjParams, function (et) {
                        callback(et[0]);
                }, showErr);
        });
} 

function convertLatLng(_lat,_lng,_incoord,_outcoord,_callback) 
{
        require([
                "esri/tasks/ProjectParameters",
                "esri/symbols/PictureMarkerSymbol"
        ],function(ProjectParameters,PictureMarkerSymbol) { 

                var inlat = _lat;
                var inlon = _lng;
                var incoord = _incoord;
                var outcoord = _outcoord;

                if (isNaN(inlat) || isNaN(inlon)) {
                        //alert("Please enter valid numbers");
                } else {
                        var inSR = new esri.SpatialReference({
                                wkid: incoord
                        });
                        var outSR = new esri.SpatialReference({
                                wkid: outcoord
                        });
                        var geometryService = new esri.tasks.GeometryService("https://utility.arcgisonline.com/ArcGIS/rest/services/Geometry/GeometryServer");
                        var inputpoint = new esri.geometry.Point(inlon, inlat, inSR);
                        var PrjParams = new esri.tasks.ProjectParameters();
                        PrjParams.geometries = [inputpoint];
                        PrjParams.outSR = outSR;

                        geometryService.project(PrjParams, function (outputpoint) {
                                // console.log('Conversion completed. Input SR: ' + incoord + '. Output SR: ' + outcoord);
                                _callback(outputpoint[0]);
                        });
                }
        });
}


        function showLoading(){
                $.ajaxSettings.async = false;                   
                $.blockUI({message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;存檔中, 請稍候...', css:{border:'none', padding:'6px', backgroundColor:'#000', '-webkit-border-radius': '10px', '-moz-border-radius': '10px', opacity: .5, color:'#FFF'}});
                $.ajaxSettings.async = true;
        }

        function closeLoading(){
                $.unblockUI(); 
                //setTimeout( function(){ $.unblockUI(); }, 1500);
        }
        
   
       
        // 返回
        function closeFancybox() {   parent.$.fancybox.close();   }
</script>
<style type="text/css">
html body table{margin:0;}
.esriSimpleSlider { left: 10px; top: 10px; }
#map_canvas { width: 100%; height: 540px; border: 2px solid #bbb; }

.findUAV{cursor:ponter;}

</style>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//End CCS script
</script>
</head>
<body>
<ccs:record name='BMSDISOBEY_DIST'>
<form id="BMSDISOBEY_DIST" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
  <table class="table" style="MARGIN-BOTTOM: 0px" cellspacing="0" cellpadding="0">
    <ccs:error_block>
    <tr id="BMSDISOBEY_DISTErrorBlock" class="Error">
      <td colspan="2"><ccs:error_text/></td>
    </tr>
    </ccs:error_block>
    <tr class="Controls">
    
      <td style="VERTICAL-ALIGN: top" colspan="2">
        <div>
          <div id="map_canvas">
          </div>
        </div>
      </td> 
      <tr class="Bottom">
        <td style="TEXT-ALIGN: right" colspan="2"><input type="hidden" id="BMSDISOBEY_DISTdom_id" value="<ccs:control name='dom_id'/>" name="<ccs:control name='dom_id' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTprdct_yy" value="<ccs:control name='prdct_yy'/>" name="<ccs:control name='prdct_yy' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTREG_CG" value="<ccs:control name='REG_CG'/>" name="<ccs:control name='REG_CG' property='name'/>">
          <ccs:button name='Button_Cancel'><input type="submit" onclick="closeFancybox();" id="BMSDISOBEY_DISTButton_Cancel" class="btn btn-success" alt="回&nbsp;上&nbsp;頁" value="回&nbsp;上&nbsp;頁"   style="float: left;"  name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button><input type="hidden" id="BMSDISOBEY_DISTEXP_NO" value="<ccs:control name='EXP_NO'/>" name="<ccs:control name='EXP_NO' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTlng" value="<ccs:control name='lng'/>" name="<ccs:control name='lng' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTlat" value="<ccs:control name='lat'/>" name="<ccs:control name='lat' property='name'/>"></td>
      </tr>
    </table>
  </form>
  </ccs:record>
  </body>
  </html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-5C96FC04--%>
<%im30101_man_2Model.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-B3E002FA--%>
<%im30101_man_2Model.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

