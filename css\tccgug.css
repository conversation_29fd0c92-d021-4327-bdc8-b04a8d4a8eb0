﻿@page { size: A3 landscape; }  

html,
body,
#mapDiv{
	height: 100%;
    width: 100%;
}

html,
body,
#mapDiv,
button, input, optgroup, select, textarea
table {
    font-family: <PERSON><PERSON>, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif;
	/*overflow-x: hidden;*/
	/*,div,ul,li,*/
}

label{
	font-size: 16px;
	font-weight: 500;
}

.btn{
	font-size: 16px;
}

#searchDiv {
    background-color: white;
    width: 328px;
    position: absolute;
    z-index: 40;
    height: 100%;
    box-shadow: 2px 2px 2px rgba(100, 100, 100, 0.6), 4px 4px 6px rgba(100, 100, 100, 0.4), 6px 6px 12px rgba(100, 100, 100, 0.4);
    margin-right: 6px;
    z-index: 51;
}

#mapDiv {
    width: 70%;
    float: right;
}

.block {
    display: inline-block;
    width: 100%;
}

.UG-colorRed {
    color: #fa4f4f;
}

.UG-colorGray {
    color: #7b7b7b;
}
.UG-colorBlue {
    color: #5790c1 !important;
}

.visibile-hidden {
    visibility: hidden;
}

.row.toggle {
    text-align: left;
}

.li-Title {
    font-weight: 500;
    font-size: 16px;
    padding-right: 4px;
    padding-left: 21px;
	font-weight: 700;
}

.li-Title-2 {
    font-weight: 700;
    font-size: 14px;
	color:#00698c;
}

.li-Title-3 {
    font-size: 14px;
}

.content-3 {
    text-align: left;
    height: 30px;
    padding-left: 28px;
}

.list-group {
    padding-left: 0;
    margin-bottom: 0px !important;
}

input[type=range].transparent-range {
    display: inline-block;
    margin-top: 4px;
    width: 100%;
    margin-top: 9px;
}

.transparent-range-div {
    float: right;
    display: flex;
    ;
    text-align: right;
    width: 106px;
    padding-right: 13px;
}

.location-choice-block {
    display: inline-block;
    width: 33%;
}

.collapse {
    /*padding-top: 3px;*/
}

.mapServerTitle {
    cursor: pointer;
}

.legend {
    width: 36px;
    height: 18px;
    margin-right: 8px;
    display: inline-block;
    border: 1px solid;
    top: 5px;
    position: relative;
}

.infoWindowTitle {
    color: #337ab7;
    padding-right: 5px;
}

fa-chevron-down,
fa-chevron-lleft {
    color: #778390;
}

.chosen-container-single .chosen-single {
    height: 26px !important;
    color: #808080;
}

.chosen-container {
    font-size: 14px !important;
}

input[type="range"] {
    -webkit-appearance: none;
    border-radius: 2px;
    width: 200px;
    height: 3px;
    background-image: -webkit-linear-gradient(left, #337ab7 0%, #337ab7 50%, #fff 50%, #fff 100%);
    box-shadow: inset #759dc0 0 0 5px;
    outline: none;
    transition: .1s;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 10px;
    height: 10px;
    background: #337ab7;
    border-radius: 50%;
    transition: .1s;
}

input[type="range"]::-webkit-slider-thumb:hover,
input[type="range"]::-webkit-slider-thumb:active {
    width: 16px;
    height: 16px;
}

#layerInfoWindow {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

#layerInfoWindow td,
#customers th {
    border: 1px solid #ddd;
    padding: 8px;
}


#layerInfoWindow th {
    padding-top: 5px;
    padding-bottom: 5px;
    text-align: left;
    background-color: #19b9b2;
    color: white;
    font-size: 13px;
    font-weight: 400;
	border: 1px solid #19B9B2;
}

#layerInfoWindow td {
    background-color: #2eb9b208;
}

.dijitReset .dijitInline .dijitButtonText {
    width: 140px;
    background-color: white;
}

/* make all dijit buttons the same width */

.dijitButton .dijitButtonNode,
#drawingWrapper,
#printButton {
    width: 100%;
}

.esriPrint {
    padding: 0;
    width: 100%;
    text-align: center;
}

::-webkit-scrollbar-track
{
	/*
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	background-color: #F5F5F5;
	*/
}

::-webkit-scrollbar
{
	width: 6px;
	background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb
{
	background-color: #b5b5b5;;
}

::-webkit-scrollbar {
    /*display: none;*/
}

.display-none {
    display: none !important;
}

.UserName {
    position: absolute;
    right: 14px;
    top: 69px;
    color: white;
    font-size: 18px;
}

.error {
    color: red;
	font-size:16px;
}

/*
a:hover{ 
 color:rgb(87, 144, 193) !important;
}
*/

.tabs {
    text-align: left;
	overflow-x: hidden;
}

.tabs li {
    text-align: left;
}

.tabs .tab a {
    padding: 0px 15px;
}

.menuTabDiv {
    display: initial;
}

.menuTabDiv i {
    vertical-align: middle;
}

.menuTabDiv span {
    vertical-align: middle;
}

.menu-Title {
    font-weight: 500;
    font-size: 18px;
    padding-right: 4px;
}

a {
    text-decoration: none !important;
}

.content-heading {
    COLOR: #2980B9;
    font-size: 24px;
    border-bottom: 1px solid #c7c9c9;
    padding-bottom: 15px;
}

.esriSimpleSliderTL {
    left: 350px;
}

.esriPopup .actionsPane .zoomTo {
    display: none;
}

.MainContainer {
}

.searchContainer {
	padding: 16px 12px 16px 18px;
	border: 1px solid #dddddd;
}

.resultContainer {
	padding: 10px;
    max-height: 500px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.input-marginLeft-6 {
	margin-left: 6px;
}

.spacing-MarginLeft-14{
	margin-left: 14px;
}

.inputContainer {
	padding-left: 19px;
    padding-top: 4px;
}

.inputTitle-Span {
	font-size: 16px;
}

.divLine-MarginTop-10 {
	margin-top: 10px;
}

.collection-item-a{
	height: 52px;
    padding: 16px 0px 16px 18px !important; 
    font-weight: 700;
}
    
#resultInfo {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

#resultInfo td,
#customers th {
    border: 1px solid #ddd;
    padding: 8px;
}


#resultInfo th {
    padding-top: 5px;
    padding-bottom: 5px;
    text-align: left;
    background-color: #19b9b2;
    color: white;
    font-size: 14px;
    font-weight: 400;
	border: 1px solid #19B9B2;
}

#resultInfo td {
    background-color: #2eb9b208;
}

#s_REG_KIND_chosen {
	WIDTH: 120px !important
}

