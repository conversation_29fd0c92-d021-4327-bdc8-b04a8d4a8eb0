# 專案分析報告 (Project Analysis Report)

本文件旨在記錄對本專案的架構、模式和技術細節的分析與理解。

## 1. 專案概觀 (Project Overview)

*   **核心框架:** 本專案主要由 **CodeCharge Studio** RAD 工具生成。
*   **技術棧:**
    *   **後端:** Java Server Pages (JSP) 2.3
    *   **前端:** HTML, CSS, JavaScript (包含自訂腳本和可能的函式庫如 jQuery)
    *   **伺服器:** Apache Tomcat
    *   **資料庫:**
        *   Microsoft SQL Server (`DBConn2`)
        *   PostgreSQL (`DBConn`)
*   **整合:**
    *   **GIS:** 專案包含與 ArcGIS 相關的 JavaScript 檔案 (`ezekArcgisToolBox.js`)。
    *   **排程任務:** 使用 Quartz Scheduler (`quartz.properties`)。

## 2. 架構模式 (Architectural Patterns)

專案中主要存在兩種實作模式：

### 2.1. 標準 CodeCharge 三檔案分層模式 (Three-File Tiered Pattern)

這是專案中最常見的、由 CodeCharge Studio 生成的模式。它將一個功能單元分散到三個檔案中，每個檔案有明確的分工，但它並非嚴格的 MVC 架構。

*   **呈現層 (Presentation Layer): `*_man.jsp` / `*_lis.jsp`**
    *   **角色:** 負責產生使用者介面 (UI)。
    *   **內容:** 主要由 HTML 和 CodeCharge 的自訂標籤 (`<ccs:...>`) 組成，用於顯示動態資料。

*   **設定與資料定義層 (Configuration & Data Definition Layer): `*_man.xml` / `*_lis.xml`**
    *   **角色:** 作為呈現層的「藍圖」或設定檔。
    *   **內容:** 以 XML 格式定義頁面所使用的資料來源、元件 (如表單、網格)、事件和驗證規則。

*   **邏輯處理層 (Logic Handling Layer): `*_Handlers.jsp`**
    *   **角色:** 處理後端業務邏輯，特別是表單提交。
    *   **內容:** 包含大量 Java Scriptlet (`<%...%>`)，負責從請求中讀取資料、執行資料庫 CRUD 操作，並在完成後將使用者重導向。

*   **與 ASP.NET Web Forms 的類比:**
    *   這個三檔案模式在設計哲學上與 **ASP.NET Web Forms** 高度相似。可以理解為：
        *   `*_man.jsp` / `*_lis.jsp`  ↔️  `.aspx` 檔案 (定義頁面佈局和伺服器控制項)。
        *   `*_Handlers.jsp` ↔️  `.aspx.cs` 程式碼後置檔案 (處理伺服器端事件)。
        *   `<ccs:Grid>` 等自訂標籤 ↔️  `<asp:GridView>` 等伺服器控制項。
    *   兩者都是以頁面為中心、事件驅動的伺服器端渲染模型，對於有 Web Forms 經驗的開發者來說會非常熟悉。

*   **架構評價：反模式 (Anti-Pattern):**
    *   您已指出，這種與 ASP.NET Web Forms 早期模式類似的架構，在現代網頁開發中被視為一種 **反模式**。
    *   **主要缺點包括：**
        *   **高度耦合:** 表現層與業務邏輯緊密綁定。
        *   **可測試性差:** 難以對嵌入在 JSP 中的邏輯進行自動化單元測試。
        *   **維護困難:** 程式碼混雜，職責不清，難以協作與修改。
    *   這個評價為未來任何可能的「現代化重構」提供了核心依據。

### 2.2. 自訂後端 API 模式

對於標準模式難以處理的複雜業務，專案採用了自訂的後端 JSP 端點。

*   **範例:** `case_empty_dis.jsp`
*   **角色:** 作為一個純後端的 API 端點，由前端 AJAX 呼叫。
*   **特點:**
    *   **無 HTML 輸出:** 不產生使用者介面，僅輸出 JSON 字串。
    *   **手動交易控制:** 能夠執行多個 SQL 操作，並在 `try-catch` 區塊中進行手動的 `commit` 和 `rollback`，確保資料庫交易的原子性。
    *   **AJAX 驅動:** 前端 JavaScript 呼叫此 JSP，並根據回傳的 JSON 結果執行後續動作 (如提示訊息、頁面跳轉)。

## 3. 請求生命週期 (Request Lifecycle)

一個典型的 HTTP 請求會經過以下處理流程：

1.  **Tomcat 接收請求**。
2.  **過濾器鏈 (Filter Chain):**
    *   `ContentSecurityPolicyFilter` (`hostHeaderFilter`): 檢查 `Host` 標頭，防止主機標頭注入攻擊。
    *   `PanelFilter` & `IncludeFilter`: CodeCharge 核心過濾器，攔截對 `.jsp` 的請求。它們解析對應的 `.xml` 設定檔，初始化 Java Bean (模型)，並為 JSP 渲染做準備。
3.  **JSP 處理:**
    *   **視圖 JSP (`.jsp`):** 讀取 Filter 準備好的模型資料，透過自訂標籤渲染成最終 HTML。
    *   **Handlers JSP (`Handlers.jsp`):** 執行 Java 程式碼，處理資料庫邏輯，然後重導向。
    *   **API JSP (`case_empty_dis.jsp`):** 執行 Java 程式碼，處理資料庫邏輯，並輸出 JSON。

## 4. 關鍵設定檔 (Key Configuration Files)

*   **`WEB-INF/web.xml`:**
    *   定義 Servlet、Filter (包括 CodeCharge 核心 Filter 和自訂安全 Filter)。
    *   設定應用程式啟動時載入的 `InitServlet`。
    *   定義歡迎頁面 (`login.jsp`)。
*   **`WEB-INF/site.properties`:**
    *   **極其重要**。
    *   定義了 `DBConn` (PostgreSQL) 和 `DBConn2` (MSSQL) 兩個資料庫的連線字串、帳號和密碼。
    *   定義了應用程式的本地化、樣式、驗證等全域設定。

## 5. 已驗證的推論 (Verified Inferences)

透過分析原始碼，我們驗證了以下幾點：

*   **資料庫連線池:** 專案採用了基於單例模式 (`DBConnectionManager.getInstance()`) 的資料庫連線池。所有 JSP 都從此共享池中獲取和釋放連線，確保了資源的有效管理。證據遍布於超過 50 個 Handlers 和自訂 JSP 檔案中。

*   **`case_empty_dis.jsp` 的完整流程:**
    *   **命名:** `dis` 的含義可推斷為 **Dispose (處理)** 或 **Dispatch (分派)**，符合其「結案處理與分派」的功能。
    *   **呼叫者:** 該檔案由 `im40101_lis.jsp` 中的內聯 JavaScript 函式 `handleEmptyDIS()` 透過 AJAX POST 請求呼叫。
    *   **工作流程:** 前端傳遞 `caseId`, `accRlt`, `empNo` -> 後端 `case_empty_dis.jsp` 執行資料庫交易 -> 後端回傳 `{result: 'OK', url: '...'}` 的 JSON -> 前端根據回傳的 `url` 進行頁面跳轉。

---
*文件版本: 1.1.0*
*最後更新: 2025-07-05*

---
*文件版本: 1.0.0*
*最後更新: 2025-07-05*
