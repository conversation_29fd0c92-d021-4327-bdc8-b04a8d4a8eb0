<%--JSP Page Init @1-B523B666--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new VerticalMenuServiceChecker()).check(request, response, getServletContext())) return;%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-9E58D4B1--%>
<%@include file="VerticalMenuHandlers.jsp"%>
<%
    if (!VerticalMenuModel.isVisible()) return;
    if (VerticalMenuParent != null) {
        if (!VerticalMenuParent.getChild(request.getParameter("controlName")).isVisible()) return;
    }
    pageContext.setAttribute("parent", VerticalMenuModel);
    pageContext.setAttribute("page", VerticalMenuModel);
    VerticalMenuModel.fireOnInitializeViewEvent(new Event());
    VerticalMenuModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) VerticalMenuModel;
    String pathToRoot = request.getAttribute("parentPathToRoot") == null ? "" : (String) request.getAttribute("parentPathToRoot");

    // Include once for client scripts
    String scripts = "|";
    String includes = (String) request.getAttribute("scriptIncludes");
    request.setAttribute("scriptIncludes", includes + scripts);

    if (!VerticalMenuModel.isVisible()) return;
    ((ModelAttribute) curPage.getAttribute("pathToCurrentPage")).setValue(pathToRoot + "Designs/Light/");
%>
<%--End Page Body--%>

<%--JSP Page Content @1-A0634745--%>
<div class="vmenublock clearfix">
        <div class="vmenublockheader">
            <h3 class="t"><ccs:contentplaceholder name='Title'></ccs:contentplaceholder></h3>
        </div>
        <div class="vmenublockcontent">
<ul class="vmenu"><ccs:contentplaceholder name='MenuItems'></ccs:contentplaceholder></ul>
                
        </div>
</div>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-A116B6C0--%>
<%VerticalMenuModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-4E60483E--%>
<%VerticalMenuModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

