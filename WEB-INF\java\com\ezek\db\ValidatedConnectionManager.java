package com.ezek.db;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;
import java.io.InputStream;
import java.io.IOException;
import com.codecharge.util.CCLogger;

/**
 * 資料庫連線驗證管理器 - 方案一實施
 * 
 * 此類別為現有的 CodeCharge Studio 系統提供經過驗證的資料庫連線。
 * 它將原本的連線池邏輯替換為使用 Tomcat DataSource 的驗證連線池。
 * 
 * 主要功能：
 * 1. 連線借出前自動驗證 (testOnBorrow=true)
 * 2. 閒置連線定期驗證 (testWhileIdle=true)  
 * 3. 連線逾時自動回收 (removeAbandoned=true)
 * 4. 與現有 CodeCharge 程式碼完全向後相容
 * 
 * <AUTHOR> Administrator
 * @version 1.0
 * @since 2025-01-23
 */
public class ValidatedConnectionManager {
    
    private static final CCLogger logger = CCLogger.getInstance();
    
    // DataSource 快取
    private static DataSource postgresDataSource = null;
    private static DataSource sqlServerDataSource = null;
    
    /**
     * 取得經過驗證的 PostgreSQL 連線
     * 
     * @return 已驗證的資料庫連線
     * @throws SQLException 連線建立失敗時拋出
     */
    public static Connection getPostgreSQLConnection() throws SQLException {
        try {
            if (postgresDataSource == null) {
                Context initContext = new InitialContext();
                Context envContext = (Context) initContext.lookup("java:/comp/env");
                postgresDataSource = (DataSource) envContext.lookup("jdbc/PostgreSQLDB");
                logger.debug("PostgreSQL DataSource 初始化完成");
            }
            
            // 取得連線 (已經過 testOnBorrow 驗證)
            Connection conn = postgresDataSource.getConnection();
            logger.debug("取得 PostgreSQL 連線: " + conn.toString());
            return conn;
            
        } catch (NamingException e) {
            logger.error("PostgreSQL DataSource 查找失敗", e);
            throw new SQLException("無法取得 PostgreSQL DataSource", e);
        }
    }
    
    /**
     * 取得經過驗證的 SQL Server 連線
     * 
     * @return 已驗證的資料庫連線
     * @throws SQLException 連線建立失敗時拋出
     */
    public static Connection getSQLServerConnection() throws SQLException {
        try {
            if (sqlServerDataSource == null) {
                Context initContext = new InitialContext();
                Context envContext = (Context) initContext.lookup("java:/comp/env");
                sqlServerDataSource = (DataSource) envContext.lookup("jdbc/SQLServerDB");
                logger.debug("SQL Server DataSource 初始化完成");
            }
            
            // 取得連線 (已經過 testOnBorrow 驗證)
            Connection conn = sqlServerDataSource.getConnection();
            logger.debug("取得 SQL Server 連線: " + conn.toString());
            return conn;
            
        } catch (NamingException e) {
            logger.error("SQL Server DataSource 查找失敗", e);
            throw new SQLException("無法取得 SQL Server DataSource", e);
        }
    }
    
    /**
     * 向後相容方法：根據連線池名稱取得連線
     * 
     * @param poolName 連線池名稱 ("DBConn" 或 "DBConn2")
     * @return 已驗證的資料庫連線
     * @throws SQLException 連線建立失敗時拋出
     */
    public static Connection getValidatedConnection(String poolName) throws SQLException {
        if ("DBConn".equals(poolName)) {
            return getPostgreSQLConnection();
        } else if ("DBConn2".equals(poolName)) {
            return getSQLServerConnection();
        } else {
            throw new SQLException("未知的連線池名稱: " + poolName);
        }
    }
    
    /**
     * 測試連線是否有效
     * 
     * @param conn 要測試的連線
     * @param timeoutSeconds 測試逾時秒數
     * @return true 如果連線有效
     */
    public static boolean isConnectionValid(Connection conn, int timeoutSeconds) {
        if (conn == null) {
            return false;
        }
        
        try {
            // Java 6+ 標準方法
            return conn.isValid(timeoutSeconds);
        } catch (SQLException e) {
            logger.warn("連線驗證失敗: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 手動驗證連線 (備用方法)
     * 
     * @param conn 要驗證的連線
     * @param validationQuery 驗證用 SQL
     * @return true 如果連線有效
     */
    public static boolean validateConnectionManually(Connection conn, String validationQuery) {
        if (conn == null) {
            return false;
        }
        
        Statement stmt = null;
        try {
            stmt = conn.createStatement();
            stmt.setQueryTimeout(5); // 5秒逾時
            stmt.executeQuery(validationQuery);
            return true;
        } catch (SQLException e) {
            logger.warn("手動連線驗證失敗: " + e.getMessage());
            return false;
        } finally {
            if (stmt != null) {
                try {
                    stmt.close();
                } catch (SQLException e) {
                    logger.warn("關閉驗證 Statement 失敗", e);
                }
            }
        }
    }
}