-- ========================================================================
-- 新北市違章建築管理系統 - 資料庫維護腳本
-- ========================================================================
-- 
-- 目的：定期維護資料庫效能，監控索引使用情況，統計資訊更新
-- 
-- 使用方式：
-- 1. 每日執行：daily_maintenance.sql
-- 2. 每週執行：weekly_maintenance.sql  
-- 3. 每月執行：monthly_maintenance.sql
-- 4. 監控查詢：monitoring_queries.sql
-- ========================================================================

-- ========================================================================
-- 1. 每日維護腳本
-- ========================================================================

-- 1.1 更新統計資訊（每日執行）
-- 檔案：daily_maintenance.sql
\echo '=== 每日維護腳本開始 ==='
\echo '執行時間：' `date`

-- 更新主要表的統計資訊
ANALYZE ibmcase;
ANALYZE ibmfym;
ANALYZE ibmlist;
ANALYZE ibmsts;

-- 檢查資料庫大小變化
SELECT 
    'Database Size Check' as check_type,
    pg_size_pretty(pg_database_size('bms')) as database_size,
    NOW() as check_time;

-- 檢查表大小變化
SELECT 
    'Table Size Check' as check_type,
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts', 'ibmcprp')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 檢查長時間運行的查詢
SELECT 
    'Long Running Queries' as check_type,
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state = 'active';

\echo '=== 每日維護腳本完成 ==='

-- ========================================================================
-- 2. 每週維護腳本
-- ========================================================================

-- 2.1 索引維護（每週執行）
-- 檔案：weekly_maintenance.sql
\echo '=== 每週維護腳本開始 ==='
\echo '執行時間：' `date`

-- 重建索引統計資訊
REINDEX INDEX CONCURRENTLY ibmcase_pkey;
REINDEX INDEX CONCURRENTLY ibmfym_pkey;
REINDEX INDEX CONCURRENTLY ibmlist_pk;
REINDEX INDEX CONCURRENTLY ibmsts_pkey;

-- 檢查索引膨脹情況
SELECT 
    'Index Bloat Check' as check_type,
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY pg_relation_size(indexrelid) DESC;

-- 檢查未使用的索引
SELECT 
    'Unused Indexes Check' as check_type,
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
AND idx_scan = 0
ORDER BY pg_relation_size(indexrelid) DESC;

-- 清理過期的連線
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'idle' 
AND now() - state_change > interval '1 hour';

\echo '=== 每週維護腳本完成 ==='

-- ========================================================================
-- 3. 每月維護腳本
-- ========================================================================

-- 3.1 全面維護（每月執行）
-- 檔案：monthly_maintenance.sql
\echo '=== 每月維護腳本開始 ==='
\echo '執行時間：' `date`

-- 執行 VACUUM 清理
VACUUM ANALYZE ibmcase;
VACUUM ANALYZE ibmfym;
VACUUM ANALYZE ibmlist;
VACUUM ANALYZE ibmsts;

-- 檢查資料表膨脹情況
SELECT 
    'Table Bloat Analysis' as analysis_type,
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_dead_tup as dead_tuples,
    CASE WHEN n_tup_ins + n_tup_upd + n_tup_del = 0 THEN 0 
         ELSE n_dead_tup::float / (n_tup_ins + n_tup_upd + n_tup_del) * 100 
    END as bloat_ratio
FROM pg_stat_user_tables 
WHERE schemaname = 'public' 
AND tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY bloat_ratio DESC;

-- 檢查慢查詢統計
SELECT 
    'Slow Query Analysis' as analysis_type,
    query,
    calls,
    total_time,
    mean_time,
    min_time,
    max_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%ibmcase%' OR query LIKE '%ibmfym%' OR query LIKE '%ibmlist%' OR query LIKE '%ibmsts%'
ORDER BY mean_time DESC
LIMIT 10;

-- 檢查鎖定情況
SELECT 
    'Lock Analysis' as analysis_type,
    pg_stat_activity.pid,
    pg_stat_activity.query,
    pg_locks.mode,
    pg_locks.granted
FROM pg_stat_activity, pg_locks 
WHERE pg_stat_activity.pid = pg_locks.pid
AND NOT pg_locks.granted;

\echo '=== 每月維護腳本完成 ==='

-- ========================================================================
-- 4. 效能監控查詢
-- ========================================================================

-- 4.1 索引效能監控
-- 檔案：monitoring_queries.sql
\echo '=== 效能監控查詢開始 ==='

-- 查詢最常使用的索引
SELECT 
    'Most Used Indexes' as monitor_type,
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY idx_scan DESC
LIMIT 20;

-- 查詢最少使用的索引
SELECT 
    'Least Used Indexes' as monitor_type,
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY idx_scan ASC
LIMIT 20;

-- 查詢表的存取統計
SELECT 
    'Table Access Statistics' as monitor_type,
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_dead_tup
FROM pg_stat_user_tables 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY seq_scan DESC;

-- 查詢緩存命中率
SELECT 
    'Cache Hit Ratio' as monitor_type,
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    CASE WHEN heap_blks_hit + heap_blks_read = 0 THEN 0 
         ELSE heap_blks_hit::float / (heap_blks_hit + heap_blks_read) * 100 
    END as cache_hit_ratio
FROM pg_statio_user_tables 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY cache_hit_ratio DESC;

-- 查詢索引緩存命中率
SELECT 
    'Index Cache Hit Ratio' as monitor_type,
    schemaname,
    tablename,
    indexname,
    idx_blks_read,
    idx_blks_hit,
    CASE WHEN idx_blks_hit + idx_blks_read = 0 THEN 0 
         ELSE idx_blks_hit::float / (idx_blks_hit + idx_blks_read) * 100 
    END as index_cache_hit_ratio
FROM pg_statio_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY index_cache_hit_ratio DESC;

\echo '=== 效能監控查詢完成 ==='

-- ========================================================================
-- 5. 問題診斷查詢
-- ========================================================================

-- 5.1 查詢阻塞情況
SELECT 
    'Blocking Queries' as diagnostic_type,
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS blocking_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- 5.2 查詢磁碟使用情況
SELECT 
    'Disk Usage Analysis' as diagnostic_type,
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
    ROUND(
        (pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename))::numeric 
        / pg_total_relation_size(schemaname||'.'||tablename) * 100, 2
    ) as index_ratio
FROM pg_tables 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts', 'ibmcprp')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ========================================================================
-- 6. 自動化維護腳本生成
-- ========================================================================

-- 6.1 生成 cron 腳本
-- 檔案：setup_cron_jobs.sh
/*
#!/bin/bash
# 新北市違章建築管理系統 - 自動化維護腳本設定

# 設定環境變數
export PGPASSWORD='S!@h@202203'
export PGHOST='localhost'
export PGPORT='5432'
export PGUSER='postgres'
export PGDATABASE='bms'

# 每日維護 (每天凌晨 2:00 執行)
echo "0 2 * * * /usr/bin/psql -f /path/to/daily_maintenance.sql >> /var/log/db_maintenance.log 2>&1" | crontab -

# 每週維護 (每週日凌晨 3:00 執行)
echo "0 3 * * 0 /usr/bin/psql -f /path/to/weekly_maintenance.sql >> /var/log/db_maintenance.log 2>&1" | crontab -

# 每月維護 (每月1日凌晨 4:00 執行)
echo "0 4 1 * * /usr/bin/psql -f /path/to/monthly_maintenance.sql >> /var/log/db_maintenance.log 2>&1" | crontab -

# 效能監控 (每小時執行)
echo "0 * * * * /usr/bin/psql -f /path/to/monitoring_queries.sql >> /var/log/db_monitoring.log 2>&1" | crontab -

echo "Cron jobs for database maintenance have been set up successfully."
*/

-- ========================================================================
-- 使用說明
-- ========================================================================
/*
1. 每日維護：
   PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -c "ANALYZE ibmcase; ANALYZE ibmfym; ANALYZE ibmlist; ANALYZE ibmsts;"

2. 每週維護：
   PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f weekly_maintenance.sql

3. 每月維護：
   PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f monthly_maintenance.sql

4. 效能監控：
   PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f monitoring_queries.sql

5. 問題診斷：
   根據監控結果，執行相應的診斷查詢
*/

-- ========================================================================
-- 維護腳本完成
-- ========================================================================