<%@ page contentType="text/javascript" import="java.io.*" %><%
/*
 * ClientI18N.jsp - 國際化JavaScript檔案載入器
 * 用於動態載入國際化的JavaScript檔案
 */

String fileName = request.getParameter("file");
String locale = request.getParameter("locale");

if (fileName == null || fileName.trim().isEmpty()) {
    response.setStatus(404);
    out.println("// Error: No file parameter specified");
    return;
}

// 基本安全檢查，防止路徑遍歷攻擊
if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
    response.setStatus(403);
    out.println("// Error: Invalid file name");
    return;
}

// 支援的檔案清單
String[] validFiles = {"Globalize.js", "Functions.js", "DatePicker.js"};
boolean isValidFile = false;
for (String validFile : validFiles) {
    if (validFile.equals(fileName)) {
        isValidFile = true;
        break;
    }
}

if (!isValidFile) {
    response.setStatus(404);
    out.println("// Error: File not found - " + fileName);
    return;
}

// 設定JavaScript檔案的基礎路徑
String basePath = application.getRealPath("/js/lib/");
File jsFile = new File(basePath, fileName);

if (!jsFile.exists() || !jsFile.isFile()) {
    response.setStatus(404);
    out.println("// Error: File not found on disk - " + fileName);
    return;
}

try {
    // 讀取並輸出JavaScript檔案內容
    BufferedReader reader = new BufferedReader(new FileReader(jsFile));
    String line;
    while ((line = reader.readLine()) != null) {
        out.println(line);
    }
    reader.close();
} catch (IOException e) {
    response.setStatus(500);
    out.println("// Error reading file: " + e.getMessage());
}
%>