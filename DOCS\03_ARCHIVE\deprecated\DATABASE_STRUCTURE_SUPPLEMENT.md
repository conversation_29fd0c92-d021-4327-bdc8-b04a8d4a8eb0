# 資料庫結構補充文件

> **任務編號**: T1.2.1  
> **文件建立**: 2025-07-05  
> **狀態**: 完成  
> **總工時**: 4小時

## 摘要

本文件補充新北市違章建築管理系統中 `ibmlawfee` 相關表結構的詳細文件，包括主表、分期付款表和記錄表的完整結構分析。

## ibmlawfee 表結構

### 基本資訊
- **表名**: ibmlawfee
- **用途**: 違章建築法務收費管理
- **主鍵**: case_id (NOT NULL)
- **記錄數**: 14筆（截至文件建立時）

### 欄位定義

| 欄位名稱 | 資料型別 | 長度 | 必填 | 預設值 | 說明 |
|----------|----------|------|------|--------|------|
| case_id | varchar | 10 | ✓ | - | 法務收費案件編號 |
| reg_yy | varchar | 3 | | - | 認定號碼-1（年度） |
| reg_no | varchar | 7 | | - | 認定號碼-2（流水號） |
| construction_date | varchar | 5 | | - | 工期 |
| toll_name | varchar | 10 | | - | 收費對象 |
| paymentbook_date | numeric | 8,0 | | - | 繳款費開立日期 |
| reimburse_no | varchar | 50 | | - | 繳款書銷帳編號 |
| paymentbook_no | varchar | 50 | | - | 繳款書流水號 |
| punishment_date | numeric | 8,0 | | - | 處分書開立日期 |
| punishment_no | varchar | 50 | | - | 處分書文號 |
| punishment_sent_date | numeric | 8,0 | | - | 處分書送達日期 |
| final_payment_date | numeric | 8,0 | | - | 最後繳款期限 |
| payment_date | numeric | 8,0 | | - | 繳款日期 |
| check_property_date | numeric | 8,0 | | - | 函查財產日期 |
| transfer_date | numeric | 8,0 | | - | 移送行政執行日期 |
| received_date | numeric | 8,0 | | - | 行政執行署收款日期 |
| is_installment | varchar | 1 | | - | 是否分期付款 |
| payable_amount | integer | - | | - | 應繳款金額 |
| pay_amount | integer | - | | - | 已繳款金額 |
| unpay_amount | integer | - | | - | 未付款金額 |
| payment_type | varchar | 1 | | - | 繳款情形 |
| note | varchar | 200 | | - | 備註 |
| cr_user | varchar | 10 | | - | 建立人員 |
| cr_date | numeric | 14,0 | | - | 建立日期 |
| op_user | varchar | 10 | | - | 修改人員 |
| op_date | numeric | 14,0 | | - | 修改日期 |
| toll_type | varchar | 3 | | - | 收費類型 |
| toll_type_note | varchar | 200 | | - | 收費類型說明 |
| payment_type_note | varchar | 200 | | - | 繳款情形說明 |

### 觸發器

1. **get_new_case_id**
   - 類型: BEFORE INSERT
   - 功能: 自動產生新的案件編號
   - 規則: 基於當前年月（扣除191100）+ 5位流水號

2. **set_new_ibmlawfee_log**
   - 類型: BEFORE INSERT OR UPDATE
   - 功能: 記錄異動日誌
   - 目標: log_ibmlawfee 表

## ibmlawfee_installment 表結構

### 基本資訊
- **表名**: ibmlawfee_installment
- **用途**: 分期付款記錄
- **主鍵**: id (自增序列)

### 欄位定義

| 欄位名稱 | 資料型別 | 長度 | 必填 | 預設值 | 說明 |
|----------|----------|------|------|--------|------|
| case_id | varchar | 10 | | - | 法務收費案件編號 |
| year | varchar | 3 | | - | 年度 |
| date | numeric | 8,0 | | - | 繳款日期 |
| amount | integer | - | | - | 繳款金額 |
| id | integer | - | ✓ | nextval('id_add'::regclass) | 自增主鍵 |

## log_ibmlawfee 表結構

### 基本資訊
- **表名**: log_ibmlawfee
- **用途**: ibmlawfee 異動記錄表
- **主鍵**: id (UUID)

### 欄位定義
- 包含 ibmlawfee 主表的所有欄位
- 額外欄位:
  - `id`: UUID 主鍵
  - `action_time`: 異動時間戳（預設 now()）

## 業務流程分析

### 案件編號規則
```sql
-- 案件編號格式: YYMMMXXXXX
-- YY: 年度（當前年月 - 191100）
-- MMM: 月份（3位）
-- XXXXX: 5位流水號
```

### 收費流程
1. **建立收費案件** → ibmlawfee.case_id 自動產生
2. **開立繳款書** → paymentbook_date, paymentbook_no
3. **處分書程序** → punishment_date, punishment_no, punishment_sent_date
4. **繳款處理** → payment_date, pay_amount
5. **分期處理** → is_installment = 'Y', 記錄至 ibmlawfee_installment
6. **強制執行** → transfer_date, received_date

### 資料驗證建議

1. **金額驗證**
   ```sql
   -- payable_amount = pay_amount + unpay_amount
   CHECK (payable_amount = COALESCE(pay_amount, 0) + COALESCE(unpay_amount, 0))
   ```

2. **日期邏輯驗證**
   ```sql
   -- 繳款日期不能早於開立日期
   CHECK (payment_date >= paymentbook_date OR payment_date IS NULL)
   ```

## 查詢範例

### 1. 案件收費統計
```sql
SELECT 
    toll_type,
    COUNT(*) as case_count,
    SUM(payable_amount) as total_payable,
    SUM(pay_amount) as total_paid,
    SUM(unpay_amount) as total_unpaid
FROM ibmlawfee 
GROUP BY toll_type
ORDER BY toll_type;
```

### 2. 分期付款案件
```sql
SELECT 
    l.case_id,
    l.toll_name,
    l.payable_amount,
    COUNT(i.id) as installment_count,
    SUM(i.amount) as total_installment_paid
FROM ibmlawfee l
LEFT JOIN ibmlawfee_installment i ON l.case_id = i.case_id
WHERE l.is_installment = 'Y'
GROUP BY l.case_id, l.toll_name, l.payable_amount;
```

### 3. 逾期未繳案件
```sql
SELECT 
    case_id,
    toll_name,
    payable_amount,
    unpay_amount,
    final_payment_date
FROM ibmlawfee 
WHERE unpay_amount > 0 
  AND final_payment_date < TO_NUMBER(TO_CHAR(CURRENT_DATE, 'YYYYMMDD'), '99999999');
```

## DDL 語句

### 建立主表索引建議
```sql
-- 建議增加的索引
CREATE INDEX idx_ibmlawfee_reg_yy_no ON ibmlawfee(reg_yy, reg_no);
CREATE INDEX idx_ibmlawfee_payment_date ON ibmlawfee(payment_date);
CREATE INDEX idx_ibmlawfee_payment_type ON ibmlawfee(payment_type);
CREATE INDEX idx_ibmlawfee_toll_type ON ibmlawfee(toll_type);
```

### 建立約束建議
```sql
-- 金額邏輯約束
ALTER TABLE ibmlawfee 
ADD CONSTRAINT chk_amount_consistency 
CHECK (payable_amount = COALESCE(pay_amount, 0) + COALESCE(unpay_amount, 0));

-- 分期付款邏輯約束
ALTER TABLE ibmlawfee 
ADD CONSTRAINT chk_installment_flag 
CHECK (is_installment IN ('Y', 'N', NULL));
```

## 注意事項

1. **資安風險**: 該表含敏感財務資料，需要適當的存取控制
2. **備份策略**: 建議定期備份，特別是月底結算前
3. **效能考量**: 大量查詢時建議使用適當索引
4. **審計要求**: log_ibmlawfee 保存完整異動記錄，建議定期歸檔

---

*此文件由【B】Claude Code - 後端開發任務組產出*