<%--JSP Page Init @1-7E5D825F--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%@ page import="java.io.*,java.net.*,java.nio.file.Files,java.time.LocalDateTime,java.time.format.DateTimeFormatter"%>
<%@page contentType="text/html; charset=UTF-8"%>
<%--End JSP Page Init--%>
<jsp:useBean id="IM40201" class="com.ezek.report.IM40201">
	<jsp:setProperty name="IM40201" property="reset" value="emptyString" />
	<jsp:setProperty name="IM40201" property="*" />
</jsp:useBean>
<%
	// 固定資料庫連線名稱
	String CONNECTION_NAME = "DBConn";
	String TEMPLATE_FILE_NAME = "";
	String result = "0"; 
	String responseMessage = "";

	String import_id = request.getParameter("import_id"); //匯入編號
	if (import_id == null || !isValidUUID(import_id)) {
		responseMessage = "{\"success\":\"" + result + "\", \"message\":\"無效的import_id\"}";
		throw new Exception("無效的import_id");
	}

	try {
		// 檢查 import_id 是否存在於 im52101_excel_imports 表中
		String importExists = Utils.convertToString(DBTools.dLookUp("COUNT(*)", "im52101_excel_imports", "import_id = '" + import_id + "'", "DBConn"));

		if (importExists == null || "0".equals(importExists) || "".equals(importExists)) {
			// 如果記錄不存在，直接回應 200 但不顯示任何資訊
			responseMessage = "{\"success\":\"" + result + "\", \"message\":\"資料庫中找不到此import_id\"}";
			throw new Exception("資料庫中找不到此import_id");
		}

		String case_id = request.getParameter("case_id"); //案件編號

		String reg_num = Utils.convertToString(DBTools.dLookUp("reg_yy || reg_no", "IBMCASE", "case_id = '" + case_id + "'", "DBConn"));
		// 打包檔案類型 ANO_END 拆除附件
		String ANO_Array[] = { "ANO_END" };
		String repNM = "結案報告書";

		// 有認定號碼用認定號碼
		if (reg_num != null && !"".equals(reg_num.trim())) {
			repNM += "_" + reg_num;
		} else {
			responseMessage = "{\"success\":\"" + result + "\", \"message\":\"找不到認定號碼\"}";
			throw new Exception("找不到認定號碼");
		}

		

	

		// 系統路徑參數
		String SEPARATOR = System.getProperty("file.separator");
		String REAL_PATH = application.getRealPath("/");

		// 專案所在位置
		String rootPath = REAL_PATH;
		rootPath += (REAL_PATH.substring(REAL_PATH.length() - 1).equals(SEPARATOR)) ? "" : SEPARATOR;

		HashMap<String, Object> mParameters = new HashMap<String, Object>();
		String[] conditionList = new String[1];
		// report位置
		String reportPath = rootPath;
		reportPath += "report" + SEPARATOR;
		String out_ext = "PDF"; //產出報表類型
		String dataNeedType = "ALL"; //報表類型
		// get the value(s) from the URL parameter(s)
		conditionList[0] = case_id;
		// 報表列印類型
		TEMPLATE_FILE_NAME = repNM + ".pdf";
		mParameters.put("conditionList", conditionList);
		mParameters.put("outExt", "PDF"); // 下載副檔名
		mParameters.put("dataNeedType", dataNeedType);
		mParameters.put("outFileName", TEMPLATE_FILE_NAME);
		// IM40201 不使用 ZIP_TAG 參數
		mParameters.put("onlySecondCopy", "Y");

		IM40201.setAppPath(rootPath);
		IM40201.setReportPath(reportPath);
		IM40201.produceSecondCopyOnly(mParameters);

		// 結案報告書
		File pdfFile = new File(reportPath + "output" + SEPARATOR + TEMPLATE_FILE_NAME);

		// // 壓縮檔 所在位置
		String picPath = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
		String originalFilePath = picPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR;
		String zipRootPath = picPath + "im52101" + SEPARATOR + "temp" + SEPARATOR + import_id + SEPARATOR;
		String zipReportPath = zipRootPath + reg_num + SEPARATOR;

		File zipRootFolder = new File(zipRootPath);
		if (!zipRootFolder.exists()) {
			zipRootFolder.mkdirs();
		}
		File zipTmpFolder = new File(zipReportPath);
		if (!zipTmpFolder.exists()) {
			zipTmpFolder.mkdir();
		}

		if (pdfFile.exists()) {
			File toPdfFile = new File(zipReportPath + TEMPLATE_FILE_NAME);

			copyFileUsingJava7Files(pdfFile, toPdfFile);

			for (int an_i = 0; an_i < ANO_Array.length; an_i++) {
				File fromAno = new File(originalFilePath + ANO_Array[an_i]);
				File toAno = new File(zipReportPath + ANO_Array[an_i]);
				if (fromAno.exists()) {
					toAno.mkdir();
					copyFolder(fromAno, toAno);
				}
			}

			if (pdfFile.exists()) {
				pdfFile.delete();
			}
		}

		result = "1";
		responseMessage = "{\"success\":\"" + result + "\", \"message\":\"處理完成\"}";
	} catch (Exception e) {
		System.err.println("An error occurred: " + e.getMessage());
		e.printStackTrace();
		if ("0".equals(result)) {
			responseMessage = "{\"success\":\"" + result + "\", \"message\":\"" + e.getMessage() + "\"}";
		}
	} finally {
		out.println(responseMessage);
		out.flush();
	}
%>

<%!
	// Method to validate if a string is a valid UUID format
	private static boolean isValidUUID(String uuid) {
		if (uuid == null) {
			return false;
		}
		// Trim whitespace before validation
		String trimmedUuid = uuid.trim();
		// Regex for UUID format: 8-4-4-4-12 hexadecimal characters
		String uuidRegex = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$";
		return trimmedUuid.matches(uuidRegex);
	}

	private static void copyFileUsingJava7Files(File source, File dest) throws IOException {
		if (source.exists() && !dest.exists()) {
			Files.copy(source.toPath(), dest.toPath());
		}
	}

	private static void copyFolder(File source, File dest) throws Exception {
		if (source.isDirectory()) {
			for (File tmp : source.listFiles()) {
				copyFileUsingJava7Files(new File(source, tmp.getName()), new File(dest, tmp.getName()));
			}
		} else {
			copyFileUsingJava7Files(source, dest);
		}
	}
%>
