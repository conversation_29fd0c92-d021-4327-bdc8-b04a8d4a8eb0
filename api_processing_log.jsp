<%@page contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils"%>
<%@page import="java.sql.*, java.util.Date, java.util.UUID, org.json.simple.*"%>
<%@page import="javax.servlet.*, java.net.URLDecoder, java.text.SimpleDateFormat" %>

<%
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    JSONObject outJson = new JSONObject();
    String action = request.getParameter("action"); // batch_info, stage_stats, processing_logs
    String importId = request.getParameter("import_id");
    String viewType = request.getParameter("view"); // exceptions, all
    String pageNum = request.getParameter("page");
    String pageSize = request.getParameter("size");
    
    if (StringUtils.isEmpty(action)) {
        action = "batch_info";
    }
    
    if (StringUtils.isEmpty(importId)) {
        outJson.put("result", "NG");
        outJson.put("message", "缺少必要的 import_id 參數");
        out.println(outJson.toString());
        return;
    }
    
    // 分頁參數
    int currentPage = 1;
    int recordsPerPage = 50;
    
    try {
        if (!StringUtils.isEmpty(pageNum)) {
            currentPage = Integer.parseInt(pageNum);
        }
        if (!StringUtils.isEmpty(pageSize)) {
            recordsPerPage = Integer.parseInt(pageSize);
        }
    } catch (NumberFormatException e) {
        // 使用預設值
    }
    
    DBConnectionManager dbcm = null;
    Connection conn = null;
    PreparedStatement pstmt = null;
    ResultSet rs = null;
    
    try {
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection("DBConn");
        
        if ("batch_info".equals(action)) {
            // 批次基本資訊
            String sql = "SELECT import_id, original_file_name, upload_timestamp, status, " +
                        "total_rows_in_excel, processed_rows_count, error_rows_count, " +
                        "processing_start_time, processing_end_time, cr_user, acc_memo " +
                        "FROM im52101_excel_imports WHERE import_id = ?";
            
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, importId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                JSONObject batchInfo = new JSONObject();
                batchInfo.put("import_id", rs.getString("import_id"));
                batchInfo.put("original_file_name", rs.getString("original_file_name"));
                batchInfo.put("upload_timestamp", rs.getString("upload_timestamp"));
                batchInfo.put("status", rs.getString("status"));
                batchInfo.put("total_rows_in_excel", rs.getInt("total_rows_in_excel"));
                batchInfo.put("processed_rows_count", rs.getInt("processed_rows_count"));
                batchInfo.put("error_rows_count", rs.getInt("error_rows_count"));
                batchInfo.put("processing_start_time", rs.getString("processing_start_time"));
                batchInfo.put("processing_end_time", rs.getString("processing_end_time"));
                batchInfo.put("cr_user", rs.getString("cr_user"));
                batchInfo.put("acc_memo", rs.getString("acc_memo"));
                
                // 轉換狀態為中文
                String status = rs.getString("status");
                String displayStatus = status;
                if ("NEW_UPLOAD".equals(status)) {
                    displayStatus = "未處理";
                } else if ("PENDING".equals(status)) {
                    displayStatus = "待處理";
                } else if ("PROCESSING".equals(status)) {
                    displayStatus = "處理中";
                } else if ("COMPLETED".equals(status)) {
                    displayStatus = "匯入完成";
                } else if ("FAILED".equals(status)) {
                    displayStatus = "匯入失敗";
                }
                batchInfo.put("status_display", displayStatus);
                
                outJson.put("result", "OK");
                outJson.put("data", batchInfo);
            } else {
                outJson.put("result", "NG");
                outJson.put("message", "找不到指定的批次資料");
            }
            
        } else if ("stage_stats".equals(action)) {
            // 處理階段統計
            String sql = "SELECT processing_stage, " +
                        "CASE processing_stage " +
                        "    WHEN 'EXCEL_PARSING' THEN 'Excel 解析階段' " +
                        "    WHEN 'DATA_VALIDATION' THEN '資料驗證階段' " +
                        "    WHEN 'JSP_CALLING' THEN 'JSP 呼叫階段' " +
                        "    WHEN 'FILE_COLLECTING' THEN '檔案收集階段' " +
                        "    WHEN 'COMPLETED' THEN '處理完成' " +
                        "    ELSE processing_stage " +
                        "END as stage_description, " +
                        "COUNT(*) as total_records, " +
                        "COUNT(CASE WHEN processing_status = '處理成功' THEN 1 END) as success_records, " +
                        "COUNT(CASE WHEN processing_status != '處理成功' THEN 1 END) as failed_records, " +
                        "ROUND(COUNT(CASE WHEN processing_status = '處理成功' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate " +
                        "FROM im52101_excel_processing_log " +
                        "WHERE import_id = ? " +
                        "GROUP BY processing_stage " +
                        "ORDER BY " +
                        "    CASE processing_stage " +
                        "        WHEN 'EXCEL_PARSING' THEN 1 " +
                        "        WHEN 'DATA_VALIDATION' THEN 2 " +
                        "        WHEN 'JSP_CALLING' THEN 3 " +
                        "        WHEN 'FILE_COLLECTING' THEN 4 " +
                        "        WHEN 'COMPLETED' THEN 5 " +
                        "    END";
            
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, importId);
            rs = pstmt.executeQuery();
            
            JSONArray stageStats = new JSONArray();
            while (rs.next()) {
                JSONObject stage = new JSONObject();
                stage.put("processing_stage", rs.getString("processing_stage"));
                stage.put("stage_description", rs.getString("stage_description"));
                stage.put("total_records", rs.getInt("total_records"));
                stage.put("success_records", rs.getInt("success_records"));
                stage.put("failed_records", rs.getInt("failed_records"));
                stage.put("success_rate", rs.getDouble("success_rate"));
                stageStats.add(stage);
            }
            
            outJson.put("result", "OK");
            outJson.put("data", stageStats);
            
        } else if ("processing_logs".equals(action)) {
            // 處理記錄詳細清單
            String whereClause = "import_id = ?";
            if ("exceptions".equals(viewType)) {
                whereClause += " AND processing_status IN ('案件不存在', '格式錯誤', '重複的認定號碼', '處理失敗')";
            }
            
            // 先查詢總記錄數
            String countSql = "SELECT COUNT(*) as total FROM im52101_excel_processing_log WHERE " + whereClause;
            pstmt = conn.prepareStatement(countSql);
            pstmt.setString(1, importId);
            rs = pstmt.executeQuery();
            
            int totalRecords = 0;
            if (rs.next()) {
                totalRecords = rs.getInt("total");
            }
            rs.close();
            pstmt.close();
            
            // 計算分頁
            int totalPages = (int) Math.ceil((double) totalRecords / recordsPerPage);
            int offset = (currentPage - 1) * recordsPerPage;
            
            // 查詢分頁資料
            String sql = "SELECT log_id, row_number, reg_num, case_id, processing_stage, " +
                        "CASE processing_stage " +
                        "    WHEN 'EXCEL_PARSING' THEN 'Excel 解析' " +
                        "    WHEN 'DATA_VALIDATION' THEN '資料驗證' " +
                        "    WHEN 'JSP_CALLING' THEN 'JSP 呼叫' " +
                        "    WHEN 'FILE_COLLECTING' THEN '檔案收集' " +
                        "    WHEN 'COMPLETED' THEN '處理完成' " +
                        "    ELSE processing_stage " +
                        "END as stage_description, " +
                        "processing_status, error_message, processing_datetime " +
                        "FROM im52101_excel_processing_log " +
                        "WHERE " + whereClause + " " +
                        "ORDER BY row_number ASC, processing_datetime DESC " +
                        "LIMIT ? OFFSET ?";
            
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, importId);
            pstmt.setInt(2, recordsPerPage);
            pstmt.setInt(3, offset);
            rs = pstmt.executeQuery();
            
            JSONArray processingLogs = new JSONArray();
            while (rs.next()) {
                JSONObject log = new JSONObject();
                log.put("log_id", rs.getLong("log_id"));
                log.put("row_number", rs.getInt("row_number"));
                log.put("reg_num", rs.getString("reg_num"));
                log.put("case_id", rs.getString("case_id"));
                log.put("processing_stage", rs.getString("processing_stage"));
                log.put("stage_description", rs.getString("stage_description"));
                log.put("processing_status", rs.getString("processing_status"));
                log.put("error_message", rs.getString("error_message"));
                log.put("processing_datetime", rs.getString("processing_datetime"));
                
                // 狀態樣式類別
                String statusClass = "";
                String status = rs.getString("processing_status");
                if ("處理成功".equals(status)) {
                    statusClass = "success";
                } else if ("案件不存在".equals(status) || "格式錯誤".equals(status) || "重複的認定號碼".equals(status)) {
                    statusClass = "warning";
                } else if ("處理失敗".equals(status)) {
                    statusClass = "danger";
                }
                log.put("status_class", statusClass);
                
                processingLogs.add(log);
            }
            
            // 分頁資訊
            JSONObject pagination = new JSONObject();
            pagination.put("current_page", currentPage);
            pagination.put("total_pages", totalPages);
            pagination.put("total_records", totalRecords);
            pagination.put("records_per_page", recordsPerPage);
            pagination.put("has_prev", currentPage > 1);
            pagination.put("has_next", currentPage < totalPages);
            
            outJson.put("result", "OK");
            outJson.put("data", processingLogs);
            outJson.put("pagination", pagination);
            
        } else {
            outJson.put("result", "NG");
            outJson.put("message", "不支援的 action: " + action);
        }
        
    } catch (SQLException sqle) {
        outJson.put("result", "NG");
        outJson.put("message", "資料庫錯誤: " + sqle.getMessage());
        System.err.println("API Processing Log SQLException: " + sqle.getMessage());
        sqle.printStackTrace();
    } catch (Exception e) {
        outJson.put("result", "NG");
        outJson.put("message", "系統錯誤: " + e.getMessage());
        System.err.println("API Processing Log Exception: " + e.getMessage());
        e.printStackTrace();
    } finally {
        if (rs != null) {
            try { rs.close(); } catch (SQLException e) { /* ignore */ }
        }
        if (pstmt != null) {
            try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        }
        if (conn != null && dbcm != null) {
            try {
                dbcm.freeConnection("DBConn", conn);
            } catch (Exception e) {
                System.err.println("API Processing Log Connection Close Error: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
    
    out.println(outJson.toString());
%>