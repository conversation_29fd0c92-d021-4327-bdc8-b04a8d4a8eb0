<!--Page model head @1-B026CFB5-->
<Page name="MasterPage" restricted="False" included="False"	masterPage="" accessDeniedPage=".jsp" convertRule="Absolute" onlySslAccess="False">
<!--End Page model head-->

<!--Page componets @1-919C0A27-->
    <ContentPlaceholder  name="Head" pathID="MasterPageHead">
    </ContentPlaceholder>
    <ContentPlaceholder  name="Content" pathID="MasterPageContent">
    </ContentPlaceholder>
    <ContentPlaceholder  name="Menu" pathID="MasterPageMenu">
    </ContentPlaceholder>
    <ContentPlaceholder  name="Sidebar1" pathID="MasterPageSidebar1">
    </ContentPlaceholder>
    <ContentPlaceholder  name="HeaderSidebar" pathID="MasterPageHeaderSidebar">
    </ContentPlaceholder>
<!--End Page componets-->

<!--Page attributes @1-CF6A0607-->
    <Attribute id = "8" name = "pathToCurrentPage" sourceType = "" source = "./Designs/Light/" place = "" />
<!--End Page attributes-->

<!--Page model tail @1-2BAFA7FA-->
</Page>
<!--End Page model tail-->

