<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils"%>

<%@page import="java.sql.*, java.util.Date, java.util.UUID, org.json.simple.*"%>
<%@page import="javax.servlet.*, java.net.URLDecoder" %>
<%
    JSONObject jsonResponse  = new JSONObject();
    DBConnectionManager dbcm = null;
    Connection conn = null;
  
    PreparedStatement pstmt = null;
    ResultSet rs = null;
    
  
    try {
        // Get the import_id parameter and convert to UUID
        String importIdStr = request.getParameter("id");
        if (importIdStr == null || importIdStr.trim().isEmpty()) {
            throw new Exception("Import ID is required");
        }
        
        UUID importId = UUID.fromString(importIdStr);

	
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection("DBConn");
        conn.setAutoCommit(false);

        String sql = "SELECT i.import_id, i.status " +
                    "FROM im52101_excel_imports i " +
                    "WHERE import_id = ?::uuid";
                    
        pstmt = conn.prepareStatement(sql);
        pstmt.setObject(1, importId); // Use setObject for UUID
        rs = pstmt.executeQuery();

        if (rs.next()) {
            String dbStatus = rs.getString("status");
            String displayStatus = "";

            // Convert database status to display status
            if (dbStatus.equals("NEW_UPLOAD")) {
                displayStatus = "未處理";
            } else if (dbStatus.equals("PENDING")) {
                displayStatus = "待處理";
            } else if (dbStatus.equals("PROCESSING")) {
                displayStatus = "處理中";
            } else if (dbStatus.equals("COMPLETED")) {
                displayStatus = "完成";
            } else if (dbStatus.equals("FAILED")) {
                displayStatus = "錯誤";
            }


            jsonResponse.put("status", displayStatus);
        }

    } catch (Exception e) {
        jsonResponse.put("error", e.getMessage());
    } finally {
        
        // Close database resources
        try { if (rs != null) rs.close(); } catch (Exception e) {}
     if (pstmt != null) {
			try {
				pstmt.close();
			} catch (SQLException sqle) {
				System.err.println("case_empty_dis ::: executeSqlCmd ::: SQLException");
				sqle.printStackTrace();
			}
		}
		
		if (conn != null && dbcm != null) {
			try {
				conn.close();
				
				// Return the connection to the named pool
				dbcm.freeConnection("DBConn", conn);
			} catch (SQLException sqle) {
				System.err.println("case_empty_dis ::: executeSqlCmd ::: SQLException");
				sqle.printStackTrace();
			}
		}
    }

    // Send JSON response
    out.print(jsonResponse.toString());
    out.flush();
%>