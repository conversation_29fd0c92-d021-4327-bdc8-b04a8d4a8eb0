# JSP檔案中的資料表引用分析

## 核心系統表 (Core System Tables)

### 1. IBMCASE（案件主表）
- **引用檔案**: 
  - case_empty_dis.jsp (UPDATE)
  - case_withdraw.jsp (UPDATE)
  - getBDSJson.jsp (SELECT)
  - im10101_lis.jsp (SELECT)
  - im10101_lisHandlers.jsp (SELECT)
  - im10101_man_AHandlers.jsp (INSERT, SELECT)
  - im10101_man_BHandlers.jsp
  - im10101_man_CHandlers.jsp
  - 以及更多im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 違章建築案件的主要資料表

### 2. IBMFYM（流程記錄表）
- **引用檔案**: 
  - case_empty_dis.jsp (INSERT)
  - im10101_man_AHandlers.jsp (INSERT, SELECT, UPDATE, DELETE)
  - im10101_man_BHandlers.jsp (INSERT, SELECT, UPDATE)
  - im10101_man_CHandlers.jsp
  - im10101_man_B.jsp (HTML表單)

- **SQL語句類型**: SELECT, INSERT, UPDATE, DELETE
- **用途**: 案件處理流程歷程記錄

### 3. IBMCODE（系統代碼表）
- **引用檔案**: 
  - im10101_lisHandlers.jsp (SELECT)
  - im10101_man_A.jsp (SELECT)
  - im10101_man_AHandlers.jsp (SELECT)
  - im10101_man_B.jsp (SELECT)
  - im10101_man_BHandlers.jsp (SELECT)
  - im10101_man_C.jsp (SELECT)
  - im10101_man_CHandlers.jsp (SELECT)
  - im10101_man_copyCase.jsp (SELECT)

- **SQL語句類型**: SELECT
- **用途**: 系統代碼定義表，包含狀態碼、工作類型等

### 4. IBMSTS（狀態表）
- **引用檔案**: 
  - im10101_lisHandlers.jsp (SELECT)
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, UPDATE
- **用途**: 案件狀態管理

### 5. IBMUSER（使用者表）
- **引用檔案**: 
  - im10101_lisHandlers.jsp (SELECT)
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, UPDATE
- **用途**: 系統使用者管理

## 業務關聯表 (Business Related Tables)

### 6. IBMCSLAN（案件地號表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 案件相關地號資料

### 7. IBMDISNM（違建人名表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 違建人姓名資料

### 8. IBMCSPRJ（案件專案表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 案件專案關聯

### 9. IBMLIST（清單表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 各種清單資料

### 10. IBMMNRP（報表相關表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表管理

## 特殊功能表 (Special Function Tables)

### 11. IBMCASE_CP（案件複製表）
- **引用檔案**: 
  - im10101_man_AHandlers.jsp

- **SQL語句類型**: SELECT, INSERT
- **用途**: 案件複製功能

### 12. IBMVIOLATION_LAND（違建土地表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT
- **用途**: 違建土地資料

### 13. BMSDISOBEY_DIST（違建分區表）
- **引用檔案**: 
  - im10101_lisHandlers.jsp

- **SQL語句類型**: SELECT, UPDATE
- **用途**: 違建分區管理

## 勞工相關表 (Labor Related Tables)

### 14. IBMLABORLAW（勞工法表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 勞工法規相關

### 15. IBMLABORLAW_CASE（勞工法案件表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 勞工法規案件

### 16. IBMLABORLAW_FILE（勞工法檔案表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 勞工法規檔案

### 17. IBMLABORLAW_CSLAN（勞工法地號表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 勞工法規地號

### 18. IBMLABORLAW_ADDRESS（勞工法地址表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 勞工法規地址

### 19. IBMLAWFEE（法規費用表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 法規費用管理

### 20. IBMLAWFEE_INSTALLMENT（法規費用分期表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 法規費用分期

## 火災相關表 (Fire Related Tables)

### 21. IBMFIRECASE（火災案件表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 火災案件管理

### 22. IBMFIRECASE_FILE（火災案件檔案表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 火災案件檔案

### 23. IBMFIRECASE_CSLAN（火災案件地號表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 火災案件地號

### 24. IBMFIRECASE_ADDRESS（火災案件地址表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 火災案件地址

## UAV相關表 (UAV Related Tables)

### 25. UAVDOM（無人機主表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 無人機資料管理

### 26. UAVDOMRL（無人機關聯表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 無人機關聯資料

### 27. UAVCSRL（無人機案件關聯表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 無人機案件關聯

### 28. UAVDCP（無人機資料複製表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 無人機資料複製

## 系統管理表 (System Management Tables)

### 29. IBMPERM（權限表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 系統權限管理

### 30. IBMROLE（角色表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 系統角色管理

### 31. IBMMENU（選單表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 系統選單管理

## 其他表格 (Other Tables)

### 32. IBMRPLAN（報表計畫表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表計畫管理

### 33. IBMRPLI（報表列表表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表列表管理

### 34. IBMRPUC（報表使用者表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表使用者管理

### 35. IBMRPFR（報表格式表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表格式管理

### 36. IBMRPSM（報表小計表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表小計管理

### 37. IBMMNRPF（報表檔案表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 報表檔案管理

### 38. IBMCPRP（複製相關表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 複製相關功能

### 39. IBMIFAP（界面應用表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 界面應用管理

### 40. IBMCASEBATCHIMPORT（案件批次匯入表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: 案件批次匯入

## 外部相關表 (External Related Tables)

### 41. BMSEMP（員工表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT
- **用途**: 員工資料管理

### 42. BDMLIST（基本資料清單表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT
- **用途**: 基本資料清單

### 43. BDMBASE（基本資料表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT
- **用途**: 基本資料管理

### 44. DMLLIST（資料管理清單表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT
- **用途**: 資料管理清單

### 45. ICGBM（系統整合表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, UPDATE
- **用途**: 系統整合管理

### 46. IM52101_EXCEL_IMPORTS（Excel匯入表）
- **引用檔案**: 
  - 多個im系列JSP檔案

- **SQL語句類型**: SELECT, INSERT, UPDATE
- **用途**: Excel匯入功能

## 摘要

總計發現 **46個主要資料表**，其中：
- **核心系統表**: 5個 (IBMCASE, IBMFYM, IBMCODE, IBMSTS, IBMUSER)
- **業務關聯表**: 5個 (IBMCSLAN, IBMDISNM, IBMCSPRJ, IBMLIST, IBMMNRP)
- **特殊功能表**: 3個 (IBMCASE_CP, IBMVIOLATION_LAND, BMSDISOBEY_DIST)
- **勞工相關表**: 6個 (IBMLABORLAW系列)
- **火災相關表**: 4個 (IBMFIRECASE系列)
- **UAV相關表**: 4個 (UAV系列)
- **系統管理表**: 3個 (IBMPERM, IBMROLE, IBMMENU)
- **其他表格**: 10個 (IBMRP系列等)
- **外部相關表**: 6個 (BMSEMP, BDMLIST等)

這些表格涵蓋了違章建築管理系統的完整功能，包括案件管理、流程控制、使用者管理、報表功能、系統整合等各個方面。

注意：基於CLAUDE.md的指導，系統使用的是以下核心表格：
- **buildcase** (對應IBMCASE)
- **tbflow** (對應IBMFYM)
- **ibmcode** (系統代碼表)
- **employees** (對應BMSEMP)
- **districts** (地區表，可能在其他檔案中)