package com.ezek.db;

import com.codecharge.db.JDBCConnection;
import com.codecharge.util.CCLogger;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 增強版連線池連線類別 - 方案一實施
 * 
 * 此類別擴展原有的 JDBCConnection，整合經過驗證的連線池功能。
 * 它確保每次取得的連線都是有效的，避免因資料庫逾時導致的連線失效問題。
 * 
 * 主要改進：
 * 1. 使用經過驗證的 Tomcat DataSource
 * 2. 連線取得前自動驗證
 * 3. 連線失效時自動重新取得
 * 4. 完全向後相容現有程式碼
 * 
 * 使用方式：
 * 在現有程式碼中將 PoolJDBCConnection 替換為 EnhancedPoolJDBCConnection
 * 
 * <AUTHOR> Administrator
 * @version 1.0
 * @since 2025-01-23
 */
public class EnhancedPoolJDBCConnection extends JDBCConnection {
    
    private static final CCLogger logger = CCLogger.getInstance();
    
    public EnhancedPoolJDBCConnection(String name) {
        this(name, true);
    }
    
    public EnhancedPoolJDBCConnection(String name, boolean initConnection) {
        super(name, initConnection);
    }
    
    /**
     * 取得經過驗證的資料庫連線
     * 
     * 此方法會：
     * 1. 檢查現有連線是否有效
     * 2. 如果無效或不存在，取得新的驗證連線
     * 3. 記錄連線狀態以便除錯
     */
    @Override
    public void getConnection() {
        // 如果已有連線，先檢查是否有效
        if (this.conn != null) {
            if (ValidatedConnectionManager.isConnectionValid(this.conn, 5)) {
                logger.debug("現有連線仍然有效: " + this.poolName);
                return; // 連線有效，直接使用
            } else {
                logger.warn("現有連線已失效，將重新取得: " + this.poolName);
                closeConnection(); // 關閉失效連線
            }
        }
        
        // 取得新的驗證連線
        try {
            this.conn = ValidatedConnectionManager.getValidatedConnection(this.poolName);
            logger.info("成功取得驗證連線: " + this.poolName);
            
            // 額外驗證 (雙重保險)
            if (!ValidatedConnectionManager.isConnectionValid(this.conn, 5)) {
                logger.error("新連線驗證失敗: " + this.poolName);
                throw new RuntimeException("新取得的連線驗證失敗: " + this.poolName);
            }
            
        } catch (SQLException e) {
            logger.error("取得驗證連線失敗: " + this.poolName, e);
            throw new RuntimeException("無法建立資料庫連線 '" + this.poolName + "'", e);
        }
    }
    
    /**
     * 關閉連線
     * 
     * 由於使用 Tomcat DataSource，連線會自動回到連線池
     */
    @Override
    public void closeConnection() {
        if (this.conn != null) {
            try {
                if (!this.conn.isClosed()) {
                    this.conn.close(); // 回到連線池
                    logger.debug("連線已歸還到連線池: " + this.poolName);
                }
            } catch (SQLException e) {
                logger.error("關閉連線時發生錯誤: " + this.poolName, e);
            } finally {
                this.conn = null;
            }
        }
    }
    
    /**
     * 檢查連線狀態 (新增方法)
     * 
     * @return true 如果連線有效且可用
     */
    public boolean isConnectionValid() {
        return this.conn != null && 
               ValidatedConnectionManager.isConnectionValid(this.conn, 5);
    }
    
    /**
     * 強制重新整理連線 (新增方法)
     * 
     * 在長時間運行的程序中，可以調用此方法確保連線新鮮度
     */
    public void refreshConnection() {
        logger.info("強制重新整理連線: " + this.poolName);
        closeConnection();
        getConnection();
    }
}