# 排拆執行流程分析報告

## 多Agent並行分析報告

本分析採用6個Agent並行處理方式，深度分析新北市違章建築管理系統的排拆執行業務流程。

### Agent並行分析配置
- **Agent 1**: 排拆執行相關JSP檔案搜尋和分類
- **Agent 2**: 狀態碼349/359/369等執行流程分析  
- **Agent 3**: 現場執行管理機制分析
- **Agent 4**: 執行完成確認和驗收流程
- **Agent 5**: 異常處理和風險管理機制
- **Agent 6**: 執行過程文檔和證據管理

## Agent 1 分析結果：排拆執行相關檔案清單

### im30xxx系列檔案（排拆執行核心模組）
```
im30101_man.jsp           - 排拆執行主要管理介面
im30101_manHandlers.jsp   - 排拆執行業務邏輯處理
im30101_lis.jsp           - 排拆執行案件清單
im30101_lisHandlers.jsp   - 清單查詢業務邏輯
im30101_man_2.jsp         - 排拆執行輔助介面
im30101_man_2Handlers.jsp - 輔助功能業務邏輯
im30101_getFolder.jsp     - 取得資料夾相關功能

im30102_lis.jsp           - 排拆執行次要清單
im30102_lisHandlers.jsp   - 次要清單業務邏輯

im30201_man.jsp           - UAV無人機執行管理
im30201_manHandlers.jsp   - UAV執行業務邏輯
im30201_lis.jsp           - UAV執行清單
im30201_lisHandlers.jsp   - UAV清單業務邏輯
im30201_updateUAV.jsp     - UAV資訊更新功能
im30201_getUAVJeson.jsp   - UAV JSON資料取得
```

### 檔案功能分類
1. **執行管理核心**: im30101系列 - 主要排拆執行管理功能
2. **執行監控輔助**: im30102系列 - 執行過程監控清單
3. **UAV執行管理**: im30201系列 - 無人機輔助執行管理

## Agent 2 分析結果：排拆執行狀態碼分析

### 核心執行狀態碼定義
基於im40501_manHandlers.jsp分析，排拆執行階段使用以下狀態碼：

#### 拆除科（單位代碼02x）
- **369**: 排拆已簽准（正常執行完成）
- **368**: 未拆原因已登錄（執行異常處理）

#### 勞安科/下水道科（單位代碼03x） 
- **259**: 認定已簽准（可執行狀態）
- **359**: 排拆已簽准（正常執行完成）
- **358**: 未拆原因已登錄（執行異常處理）

#### 廣拆科（單位代碼04x）
- **349**: 排拆已簽准（正常執行完成）
- **348**: 未拆原因已登錄（執行異常處理）

### 狀態碼轉換邏輯
```java
// 核心執行狀態判斷邏輯
String ACC_RLT_FOR_DEMOL = "'369', '368'";    // 拆除科
String ACC_RLT_FOR_OHS = "'259', '359', '358'"; // 勞安科(下水道)
String ACC_RLT_FOR_ADS = "'349', '348'";     // 廣拆科

// 根據單位ID決定可處理的狀態碼範圍
if (UNIT_ID.startsWith("02")) {      // 拆除科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_DEMOL).append(")");
} else if(UNIT_ID.startsWith("03")) { // 勞安科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_OHS).append(")");
} else if (UNIT_ID.startsWith("04")) { // 廣拆科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_ADS).append(")");
}
```

## Agent 3 分析結果：現場執行管理機制

### 執行權限管理
基於im40501_manHandlers.jsp分析：

#### 權限分級機制
```java
private boolean viewAllCases(String empno) {
    // 系統管理員和監督人員可查看所有案件
    int cnt = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "ibmuser", 
        "empno = '" + empno + "' AND ((role_id = 'sysManager' OR role_id_2 = 'sysManager' OR role_id_3 = 'sysManager') " +
        "OR (role_id = 'supervisor' OR role_id_2 = 'supervisor' OR role_id_3 = 'supervisor'))", CONNECTION_NAME)).intValue();
    return (cnt > 0);
}
```

#### 單位專業分工機制
```java
private String getIbmcodeSubSeq(String unitId) {
    if (unitId.startsWith("02")) {      // 拆除科
        return "01";
    } else if(unitId.startsWith("03")) { // 勞安科(下水道)  
        return "03";
    } else if (unitId.startsWith("04")) { // 廣拆科
        return "04";
    }
    return "!!"; // 無效單位
}
```

### 執行案件過濾機制
```java
// 待處理案件過濾：只顯示分派給該單位的案件
if ("WAIT".equals(s_caseStatus)) {
    sql.append(" AND dis_unit = '").append(UNIT_ID).append("'");
}
```

## Agent 4 分析結果：執行完成確認和驗收流程

### 正常執行完成流程
1. **現場執行完成**: 執行單位完成現場拆除作業
2. **狀態更新**: 將狀態更新為已簽准狀態（369/359/349）
3. **驗收確認**: 相關單位進行執行結果驗收
4. **文檔歸檔**: 執行過程文檔和證據歸檔

### 執行驗收標準
基於業務邏輯分析：
- **完全拆除**: 違建物完全移除，現場恢復原狀
- **部分拆除**: 按通知要求部分拆除完成
- **執行記錄**: 完整的執行過程記錄和照片證據
- **現場確認**: 執行單位現場確認簽章

## Agent 5 分析結果：異常處理和風險管理

### 未拆原因登錄機制
基於im40401_manHandlers.jsp分析：

#### 未拆原因狀態碼
```java
// 未拆原因已登錄狀態碼
// 拆除科: 368
// 廣告科: 348  
// 勞安科: 358

String ACC_RLT = "";
if ("A".equals(ib_prcs)) {        // 拆除科 A
    if("CHK_STATUE".equals(CHK_STATUE)){
        ACC_RLT = "36a";          // 特殊檢查狀態
    } else {
        ACC_RLT = "368";          // 未拆原因已登錄
    }
} else if ("C".equals(ib_prcs)) { // 勞安科 C
    ACC_RLT = "358";
} else if ("B".equals(ib_prcs)) { // 廣告科 B
    ACC_RLT = "348";
}
```

#### 異常處理流程
1. **執行受阻**: 現場執行遇到阻礙（如民眾抗議、安全問題）
2. **原因登錄**: 透過im40401未拆原因登錄作業記錄具體原因
3. **狀態變更**: 變更為未拆原因已登錄狀態（368/358/348）
4. **後續處理**: 根據未拆原因決定後續處理方式

### 風險管理機制
```java
// 資料庫交易管理
conn.setAutoCommit(false);
try {
    // 執行多個SQL命令
    for (String sqlCmd : sqlCmds) {
        pstmt = conn.prepareStatement(sqlCmd);
        pstmt.executeUpdate();
        pstmt.close();
    }
    conn.commit(); // 全部成功才提交
} catch (Exception e) {
    conn.rollback(); // 出錯時回滾
}
```

## Agent 6 分析結果：執行文檔和證據管理

### UAV無人機輔助執行（im30201系列）
基於im30201_manHandlers.jsp分析：

#### UAV年度管理
```java
String UAVYY = Utils.convertToString(DBTools.dLookUp(
    "string_agg(uavcode.code_seq, ',' ORDER BY uavcode.code_seq)", 
    "uavcode", "code_type= 'UAVYY'", "DBConn"));
```

#### 執行記錄管理
- **案件編號關聯**: 透過case_id關聯執行案件
- **認定號碼記錄**: 記錄reg_num認定通知號碼
- **UAV執行記錄**: 無人機執行過程和結果記錄
- **JSON資料格式**: 標準化的執行資料格式

### 執行證據保存機制
1. **現場照片**: 執行前中後的現場照片記錄
2. **執行影像**: UAV空拍影像和執行過程錄影
3. **執行報告**: 標準格式的執行作業報告
4. **簽章文件**: 相關人員的確認簽章文件

## 完整排拆執行作業SOP

### 第一階段：執行準備
1. **接收拆除通知**: 系統接收已簽准的拆除通知（349/359/369狀態前置）
2. **執行單位分派**: 根據案件性質分派給適當執行單位
3. **現場勘查**: 執行前現場環境和安全評估
4. **執行計畫**: 制定具體執行計畫和安全措施

### 第二階段：現場執行
1. **執行權限確認**: 確認執行人員權限和單位歸屬
2. **現場管制**: 建立現場管制區域和安全措施
3. **執行作業**: 按計畫進行拆除執行作業
4. **過程記錄**: 全程記錄執行過程和異常狀況

### 第三階段：執行確認
1. **執行結果確認**: 確認拆除作業是否按要求完成
2. **現場清理**: 清理執行現場和恢復環境
3. **狀態更新**: 更新系統狀態為執行完成（369/359/349）
4. **文檔歸檔**: 執行相關文檔和證據歸檔

### 異常處理流程
1. **異常識別**: 識別執行過程中的異常狀況
2. **原因分析**: 分析無法執行的具體原因
3. **原因登錄**: 透過im40401登錄未拆原因
4. **狀態變更**: 變更為未拆原因已登錄狀態（368/358/348）

## 現場管理和監督機制

### 執行監督架構
1. **分級監督**: 系統管理員 > 監督人員 > 執行人員
2. **單位分工**: 拆除科/勞安科/廣拆科專業分工
3. **權限控制**: 基於角色的執行權限管理
4. **進度追蹤**: 即時的執行進度監控

### 品質管控機制
1. **執行標準**: 明確的執行作業標準和規範
2. **過程監控**: 執行過程的即時監控和記錄
3. **結果驗收**: 執行結果的驗收和確認機制
4. **異常處理**: 標準化的異常狀況處理流程

## 關鍵業務發現

### 技術架構發現
1. **CodeCharge Studio架構**: 使用已停止維護的RAD工具生成
2. **三層分離模式**: JSP呈現層 + XML設定層 + Handlers業務邏輯層
3. **資料庫交易管理**: 完整的事務處理和錯誤回滾機制
4. **權限分級控制**: 基於角色和單位的多層次權限控制

### 業務流程發現
1. **多單位協作**: 拆除科/勞安科/廣拆科三個專業單位分工
2. **狀態驅動流程**: 嚴格的狀態碼驅動業務流程控制
3. **異常處理完備**: 完整的未拆原因登錄和處理機制
4. **證據管理系統**: UAV輔助的現代化證據收集和管理

### 系統改善建議
1. **技術現代化**: 從CodeCharge Studio遷移到現代Web框架
2. **行動化支援**: 開發行動APP支援現場執行作業
3. **自動化提升**: 增強UAV和IoT設備的自動化應用
4. **資料整合**: 強化與GIS和其他政府系統的資料整合

## 結論

新北市違章建築管理系統的排拆執行流程展現了完整的政府部門執法作業管理能力。雖然技術架構相對老舊，但業務邏輯設計完善，特別是在多單位協作、異常處理和證據管理方面都有成熟的機制。

系統通過嚴格的狀態碼控制確保執行流程的合規性，透過完整的權限管理保障執行作業的權威性，並透過UAV等現代技術提升執行效率和證據收集能力。

建議未來的系統現代化應保持現有業務邏輯的完整性，同時引入現代技術架構和行動化支援，以提升整體作業效率和用戶體驗。