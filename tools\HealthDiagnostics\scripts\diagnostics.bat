@echo off
REM Health Diagnostics Tool 啟動腳本 (Windows)
REM 新北市違章建築管理系統

setlocal enabledelayedexpansion

REM 設定變數
set SCRIPT_DIR=%~dp0
set APP_HOME=%SCRIPT_DIR%..
set LIB_DIR=%APP_HOME%\lib
set CONFIG_DIR=%APP_HOME%\config
set MAIN_CLASS=com.ntpc.violation.diagnostics.cli.DiagnosticsCLI

REM Java 設定
set JAVA_OPTS=-Xms256m -Xmx512m
set JAVA_OPTS=%JAVA_OPTS% -Dfile.encoding=UTF-8
set JAVA_OPTS=%JAVA_OPTS% -Duser.timezone=Asia/Taipei
set JAVA_OPTS=%JAVA_OPTS% -Djava.util.logging.config.file=%CONFIG_DIR%\logging.properties

REM 檢查 Java
if "%JAVA_HOME%"=="" (
    set JAVA_CMD=java
) else (
    set JAVA_CMD=%JAVA_HOME%\bin\java
)

REM 檢查 Java 版本
for /f tokens^=2-5^ delims^=.-_^" %%j in ('"%JAVA_CMD%" -version 2^>^&1') do set JAVA_VERSION=%%j

if %JAVA_VERSION% LSS 8 (
    echo 錯誤: 需要 Java 8 或以上版本，目前版本為 %JAVA_VERSION%
    exit /b 1
)

REM 建立 classpath
set CLASSPATH=%APP_HOME%\src
set CLASSPATH=%CLASSPATH%;%CONFIG_DIR%

REM 加入所有 JAR 檔
for %%i in ("%LIB_DIR%\*.jar") do (
    set CLASSPATH=!CLASSPATH!;%%i
)

REM 加入擴充 JAR 檔
if exist "%LIB_DIR%\extensions" (
    for %%i in ("%LIB_DIR%\extensions\*.jar") do (
        set CLASSPATH=!CLASSPATH!;%%i
    )
)

REM 建立報告目錄
set REPORTS_DIR=%APP_HOME%\reports
if not exist "%REPORTS_DIR%" mkdir "%REPORTS_DIR%"

REM 日誌目錄
set LOG_DIR=%APP_HOME%\logs
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM 設定日誌檔案
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do set DATE=%%c%%a%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set TIME=%%a%%b
set LOG_FILE=%LOG_DIR%\diagnostics_%DATE%_%TIME%.log

REM 執行診斷工具
echo 啟動 Health Diagnostics Tool...
echo Java 版本: %JAVA_VERSION%
echo 工作目錄: %APP_HOME%
echo 日誌檔案: %LOG_FILE%
echo.

REM 執行 Java 程式
"%JAVA_CMD%" %JAVA_OPTS% ^
    -classpath "%CLASSPATH%" ^
    -Dapp.home="%APP_HOME%" ^
    -Dlog.file="%LOG_FILE%" ^
    %MAIN_CLASS% %*

endlocal