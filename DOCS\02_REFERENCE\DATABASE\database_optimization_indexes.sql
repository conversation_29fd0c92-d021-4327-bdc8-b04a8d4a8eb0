-- ========================================================================
-- 新北市違章建築管理系統 - 資料庫效能優化索引腳本
-- ========================================================================
-- 
-- 目的：優化大型資料表的查詢效能
-- 資料規模：
-- - ibmcase: 419,854 rows (257MB)
-- - ibmfym: 1,045,355 rows (93MB) 
-- - ibmlist: 697,635 rows (99MB)
-- - ibmsts: 419,877 rows (22MB)
--
-- 執行方式：
-- PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f database_optimization_indexes.sql
-- ========================================================================

-- 開始事務
BEGIN;

-- 記錄開始時間
SELECT 'Starting database optimization at: ' || NOW();

-- ========================================================================
-- 1. ibmcase 表優化索引（案件主表）
-- ========================================================================

-- 1.1 狀態查詢優化 - 按狀態、處理類型、排序類型查詢
DROP INDEX IF EXISTS idx_ibmcase_status_process;
CREATE INDEX CONCURRENTLY idx_ibmcase_status_process 
ON ibmcase (status, ib_prcs, dis_type, dis_sort);

-- 1.2 日期範圍查詢優化 - 登記日期、審核日期
DROP INDEX IF EXISTS idx_ibmcase_dates;
CREATE INDEX CONCURRENTLY idx_ibmcase_dates 
ON ibmcase (reg_date, audnm_date, reg_rec_date);

-- 1.3 承辦人員查詢優化
DROP INDEX IF EXISTS idx_ibmcase_employees;
CREATE INDEX CONCURRENTLY idx_ibmcase_employees 
ON ibmcase (reg_emp, b_notice_emp, rsult_emp, dmltn_emp);

-- 1.4 地址查詢優化 - 複合索引
DROP INDEX IF EXISTS idx_ibmcase_address;
CREATE INDEX CONCURRENTLY idx_ibmcase_address 
ON ibmcase (dis_b_addzon, dis_b_add1, dis_b_add2, dis_b_add3);

-- 1.5 座標查詢優化 - GIS相關
DROP INDEX IF EXISTS idx_ibmcase_coordinates;
CREATE INDEX CONCURRENTLY idx_ibmcase_coordinates 
ON ibmcase (x_coordinate, y_coordinate);

-- 1.6 案件編號前綴查詢優化 - 改進現有索引
DROP INDEX IF EXISTS idx_ibmcase_case_prefix;
CREATE INDEX CONCURRENTLY idx_ibmcase_case_prefix 
ON ibmcase (substr(case_id, 1, 6));

-- 1.7 違建人查詢優化
DROP INDEX IF EXISTS idx_ibmcase_violator;
CREATE INDEX CONCURRENTLY idx_ibmcase_violator 
ON ibmcase (usr_id, ib_user);

-- 1.8 建物資訊查詢優化
DROP INDEX IF EXISTS idx_ibmcase_building;
CREATE INDEX CONCURRENTLY idx_ibmcase_building 
ON ibmcase (building_category, building_kind, blduse);

-- ========================================================================
-- 2. ibmfym 表優化索引（案件流程記錄）
-- ========================================================================

-- 2.1 案件流程查詢優化 - 最常使用的查詢模式
DROP INDEX IF EXISTS idx_ibmfym_case_flow;
CREATE INDEX CONCURRENTLY idx_ibmfym_case_flow 
ON ibmfym (case_id, acc_date, acc_time, acc_job);

-- 2.2 處理結果查詢優化
DROP INDEX IF EXISTS idx_ibmfym_results;
CREATE INDEX CONCURRENTLY idx_ibmfym_results 
ON ibmfym (acc_rlt, acc_rlt2, acc_date);

-- 2.3 承辦人員流程查詢
DROP INDEX IF EXISTS idx_ibmfym_employee_flow;
CREATE INDEX CONCURRENTLY idx_ibmfym_employee_flow 
ON ibmfym (b_notice_emp, dmltn_emp, rsult_emp);

-- 2.4 日期範圍查詢優化
DROP INDEX IF EXISTS idx_ibmfym_date_range;
CREATE INDEX CONCURRENTLY idx_ibmfym_date_range 
ON ibmfym (acc_date, b_notice_date, dis_notice_date, end_date);

-- 2.5 操作者查詢優化
DROP INDEX IF EXISTS idx_ibmfym_operators;
CREATE INDEX CONCURRENTLY idx_ibmfym_operators 
ON ibmfym (op_user, up_user, cr_date);

-- ========================================================================
-- 3. ibmlist 表優化索引（案件檔案管理）
-- ========================================================================

-- 3.1 案件檔案查詢優化 - 主要查詢模式
DROP INDEX IF EXISTS idx_ibmlist_case_files;
CREATE INDEX CONCURRENTLY idx_ibmlist_case_files 
ON ibmlist (case_id, pic_kind, pic_seq, cr_date);

-- 3.2 檔案類型查詢優化
DROP INDEX IF EXISTS idx_ibmlist_file_types;
CREATE INDEX CONCURRENTLY idx_ibmlist_file_types 
ON ibmlist (filekind, pic_kind, thombed);

-- 3.3 時間範圍查詢優化
DROP INDEX IF EXISTS idx_ibmlist_time_range;
CREATE INDEX CONCURRENTLY idx_ibmlist_time_range 
ON ibmlist (cr_date, op_date, start_date, end_date);

-- 3.4 操作者查詢優化
DROP INDEX IF EXISTS idx_ibmlist_users;
CREATE INDEX CONCURRENTLY idx_ibmlist_users 
ON ibmlist (cr_user, op_user, cr_date);

-- ========================================================================
-- 4. ibmsts 表優化索引（案件狀態記錄）
-- ========================================================================

-- 4.1 案件狀態查詢優化
DROP INDEX IF EXISTS idx_ibmsts_case_status;
CREATE INDEX CONCURRENTLY idx_ibmsts_case_status 
ON ibmsts (case_id, acc_rlt, acc_date);

-- 4.2 狀態時間查詢優化
DROP INDEX IF EXISTS idx_ibmsts_status_time;
CREATE INDEX CONCURRENTLY idx_ibmsts_status_time 
ON ibmsts (acc_date, acc_time, acc_job);

-- 4.3 處理結果統計查詢優化
DROP INDEX IF EXISTS idx_ibmsts_result_stats;
CREATE INDEX CONCURRENTLY idx_ibmsts_result_stats 
ON ibmsts (acc_rlt, acred_ts, demol_ts);

-- ========================================================================
-- 5. 交叉表查詢優化索引
-- ========================================================================

-- 5.1 案件與檔案關聯查詢優化
DROP INDEX IF EXISTS idx_ibmcase_list_join;
CREATE INDEX CONCURRENTLY idx_ibmcase_list_join 
ON ibmcase (case_id, status, reg_date);

-- 5.2 案件與流程關聯查詢優化
DROP INDEX IF EXISTS idx_ibmcase_fym_join;
CREATE INDEX CONCURRENTLY idx_ibmcase_fym_join 
ON ibmcase (case_id, finish_state, reg_date);

-- ========================================================================
-- 6. 全文檢索索引（使用 GIN 索引）
-- ========================================================================

-- 6.1 地址全文檢索
DROP INDEX IF EXISTS idx_ibmcase_address_gin;
CREATE INDEX CONCURRENTLY idx_ibmcase_address_gin 
ON ibmcase USING gin (to_tsvector('simple', 
    coalesce(dis_b_add_desc, '') || ' ' || 
    coalesce(caddress, '') || ' ' || 
    coalesce(anotheraddress, '')));

-- 6.2 違建人姓名全文檢索
DROP INDEX IF EXISTS idx_ibmcase_violator_gin;
CREATE INDEX CONCURRENTLY idx_ibmcase_violator_gin 
ON ibmcase USING gin (to_tsvector('simple', coalesce(ib_user, '')));

-- 6.3 備註內容全文檢索
DROP INDEX IF EXISTS idx_ibmfym_memo_gin;
CREATE INDEX CONCURRENTLY idx_ibmfym_memo_gin 
ON ibmfym USING gin (to_tsvector('simple', coalesce(acc_memo, '')));

-- ========================================================================
-- 7. 部分索引（Partial Index）- 針對常用條件
-- ========================================================================

-- 7.1 僅索引有效案件
DROP INDEX IF EXISTS idx_ibmcase_active;
CREATE INDEX CONCURRENTLY idx_ibmcase_active 
ON ibmcase (case_id, status, reg_date) 
WHERE status IS NOT NULL AND status != '';

-- 7.2 僅索引最近一年的案件
DROP INDEX IF EXISTS idx_ibmcase_recent;
CREATE INDEX CONCURRENTLY idx_ibmcase_recent 
ON ibmcase (case_id, reg_date, status) 
WHERE reg_date >= (EXTRACT(YEAR FROM NOW()) - 1) * 10000 + 101;

-- 7.3 僅索引有檔案的案件
DROP INDEX IF EXISTS idx_ibmlist_with_files;
CREATE INDEX CONCURRENTLY idx_ibmlist_with_files 
ON ibmlist (case_id, pic_kind, cr_date) 
WHERE filename IS NOT NULL AND filename != '';

-- ========================================================================
-- 8. 統計資訊更新
-- ========================================================================

-- 更新所有相關表的統計資訊
ANALYZE ibmcase;
ANALYZE ibmfym;
ANALYZE ibmlist;
ANALYZE ibmsts;
ANALYZE ibmcprp;

-- ========================================================================
-- 9. 索引使用情況查詢
-- ========================================================================

-- 查詢新建索引的使用情況（執行後可用於監控）
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
ORDER BY tablename, indexname;

-- 完成事務
COMMIT;

-- 記錄完成時間
SELECT 'Database optimization completed at: ' || NOW();

-- ========================================================================
-- 執行後建議
-- ========================================================================
-- 1. 執行 VACUUM ANALYZE 以更新統計資訊
-- 2. 監控索引使用情況，移除未使用的索引
-- 3. 根據實際查詢模式調整索引策略
-- 4. 定期重建索引以維持效能
-- ========================================================================