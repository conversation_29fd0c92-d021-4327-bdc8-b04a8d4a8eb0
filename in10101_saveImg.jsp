<%@page language="java" contentType="application/json; charset=UTF-8" pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>
<%@page import="com.codecharge.*,com.codecharge.util.*,com.codecharge.db.*"%>
<%@page import="java.io.*,java.net.*,java.nio.file.*"%>
<%@page import="org.apache.commons.io.FileUtils"%>
<%@page import="org.json.simple.JSONObject"%>
<%@page import="java.sql.*"%>
<%@page import="java.awt.*,java.awt.image.BufferedImage,javax.imageio.ImageIO"%>
<%@page import="javax.net.ssl.*,java.security.cert.X509Certificate"%>
<%@page import="java.net.URL,java.net.HttpURLConnection"%>

<%!
	/* ---------- 方法宣告 ---------- */
	// 移除不安全的 trustAllHosts() 方法
	// 現在使用系統預設的 SSL 憑證驗證機制
    /* ============================================================
       共用：統一輸出 JSON 物件
       ------------------------------------------------------------ */
    private void sendJson(javax.servlet.jsp.JspWriter out,
                          String result, String code, String message) throws IOException {
        JSONObject json = new JSONObject();
        json.put("result",  result);
        json.put("code",    code == null ? "" : code);
        json.put("message", message == null ? "" : message);
        out.print(json.toJSONString());
        out.flush();
    }

    /* ============================================================
       前置：imgUrl 連線與標頭檢測
       ------------------------------------------------------------ */
    private void verifyImageUrl(String urlStr) throws Exception {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("HEAD");
            conn.setConnectTimeout(8_000);
            conn.setReadTimeout(8_000);

            int status = conn.getResponseCode();
            if (status != HttpURLConnection.HTTP_OK) {
                throw new Exception("HTTP status = " + status);
            }

            String ctype = conn.getContentType();
            if (ctype == null || !ctype.toLowerCase().startsWith("image")) {
                throw new Exception("Content-Type = " + ctype);
            }

            int len = conn.getContentLength();
            if (len == 0) {
                throw new Exception("Content-Length = 0");
            }
        } finally {
            if (conn != null) conn.disconnect();
        }
    }

    /* ============================================================
       圖片處理：右下角加文字（含 MAP 遮罩）
       ------------------------------------------------------------ */
    public static void setImgText(File imgFile, String outFileName,
                                  String inText, String imgType) throws Exception {
        BufferedImage image = ImageIO.read(imgFile);
        int w = image.getWidth(), h = image.getHeight(),
            fontSize = 26, pad = 10;

        Graphics g = image.getGraphics();
        g.setFont(new Font("微軟正黑體", Font.PLAIN, fontSize));
        FontMetrics fm = g.getFontMetrics();

        int textW = fm.stringWidth(inText);
        int x = w - textW - pad, y = h - pad;

        /* 白底遮罩 */
        g.setColor(Color.white);
        if ("MAP".equals(imgType)) {
            g.fillRect(x + pad, y - 10, textW, 20);
        } else {
            g.fillRect(x, y - fontSize, textW, 60);
        }

        /* 文字 */
        g.setColor(Color.black);
        g.drawString(inText, x, y);
        g.dispose();

        ImageIO.write(image, "jpg", new File(outFileName));
    }
%>

<%
/* ================================================================
   主要流程
   ================================================================ */
try {
    /* ---------- 0. SSL 連線配置（使用系統信任庫） ---------- */
    // 不安全模式：信任所有憑證
    TrustManager[] trustAll = {
        new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() { return null; }
            public void checkClientTrusted(X509Certificate[] c,String a){}
            public void checkServerTrusted(X509Certificate[] c,String a){}
        }
    };
    SSLContext sc = SSLContext.getInstance("TLS");
    sc.init(null, trustAll, new java.security.SecureRandom());
    HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
        public boolean verify(String h, SSLSession s) { return true; }
    });
    
    /* ---------- 1. 讀取與驗證參數 ---------- */
    String EXP_NO   = request.getParameter("EXP_NO");
    String imgUrl   = request.getParameter("imgUrl");
    String Img_type = request.getParameter("Img_type");     // PIT / MAP
    String Xc       = request.getParameter("Xc");
    String Yc       = request.getParameter("Yc");
    String CR_USER  = (String) session.getAttribute("UserID");

    if (org.apache.commons.lang.StringUtils.isEmpty(EXP_NO)) {
        sendJson(out, "error", "PARAM_EXP_NO_MISSING", "EXP_NO 參數缺失"); return;
    }
    if (org.apache.commons.lang.StringUtils.isEmpty(imgUrl)) {
        sendJson(out, "error", "PARAM_IMGURL_MISSING", "imgUrl 參數缺失"); return;
    }
    if ("PIT".equals(Img_type) &&
       (org.apache.commons.lang.StringUtils.isEmpty(Xc) ||
        org.apache.commons.lang.StringUtils.isEmpty(Yc))) {
        sendJson(out, "error", "PARAM_COORD_MISSING", "PIT 類型需提供 Xc / Yc"); return;
    }

    /* ---------- 2. imgUrl 讀取測試 ---------- */
    try {
        verifyImageUrl(imgUrl);
    } catch (Exception e) {
        String msg = e.getMessage();
        if (msg != null && msg.contains("Content-Type")) {
            sendJson(out, "error", "IMGURL_INVALIDTYPE", "imgUrl Content-Type 非影像：" + msg); return;
        }
        if (msg != null && msg.contains("Content-Length")) {
            sendJson(out, "error", "IMGURL_ZEROSIZE", "imgUrl 回傳長度為 0"); return;
        }
        sendJson(out, "error", "IMGURL_UNREACHABLE", "imgUrl"+ imgUrl +" 無法讀取：" + msg); return;
    }

    /* ---------- 3. 路徑與目錄準備 ---------- */
    String REAL_PATH = request.getRealPath("/");
    String SEP       = System.getProperty("file.separator");
    String rootDir   = REAL_PATH.endsWith(SEP) ? REAL_PATH : REAL_PATH + SEP;

    /* 正式圖片目錄（PICTEMPPATH / EXP_NO 相關階層） */
    String picBaseDir = Utils.convertToString(
        DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn")
    );
    String picPath = picBaseDir + EXP_NO.substring(0, 3) + SEP +
                     EXP_NO + SEP + "GEO" + SEP;
    File picDir = new File(picPath);
    FileUtils.forceMkdir(picDir);

    /* 暫存目錄 */
    String tmpDirPath = rootDir + "img" + SEP + "in10101_imgUrlOutput";
    File tmpDir = new File(tmpDirPath);
    FileUtils.forceMkdir(tmpDir);

    /* ---------- 4. 下載圖片至暫存 ---------- */
    File tmpFile = new File(tmpDirPath + SEP + EXP_NO + ".jpg");
    try {
        FileUtils.copyURLToFile(new URL(imgUrl), tmpFile);
    } catch (IOException ex) {
        sendJson(out, "error", "DOWNLOAD_FAIL", "圖片下載失敗：" + ex.getMessage()); return;
    }

    JDBCConnection jdbc = null;
    try {
        /* ---------- 5. 圖片處理與資料庫寫入 ---------- */
        if ("PIT".equals(Img_type)) {                      /* 違建位置略圖 */
            String outFileName = EXP_NO + "_PIT.jpg";
            try {
                String text = "違建位置座標：" + Xc + "E, " + Yc + "N";
                setImgText(tmpFile, tmpFile.getAbsolutePath(), text, "");
            } catch (Exception ex) {
                sendJson(out, "error", "IMG_PROC_FAIL", "圖片處理失敗：" + ex.getMessage()); return;
            }

            try {
                Files.copy(tmpFile.toPath(),
                           Paths.get(picPath + outFileName),
                           StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException ex) {
                sendJson(out, "error", "FILE_COPY_FAIL", "圖片複製失敗：" + ex.getMessage()); return;
            }

            try {jdbc = JDBCConnectionFactory.getJDBCConnection("DBConn");
            } catch (Exception ex) {
                sendJson(out, "error", "DB_CONN_FAIL", "資料庫連線失敗：" + ex.getMessage()); return;
            }

            try {
                /* 更新 IBMCASE 經緯度 */
                String sql1 = "UPDATE IBMCASE SET X_COORDINATE=" + Xc +
                              ", Y_COORDINATE=" + Yc +
                              " WHERE CASE_ID='" + EXP_NO + "'";
                jdbc.executeUpdate(sql1);

                /* 新增／更新 IBMLIST */
                String picName = EXP_NO + "_違建位置略圖";
                String check = Utils.convertToString(
                    DBTools.dLookUp("FILENAME","IBMLIST",
                                    "SYSTID='IBM' AND CASE_ID='" + EXP_NO + "' AND PIC_KIND='GEO' AND PIC_SEQ=1",
                                    "DBConn"));

                if (org.apache.commons.lang.StringUtils.isEmpty(check)) {
                    String sql2 = "INSERT INTO IBMLIST (SYSTID,CASE_ID,PIC_KIND,PIC_SEQ," +
                                  "PICNAME,FILENAME,FILEKIND,CR_USER,CR_DATE) VALUES (" +
                                  "'IBM','" + EXP_NO + "','GEO',1,'" + picName + "','" +
                                  outFileName + "','jpg','" + CR_USER +
                                  "',TO_NUMBER(TO_CHAR(CURRENT_DATE,'yyyymmdd'),'99999999')-19110000)";
                    jdbc.executeUpdate(sql2);
                } else {
                    String sql3 = "UPDATE IBMLIST SET FILENAME='" + outFileName +
                                  "',FILEKIND='jpg',CR_USER='" + CR_USER +
                                  "' WHERE SYSTID='IBM' AND CASE_ID='" + EXP_NO +
                                  "' AND PIC_KIND='GEO' AND PIC_SEQ=1";
                    jdbc.executeUpdate(sql3);
                }
            } catch (Exception ex) {
                sendJson(out, "error", "SQL_EXEC_FAIL", "SQL 執行失敗：" + ex.getMessage()); return;
            }

        } else {                                           /* 平面示意圖 MAP */
            String outFileName = EXP_NO + "_MAP.jpg";
            try {
                setImgText(tmpFile, tmpFile.getAbsolutePath(), "                                          ", "MAP");
            } catch (Exception ex) {
                sendJson(out, "error", "IMG_PROC_FAIL", "圖片處理失敗：" + ex.getMessage()); return;
            }

            try {
                Files.copy(tmpFile.toPath(),
                           Paths.get(picPath + outFileName),
                           StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException ex) {
                sendJson(out, "error", "FILE_COPY_FAIL", "圖片複製失敗：" + ex.getMessage()); return;
            }

            try {jdbc = JDBCConnectionFactory.getJDBCConnection("DBConn");
            } catch (Exception ex) {
                sendJson(out, "error", "DB_CONN_FAIL", "資料庫連線失敗：" + ex.getMessage()); return;
            }

            try {
                String picName = EXP_NO + "_平面示意圖";
                String check = Utils.convertToString(
                    DBTools.dLookUp("FILENAME","IBMLIST",
                                    "SYSTID='IBM' AND CASE_ID='" + EXP_NO + "' AND PIC_KIND='GEO' AND PIC_SEQ=2",
                                    "DBConn"));

                if (org.apache.commons.lang.StringUtils.isEmpty(check)) {
                    String sql2 = "INSERT INTO IBMLIST (SYSTID,CASE_ID,PIC_KIND,PIC_SEQ," +
                                  "PICNAME,FILENAME,FILEKIND,CR_USER,CR_DATE) VALUES (" +
                                  "'IBM','" + EXP_NO + "','GEO',2,'" + picName + "','" +
                                  outFileName + "','jpg','" + CR_USER +
                                  "',TO_NUMBER(TO_CHAR(CURRENT_DATE,'yyyymmdd'),'99999999')-19110000)";
                    jdbc.executeUpdate(sql2);
                } else {
                    String sql3 = "UPDATE IBMLIST SET FILENAME='" + outFileName +
                                  "',FILEKIND='jpg',CR_USER='" + CR_USER +
                                  "' WHERE SYSTID='IBM' AND CASE_ID='" + EXP_NO +
                                  "' AND PIC_KIND='GEO' AND PIC_SEQ=2";
                    jdbc.executeUpdate(sql3);
                }
            } catch (Exception ex) {
                sendJson(out, "error", "SQL_EXEC_FAIL", "SQL 執行失敗：" + ex.getMessage()); return;
            }
        }

    } finally {
        if (jdbc != null) jdbc.closeConnection();
        if (tmpFile.exists()) tmpFile.delete();            // 清理暫存
    }

    /* ---------- 6. 成功結束 ---------- */
    sendJson(out, "success", "", "");

} catch (Exception ex) {
    /* 最終保險：未知例外 */
    sendJson(out, "error", "UNKNOWN_ERROR", ex.getMessage());
}
%>
