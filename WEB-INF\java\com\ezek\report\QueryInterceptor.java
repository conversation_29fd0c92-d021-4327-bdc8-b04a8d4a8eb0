package com.ezek.report;

import java.lang.reflect.Method;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.sql.*;
import java.util.*;
import com.codecharge.db.JDBCConnection;
import com.codecharge.db.DBConnectionManager;
import com.codecharge.util.DBTools;

/**
 * 查詢攔截器
 * 
 * 透過 Java Dynamic Proxy 實現非侵入式的查詢監控
 * 攔截 CodeCharge Studio 框架中的關鍵資料庫操作方法
 * 
 * 支援攔截的方法：
 * 1. JDBCConnection.getRows()
 * 2. DBTools.dLookUp()
 * 3. Connection.prepareStatement()
 * 4. PreparedStatement.executeQuery()
 * 5. PreparedStatement.executeUpdate()
 */
public class QueryInterceptor {
    
    private static final ThreadLocal<QueryContext> contextThreadLocal = new ThreadLocal<>();
    private static boolean interceptorEnabled = false;
    
    /**
     * 查詢上下文
     */
    public static class QueryContext {
        private final List<QueryExecution> executions = new ArrayList<>();
        private final long startTime = System.currentTimeMillis();
        private String currentOperation;
        private int depth = 0;
        
        public void recordExecution(String sql, long executionTime, String method) {
            executions.add(new QueryExecution(sql, executionTime, method, System.currentTimeMillis()));
        }
        
        public List<QueryExecution> getExecutions() { return executions; }
        public void setCurrentOperation(String operation) { this.currentOperation = operation; }
        public String getCurrentOperation() { return currentOperation; }
        public void incrementDepth() { depth++; }
        public void decrementDepth() { depth--; }
        public int getDepth() { return depth; }
    }
    
    /**
     * 查詢執行記錄
     */
    public static class QueryExecution {
        public final String sql;
        public final long executionTime;
        public final String method;
        public final long timestamp;
        
        public QueryExecution(String sql, long executionTime, String method, long timestamp) {
            this.sql = sql;
            this.executionTime = executionTime;
            this.method = method;
            this.timestamp = timestamp;
        }
    }
    
    // === JDBCConnection 攔截器 ===
    
    /**
     * JDBCConnection 動態代理處理器
     */
    public static class JDBCConnectionHandler implements InvocationHandler {
        private final JDBCConnection target;
        
        public JDBCConnectionHandler(JDBCConnection target) {
            this.target = target;
        }
        
        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            
            if ("getRows".equals(methodName) && interceptorEnabled) {
                return interceptGetRows(method, args);
            }
            
            // 其他方法直接委派給原始物件
            return method.invoke(target, args);
        }
        
        private Object interceptGetRows(Method method, Object[] args) throws Throwable {
            String sql = (args != null && args.length > 0) ? args[0].toString() : "UNKNOWN";
            String callerMethod = getCallerMethod();
            
            long startTime = System.currentTimeMillis();
            
            try {
                // 記錄查詢開始
                QueryContext context = getQueryContext();
                context.incrementDepth();
                context.setCurrentOperation("getRows");
                
                // 執行原始方法
                Object result = method.invoke(target, args);
                
                // 記錄執行時間和結果
                long executionTime = System.currentTimeMillis() - startTime;
                context.recordExecution(sql, executionTime, callerMethod);
                
                // 通知 N+1 檢測器
                NPlusOneQueryDetector.recordQueryExecution(sql, executionTime, callerMethod);
                
                return result;
                
            } finally {
                QueryContext context = getQueryContext();
                context.decrementDepth();
                
                // 如果是最外層調用，檢查是否有 N+1 模式
                if (context.getDepth() == 0) {
                    checkForNPlusOnePattern(context);
                }
            }
        }
    }
    
    // === DBTools 攔截器 ===
    
    /**
     * DBTools.dLookUp 方法攔截器
     * 由於 dLookUp 是靜態方法，使用 AspectJ 或字節碼操作會更適合
     * 這裡提供一個包裝器方法的範例
     */
    public static class DBToolsWrapper {
        
        /**
         * 包裝的 dLookUp 方法
         */
        public static String dLookUp(String fields, String table, String condition, String connectionName) {
            if (!interceptorEnabled) {
                return DBTools.dLookUp(fields, table, condition, connectionName);
            }
            
            String callerMethod = getCallerMethod();
            long startTime = System.currentTimeMillis();
            
            try {
                // 記錄查詢開始
                QueryContext context = getQueryContext();
                context.incrementDepth();
                context.setCurrentOperation("dLookUp");
                
                // 執行原始方法
                String result = DBTools.dLookUp(fields, table, condition, connectionName);
                
                // 記錄執行時間
                long executionTime = System.currentTimeMillis() - startTime;
                context.recordExecution(
                    String.format("SELECT %s FROM %s WHERE %s", fields, table, condition),
                    executionTime, 
                    callerMethod
                );
                
                // 通知 N+1 檢測器
                NPlusOneQueryDetector.recordLookupExecution(table, condition, executionTime, callerMethod);
                
                return result;
                
            } finally {
                QueryContext context = getQueryContext();
                context.decrementDepth();
                
                if (context.getDepth() == 0) {
                    checkForNPlusOnePattern(context);
                }
            }
        }
    }
    
    // === Connection 攔截器 ===
    
    /**
     * Connection 動態代理處理器
     */
    public static class ConnectionHandler implements InvocationHandler {
        private final Connection target;
        
        public ConnectionHandler(Connection target) {
            this.target = target;
        }
        
        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            
            if ("prepareStatement".equals(methodName) && interceptorEnabled) {
                return interceptPrepareStatement(method, args);
            }
            
            return method.invoke(target, args);
        }
        
        private Object interceptPrepareStatement(Method method, Object[] args) throws Throwable {
            String sql = (args != null && args.length > 0) ? args[0].toString() : "UNKNOWN";
            
            // 創建 PreparedStatement 的代理
            PreparedStatement originalPS = (PreparedStatement) method.invoke(target, args);
            return Proxy.newProxyInstance(
                originalPS.getClass().getClassLoader(),
                new Class[]{PreparedStatement.class},
                new PreparedStatementHandler(originalPS, sql)
            );
        }
    }
    
    /**
     * PreparedStatement 動態代理處理器
     */
    public static class PreparedStatementHandler implements InvocationHandler {
        private final PreparedStatement target;
        private final String sql;
        
        public PreparedStatementHandler(PreparedStatement target, String sql) {
            this.target = target;
            this.sql = sql;
        }
        
        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            String methodName = method.getName();
            
            if (("executeQuery".equals(methodName) || "executeUpdate".equals(methodName)) && interceptorEnabled) {
                return interceptExecute(method, args, methodName);
            }
            
            return method.invoke(target, args);
        }
        
        private Object interceptExecute(Method method, Object[] args, String executeType) throws Throwable {
            String callerMethod = getCallerMethod();
            long startTime = System.currentTimeMillis();
            
            try {
                QueryContext context = getQueryContext();
                context.incrementDepth();
                context.setCurrentOperation(executeType);
                
                Object result = method.invoke(target, args);
                
                long executionTime = System.currentTimeMillis() - startTime;
                context.recordExecution(sql, executionTime, callerMethod);
                
                NPlusOneQueryDetector.recordQueryExecution(sql, executionTime, callerMethod);
                
                return result;
                
            } finally {
                QueryContext context = getQueryContext();
                context.decrementDepth();
                
                if (context.getDepth() == 0) {
                    checkForNPlusOnePattern(context);
                }
            }
        }
    }
    
    // === 輔助方法 ===
    
    /**
     * 取得查詢上下文
     */
    private static QueryContext getQueryContext() {
        QueryContext context = contextThreadLocal.get();
        if (context == null) {
            context = new QueryContext();
            contextThreadLocal.set(context);
        }
        return context;
    }
    
    /**
     * 取得調用者方法名稱
     */
    private static String getCallerMethod() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        for (int i = 2; i < stackTrace.length; i++) {
            String className = stackTrace[i].getClassName();
            String methodName = stackTrace[i].getMethodName();
            
            // 跳過攔截器本身的方法
            if (className.contains("QueryInterceptor") || 
                className.contains("$Proxy") ||
                className.startsWith("java.lang.reflect")) {
                continue;
            }
            
            // 找到應用程式的方法
            if (className.startsWith("com.ezek") || className.contains("Handler")) {
                return className + "." + methodName + ":" + stackTrace[i].getLineNumber();
            }
        }
        
        return "UNKNOWN_CALLER";
    }
    
    /**
     * 檢查 N+1 模式
     */
    private static void checkForNPlusOnePattern(QueryContext context) {
        List<QueryExecution> executions = context.getExecutions();
        if (executions.size() < 3) return;
        
        // 分析查詢模式
        Map<String, List<QueryExecution>> queryGroups = groupSimilarQueries(executions);
        
        for (Map.Entry<String, List<QueryExecution>> entry : queryGroups.entrySet()) {
            List<QueryExecution> group = entry.getValue();
            if (group.size() >= NPlusOneQueryDetector.N_PLUS_ONE_THRESHOLD) {
                // 檢查執行時間間隔
                if (isRapidExecution(group)) {
                    System.err.println(String.format(
                        "Potential N+1 Pattern: %d similar queries executed rapidly\n" +
                        "Query pattern: %s\n" +
                        "Methods: %s",
                        group.size(),
                        entry.getKey(),
                        group.stream().map(e -> e.method).distinct().toString()
                    ));
                }
            }
        }
        
        // 清除當前上下文
        contextThreadLocal.remove();
    }
    
    /**
     * 將相似的查詢分組
     */
    private static Map<String, List<QueryExecution>> groupSimilarQueries(List<QueryExecution> executions) {
        Map<String, List<QueryExecution>> groups = new HashMap<>();
        
        for (QueryExecution execution : executions) {
            String normalizedSql = normalizeQuery(execution.sql);
            groups.computeIfAbsent(normalizedSql, k -> new ArrayList<>()).add(execution);
        }
        
        return groups;
    }
    
    /**
     * 檢查是否為快速連續執行
     */
    private static boolean isRapidExecution(List<QueryExecution> executions) {
        if (executions.size() < 2) return false;
        
        long firstTime = executions.get(0).timestamp;
        long lastTime = executions.get(executions.size() - 1).timestamp;
        
        // 如果在2秒內執行多次相同查詢，視為快速執行
        return (lastTime - firstTime) < 2000;
    }
    
    /**
     * 正規化查詢字串
     */
    private static String normalizeQuery(String sql) {
        if (sql == null) return "";
        
        return sql.replaceAll("'[^']*'", "'?'")
                  .replaceAll("\\b\\d+\\b", "?")
                  .replaceAll("\\s+", " ")
                  .trim()
                  .toUpperCase();
    }
    
    // === 公開 API ===
    
    /**
     * 啟用攔截器
     */
    public static void enableInterceptor() {
        interceptorEnabled = true;
        System.out.println("Query Interceptor enabled");
    }
    
    /**
     * 停用攔截器
     */
    public static void disableInterceptor() {
        interceptorEnabled = false;
        contextThreadLocal.remove();
        System.out.println("Query Interceptor disabled");
    }
    
    /**
     * 創建 JDBCConnection 的代理
     */
    public static JDBCConnection createJDBCConnectionProxy(JDBCConnection original) {
        return (JDBCConnection) Proxy.newProxyInstance(
            original.getClass().getClassLoader(),
            new Class[]{JDBCConnection.class},
            new JDBCConnectionHandler(original)
        );
    }
    
    /**
     * 創建 Connection 的代理
     */
    public static Connection createConnectionProxy(Connection original) {
        return (Connection) Proxy.newProxyInstance(
            original.getClass().getClassLoader(),
            original.getClass().getInterfaces(),
            new ConnectionHandler(original)
        );
    }
    
    /**
     * 取得當前查詢統計
     */
    public static QueryContext getCurrentContext() {
        return contextThreadLocal.get();
    }
    
    /**
     * 清除當前上下文
     */
    public static void clearCurrentContext() {
        contextThreadLocal.remove();
    }
}