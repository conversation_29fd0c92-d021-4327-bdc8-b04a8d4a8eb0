﻿/* 隱藏tag */
[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak], .ng-cloak, .x-ng-cloak {
  display: none !important;
}
/* 登入頁 */
.header {
    display: table;
    position: relative;
    width: 98%;
    height: 100%;
    background: url(../img/loginBackground/loginBackground_1.jpg?1090613) no-repeat center center;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    background-size: cover;
    -o-background-size: cover;
    transition: background 1s;
}



.login_container {
  display: table-cell;
  vertical-align: middle; 
  margin-top: -173px; margin-left: -175px; 
  position: absolute; top: 50%; left: 50%;
  width: 350px;
  height: 410px; border-radius: 10px; background-color: rgba(221, 221, 221, 0.6);
}
/*vertical-container 高度要同 login_container*/
.vertical-container {
  height: 410px;
  display: -webkit-flex;
  display:         flex;
  -webkit-align-items: center;
          align-items: center;
  -webkit-justify-content: center;
          justify-content: center;
}
.login_wrap {
  width: 310px;
  /*height: 150px;*/
  margin-left: auto;
  margin-right: auto;
  text-align:left;
}

.loginFooter {
	font-style: inherit !important;
	text-align: center;
	font-size: 14px;
    margin-top: 0px;
    margin-bottom: 0px;
    font-family: Arial, '黑體-繁', 'Heiti TC', '微軟正黑體', 'Microsoft JhengHei', sans-serif;
}

.btn-primary-login {
	background: rgba(0,0,0,0.4); 
	background-color: #428bca;
	border-color: #ffffff;
	color: #ffffff;
}

.btn-primary-login:hover {
	background-color: #2a6496;
	color: #ffffff;
}

.input-group-addon{
	background: rgba(0,0,0,0.4); 
	color: #FFF; 
	border: 0px; 
	padding: 7px 12px 5px; 
	min-width: 40px;
}

.form-control, input, select, select[multiple], textarea{
	background: rgba(255,255,255,0.5); 
	border-color: transparent; 
	color: #000;
}

.form-control:focus,
.single-line:focus {
	border-color: #2a6496 !important;
}

.form-control::-moz-placeholder {
	color: #666;
	opacity: 1;
}
.form-control:-ms-input-placeholder {
	color: #666;
}
.form-control::-webkit-input-placeholder {
	color: #666;
}

h2.title {
	color:  #333;
	margin-top:10px;
	margin-bottom:10px;
	font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif !important;
	font-weight:500;
	font-size:30px;
}

h3.title {
	color:  #474747;
	font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif !important;
	font-size: 16px;
    font-style: inherit;
}

/* 其它頁 */
/* pageBackground */
.pageBackground{
		/*display: table;*/
		position: relative;
		width: 100%;
		height: 100%;
		background: url(../img/pageBackground.jpg) no-repeat center center;
		background-color:#b2f3ff;
		-webkit-background-size: cover;
		-moz-background-size: cover;
		background-size: cover;
		-o-background-size: cover;
	}
	
.timeText{
		 font-size: 14px;
		 margin-bottom:2px;
	}
	
.waterText{
		 color:#0065ff;
	}

.box-icon {
  background: #36a9e1;
  float: right;
}

.box-icon i {
  display: inline-block;
  color: white;
  text-align: center;
  width: 36px;
  padding: 10px 0;
  -webkit-transition: all .1s ease-in-out;
  -moz-transition: all .1s ease-in-out;
  -ms-transition: all .1s ease-in-out;
  -o-transition: all .1s ease-in-out;
  transition: all .1s ease-in-out;
  opacity: .8;
  filter: alpha(opacity=80);
  -ms-filter: "alpha(opacity=80)";
  border: 0px solid #fff;
  text-decoration: none;
}

.ibox-title h5 {
	font-size: 20px;
	float: left;
	padding: 8px 0;
	margin: 0 0 0 20px;
}
