<%@page pageEncoding="utf-8"%><%@page import="com.ezek.utils.EzekUtils"%>

<%--== Handlers ==--%> <%--im10101_lis Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

//Feature checker Head @1-AFC6B4D5
    public class im10101_lisServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//im10101_lis Page Handler Head @1-30F4A223
    public class im10101_lisPageHandler implements PageListener {
//End im10101_lis Page Handler Head

//im10101_lis BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End im10101_lis BeforeInitialize Method Head

//im10101_lis BeforeInitialize Method Tail @1-FCB6E20C
        }
//End im10101_lis BeforeInitialize Method Tail

//im10101_lis AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End im10101_lis AfterInitialize Method Head

//Event AfterInitialize Action Custom Code @37-44795B7A


			//中文
            String currentProgramId = "im10101";
            String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID"));          
            if (!StringUtils.isEmpty(PROGRAM_ID) && !PROGRAM_ID.equals(currentProgramId)) {
	            for (int i = 1; i <= 15; i++) SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + String.valueOf(i), "");
            }
            SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PROGRAM_ID", currentProgramId);

//End Event AfterInitialize Action Custom Code

//Event AfterInitialize Action Validate onTimeout_Synct @71-4B8A4259
          if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
//End Event AfterInitialize Action Validate onTimeout_Synct

//im10101_lis AfterInitialize Method Tail @1-FCB6E20C
        }
//End im10101_lis AfterInitialize Method Tail

//im10101_lis OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End im10101_lis OnInitializeView Method Head

//im10101_lis OnInitializeView Method Tail @1-FCB6E20C
        }
//End im10101_lis OnInitializeView Method Tail

//im10101_lis BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End im10101_lis BeforeShow Method Head

//im10101_lis BeforeShow Method Tail @1-FCB6E20C
        }
//End im10101_lis BeforeShow Method Tail

//im10101_lis BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End im10101_lis BeforeOutput Method Head

//im10101_lis BeforeOutput Method Tail @1-FCB6E20C
        }
//End im10101_lis BeforeOutput Method Tail

//im10101_lis BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End im10101_lis BeforeUnload Method Head

//im10101_lis BeforeUnload Method Tail @1-FCB6E20C
        }
//End im10101_lis BeforeUnload Method Tail

//im10101_lis onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End im10101_lis onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//im10101_lis onCache Method Tail @1-FCB6E20C
        }
//End im10101_lis onCache Method Tail

//im10101_lis Page Handler Tail @1-FCB6E20C
    }
//End im10101_lis Page Handler Tail

//BMSDISOBEY_DIST Grid Handler Head @2-8C8836A9
    public class im10101_lisBMSDISOBEY_DISTGridHandler implements GridListener, GridDataObjectListener {
//End BMSDISOBEY_DIST Grid Handler Head

//BMSDISOBEY_DIST afterInitialize Method Head @2-89E84600
        public void afterInitialize(Event e) {
//End BMSDISOBEY_DIST afterInitialize Method Head

//BMSDISOBEY_DIST afterInitialize Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST afterInitialize Method Tail

//BMSDISOBEY_DIST BeforeShow Method Head @2-46046458
        public void beforeShow(Event e) {
//End BMSDISOBEY_DIST BeforeShow Method Head

//Event BeforeShow Action Custom Code @131-44795B7A
	
	String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
	String UNIT_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));
	
	String ROLE_ID = Utils.convertToString(DBTools.dLookUp("ROLE_ID", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
	String ROLE_ID_2 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_2", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
	String ROLE_ID_3 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_3", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));		
	
	//判定角色若為系統管理者或隊部長官
	if("sysManager".equals(ROLE_ID) || "sysManager".equals(ROLE_ID_2) || "sysManager".equals(ROLE_ID_3) || "supervisor".equals(ROLE_ID) || "supervisor".equals(ROLE_ID_2) || "supervisor".equals(ROLE_ID_3)){
		e.getGrid().getControl("LinkA_insert").setVisible(false);
		e.getGrid().getControl("LinkB_insert").setVisible(true);
		e.getGrid().getControl("LinkD_insert").setVisible(false);
	}
	else{
		//認定一科
		if("011".equals(UNIT_ID)){
			e.getGrid().getControl("LinkB_insert").setVisible(false);
			e.getGrid().getControl("LinkD_insert").setVisible(false);
		}
		//認定二科
		else if("012".equals(UNIT_ID)){
			e.getGrid().getControl("LinkB_insert").setVisible(false);
			e.getGrid().getControl("LinkD_insert").setVisible(false);
		}
		//廣拆科
		else if("041".equals(UNIT_ID)){
			e.getGrid().getControl("LinkA_insert").setVisible(false);
			e.getGrid().getControl("LinkD_insert").setVisible(false);
		}
		//勞安科
		else if("031".equals(UNIT_ID)){
			e.getGrid().getControl("LinkA_insert").setVisible(false);
			e.getGrid().getControl("LinkB_insert").setVisible(false);
		}
		else{
			e.getGrid().getControl("LinkA_insert").setVisible(false);
			e.getGrid().getControl("LinkB_insert").setVisible(false);
			e.getGrid().getControl("LinkD_insert").setVisible(false);
		}
	}
	
//End Event BeforeShow Action Custom Code

//BMSDISOBEY_DIST BeforeShow Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeShow Method Tail

//BMSDISOBEY_DIST BeforeShowRow Method Head @2-BDFD38FC
        public void beforeShowRow(Event e) {
//End BMSDISOBEY_DIST BeforeShowRow Method Head

//Event BeforeShowRow Action Custom Code @28-44795B7A
	
	ezekTool ezTool = new ezekTool();
	String CASE_ID = Utils.convertToString(e.getGrid().getControl("CASE_ID").getValue());
	String AUDNM_DATE = Utils.convertToString(e.getGrid().getControl("AUDNM_DATE").getValue());
	String reg_yy = Utils.convertToString(e.getGrid().getControl("reg_yy").getValue());
	String reg_no = Utils.convertToString(e.getGrid().getControl("reg_no").getValue());
	
	String ib_prcs = Utils.convertToString(DBTools.dLookUp("IB_PRCS", " IBMCASE", " CASE_ID = '"+CASE_ID+"' ", "DBConn"));
	
	String emptyAudnmDate = "";
	
	//-------
	//show 查報日期
	//------
	if( !StringUtils.isEmpty(AUDNM_DATE)){
		emptyAudnmDate = ezTool.formateDate(AUDNM_DATE);
	}
	e.getGrid().getControl("AUDNM_DATE").setValue(emptyAudnmDate);
	
	//-------
	//show 案件狀態
	//------
	// 認定協同 
	// 認定科         234
	// 勞安科(下水道) 254
	// 廣告科         244
	// 陳核 232,252
	// 認定科         232
	// 勞安科(下水道) 252
	// 廣告科         none
	// 2021/10/07 Add 廣告科 344
	// 110/10/11  Yao 除了 紅綠 其它都黑
	String UNEDITABLE_ACC_RLT = "234,244,254,232,252";
	String ACC_RLT =  Utils.convertToString(e.getGrid().getControl("ACC_RLT").getValue());
	String ACC_RLT_NAME = "", ACC_RLT_HTML = "";
	
	if (!StringUtils.isEmpty(ACC_RLT)) {
		ACC_RLT_NAME = Utils.convertToString(DBTools.dLookUp("CODE_DESC", " IBMCODE", " CODE_SEQ = '"+ACC_RLT+"' and CODE_TYPE = 'RLT'", "DBConn"));
	
		if("23b".equals(ACC_RLT) || "24b".equals(ACC_RLT) || "25b".equals(ACC_RLT)){
			ACC_RLT_HTML = "<label style= 'color:green;'>"+ACC_RLT_NAME+"</label>";	
		}
		else if("36a".equals(ACC_RLT) || "34a".equals(ACC_RLT) || "35a".equals(ACC_RLT) || "237".equals(ACC_RLT) || "247".equals(ACC_RLT) || "257".equals(ACC_RLT)){
			ACC_RLT_HTML = "<label style= 'color:red;'>"+ACC_RLT_NAME+"</label>";	
		}else{
			ACC_RLT_HTML = "<label>"+ACC_RLT_NAME+"</label>";
		}
		e.getGrid().getControl("view_acc_rlt").setValue(ACC_RLT_HTML);
		e.getGrid().getControl("acc_rlt_name").setValue(ACC_RLT_NAME);
	}
	
	//-------
	//認定作業種類依IB_PRCS判斷
	//------
	if("A".equals(ib_prcs)){
		e.getGrid().getControl("LinkB").setVisible(false);
		e.getGrid().getControl("LinkD").setVisible(false);
	}
	else if("B".equals(ib_prcs)){
		e.getGrid().getControl("LinkA").setVisible(false);
		e.getGrid().getControl("LinkD").setVisible(false);
	}
	else if("C".equals(ib_prcs)){
		e.getGrid().getControl("LinkA").setVisible(false);
		e.getGrid().getControl("LinkB").setVisible(false);
	}
	else{
		e.getGrid().getControl("LinkA").setVisible(false);
		e.getGrid().getControl("LinkB").setVisible(false);
		e.getGrid().getControl("LinkD").setVisible(false);
	}
	
	// 認定協同
	// 認定科         234
	// 勞安科(下水道) 254
	// 廣告科         244
	// 陳核 232,252
	// 認定科         232
	// 勞安科(下水道) 252
	// 廣告科         none
	// Not allow to edit when status is 認定協同 or 陳核
	if (UNEDITABLE_ACC_RLT.indexOf(ACC_RLT) > -1) {
		e.getGrid().getControl("LinkA").setVisible(false);
		e.getGrid().getControl("LinkB").setVisible(false);
		e.getGrid().getControl("LinkD").setVisible(false);
	}
	
	//-------
	//組合認定號碼
	//------
	if (!StringUtils.isEmpty(reg_yy) && !StringUtils.isEmpty(reg_no)) {
		e.getGrid().getControl("reg_num").setValue(reg_yy  + reg_no);
	}

	// 判斷是否可抽回和抽回刪除
	String opened = Utils.convertToString(DBTools.dLookUp("case_id", " caseopened", " CASE_ID = '"+CASE_ID+"' ", "DBConn"));
	String status = Utils.convertToString(DBTools.dLookUp("STATUS", " IBMCASE", " CASE_ID = '"+CASE_ID+"' ", "DBConn"));

	// 協同中的案件，隱藏所有操作按鈕
	if (UNEDITABLE_ACC_RLT.indexOf(ACC_RLT) > -1) {
		e.getGrid().getControl("LinkW").setVisible(false);
		try { e.getGrid().getControl("LinkWithdrawDelete").setVisible(false); } catch (Exception ex) {}
	}
	else {
		e.getGrid().getControl("LinkW").setVisible(false);
		try { e.getGrid().getControl("LinkWithdrawDelete").setVisible(false); } catch (Exception ex) {}
	}
//End Event BeforeShowRow Action Custom Code

//BMSDISOBEY_DIST BeforeShowRow Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeShowRow Method Tail

//BMSDISOBEY_DIST BeforeSelect Method Head @2-E5EC9AD3
        public void beforeSelect(Event e) {
//End BMSDISOBEY_DIST BeforeSelect Method Head

//BMSDISOBEY_DIST BeforeSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeSelect Method Tail

//BMSDISOBEY_DIST BeforeBuildSelect Method Head @2-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeBuildSelect Method Head

//Event BeforeBuildSelect Action Custom Code @29-44795B7A
	

			String newWhereStr = "", oldWhereStr = e.getCommand().getWhere();	
			String session_1 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_1"));
			String session_2 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_2"));
			String session_3 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_3"));					
			String session_4 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_4"));
			String session_5 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_5"));
			String session_6 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_6"));
			String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
			String UNIT_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));
			
			String ROLE_ID = Utils.convertToString(DBTools.dLookUp("ROLE_ID", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
			String ROLE_ID_2 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_2", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
			String ROLE_ID_3 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_3", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));					
		
			newWhereStr = " " ;
			
			// 認定協同
			// 認定科         234
			// 勞安科(下水道) 254
			// 廣告科         244
			// 陳核 232,252
			// 認定科         232
			// 勞安科(下水道) 252
			// 廣告科         none
			// 2021/10/07 Add 廣告科 344
			// 2021/10/11 Yao 增加拆除退回認定 32a 
			
			String unit01 = "'231','23b','237','234','232','32a' "; // 認定科 
			String unit04 = "'241','24b','347','244','344' "; // 廣告科
			String unit03 = "'251','25b','257','254','252' "; // 勞安科
			
			
			
			//判定角色若為系統管理者或隊部長官
			if("sysManager".equals(ROLE_ID) || "sysManager".equals(ROLE_ID_2) || "sysManager".equals(ROLE_ID_3) || "supervisor".equals(ROLE_ID) || "supervisor".equals(ROLE_ID_2) || "supervisor".equals(ROLE_ID_3)){
				newWhereStr += " ibmsts.acc_rlt in("+unit01+", "+unit04+", "+unit03+")";
			}
			else{
				//認定一科
				if("011".equals(UNIT_ID) || "012".equals(UNIT_ID)){
					newWhereStr += " ibmsts.acc_rlt in("+unit01+") and ibmcase.REG_EMP = '"+UserID+"' ";
				}
				//廣拆科
				else if("041".equals(UNIT_ID)){
					newWhereStr += " ibmsts.acc_rlt in("+unit04+") and ibmcase.REG_EMP = '"+UserID+"' ";
				}
				//勞安科
				else if("031".equals(UNIT_ID)){
					newWhereStr += " ibmsts.acc_rlt in("+unit03+") and ibmcase.REG_EMP = '"+UserID+"' ";
				}
				else{
					newWhereStr += " 1 = 2";
				}
			}
			
			//銷案
			if (!StringUtils.isEmpty(newWhereStr))
			{
				newWhereStr+=" and ";
			}
			newWhereStr += "is_closed is null";

			if (!StringUtils.isEmpty(session_1) && !StringUtils.isEmpty(session_2)) {	
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";	
				newWhereStr += "'"+ session_1 +"' <= RVLDATE   and  RVLDATE <= '"+ session_2 +"' ";
			}else if( !StringUtils.isEmpty(session_1) ){
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += "'"+ session_1 +"' <= RVLDATE  ";
			}else if( !StringUtils.isEmpty(session_2) ){ 
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += " RVLDATE <= '"+ session_2 +"' ";
			}
		
			if (!StringUtils.isEmpty(session_3)) {
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += " ibmsts.ACC_RLT  =  '"+ session_3 +"' ";
			}
			if (!StringUtils.isEmpty(session_4)) {
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += "  ibmcase.CASE_ID  like  '%"+ session_4 +"%' ";
			}
			if (!StringUtils.isEmpty(session_5)) {
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += "  REG_YY like   '%"+ session_5 +"%' ";
			}
			if (!StringUtils.isEmpty(session_6)) {
				if (!StringUtils.isEmpty(newWhereStr))newWhereStr+=" and ";
				newWhereStr += "  REG_NO  like  '%"+ session_6 +"%' ";
			}
			
			if (!StringUtils.isEmpty(oldWhereStr)) newWhereStr = oldWhereStr + " AND  " + newWhereStr;
			e.getCommand().setWhere(newWhereStr);	

			
			
//End Event BeforeBuildSelect Action Custom Code

//BMSDISOBEY_DIST BeforeBuildSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeBuildSelect Method Tail

//BMSDISOBEY_DIST BeforeExecuteSelect Method Head @2-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeExecuteSelect Method Head

//BMSDISOBEY_DIST BeforeExecuteSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeExecuteSelect Method Tail

//BMSDISOBEY_DIST AfterExecuteSelect Method Head @2-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST AfterExecuteSelect Method Head

//BMSDISOBEY_DIST AfterExecuteSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterExecuteSelect Method Tail

//BMSDISOBEY_DIST Grid Handler Tail @2-FCB6E20C
    }
//End BMSDISOBEY_DIST Grid Handler Tail

//BMSDISOBEY_DIST_TotalRecords Label Handler Head @4-6F8E28D6
    public class BMSDISOBEY_DISTBMSDISOBEY_DIST_TotalRecordsLabelHandler implements ControlListener {
        public void beforeShow(Event e) {
//End BMSDISOBEY_DIST_TotalRecords Label Handler Head

//Event BeforeShow Action Retrieve number of records @5-9D59B397
        ((Control) e.getSource()).setValue( ((Grid) e.getParent()).getAmountOfRows());
//End Event BeforeShow Action Retrieve number of records

//BMSDISOBEY_DIST_TotalRecords Label Handler Tail @4-F5FC18C5
        }
    }
//End BMSDISOBEY_DIST_TotalRecords Label Handler Tail

//LinkA_insert Link Handler Head @84-FDAE9B00
    public class BMSDISOBEY_DISTLinkA_insertLinkHandler implements ControlListener {
        public void beforeShow(Event e) {
//End LinkA_insert Link Handler Head

//Link parametrs expression @84-360B99BB
            e.getComponent().getLink("LinkA_insert").getParameter("case_id").setValue("");
//End Link parametrs expression

//LinkA_insert Link Handler Tail @84-F5FC18C5
        }
    }
//End LinkA_insert Link Handler Tail

//LinkB_insert Link Handler Head @123-0F0FBEAC
    public class BMSDISOBEY_DISTLinkB_insertLinkHandler implements ControlListener {
        public void beforeShow(Event e) {
//End LinkB_insert Link Handler Head

//Link parametrs expression @123-70D9ACDF
            e.getComponent().getLink("LinkB_insert").getParameter("case_id").setValue("");
//End Link parametrs expression

//LinkB_insert Link Handler Tail @123-F5FC18C5
        }
    }
//End LinkB_insert Link Handler Tail

//LinkD_insert Link Handler Head @125-313CF3B5
    public class BMSDISOBEY_DISTLinkD_insertLinkHandler implements ControlListener {
        public void beforeShow(Event e) {
//End LinkD_insert Link Handler Head

//Link parametrs expression @125-FD7DC617
            e.getComponent().getLink("LinkD_insert").getParameter("case_id").setValue("");
//End Link parametrs expression

//LinkD_insert Link Handler Tail @125-F5FC18C5
        }
    }
//End LinkD_insert Link Handler Tail

//BMSDISOBEY_DISTSearch Record Handler Head @13-9A258CFB
    public class im10101_lisBMSDISOBEY_DISTSearchRecordHandler implements RecordListener, RecordDataObjectListener {
//End BMSDISOBEY_DISTSearch Record Handler Head

//BMSDISOBEY_DISTSearch afterInitialize Method Head @13-89E84600
        public void afterInitialize(Event e) {
//End BMSDISOBEY_DISTSearch afterInitialize Method Head

//BMSDISOBEY_DISTSearch afterInitialize Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch afterInitialize Method Tail

//BMSDISOBEY_DISTSearch OnSetDataSource Method Head @13-9B7FBFCF
        public void onSetDataSource(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch OnSetDataSource Method Head

//BMSDISOBEY_DISTSearch OnSetDataSource Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch OnSetDataSource Method Tail

//BMSDISOBEY_DISTSearch BeforeShow Method Head @13-46046458
        public void beforeShow(Event e) {
//End BMSDISOBEY_DISTSearch BeforeShow Method Head

//Event BeforeShow Action Custom Code @31-44795B7A

			String SEARCHPARAM_1 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_1"));
			String SEARCHPARAM_2 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_2"));
			String SEARCHPARAM_3 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_3"));			
			String SEARCHPARAM_4 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_4"));
			String SEARCHPARAM_5 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_5"));
			String SEARCHPARAM_6 = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEARCHPARAM_6"));
			
			if (!StringUtils.isEmpty(SEARCHPARAM_1)) {
				e.getRecord().getControl("s_S_DATE").setValue(SEARCHPARAM_1);
			}else{
				e.getRecord().getControl("s_S_DATE").setValue("");
			}
			if (!StringUtils.isEmpty(SEARCHPARAM_2)) {
				e.getRecord().getControl("s_E_DATE").setValue(SEARCHPARAM_2);
			}else{
				e.getRecord().getControl("s_E_DATE").setValue("");
			}
			if (!StringUtils.isEmpty(SEARCHPARAM_3)) {
				e.getRecord().getControl("s_STATUS").setValue(SEARCHPARAM_3);
			}else{
				e.getRecord().getControl("s_STATUS").setValue("");
			}	
			if (!StringUtils.isEmpty(SEARCHPARAM_4)) {
				e.getRecord().getControl("s_CASE_ID").setValue(SEARCHPARAM_4);
			}else{
				e.getRecord().getControl("s_CASE_ID").setValue("");
			}
			
			String s_regnum = "";
			// Field:s_reg_yy_b
			if (!StringUtils.isEmpty(SEARCHPARAM_5)) {
				e.getRecord().getControl("s_REG_YY").setValue(SEARCHPARAM_5);
				s_regnum = SEARCHPARAM_5;
			}
			// Field:s_reg_no_b
			if (!StringUtils.isEmpty(SEARCHPARAM_6)) {
				e.getRecord().getControl("s_REG_NO").setValue(SEARCHPARAM_6);
				s_regnum += SEARCHPARAM_6;
			}
			
			// Field:s_regnum_b
			e.getRecord().getControl("s_REG_NUM").setValue(s_regnum);
			

//End Event BeforeShow Action Custom Code

//BMSDISOBEY_DISTSearch BeforeShow Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeShow Method Tail

//BMSDISOBEY_DISTSearch OnValidate Method Head @13-5F430F8E
        public void onValidate(Event e) {
//End BMSDISOBEY_DISTSearch OnValidate Method Head

//Event OnValidate Action Custom Code @30-44795B7A


			String searchParam_1 = Utils.convertToString(e.getRecord().getControl("s_S_DATE").getValue());
			String searchParam_2 = Utils.convertToString(e.getRecord().getControl("s_E_DATE").getValue());
			String searchParam_3 = Utils.convertToString(e.getRecord().getControl("s_STATUS").getValue());	
			String searchParam_4 = Utils.convertToString(e.getRecord().getControl("s_CASE_ID").getValue());	
			String searchParam_5 = Utils.convertToString(e.getRecord().getControl("s_REG_YY").getValue());	
			String searchParam_6 = Utils.convertToString(e.getRecord().getControl("s_REG_NO").getValue());
			String s_regnum = Utils.convertToString(e.getRecord().getControl("s_REG_NUM").getValue());
		
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_1", searchParam_1);
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_2", searchParam_2);
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_3", searchParam_3);
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_4", searchParam_4);
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_5", searchParam_5);
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_6", searchParam_6);
			
			String checkResult = "";
			
			if (!StringUtils.isEmpty(s_regnum)) {
				checkResult = EzekUtils.checkRegNum(e.getRecord().getControl("s_REG_NUM").getCaption(), s_regnum);
				if (!StringUtils.isEmpty(checkResult)) {
					e.getRecord().getControl("s_REG_NUM").addError(checkResult);
				}
			}
			
//End Event OnValidate Action Custom Code

//BMSDISOBEY_DISTSearch OnValidate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch OnValidate Method Tail

//BMSDISOBEY_DISTSearch BeforeSelect Method Head @13-E5EC9AD3
        public void beforeSelect(Event e) {
//End BMSDISOBEY_DISTSearch BeforeSelect Method Head

//BMSDISOBEY_DISTSearch BeforeSelect Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeSelect Method Tail

//BMSDISOBEY_DISTSearch BeforeBuildSelect Method Head @13-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeBuildSelect Method Head

//BMSDISOBEY_DISTSearch BeforeBuildSelect Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeBuildSelect Method Tail

//BMSDISOBEY_DISTSearch BeforeExecuteSelect Method Head @13-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeExecuteSelect Method Head

//BMSDISOBEY_DISTSearch BeforeExecuteSelect Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeExecuteSelect Method Tail

//BMSDISOBEY_DISTSearch AfterExecuteSelect Method Head @13-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch AfterExecuteSelect Method Head

//BMSDISOBEY_DISTSearch AfterExecuteSelect Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterExecuteSelect Method Tail

//BMSDISOBEY_DISTSearch BeforeInsert Method Head @13-75B62B83
        public void beforeInsert(Event e) {
//End BMSDISOBEY_DISTSearch BeforeInsert Method Head

//BMSDISOBEY_DISTSearch BeforeInsert Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeInsert Method Tail

//BMSDISOBEY_DISTSearch BeforeBuildInsert Method Head @13-FD6471B0
        public void beforeBuildInsert(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeBuildInsert Method Head

//BMSDISOBEY_DISTSearch BeforeBuildInsert Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeBuildInsert Method Tail

//BMSDISOBEY_DISTSearch BeforeExecuteInsert Method Head @13-4EB41272
        public void beforeExecuteInsert(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeExecuteInsert Method Head

//BMSDISOBEY_DISTSearch BeforeExecuteInsert Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeExecuteInsert Method Tail

//BMSDISOBEY_DISTSearch AfterExecuteInsert Method Head @13-C4572C5E
        public void afterExecuteInsert(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch AfterExecuteInsert Method Head

//BMSDISOBEY_DISTSearch AfterExecuteInsert Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterExecuteInsert Method Tail

//BMSDISOBEY_DISTSearch AfterInsert Method Head @13-767A9165
        public void afterInsert(Event e) {
//End BMSDISOBEY_DISTSearch AfterInsert Method Head

//BMSDISOBEY_DISTSearch AfterInsert Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterInsert Method Tail

//BMSDISOBEY_DISTSearch BeforeUpdate Method Head @13-33A3CFAC
        public void beforeUpdate(Event e) {
//End BMSDISOBEY_DISTSearch BeforeUpdate Method Head

//BMSDISOBEY_DISTSearch BeforeUpdate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeUpdate Method Tail

//BMSDISOBEY_DISTSearch BeforeBuildUpdate Method Head @13-37688606
        public void beforeBuildUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeBuildUpdate Method Head

//BMSDISOBEY_DISTSearch BeforeBuildUpdate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeBuildUpdate Method Tail

//BMSDISOBEY_DISTSearch BeforeExecuteUpdate Method Head @13-84B8E5C4
        public void beforeExecuteUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeExecuteUpdate Method Head

//BMSDISOBEY_DISTSearch BeforeExecuteUpdate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeExecuteUpdate Method Tail

//BMSDISOBEY_DISTSearch AfterExecuteUpdate Method Head @13-0E5BDBE8
        public void afterExecuteUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch AfterExecuteUpdate Method Head

//BMSDISOBEY_DISTSearch AfterExecuteUpdate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterExecuteUpdate Method Tail

//BMSDISOBEY_DISTSearch AfterUpdate Method Head @13-306F754A
        public void afterUpdate(Event e) {
//End BMSDISOBEY_DISTSearch AfterUpdate Method Head

//BMSDISOBEY_DISTSearch AfterUpdate Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterUpdate Method Tail

//BMSDISOBEY_DISTSearch BeforeDelete Method Head @13-752E3118
        public void beforeDelete(Event e) {
//End BMSDISOBEY_DISTSearch BeforeDelete Method Head

//BMSDISOBEY_DISTSearch BeforeDelete Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeDelete Method Tail

//BMSDISOBEY_DISTSearch BeforeBuildDelete Method Head @13-01A46505
        public void beforeBuildDelete(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeBuildDelete Method Head

//BMSDISOBEY_DISTSearch BeforeBuildDelete Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeBuildDelete Method Tail

//BMSDISOBEY_DISTSearch BeforeExecuteDelete Method Head @13-B27406C7
        public void beforeExecuteDelete(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch BeforeExecuteDelete Method Head

//BMSDISOBEY_DISTSearch BeforeExecuteDelete Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch BeforeExecuteDelete Method Tail

//BMSDISOBEY_DISTSearch AfterExecuteDelete Method Head @13-389738EB
        public void afterExecuteDelete(DataObjectEvent e) {
//End BMSDISOBEY_DISTSearch AfterExecuteDelete Method Head

//BMSDISOBEY_DISTSearch AfterExecuteDelete Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterExecuteDelete Method Tail

//BMSDISOBEY_DISTSearch AfterDelete Method Head @13-76E28BFE
        public void afterDelete(Event e) {
//End BMSDISOBEY_DISTSearch AfterDelete Method Head

//BMSDISOBEY_DISTSearch AfterDelete Method Tail @13-FCB6E20C
        }
//End BMSDISOBEY_DISTSearch AfterDelete Method Tail

//BMSDISOBEY_DISTSearch Record Handler Tail @13-FCB6E20C
    }
//End BMSDISOBEY_DISTSearch Record Handler Tail

//Button_Clean Button Handler Head @19-12E495EC
    public class BMSDISOBEY_DISTSearchButton_CleanButtonHandler implements ButtonListener {
//End Button_Clean Button Handler Head

//Button_Clean OnClick Method Head @19-A9885EEC
        public void onClick(Event e) {
//End Button_Clean OnClick Method Head

//Event OnClick Action Custom Code @32-44795B7A


	for (int i = 1; i <= 15; i++) {
				SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + i, "");
		}

//End Event OnClick Action Custom Code

//Button_Clean OnClick Method Tail @19-FCB6E20C
        }
//End Button_Clean OnClick Method Tail

//Button_Clean BeforeShow Method Head @19-46046458
        public void beforeShow(Event e) {
//End Button_Clean BeforeShow Method Head

//Button_Clean BeforeShow Method Tail @19-FCB6E20C
        }
//End Button_Clean BeforeShow Method Tail

//Button_Clean Button Handler Tail @19-FCB6E20C
    }
//End Button_Clean Button Handler Tail

//s_STATUS ListBox Handler Head @15-8FEA9F0B
    public class BMSDISOBEY_DISTSearchs_STATUSListBoxHandler implements ValidationListener, ListDataObjectListener {
//End s_STATUS ListBox Handler Head

//s_STATUS BeforeShow Method Head @15-46046458
        public void beforeShow(Event e) {
//End s_STATUS BeforeShow Method Head

//s_STATUS BeforeShow Method Tail @15-FCB6E20C
        }
//End s_STATUS BeforeShow Method Tail

//s_STATUS OnValidate Method Head @15-5F430F8E
        public void onValidate(Event e) {
//End s_STATUS OnValidate Method Head

//s_STATUS OnValidate Method Tail @15-FCB6E20C
        }
//End s_STATUS OnValidate Method Tail

//s_STATUS BeforeBuildSelect Method Head @15-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End s_STATUS BeforeBuildSelect Method Head

//Event BeforeBuildSelect Action Custom Code @153-44795B7A
		
		String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
		String UNIT_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));
		
		String ROLE_ID = Utils.convertToString(DBTools.dLookUp("ROLE_ID", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
		String ROLE_ID_2 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_2", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));
		String ROLE_ID_3 = Utils.convertToString(DBTools.dLookUp("ROLE_ID_3", " IBMUSER", " EMPNO = '"+UserID+"' ", "DBConn"));	
		
		String oldWhereStr = e.getCommand().getWhere();
		String newWhereStr = "CODE_TYPE = 'RLT' and ";	
		
		// 認定協同
		// 認定科         234
		// 勞安科(下水道) 254
		// 廣告科         244
		// 陳核 232,252
		// 認定科         232
		// 勞安科(下水道) 252
		// 廣告科         none
		
		String unit01 = "'231','23b','237','234','232','32a' "; // 認定科 
		String unit04 = "'241','24b','347','244','344' "; // 廣告科
		String unit03 = "'251','25b','257','254','252' "; // 勞安科
		
		//判定角色若為系統管理者或隊部長官
		if("sysManager".equals(ROLE_ID) || "sysManager".equals(ROLE_ID_2) || "sysManager".equals(ROLE_ID_3) || "supervisor".equals(ROLE_ID) || "supervisor".equals(ROLE_ID_2) || "supervisor".equals(ROLE_ID_3)){
			newWhereStr += "CODE_SEQ in("+unit01+", "+unit04+", "+unit03+")";
		}
		else{
			//認定一科
			if("011".equals(UNIT_ID) || "012".equals(UNIT_ID)){
				newWhereStr += "CODE_SEQ in("+unit01+")";
			}
			//廣拆科
			else if("041".equals(UNIT_ID)){
				newWhereStr += "CODE_SEQ in("+unit04+")";
			}
			//勞安科
			else if("031".equals(UNIT_ID)){
				newWhereStr += "CODE_SEQ in("+unit03+")";
			}
			else{
				newWhereStr += "1 = 2";
			}
		}
		
		

		if (!StringUtils.isEmpty(oldWhereStr)) newWhereStr = oldWhereStr + " AND  " + newWhereStr;
		e.getCommand().setWhere(newWhereStr);
			
//End Event BeforeBuildSelect Action Custom Code

//s_STATUS BeforeBuildSelect Method Tail @15-FCB6E20C
        }
//End s_STATUS BeforeBuildSelect Method Tail

//s_STATUS BeforeExecuteSelect Method Head @15-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End s_STATUS BeforeExecuteSelect Method Head

//s_STATUS BeforeExecuteSelect Method Tail @15-FCB6E20C
        }
//End s_STATUS BeforeExecuteSelect Method Tail

//s_STATUS AfterExecuteSelect Method Head @15-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End s_STATUS AfterExecuteSelect Method Head

//s_STATUS AfterExecuteSelect Method Tail @15-FCB6E20C
        }
//End s_STATUS AfterExecuteSelect Method Tail

//s_STATUS ListBox Handler Tail @15-FCB6E20C
    }
//End s_STATUS ListBox Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-669EEF6F
    Page im10101_lisModel = (Page)request.getAttribute("im10101_lis_page");
    Page im10101_lisParent = (Page)request.getAttribute("im10101_lisParent");
    if (im10101_lisModel == null) {
        PageController im10101_lisCntr = new PageController(request, response, application, "/im10101_lis.xml" );
        im10101_lisModel = im10101_lisCntr.getPage();
        im10101_lisModel.setRelativePath("./");
        //if (im10101_lisParent != null) {
            //if (!im10101_lisParent.getChild(im10101_lisModel.getName()).isVisible()) return;
        //}
        im10101_lisModel.addPageListener(new im10101_lisPageHandler());
        ((Grid)im10101_lisModel.getChild("BMSDISOBEY_DIST")).addGridListener(new im10101_lisBMSDISOBEY_DISTGridHandler());
        ((Label)((Grid)im10101_lisModel.getChild("BMSDISOBEY_DIST")).getChild("BMSDISOBEY_DIST_TotalRecords")).addControlListener(new BMSDISOBEY_DISTBMSDISOBEY_DIST_TotalRecordsLabelHandler());
        ((Link)((Grid)im10101_lisModel.getChild("BMSDISOBEY_DIST")).getChild("LinkA_insert")).addControlListener(new BMSDISOBEY_DISTLinkA_insertLinkHandler());
        ((Link)((Grid)im10101_lisModel.getChild("BMSDISOBEY_DIST")).getChild("LinkB_insert")).addControlListener(new BMSDISOBEY_DISTLinkB_insertLinkHandler());
        ((Link)((Grid)im10101_lisModel.getChild("BMSDISOBEY_DIST")).getChild("LinkD_insert")).addControlListener(new BMSDISOBEY_DISTLinkD_insertLinkHandler());
        ((Record)im10101_lisModel.getChild("BMSDISOBEY_DISTSearch")).addRecordListener(new im10101_lisBMSDISOBEY_DISTSearchRecordHandler());
        ((Button)((Record)im10101_lisModel.getChild("BMSDISOBEY_DISTSearch")).getChild("Button_Clean")).addButtonListener(new BMSDISOBEY_DISTSearchButton_CleanButtonHandler());
        ((ListBox)((Record)im10101_lisModel.getChild("BMSDISOBEY_DISTSearch")).getChild("s_STATUS")).addValidationListener(new BMSDISOBEY_DISTSearchs_STATUSListBoxHandler());
        im10101_lisCntr.process();
%>
<%
        if (im10101_lisParent == null) {
            im10101_lisModel.setCookies();
            if (im10101_lisModel.redirect()) return;
        } else {
            im10101_lisModel.redirect();
        }
    }
//End Processing

%>

<%!
	public class ezekTool{
		public String formateDate(String dateOld) 
		{
			String date = "";

			if(!StringUtils.isEmpty(dateOld))
			{

				if(dateOld.length() == 13)// yyymmddhhmiss
				{
					date = dateOld.substring(0,3)+"/"+dateOld.substring(3,5)+"/"+dateOld.substring(5,7) + " " + dateOld.substring(7,9) + ":" + dateOld.substring(9,11);
				}
				else if(dateOld.length() == 12) // yymmddhhmiss
				{
					date = dateOld.substring(0,2)+"/"+dateOld.substring(2,4)+"/"+dateOld.substring(4,6) + " " + dateOld.substring(6,8) + ":" + dateOld.substring(8,10);
				}
				else if(dateOld.length() == 14) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,4)+"/"+dateOld.substring(4,6)+"/"+dateOld.substring(6,8) + " " + dateOld.substring(8,10) + ":" + dateOld.substring(10,12);
				}
				else if(dateOld.length() == 6) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,2)+"/"+dateOld.substring(2,4)+"/"+dateOld.substring(4,6);
				}
				else if(dateOld.length() == 7) // yyyymmddhhmiss
				{
					date = dateOld.substring(0,3)+"/"+dateOld.substring(3,5)+"/"+dateOld.substring(5,7);
				}
				else{
					date = "";
				}
			}

			return date;
		}
	}
%>
