<%@page pageEncoding="utf-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils"%> 

<%@page import="java.sql.*, java.util.Date, java.util.UUID, org.json.simple.*"%> 
<%@page import="javax.servlet.*, java.net.URLDecoder" %>
<%@page import="com.ezek.utils.EzekUtils"%>
<%@ page import="java.time.LocalDate" %>
<%@ page import="java.time.LocalDateTime" %>
<%@ page import="java.time.format.DateTimeFormatter" %>
<%@ page import="java.sql.Timestamp" %>

<%-- Handlers --%>
<%!
    // Workaround for JRun 3.1
    private final String CONNECTION_NAME = "DBConn";

    /**
     * 獲取當前民國日期字串 (格式: yyy/MM/dd) FOR DISPLAY
     * @return 民國年份日期字串，例如: 112/11/11 (民國 112年11月11日)
     */
    private String getTaiwanDisplayDate() {
        LocalDate today = LocalDate.now();
        int rocYear = today.getYear() - 1911;
        return String.format("%d/%02d/%02d", 
                rocYear,
                today.getMonthValue(),
                today.getDayOfMonth()
        );
    }

    // Feature checker
    public class im52101_manServiceChecker implements com.codecharge.feature.IServiceChecker {
        public boolean check(HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
    }

    // Page Handler
    public class im52101_manPageHandler implements PageListener {
        public void beforeInitialize(Event e) {
        }

        public void afterInitialize(Event e) {
            // Validate timeout sync
            if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True") {
                e.getPage().setRedirectString("timeout_err.jsp");
            }  
        }

        public void onInitializeView(Event e) {
        }

        public void beforeShow(Event e) {
           
        }

        public void beforeOutput(Event e) {
        }

        public void beforeUnload(Event e) {
        }

        public void onCache(CacheEvent e) {
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
                // Custom code before get cachedItem
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
                // Custom code before put cachedItem
            }
        }
    }

    // Record Handler for im52101ExcelImport
    public class im52101_man_im52101ExcelImport_RecordHandler implements RecordListener, RecordDataObjectListener {
        public void afterInitialize(Event e) {
        }

        public void onSetDataSource(DataObjectEvent e) {
        }

        public void beforeShow(Event e) {
            String UserID = Utils.convertToString(
                SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID")
            );
            String UserName = Utils.convertToString(
                DBTools.dLookUp(" empname ", "ibmuser", " empno = '" + UserID + "'", CONNECTION_NAME)
            );

            // Set display values for JSP
            e.getPage().getRequest().setAttribute("upload_date_show", getTaiwanDisplayDate());
            e.getPage().getRequest().setAttribute("cr_user_show", UserName);

            DBConnectionManager dbcm = null;
            Connection conn = null;
            PreparedStatement pstmt = null;
			ResultSet rs = null; // Not strictly needed for this INSERT if not RETURNING an auto-gen key other than UUID
            String generatedUUID = UUID.randomUUID().toString();

            try{
                dbcm = DBConnectionManager.getInstance();
                conn = dbcm.getConnection(CONNECTION_NAME);
                conn.setAutoCommit(false);

                String sql = "INSERT INTO public.im52101_excel_imports " + 
                             "(import_id, cr_user, upload_timestamp, status, acc_memo) " + 
                             "VALUES (?, ?, ?, ?, ?)";  

                pstmt = conn.prepareStatement(sql);
             
                pstmt.setString(1, generatedUUID);              // import_id (UUID)
                pstmt.setString(2, UserID);                     // cr_user
                pstmt.setTimestamp(3, new Timestamp(System.currentTimeMillis())); // upload_timestamp
                pstmt.setString(4, "NEW");               // status
                pstmt.setString(5, "");                         // acc_memo (initial empty string)
           
                pstmt.executeUpdate(); // Use executeUpdate for INSERT without RETURNING specific columns to ResultSet
                
                e.getRecord().getControl("import_id").setValue(generatedUUID);
                // If acc_memo needs to be pre-filled or fetched, do it here.
                // e.getRecord().getControl("acc_memo").setValue("Default memo if any"); 

                conn.commit();
            } catch (Exception localException) {
                System.err.println("im52101_manHandlers.jsp - beforeShow error: " + localException.toString());
                localException.printStackTrace(); // For more detailed error logging
                if (conn != null) {
                    try {
                        conn.rollback();
                    } catch (SQLException ex) {
                        System.err.println("Rollback failed: " + ex.toString());
                    }
                }
                // Optionally, set an error message for the page
                // e.getPage().addError("Error initializing data: " + localException.getMessage());
            } finally {
               if (rs != null) { try { rs.close(); } catch (SQLException sqle) { sqle.printStackTrace(); } }
               if (pstmt != null) { try { pstmt.close(); } catch (SQLException sqle) { sqle.printStackTrace(); } }
               if (conn != null && dbcm != null) {
                    try {
                        if (!conn.isClosed()) { // Check if connection is not already closed
                           // dbcm.freeConnection(CONNECTION_NAME, conn); // freeConnection also closes it
                           conn.close(); // It's often simpler to just close it here
                        }
                    } catch (SQLException sqle) {
                        sqle.printStackTrace();
                    }
                }
            }
        }

        public void onValidate(Event e) {
        }

        public void beforeSelect(Event e) {
            // This handler is for a new record, so select might not be applicable unless fetching existing memo
            // If you need to load acc_memo for an existing import_id (e.g. if page can edit existing):
            // String importId = e.getRecord().getControl("import_id").getRawValue();
            // if (importId != null && !importId.isEmpty()) {
            //     Object memo = DBTools.dLookUp("acc_memo", "im52101_excel_imports", "import_id = '" + importId + "'", CONNECTION_NAME);
            //     if (memo != null) {
            //         e.getRecord().getControl("acc_memo").setValue(memo.toString());
            //     }
            // }
        }

        public void beforeBuildSelect(DataObjectEvent e) {
        }

        public void beforeExecuteSelect(DataObjectEvent e) {
        }

        public void afterExecuteSelect(DataObjectEvent e) {
        }

        public void beforeInsert(Event e) {
        }

        public void beforeBuildInsert(DataObjectEvent e) {
        }

        public void beforeExecuteInsert(DataObjectEvent e) {
        }

        public void afterExecuteInsert(DataObjectEvent e) {
        }

        public void afterInsert(Event e) {
        }

        public void beforeUpdate(Event e) {
            // This would be called if the Record's <Update> operation is triggered by a CCS button.
            // If using AJAX to a separate save.jsp, this might not be hit for memo saving.
        }

        public void beforeBuildUpdate(DataObjectEvent e) {
        }

        public void beforeExecuteUpdate(DataObjectEvent e) {
        }

        public void afterExecuteUpdate(DataObjectEvent e) {
        }

        public void afterUpdate(Event e) {
        }

        public void beforeDelete(Event e) {
        }

        public void beforeBuildDelete(DataObjectEvent e) {
        }

        public void beforeExecuteDelete(DataObjectEvent e) {
        }

        public void afterExecuteDelete(DataObjectEvent e) {
        }

        public void afterDelete(Event e) {
        }
    }
%>

<%
    // Processing
    Page im52101_manModel = (Page)request.getAttribute("im52101_man_page");
    Page im52101_manParent = (Page)request.getAttribute("im52101_manParent");
    
    if (im52101_manModel == null) {
        PageController im52101_manCntr = new PageController(request, response, application, "/im52101_man.xml");
        im52101_manModel = im52101_manCntr.getPage();
        im52101_manModel.setRelativePath("./");
        
        im52101_manModel.addPageListener(new im52101_manPageHandler());
        ((Record)im52101_manModel.getChild("ibmcase")).addRecordListener(new im52101_man_im52101ExcelImport_RecordHandler()); //Match record name in XML
        im52101_manCntr.process();
        
        if (im52101_manParent == null) {
            im52101_manModel.setCookies();
            if (im52101_manModel.redirect()) return;
        } else {
            im52101_manModel.redirect();
        }
    }
%> 