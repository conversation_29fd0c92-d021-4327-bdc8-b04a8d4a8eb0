<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils"%>

<%@page import="java.sql.*, java.util.Date, java.util.UUID, org.json.simple.*"%>
<%@page import="javax.servlet.*, java.net.URLDecoder" %>

<%!
/**
 * 根據當前狀態碼取得抽回後的目標狀態碼
 * @param currentStatus 當前狀態碼
 * @return 抽回後的目標狀態碼
 */
private String getWithdrawTargetStatus(String currentStatus) {
    switch(currentStatus) {
        case "232": return "231"; // 一般認定陳核 → 一般認定初建
        case "252": return "251"; // 下水道認定陳核 → 下水道認定初建
        case "342": return "344"; // 一般排拆陳核 → 一般排拆辦理中
        case "352": return "354"; // 廣告排拆陳核 → 廣告排拆辦理中
        case "362": return "364"; // 下水道排拆陳核 → 下水道排拆辦理中
        case "442": return "441"; // 一般結案陳核 → 一般結案辦理中
        case "452": return "451"; // 廣告結案陳核 → 廣告結案辦理中
        case "462": return "461"; // 下水道結案陳核 → 下水道結案辦理中
        default: return currentStatus;
    }
}
%>

<%

	String caseId = request.getParameter("caseId");
	String accRlt = request.getParameter("accRlt");
	String empNo = request.getParameter("empNo");
	String uuid = UUID.randomUUID().toString();
	JSONObject outJSon = new JSONObject();
	
	if (!"232".equals(accRlt)
			&& !"252".equals(accRlt)
			&& !"342".equals(accRlt)
			&& !"352".equals(accRlt)
			&& !"362".equals(accRlt)
			&& !"442".equals(accRlt)
			&& !"452".equals(accRlt)
			&& !"462".equals(accRlt)) {
		outJSon.put("result", "-1");
		return;
	}
	
	JDBCConnection conn = null;
	//String UPD_CASE_SQL = " UPDATE public.ibmcase set status = '01' WHERE case_id=";
	//UPD_CASE_SQL += "'" + caseId + "' AND status = '02'";\
	
	//案件狀態
	String PRE_ACC_RLT = Utils.convertToString(DBTools.dLookUp("ACC_RLT", "CASESUBMITSTS", "CASE_ID = '"+caseId+"'", "DBConn"));
	
	String newRlt = "";
	
	if(!StringUtils.isEmpty(PRE_ACC_RLT)){
		newRlt = PRE_ACC_RLT;
	} else {
		// 使用函數取得抽回後的目標狀態
		newRlt = getWithdrawTargetStatus(accRlt);
	}
	
	String UPD_STS_SQL = " UPDATE public.ibmsts set acc_rlt = '"+newRlt+"' WHERE case_id=";
	UPD_STS_SQL += "'" + caseId + "' AND acc_rlt = '"+accRlt+"'";
	System.err.println(UPD_STS_SQL);
	String INS_SQL = " INSERT INTO public.record (uuid,case_id,rec_type,org_rec,new_rec,empno) VALUES(";
	INS_SQL += "'" + uuid + "'";
	INS_SQL += ",'" + caseId + "'";
	INS_SQL += ",'案件抽回'";
	INS_SQL += ",'" + accRlt + "'";
	INS_SQL += ",'" + newRlt + "'";
	INS_SQL += ",'" + empNo + "'";
	INS_SQL += ")";
	
	try{
		conn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		//conn.executeUpdate(UPD_CASE_SQL);
		conn.executeUpdate(UPD_STS_SQL);
		conn.executeUpdate(INS_SQL);
		outJSon.put("result", "");
	}catch(Exception e){
		outJSon.put("result", e.toString());
		System.err.println(" post_it_insert err::" + e.toString());
	}finally{
		if(conn!=null){
			conn.closeConnection();
		}
	}
	
	out.println(outJSon.toString());

%>
