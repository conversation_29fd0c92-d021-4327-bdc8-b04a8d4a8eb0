# 違章建築資料拋送至國土署系統 - 測試計畫書

## 文件資訊
- **文件版本**：1.0
- **建立日期**：2025-07-08
- **系統名稱**：違章建築資料同步服務
- **專案編號**：BMS-SYNC-2025

## 目錄
1. [測試概述](#測試概述)
2. [測試範圍](#測試範圍)
3. [測試環境](#測試環境)
4. [單元測試計畫](#單元測試計畫)
5. [整合測試計畫](#整合測試計畫)
6. [壓力測試計畫](#壓力測試計畫)
7. [使用者驗收測試](#使用者驗收測試)
8. [測試時程規劃](#測試時程規劃)
9. [風險評估](#風險評估)

## 測試概述

### 測試目標
1. 確保資料同步服務的功能正確性
2. 驗證系統效能符合需求規格
3. 確認資料完整性與一致性
4. 驗證錯誤處理與復原機制
5. 確保系統安全性符合標準

### 測試策略
- **分層測試**：從單元測試到系統整合測試
- **自動化優先**：盡可能自動化測試案例
- **持續整合**：整合至CI/CD流程
- **風險導向**：優先測試高風險功能

### 成功標準
- 單元測試覆蓋率 > 80%
- 整合測試通過率 = 100%
- 壓力測試無記憶體洩漏
- 效能指標符合SLA要求

## 測試範圍

### 測試項目
1. **資料存取層（Repository）**
   - 案件資料CRUD操作
   - 同步佇列管理
   - 稽核記錄功能
   
2. **業務邏輯層（Service）**
   - 資料轉換邏輯
   - 同步排程作業
   - 錯誤處理機制
   
3. **API整合層**
   - 國土署API呼叫
   - 重試機制
   - 回應處理
   
4. **資料庫層**
   - 觸發器運作
   - 交易完整性
   - 效能查詢

### 排除範圍
- 國土署API內部邏輯
- 第三方套件功能
- 現有違建管理系統

## 測試環境

### 環境配置

| 環境 | 用途 | 配置 | 資料庫 |
|------|------|------|--------|
| DEV | 開發測試 | 2 Core, 4GB RAM | PostgreSQL 12 (Local) |
| SIT | 系統整合測試 | 4 Core, 8GB RAM | PostgreSQL 12 (獨立) |
| UAT | 使用者驗收 | 8 Core, 16GB RAM | PostgreSQL 12 (HA) |
| PERF | 壓力測試 | 16 Core, 32GB RAM | PostgreSQL 12 (Cluster) |

### 測試資料準備
```sql
-- 建立測試資料集
-- 1. 基本測試資料（100筆）
INSERT INTO violation_cases (case_no, case_type, status, current_stage, data, created_by)
SELECT 
    'TEST' || LPAD(generate_series::TEXT, 6, '0'),
    CASE (random() * 2)::INT 
        WHEN 0 THEN 'NORMAL'
        WHEN 1 THEN 'ADVERT'
        ELSE 'SEWER'
    END,
    'active',
    '231',
    jsonb_build_object(
        'tbvio_name', '測試違建人' || generate_series,
        'address1', '測試地址' || generate_series,
        'rpt_day', CURRENT_DATE - (random() * 365)::INT
    ),
    'TEST_USER'
FROM generate_series(1, 100);

-- 2. 壓力測試資料（10萬筆）
-- 使用相同邏輯但數量更大
```

## 單元測試計畫

### Repository層測試

#### CaseRepository測試
```csharp
[TestClass]
public class CaseRepositoryTests
{
    private ICaseRepository _repository;
    private IDbConnection _connection;
    
    [TestInitialize]
    public void Setup()
    {
        _connection = new NpgsqlConnection(TestConfig.ConnectionString);
        _repository = new CaseRepository(_connection);
    }
    
    [TestMethod]
    public async Task GetByIdAsync_ExistingCase_ReturnsCase()
    {
        // Arrange
        var caseId = 1;
        
        // Act
        var result = await _repository.GetByIdAsync(caseId);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(caseId, result.Id);
    }
    
    [TestMethod]
    public async Task GetByCaseNoAsync_ValidCaseNo_ReturnsCase()
    {
        // Arrange
        var caseNo = "A113000001";
        
        // Act
        var result = await _repository.GetByCaseNoAsync(caseNo);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(caseNo, result.CaseNo);
    }
    
    [TestMethod]
    public async Task CreateAsync_ValidData_ReturnsNewId()
    {
        // Arrange
        var newCase = new ViolationCase
        {
            CaseNo = "TEST000001",
            CaseType = "NORMAL",
            Status = "active",
            CurrentStage = "231",
            Data = JsonDocument.Parse("{\"test\": \"data\"}"),
            CreatedBy = "TEST_USER",
            UpdatedBy = "TEST_USER"
        };
        
        // Act
        using var transaction = _connection.BeginTransaction();
        var newId = await _repository.CreateAsync(newCase);
        transaction.Rollback(); // 回滾測試資料
        
        // Assert
        Assert.IsTrue(newId > 0);
    }
    
    [TestMethod]
    public async Task UpdateAsync_ExistingCase_UpdatesSuccessfully()
    {
        // Arrange
        var existingCase = await _repository.GetByIdAsync(1);
        existingCase.CurrentStage = "232";
        existingCase.UpdatedBy = "TEST_UPDATE";
        
        // Act
        using var transaction = _connection.BeginTransaction();
        await _repository.UpdateAsync(existingCase);
        var updated = await _repository.GetByIdAsync(1);
        transaction.Rollback();
        
        // Assert
        Assert.AreEqual("232", updated.CurrentStage);
    }
    
    [TestMethod]
    public async Task GetPendingSyncAsync_HasPendingCases_ReturnsList()
    {
        // Arrange
        var limit = 10;
        
        // Act
        var results = await _repository.GetPendingSyncAsync(limit);
        
        // Assert
        Assert.IsNotNull(results);
        Assert.IsTrue(results.Count() <= limit);
    }
}
```

#### SyncQueueRepository測試
```csharp
[TestClass]
public class SyncQueueRepositoryTests
{
    private ISyncQueueRepository _repository;
    private IDbConnection _connection;
    
    [TestMethod]
    public async Task EnqueueAsync_ValidItem_ReturnsQueueId()
    {
        // Arrange
        var queueItem = new SyncQueueItem
        {
            CaseId = 1,
            Operation = "CREATE",
            Priority = 1,
            Status = "pending"
        };
        
        // Act
        using var transaction = _connection.BeginTransaction();
        var queueId = await _repository.EnqueueAsync(queueItem);
        transaction.Rollback();
        
        // Assert
        Assert.IsTrue(queueId > 0);
    }
    
    [TestMethod]
    public async Task DequeueBatchAsync_HasPendingItems_ReturnsItems()
    {
        // Arrange
        var batchSize = 5;
        
        // Act
        var items = await _repository.DequeueBatchAsync(batchSize);
        
        // Assert
        Assert.IsNotNull(items);
        Assert.IsTrue(items.Count() <= batchSize);
        Assert.IsTrue(items.All(i => i.Status == "processing"));
    }
    
    [TestMethod]
    public async Task UpdateStatusAsync_ValidQueueId_UpdatesStatus()
    {
        // Arrange
        var queueId = 1;
        var newStatus = "completed";
        
        // Act
        using var transaction = _connection.BeginTransaction();
        await _repository.UpdateStatusAsync(queueId, newStatus);
        transaction.Rollback();
        
        // Assert
        // 驗證狀態已更新
    }
    
    [TestMethod]
    public async Task IncrementRetryAsync_FailedItem_IncrementsRetryCount()
    {
        // Arrange
        var queueId = 1;
        var nextRetry = DateTime.Now.AddMinutes(5);
        
        // Act
        using var transaction = _connection.BeginTransaction();
        await _repository.IncrementRetryAsync(queueId, nextRetry);
        transaction.Rollback();
        
        // Assert
        // 驗證重試次數已增加
    }
}
```

### Service層測試

#### DataMapper測試
```csharp
[TestClass]
public class DataMapperTests
{
    private DataMapper _mapper;
    
    [TestInitialize]
    public void Setup()
    {
        _mapper = new DataMapper();
    }
    
    [TestMethod]
    public void MapToApiRequest_CompleteData_MapsAllFields()
    {
        // Arrange
        var caseData = JsonDocument.Parse(@"{
            ""rpt_day"": ""2024-01-01"",
            ""rep_name"": ""測試查報人"",
            ""tbvio_name"": ""測試違建人"",
            ""tbvio_unid"": ""A123456789"",
            ""address1"": ""測試地址"",
            ""build_type"": ""RC""
        }");
        
        var violationCase = new ViolationCase
        {
            CaseNo = "A113000001",
            CaseType = "NORMAL",
            CurrentStage = "231",
            Data = caseData
        };
        
        // Act
        var apiRequest = _mapper.MapToApiRequest(violationCase, null);
        
        // Assert
        Assert.AreEqual("A113000001", apiRequest.CaseNo);
        Assert.AreEqual("01", apiRequest.CaseType);
        Assert.AreEqual("231", apiRequest.StageCode);
        Assert.AreEqual("測試查報人", apiRequest.ReporterName);
        Assert.AreEqual("測試違建人", apiRequest.ViolatorName);
    }
    
    [TestMethod]
    public void MapCaseType_AllTypes_ReturnsCorrectCode()
    {
        // Arrange & Act & Assert
        Assert.AreEqual("01", _mapper.MapCaseType("NORMAL"));
        Assert.AreEqual("02", _mapper.MapCaseType("ADVERT"));
        Assert.AreEqual("03", _mapper.MapCaseType("SEWER"));
        Assert.AreEqual("00", _mapper.MapCaseType("UNKNOWN"));
    }
    
    [TestMethod]
    public void MapStageData_RegisterStage_MapsCorrectly()
    {
        // Arrange
        var stageData = JsonDocument.Parse(@"{
            ""reg_date"": ""2024-01-01"",
            ""s_empno"": ""EMP001""
        }").RootElement;
        
        // Act
        var mapped = _mapper.MapStageData("REGISTER", stageData) as RegisterStageData;
        
        // Assert
        Assert.IsNotNull(mapped);
        Assert.AreEqual(new DateTime(2024, 1, 1), mapped.RegisterDate);
        Assert.AreEqual("EMP001", mapped.AssignedOfficer);
    }
}
```

#### SyncService測試
```csharp
[TestClass]
public class SyncServiceTests
{
    private Mock<ICaseRepository> _mockCaseRepo;
    private Mock<ISyncQueueRepository> _mockQueueRepo;
    private Mock<INationalAPIClient> _mockApiClient;
    private SyncService _service;
    
    [TestInitialize]
    public void Setup()
    {
        _mockCaseRepo = new Mock<ICaseRepository>();
        _mockQueueRepo = new Mock<ISyncQueueRepository>();
        _mockApiClient = new Mock<INationalAPIClient>();
        
        _service = new SyncService(
            _mockCaseRepo.Object,
            _mockQueueRepo.Object,
            _mockApiClient.Object
        );
    }
    
    [TestMethod]
    public async Task ProcessBatchAsync_SuccessfulSync_UpdatesStatus()
    {
        // Arrange
        var queueItems = new List<SyncQueueItem>
        {
            new SyncQueueItem { Id = 1, CaseId = 1, Operation = "CREATE" }
        };
        
        var testCase = new ViolationCase
        {
            Id = 1,
            CaseNo = "TEST001",
            Data = JsonDocument.Parse("{}")
        };
        
        _mockQueueRepo.Setup(x => x.DequeueBatchAsync(It.IsAny<int>()))
            .ReturnsAsync(queueItems);
        
        _mockCaseRepo.Setup(x => x.GetByIdAsync(1))
            .ReturnsAsync(testCase);
        
        _mockApiClient.Setup(x => x.SendDataAsync(It.IsAny<NationalApiRequest>()))
            .ReturnsAsync(new ApiResponse { Success = true });
        
        // Act
        var result = await _service.ProcessBatchAsync();
        
        // Assert
        Assert.AreEqual(1, result.SuccessCount);
        Assert.AreEqual(0, result.FailedCount);
        
        _mockQueueRepo.Verify(x => 
            x.UpdateStatusAsync(1, "completed", null), 
            Times.Once);
    }
    
    [TestMethod]
    public async Task ProcessBatchAsync_ApiFailure_IncrementsRetry()
    {
        // Arrange
        var queueItems = new List<SyncQueueItem>
        {
            new SyncQueueItem 
            { 
                Id = 1, 
                CaseId = 1, 
                Operation = "CREATE",
                RetryCount = 0,
                MaxRetry = 3
            }
        };
        
        _mockQueueRepo.Setup(x => x.DequeueBatchAsync(It.IsAny<int>()))
            .ReturnsAsync(queueItems);
        
        _mockApiClient.Setup(x => x.SendDataAsync(It.IsAny<NationalApiRequest>()))
            .ThrowsAsync(new ApiException("Connection timeout"));
        
        // Act
        var result = await _service.ProcessBatchAsync();
        
        // Assert
        Assert.AreEqual(0, result.SuccessCount);
        Assert.AreEqual(1, result.FailedCount);
        
        _mockQueueRepo.Verify(x => 
            x.IncrementRetryAsync(1, It.IsAny<DateTime>()), 
            Times.Once);
    }
}
```

## 整合測試計畫

### API整合測試

#### 國土署API整合測試
```csharp
[TestClass]
[TestCategory("Integration")]
public class NationalAPIIntegrationTests
{
    private NationalAPIClient _client;
    private HttpClient _httpClient;
    
    [TestInitialize]
    public void Setup()
    {
        _httpClient = new HttpClient();
        _client = new NationalAPIClient(_httpClient, TestConfig.ApiSettings);
    }
    
    [TestMethod]
    public async Task SendDataAsync_ValidRequest_ReturnsSuccess()
    {
        // Arrange
        var request = new NationalApiRequest
        {
            CaseNo = "TEST001",
            CaseType = "01",
            StageCode = "231",
            ReportDate = DateTime.Now,
            ReporterName = "整合測試",
            ViolatorName = "測試違建人",
            Address = "測試地址"
        };
        
        // Act
        var response = await _client.SendDataAsync(request);
        
        // Assert
        Assert.IsTrue(response.Success);
        Assert.IsNotNull(response.TransactionId);
    }
    
    [TestMethod]
    public async Task SendDataAsync_InvalidData_ReturnsValidationError()
    {
        // Arrange
        var request = new NationalApiRequest
        {
            CaseNo = "", // 無效的案件編號
            CaseType = "99" // 無效的案件類型
        };
        
        // Act
        var response = await _client.SendDataAsync(request);
        
        // Assert
        Assert.IsFalse(response.Success);
        Assert.IsNotNull(response.ErrorMessage);
        Assert.IsTrue(response.ErrorMessage.Contains("validation"));
    }
    
    [TestMethod]
    [Timeout(30000)] // 30秒超時
    public async Task SendDataAsync_NetworkTimeout_ThrowsTimeoutException()
    {
        // Arrange
        var slowClient = new HttpClient
        {
            Timeout = TimeSpan.FromSeconds(1) // 1秒超時
        };
        var client = new NationalAPIClient(slowClient, TestConfig.ApiSettings);
        
        var request = new NationalApiRequest { /* ... */ };
        
        // Act & Assert
        await Assert.ThrowsExceptionAsync<TaskCanceledException>(
            async () => await client.SendDataAsync(request)
        );
    }
}
```

### 資料庫整合測試

#### 觸發器測試
```csharp
[TestClass]
[TestCategory("Integration")]
public class DatabaseTriggerTests
{
    private IDbConnection _connection;
    
    [TestMethod]
    public async Task AuditTrigger_InsertCase_CreatesAuditLog()
    {
        // Arrange
        using var transaction = _connection.BeginTransaction();
        
        // Act
        var caseId = await InsertTestCase(_connection);
        
        // Assert
        var auditLogs = await GetAuditLogs(_connection, "violation_cases", caseId);
        Assert.AreEqual(1, auditLogs.Count);
        Assert.AreEqual("INSERT", auditLogs[0].Action);
        
        transaction.Rollback();
    }
    
    [TestMethod]
    public async Task AuditTrigger_UpdateCase_RecordsChangedFields()
    {
        // Arrange
        using var transaction = _connection.BeginTransaction();
        var caseId = await InsertTestCase(_connection);
        
        // Act
        await UpdateCaseStage(_connection, caseId, "232");
        
        // Assert
        var auditLogs = await GetAuditLogs(_connection, "violation_cases", caseId);
        var updateLog = auditLogs.FirstOrDefault(l => l.Action == "UPDATE");
        
        Assert.IsNotNull(updateLog);
        Assert.Contains("current_stage", updateLog.ChangedFields);
        
        transaction.Rollback();
    }
}
```

### 端對端測試

#### 完整同步流程測試
```csharp
[TestClass]
[TestCategory("E2E")]
public class SyncFlowE2ETests
{
    [TestMethod]
    public async Task CompleteSyncFlow_NewCase_SyncsSuccessfully()
    {
        // Arrange
        var testCaseNo = "E2E" + DateTime.Now.Ticks;
        
        // Act
        // 1. 建立新案件
        var caseId = await CreateViolationCase(testCaseNo);
        
        // 2. 等待觸發器將案件加入同步佇列
        await Task.Delay(1000);
        
        // 3. 執行同步作業
        var syncResult = await RunSyncJob();
        
        // 4. 驗證同步結果
        var syncHistory = await GetSyncHistory(caseId);
        
        // Assert
        Assert.IsTrue(syncResult.SuccessCount > 0);
        Assert.IsNotNull(syncHistory);
        Assert.AreEqual("success", syncHistory.Result);
        
        // Cleanup
        await CleanupTestData(caseId);
    }
    
    [TestMethod]
    public async Task CompleteSyncFlow_WithRetry_EventuallySucceeds()
    {
        // Arrange
        // 模擬前兩次失敗，第三次成功
        ConfigureApiMockForRetry();
        
        var testCaseNo = "RETRY" + DateTime.Now.Ticks;
        var caseId = await CreateViolationCase(testCaseNo);
        
        // Act
        // 執行三次同步作業
        for (int i = 0; i < 3; i++)
        {
            await RunSyncJob();
            await Task.Delay(2000); // 等待重試延遲
        }
        
        // Assert
        var syncQueue = await GetSyncQueueItem(caseId);
        Assert.AreEqual("completed", syncQueue.Status);
        Assert.AreEqual(2, syncQueue.RetryCount); // 重試了2次
    }
}
```

## 壓力測試計畫

### 測試目標
1. 評估系統在高負載下的效能表現
2. 找出系統效能瓶頸
3. 驗證資源使用情況
4. 確認系統穩定性

### 測試場景

#### 場景1：正常負載測試
```csharp
[TestClass]
[TestCategory("Performance")]
public class NormalLoadTests
{
    [TestMethod]
    public async Task NormalLoad_1000Cases_CompletesWithinSLA()
    {
        // Arrange
        var testCases = GenerateTestCases(1000);
        var stopwatch = Stopwatch.StartNew();
        
        // Act
        foreach (var batch in testCases.Batch(50))
        {
            await ProcessBatch(batch);
        }
        
        stopwatch.Stop();
        
        // Assert
        Assert.IsTrue(stopwatch.Elapsed < TimeSpan.FromMinutes(10),
            $"處理1000筆案件耗時: {stopwatch.Elapsed}");
    }
}
```

#### 場景2：尖峰負載測試
```csharp
[TestMethod]
public async Task PeakLoad_5000ConcurrentCases_NoMemoryLeak()
{
    // Arrange
    var initialMemory = GC.GetTotalMemory(true);
    var testCases = GenerateTestCases(5000);
    
    // Act
    var tasks = testCases.Select(async testCase =>
    {
        await SimulateDataSync(testCase);
    });
    
    await Task.WhenAll(tasks);
    
    // Force garbage collection
    GC.Collect();
    GC.WaitForPendingFinalizers();
    GC.Collect();
    
    var finalMemory = GC.GetTotalMemory(true);
    
    // Assert
    var memoryIncrease = finalMemory - initialMemory;
    var memoryIncreaseMB = memoryIncrease / (1024 * 1024);
    
    Assert.IsTrue(memoryIncreaseMB < 100,
        $"記憶體增加超過預期: {memoryIncreaseMB}MB");
}
```

#### 場景3：持續負載測試
```csharp
[TestMethod]
[Timeout(3600000)] // 1小時
public async Task SustainedLoad_1HourOperation_StablePerformance()
{
    // Arrange
    var endTime = DateTime.Now.AddHours(1);
    var metrics = new List<PerformanceMetric>();
    
    // Act
    while (DateTime.Now < endTime)
    {
        var batchStopwatch = Stopwatch.StartNew();
        
        // 每分鐘處理100筆
        var batch = GenerateTestCases(100);
        await ProcessBatch(batch);
        
        batchStopwatch.Stop();
        
        metrics.Add(new PerformanceMetric
        {
            Timestamp = DateTime.Now,
            BatchSize = 100,
            Duration = batchStopwatch.Elapsed,
            MemoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024)
        });
        
        await Task.Delay(60000); // 等待1分鐘
    }
    
    // Assert
    // 驗證效能穩定性
    var avgDuration = metrics.Average(m => m.Duration.TotalSeconds);
    var maxDuration = metrics.Max(m => m.Duration.TotalSeconds);
    
    Assert.IsTrue(maxDuration < avgDuration * 1.5,
        "效能波動超過50%");
}
```

### 壓力測試工具配置

#### JMeter測試計畫
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
    <hashTree>
        <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="違建同步壓力測試">
            <stringProp name="TestPlan.comments">測試資料同步API的效能</stringProp>
        </TestPlan>
        <hashTree>
            <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="同步API測試">
                <intProp name="ThreadGroup.num_threads">100</intProp>
                <intProp name="ThreadGroup.ramp_time">60</intProp>
                <intProp name="ThreadGroup.duration">3600</intProp>
            </ThreadGroup>
            <hashTree>
                <HTTPSamplerProxy guiclass="HttpTestSampleGui" testclass="HTTPSamplerProxy">
                    <stringProp name="HTTPSampler.path">/api/sync</stringProp>
                    <stringProp name="HTTPSampler.method">POST</stringProp>
                </HTTPSamplerProxy>
            </hashTree>
        </hashTree>
    </hashTree>
</jmeterTestPlan>
```

### 效能指標監控

#### 關鍵指標
```csharp
public class PerformanceMonitor
{
    public async Task<PerformanceReport> GenerateReport()
    {
        return new PerformanceReport
        {
            // 回應時間
            AverageResponseTime = await CalculateAvgResponseTime(),
            P95ResponseTime = await CalculatePercentile(95),
            P99ResponseTime = await CalculatePercentile(99),
            
            // 吞吐量
            TransactionsPerSecond = await CalculateTPS(),
            SuccessfulTransactions = await CountSuccessful(),
            FailedTransactions = await CountFailed(),
            
            // 資源使用
            CPUUsage = await GetCPUUsage(),
            MemoryUsage = await GetMemoryUsage(),
            DatabaseConnections = await GetActiveConnections(),
            
            // 錯誤率
            ErrorRate = await CalculateErrorRate(),
            TimeoutRate = await CalculateTimeoutRate()
        };
    }
}
```

## 使用者驗收測試

### UAT測試案例

#### 測試案例1：新案件同步
| 步驟 | 動作 | 預期結果 | 實際結果 | 通過 |
|------|------|----------|----------|------|
| 1 | 在違建系統建立新案件 | 案件建立成功 | | ☐ |
| 2 | 等待30分鐘（同步週期） | 同步作業自動執行 | | ☐ |
| 3 | 查詢同步歷程 | 顯示同步成功記錄 | | ☐ |
| 4 | 至國土署系統查詢 | 可查到對應案件 | | ☐ |

#### 測試案例2：案件狀態更新
| 步驟 | 動作 | 預期結果 | 實際結果 | 通過 |
|------|------|----------|----------|------|
| 1 | 更新案件狀態從231→232 | 狀態更新成功 | | ☐ |
| 2 | 檢查同步佇列 | 產生UPDATE記錄 | | ☐ |
| 3 | 執行同步 | 同步成功 | | ☐ |
| 4 | 驗證國土署資料 | 狀態已更新 | | ☐ |

#### 測試案例3：錯誤處理驗證
| 步驟 | 動作 | 預期結果 | 實際結果 | 通過 |
|------|------|----------|----------|------|
| 1 | 模擬網路中斷 | 同步失敗 | | ☐ |
| 2 | 檢查錯誤記錄 | 記錄詳細錯誤訊息 | | ☐ |
| 3 | 恢復網路連線 | 連線恢復 | | ☐ |
| 4 | 等待重試 | 自動重試並成功 | | ☐ |

### UAT環境準備清單
- [ ] 測試環境部署完成
- [ ] 測試資料準備就緒
- [ ] 測試帳號權限設定
- [ ] 國土署測試API開通
- [ ] 監控工具配置完成
- [ ] 測試文件發放

## 測試時程規劃

### 整體時程表
```mermaid
gantt
    title 測試時程規劃
    dateFormat YYYY-MM-DD
    
    section 準備階段
    環境準備          :2025-01-15, 3d
    測試資料準備      :2025-01-17, 2d
    測試案例評審      :2025-01-19, 1d
    
    section 測試執行
    單元測試         :2025-01-20, 5d
    整合測試         :2025-01-25, 5d
    壓力測試         :2025-01-30, 3d
    UAT測試          :2025-02-02, 5d
    
    section 收尾階段
    問題修復         :2025-02-07, 3d
    回歸測試         :2025-02-10, 2d
    測試報告         :2025-02-12, 1d
```

### 里程碑
- **2025-01-20**：開始測試執行
- **2025-01-30**：功能測試完成
- **2025-02-02**：開始UAT測試
- **2025-02-12**：測試結案

## 風險評估

### 測試風險識別

| 風險項目 | 可能性 | 影響度 | 緩解措施 |
|----------|--------|--------|----------|
| 測試環境不穩定 | 中 | 高 | 建立備援環境，每日備份 |
| 測試資料不足 | 低 | 中 | 開發資料產生工具 |
| 國土署API變更 | 低 | 高 | 保持溝通，提前通知 |
| 人力資源不足 | 中 | 中 | 培訓備援人員 |
| 時程延誤 | 中 | 高 | 預留緩衝時間 |

### 風險應對計畫

#### 高風險項目處理
1. **測試環境問題**
   - 每日自動備份
   - 建立環境健康檢查
   - 準備快速復原程序

2. **API介面變更**
   - 版本控制管理
   - 建立模擬API
   - 保持雙方溝通

#### 中風險項目處理
1. **資料準備**
   - 自動化測試資料產生
   - 生產資料脫敏使用
   - 建立資料字典

2. **時程管理**
   - 每日進度追蹤
   - 問題即時上報
   - 動態調整計畫

## 測試工具與框架

### 單元測試框架
- **MSTest**：.NET原生測試框架
- **Moq**：模擬框架
- **FluentAssertions**：斷言庫

### 整合測試工具
- **Postman**：API測試
- **Newman**：API自動化測試
- **RestSharp**：HTTP客戶端

### 壓力測試工具
- **JMeter**：負載測試
- **Grafana**：效能監控
- **Application Insights**：APM工具

### 測試管理工具
- **Azure DevOps**：測試案例管理
- **Git**：版本控制
- **Jenkins**：CI/CD整合

## 測試報告模板

### 測試執行摘要
```markdown
# 測試執行報告

## 基本資訊
- 測試階段：[單元/整合/壓力/UAT]
- 測試期間：YYYY-MM-DD ~ YYYY-MM-DD
- 測試人員：[姓名]
- 環境版本：[版本號]

## 測試結果摘要
- 計畫測試案例數：[數量]
- 實際執行案例數：[數量]
- 通過案例數：[數量]
- 失敗案例數：[數量]
- 通過率：[百分比]

## 缺陷統計
- 嚴重缺陷：[數量]
- 主要缺陷：[數量]
- 次要缺陷：[數量]
- 建議改善：[數量]

## 關鍵發現
1. [重要發現1]
2. [重要發現2]
3. [重要發現3]

## 建議事項
1. [建議1]
2. [建議2]
3. [建議3]
```

## 附錄

### 測試資料SQL腳本
```sql
-- 測試資料清理
DELETE FROM sync_history WHERE case_id IN (SELECT id FROM violation_cases WHERE case_no LIKE 'TEST%');
DELETE FROM sync_queue WHERE case_id IN (SELECT id FROM violation_cases WHERE case_no LIKE 'TEST%');
DELETE FROM case_stages WHERE case_id IN (SELECT id FROM violation_cases WHERE case_no LIKE 'TEST%');
DELETE FROM violation_cases WHERE case_no LIKE 'TEST%';

-- 重置序列
ALTER SEQUENCE violation_cases_id_seq RESTART WITH 1000000;
ALTER SEQUENCE sync_queue_id_seq RESTART WITH 1000000;
```

### 效能測試查詢
```sql
-- 同步效能統計
SELECT 
    DATE(created_at) as sync_date,
    COUNT(*) as total_syncs,
    SUM(CASE WHEN result = 'success' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN result = 'failed' THEN 1 ELSE 0 END) as failed_count,
    AVG(execution_time) as avg_execution_time,
    MAX(execution_time) as max_execution_time,
    MIN(execution_time) as min_execution_time
FROM sync_history
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY sync_date DESC;

-- 佇列處理效率
SELECT 
    status,
    COUNT(*) as count,
    AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_wait_seconds,
    MAX(retry_count) as max_retries
FROM sync_queue
WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
GROUP BY status;
```

### 測試檢查清單

#### 測試前檢查
- [ ] 測試環境已就緒
- [ ] 測試資料已準備
- [ ] 測試工具已安裝
- [ ] 權限已設定
- [ ] 備份已完成

#### 測試中檢查
- [ ] 測試案例執行記錄
- [ ] 缺陷即時登記
- [ ] 環境監控正常
- [ ] 資料一致性驗證
- [ ] 效能指標收集

#### 測試後檢查
- [ ] 測試報告完成
- [ ] 缺陷已分派
- [ ] 環境已清理
- [ ] 資料已歸檔
- [ ] 經驗已總結

---

本測試計畫書為違章建築資料拋送至國土署系統專案的完整測試指南，涵蓋從單元測試到使用者驗收測試的所有階段。透過嚴謹的測試流程，確保系統的品質與穩定性。