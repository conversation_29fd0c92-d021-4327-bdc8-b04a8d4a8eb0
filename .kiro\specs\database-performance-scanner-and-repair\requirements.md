# 資料庫效能掃描與修復系統 - 需求規格書

## 功能概述

本功能旨在建立一個自動化的資料庫效能掃描與修復系統，專門針對新北市違章建築管理系統的PostgreSQL資料庫進行深度分析。系統將識別並修復導致資料庫效能問題的四大核心問題：索引缺失、N+1查詢、查詢優化不足及分頁機制缺失，確保37萬筆案件資料和100萬筆流程記錄的高效存取。

## 需求規格

### 1. 資料庫效能掃描引擎

**User Story:** 作為系統管理員，我需要一個自動化掃描工具來檢測資料庫效能問題，以便提前發現並解決潛在的效能瓶頸。

**Acceptance Criteria:**
1. 當系統啟動掃描時，系統應當自動連接到PostgreSQL主資料庫(localhost:5432/bms)和SQL Server輔助資料庫(**************:2433/ramsGIS)
2. 當執行全面掃描時，系統應當分析所有核心資料表(buildcase、tbflow、ibmcode等)的查詢模式
3. 當掃描完成時，系統應當生成詳細的效能分析報告，包含問題嚴重程度分級
4. 當發現關鍵效能問題時，系統應當立即發送告警通知給相關管理員
5. 當系統運行時，掃描過程不應當影響正常業務操作的效能

### 2. 索引缺失檢測與建議

**User Story:** 作為資料庫管理員，我需要系統自動檢測缺失的索引，以便消除全表掃描問題並提升查詢效能。

**Acceptance Criteria:**
1. 當分析查詢計劃時，系統應當識別所有導致全表掃描(Seq Scan)的查詢
2. 當檢測到頻繁查詢的欄位時，系統應當評估建立索引的必要性和效益
3. 當發現索引缺失時，系統應當自動生成CREATE INDEX的SQL建議語句
4. 當評估索引建議時，系統應當考慮查詢頻率、資料表大小和維護成本
5. 當生成索引建議時，系統應當優先處理核心業務欄位(caseopened、s_empno、case_no)

### 3. N+1查詢問題檢測

**User Story:** 作為開發人員，我需要系統檢測出程式碼中的N+1查詢模式，以便優化資料存取邏輯並減少資料庫負載。

**Acceptance Criteria:**
1. 當分析應用程式日誌時，系統應當識別迴圈中執行的重複查詢模式
2. 當檢測到N+1查詢時，系統應當記錄具體的JSP頁面和查詢語句位置
3. 當發現問題時，系統應當提供具體的修復建議(如使用JOIN替代多次查詢)
4. 當分析CodeCharge架構時，系統應當特別關注*_Handlers.jsp中的資料存取邏輯
5. 當提供修復建議時，系統應當考慮Legacy系統的架構限制

### 4. 查詢優化分析

**User Story:** 作為效能調優專家，我需要系統分析SQL查詢的執行計劃，以便識別低效查詢並提供優化建議。

**Acceptance Criteria:**
1. 當執行查詢分析時，系統應當使用PostgreSQL的EXPLAIN ANALYZE收集詳細執行統計
2. 當檢測到慢查詢時，系統應當分析查詢的成本、執行時間和資源消耗
3. 當發現優化機會時，系統應當提供具體的SQL重寫建議
4. 當分析複雜查詢時，系統應當考慮業務邏輯的正確性，不破壞原有功能
5. 當優化建議涉及資料表結構時，系統應當評估對現有觸發器(91個)的影響

### 5. 分頁機制實施

**User Story:** 作為使用者，我需要系統在載入大量資料時使用分頁機制，以便避免系統響應緩慢和記憶體溢位問題。

**Acceptance Criteria:**
1. 當檢測到列表頁面時，系統應當評估是否需要實施分頁機制
2. 當資料量超過1000筆時，系統應當強制建議實施分頁
3. 當實施分頁時，系統應當提供LIMIT/OFFSET的SQL重構建議
4. 當修改列表頁面時，系統應當保持與CodeCharge架構的相容性
5. 當實施分頁後，系統應當驗證功能完整性，確保不影響業務邏輯

### 6. 自動修復執行

**User Story:** 作為系統管理員，我需要系統能夠自動執行安全的修復操作，以便快速解決效能問題並減少人工干預。

**Acceptance Criteria:**
1. 當修復建議確認後，系統應當自動執行安全的索引建立操作
2. 當執行修復時，系統應當建立完整的回復機制和備份點
3. 當修復過程中，系統應當即時監控資料庫效能變化
4. 當發現修復風險時，系統應當立即停止操作並回復至修復前狀態
5. 當修復完成時，系統應當驗證修復效果並記錄效能改善指標

### 7. 效能監控儀表板

**User Story:** 作為運維人員，我需要一個即時的效能監控介面，以便持續追蹤資料庫健康狀況和效能趨勢。

**Acceptance Criteria:**
1. 當開啟儀表板時，系統應當顯示即時的資料庫連線數、查詢響應時間和資源使用率
2. 當效能指標異常時，系統應當在儀表板上顯示明顯的視覺告警
3. 當查看歷史趋勢時，系統應當提供可配置時間範圍的效能圖表
4. 當分析效能數據時，系統應當支援按業務模組(一般違建、廣告違建、下水道違建)分類查看
5. 當發現效能問題時，系統應當直接從儀表板連結到詳細的問題分析報告

### 8. 告警與通知系統

**User Story:** 作為資料庫管理員，我需要在效能問題發生時及時收到通知，以便快速響應並防止系統崩潰。

**Acceptance Criteria:**
1. 當資料庫效能指標超出預設閾值時，系統應當立即發送告警通知
2. 當發生嚴重效能問題時，系統應當支援多種通知方式(電子郵件、簡訊、系統通知)
3. 當設定告警規則時，系統應當允許按不同嚴重程度級別配置不同的響應策略
4. 當告警發生時，系統應當提供問題的初步診斷資訊和建議處理步驟
5. 當問題解決後，系統應當自動發送問題解決確認通知

### 9. 報告生成與匯出

**User Story:** 作為專案經理，我需要定期的效能分析報告，以便向上級匯報系統狀況並制定改善計劃。

**Acceptance Criteria:**
1. 當生成報告時，系統應當包含效能問題摘要、修復建議和執行狀況
2. 當匯出報告時，系統應當支援PDF、Excel和HTML格式
3. 當排程報告時，系統應當支援每日、每週、每月的自動報告生成
4. 當報告包含敏感資訊時，系統應當實施適當的存取控制和資料遮罩
5. 當報告涉及多個時間段時，系統應當提供效能趨勢比較和改善建議

### 10. 安全性與權限控制

**User Story:** 作為資訊安全管理員，我需要確保效能掃描系統的操作權限得到適當控制，以防止未授權的資料庫修改。

**Acceptance Criteria:**
1. 當使用者登入系統時，系統應當驗證使用者身份並檢查相應權限
2. 當執行修復操作時，系統應當要求額外的管理員授權確認
3. 當存取敏感資料時，系統應當記錄完整的操作日誌和審計追蹤
4. 當設定系統參數時，系統應當限制只有具備相應權限的使用者才能修改
5. 當系統運行時，所有操作應當遵循最小權限原則，避免過度授權風險

## 技術考量

### 相容性需求
- 必須與現有的CodeCharge Studio架構完全相容
- 支援PostgreSQL 15+和SQL Server資料庫
- 整合現有的91個資料庫觸發器機制
- 保護現有的業務邏輯和狀態轉換規則

### 效能需求
- 掃描過程不應影響正常業務操作效能超過5%
- 支援37萬筆案件資料和100萬筆流程記錄的高效分析
- 修復操作完成時間不超過預估時間的120%
- 系統響應時間在正常負載下不超過3秒

### 安全需求
- 實施完整的操作日誌和審計追蹤
- 支援角色型存取控制(RBAC)
- 加密儲存敏感的資料庫連線資訊
- 實施輸入驗證防止SQL注入攻擊