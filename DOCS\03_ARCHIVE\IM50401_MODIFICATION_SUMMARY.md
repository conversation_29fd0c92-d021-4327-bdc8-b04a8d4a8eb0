# IM50401 修正完成摘要

## 執行時間
2025-07-09

## 修正內容
已成功修改 `IM50401.java` 中的 14 個 SQL 查詢，加入銷案排除條件 `(is_closed IS NULL OR is_closed <> '1')`

### 修改的查詢欄位
1. **D20-D24**（前一月份統計）- 5 個查詢
2. **D26-D30**（前二月份統計）- 5 個查詢  
3. **D31-D34**（本月份分類合計）- 4 個查詢

## 技術細節
- 在每個 COUNT 查詢的 WHERE 條件中加入銷案排除邏輯
- 確保條件加在 `status <> '01'` 之後，維持查詢邏輯一致性
- 所有修改都遵循現有程式碼風格

## 檔案位置
- 原始碼：`/WEB-INF/java/com/ezek/report/IM50401.java`
- 備份檔：`/WEB-INF/java/com/ezek/report/IM50401.java.backup`
- 編譯後：`/WEB-INF/classes/com/ezek/report/IM50401.class`

## 下一步驟
1. 在有 Java 環境的機器上編譯程式（參考 `IM50401_COMPILE_INSTRUCTIONS.md`）
2. 部署編譯後的 class 檔案
3. 執行測試驗證統計數據正確性

## 預期結果
- 2025年6月份 D 類案件統計應為 254 筆（排除 1 筆銷案）
- 所有統計欄位都正確排除銷案案件
- 報表其他功能正常運作