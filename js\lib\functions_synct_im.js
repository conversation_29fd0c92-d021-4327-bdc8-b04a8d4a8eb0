/**
 * @author: <PERSON>
 * @created: 2021/05/03
 * 
 * @modifier: <PERSON>
 * @updated: 2021/05/05
 * @issue: Add common functions.
 * @solution: Add ezek_validateDates, ezek_prettifyDates, ezek_setAddressOption.
 * 
 * @modifier: <PERSON>, <PERSON>
 * @updated: 2021/06/02
 * @issue: 1. <PERSON> adds a common function.
 *         2. A new value for var:KEY.
 * @solution: 1. Add iniInputLinked.
 *            2. Sam changes the value of var:KEY.
 * 
 * @modifier: <PERSON>
 * @updated: 2021/06/08
 * @issue: Web console keeps outputing logs.
 * @solution: Comment off the console log.
 * 
 * @modifier: <PERSON>
 * @updated: 2021/06/10
 * @issue: The linkage action between zip code and road is not working.
 * @solution: Can not encapsulate request data in encodeURI.
 * 
 * @modifier: <PERSON>. <PERSON>
 * @updated: 2021/06/15
 * @issue: Add new linkage action for 認定號碼. It links to its label(for viewing) and actual 
 *         inputs.
 * @solution: None.
 * 
 * @modifier: <PERSON>
 * @updated: 2021/06/18
 * @issue: Function ezek_setAddressOption is not working when using call back function.
 * @solution: The call back function should place inside the ajax.complete.
 * 
 * @modifier: <PERSON> C. Fan
 * @updated: 2021/10/05
 * @comment: I disclaim being the modifier before 2021/10/05.
 * @issue: Client requests the functionality of inputting 起始文號號碼未滿10碼補0補滿 and when blur,
 *         auto fill 迄止文號號碼未滿10碼補9補滿.
 * @solution: Overload function inputsLinked with 1 extra parameter to achieve it.
 * 
 * @modifier: 
 * @updated: YYYY/MM/DD
 * @issue: 
 * @solution: 
 **/

var KEY = "2050600_1676";
var TARGET_CITY = "新北市";
var REQUEST_URL_PREFIX = location.protocol + "//www.gis.ntpc.gov.tw/Func_Service/QueryFastLocAPI.aspx?Key=" + KEY;
// 行政區
var REQUEST_URL_district = REQUEST_URL_PREFIX + "&Cmd=getTownList";
// 地段
var REQUEST_URL_landSection = REQUEST_URL_PREFIX + "&Cmd=getLandSection";
// 路街段
var REQUEST_URL_road = REQUEST_URL_PREFIX + "&Cmd=getAddressRoad";
// 巷
var REQUEST_URL_lane = REQUEST_URL_PREFIX + "&Cmd=getAddressLane";
// 弄
var REQUEST_URL_alley = REQUEST_URL_PREFIX +"&Cmd=getAddressLong";

/**
 * Validate the specific date field.
 * 
 * @param {String} formId Form ID.
 * @param {String} dates  Single date field or multiple date fields, which are separated by semicolon.
 * 
 * @return {Boolean} It will alert when encounting invalid date.
 **/
function ezek_validateDates(formId, dates) {
	var datesArray = dates.split(";"), numOfDateFields = datesArray.length / 2, elements = [], elementNames = [], idx = 0, numOfElements = 0, elementName = "", dateField = {};

	for (idx = 0; idx < numOfDateFields; idx++) {
		elements.push( datesArray[(idx * 2)] );
		elementNames.push( datesArray[(idx * 2 + 1)] );
	}

	numOfElements = elements.length;
	for (idx = 0; idx < numOfElements; idx++) {
		dateField = document.getElementById(formId).elements.namedItem( elements[idx] );
		if (!IsCDate(dateField.value)) {
			alert("欄位 " + elementNames[idx] + " 的日期不正確.");
			dateField.select();
			return false;
		}
	}

	return true;
}

/**
 * Validate the specific date field in pair.
 * 
 * @param {String} formId Form ID.
 * @param {Object} dates  An array of date field.
 * 
 * @return {Boolean} It will alert when encounting invalid date.
 **/
function ezek_validateBetweenDates(formId, dates) {
	var dateObj = {}, numOfComparison = dates.length;
	var earlierDateFieldVal = "", laterDateFieldVal = "";
	var bdate_dig = 0, edate_dig = 0;
	
	for (var idx = 0; idx < numOfComparison; idx++) {
		dateObj = dates[idx];
		
		earlierDateFieldVal = document.getElementById(formId).elements.namedItem( dateObj.field1 ).value;
		laterDateFieldVal = document.getElementById(formId).elements.namedItem( dateObj.field2 ).value;
		
		if (earlierDateFieldVal !== "" && laterDateFieldVal !== "") {
			bdate_dig = +(earlierDateFieldVal.replace(/\//g, ""));
			edate_dig = +(laterDateFieldVal.replace(/\//g, ""));
			
			if (bdate_dig > edate_dig) {
				alert("欄位 " + dateObj.field1Caption + " 的日期須早於欄位 " + dateObj.field2Caption + " 的日期.");
				return false;
			}
		}
	}

	return true;
}

/**
 * Make the date field value into yyy/mm/dd style.
 * 
 * @param {String} formState Form content state.
 * @param {String} formId    Form ID.
 * @param {String} dates     Single date field or multiple date fields, which are separated by semicolon.
 **/
function ezek_prettifyDates(formState, formId, dates) {
	var elements = dates.split(";"), numOfElements = elements.length, idx = 0, dateField = {}, loop = 0;
	
	switch (formState) {
	case "OnLoad":
		var padding0Str = "";
		
		for (idx = 0; idx < numOfElements; idx++) {
			dateField = document.getElementById(formId).elements.namedItem( elements[idx] );
			// Only when having value with no date delimiter, then format the date with the date delimiter
			if (dateField.value.length > 0 && dateField.value.indexOf("/") === -1) {
				padding0Str = "";
				for (loop = 0; loop < (7 - dateField.value.length); loop++) padding0Str += "0";
				// Pad 0 at the front
				dateField.value = padding0Str + dateField.value;
				dateField.value = RSplitChar(dateField.value, "/", "2,2");
			}
		}
		break;
	case "OnSubmit":
		for (idx = 0; idx < numOfElements; idx++) {
			dateField = document.getElementById(formId).elements.namedItem( elements[idx] );
			dateField.value = EraseChar(dateField.value, "/");
		}
		break;
	default:
		var dateParts = [];
		
		for (idx = 0; idx < numOfElements; idx++) {
			dateField = document.getElementById(formId).elements.namedItem( elements[idx] );
			dateParts = dateField.value.split("/");
			if (dateParts.length === 3) {
				for (loop = dateParts[0].length; loop < 3; loop++) dateParts[0] = "0" + dateParts[0]; // Taiwan year
				for (loop = dateParts[1].length; loop < 2; loop++) dateParts[1] = "0" + dateParts[1]; // month
				for (loop = dateParts[2].length; loop < 2; loop++) dateParts[2] = "0" + dateParts[2]; // day
				// Compose date as year/month/day format
				dateField.value = dateParts[0] + "/" + dateParts[1] + "/" + dateParts[2];
			}
		}
		
		dateParts.length = 0
		break;
	}
}

/**
 * Format date according to the desire delimiter style.
 * 
 * @param {String} srcStr    Raw date string
 * @param {String} dateType  Type of date in yyyymmdd/yyymmdd/yyyymm/yyymm/mmdd
 * @param {String} delimiter Desire delimiter style
 * 
 * @return {String} Formatted date with the delimiter
 **/
function ezek_formatDate(srcStr, dateType, delimiter) {
	var finalStr = "";
	
	if (arguments.length === 3) {
		var readyForDelimiter = false, dateStyle = dateType.toUpperCase();
		
		switch (dateStyle) {
			case "YYYYMMDD":
				if (srcStr.length === 8) {
					readyForDelimiter = true;
				}
				break;
			case "YYYMMDD":
				if (srcStr.length >= 5 && srcStr.length <= 7) {
					srcStr = srcStr.padStart(7, "0");
					readyForDelimiter = true;
				}
				break;
			case "YYYYMM":
				if (srcStr.length >= 3 && srcStr.length <= 6) {
					srcStr = srcStr.padStart(6, "0");
					readyForDelimiter = true;
				}
				break;
			case "YYYMM":
				if (srcStr.length >= 3 && srcStr.length <= 5) {
					srcStr = srcStr.padStart(5, "0");
					readyForDelimiter = true;
				}
				break;
			case "MMDD":
				if (srcStr.length === 4) {
					readyForDelimiter = true;
				}
				break;
		} // End of SWITCH: (dateStyle)
		
		if (readyForDelimiter) {
			var delimiterType = delimiter.toUpperCase();

			switch (dateStyle) {
				case "YYYYMMDD":
				case "YYYMMDD":
					if (delimiterType === "MANDARIN") {
						finalStr = srcStr.substring(0, (srcStr.length - 4)) + "年" + srcStr.substring((srcStr.length - 4), (srcStr.length - 2)) + "月" + srcStr.substring((srcStr.length - 2)) + "日";
					} else {
						finalStr = srcStr.substring(0, (srcStr.length - 4)) + delimiter + srcStr.substring((srcStr.length - 4), (srcStr.length - 2)) + delimiter + srcStr.substring((srcStr.length - 2));
					}
					break;
				case "YYYYMM":
				case "YYYMM":
					if (delimiterType === "MANDARIN") {
						finalStr = srcStr.substring(0, (srcStr.length - 2)) + "年" + srcStr.substring((srcStr.length - 2)) + "月";
					} else {
						finalStr = srcStr.substring(0, (srcStr.length - 2)) + delimiter + srcStr.substring((srcStr.length - 2));
					}
					break;
				case "MMDD":
					if (delimiterType === "MANDARIN") {
						finalStr = srcStr.substring(0, (srcStr.length - 2)) + "月" + srcStr.substring((srcStr.length - 2)) + "日";
					} else {
						finalStr = srcStr.substring(0, (srcStr.length - 2)) + delimiter + srcStr.substring((srcStr.length - 2));
					}
					break;
			}
		} // End of IF: (readyForDelimiter)
	} // End of IF: (arguments.length === 3)
	
	return finalStr;
}

/**
 * Validate the specific numeric field.
 * 
 * @param {String} formId   Form ID.
 * @param {Object} numerics An array of numeric fields.
 * 
 * @return {Boolean} It will alert when encounting invalid numeric.
 **/
function ezek_validateNumericFields(formId, numerics) {
	var numericObj = {}, numOfComparison = numerics.length;
	var numericFieldLowerVal = "", numericFieldHigherVal = "";
	var lowerValReady = false, higherValReady = false;
	var lowerVal_dig = 0, higherVal_dig = 0;
	
	for (var idx = 0; idx < numOfComparison; idx++) {
		numericObj = numerics[idx];
		
		numericFieldLowerVal = document.getElementById(formId).elements.namedItem( numericObj.field1 ).value;
		if (numericFieldLowerVal !== "") {
			numericFieldLowerVal = numericFieldLowerVal.replace(/,/g, "");
			if (isNumber(numericFieldLowerVal)) {
				lowerValReady = true;
			} else {
				alert("欄位 " + numericObj.field1Caption + " 是無效的. 請使用以下格式: 浮點/整數.");
				return false;
			}
		}

		numericFieldHigherVal = document.getElementById(formId).elements.namedItem( numericObj.field2 ).value;
		if (numericFieldHigherVal !== "") {
			numericFieldHigherVal = numericFieldHigherVal.replace(/,/g, "");
			if (isNumber(numericFieldHigherVal)) {
				higherValReady = true;
			} else {
				alert("欄位 " + numericObj.field2Caption + " 是無效的. 請使用以下格式: 浮點/整數.");
				return false;
			}
		}

		if (lowerValReady && higherValReady) {
			lowerVal_dig = +(numericFieldLowerVal);
			higherVal_dig = +(numericFieldHigherVal);
			
			if (lowerVal_dig > higherVal_dig) {
				alert("欄位 " + numericObj.field1Caption + " 的值不得大於欄位 " + numericObj.field2Caption + " 的值.");
				return false;
			}
		}
		
		// Reset for the next iteration
		lowerValReady = false;
		higherValReady = false;
	}

	return true;
}

/**
 * Validate the string is a numeric value or not.
 * 
 * @param n: the numeric string
 * 
 * @return: true or false
 **/
function isNumber(n) {
	return !isNaN(parseFloat(n)) && isFinite(n);
}

/**
 * Padding 0 to the front of the string to the desire number.
 * 
 * @param {String}          srcStr    Raw string
 * @param {String, Integer} numOfZero Number of zero at the front of the string
 * 
 * @return {String} Formatted string with padding zero at the front
 **/
function ezek_padZero(srcStr, numOfZero) {
	return srcStr.padStart(+numOfZero, "0");
}

function ezek_padEndZero(srcStr, numOfZero) {
	return srcStr.padEnd(+numOfZero, "0");
}

function ezek_padEndNine(srcStr, numOfZero) {
	return srcStr.padEnd(+numOfZero, "9");
}


/**
 * Dynamically set up the options of a specific address listbox.
 *
 * @param {String} args1   Address type.
 * @param {Object} args2   Target listbox.
 * @param {Object} [args3] An Object; it may be the address search condition or a function.
 * @param {Object} [args4] An Object; usually a function or the name of the function.
 **/
function ezek_setAddressOption() {
	var url = "", nodeName = "";
	var type = arguments[0], listbox = arguments[1];
	var data = {}, callback = {};
	
	switch (type) {
	case "district": // 行政區
		url = REQUEST_URL_district;
		nodeName = "TOWNSHIP";
		break; 
	case "landSection": // 地段
		url = REQUEST_URL_landSection;
		nodeName = "SECTION";
		break;
	case "road": // 路街段
		url = REQUEST_URL_road;
		nodeName = "ROAD";
		break;
	case "lane": // 巷
		url = REQUEST_URL_lane;
		nodeName = "LANE";
		break;
	case "alley": // 弄
		url = REQUEST_URL_alley;
		nodeName = "LONG";
		break;
	}
	
	switch (arguments.length) {
	case 3:
		if (typeof arguments[2] === "object") {
			data = arguments[2];
		} else {
			callback = arguments[2];
		}
		break;
	case 4:
		data = arguments[2];
		callback = arguments[3];
		break;
	}

	listbox.prop("disabled", true).trigger("chosen:updated");
	
	$.ajax({
		url: encodeURI(url), 
		type: "GET", 
		dataType: "xml", 
		data: data, 
		success: function(data) {
			//DEBUG console.log(data);
			var newOptions = [];
			
			$(data).find(nodeName).each(function() {
				newOptions.push({ boundColumn: $(this).attr("DATA"), textColumn: $(this).text() });
			});
			
			listbox.fillOptions(newOptions);
		}, 
		error: function(xhr, status, error) {
			console.error("functions_synct_im.js ::: ezek_setAddressOption ::: + " + error);
		}, 
		complete: function(xhr, status) {
			listbox.prop("disabled", false).trigger("chosen:updated");
			
			if (typeof callback === "function") {
				callback();
			}
		}
	});
}

/**
 * 把對應欄位帶值
 *  @param {String} args1   Address type.
 **/
function iniInputLinked(_id1, _id2, _num) {
	_num = (typeof _num !== "undefined") ? _num : 0;
	
	$("#" + _id1).off().on("focusout", function() {
		var inputValue = ezek_padZero($(this).val(),  _num);

		$("#" + _id1).val(inputValue);
		$("#" + _id2).val(inputValue);
	});
}

//--------
// Yao
// 110/09/24
// 前面補0後面補9
//--------
function iniInputLinked_jur(_id1, _id2, _num) {
	_num = (typeof _num !== "undefined") ? _num : 0;
	$("#" + _id1).on("focusout", function() {
		if( $(this).val() ){
			var inputValue = ezek_padEndZero($(this).val(),  _num);
			var inputValue_E = ezek_padEndNine($(this).val(),  _num);
			
			$("#" + _id1).val(inputValue);
			$("#" + _id2).val(inputValue_E);
		}
		
	});
}

function inputsLinked(inputFieldId, viewFieldId, fieldId_reg_yy, fieldId_reg_no) {
	var thisVal = "", composeRegistrationNumberResult = {};
	
	viewFieldId = "#" + viewFieldId;
	
	composeRegistrationNumberResult = composeRegistrationNumber("");
	if (composeRegistrationNumberResult.complete) {
		$(viewFieldId).removeClass("regnum-incomplete");
	} else {
		if (!$(viewFieldId).hasClass("regnum-incomplete")) {
			$(viewFieldId).addClass("regnum-incomplete");
		}
	}
	$(viewFieldId).text(composeRegistrationNumberResult.val);
	
	$("#" + inputFieldId).off().on("input", function() {
		thisVal = $(this).val();
		
		composeRegistrationNumberResult = composeRegistrationNumber(thisVal);
		if (composeRegistrationNumberResult.complete) {
			$(viewFieldId).removeClass("regnum-incomplete");
		} else {
			if (!$(viewFieldId).hasClass("regnum-incomplete")) {
				$(viewFieldId).addClass("regnum-incomplete");
			}
		}
		$(viewFieldId).text(composeRegistrationNumberResult.val);
		autoFillin(fieldId_reg_yy, fieldId_reg_no, thisVal);
	});
}

/**
 * When inputting 起始文號號碼 field, make the inputting value to its corresponding fields. When 
 * focusing out, it will pad 0 to fill up to 10 characters, and 迄止號號碼 field will pad 9 to fill 
 * up to 10 characters.
 * 
 * @param {String} inputFieldId      起始文號號碼 field ID.
 * @param {String} viewFieldId       起始文號號碼 viewing label.
 * @param {String} fieldId_reg_yy    起始文號號碼-年度 field ID.
 * @param {String} fieldId_reg_no    起始文號號碼-流水號 field ID.
 * @param {String} respondingFieldId 迄止文號號碼 field ID.
 **/
function inputsLinked(inputFieldId, viewFieldId, fieldId_reg_yy, fieldId_reg_no, respondingFieldId) {
	var thisVal = "", composeRegistrationNumberResult = {}, 
		NUMBER_OF_SPACE_TO_FILL = 10;
	
	viewFieldId = "#" + viewFieldId;
	
	composeRegistrationNumberResult = composeRegistrationNumber("");
	if (composeRegistrationNumberResult.complete) {
		$(viewFieldId).removeClass("regnum-incomplete");
	} else {
		if (!$(viewFieldId).hasClass("regnum-incomplete")) {
			$(viewFieldId).addClass("regnum-incomplete");
		}
	}
	$(viewFieldId).text(composeRegistrationNumberResult.val);
	
	$("#" + inputFieldId).off().on({
		input: function() {
			thisVal = $(this).val();
			
			composeRegistrationNumberResult = composeRegistrationNumber(thisVal);
			if (composeRegistrationNumberResult.complete) {
				$(viewFieldId).removeClass("regnum-incomplete");
			} else {
				if (!$(viewFieldId).hasClass("regnum-incomplete")) {
					$(viewFieldId).addClass("regnum-incomplete");
				}
			}
			$(viewFieldId).text(composeRegistrationNumberResult.val);
			autoFillin(fieldId_reg_yy, fieldId_reg_no, thisVal);
		}, 
		blur: function() {
			thisVal = $(this).val();

			if (respondingFieldId !== "" && thisVal.length > 0) {
				// Fillup 起始文號號碼 with 0 to 10 characters
				var automateNum = ezek_padEndZero(thisVal, NUMBER_OF_SPACE_TO_FILL);
				
				composeRegistrationNumberResult = composeRegistrationNumber(automateNum);
				if (composeRegistrationNumberResult.complete) {
					$(viewFieldId).removeClass("regnum-incomplete");
				} else {
					if (!$(viewFieldId).hasClass("regnum-incomplete")) {
						$(viewFieldId).addClass("regnum-incomplete");
					}
				}
				$(viewFieldId).text(composeRegistrationNumberResult.val);
				autoFillin(fieldId_reg_yy, fieldId_reg_no, automateNum);
				
				$("#" + inputFieldId).val(automateNum);
				// Fillup 迄止文號號碼 with 9 to 10 characters
				$("#" + respondingFieldId).val(ezek_padEndNine(thisVal, NUMBER_OF_SPACE_TO_FILL)).trigger("input");
			}
		}
	});
}

/**
 * Compose 文號號碼 in ###-####### style.
 *
 * @param {String} str 文號號碼.
 * 
 * @return {Boolean} True when 文號號碼 is 10-character long.
 * @return {String}  Formatted 文號號碼.
 **/
function composeRegistrationNumber(str) {
	var UNDERSCORE = "_", HYPHEN = "－", MAX_REGNUM = 10, flag = true;
	var inputsArray = str.split(""), inputsArrayLen = inputsArray.length;
	var idx = 0;
	var registrationNumber = "";
	
	// Fillin numbers
	for (idx = 0; idx < inputsArrayLen; idx++) {
		registrationNumber += inputsArray[idx];
	}
	// Fillin underscore
	for (idx = inputsArrayLen; idx < MAX_REGNUM; idx++) {
		registrationNumber += UNDERSCORE;
		flag = false;
	}
	// Insert hyphen
	registrationNumber = registrationNumber.substring(0, 3) + HYPHEN + registrationNumber.substring(3);

	return {
		complete: flag,
		val: registrationNumber
	};
}

/**
 * Auto write 文號號碼 to corresponding fields.
 *
 * @param {String} fieldId_reg_yy 文號-年度 field ID.
 * @param {String} fieldId_reg_no 文號-流水號 field ID.
 * @param {String} regnum         文號號碼.
 **/
function autoFillin(fieldId_reg_yy, fieldId_reg_no, regnum) {
	var reg_yy = "", reg_no = "";
	var regnumLen = regnum.length;
	
	$("#" + fieldId_reg_yy + ", #" + fieldId_reg_no).val("");
	
	if (regnumLen < 4) {
		$("#" + fieldId_reg_yy).val(regnum);
	} else {
		$("#" + fieldId_reg_yy).val( regnum.substring(0, 3) );
		$("#" + fieldId_reg_no).val( regnum.substring(3) );
	}
} 


