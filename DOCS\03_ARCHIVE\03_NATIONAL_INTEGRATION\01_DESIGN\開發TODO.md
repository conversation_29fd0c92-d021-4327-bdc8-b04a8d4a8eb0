# 違章建築資料拋送至國土署系統 - 開發TODO清單

## 📋 專案總覽
- **專案名稱**：違章建築資料拋送系統
- **技術架構**：.NET 8 + PostgreSQL + Windows Service
- **預計工期**：8-10週
- **團隊規模**：3-4名開發人員

## 🎯 里程碑規劃

### Phase 1: 基礎建設（第1-2週）
- [ ] 環境設置與專案初始化
- [ ] 資料庫架構實作
- [ ] 核心Domain模型建立

### Phase 2: 核心功能開發（第3-5週）
- [ ] Repository層實作
- [ ] 資料轉換引擎開發
- [ ] API客戶端開發

### Phase 3: 系統整合（第6-7週）
- [ ] Windows Service開發
- [ ] 監控系統實作
- [ ] 錯誤處理機制

### Phase 4: 測試與部署（第8-10週）
- [ ] 完整測試執行
- [ ] 效能優化
- [ ] 正式環境部署

---

## 🏗️ Phase 1: 基礎建設

### 1.1 環境設置
- [ ] 建立Git repository
- [ ] 設定CI/CD pipeline
- [ ] 建立開發、測試、正式環境
- [ ] 安裝必要開發工具
  - [ ] Visual Studio 2022
  - [ ] PostgreSQL 15
  - [ ] pgAdmin 4
  - [ ] Postman

### 1.2 專案架構
```
- [ ] 建立解決方案結構
      ViolationSync/
      ├── ViolationSync.Domain/
      ├── ViolationSync.Infrastructure/
      ├── ViolationSync.Application/
      ├── ViolationSync.API/
      ├── ViolationSync.Service/
      └── ViolationSync.Tests/
```

### 1.3 資料庫實作
- [ ] 建立PostgreSQL資料庫
- [ ] 執行DDL腳本建立表格
  - [ ] violation_cases
  - [ ] case_stages  
  - [ ] sync_queue
  - [ ] sync_history
  - [ ] audit_log
- [ ] 建立索引
- [ ] 實作稽核觸發器
- [ ] 建立資料視圖
- [ ] 準備測試資料

### 1.4 基礎設施
- [ ] 設定appsettings.json
- [ ] 實作Configuration管理
- [ ] 設定Dependency Injection
- [ ] 設定Logging (Serilog)
- [ ] 實作Exception Handling middleware

---

## 💻 Phase 2: 核心功能開發

### 2.1 Domain層
- [ ] 建立Entity類別
  - [ ] ViolationCase
  - [ ] CaseStage
  - [ ] SyncQueueItem
  - [ ] SyncHistory
  - [ ] AuditLog
- [ ] 定義Enum類型
  - [ ] CaseType
  - [ ] StageType
  - [ ] SyncStatus
  - [ ] OperationType
- [ ] 建立ValueObject
  - [ ] CaseNumber
  - [ ] Address
  - [ ] PersonalId

### 2.2 Repository層
- [ ] 實作基礎Repository介面
  - [ ] IRepository<T>
  - [ ] IUnitOfWork
- [ ] 實作具體Repository
  - [ ] CaseRepository
    - [ ] GetByIdAsync()
    - [ ] GetByCaseNoAsync()
    - [ ] CreateAsync()
    - [ ] UpdateAsync()
    - [ ] GetPendingSyncAsync()
  - [ ] SyncQueueRepository
    - [ ] EnqueueAsync()
    - [ ] DequeueBatchAsync()
    - [ ] UpdateStatusAsync()
    - [ ] IncrementRetryAsync()
  - [ ] AuditRepository
    - [ ] GetAuditHistoryAsync()
    - [ ] GetAuditAtDateAsync()
- [ ] 實作Dapper連線管理
- [ ] 實作交易處理

### 2.3 資料轉換引擎
- [ ] 實作DataMapper基礎類別
- [ ] 建立欄位對應設定
  - [ ] 基本資料對應
  - [ ] 階段資料對應
  - [ ] 附件資料對應
- [ ] 實作資料驗證器
  - [ ] 必填欄位檢核
  - [ ] 格式驗證（身分證、電話等）
  - [ ] 業務邏輯驗證
- [ ] 實作資料清理器
  - [ ] 特殊字元處理
  - [ ] 全形半形轉換
  - [ ] 地址標準化

### 2.4 API客戶端
- [ ] 實作HttpClient Factory
- [ ] 建立API客戶端介面
  - [ ] IAuthenticationClient
  - [ ] ICaseApiClient
  - [ ] IFileUploadClient
- [ ] 實作OAuth 2.0認證
  - [ ] Token取得
  - [ ] Token更新
  - [ ] Token快取
- [ ] 實作API呼叫方法
  - [ ] CreateCase()
  - [ ] UpdateCaseStatus()
  - [ ] UploadFile()
  - [ ] BatchSync()
- [ ] 實作Polly重試政策
- [ ] 實作Circuit Breaker

---

## 🔧 Phase 3: 系統整合

### 3.1 Windows Service
- [ ] 建立Windows Service專案
- [ ] 實作BackgroundService
- [ ] 設定Hangfire排程
  - [ ] 定期同步作業
  - [ ] 重試作業
  - [ ] 清理作業
- [ ] 實作服務生命週期管理
- [ ] 加入健康檢查端點

### 3.2 同步引擎
- [ ] 實作SyncOrchestrator
  - [ ] 讀取待同步佇列
  - [ ] 批次處理邏輯
  - [ ] 並行處理控制
- [ ] 實作錯誤處理策略
  - [ ] 暫時性錯誤重試
  - [ ] 永久性錯誤標記
  - [ ] 告警通知
- [ ] 實作同步狀態追蹤
- [ ] 實作效能監控

### 3.3 監控系統
- [ ] 整合Prometheus
  - [ ] 設定metrics收集
  - [ ] 自訂業務指標
- [ ] 建立Grafana儀表板
  - [ ] 系統健康度
  - [ ] 同步成功率
  - [ ] 效能指標
- [ ] 實作告警規則
  - [ ] 系統異常告警
  - [ ] 業務異常告警
- [ ] 整合LINE Notify

### 3.4 管理介面
- [ ] 建立Web管理介面
  - [ ] 同步狀態查詢
  - [ ] 錯誤記錄查看
  - [ ] 手動觸發同步
  - [ ] 系統設定管理
- [ ] 實作RESTful API
  - [ ] GET /api/sync/status
  - [ ] GET /api/sync/history
  - [ ] POST /api/sync/trigger
  - [ ] GET /api/system/health

---

## 🧪 Phase 4: 測試與部署

### 4.1 單元測試
- [ ] Repository層測試
  - [ ] Mock資料庫連線
  - [ ] 測試CRUD操作
  - [ ] 測試交易處理
- [ ] Service層測試
  - [ ] DataMapper測試
  - [ ] 驗證器測試
  - [ ] API客戶端測試
- [ ] 達到80%程式碼覆蓋率

### 4.2 整合測試
- [ ] 資料庫整合測試
  - [ ] 觸發器測試
  - [ ] 交易測試
- [ ] API整合測試
  - [ ] Mock API Server
  - [ ] 端對端流程測試
- [ ] 錯誤處理測試

### 4.3 壓力測試
- [ ] 設定JMeter測試腳本
- [ ] 執行負載測試
  - [ ] 正常負載（1000筆/小時）
  - [ ] 尖峰負載（5000筆/小時）
- [ ] 分析效能瓶頸
- [ ] 優化資料庫查詢
- [ ] 優化並行處理

### 4.4 部署準備
- [ ] 準備部署文件
- [ ] 建立部署腳本
- [ ] 設定環境變數
- [ ] 準備資料庫遷移腳本
- [ ] 建立備份還原程序

### 4.5 正式部署
- [ ] 執行部署前檢查
- [ ] 備份現有系統
- [ ] 部署資料庫
  - [ ] 執行DDL
  - [ ] 建立初始資料
- [ ] 部署應用程式
  - [ ] 安裝Windows Service
  - [ ] 設定IIS（如需要）
- [ ] 執行部署後驗證
- [ ] 監控系統狀態

---

## 📊 進度追蹤

### 每日站立會議檢查項目
- [ ] 昨日完成項目
- [ ] 今日預計項目
- [ ] 遇到的障礙

### 每週進度檢核
- [ ] 完成度評估
- [ ] 風險識別
- [ ] 下週計劃調整

### 里程碑檢核點
- [ ] Phase 1完成：基礎架構ready
- [ ] Phase 2完成：核心功能可demo
- [ ] Phase 3完成：系統整合測試通過
- [ ] Phase 4完成：正式上線

---

## 🚨 風險管理

### 技術風險
- [ ] 國土署API規格變更
- [ ] 大量資料同步效能問題
- [ ] 網路不穩定導致同步失敗

### 時程風險
- [ ] 需求變更
- [ ] 測試發現重大問題
- [ ] 部署環境準備延遲

### 緩解措施
- [ ] 每週與國土署確認API規格
- [ ] 及早進行壓力測試
- [ ] 準備rollback計劃

---

## 📝 備註

### 開發規範
- 遵循C# Coding Standards
- 所有public方法需有XML註解
- 單元測試需涵蓋所有public方法
- Code Review後才可合併至main

### 版本控制
- Feature branch命名：feature/功能名稱
- Commit message格式：[類型] 簡短描述
- 每個PR需至少一位reviewer

### 文件要求
- API文件使用Swagger自動產生
- 維護CHANGELOG.md
- 更新部署手冊

---

## ✅ 完成標準

### Definition of Done
- [ ] 程式碼已撰寫完成
- [ ] 單元測試已通過
- [ ] Code Review已完成
- [ ] 整合測試已通過
- [ ] 文件已更新
- [ ] 部署至測試環境成功

### 專案完成標準
- [ ] 所有功能開發完成
- [ ] 測試覆蓋率達80%
- [ ] 壓力測試達標
- [ ] 監控系統正常運作
- [ ] 正式環境部署成功
- [ ] 交付所有文件

---

最後更新時間：2024-01-08
負責人：開發團隊