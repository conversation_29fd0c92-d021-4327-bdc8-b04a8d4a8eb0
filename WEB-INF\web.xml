<?xml version="1.0"?>
<!DOCTYPE web-app PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN" "http://java.sun.com/dtd/web-app_2_3.dtd">
<!-- //webApp configuration head @1-AFEB8EC7 -->
<web-app>
	<context-param>
		<param-name>quartz:config-file</param-name>
		<param-value>quartz.properties</param-value>
	</context-param>
	<context-param>
		<param-name>quartz:shutdown-on-unload</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
		<param-name>quartz:wait-on-shutdown</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
		<param-name>quartz:start-on-load</param-name>
		<param-value>true</param-value>
	</context-param>
	<filter>
		<filter-name>panelFilter</filter-name>
		<filter-class>com.codecharge.util.filters.PanelFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<filter>
		<filter-name>includeFilter</filter-name>
		<filter-class>com.codecharge.util.filters.IncludeFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
	</filter>
	<!-- 【安全修復】主機標頭注入防護過濾器 -->
	<!-- COMMENTED OUT DUE TO MISSING CLASS
	<filter>
		<filter-name>hostHeaderFilter</filter-name>
		<filter-class>com.ezek.report.ContentSecurityPolicyFilter</filter-class>
		<init-param>
			<param-name>allowedHosts</param-name>
			<param-value>localhost,127.0.0.1,building.ntpc.gov.tw</param-value>
		</init-param>
	</filter>
	-->
	<filter-mapping>
		<filter-name>panelFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>includeFilter</filter-name>
		<url-pattern>*.jsp</url-pattern>
	</filter-mapping>
	<!-- COMMENTED OUT DUE TO MISSING CLASS
	<filter-mapping>
		<filter-name>hostHeaderFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	-->
	<listener>
		<listener-class>org.quartz.ee.servlet.QuartzInitializerListener</listener-class>
	</listener>
	<servlet>
		<servlet-name>siteInit</servlet-name>
		<servlet-class>com.codecharge.InitServlet</servlet-class>
		<init-param>
			<param-name>configuration.file</param-name>
			<param-value>/WEB-INF/site.properties</param-value>
		</init-param>
		<init-param>
			<param-name>languages.file</param-name>
			<param-value>/WEB-INF/languages.properties</param-value>
		</init-param>
		<init-param>
			<param-name>localesInfo.file</param-name>
			<param-value>/WEB-INF/localesInfo.properties</param-value>
		</init-param>
		<init-param>
			<param-name>controllerHandlerClassName</param-name>
			<param-value>com.codecharge.util.DefaultControllerHandler</param-value>
		</init-param>
		<init-param>
			<param-name>usedWarFile</param-name>
			<param-value>false</param-value>
		</init-param>
		<init-param>
			<param-name>convertReturnLinkToAbsolute</param-name>
			<param-value>false</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet>
		<servlet-name>ClientI18N</servlet-name>
		<servlet-class>com.codecharge.GetFileServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>ClientI18N</servlet-name>
		<url-pattern>/ClientI18N.jsp</url-pattern>
	</servlet-mapping>
	<welcome-file-list>
		<welcome-file>login.jsp</welcome-file>
	</welcome-file-list>
	<taglib>
		<taglib-uri>/ccstags</taglib-uri>
		<taglib-location>/WEB-INF/CCStags.tld</taglib-location>
	</taglib>
</web-app>