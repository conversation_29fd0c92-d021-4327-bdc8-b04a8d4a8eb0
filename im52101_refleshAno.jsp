<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.db.*,com.codecharge.validation.*"%>
<%@page import="java.sql.PreparedStatement"%>
<%@page import="java.io.*, org.apache.commons.io.IOUtils"%>
<%@page import="java.sql.*, java.util.Date, org.json.simple.*"%>
<%@page import="javax.servlet.*, java.net.URLDecoder" %>
<%@page import="com.oreilly.servlet.MultipartRequest, java.nio.file.*" %>

<%!
    // Constants
    private static final String CONNECTION_NAME = "DBConn";

    // Abstract base class for file handlers
    private abstract class FileHandler {
        protected final String fileId;
        protected final Connection conn;
        
        public FileHandler(String fileId, Connection conn) {
            this.fileId = fileId;
            this.conn = conn;
        }
        
        // Template method pattern
        public final String process() throws Exception {
            ResultSet rs = null;
            PreparedStatement pstmt = null;
            try {
                DbRow data = fetchFileData();
                if (data == null) {
                    return buildDefaultHtml();
                }
                String filePath = getFilePath(data);
                if (StringUtils.isEmpty(filePath)) {
                    return buildDefaultHtml();
                }
                String[] fileDetails = getFileDetails(filePath);
              
                return buildHtml(fileDetails[0], fileDetails[1]);
            } catch (Exception e) {
                System.err.println("Error in FileHandler.process: " + e.getMessage());
                e.printStackTrace();
                throw e;
            } finally {
                if (rs != null) try { rs.close(); } catch (SQLException e) {}
                if (pstmt != null) try { pstmt.close(); } catch (SQLException e) {}
            }
        }
        
        protected abstract String getQuery();
        protected abstract String getFilePathColumn();
        protected abstract String getDivId();
        
        protected DbRow fetchFileData() throws Exception {
            PreparedStatement pstmt = null;
            ResultSet rs = null;
            
            try {
              
                pstmt = conn.prepareStatement(getQuery());
                pstmt.setString(1, fileId);
                rs = pstmt.executeQuery();
                
                if (rs.next()) {
                    DbRow row = new DbRow();
                    String columnValue = rs.getString(getFilePathColumn());
                    row.put(getFilePathColumn(), columnValue);
                    return row;
                }
                return null;

            } finally {
                if (rs != null) try { rs.close(); } catch (SQLException e) {}
                if (pstmt != null) try { pstmt.close(); } catch (SQLException e) {}
            }
        }
        
        protected String getFilePath(DbRow data) {
            Object value = data.get(getFilePathColumn());
            return value == null ? "" : value.toString().trim();
        }
        
        protected String[] getFileDetails(String filePath) {
            if (StringUtils.isEmpty(filePath)) {
                return new String[]{"", ""};
            }
            try {
                File file = new File(filePath);
                if (!file.exists()) {
                    System.err.println("File does not exist: " + filePath);
                    return new String[]{"", ""};
                }
                String fileName = file.getName();
                int dotIndex = fileName.lastIndexOf(".");
                if (dotIndex > 0) {
                    return new String[]{
                        fileName.substring(0, dotIndex),
                        fileName.substring(dotIndex + 1)
                    };
                }
                return new String[]{fileName, ""};
            } catch (Exception e) {
                System.err.println("Error in getFileDetails: " + e.getMessage());
                e.printStackTrace();
                return new String[]{"", ""};
            }
        }
        
        protected String buildDefaultHtml() {
            return String.format(
                "<div><a href='#' class='pointer' onclick=\"addUPD('%s','%s');\">上傳檔案</a></div></li></div>",
                fileId, getClass().getSimpleName().toLowerCase().replace("filehandler", "")
            );
        }
        
        protected String buildHtml(String showName, String fileKind) {
            if (StringUtils.isEmpty(showName)) {
                return buildDefaultHtml();
            }
            return String.format(
                "<div id='%s'>%s.%s</div>",
                getDivId(), showName, fileKind
            );
        }
    }
    
    // Concrete implementation for Excel files
    private class ExcelFileHandler extends FileHandler {
        public ExcelFileHandler(String fileId, Connection conn) {
            super(fileId, conn);
        }
        
        @Override
        protected String getQuery() {
            return "SELECT original_file_name, stored_file_path FROM im52101_excel_imports WHERE import_id::text = ?";
        }
        
        @Override
        protected String getFilePathColumn() {
            return "stored_file_path";
        }
        
        @Override
        protected String getDivId() {
            return "showExcelName";
        }
    }
%>

<%
    // Input validation
    String fileId = request.getParameter("id"); 

    DBConnectionManager dbcm = null;
    Connection conn = null;
    String htmlResponse = "";
    
    try {
        // 驗證fileId參數
        if (fileId == null || fileId.trim().isEmpty()) {
            throw new IllegalArgumentException("檔案ID不能為空");
        }
        
        // 檢查fileId是否為有效的UUID格式
        try {
            java.util.UUID.fromString(fileId);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("檔案ID格式無效，必須是有效的UUID");
        }
        
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection(CONNECTION_NAME);
        
        // 使用適當的處理器處理檔案
        FileHandler handler = new ExcelFileHandler(fileId, conn);
        htmlResponse = handler.process();
        
    } catch (IllegalArgumentException e) {
        System.err.println("Invalid argument: " + e.getMessage());
        e.printStackTrace();
        response.sendError(HttpServletResponse.SC_BAD_REQUEST, "無效的參數: " + e.getMessage());
        return;
        
    } catch (Exception e) {
        System.err.println("Error in file processing: " + e.getMessage());
        e.printStackTrace();
        application.log("Error in file processing: " + e.getMessage(), e);
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        htmlResponse = "<div class='error'>處理檔案時發生錯誤: " + e.getMessage() + "</div>";
        
    } finally {
        if (conn != null) {
            try {
                dbcm.freeConnection(CONNECTION_NAME, conn);
            } catch (Exception e) {
                application.log("Error closing database connection: " + e.getMessage(), e);
            }
        }
    }
    
    // Output response
    out.print(htmlResponse);
%>