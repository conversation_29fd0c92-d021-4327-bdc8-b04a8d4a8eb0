﻿/**
 * @author: <PERSON>
 * @created: 2021/06/01
 * @comment: Copied from in101_man_3.js and made minimum changes to allow it to work.
 * 
 * @modifier: 
 * @updated: YYYY/MM/DD
 * @issue: 
 * @solution: 
 
 110/08/02 
 http 改 HTTPS
 去掉桃園 tycg箱關聯結
 **/

var markerPic;
var WGS84_point, XY_point;
var gsvc, wgs84SR, twd97SR, webMercatorSR;
var currentCoordinate;
var addressMarker, graphic;
var showText, graphic_text, containText;
var geocoder;	
var ezekMap;									//Google Geocoder
var sys_arcgisMapServiceURL = location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/";
var token="17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token="+token, 
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token="+token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer", 
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token="+token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token="+token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";
var orthoimageName = "正射影像圖";
var scopeLayerName = "行政區域圖";
var landLayerName = "地籍圖";
var bcmsLayerName = "地籍套繪圖";
function ezek_InitMap() {
    require([
        "esri/map",
        "esri/dijit/Print",
        "esri/geometry/Point",
        "esri/geometry/webMercatorUtils",
        "esri/graphic",
        "esri/Color",
        "esri/symbols/PictureMarkerSymbol",
        "esri/symbols/SimpleMarkerSymbol",
        "esri/symbols/SimpleLineSymbol",
        "esri/symbols/TextSymbol",
        "esri/symbols/Font",
        "esri/toolbars/edit",
        "esri/tasks/PrintTemplate",
        "esri/tasks/GeometryService",
        "esri/SpatialReference",
		"esri/dijit/Search",
        "dojo/_base/event",
        "dojo/dom",
        "dojo/on",
        "dojo/parser",
        "dojo/domReady!"
    ], function(Map, Print, Point, WebMercatorUtils, Graphic, Color, PictureMarkerSymbol, SimpleMarkerSymbol, SimpleLineSymbol, TextSymbol, Font, Edit, PrintTemplate
	, GeometryService, SpatialReference,Search, event, dom, on, parser) {
        parser.parse();

		var addrToal = decodeURI(getUrlParameter("ADDR"));
        var WGS84_point = new Point();
        showText = new TextSymbol(" ").setColor(new Color([128, 0, 0])).setAlign(Font.ALIGN_START).setFont(new Font("24pt")).setOffset(0, 60);
        markerPic = new PictureMarkerSymbol(  location.protocol+'//icdc.ntpc.gov.tw/ntpcin/img/focus.png?1071030', 30, 30);

        esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";

        //設定座標轉換服務
		var sys_arcgisMapService = location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/";
        gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");

        wgs84SR = new SpatialReference({wkid: 4326}); //WGS84
        twd97SR = new SpatialReference({wkid: 102443}); //TWD_97_TM2
        webMercatorSR = new SpatialReference({wkid: 102100}); //WGS 1984 Web Mercator Projection
		//載入Google Geocoder(地址定位使用)
		geocoder = new google.maps.Geocoder();
        ezekMap = new Map("map_canvas", {
            logo: false,
            slider: true,
            sliderStyle: "small",
            sliderPosition: "top-left",
            spatialReference: {
                wkid: 102443
            },
            minZoom: 3,
            maxZoom: 12 // Limit the zoom number; prevent extreme zooming to have no map image
        });
		
        //var tiledMap = new esri.layers.ArcGISDynamicMapServiceLayer(mapUrl, { id: "baseMap" });
	
      
		var tiledMap = null,TgosLayer_F2 = null;
		tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
			"opacity": 1,
			id: normalTileName2
		});
        ezekMap.addLayer(tiledMap, 0);
		
		// 1: 1/1000以上 電子地圖
		TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
			"opacity": 1,
			id: normalTileName1
		});
		ezekMap.addLayer(TgosLayer_F2, 1);

		//--------
		// search
		//--------
		
        on(ezekMap, "load", function() {
            // initialize; store the coordinate before any editing
            currentCoordinate = [$("[name=lng]").val(), $("[name=lat]").val()];
            var ZOOML = $("[name=ZOOML]").val();

            // place the infowindow at the specific position
            ezekMap.infoWindow.anchor = "top";
            ezekMap.infoWindow.offsetY = 10;
            editToolbar = new Edit(ezekMap);

            var int_ZOOML = 10;
            if ($("[name=lng]").val() == "" || $("[name=lat]").val() == "") {
                $("[name=lng]").val("121.43124831920882");
                $("[name=lat]").val("25.00038938975144");
				//25.00038938975144, 121.43124831920882
                int_ZOOML = 3;

            }
            if ($("[name=lng]").val() !== "" && $("[name=lat]").val() !== "") {
                var longitude = parseFloat($("[name=lng]").val()),
                    latitude = parseFloat($("[name=lat]").val());

             WGS84_point_Zoom(longitude, latitude);

            }
        });//END  on(ezekMap, "load", function() 
		
		//-----
		// zoom 經緯度
		//----
		function WGS84_point_Zoom( _longitude, _latitude){
			
			  currentCoordinate[0] = parseFloat(_longitude.toFixed(8));
                currentCoordinate[1] = parseFloat(_latitude.toFixed(8));
                $("[name=X_COORDINATE]").val(currentCoordinate[0]);
                $("[name=Y_COORDINATE]").val(currentCoordinate[1]);
                showInfoWindow();
			var int_ZOOML = 10;    
			var ZOOML = $("[name=ZOOML]").val();
			if (ZOOML) int_ZOOML = parseFloat(ZOOML);
	
			WGS84_point.setLongitude(_longitude).setLatitude(_latitude);
			
			coordToMapSR_geometry(WGS84_point, function(data) {
				XY_point = new Point(data.x, data.y, ezekMap.spatialReference);
				graphic = new Graphic(XY_point, markerPic);
				addressMarker = new Graphic(XY_point, markerPic);
				
				ezekMap.graphics.add(addressMarker);				
				ezekMap.graphics.add(graphic);
				/*setTimeout(function() {
					editToolbar.activate(Edit.MOVE, graphic);
					detectGraphicMovement();
				}, 1500);*/
				centerAndZoom(int_ZOOML);
			});
		}
        
        function detectGraphicMovement() {

            editToolbar.on("graphic-first-move", function(evt) {
                ezek_removeLocMarker();
                ezekMap.infoWindow.hide();
            });

            editToolbar.on("graphic-move-stop", function(evt) {
                $.ajaxSettings.async = false;
                var longitude = evt.graphic.geometry.x,
                    latitude = evt.graphic.geometry.y;
                XY_point.x = longitude;
                XY_point.y = latitude;
                setLngLat(longitude, latitude);

                setTimeout(function() {
                    centerAndZoom(ezekMap.getZoom());
                }, 500);

                $.ajaxSettings.async = true;
            });

        }

        //-------
        // 
        //------
        function setLngLat(TMD_97_X, TMD_97_Y) {

            ezek_MapSRtoWGS84(TMD_97_X, TMD_97_Y, function(data) {
                currentCoordinate[0] = parseFloat(data.x.toFixed(8));
                currentCoordinate[1] = parseFloat(data.y.toFixed(8));
                $("[name=X_COORDINATE]").val(currentCoordinate[0]);
                $("[name=Y_COORDINATE]").val(currentCoordinate[1]);
                showInfoWindow();
            });

        }

        //-------
        // 
        //------
        function showInfoWindow() {
            /*
            ezekMap.infoWindow.resize(160, 60);
            ezekMap.infoWindow.setTitle("座標");
            ezekMap.infoWindow.setContent( "經度：" + currentCoordinate[0] + "<br>緯度：" + currentCoordinate[1] );
            ezekMap.infoWindow.show(XY_point, ezekMap.getInfoWindowAnchor(XY_point));
            */

            /*
            if (graphic_text != null) ezekMap.graphics.remove(graphic_text);
            containText =  currentCoordinate[0] + " E, " + currentCoordinate[1] + " N"  ;
				
            showText.setText(containText);
            graphic_text = new esri.Graphic(XY_point, showText);
            ezekMap.graphics.add(graphic_text);
            */
            containText = "違建位置座標：" + currentCoordinate[0] + " E, " + currentCoordinate[1] + " N";
            $("#showLatLng").text(containText);
        }
        //-------
        // 
        //------
        function centerAndZoom(zoomLevel) {
            ezekMap.centerAndZoom(XY_point, zoomLevel)
                .then(function() {
                    showInfoWindow();
                });

        }
    });
}

	
//------------------------------------------------------------------------
// 分別清除圖標
//------------------------------------------------------------------------

function ezek_removeLocMarker() {
    if (addressMarker != null) ezekMap.graphics.remove(addressMarker);
    if (graphic_text != null) ezekMap.graphics.remove(graphic_text);

    //if (landNumMarker != null) ezekMap.graphics.remove(landNumMarker);

}
//-------
// 按鈕 存檔 onclick();
//------
function getCoordinate_new() {
    // 經度
    //window.opener.$("[name=lng]").val( currentCoordinate[0] );
    // 緯度
    //window.opener.$("[name=lat]").val( currentCoordinate[1] );

    // close this window
    //window.close();
    var zoomLevel = ezekMap.getZoom();
    parent.fnParent(currentCoordinate[0], currentCoordinate[1], zoomLevel, 'Y');
    distroyView();
}
//-------
// 按鈕 取消 onclick();
//------
function distroyView() {
    parent.$.fancybox.close();
}

//------------------------------------------------------------------------
// 地址定位圖標  wgs84  googleSearchBox
//------------------------------------------------------------------------
function return_geometry_wgs84(loc) {
    var pt;
    require(["esri/geometry/webMercatorUtils", "esri/geometry/Point", "esri/graphic"], function(WebMercatorUtils, Point, Graphic) {
        var twdloc = WebMercatorUtils.lngLatToXY(loc.x, loc.y);
        pt = new Point(twdloc[0], twdloc[1], ezekMap.spatialReference);

    });

    return pt;
}

//------------------------------------------------------------------------
// 將google的底圖使用的座標值(twd97SR)轉為經緯度座標
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function ezek_MapSRtoWGS84(sx, sy, callback) {
    require(["esri/geometry/Point", "esri/tasks/ProjectParameters"], function(Point, ProjectParameters) {
        var inPoint = new Point(sx, sy, twd97SR);
        var prjParams = new ProjectParameters();
        prjParams.geometries = [inPoint];
        prjParams.outSR = wgs84SR;
        gsvc.project(prjParams, function(pt) {
            var outPoint = pt[0];
            this.x = outPoint.x;
            this.y = outPoint.y;
            callback(this);
        }, showErr);
    });
}
//------------------------------------------------------------------------
// 將geometry物件轉為底圖使用的座標系統
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function coordToMapSR_geometry(inGeometry, callback) {
    require(["esri/geometry/Geometry", "esri/tasks/ProjectParameters", "esri/tasks/GeometryService"], function(Geometry, ProjectParameters, GeometryService) {
	 
	 var prjParams = new ProjectParameters();
        prjParams.geometries = [inGeometry];
        prjParams.outSR = ezekMap.spatialReference;
       
	   gsvc.project(prjParams, function(et) {
            callback(et[0]);
        }, showErr);
    });
}
//------------------------------------------------------------------------
// 顯示錯誤訊息
//------------------------------------------------------------------------
function showErr(err) {
    alert(err.name + " : " + err.message);
}
//-----------------------------------------------------------------------------
// 抓取指定URL參數值
//-----------------------------------------------------------------------------
function getUrlParameter(parameterName) {
	parameterName = parameterName.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");  
	var regexS = "[\\?&]" + parameterName + "=([^&" + "#]*)";    
	var regex = new RegExp(regexS);
	var results = regex.exec(window.location.href);
	if (results == null) {
		return "";
	} else {
		return results[1];
	}
}

function showLoading() {
    $.ajaxSettings.async = false;
    $.blockUI({
        message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;存檔中, 請稍候...',
        css: {
            border: 'none',
            padding: '6px',
            backgroundColor: '#000',
            '-webkit-border-radius': '10px',
            '-moz-border-radius': '10px',
            opacity: .5,
            color: '#FFF'
        }
    });
    $.ajaxSettings.async = true;
}

function closeLoading() {
    $.unblockUI();
}