<%@page pageEncoding="utf-8"%><%--== Handlers ==--%> <%--im52101_upload Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

//Feature checker Head @1-6387CEAC
    public class im52101_uploadService<PERSON>he<PERSON> implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//im52101_upload Page Handler Head @1-C2764F14
    public class im52101_uploadPageHandler implements PageListener {
//End im52101_upload Page Handler Head
//im52101_upload BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End im52101_upload BeforeInitialize Method Head

//im52101_upload BeforeInitialize Method Tail @1-FCB6E20C
        }
//End im52101_upload BeforeInitialize Method Tail

//im52101_upload AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End im52101_upload AfterInitialize Method Head
       if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True") {
                e.getPage().setRedirectString("timeout_err.jsp");
            }  
//Event AfterInitialize Action Custom Code @11-44795B7A
	String fileId = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("id"));
	
	
	if (!StringUtils.isEmpty(fileId))
                SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("id", fileId);

//End Event AfterInitialize Action Custom Code

//im52101_upload AfterInitialize Method Tail @1-FCB6E20C
        }
//End im52101_upload AfterInitialize Method Tail

//im52101_upload OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End im52101_upload OnInitializeView Method Head

//im52101_upload OnInitializeView Method Tail @1-FCB6E20C
        }
//End im52101_upload OnInitializeView Method Tail

//im52101_upload BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End im52101_upload BeforeShow Method Head

//im52101_upload BeforeShow Method Tail @1-FCB6E20C
        }
//End im52101_upload BeforeShow Method Tail

//im52101_upload BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End im52101_upload BeforeOutput Method Head

//im52101_upload BeforeOutput Method Tail @1-FCB6E20C
        }
//End im52101_upload BeforeOutput Method Tail

//im52101_upload BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End im52101_upload BeforeUnload Method Head

//im52101_upload BeforeUnload Method Tail @1-FCB6E20C
        }
//End im52101_upload BeforeUnload Method Tail

//im52101_upload onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End im52101_upload onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//im52101_upload onCache Method Tail @1-FCB6E20C
        }
//End im52101_upload onCache Method Tail

//im52101_upload Page Handler Tail @1-FCB6E20C
    }
//End im52101_upload Page Handler Tail

//fileupload Record Handler Head @2-C90ABCF3
    public class im52101_uploadfileuploadRecordHandler implements RecordListener, RecordDataObjectListener {
//End fileupload Record Handler Head

//fileupload afterInitialize Method Head @2-89E84600
        public void afterInitialize(Event e) {
//End fileupload afterInitialize Method Head

//fileupload afterInitialize Method Tail @2-FCB6E20C
        }
//End fileupload afterInitialize Method Tail

//fileupload OnSetDataSource Method Head @2-9B7FBFCF
        public void onSetDataSource(DataObjectEvent e) {
//End fileupload OnSetDataSource Method Head

//fileupload OnSetDataSource Method Tail @2-FCB6E20C
        }
//End fileupload OnSetDataSource Method Tail

//fileupload BeforeShow Method Head @2-46046458
        public void beforeShow(Event e) {
//End fileupload BeforeShow Method Head

//Event BeforeShow Action Custom Code @9-44795B7A

	
	String fileId = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("id"));   
	String fileType = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("t")); 
	String uploadTitle = "附件";  
	
	
	e.getRecord().getControl("uploadTitle").setValue(uploadTitle);


//End Event BeforeShow Action Custom Code

//fileupload BeforeShow Method Tail @2-FCB6E20C
        }
//End fileupload BeforeShow Method Tail

//fileupload OnValidate Method Head @2-5F430F8E
        public void onValidate(Event e) {
//End fileupload OnValidate Method Head

//fileupload OnValidate Method Tail @2-FCB6E20C
        }
//End fileupload OnValidate Method Tail

//fileupload BeforeSelect Method Head @2-E5EC9AD3
        public void beforeSelect(Event e) {
//End fileupload BeforeSelect Method Head

//fileupload BeforeSelect Method Tail @2-FCB6E20C
        }
//End fileupload BeforeSelect Method Tail

//fileupload BeforeBuildSelect Method Head @2-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End fileupload BeforeBuildSelect Method Head

//fileupload BeforeBuildSelect Method Tail @2-FCB6E20C
        }
//End fileupload BeforeBuildSelect Method Tail

//fileupload BeforeExecuteSelect Method Head @2-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End fileupload BeforeExecuteSelect Method Head

//fileupload BeforeExecuteSelect Method Tail @2-FCB6E20C
        }
//End fileupload BeforeExecuteSelect Method Tail

//fileupload AfterExecuteSelect Method Head @2-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End fileupload AfterExecuteSelect Method Head

//fileupload AfterExecuteSelect Method Tail @2-FCB6E20C
        }
//End fileupload AfterExecuteSelect Method Tail

//fileupload BeforeInsert Method Head @2-75B62B83
        public void beforeInsert(Event e) {
//End fileupload BeforeInsert Method Head

//fileupload BeforeInsert Method Tail @2-FCB6E20C
        }
//End fileupload BeforeInsert Method Tail

//fileupload BeforeBuildInsert Method Head @2-FD6471B0
        public void beforeBuildInsert(DataObjectEvent e) {
//End fileupload BeforeBuildInsert Method Head

//fileupload BeforeBuildInsert Method Tail @2-FCB6E20C
        }
//End fileupload BeforeBuildInsert Method Tail

//fileupload BeforeExecuteInsert Method Head @2-4EB41272
        public void beforeExecuteInsert(DataObjectEvent e) {
//End fileupload BeforeExecuteInsert Method Head

//fileupload BeforeExecuteInsert Method Tail @2-FCB6E20C
        }
//End fileupload BeforeExecuteInsert Method Tail

//fileupload AfterExecuteInsert Method Head @2-C4572C5E
        public void afterExecuteInsert(DataObjectEvent e) {
//End fileupload AfterExecuteInsert Method Head

//fileupload AfterExecuteInsert Method Tail @2-FCB6E20C
        }
//End fileupload AfterExecuteInsert Method Tail

//fileupload AfterInsert Method Head @2-767A9165
        public void afterInsert(Event e) {
//End fileupload AfterInsert Method Head

//fileupload AfterInsert Method Tail @2-FCB6E20C
        }
//End fileupload AfterInsert Method Tail

//fileupload BeforeUpdate Method Head @2-33A3CFAC
        public void beforeUpdate(Event e) {
//End fileupload BeforeUpdate Method Head

//fileupload BeforeUpdate Method Tail @2-FCB6E20C
        }
//End fileupload BeforeUpdate Method Tail

//fileupload BeforeBuildUpdate Method Head @2-37688606
        public void beforeBuildUpdate(DataObjectEvent e) {
//End fileupload BeforeBuildUpdate Method Head

//fileupload BeforeBuildUpdate Method Tail @2-FCB6E20C
        }
//End fileupload BeforeBuildUpdate Method Tail

//fileupload BeforeExecuteUpdate Method Head @2-84B8E5C4
        public void beforeExecuteUpdate(DataObjectEvent e) {
//End fileupload BeforeExecuteUpdate Method Head

//fileupload BeforeExecuteUpdate Method Tail @2-FCB6E20C
        }
//End fileupload BeforeExecuteUpdate Method Tail

//fileupload AfterExecuteUpdate Method Head @2-0E5BDBE8
        public void afterExecuteUpdate(DataObjectEvent e) {
//End fileupload AfterExecuteUpdate Method Head

//fileupload AfterExecuteUpdate Method Tail @2-FCB6E20C
        }
//End fileupload AfterExecuteUpdate Method Tail

//fileupload AfterUpdate Method Head @2-306F754A
        public void afterUpdate(Event e) {
//End fileupload AfterUpdate Method Head

//fileupload AfterUpdate Method Tail @2-FCB6E20C
        }
//End fileupload AfterUpdate Method Tail

//fileupload BeforeDelete Method Head @2-752E3118
        public void beforeDelete(Event e) {
//End fileupload BeforeDelete Method Head

//fileupload BeforeDelete Method Tail @2-FCB6E20C
        }
//End fileupload BeforeDelete Method Tail

//fileupload BeforeBuildDelete Method Head @2-01A46505
        public void beforeBuildDelete(DataObjectEvent e) {
//End fileupload BeforeBuildDelete Method Head

//fileupload BeforeBuildDelete Method Tail @2-FCB6E20C
        }
//End fileupload BeforeBuildDelete Method Tail

//fileupload BeforeExecuteDelete Method Head @2-B27406C7
        public void beforeExecuteDelete(DataObjectEvent e) {
//End fileupload BeforeExecuteDelete Method Head

//fileupload BeforeExecuteDelete Method Tail @2-FCB6E20C
        }
//End fileupload BeforeExecuteDelete Method Tail

//fileupload AfterExecuteDelete Method Head @2-389738EB
        public void afterExecuteDelete(DataObjectEvent e) {
//End fileupload AfterExecuteDelete Method Head

//fileupload AfterExecuteDelete Method Tail @2-FCB6E20C
        }
//End fileupload AfterExecuteDelete Method Tail

//fileupload AfterDelete Method Head @2-76E28BFE
        public void afterDelete(Event e) {
//End fileupload AfterDelete Method Head

//fileupload AfterDelete Method Tail @2-FCB6E20C
        }
//End fileupload AfterDelete Method Tail

//fileupload Record Handler Tail @2-FCB6E20C
    }
//End fileupload Record Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-38A8EE97
    Page im52101_uploadModel = (Page)request.getAttribute("im52101_upload_page");
    Page im52101_uploadParent = (Page)request.getAttribute("im52101_uploadParent");
    if (im52101_uploadModel == null) {
        PageController im52101_uploadCntr = new PageController(request, response, application, "/im52101_upload.xml" );
        im52101_uploadModel = im52101_uploadCntr.getPage();
        im52101_uploadModel.setRelativePath("./");
        //if (im52101_uploadParent != null) {
            //if (!im52101_uploadParent.getChild(im52101_uploadModel.getName()).isVisible()) return;
        //}
        im52101_uploadModel.addPageListener(new im52101_uploadPageHandler());
        ((Record)im52101_uploadModel.getChild("fileupload")).addRecordListener(new im52101_uploadfileuploadRecordHandler());
        im52101_uploadCntr.process();
%>
<%
        if (im52101_uploadParent == null) {
            im52101_uploadModel.setCookies();
            if (im52101_uploadModel.redirect()) return;
        } else {
            im52101_uploadModel.redirect();
        }
    }
//End Processing

%>
