# 化石化系統維護指南 (Maintenance Guide)

## 🎯 指南目標

為化石化系統建立標準化維護流程，確保在CodeCharge Studio無法重新生成的情況下，安全有效地進行系統維護和功能擴展。

## 📋 維護原則

### 核心原則
1. **非破壞性** - 永遠不修改核心架構
2. **可回復性** - 所有變更都可以復原
3. **漸進式** - 小步快跑，逐步改善
4. **文檔化** - 記錄所有變更和決策
5. **測試先行** - 任何變更都要充分測試

### 禁止事項 🚫
- 修改CodeCharge核心類別庫
- 刪除或重命名現有JSP主檔案
- 變更資料庫主鍵結構
- 移除現有Handler邏輯
- 修改XML配置的核心元素

## 🔧 日常維護流程

### 1. 功能新增流程

#### Step 1: 需求分析
```markdown
## 需求分析範本

**需求描述**：
- 功能目標：
- 使用者角色：
- 操作流程：

**技術評估**：
- 是否有相似功能可複製：[ ]
- 是否需要新增資料表：[ ]
- 是否需要修改現有邏輯：[ ]
- 預估複雜度：[低/中/高]

**風險評估**：
- 對現有功能影響：[無/低/中/高]
- 技術難度：[低/中/高]
- 測試複雜度：[低/中/高]
```

#### Step 2: 尋找相似功能
```bash
#!/bin/bash
# 相似功能搜尋腳本

echo "=== 尋找相似功能 ==="

# 1. 搜尋相似的檔案名稱
find . -name "*${KEYWORD}*" -type f

# 2. 搜尋相似的功能描述
grep -r "${KEYWORD}" --include="*.jsp" --include="*.java"

# 3. 分析檔案結構
ls -la *${SIMILAR_MODULE}*

echo "找到的相似模組："
echo "1. ${MODULE_1}"
echo "2. ${MODULE_2}"
echo "3. ${MODULE_3}"
```

#### Step 3: 安全複製
```bash
#!/bin/bash
# 安全複製腳本

SOURCE_MODULE="im10101"
TARGET_MODULE="im10102"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "=== 開始複製模組 ${SOURCE_MODULE} → ${TARGET_MODULE} ==="

# 1. 建立備份
mkdir -p backup/${TIMESTAMP}
cp -r ${SOURCE_MODULE}* backup/${TIMESTAMP}/

# 2. 複製檔案
cp ${SOURCE_MODULE}_man.jsp ${TARGET_MODULE}_man.jsp
cp ${SOURCE_MODULE}_man.xml ${TARGET_MODULE}_man.xml
cp ${SOURCE_MODULE}_manHandlers.jsp ${TARGET_MODULE}_manHandlers.jsp

# 3. 參數替換
sed -i "s/${SOURCE_MODULE}/${TARGET_MODULE}/g" ${TARGET_MODULE}_man.jsp
sed -i "s/${SOURCE_MODULE}/${TARGET_MODULE}/g" ${TARGET_MODULE}_man.xml
sed -i "s/${SOURCE_MODULE}/${TARGET_MODULE}/g" ${TARGET_MODULE}_manHandlers.jsp

echo "複製完成，請檢查檔案："
ls -la ${TARGET_MODULE}*
```

#### Step 4: 參數客製化
**必須修改的參數清單**：
```javascript
// JSP檔案中需要修改的參數
1. 頁面標題 <title>標籤
2. 模組ID變數
3. 資料表名稱
4. 欄位名稱對應
5. 顯示標籤文字
6. 驗證規則參數
7. 權限檢查條件
```

### 2. Bug修復流程

#### Step 1: 問題確認
```markdown
## Bug報告範本

**Bug描述**：
- 問題現象：
- 重現步驟：
- 預期結果：
- 實際結果：

**環境資訊**：
- 瀏覽器版本：
- 使用者角色：
- 資料範例：

**影響評估**：
- 影響使用者數：
- 業務影響程度：[低/中/高/緊急]
- 資料完整性風險：[無/低/中/高]
```

#### Step 2: 問題定位
```bash
#!/bin/bash
# Bug定位輔助腳本

BUG_MODULE="im10101"
ERROR_KEYWORD="錯誤關鍵字"

echo "=== Bug定位分析 ==="

# 1. 檢查相關檔案
echo "相關檔案："
find . -name "*${BUG_MODULE}*"

# 2. 搜尋錯誤關鍵字
echo "錯誤相關代碼："
grep -n "${ERROR_KEYWORD}" ${BUG_MODULE}*.jsp ${BUG_MODULE}*.java

# 3. 檢查日誌
echo "最近錯誤日誌："
tail -100 logs/catalina.out | grep -i error

# 4. 檢查資料庫操作
echo "相關資料庫操作："
grep -n "DBTools\|SQLException" ${BUG_MODULE}*
```

#### Step 3: 安全修復
```bash
#!/bin/bash
# 安全修復流程

FILE_TO_FIX="${BUG_MODULE}_manHandlers.jsp"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "=== 開始修復 ${FILE_TO_FIX} ==="

# 1. 建立備份
cp ${FILE_TO_FIX} ${FILE_TO_FIX}.backup.${TIMESTAMP}

# 2. 記錄修改前狀態
wc -l ${FILE_TO_FIX}
md5sum ${FILE_TO_FIX}

# 3. 進行修復（手動）
echo "請手動修復檔案，修復完成後按Enter繼續..."
read

# 4. 記錄修改後狀態
wc -l ${FILE_TO_FIX}
md5sum ${FILE_TO_FIX}

# 5. 產生修復記錄
cat > fix_record_${TIMESTAMP}.md << EOF
## Bug修復記錄

**修復時間**: ${TIMESTAMP}
**修復檔案**: ${FILE_TO_FIX}
**修復內容**: [請描述修復內容]
**測試結果**: [請記錄測試結果]
**影響評估**: [請評估修復影響]

**回復指令**: 
cp ${FILE_TO_FIX}.backup.${TIMESTAMP} ${FILE_TO_FIX}
EOF

echo "修復完成，記錄檔案：fix_record_${TIMESTAMP}.md"
```

### 3. 效能優化流程

#### 資料庫查詢優化
```java
// Handler檔案中的常見優化模式

// 優化前：多次資料庫查詢
String name = DBTools.dLookUp("NAME", "IBMCASE", "CASE_ID='"+caseId+"'", "DBConn");
String addr = DBTools.dLookUp("ADDRESS", "IBMCASE", "CASE_ID='"+caseId+"'", "DBConn");
String date = DBTools.dLookUp("CREATE_DATE", "IBMCASE", "CASE_ID='"+caseId+"'", "DBConn");

// 優化後：單次查詢
ResultSet rs = DBTools.executeQuery("SELECT NAME, ADDRESS, CREATE_DATE FROM IBMCASE WHERE CASE_ID='"+caseId+"'", "DBConn");
if (rs.next()) {
    String name = rs.getString("NAME");
    String addr = rs.getString("ADDRESS");
    String date = rs.getString("CREATE_DATE");
}
rs.close();
```

#### 前端效能優化
```javascript
// 前端效能優化檢查清單

// 1. 減少DOM操作
// 優化前：
$('#field1').val(data.field1);
$('#field2').val(data.field2);
$('#field3').val(data.field3);

// 優化後：
var $form = $('#myForm');
$form.find('#field1').val(data.field1);
$form.find('#field2').val(data.field2);
$form.find('#field3').val(data.field3);

// 2. 使用事件委派
// 優化前：
$('.button').click(function() { ... });

// 優化後：
$(document).on('click', '.button', function() { ... });

// 3. 延遲載入
// 大型資料表分頁載入
```

## 🛠️ 維護工具箱

### 1. 檔案管理工具
```bash
#!/bin/bash
# 檔案管理工具集

# 檔案備份
backup_file() {
    local file=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    cp "$file" "$file.backup.$timestamp"
    echo "已備份：$file.backup.$timestamp"
}

# 檔案差異比較
compare_files() {
    local file1=$1
    local file2=$2
    echo "=== 檔案差異比較 ==="
    diff -u "$file1" "$file2" | head -50
}

# 尋找相關檔案
find_related() {
    local module=$1
    echo "=== ${module} 相關檔案 ==="
    find . -name "*${module}*" -type f | sort
}
```

### 2. 程式碼分析工具
```bash
#!/bin/bash
# 程式碼分析工具

# 分析JSP檔案複雜度
analyze_jsp() {
    local file=$1
    echo "=== JSP檔案分析：$file ==="
    echo "總行數：$(wc -l < "$file")"
    echo "Java代碼行數：$(grep -c '<% \|%>' "$file")"
    echo "HTML行數：$(grep -c '<[^%]' "$file")"
    echo "JavaScript行數：$(grep -c 'function\|var ' "$file")"
}

# 檢查SQL注入風險
check_sql_injection() {
    local file=$1
    echo "=== SQL注入風險檢查：$file ==="
    grep -n "'+.*+'" "$file" || echo "未發現明顯SQL注入風險"
}

# 檢查XSS風險
check_xss() {
    local file=$1
    echo "=== XSS風險檢查：$file ==="
    grep -n "innerHTML\|document.write" "$file" || echo "未發現明顯XSS風險"
}
```

### 3. 測試輔助工具
```bash
#!/bin/bash
# 測試輔助工具

# 功能測試檢查清單
test_function() {
    local module=$1
    echo "=== 功能測試：$module ==="
    
    # 檢查頁面載入
    echo "[ ] 頁面正常載入"
    echo "[ ] 無JavaScript錯誤"
    echo "[ ] CSS樣式正確"
    
    # 檢查基本功能
    echo "[ ] 表單驗證正常"
    echo "[ ] 資料儲存成功"
    echo "[ ] 查詢功能正常"
    echo "[ ] 權限控制正確"
    
    # 檢查整合功能
    echo "[ ] 與其他模組整合正常"
    echo "[ ] 資料庫操作無錯誤"
    echo "[ ] 日誌記錄正確"
}

# 效能測試
performance_test() {
    local url=$1
    echo "=== 效能測試：$url ==="
    curl -o /dev/null -s -w "時間: %{time_total}s, 大小: %{size_download}bytes\n" "$url"
}
```

## 📊 維護記錄範本

### 功能新增記錄
```markdown
# 功能新增記錄

## 基本資訊
- **日期**: YYYY/MM/DD
- **開發者**: [姓名]
- **功能模組**: [模組ID]
- **需求來源**: [需求單號]

## 技術實作
- **來源模組**: [複製來源]
- **修改檔案**: 
  - [ ] XXX.jsp
  - [ ] XXX.xml  
  - [ ] XXXHandlers.jsp
- **新增檔案**:
  - [ ] XXX.js
  - [ ] XXX.css

## 主要變更
1. [變更1說明]
2. [變更2說明]
3. [變更3說明]

## 測試結果
- [ ] 單元測試通過
- [ ] 整合測試通過
- [ ] 使用者驗收測試通過
- [ ] 效能測試通過

## 部署資訊
- **測試環境部署**: YYYY/MM/DD
- **正式環境部署**: YYYY/MM/DD
- **回復計劃**: [如有問題的回復步驟]

## 後續維護
- **注意事項**: [特殊維護注意事項]
- **相關文檔**: [相關技術文檔連結]
```

### Bug修復記錄
```markdown
# Bug修復記錄

## Bug資訊
- **Bug ID**: [Bug編號]
- **發現日期**: YYYY/MM/DD
- **修復日期**: YYYY/MM/DD
- **修復者**: [姓名]
- **嚴重程度**: [低/中/高/緊急]

## 問題描述
- **現象**: [問題現象描述]
- **影響範圍**: [影響的功能和使用者]
- **根本原因**: [技術根因分析]

## 修復方案
- **修復策略**: [修復方法]
- **修改檔案**: [具體修改的檔案]
- **程式碼變更**: [關鍵程式碼修改]

## 驗證結果
- [ ] 問題重現測試
- [ ] 修復效果驗證
- [ ] 回歸測試通過
- [ ] 效能影響評估

## 防範措施
- **預防措施**: [避免類似問題的措施]
- **監控加強**: [新增的監控項目]
```

## 🚨 緊急維護程序

### 系統異常處理
```bash
#!/bin/bash
# 緊急維護腳本

EMERGENCY_LOG="/var/log/emergency_$(date +%Y%m%d_%H%M%S).log"

emergency_response() {
    echo "=== 緊急維護開始 ===" | tee -a $EMERGENCY_LOG
    
    # 1. 系統狀態檢查
    echo "檢查系統狀態..." | tee -a $EMERGENCY_LOG
    systemctl status tomcat | tee -a $EMERGENCY_LOG
    
    # 2. 錯誤日誌收集
    echo "收集錯誤日誌..." | tee -a $EMERGENCY_LOG
    tail -200 logs/catalina.out | grep -i error | tee -a $EMERGENCY_LOG
    
    # 3. 備份目前狀態
    echo "備份目前狀態..." | tee -a $EMERGENCY_LOG
    tar -czf emergency_backup_$(date +%Y%m%d_%H%M%S).tar.gz webapps/
    
    # 4. 通知相關人員
    echo "通知相關人員..." | tee -a $EMERGENCY_LOG
    # mail -s "系統緊急維護" <EMAIL> < $EMERGENCY_LOG
    
    echo "=== 緊急維護記錄完成 ===" | tee -a $EMERGENCY_LOG
}
```

### 快速復原程序
```bash
#!/bin/bash
# 快速復原腳本

restore_from_backup() {
    local backup_file=$1
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    echo "=== 開始系統復原 ==="
    
    # 1. 停止服務
    systemctl stop tomcat
    
    # 2. 備份當前狀態
    mv webapps webapps.before.restore.$timestamp
    
    # 3. 復原備份
    tar -xzf "$backup_file"
    
    # 4. 重啟服務
    systemctl start tomcat
    
    # 5. 驗證復原結果
    sleep 30
    curl -f http://localhost:8080/health-check || echo "復原後健康檢查失敗"
    
    echo "=== 系統復原完成 ==="
}
```

## 🎯 維護最佳實踐

### 代碼品質檢查
1. **每次修改前**：建立備份
2. **修改過程中**：小步提交，頻繁測試
3. **修改完成後**：完整回歸測試
4. **部署前**：在測試環境完整驗證

### 文檔維護
1. **即時記錄**：每個變更都要記錄
2. **標準格式**：使用統一的記錄範本
3. **版本控制**：重要文檔納入版本控制
4. **定期回顧**：每月檢視維護記錄

### 知識分享
1. **團隊分享**：定期技術分享會議
2. **經驗傳承**：建立mentor制度
3. **問題庫**：建立常見問題解決方案庫
4. **培訓計劃**：新人培訓標準化

---

**建立日期**: 2025-01-05  
**負責人**: 全棧開發者  
**最後更新**: 2025-01-05  
**版本**: v1.0