# 自動結案機制分析 (Automatic Case Closing Mechanism Analysis)

## 📋 文件資訊
- **文件編號**: T2.7.2
- **文件目的**: 分析新北市違章建築管理系統的自動結案機制
- **分析日期**: 2025-07-05
- **負責單位**: 【D】Claude Code - DevOps與技術架構任務組

---

## 🔍 核心發現

### ⚠️ 重要發現：系統中不存在自動結案機制

經過全面分析，本系統**完全依賴人工結案流程**，所有結案操作都需要使用者明確執行，沒有任何自動化結案功能。

---

## 📊 現行結案機制分析

### 1. 人工結案流程架構

```mermaid
graph TD
    A[案件處理完成] --> B{選擇結案方式}
    B -->|正常結案| C[填寫結案資料<br/>im60301_man_saveCase.jsp]
    B -->|空案處理| D[快速結案<br/>case_empty_dis.jsp]
    
    C --> E[結案預審<br/>狀態: 449/459/469]
    E --> F[主管審核<br/>im40601_man.jsp]
    F --> G[正式結案<br/>狀態: 440/450/460]
    
    D --> H[直接結案<br/>狀態: 441/451/461]
```

### 2. 結案狀態碼體系

| 違建類型 | 預審狀態 | 正式結案 | 空案結案 | 對應單位 |
|---------|---------|---------|---------|----------|
| 廣告物 | 449 | 440 | 441 | 廣告科 |
| 下水道 | 459 | 450 | 451 | 勞安科 |
| 一般 | 469 | 460 | 461 | 拆除科 |

### 3. 結案原因代碼 (b_end_item)

#### A類：自行拆除
- A-01: 查報人自行（己）改善
- A-02: 查報人依限改善
- A-03: 查報人尚未取得（未核准）使照自行拆除改善
- A-04: 查報人（核准）取得使照自行拆除改善
- A-05: 建物尚未取得（未核准）使照業主自行拆除改善
- A-06: 建物（核准）取得使照業主自行拆除改善
- A-07: 其他人代為處理

#### B類：執行拆除
- B-01: 依法執行拆除
- B-02: 代履行拆除
- B-03: 勸導拆除
- B-04: 其他單位處理（如：工務局廣告科拆除）
- B-05: 已拆除完成

#### C類：其他結案
- C-01: 建築法第99條裁罰
- C-02: 查無違建事實
- C-03: 法院判決
- C-04: 撤銷（行政程序撤銷）
- C-05: 建管處註銷
- C-06: 不屬建管處權責
- C-07: 其他（請於備註欄內詳述）

---

## 🔧 技術架構分析

### 1. Quartz 排程器配置（未使用）

```properties
# quartz.properties 存在但未啟用
org.quartz.scheduler.instanceName = BMS_Scheduler
org.quartz.threadPool.threadCount = 5
org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore
```

**發現**：
- 配置檔案存在：`WEB-INF/classes/quartz.properties`
- 工作定義為空：`WEB-INF/classes/quartz_data.xml` 無任何排程任務
- 結論：框架已整合但未實際使用

### 2. 資料庫觸發器（不存在）

經查詢，資料庫中沒有任何與自動結案相關的觸發器或預存程序。

### 3. 批次處理類別（不存在）

未發現任何實作 `org.quartz.Job` 介面的自動結案類別。

---

## 💡 潛在自動化機會

### 1. 時間基準規則

```java
// 概念性實作範例
public class AutoClosingJob implements Job {
    @Override
    public void execute(JobExecutionContext context) {
        // 規則1：超過180天無活動的案件
        String sql = "SELECT case_id FROM ibmsts " +
                    "WHERE acc_rlt IN ('449','459','469') " +
                    "AND DATEDIFF(day, acc_date, GETDATE()) > 180";
        
        // 規則2：已確認拆除完成超過30天
        String sql2 = "SELECT case_id FROM ibmcase " +
                     "WHERE b_finish_date IS NOT NULL " +
                     "AND DATEDIFF(day, b_finish_date, GETDATE()) > 30";
    }
}
```

### 2. 條件基準規則

- **拆除完成確認**：現場勘查確認已拆除 → 自動結案
- **使照取得**：系統對接確認已取得使用執照 → 自動結案
- **期限屆滿**：改善期限到期且無異議 → 自動結案

### 3. 建議的自動化架構

```xml
<!-- quartz_data.xml 範例配置 -->
<job-scheduling-data>
    <schedule>
        <job>
            <name>AutoClosingJob</name>
            <job-class>com.ntpc.bms.jobs.AutoClosingJob</job-class>
        </job>
        <trigger>
            <cron>
                <name>AutoClosingTrigger</name>
                <job-name>AutoClosingJob</job-name>
                <cron-expression>0 0 2 * * ?</cron-expression> <!-- 每天凌晨2點執行 -->
            </cron>
        </trigger>
    </schedule>
</job-scheduling-data>
```

---

## 🚨 風險評估

### 1. 為何沒有自動化？

**可能原因**：
- **法規要求**：政府違建處理可能需要人工審核以符合法規
- **責任歸屬**：自動結案可能造成責任認定困難
- **例外情況**：違建案件複雜度高，難以制定統一規則
- **審計需求**：需要明確的人員簽核軌跡

### 2. 自動化風險

- **誤判風險**：自動結案可能錯誤關閉需要持續追蹤的案件
- **法律風險**：未經適當程序結案可能引發爭議
- **資料完整性**：自動化可能遺漏必要的結案文件

---

## 📋 現行結案作業要點

### 1. 結案前檢查清單
- ✓ 違建已拆除或改善
- ✓ 相關文件齊全
- ✓ 現場複查完成
- ✓ 無待辦事項
- ✓ 符合結案條件

### 2. 結案資料必填欄位
- `b_end_item`：結案原因代碼
- `b_finish_date`：完成日期
- `end_chk_date`：複查日期
- `end_reg_yy`：結案年度
- `end_reg_no`：結案號碼

### 3. 權限控制
- 一般承辦人：填寫結案資料
- 單位主管：審核結案申請
- 系統管理員：批次結案處理

---

## 🎯 改進建議

### 1. 短期改進（維持人工流程）

**通知提醒機制**
```sql
-- 建立待結案提醒視圖
CREATE VIEW v_pending_closing AS
SELECT case_id, reg_unit, DATEDIFF(day, acc_date, GETDATE()) as pending_days
FROM ibmsts
WHERE acc_rlt IN ('449','459','469')
  AND DATEDIFF(day, acc_date, GETDATE()) > 30;
```

**批次處理優化**
- 提供更好的批次結案介面
- 加入結案預覽功能
- 增加結案檢核報表

### 2. 長期規劃（引入自動化）

**階段性實施**
1. **第一階段**：自動產生結案建議清單
2. **第二階段**：半自動結案（需人工確認）
3. **第三階段**：特定條件全自動結案

**必要配套措施**
- 建立完整的審計日誌
- 提供手動覆寫機制
- 定期檢討自動化規則

---

## 📊 結案統計建議

### 建議新增的統計指標

```sql
-- 平均結案時間
SELECT 
    CASE 
        WHEN acc_rlt = '440' THEN '廣告物'
        WHEN acc_rlt = '450' THEN '下水道'
        WHEN acc_rlt = '460' THEN '一般'
    END as case_type,
    AVG(DATEDIFF(day, create_date, rsult_rec_time)) as avg_days
FROM ibmcase
WHERE acc_rlt IN ('440','450','460')
GROUP BY acc_rlt;

-- 結案原因分布
SELECT 
    b_end_item,
    COUNT(*) as case_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM ibmcase
WHERE is_closed = 1
GROUP BY b_end_item
ORDER BY case_count DESC;
```

---

## 🔚 結論

1. **現況確認**：系統完全依賴人工結案，無自動化機制
2. **技術就緒**：Quartz 框架已整合，可支援未來自動化需求
3. **業務考量**：自動化需謹慎評估法規與業務需求
4. **改進方向**：建議先優化人工流程，再逐步導入自動化

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*任務編號：T2.7.2*
*完成日期：2025-07-05*