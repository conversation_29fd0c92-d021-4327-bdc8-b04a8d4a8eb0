# 新北市違章建築管理系統 - 復原測試排程手冊

## 📅 復原測試概述

### 測試目的
- 驗證備份檔案完整性和可復原性
- 確保災難復原程序有效性
- 符合政府資訊系統安全規範
- 提升操作人員復原技能
- 滿足 RTO (4小時) 和 RPO (1小時) 目標

### 測試原則
- **定期執行**：按照既定排程執行
- **隔離環境**：使用獨立測試環境
- **完整記錄**：詳細記錄測試過程和結果
- **持續改進**：根據測試結果優化程序

---

## 🗓️ 測試排程表

### 每日測試 (Daily Tests)
| 時間 | 測試項目 | 負責人 | 預計耗時 |
|------|----------|--------|----------|
| 06:00 | 備份檔案驗證 | 系統管理員 | 15分鐘 |
| 18:00 | 系統健康檢查 | 系統管理員 | 10分鐘 |

### 每週測試 (Weekly Tests)
| 星期 | 時間 | 測試項目 | 負責人 | 預計耗時 |
|------|------|----------|--------|----------|
| 週一 | 08:00 | PostgreSQL 資料庫復原測試 | 資料庫管理員 | 2小時 |
| 週三 | 08:00 | SQL Server 資料庫復原測試 | 資料庫管理員 | 1.5小時 |
| 週五 | 08:00 | 應用程式復原測試 | 系統管理員 | 1小時 |
| 週六 | 14:00 | 整合功能測試 | 系統管理員 | 2小時 |

### 每月測試 (Monthly Tests)
| 日期 | 時間 | 測試項目 | 負責人 | 預計耗時 |
|------|------|----------|--------|----------|
| 第1個週六 | 09:00 | 完整災難復原演練 | 全體團隊 | 4小時 |
| 第3個週六 | 09:00 | 跨區域復原測試 | 系統管理員 | 3小時 |

### 季度測試 (Quarterly Tests)
| 季度 | 月份 | 測試項目 | 負責人 | 預計耗時 |
|------|------|----------|--------|----------|
| Q1 | 3月 | 完整系統容錯測試 | 全體團隊 | 8小時 |
| Q2 | 6月 | 業務連續性測試 | 全體團隊 | 6小時 |
| Q3 | 9月 | 安全事件復原測試 | 全體團隊 | 4小時 |
| Q4 | 12月 | 年度綜合演練 | 全體團隊 | 8小時 |

---

## 📋 測試程序

### 每日備份驗證測試

#### 測試目標
- 確認備份檔案完整性
- 驗證備份檔案可讀性
- 檢查備份大小異常

#### 測試步驟
```powershell
# 1. 執行備份驗證腳本
cd D:\apache-tomcat-9.0.98\webapps\src\backup_scripts
.\recovery_testing.ps1 -TestType database_restore -TestEnvironment test -CleanupAfterTest

# 2. 檢查測試結果
$testReport = Get-Content ".\reports\recovery_test_report_*.json" | ConvertFrom-Json
Write-Host "測試結果：$($testReport.test_summary.overall_success)"

# 3. 記錄測試結果
$dailyTestLog = @{
    "date" = Get-Date
    "test_type" = "daily_backup_verification"
    "postgresql_backup_test" = $testReport.test_results[0].success
    "sqlserver_backup_test" = $testReport.test_results[1].success
    "overall_result" = $testReport.test_summary.overall_success
}

$dailyTestLog | ConvertTo-Json | Out-File ".\logs\daily_test_$(Get-Date -Format 'yyyyMMdd').json"
```

### 每週資料庫復原測試

#### 週一：PostgreSQL 復原測試
```powershell
# 執行PostgreSQL復原測試
.\recovery_testing.ps1 -TestType database_restore -TestEnvironment test -CleanupAfterTest

# 測試檢查項目：
# □ 備份檔案存在且完整
# □ 測試資料庫建立成功
# □ 資料復原完成
# □ 資料完整性驗證通過
# □ 表格數量正確
# □ 關鍵資料表記錄數量正確
# □ 測試環境清理完成
```

#### 週三：SQL Server 復原測試
```powershell
# 執行SQL Server復原測試
.\recovery_testing.ps1 -TestType database_restore -TestEnvironment test -CleanupAfterTest

# 測試檢查項目：
# □ 備份檔案存在且完整
# □ 測試資料庫建立成功
# □ 資料復原完成
# □ 資料完整性驗證通過
# □ 空間資料完整性檢查
# □ 索引重建完成
# □ 測試環境清理完成
```

#### 週五：應用程式復原測試
```powershell
# 執行應用程式復原測試
.\recovery_testing.ps1 -TestType application_restore -TestEnvironment test -CleanupAfterTest

# 測試檢查項目：
# □ 應用程式備份檔案存在
# □ 檔案解壓縮成功
# □ 目錄結構正確
# □ 配置檔案完整
# □ JSP檔案完整
# □ 資源檔案完整
# □ 測試環境清理完成
```

### 每月完整災難復原演練

#### 演練範圍
- 完整系統災難模擬
- 多重故障情境
- 人員協調測試
- 通報流程驗證
- 業務連續性測試

#### 演練腳本
```powershell
# 月度災難復原演練
Write-Host "=== 月度災難復原演練開始 ===" -ForegroundColor Cyan
$drillStartTime = Get-Date

# 階段1：災難模擬 (0-15分鐘)
Write-Host "階段1：災難模擬" -ForegroundColor Yellow
Start-Sleep -Seconds 5  # 模擬災難發生

# 階段2：災難評估 (15-30分鐘)
Write-Host "階段2：災難評估" -ForegroundColor Yellow
.\disaster_recovery.ps1 -Operation assessment

# 階段3：復原決策 (30-45分鐘)
Write-Host "階段3：復原決策" -ForegroundColor Yellow
# 模擬決策過程

# 階段4：執行復原 (45分鐘-4小時)
Write-Host "階段4：執行復原" -ForegroundColor Yellow
.\disaster_recovery.ps1 -Operation recovery -TestEnvironment test

# 階段5：驗證測試 (4小時後)
Write-Host "階段5：驗證測試" -ForegroundColor Yellow
.\recovery_testing.ps1 -TestType full_recovery -TestEnvironment test

$drillEndTime = Get-Date
$drillDuration = ($drillEndTime - $drillStartTime).TotalMinutes

Write-Host "=== 演練完成 ===" -ForegroundColor Green
Write-Host "演練時間：$([math]::Round($drillDuration, 2)) 分鐘" -ForegroundColor Green
Write-Host "RTO目標：240分鐘" -ForegroundColor Blue
Write-Host "是否達成RTO：$(if ($drillDuration -le 240) { '是' } else { '否' })" -ForegroundColor $(if ($drillDuration -le 240) { "Green" } else { "Red" })
```

---

## 📊 測試結果記錄

### 測試結果範本

#### 每日測試記錄
```json
{
  "test_date": "2024-07-09",
  "test_type": "daily_backup_verification",
  "tests": [
    {
      "test_name": "postgresql_backup_verification",
      "status": "passed",
      "duration_minutes": 2.5,
      "backup_file": "bms_full_20240709_020000.sql.gz",
      "backup_size_mb": 1024.5,
      "verification_result": "checksum_passed"
    },
    {
      "test_name": "sqlserver_backup_verification", 
      "status": "passed",
      "duration_minutes": 1.8,
      "backup_file": "ramsGIS_full_20240709_010000.bak",
      "backup_size_mb": 512.3,
      "verification_result": "restore_verify_passed"
    }
  ],
  "overall_result": "passed",
  "total_duration_minutes": 4.3,
  "tester": "系統管理員",
  "notes": "所有測試項目正常通過"
}
```

#### 每週測試記錄
```json
{
  "test_week": "2024-W28",
  "tests": [
    {
      "test_date": "2024-07-08",
      "test_type": "postgresql_restore",
      "status": "passed",
      "duration_minutes": 85,
      "test_database": "bms_test_20240708102030",
      "restored_tables": 45,
      "restored_records": 370000,
      "verification_queries": 8,
      "all_verifications_passed": true
    },
    {
      "test_date": "2024-07-10",
      "test_type": "sqlserver_restore",
      "status": "passed", 
      "duration_minutes": 72,
      "test_database": "ramsGIS_test_20240710102030",
      "restored_tables": 25,
      "restored_records": 150000,
      "verification_queries": 6,
      "all_verifications_passed": true
    }
  ],
  "overall_result": "passed",
  "total_duration_minutes": 157,
  "rto_compliance": true,
  "improvements_identified": [
    "優化PostgreSQL復原腳本",
    "增加並行復原處理"
  ]
}
```

#### 每月演練記錄
```json
{
  "drill_date": "2024-07-06",
  "drill_type": "full_disaster_recovery",
  "participants": [
    "系統管理員",
    "資料庫管理員", 
    "IT主管",
    "業務代表"
  ],
  "timeline": {
    "disaster_simulation": "09:00-09:15",
    "assessment": "09:15-09:30",
    "decision": "09:30-09:45",
    "recovery": "09:45-12:45",
    "verification": "12:45-13:00"
  },
  "results": {
    "rto_achieved": true,
    "rpo_achieved": true,
    "total_duration_hours": 4.0,
    "systems_recovered": [
      "PostgreSQL Database",
      "SQL Server Database",
      "Tomcat Application",
      "Web Interface"
    ],
    "issues_encountered": [
      "配置檔案復原延遲10分鐘",
      "第一次服務啟動失敗"
    ]
  },
  "lessons_learned": [
    "需要改進配置檔案備份程序",
    "應該增加服務啟動重試機制"
  ],
  "action_items": [
    {
      "item": "優化配置檔案復原程序",
      "assignee": "系統管理員",
      "due_date": "2024-07-20"
    },
    {
      "item": "實施自動服務啟動重試",
      "assignee": "系統管理員", 
      "due_date": "2024-07-25"
    }
  ]
}
```

---

## 📈 測試指標與KPI

### 關鍵績效指標

#### 復原測試成功率
```powershell
# 計算每月復原測試成功率
function Get-MonthlyTestSuccessRate {
    param(
        [int]$Year = (Get-Date).Year,
        [int]$Month = (Get-Date).Month
    )
    
    $testLogPath = ".\logs"
    $testLogs = Get-ChildItem $testLogPath -Filter "*test_*.json" | 
                Where-Object { $_.LastWriteTime.Year -eq $Year -and $_.LastWriteTime.Month -eq $Month }
    
    $totalTests = 0
    $passedTests = 0
    
    foreach ($log in $testLogs) {
        $testData = Get-Content $log.FullName | ConvertFrom-Json
        $totalTests++
        if ($testData.overall_result -eq "passed") {
            $passedTests++
        }
    }
    
    $successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
    
    return @{
        "year" = $Year
        "month" = $Month
        "total_tests" = $totalTests
        "passed_tests" = $passedTests
        "success_rate" = $successRate
    }
}

# 取得本月測試成功率
$monthlyKPI = Get-MonthlyTestSuccessRate
Write-Host "本月測試成功率：$($monthlyKPI.success_rate)%" -ForegroundColor $(if ($monthlyKPI.success_rate -ge 95) { "Green" } else { "Red" })
```

#### RTO/RPO 達成率
```powershell
# 計算RTO達成率
function Get-RTOComplianceRate {
    param(
        [int]$TargetRTOHours = 4
    )
    
    $drillLogs = Get-ChildItem ".\logs" -Filter "*drill_*.json"
    
    $totalDrills = 0
    $rtoMetDrills = 0
    
    foreach ($log in $drillLogs) {
        $drillData = Get-Content $log.FullName | ConvertFrom-Json
        $totalDrills++
        if ($drillData.results.total_duration_hours -le $TargetRTOHours) {
            $rtoMetDrills++
        }
    }
    
    $rtoComplianceRate = if ($totalDrills -gt 0) { [math]::Round(($rtoMetDrills / $totalDrills) * 100, 2) } else { 0 }
    
    return @{
        "target_rto_hours" = $TargetRTOHours
        "total_drills" = $totalDrills
        "rto_met_drills" = $rtoMetDrills
        "compliance_rate" = $rtoComplianceRate
    }
}

# 取得RTO達成率
$rtoKPI = Get-RTOComplianceRate
Write-Host "RTO達成率：$($rtoKPI.compliance_rate)%" -ForegroundColor $(if ($rtoKPI.compliance_rate -ge 90) { "Green" } else { "Red" })
```

### 測試品質指標

#### 測試覆蓋率
- **資料庫復原測試**：100%
- **應用程式復原測試**：100%
- **整合測試**：100%
- **端到端測試**：100%
- **效能測試**：80%

#### 測試頻率達成率
- **每日測試**：目標 100%
- **每週測試**：目標 100%
- **每月測試**：目標 100%
- **季度測試**：目標 100%

---

## 🔧 測試環境管理

### 測試環境規格

#### 硬體需求
- **CPU**：4 核心以上
- **記憶體**：8GB 以上
- **硬碟**：100GB 可用空間
- **網路**：隔離測試網段

#### 軟體需求
- **作業系統**：Windows Server 2019/2022
- **資料庫**：PostgreSQL 15 + SQL Server 2019
- **應用伺服器**：Apache Tomcat 9.0.98
- **測試工具**：PowerShell 5.1+

### 測試環境維護

#### 每週維護
```powershell
# 測試環境維護腳本
Write-Host "開始測試環境維護..." -ForegroundColor Yellow

# 1. 清理測試資料
Write-Host "清理測試資料..." -ForegroundColor Blue
$testDatabases = @("bms_test_*", "ramsGIS_test_*")
foreach ($pattern in $testDatabases) {
    # 清理測試資料庫
    # (實際清理邏輯)
}

# 2. 重置測試環境
Write-Host "重置測試環境..." -ForegroundColor Blue
$testPaths = @(
    "D:\Recovery\Staging\test",
    "D:\Recovery\Temp\test",
    "D:\Recovery\Logs\test"
)
foreach ($path in $testPaths) {
    if (Test-Path $path) {
        Remove-Item $path -Recurse -Force
        New-Item -ItemType Directory -Path $path -Force
    }
}

# 3. 驗證測試環境
Write-Host "驗證測試環境..." -ForegroundColor Blue
$envCheck = @{
    "disk_space" = (Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "D:" }).FreeSpace
    "memory_available" = (Get-WmiObject -Class Win32_OperatingSystem).FreePhysicalMemory
    "services_running" = (Get-Service "PostgreSQL*", "Apache*" | Where-Object { $_.Status -eq "Running" }).Count
}

Write-Host "測試環境狀態：" -ForegroundColor Green
$envCheck | Format-Table -AutoSize

Write-Host "測試環境維護完成" -ForegroundColor Green
```

---

## 📋 測試檢查清單

### 復原測試前檢查清單

#### 環境準備
- [ ] 測試環境可用且隔離
- [ ] 備份檔案存在且完整
- [ ] 測試工具正常運作
- [ ] 測試人員到位
- [ ] 測試計畫已審核

#### 安全檢查
- [ ] 測試環境與生產環境隔離
- [ ] 敏感資料已遮罩或移除
- [ ] 測試活動已記錄
- [ ] 存取權限已設定
- [ ] 測試資料將被清理

### 復原測試後檢查清單

#### 結果驗證
- [ ] 測試結果已記錄
- [ ] 所有測試項目已完成
- [ ] 問題已識別並記錄
- [ ] 改善建議已提出
- [ ] 測試報告已產生

#### 清理工作
- [ ] 測試資料已清理
- [ ] 測試環境已重置
- [ ] 測試工具已關閉
- [ ] 測試日誌已歸檔
- [ ] 後續行動已安排

---

## 📊 測試報告範本

### 月度測試報告

```markdown
# 月度復原測試報告

## 測試摘要
- **測試期間**：2024年7月1日 - 2024年7月31日
- **測試項目**：31次每日測試 + 4次每週測試 + 1次月度演練
- **測試成功率**：97.2%
- **RTO達成率**：100%
- **RPO達成率**：100%

## 測試結果詳情

### 每日測試結果
| 日期 | PostgreSQL | SQL Server | 應用程式 | 整體結果 |
|------|------------|------------|----------|----------|
| 07/01 | ✅ | ✅ | ✅ | ✅ |
| 07/02 | ✅ | ✅ | ✅ | ✅ |
| 07/03 | ✅ | ❌ | ✅ | ❌ |
| ... | ... | ... | ... | ... |

### 每週測試結果
| 週別 | 測試類型 | 結果 | 耗時 | 備註 |
|------|----------|------|------|------|
| W27 | PostgreSQL復原 | ✅ | 85分鐘 | 正常 |
| W27 | SQL Server復原 | ✅ | 72分鐘 | 正常 |
| W28 | 應用程式復原 | ✅ | 45分鐘 | 正常 |
| W28 | 整合測試 | ✅ | 95分鐘 | 正常 |

### 月度演練結果
- **演練時間**：2024年7月6日 09:00-13:00
- **參與人員**：4人
- **演練結果**：成功
- **RTO達成**：4小時內完成
- **發現問題**：配置檔案復原延遲

## 改善建議
1. 優化配置檔案復原程序
2. 增加自動化測試覆蓋率
3. 強化測試環境管理
4. 提升人員操作熟練度

## 下月計畫
- 實施配置檔案復原優化
- 增加自動化測試項目
- 進行跨區域復原測試
- 安排人員訓練課程
```

---

## 📞 支援聯絡資訊

### 測試支援團隊
- **測試協調人**：系統管理員 (0912-345-678)
- **資料庫專家**：資料庫管理員 (0923-456-789)
- **應用程式專家**：應用程式開發者 (0934-567-890)
- **網路專家**：網路管理員 (0945-678-901)

### 緊急聯絡
- **測試期間問題**：系統管理員 (0912-345-678)
- **測試環境問題**：IT主管 (0923-456-789)
- **業務影響評估**：業務主管 (0934-567-890)

---

**文件版本**：v1.0  
**最後更新**：2024年7月9日  
**文件作者**：系統管理員  
**核准人員**：IT主管  

*本文件包含機密資訊，請妥善保管並限制存取權限*