<%@page contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.util.*, com.codecharge.db.*"%>
<%@page import="java.sql.Connection, java.sql.PreparedStatement, java.sql.ResultSet, java.sql.SQLException"%>
<%@page import="org.json.simple.JSONObject"%>

<%
    // Constants
    final String CONNECTION_NAME = "DBConn";
    final String DB_TABLE_NAME = "public.im52101_excel_imports";
    final String ID_COLUMN = "import_id";
    final String STATUS_COLUMN = "status"; // Assuming you have a 'status' column
    // Optional: Add other columns you might want to return, e.g., processed_count, error_details_link

    // Output object
    JSONObject jsonResponse = new JSONObject();
    String importId = request.getParameter("id");

    if (StringUtils.isEmpty(importId)) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        jsonResponse.put("status", "錯誤");
        jsonResponse.put("message", "缺少必要的 'id' 參數。");
        out.print(jsonResponse.toJSONString());
        return;
    }

    DBConnectionManager dbcm = null;
    Connection conn = null;
    PreparedStatement pstmt = null;
    ResultSet rs = null;

    try {
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection(CONNECTION_NAME);

        // 查詢批次基本資訊和處理統計
        String sql = "SELECT i." + STATUS_COLUMN + ", i.total_rows_in_excel, i.processed_rows_count, i.error_rows_count, " +
                     "       i.processing_start_time, i.processing_end_time " +
                     "FROM " + DB_TABLE_NAME + " i WHERE i." + ID_COLUMN + " = ?";

        pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, importId);
        rs = pstmt.executeQuery();

        if (rs.next()) {
            String status = rs.getString(STATUS_COLUMN);
            String displayStatus = status;
            
            // 轉換狀態為中文顯示
            if ("NEW_UPLOAD".equals(status)) {
                displayStatus = "未處理";
            } else if ("PENDING".equals(status)) {
                displayStatus = "待處理";
            } else if ("PROCESSING".equals(status)) {
                displayStatus = "處理中";
            } else if ("COMPLETED".equals(status)) {
                displayStatus = "匯入完成";
            } else if ("FAILED".equals(status)) {
                displayStatus = "匯入失敗";
            }
            
            jsonResponse.put("status", StringUtils.isEmpty(displayStatus) ? "狀態未明" : displayStatus);
            jsonResponse.put("originalStatus", status); // 保留原始狀態碼
            jsonResponse.put("totalRows", rs.getInt("total_rows_in_excel"));
            jsonResponse.put("processedRows", rs.getInt("processed_rows_count"));
            jsonResponse.put("errorRows", rs.getInt("error_rows_count"));
            
            // 檢查是否有處理記錄
            boolean hasProcessingLogs = checkProcessingLogs(conn, importId);
            jsonResponse.put("hasProcessingLogs", hasProcessingLogs);
            
            // 檢查是否有異常記錄
            boolean hasExceptions = checkExceptions(conn, importId);
            jsonResponse.put("hasExceptions", hasExceptions);
            
            // 根據狀態設定可用的操作
            jsonResponse.put("canViewDetails", hasProcessingLogs && 
                ("COMPLETED".equals(status) || "FAILED".equals(status)));
            jsonResponse.put("canViewExceptions", hasExceptions);
            jsonResponse.put("canDownloadReport", "COMPLETED".equals(status));
            
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            jsonResponse.put("status", "未找到");
            jsonResponse.put("message", "找不到匯入ID: " + Utils.encodeHTML(importId));
        }

    } catch (SQLException sqle) {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        jsonResponse.put("status", "資料庫錯誤");
        jsonResponse.put("message", "查詢狀態時發生資料庫錯誤: " + sqle.getMessage());
        application.log("SQL Error in im52101_ajax.jsp for id " + importId + ": " + sqle.getMessage(), sqle);
    } catch (Exception e) {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        jsonResponse.put("status", "伺服器錯誤");
        jsonResponse.put("message", "查詢狀態時發生未預期錯誤: " + e.getMessage());
        application.log("Error in im52101_ajax.jsp for id " + importId + ": " + e.getMessage(), e);
    } finally {
        if (rs != null) try { rs.close(); } catch (SQLException e) { /* ignore */ }
        if (pstmt != null) try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        if (conn != null) {
            try {
                dbcm.freeConnection(CONNECTION_NAME, conn);
            } catch (Exception e) {
                application.log("Error closing connection in im52101_ajax.jsp: " + e.getMessage(), e);
            }
        }
    }

    out.print(jsonResponse.toJSONString());
%>

<%!
    // 檢查是否有處理記錄
    private boolean checkProcessingLogs(Connection conn, String importId) throws SQLException {
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT COUNT(*) FROM im52101_excel_processing_log WHERE import_id = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, importId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            return false;
        } finally {
            if (rs != null) try { rs.close(); } catch (SQLException e) { /* ignore */ }
            if (pstmt != null) try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        }
    }
    
    // 檢查是否有異常記錄
    private boolean checkExceptions(Connection conn, String importId) throws SQLException {
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            String sql = "SELECT COUNT(*) FROM im52101_excel_processing_log " +
                        "WHERE import_id = ? AND processing_status IN ('案件不存在', '格式錯誤', '重複的認定號碼', '處理失敗')";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, importId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
            return false;
        } finally {
            if (rs != null) try { rs.close(); } catch (SQLException e) { /* ignore */ }
            if (pstmt != null) try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        }
    }
%> 