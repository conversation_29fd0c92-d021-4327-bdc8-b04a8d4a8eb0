<Page id="1" name="im52101_lis" restricted="False" included="False" masterPage="" accessDeniedPage=".jsp" convertRule="Absolute" onlySslAccess="False">

        <Label id="18" name="lblTitle" PathID="lblTitle" ContentType="δευ" Verbatim="true">
                <Caption><![CDATA[<h2>批次 Excel 匯入作業列表</h2>]]></Caption>
        </Label>

        <!-- 搜尋表單 -->
        <!-- <Record name="Search" connection="" restricted="False" masterID="" detailForm="" returnPage="im60501_lis.jsp" convertRule="Relative" preserveParams="None" allowInsert="False" allowUpdate="False" allowDelete="False" allowRead="True" visible="True">
            <Button name="Button_DoSearch" operation="Search" returnPage="" convertRule="Relative" defaultButton="True" doValidate="True" order="1">
            </Button>
            <Button name="Button_Clean" operation="Cancel" returnPage="" convertRule="Relative" defaultButton="False" doValidate="False" order="2">
            </Button>
            <Hidden name="watchdog" dataType="Text" controlSourceType="DataSource" controlSource="" required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
            </Hidden>
            <TextBox name="S_DATE" dataType="Text" controlSourceType="DataSource" controlSource="" required="False" unique="False" format="" dbFormat="" verificationRule="" errorControl="">
            </TextBox>
            <DatePicker name="DatePicker_S_DATE1" control="S_DATE" style="Styles/Blueprint/Style.css"/>
            <TextBox name="E_DATE" dataType="Text" controlSourceType="DataSource" controlSource="" required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
            </TextBox>
            <DatePicker name="DatePicker_E_DATE1" control="E_DATE" style="Styles/Blueprint/Style.css"/>
            <ExcludeParameter name="S_DATE"/>
            <ExcludeParameter name="E_DATE"/>
        </Record> -->

        <!-- 新增按鈕 -->
        <Link id="9" name="Button_Add" PathID="Button_Add" Verbatim="true" Page="im52101_man.jsp" HTMLItem="Link">
                <Caption>新增批次匯入</Caption>
                <Parameters>
                        <LinkParameter Name="ccsForm" Value="im52101_man" ParameterType="Expression" PassThrough="False"/>
                </Parameters>
        </Link>

        <!-- 資料列表 -->
        <Grid id="10" name="list" connection="DBConn" restricted="False" fetchSize="10" allowRead="True" fetchSizeLimit="100" useCount="True" visible="True">
                <Row>
                        <Label name="number" dataType="Text" controlSourceType="DataSource" controlSource="number" isHtml="False" format="" dbFormat="">
                        </Label>

                        <Link id="11" name="import_id_link" PathID="listimport_id_link" DataField="import_id" Page="im52101_man.jsp" HTMLItem="Link" PreserveParameters="GET" RemoveParameters="">
                                <Caption>匯入ID</Caption>
                                <Parameters>
                                        <LinkParameter Name="import_id" ParameterType="DataField" Field="import_id" PassThrough="False"/>
                                </Parameters>
                        </Link>
                        <Hidden name="import_id_hidden" dataType="Text" controlSourceType="DataSource" controlSource="import_id" required="" unique="" format="" dbFormat="" verificationRule="" errorControl="">
                        </Hidden>
                        <Label name="upload_timestamp" dataType="Text" controlSourceType="DataSource" controlSource="upload_timestamp" isHtml="False" format="" dbFormat="">
                        </Label>
                        <Label name="cr_user" dataType="Text" controlSourceType="DataSource" controlSource="cr_user" isHtml="False" format="" dbFormat="">
                        </Label>
                        <Label name="original_file_name" dataType="Text" controlSourceType="DataSource" controlSource="original_file_name" isHtml="False" format="" dbFormat="">
                        </Label>
                        <Label name="acc_memo" dataType="Text" controlSourceType="DataSource" controlSource="acc_memo" isHtml="False" format="" dbFormat="">
                        </Label>
                        <Label name="status" dataType="Text" controlSourceType="DataSource" controlSource="status" isHtml="False" format="" dbFormat="">
                        </Label>

                </Row>
                <Label name="list_TotalRecords" dataType="Text" controlSourceType="DataSource" controlSource="" isHtml="False" format="" dbFormat="">
                </Label>
                <Link name="Link_insert" dataType="Text" controlSourceType="DataSource" controlSource="" hrefType="Page" hrefSource="im60503_man.jsp" convertRule="Relative" format="" dbFormat="" isHtml="False" preserveParams="GET">
                </Link>

                <Sorter name="Sorter_import_id" ascColumn="import_id" descColumn="import_id DESC" visible="True"/>
                <Sorter name="Sorter_upload_timestamp" ascColumn="upload_timestamp" descColumn="upload_timestamp DESC" visible="True"/>
                <Sorter name="Sorter_cr_user" ascColumn="cr_user" descColumn="cr_user DESC" visible="True"/>
                <Sorter name="Sorter_original_file_name" ascColumn="original_file_name" descColumn="original_file_name DESC" visible="True"/>
                <Sorter name="Sorter_acc_memo" ascColumn="acc_memo" descColumn="acc_memo DESC" visible="True"/>
                <Sorter name="Sorter_status" ascColumn="status" descColumn="status DESC" visible="True"/>
                <Navigator name="Navigator" pageSizes="1;5;10;25;50"/>
                <Sort>
                        <Order Direction="Descending" Field="upload_timestamp"/>
                </Sort>
                <Select query="SELECT row_number() OVER (ORDER BY import_id ASC) AS number, 
       import_id, upload_timestamp as upload_timestamp, cr_user, original_file_name, 
       acc_memo, status, processing_start_time, processing_end_time, 
       error_log_summary
FROM public.im52101_excel_imports " type="custom" orderBy="upload_timestamp desc">
                        <CountSql query="SELECT COUNT(*) FROM im52101_excel_imports i " type="custom"/>
                </Select>
        </Grid>

</Page> 

