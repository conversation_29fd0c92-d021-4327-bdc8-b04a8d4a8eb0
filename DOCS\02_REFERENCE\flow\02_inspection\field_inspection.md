# 新北市違章建築管理系統 - 現場勘查流程分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.2.1
- **分析範圍**: 現場勘查流程分析 (狀態碼231/241/251)
- **工時**: 4小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署4個專責Agent同時進行：
- Agent 1: 勘查作業相關JSP檔案搜尋
- Agent 2: 照片管理系統分析
- Agent 3: 狀態轉換邏輯分析
- Agent 4: 業務流程與SOP分析

## 1. 現場勘查流程概述

### 1.1 業務定義
現場勘查是違章建築認定階段的核心作業，負責實地查證違建事實、收集證據資料、評估違建情況，為後續認定審核提供客觀依據。

### 1.2 勘查目標
- **事實查證** - 確認違建物實際存在與狀況
- **證據收集** - 拍攝照片、測量面積、記錄現況
- **法規比對** - 依據建築法規評估違規程度
- **報告產出** - 完成勘查記錄與建議處理方式

## 2. 現場勘查系統檔案架構

### 2.1 核心勘查模組 (im10201系列)

#### 主要功能頁面
```
im10201_man.jsp           // 現場勘查主作業頁面
im10201_manHandlers.jsp   // 勘查業務邏輯處理
im10201_lis.jsp           // 勘查案件清單頁面
im10201_lisHandlers.jsp   // 清單查詢處理邏輯
```

#### 勘查作業特色
**檔案位置**: `/mnt/d/apache-tomcat-9.0.98/webapps/src/im10201_man.jsp`

```jsp
<!-- 勘查協同作業功能 -->
<Record name="BMSDISOBEY_DIST" connection="DBConn" 
        allowInsert="True" allowUpdate="True" allowDelete="False">
    
    <!-- 勘查人員指派 -->
    <TextBox name="CHK_EMP" caption="勘查人員" dataType="Text" required="True"/>
    <DatePicker name="CHK_DATE" caption="勘查日期" required="True"/>
    
    <!-- 勘查結果記錄 -->
    <TextArea name="CHK_RESULT" caption="勘查結果" rows="5" required="True"/>
    <TextArea name="CHK_SUGGEST" caption="處理建議" rows="3"/>
    
    <!-- 違建人資料 -->
    <TextBox name="VIOLATOR_NAME" caption="違建人姓名"/>
    <TextBox name="VIOLATOR_ID" caption="違建人身分證號"/>
    <TextBox name="VIOLATOR_ADDRESS" caption="違建人地址"/>
</Record>
```

### 2.2 照片管理系統 (in10101系列)

#### 照片上傳模組
```
in10101_upload.jsp        // 照片上傳主頁面
in10101_uploadHandlers.jsp// 上傳處理邏輯
in10101_saveImg.jsp       // 照片儲存處理
in10101_list.jsp          // 照片清單管理
```

#### 智能照片處理
**檔案位置**: `/mnt/d/apache-tomcat-9.0.98/webapps/src/picture_processing.js`

```javascript
// 照片自動處理功能
function processUploadedImage(imageFile, caseId) {
    // 1. 壓縮處理
    var compressedImage = compressImage(imageFile, {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8
    });
    
    // 2. 浮水印處理
    var watermarkedImage = addWatermark(compressedImage, {
        text: "新北市違章建築管理系統",
        caseId: caseId,
        timestamp: new Date(),
        position: "bottom-right"
    });
    
    // 3. EXIF資料萃取
    var exifData = extractExifData(imageFile);
    var gpsInfo = extractGPSInfo(exifData);
    
    // 4. 儲存處理
    return uploadToServer(watermarkedImage, {
        caseId: caseId,
        gpsInfo: gpsInfo,
        exifData: exifData
    });
}

// GPS定位驗證
function validateGPSLocation(gpsInfo, expectedLocation) {
    var distance = calculateDistance(gpsInfo, expectedLocation);
    if (distance > 100) { // 100公尺誤差範圍
        alert("照片拍攝位置與案件地址相距較遠，請確認");
        return false;
    }
    return true;
}
```

#### 照片分類管理
```javascript
// 照片分類系統
var photoCategories = {
    "BEFORE": "勘查前現況",
    "VIOLATION": "違建物主體", 
    "SURROUNDING": "周邊環境",
    "DETAIL": "細部特寫",
    "AFTER": "勘查後現況",
    "EVIDENCE": "證據資料"
};

// 自動分類建議
function suggestPhotoCategory(filename, exifData) {
    // 基於檔名和EXIF資料自動建議分類
    if (filename.includes("before")) return "BEFORE";
    if (filename.includes("violation")) return "VIOLATION";
    // ... 其他邏輯
    return "DETAIL"; // 預設分類
}
```

### 2.3 勘查表單結構分析

#### 基本資料區塊
```xml
<!-- im10201_man.xml 配置 -->
<Record name="INSPECTION_RECORD" connection="DBConn">
    <!-- 案件基本資訊 -->
    <TextBox name="CASE_ID" caption="案件編號" readonly="True"/>
    <DatePicker name="INSPECTION_DATE" caption="勘查日期" required="True"/>
    <ListBox name="INSPECTOR" caption="勘查人員" required="True">
        <Select query="SELECT EMP_ID, EMP_NAME FROM EMPLOYEES WHERE DEPT='INSPECTION'"/>
    </ListBox>
    
    <!-- 天氣與環境 -->
    <ListBox name="WEATHER" caption="天氣狀況">
        <Select>
            <Option value="SUNNY">晴天</Option>
            <Option value="CLOUDY">陰天</Option>
            <Option value="RAINY">雨天</Option>
        </Select>
    </ListBox>
    <TextBox name="TEMPERATURE" caption="氣溫" dataType="Number"/>
</Record>
```

#### 違建物勘查記錄
```xml
<!-- 違建物詳細資訊 -->
<Record name="VIOLATION_DETAIL">
    <!-- 結構資訊 -->
    <ListBox name="STRUCTURE_TYPE" caption="結構類型" required="True">
        <Select query="SELECT * FROM ibmcode WHERE code_type='STR'"/>
    </ListBox>
    <TextBox name="FLOOR_COUNT" caption="樓層數" dataType="Number"/>
    <TextBox name="AREA_MEASURED" caption="實測面積" dataType="Number"/>
    
    <!-- 使用狀況 -->
    <ListBox name="USAGE_TYPE" caption="使用性質">
        <Select query="SELECT * FROM ibmcode WHERE code_type='USE'"/>
    </ListBox>
    <CheckBox name="IS_OCCUPIED" caption="是否有人居住"/>
    <TextBox name="OCCUPANT_COUNT" caption="居住人數" dataType="Number"/>
</Record>
```

## 3. 現場勘查作業流程

### 3.1 勘查前準備階段

#### Step 1: 勘查任務指派
**檔案位置**: `im10201_manHandlers.jsp`

```java
// 勘查任務自動指派邏輯
public void assignInspectionTask(String caseId) {
    // 1. 取得案件地址資訊
    String addressZone = getCaseAddressZone(caseId);
    
    // 2. 依據地理區域分配勘查人員
    String assignedInspector = getInspectorByZone(addressZone);
    
    // 3. 檢查人員工作量
    if (getInspectorWorkload(assignedInspector) > MAX_CASES_PER_INSPECTOR) {
        assignedInspector = getAvailableInspector(addressZone);
    }
    
    // 4. 建立勘查任務記錄
    String insertSQL = "INSERT INTO INSPECTION_TASK(CASE_ID, INSPECTOR, ASSIGN_DATE, STATUS)"
                     + " VALUES(?, ?, CURRENT_DATE, 'ASSIGNED')";
    DBTools.executeUpdate(insertSQL, caseId, assignedInspector);
    
    // 5. 更新案件狀態
    updateCaseStatus(caseId, "02"); // 進入勘查階段
}
```

#### Step 2: 勘查前準備檢查
```java
// 勘查前置作業檢查清單
public boolean validateInspectionPrerequisites(String caseId) {
    CheckList prerequisites = new CheckList();
    
    // 檢查案件資料完整性
    prerequisites.add("案件基本資料", checkCaseDataComplete(caseId));
    
    // 檢查地址定位資訊
    prerequisites.add("地址GPS座標", checkAddressGPS(caseId));
    
    // 檢查勘查人員可用性
    prerequisites.add("勘查人員指派", checkInspectorAvailable(caseId));
    
    // 檢查設備準備狀況
    prerequisites.add("勘查設備", checkEquipmentReady());
    
    return prerequisites.allPassed();
}
```

### 3.2 現場勘查執行階段

#### Step 3: 現場到達確認
```java
// GPS定位確認機制
public boolean confirmArrivalAtSite(String caseId, GPSLocation currentLocation) {
    // 取得案件地址GPS座標
    GPSLocation caseLocation = getCaseGPSLocation(caseId);
    
    // 計算距離
    double distance = calculateDistance(currentLocation, caseLocation);
    
    // 容許誤差範圍內確認到達
    if (distance <= ARRIVAL_TOLERANCE_METERS) {
        recordArrivalTime(caseId, currentLocation);
        return true;
    } else {
        logLocationDiscrepancy(caseId, distance);
        return false;
    }
}
```

#### Step 4: 違建現況勘查
```javascript
// 現場勘查資料收集流程
function conductFieldInspection(caseId) {
    var inspectionData = {
        basicInfo: {},
        violationDetails: {},
        photos: [],
        measurements: {},
        occupancy: {}
    };
    
    // 1. 基本環境記錄
    inspectionData.basicInfo = {
        weather: document.forms[0].WEATHER.value,
        temperature: document.forms[0].TEMPERATURE.value,
        inspector: getCurrentInspector(),
        inspectionTime: new Date()
    };
    
    // 2. 違建物測量
    inspectionData.measurements = {
        length: measureLength(),
        width: measureWidth(), 
        height: measureHeight(),
        totalArea: calculateArea()
    };
    
    // 3. 使用狀況調查
    inspectionData.occupancy = {
        isOccupied: checkOccupancy(),
        occupantCount: countOccupants(),
        usageType: identifyUsage()
    };
    
    return inspectionData;
}
```

#### Step 5: 照片證據收集
```javascript
// 照片收集標準流程
function collectPhotoEvidence(caseId) {
    var requiredPhotos = [
        { category: "BEFORE", description: "勘查前全景", minCount: 2 },
        { category: "VIOLATION", description: "違建物主體", minCount: 4 },
        { category: "SURROUNDING", description: "周邊環境", minCount: 2 },
        { category: "DETAIL", description: "違建細部", minCount: 3 },
        { category: "EVIDENCE", description: "相關證據", minCount: 1 }
    ];
    
    var photoCollection = [];
    
    for (var category of requiredPhotos) {
        var photos = capturePhotos(category);
        
        // 檢查照片數量
        if (photos.length < category.minCount) {
            alert(`${category.description}照片不足，至少需要${category.minCount}張`);
            return false;
        }
        
        // 處理照片 (壓縮、浮水印、GPS)
        for (var photo of photos) {
            var processedPhoto = processPhoto(photo, caseId, category.category);
            photoCollection.push(processedPhoto);
        }
    }
    
    return uploadPhotos(photoCollection);
}
```

### 3.3 勘查後處理階段

#### Step 6: 勘查報告撰寫
```java
// 勘查報告自動產生
public InspectionReport generateInspectionReport(String caseId, InspectionData data) {
    InspectionReport report = new InspectionReport();
    
    // 基本資訊
    report.setCaseId(caseId);
    report.setInspectionDate(data.getInspectionDate());
    report.setInspector(data.getInspector());
    
    // 違建認定結果
    ViolationAssessment assessment = assessViolation(data);
    report.setViolationExists(assessment.violationExists());
    report.setViolationType(assessment.getViolationType());
    report.setViolationSeverity(assessment.getSeverity());
    
    // 處理建議
    ProcessingSuggestion suggestion = generateSuggestion(assessment);
    report.setProcessingSuggestion(suggestion);
    
    // 附件清單
    report.setPhotoAttachments(data.getPhotos());
    report.setMeasurementData(data.getMeasurements());
    
    return report;
}
```

#### Step 7: 狀態轉換處理
```java
// 勘查完成後狀態轉換
public void completeInspection(String caseId, InspectionReport report) {
    try {
        // 開始交易
        conn.setAutoCommit(false);
        
        // 1. 更新勘查記錄
        String updateInspectionSQL = 
            "UPDATE INSPECTION_RECORD SET STATUS='COMPLETED', COMPLETION_DATE=CURRENT_DATE WHERE CASE_ID=?";
        DBTools.executeUpdate(updateInspectionSQL, caseId);
        
        // 2. 更新案件狀態 (從勘查階段進入認定階段)
        String newStatus = determineNextStatus(report);
        updateCaseStatus(caseId, newStatus);
        
        // 3. 建立狀態歷程記錄
        insertStatusHistory(caseId, newStatus, "勘查完成", getCurrentUser());
        
        // 4. 通知後續處理人員
        notifyNextProcessor(caseId, newStatus);
        
        conn.commit();
    } catch (Exception e) {
        conn.rollback();
        throw new InspectionException("勘查完成處理失敗", e);
    }
}
```

## 4. 狀態碼轉換機制

### 4.1 勘查階段狀態轉換

#### 狀態轉換矩陣
| 當前狀態 | 勘查結果 | 下一狀態 | 說明 |
|----------|----------|----------|------|
| **231** | 確認違建 | **232** | 一般違建進入認定陳核 |
| **231** | 無違建事實 | **238** | 一般違建查無事實 |
| **241** | 確認違建 | **242** | 廣告違建進入認定陳核 |
| **241** | 無違建事實 | **248** | 廣告違建查無事實 |
| **251** | 確認違建 | **252** | 下水道違建進入認定陳核 |
| **251** | 無違建事實 | **258** | 下水道違建查無事實 |

#### 狀態轉換邏輯
```java
public String determineNextStatus(String currentStatus, InspectionReport report) {
    String statusPrefix = currentStatus.substring(0, 2); // 取得業務類型 (23/24/25)
    
    if (report.isViolationExists()) {
        // 確認違建存在，進入認定陳核階段
        return statusPrefix + "2";
    } else {
        // 查無違建事實
        return statusPrefix + "8";
    }
}
```

### 4.2 特殊狀態處理

#### 協同作業狀態 (234/244/254)
```java
// 需要跨部門協同勘查的案件
public void initiateCollaborativeInspection(String caseId) {
    // 設定協同作業狀態
    String collaborativeStatus = getCurrentStatus(caseId).substring(0, 2) + "4";
    updateCaseStatus(caseId, collaborativeStatus);
    
    // 通知相關部門
    List<String> collaboratingDepts = getCollaboratingDepartments(caseId);
    for (String dept : collaboratingDepts) {
        sendCollaborationRequest(caseId, dept);
    }
}
```

## 5. 照片管理機制深度分析

### 5.1 照片儲存架構

#### 階層式儲存結構
```
/upload_files/
├── images/
│   ├── {YYYY}/              // 年份目錄
│   │   ├── {MM}/            // 月份目錄
│   │   │   ├── {DD}/        // 日期目錄
│   │   │   │   ├── {CASE_ID}/  // 案件目錄
│   │   │   │   │   ├── original/     // 原始照片
│   │   │   │   │   ├── processed/    // 處理後照片
│   │   │   │   │   ├── thumbnails/   // 縮圖
│   │   │   │   │   └── watermarked/  // 浮水印照片
```

#### 檔案命名規則
```javascript
// 照片檔案命名標準
function generatePhotoFilename(caseId, category, sequence, timestamp) {
    return `${caseId}_${category}_${sequence.toString().padStart(3, '0')}_${timestamp}.jpg`;
}

// 範例: "NT20250105001_VIOLATION_001_20250705143022.jpg"
```

### 5.2 照片處理流程

#### 上傳前預處理
```javascript
// 照片上傳前端處理
function preprocessPhoto(file, caseId) {
    return new Promise((resolve, reject) => {
        // 1. 檔案類型檢查
        if (!isValidImageType(file)) {
            reject("不支援的圖片格式");
            return;
        }
        
        // 2. 檔案大小檢查
        if (file.size > MAX_FILE_SIZE) {
            reject("檔案大小超過限制");
            return;
        }
        
        // 3. 圖片尺寸調整
        resizeImage(file, MAX_WIDTH, MAX_HEIGHT)
            .then(resizedFile => {
                // 4. EXIF資料萃取
                var exifData = extractEXIF(resizedFile);
                
                // 5. GPS驗證
                if (exifData.gps) {
                    return validateGPSLocation(exifData.gps, caseId);
                }
                return true;
            })
            .then(() => resolve(resizedFile))
            .catch(reject);
    });
}
```

#### 伺服器端處理
**檔案位置**: `in10101_saveImg.jsp`

```java
// 照片伺服器端處理
public void processUploadedPhoto(HttpServletRequest request) {
    try {
        // 1. 接收上傳檔案
        MultipartRequest multiRequest = new MultipartRequest(request, UPLOAD_PATH, MAX_SIZE);
        File uploadedFile = multiRequest.getFile("photoFile");
        
        // 2. 生成檔案路徑
        String caseId = multiRequest.getParameter("caseId");
        String category = multiRequest.getParameter("category");
        String filePath = generateFilePath(caseId, category);
        
        // 3. 複製原始檔案
        File originalFile = new File(filePath + "/original/" + uploadedFile.getName());
        FileUtils.copyFile(uploadedFile, originalFile);
        
        // 4. 產生浮水印版本
        File watermarkedFile = addWatermark(originalFile, caseId);
        
        // 5. 產生縮圖
        File thumbnailFile = generateThumbnail(originalFile, THUMBNAIL_SIZE);
        
        // 6. 更新資料庫記錄
        insertPhotoRecord(caseId, category, originalFile.getName(), 
                         watermarkedFile.getName(), thumbnailFile.getName());
        
    } catch (Exception e) {
        logger.error("照片處理失敗", e);
        throw new PhotoProcessingException(e);
    }
}
```

### 5.3 浮水印處理機制

#### 浮水印內容設計
```java
// 浮水印資訊產生
public WatermarkInfo generateWatermarkInfo(String caseId) {
    WatermarkInfo info = new WatermarkInfo();
    
    // 基本資訊
    info.setSystemName("新北市違章建築管理系統");
    info.setCaseId(caseId);
    info.setTimestamp(new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date()));
    
    // 獲取GPS資訊
    GPSLocation gps = getCurrentGPSLocation();
    if (gps != null) {
        info.setLatitude(gps.getLatitude());
        info.setLongitude(gps.getLongitude());
    }
    
    // 勘查人員資訊
    String inspector = getCurrentInspector();
    info.setInspector(inspector);
    
    return info;
}

// 浮水印套用
public File applyWatermark(File originalFile, WatermarkInfo info) {
    try {
        BufferedImage image = ImageIO.read(originalFile);
        Graphics2D g2d = image.createGraphics();
        
        // 設定字體和顏色
        g2d.setFont(new Font("微軟正黑體", Font.BOLD, 16));
        g2d.setColor(new Color(255, 255, 255, 200)); // 半透明白色
        
        // 浮水印內容
        String[] watermarkLines = {
            info.getSystemName(),
            "案件編號: " + info.getCaseId(),
            "拍攝時間: " + info.getTimestamp(),
            "拍攝人員: " + info.getInspector(),
            "GPS: " + info.getLatitude() + ", " + info.getLongitude()
        };
        
        // 計算位置 (右下角)
        int x = image.getWidth() - 300;
        int y = image.getHeight() - (watermarkLines.length * 20) - 10;
        
        // 繪製浮水印
        for (int i = 0; i < watermarkLines.length; i++) {
            g2d.drawString(watermarkLines[i], x, y + (i * 20));
        }
        
        g2d.dispose();
        
        // 儲存檔案
        File watermarkedFile = new File(originalFile.getParent() + "/watermarked/" + originalFile.getName());
        ImageIO.write(image, "jpg", watermarkedFile);
        
        return watermarkedFile;
    } catch (Exception e) {
        throw new WatermarkException("浮水印處理失敗", e);
    }
}
```

## 6. 勘查品質管控機制

### 6.1 勘查標準檢查清單

#### 必要勘查項目
```java
// 勘查品質檢查清單
public class InspectionQualityChecker {
    
    public QualityCheckResult validateInspection(InspectionData data) {
        QualityCheckResult result = new QualityCheckResult();
        
        // 1. 照片完整性檢查
        result.addCheck("照片數量", checkPhotoCount(data.getPhotos()));
        result.addCheck("照片品質", checkPhotoQuality(data.getPhotos()));
        result.addCheck("照片分類", checkPhotoCategories(data.getPhotos()));
        
        // 2. 測量資料檢查
        result.addCheck("測量完整性", checkMeasurementComplete(data.getMeasurements()));
        result.addCheck("測量合理性", checkMeasurementReasonable(data.getMeasurements()));
        
        // 3. 勘查記錄檢查
        result.addCheck("記錄完整性", checkRecordComplete(data.getRecord()));
        result.addCheck("結論合理性", checkConclusionReasonable(data.getConclusion()));
        
        return result;
    }
    
    private boolean checkPhotoCount(List<Photo> photos) {
        Map<String, Integer> categoryCount = photos.stream()
            .collect(Collectors.groupingBy(Photo::getCategory,
                    Collectors.summingInt(p -> 1)));
        
        // 檢查各分類最少照片數量
        return categoryCount.getOrDefault("VIOLATION", 0) >= 3 &&
               categoryCount.getOrDefault("SURROUNDING", 0) >= 2 &&
               categoryCount.getOrDefault("DETAIL", 0) >= 2;
    }
}
```

### 6.2 勘查結果審核機制

#### 多級審核流程
```java
// 勘查結果審核流程
public void reviewInspectionResult(String caseId, String reviewerId) {
    InspectionRecord record = getInspectionRecord(caseId);
    
    // 1. 基本資料審核
    if (!validateBasicData(record)) {
        returnForRevision(caseId, "基本資料不完整");
        return;
    }
    
    // 2. 照片證據審核
    if (!validatePhotoEvidence(record.getPhotos())) {
        returnForRevision(caseId, "照片證據不足");
        return;
    }
    
    // 3. 結論合理性審核
    if (!validateConclusion(record)) {
        returnForRevision(caseId, "勘查結論需要重新評估");
        return;
    }
    
    // 4. 審核通過，進入下一階段
    approveInspection(caseId, reviewerId);
    proceedToNextStage(caseId);
}
```

## 7. 勘查SOP文件

### 7.1 標準作業程序

#### 勘查前準備SOP
```markdown
## 勘查前準備標準作業程序

### 1. 接受勘查任務 (5分鐘)
- [ ] 確認案件編號與地址
- [ ] 檢查案件基本資料完整性
- [ ] 確認勘查日期與時間

### 2. 設備準備 (10分鐘)
- [ ] 檢查相機/平板電池電量
- [ ] 確認GPS功能正常
- [ ] 準備測量工具 (捲尺、雷射測距儀)
- [ ] 攜帶案件相關資料

### 3. 路線規劃 (5分鐘)
- [ ] 確認勘查地點GPS座標
- [ ] 規劃最佳路線
- [ ] 預估交通時間
- [ ] 通知相關人員出發時間
```

#### 現場勘查執行SOP
```markdown
## 現場勘查執行標準作業程序

### 1. 到達現場 (10分鐘)
- [ ] GPS定位確認到達
- [ ] 拍攝周邊環境全景照片
- [ ] 記錄天氣、時間等環境資訊
- [ ] 確認違建物位置

### 2. 違建調查 (30分鐘)
- [ ] 拍攝違建物各角度照片
- [ ] 測量違建物尺寸
- [ ] 調查使用狀況
- [ ] 確認建築結構與材料

### 3. 證據收集 (20分鐘)
- [ ] 拍攝相關證據照片
- [ ] 記錄違建物詳細資訊
- [ ] 調查是否有人居住
- [ ] 收集相關文件證據

### 4. 完成勘查 (10分鐘)
- [ ] 檢查資料完整性
- [ ] 上傳照片與資料
- [ ] 撰寫勘查結論
- [ ] 提出處理建議
```

### 7.2 勘查報告範本

#### 標準勘查報告格式
```java
// 勘查報告範本
public class InspectionReportTemplate {
    
    public String generateReport(InspectionData data) {
        StringBuilder report = new StringBuilder();
        
        // 報告標題
        report.append("新北市違章建築現場勘查報告\n");
        report.append("=".repeat(40)).append("\n\n");
        
        // 基本資訊
        report.append("一、基本資訊\n");
        report.append("案件編號：").append(data.getCaseId()).append("\n");
        report.append("勘查日期：").append(data.getInspectionDate()).append("\n");
        report.append("勘查人員：").append(data.getInspector()).append("\n");
        report.append("天氣狀況：").append(data.getWeather()).append("\n\n");
        
        // 違建物資訊
        report.append("二、違建物資訊\n");
        report.append("建築地址：").append(data.getAddress()).append("\n");
        report.append("建築類型：").append(data.getStructureType()).append("\n");
        report.append("使用性質：").append(data.getUsageType()).append("\n");
        report.append("建築面積：").append(data.getArea()).append(" 平方公尺\n\n");
        
        // 勘查結果
        report.append("三、勘查結果\n");
        report.append("違建事實：").append(data.isViolationExists() ? "確認違建" : "查無違建").append("\n");
        report.append("違建類型：").append(data.getViolationType()).append("\n");
        report.append("嚴重程度：").append(data.getSeverity()).append("\n\n");
        
        // 處理建議
        report.append("四、處理建議\n");
        report.append(data.getProcessingSuggestion()).append("\n\n");
        
        // 附件清單
        report.append("五、附件清單\n");
        for (Photo photo : data.getPhotos()) {
            report.append("- ").append(photo.getFilename())
                  .append(" (").append(photo.getCategory()).append(")\n");
        }
        
        return report.toString();
    }
}
```

## 8. 效能與最佳化

### 8.1 照片處理最佳化

#### 批次處理機制
```javascript
// 批次照片處理
function batchProcessPhotos(photos, caseId) {
    const BATCH_SIZE = 3;
    const batches = [];
    
    // 分割成批次
    for (let i = 0; i < photos.length; i += BATCH_SIZE) {
        batches.push(photos.slice(i, i + BATCH_SIZE));
    }
    
    // 逐批處理
    return batches.reduce((promise, batch) => {
        return promise.then(results => {
            return Promise.all(batch.map(photo => processPhoto(photo, caseId)))
                .then(batchResults => results.concat(batchResults));
        });
    }, Promise.resolve([]));
}
```

#### 照片壓縮最佳化
```javascript
// 智能照片壓縮
function smartCompress(file) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    return new Promise((resolve) => {
        img.onload = function() {
            // 計算最佳尺寸
            const { width, height } = calculateOptimalSize(img.width, img.height);
            
            canvas.width = width;
            canvas.height = height;
            
            // 繪製並壓縮
            ctx.drawImage(img, 0, 0, width, height);
            
            canvas.toBlob(resolve, 'image/jpeg', 0.8);
        };
        
        img.src = URL.createObjectURL(file);
    });
}
```

### 8.2 資料庫查詢最佳化

#### 勘查記錄查詢最佳化
```sql
-- 勘查案件查詢索引
CREATE INDEX idx_inspection_date_zone ON INSPECTION_RECORD 
(INSPECTION_DATE, ADDRESS_ZONE, STATUS);

-- 勘查人員工作量查詢
CREATE INDEX idx_inspector_workload ON INSPECTION_TASK 
(INSPECTOR, ASSIGN_DATE, STATUS);

-- 照片查詢最佳化
CREATE INDEX idx_photo_case_category ON PHOTO_RECORD 
(CASE_ID, CATEGORY, UPLOAD_DATE);
```

## 9. 安全性與權限控制

### 9.1 照片存取權限

#### 照片檢視權限控制
```java
// 照片存取權限檢查
public boolean hasPhotoAccessPermission(String userId, String caseId, String photoId) {
    // 1. 檢查使用者是否為案件相關人員
    if (isCaseRelatedUser(userId, caseId)) {
        return true;
    }
    
    // 2. 檢查使用者職級權限
    UserRole role = getUserRole(userId);
    if (role.getLevel() >= SUPERVISOR_LEVEL) {
        return true;
    }
    
    // 3. 檢查同部門權限
    if (isSameDepartment(userId, getCaseAssignedUser(caseId))) {
        return true;
    }
    
    return false;
}
```

### 9.2 資料保護機制

#### 敏感資料遮罩
```java
// 照片EXIF資料脫敏
public Photo sanitizePhotoMetadata(Photo photo, String requestUserId) {
    UserRole role = getUserRole(requestUserId);
    
    if (role.getLevel() < ADMIN_LEVEL) {
        // 一般使用者移除GPS等敏感資訊
        photo.removeGPSData();
        photo.removePersonalInfo();
    }
    
    return photo;
}
```

## 10. 系統整合與介面

### 10.1 GIS系統整合

#### 地圖定位整合
```javascript
// GIS地圖整合
function integrateCaseWithMap(caseId, gpsLocation) {
    // 1. 在地圖上標記案件位置
    addCaseMarker(gpsLocation, {
        caseId: caseId,
        status: 'INSPECTION',
        icon: 'inspection-marker'
    });
    
    // 2. 顯示周邊相關案件
    showNearbyCase(gpsLocation, 500); // 500公尺範圍
    
    // 3. 整合建築執照資料
    overlayBuildingPermits(gpsLocation);
    
    // 4. 顯示土地使用分區
    showZoningInfo(gpsLocation);
}
```

### 10.2 行動裝置支援

#### 行動版勘查介面
```javascript
// 行動裝置勘查功能
function mobileInspectionInterface() {
    // 1. GPS自動定位
    navigator.geolocation.getCurrentPosition(function(position) {
        validateLocationAccuracy(position);
    });
    
    // 2. 相機整合
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        setupCameraCapture();
    }
    
    // 3. 語音備註功能
    if ('webkitSpeechRecognition' in window) {
        setupVoiceNotes();
    }
    
    // 4. 離線模式支援
    setupOfflineMode();
}
```

## 總結

### 現場勘查流程特點

#### 系統優勢
1. **完整的照片管理** - 從上傳到處理的全流程管理
2. **智能化處理** - 自動浮水印、GPS驗證、EXIF處理
3. **標準化作業** - 完整的SOP和品質管控機制
4. **多重驗證** - GPS、時間戳、人員等多重驗證機制

#### 技術特色
1. **階層式儲存** - 井然有序的檔案管理架構
2. **批次處理** - 高效率的照片處理流程
3. **權限控制** - 細緻的存取權限管理
4. **整合能力** - 與GIS、行動裝置的良好整合

#### 改進建議
1. **行動化加強** - 開發專用行動APP
2. **AI輔助** - 導入AI自動辨識違建類型
3. **雲端整合** - 雲端儲存和同步機制
4. **即時通訊** - 勘查過程即時回報機制

新北市違章建築管理系統的現場勘查流程展現了完整的業務邏輯和技術實作，透過三階段作業流程（準備→執行→處理）、完善的照片管理機制、標準化的SOP，確保勘查作業的品質和一致性。系統具備良好的擴展性，為未來現代化改造奠定了堅實基礎。

---

**文件狀態**: ✅ 已完成  
**下一步**: 執行 T2.3.1 認定審核流程分析