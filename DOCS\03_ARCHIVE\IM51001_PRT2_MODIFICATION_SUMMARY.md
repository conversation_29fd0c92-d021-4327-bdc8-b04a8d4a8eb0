# im51001_prt_2.jsp 銷案排除修正摘要

## 執行時間
2025-07-09

## 問題描述
im51001_prt_2.jsp 產生月報表與清冊時，除了「一般違建拍照列管案件」外，其他6種類型的報表都沒有排除銷案案件。

## 修正內容
為以下6個查詢加入銷案排除條件 `(is_closed IS NULL OR is_closed <> '1')`：

### 修改位置
1. **第376行** - 一般違建認定案件（A_CHK）
2. **第382行** - 一般違建拆除案件（A_DEL）
3. **第385行** - 廣告違建認定案件（B_CHK）
4. **第388行** - 廣告違建拆除案件（B_DEL）
5. **第391行** - 下水道違建認定案件（C_CHK）
6. **第394行** - 下水道違建拆除案件（C_DEL）

### 修改前後對比
```java
// 修改前
sql_detail.append(" and ib_prcs = 'A' and "+t_yymm+"00000000 < reg_rec_date and reg_rec_date < "+t_yymm+"99999999  " );

// 修改後
sql_detail.append(" and ib_prcs = 'A' and (is_closed IS NULL OR is_closed <> '1') and "+t_yymm+"00000000 < reg_rec_date and reg_rec_date < "+t_yymm+"99999999  " );
```

## 影響範圍
此修正影響以下報表的統計數據：
- 認定科月報表認定案件清冊
- 拆除科月報表拆除案件清冊
- 廣拆科月報表認定案件清冊
- 廣拆科月報表拆除案件清冊
- 勞安科月報表認定案件清冊
- 勞安科月報表拆除案件清冊

## 檔案備份
- 原始檔案已備份至：`im51001_prt_2.jsp.backup`

## 預期效果
修正後，所有7種類型的清冊都會正確排除已銷案的案件，確保統計數據的準確性。

## 部署說明
由於這是 JSP 檔案，通常會在 Tomcat 重新編譯時自動生效。如果沒有自動生效，可能需要：
1. 清除 Tomcat work 目錄下的編譯快取
2. 重啟 Tomcat 服務