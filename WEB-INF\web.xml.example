<?xml version="1.0" encoding="UTF-8"?>
<!--
    N+1 查詢監控系統配置範例
    
    將此配置加入到現有的 web.xml 中以啟用 N+1 查詢監控功能
    注意：這是非侵入式的配置，不會影響現有系統功能
-->
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
         http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0">

    <!-- 現有的 web.xml 內容保持不變 -->
    
    <!-- ===== N+1 查詢監控系統配置 開始 ===== -->
    
    <!-- N+1 查詢監控 Filter -->
    <filter>
        <filter-name>NPlusOneMonitoringFilter</filter-name>
        <filter-class>com.ezek.report.MonitoringInjector$NPlusOneMonitoringFilter</filter-class>
        
        <!-- Filter 初始化參數 -->
        <init-param>
            <param-name>monitoringEnabled</param-name>
            <param-value>true</param-value>
            <description>是否啟用 N+1 查詢監控</description>
        </init-param>
        
        <init-param>
            <param-name>detectionThreshold</param-name>
            <param-value>5</param-value>
            <description>N+1 檢測閾值（相同查詢執行次數）</description>
        </init-param>
        
        <init-param>
            <param-name>logLevel</param-name>
            <param-value>WARN</param-value>
            <description>日誌級別：DEBUG, INFO, WARN, ERROR</description>
        </init-param>
        
        <init-param>
            <param-name>excludePatterns</param-name>
            <param-value>/static/*,/css/*,/js/*,/images/*</param-value>
            <description>排除監控的 URL 模式（逗號分隔）</description>
        </init-param>
    </filter>
    
    <!-- Filter 映射 - 監控所有 JSP 請求 -->
    <filter-mapping>
        <filter-name>NPlusOneMonitoringFilter</filter-name>
        <url-pattern>*.jsp</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
    </filter-mapping>
    
    <!-- Filter 映射 - 監控特定的 Handler 請求 -->
    <filter-mapping>
        <filter-name>NPlusOneMonitoringFilter</filter-name>
        <url-pattern>/im*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
    </filter-mapping>
    
    <!-- 監控管理 Servlet -->
    <servlet>
        <servlet-name>MonitoringManagementServlet</servlet-name>
        <servlet-class>com.ezek.report.MonitoringManagementServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>
    
    <servlet-mapping>
        <servlet-name>MonitoringManagementServlet</servlet-name>
        <url-pattern>/monitoring/*</url-pattern>
    </servlet-mapping>
    
    <!-- 系統參數配置 -->
    <context-param>
        <param-name>nplus1.monitoring.enabled</param-name>
        <param-value>true</param-value>
        <description>全域啟用/停用 N+1 監控</description>
    </context-param>
    
    <context-param>
        <param-name>nplus1.staticAnalysis.enabled</param-name>
        <param-value>true</param-value>
        <description>啟用靜態程式碼分析</description>
    </context-param>
    
    <context-param>
        <param-name>nplus1.largeDataset.analysis.enabled</param-name>
        <param-value>true</param-value>
        <description>啟用大量資料集分析</description>
    </context-param>
    
    <context-param>
        <param-name>nplus1.report.output.path</param-name>
        <param-value>/tmp/nplus1-reports</param-value>
        <description>報告輸出路徑</description>
    </context-param>
    
    <!-- 安全限制（可選） -->
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Monitoring Management</web-resource-name>
            <url-pattern>/monitoring/*</url-pattern>
            <http-method>GET</http-method>
            <http-method>POST</http-method>
        </web-resource-collection>
        <auth-constraint>
            <role-name>admin</role-name>
            <role-name>developer</role-name>
        </auth-constraint>
    </security-constraint>
    
    <!-- ===== N+1 查詢監控系統配置 結束 ===== -->
    
</web-app>