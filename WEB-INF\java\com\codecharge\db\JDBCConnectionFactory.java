package com.codecharge.db;

import com.ezek.config.SecureDBConnectionFactory;
import com.codecharge.util.CCLogger;

/**
 * JDBC 連線工廠 - 替代原有的 JDBCConnectionFactory
 * 
 * 此類別提供向後相容性，將所有連線請求轉發到 SecureDBConnectionFactory
 * 以確保現有程式碼無需修改即可使用新的安全配置系統
 * 
 * 主要變更：
 * 1. 所有連線請求都透過 SecureDBConnectionFactory 處理
 * 2. 資料庫配置從 SecureConfigManager 讀取
 * 3. 支援環境變數和加密密碼
 * 4. 保持原有 API 不變
 * 
 * <AUTHOR> Security Team
 * @version 2.0 (Security Enhanced)
 * @since 2025-07-09
 */
public class JDBCConnectionFactory {
    
    private static final CCLogger logger = CCLogger.getInstance();
    
    /**
     * 獲取 JDBC 連線 - 主要入口點
     * 
     * @param connectionName 連線名稱 ("DBConn" 或 "DBConn2")
     * @return JDBCConnection 物件
     */
    public static JDBCConnection getJDBCConnection(String connectionName) {
        try {
            logger.debug("JDBCConnectionFactory: 請求連線 - " + connectionName);
            
            // 將請求轉發到 SecureDBConnectionFactory
            return SecureDBConnectionFactory.getJDBCConnection(connectionName);
            
        } catch (Exception e) {
            logger.error("JDBCConnectionFactory: 獲取連線失敗 - " + connectionName, e);
            throw new RuntimeException("資料庫連線獲取失敗: " + connectionName, e);
        }
    }
    
    /**
     * 建立 JDBC 連線（備用方法）
     */
    public static JDBCConnection createJDBCConnection(String connectionName) {
        return getJDBCConnection(connectionName);
    }
    
    /**
     * 獲取預設連線 (DBConn)
     */
    public static JDBCConnection getDefaultConnection() {
        return getJDBCConnection("DBConn");
    }
    
    /**
     * 獲取次要連線 (DBConn2)
     */
    public static JDBCConnection getSecondaryConnection() {
        return getJDBCConnection("DBConn2");
    }
    
    /**
     * 測試連線是否可用
     */
    public static boolean testConnection(String connectionName) {
        try {
            SecureDBConnectionFactory factory = SecureDBConnectionFactory.getInstance();
            return factory.testConnection(connectionName);
        } catch (Exception e) {
            logger.error("JDBCConnectionFactory: 連線測試失敗 - " + connectionName, e);
            return false;
        }
    }
    
    /**
     * 獲取連線池狀態
     */
    public static String getConnectionPoolStatus(String connectionName) {
        try {
            SecureDBConnectionFactory factory = SecureDBConnectionFactory.getInstance();
            return factory.getConnectionPoolStatus(connectionName);
        } catch (Exception e) {
            logger.error("JDBCConnectionFactory: 獲取連線池狀態失敗 - " + connectionName, e);
            return "連線池狀態獲取失敗";
        }
    }
    
    /**
     * 重新載入連線池配置
     */
    public static void reloadConnectionPools() {
        try {
            logger.info("JDBCConnectionFactory: 重新載入連線池配置");
            SecureDBConnectionFactory factory = SecureDBConnectionFactory.getInstance();
            factory.reloadAllConnectionPools();
            logger.info("JDBCConnectionFactory: 連線池配置重新載入完成");
        } catch (Exception e) {
            logger.error("JDBCConnectionFactory: 重新載入連線池配置失敗", e);
            throw new RuntimeException("重新載入連線池配置失敗", e);
        }
    }
    
    /**
     * 關閉連線工廠
     */
    public static void shutdown() {
        try {
            logger.info("JDBCConnectionFactory: 開始關閉連線工廠");
            SecureDBConnectionFactory factory = SecureDBConnectionFactory.getInstance();
            factory.shutdown();
            logger.info("JDBCConnectionFactory: 連線工廠關閉完成");
        } catch (Exception e) {
            logger.error("JDBCConnectionFactory: 關閉連線工廠失敗", e);
        }
    }
}