<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE HTML>
<html>
<head>
    <title>處理記錄功能測試</title>
    <link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
    <script src="js/jquery/jquery.js" type="text/javascript"></script>
    <script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
</head>
<body>
    <div class="container" style="margin-top: 20px;">
        <h2>Excel 處理記錄功能測試</h2>
        
        <div class="alert alert-info">
            <strong>測試說明：</strong>此頁面用於測試新開發的處理記錄功能，包括 AJAX 狀態查詢、處理記錄顯示和匯出功能。
        </div>
        
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">1. AJAX 狀態查詢測試</h3>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <label>測試批次 ID:</label>
                    <input type="text" class="form-control" id="testImportId" value="d480dc22-d411-45b6-940d-dc066993c71d" style="width: 400px;">
                    <small class="help-block">請輸入有效的批次 ID 進行測試</small>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testAjaxStatus();">測試舊 AJAX 狀態查詢</button>
                <button type="button" class="btn btn-success" onclick="testNewAPI('batch_info');">測試新 API - 批次資訊</button>
                <button type="button" class="btn btn-info" onclick="testNewAPI('stage_stats');">測試新 API - 階段統計</button>
                <button type="button" class="btn btn-warning" onclick="testNewAPI('processing_logs');">測試新 API - 處理記錄</button>
                
                <div id="ajaxResult" class="well" style="margin-top: 15px; display: none;">
                    <h4>AJAX 回應結果：</h4>
                    <pre id="ajaxResultContent"></pre>
                </div>
            </div>
        </div>
        
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">2. 處理記錄頁面測試</h3>
            </div>
            <div class="panel-body">
                <p>點擊以下連結測試處理記錄頁面：</p>
                <div class="btn-group-vertical" role="group" style="width: 100%;">
                    <a href="processing_log_frontend.jsp?import_id=d480dc22-d411-45b6-940d-dc066993c71d" 
                       class="btn btn-info" target="_blank">查看完整處理記錄 (新前後端分離版)</a>
                    <a href="processing_log_frontend.jsp?import_id=d480dc22-d411-45b6-940d-dc066993c71d&view=exceptions" 
                       class="btn btn-warning" target="_blank">查看異常記錄 (新前後端分離版)</a>
                    <a href="im52101_processing_log_simple.jsp?import_id=d480dc22-d411-45b6-940d-dc066993c71d" 
                       class="btn btn-default" target="_blank">查看完整處理記錄 (CodeCharge版-備用)</a>
                </div>
            </div>
        </div>
        
        
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">3. 列表頁面整合測試</h3>
            </div>
            <div class="panel-body">
                <p>前往主要列表頁面測試完整整合功能：</p>
                <a href="im52101_lis.jsp" class="btn btn-primary btn-lg">前往批次 Excel 匯入列表</a>
            </div>
        </div>
        
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">4. 資料庫驗證</h3>
            </div>
            <div class="panel-body">
                <div id="dbValidation">
                    <p>測試批次的統計資訊：</p>
                    <ul class="list-group">
                        <li class="list-group-item">批次狀態: COMPLETED (匯入完成)</li>
                        <li class="list-group-item">總行數: 364</li>
                        <li class="list-group-item">處理成功: 330</li>
                        <li class="list-group-item">錯誤行數: 0 (主表顯示)</li>
                        <li class="list-group-item">實際異常記錄: 34 筆 EXCEL_PARSING 失敗</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script type="text/javascript">
        function testAjaxStatus() {
            const importId = $('#testImportId').val().trim();
            
            if (!importId) {
                alert('請輸入批次 ID');
                return;
            }
            
            $.ajax({
                url: 'im52101_ajax.jsp',
                method: 'GET',
                data: { id: importId },
                dataType: 'json',
                success: function(response) {
                    $('#ajaxResultContent').text(JSON.stringify(response, null, 2));
                    $('#ajaxResult').show();
                    
                    // 分析結果
                    let analysis = '\n\n=== 結果分析 ===\n';
                    analysis += '狀態: ' + response.status + '\n';
                    analysis += '有處理記錄: ' + (response.hasProcessingLogs ? '是' : '否') + '\n';
                    analysis += '有異常記錄: ' + (response.hasExceptions ? '是' : '否') + '\n';
                    analysis += '可查看詳情: ' + (response.canViewDetails ? '是' : '否') + '\n';
                    analysis += '可查看異常: ' + (response.canViewExceptions ? '是' : '否') + '\n';
                    analysis += '可下載報告: ' + (response.canDownloadReport ? '是' : '否') + '\n';
                    
                    $('#ajaxResultContent').text($('#ajaxResultContent').text() + analysis);
                },
                error: function(xhr, status, error) {
                    $('#ajaxResultContent').text('錯誤: ' + error + '\n回應: ' + xhr.responseText);
                    $('#ajaxResult').show();
                }
            });
        }
        
        function testNewAPI(action) {
            const importId = $('#testImportId').val().trim();
            
            if (!importId) {
                alert('請輸入批次 ID');
                return;
            }
            
            const data = {
                action: action,
                import_id: importId
            };
            
            if (action === 'processing_logs') {
                data.page = 1;
                data.size = 10; // 測試時只顯示前10筆
                data.view = 'all';
            }
            
            $.ajax({
                url: 'api_processing_log.jsp',
                method: 'GET',
                data: data,
                dataType: 'json',
                success: function(response) {
                    $('#ajaxResultContent').text(JSON.stringify(response, null, 2));
                    $('#ajaxResult').show();
                    
                    // 分析結果
                    let analysis = '\n\n=== API 測試結果分析 ===\n';
                    analysis += '動作: ' + action + '\n';
                    analysis += '狀態: ' + response.result + '\n';
                    
                    if (response.result === 'OK') {
                        if (action === 'batch_info') {
                            analysis += '檔案名稱: ' + (response.data.original_file_name || 'N/A') + '\n';
                            analysis += '狀態: ' + (response.data.status_display || 'N/A') + '\n';
                            analysis += '總筆數: ' + (response.data.total_rows_in_excel || 0) + '\n';
                        } else if (action === 'stage_stats') {
                            analysis += '階段數: ' + (response.data ? response.data.length : 0) + '\n';
                        } else if (action === 'processing_logs') {
                            analysis += '記錄數: ' + (response.data ? response.data.length : 0) + '\n';
                            if (response.pagination) {
                                analysis += '總頁數: ' + response.pagination.total_pages + '\n';
                                analysis += '總記錄數: ' + response.pagination.total_records + '\n';
                            }
                        }
                    } else {
                        analysis += '錯誤訊息: ' + (response.message || 'N/A') + '\n';
                    }
                    
                    $('#ajaxResultContent').text($('#ajaxResultContent').text() + analysis);
                },
                error: function(xhr, status, error) {
                    $('#ajaxResultContent').text('API 測試錯誤: ' + error + '\n回應: ' + xhr.responseText);
                    $('#ajaxResult').show();
                }
            });
        }
    </script>
</body>
</html>