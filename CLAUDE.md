# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# 新北市違章建築管理系統 - Claude Code 開發指南

## 🎯 系統核心資訊

### 系統定位
- **類型**：政府違章建築管理系統（Legacy系統）
- **規模**：37萬筆案件、100萬筆流程記錄
- **架構**：CodeCharge Studio三層架構（JSP+XML+Handlers）
- **狀態**：穩定運行但需現代化改造

### 技術架構
- **應用伺服器**：Apache Tomcat 9.0.98
- **主資料庫**：PostgreSQL (localhost:5432/bms)
- **輔助資料庫**：SQL Server (**************:2433/ramsGIS)
- **前端技術**：Bootstrap 5.3.3 + jQuery 3.7.1
- **開發框架**：CodeCharge Studio（已停止維護）

## 🔑 關鍵開發資訊

### 資料庫連線（⚠️ 硬編碼密碼需外部化）
```properties
# PostgreSQL
DBConn.url=************************************
DBConn.username=postgres
DBConn.password=S!@h@202203

# SQL Server (GIS)
DBConn2.url=*************************************************
DBConn2.username=user_rams2
DBConn2.password=$ystemOnlin168
```

### CodeCharge三層架構模式
```
功能模組/
├── *_man.jsp      # 表現層（UI）
├── *_man.xml      # 配置層（資料綁定）
└── *_Handlers.jsp # 邏輯層（業務處理）
```

### 檔案命名規範
- `im10xxx_man.jsp` - 管理頁面
- `im10xxx_lis.jsp` - 列表頁面
- `im10xxx_que.jsp` - 查詢頁面
- `case_xxx.jsp` - 自訂API端點

## 📊 業務邏輯核心

### 三類違建分工
| 類型 | 狀態碼範圍 | 負責單位 |
|------|------------|----------|
| 一般違建 | 2xx | 拆除科 |
| 廣告違建 | 3xx/4xx | 廣告科 |
| 下水道違建 | 5xx | 勞安科 |

### 狀態碼編碼規則（XYZ格式）
- **X**：業務類型（2=一般, 3=廣告, 5=下水道）
- **Y**：處理階段（0-9）
- **Z**：細部狀態（0-f）

### 三階段業務流程
1. **認定階段**（2xx）：231→232→234→239
2. **排拆階段**（3xx）：331→334→339
3. **結案階段**（4xx）：441→442→449→460

### 協同作業機制
- **協同中**：234/244/254
- **協同退回**：235/245/255（新增功能）
- **協同完成**：23b/24b/25b

## 🗄️ 核心資料表

### buildcase（案件主表）
- `case_no` - 案件編號（主鍵）
- `caseopened` - 當前狀態碼
- `s_empno` - 承辦人
- `case_con_user` - 協同承辦人

### tbflow（流程記錄）
- 記錄所有狀態變更歷程
- 支援多人協同處理追蹤

### ibmcode（系統參數）
- 78種代碼類型（code_type）
- RLT類型定義126個狀態碼

## ⚠️ 開發注意事項

### 必須遵守的規則
1. **查詢資料庫驗證**：禁止推測，必須實際查詢
2. **使用參數化查詢**：防止SQL注入
3. **事務控制**：複雜操作需完整事務
4. **狀態轉換驗證**：使用觸發器確保合法性

### 安全議題
- 資料庫密碼需外部化配置
- 實施輸入驗證和XSS防護
- 使用ContentSecurityPolicyFilter

### 效能考量
- 資料庫連線池：主庫80、輔庫100
- 建立適當索引（特別是caseopened、s_empno）
- 使用分頁查詢避免大量資料

## 📁 文件位置

### 核心文件
- `/DOCS/02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md` - 系統架構
- `/DOCS/02_REFERENCE/DATABASE_COMPLETE_GUIDE.md` - 資料庫指南
- `/DOCS/02_REFERENCE/BUSINESS_PROCESS_COMPLETE_GUIDE.md` - 業務流程
- `/DOCS/02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md` - 技術實施

### 活躍文件
- `/DOCS/01_ACTIVE/TASK_TRACKING_DETAIL_V3.md` - 任務追蹤
- `/DOCS/01_ACTIVE/EMERGENCY_FIXES.md` - 緊急修復

### MOI資料匯出系統文件
- `/DOCS/違章建築資料拋送至國土署系統/CLAUDE.md` - MOI系統Claude開發指南
- `/DOCS/違章建築資料拋送至國土署系統/CI-CD-PIPELINE-GUIDE.md` - CI/CD管道指南
- `/DOCS/違章建築資料拋送至國土署系統/NTPC.MOI.DataExporter/README.md` - 資料匯出服務
- `/DOCS/違章建築資料拋送至國土署系統/MOI.MonitoringDashboard/README.md` - 監控儀表板

### WebService API文件
- `/DOCS/04_WEBSERVICE_API/01_WebService_Overview.md` - WebService概覽
- `/DOCS/04_WEBSERVICE_API/02_WebService_Stages_Basic.md` - 基礎階段API
- `/DOCS/04_WEBSERVICE_API/03_WebService_Stages_Advanced.md` - 進階階段API
- `/DOCS/04_WEBSERVICE_API/04_WebService_CodeTables.md` - 代碼表API

## 🚀 快速開始

### 開發環境設置
```bash
# 基本工具需求
# Java 11+ (for legacy JSP system)
# .NET 8 SDK (for MOI data exporter)
# Docker Desktop
# PostgreSQL 15+
# Git

# 資料庫連線測試
PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms

# 查詢狀態碼定義
SELECT code_seq, code_desc FROM ibmcode WHERE code_type='RLT' ORDER BY code_seq;
```

### 常用開發指令

#### Legacy JSP系統
```bash
# 部署到Tomcat
# 直接複製JSP檔案到webapps目錄
# 無正式建置程序

# 重啟Tomcat
$CATALINA_HOME/bin/shutdown.sh
$CATALINA_HOME/bin/startup.sh
```

#### MOI資料匯出系統(.NET 8)
```bash
# 建置與測試
cd DOCS/違章建築資料拋送至國土署系統/NTPC.MOI.DataExporter
dotnet restore
dotnet build --configuration Release
dotnet test --configuration Release --collect:"XPlat Code Coverage"

# 啟動開發環境
docker-compose up -d

# 執行Windows Service
dotnet run --project src/NTPC.MOI.DataExporter.Service

# 執行監控儀表板
cd ../MOI.MonitoringDashboard
dotnet run
```

#### 資料庫測試
```bash
# 執行PostgreSQL測試套件
PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f DOCS/違章建築資料拋送至國土署系統/psql_tests/00_master_test_runner.sql

# 個別測試
psql -h localhost -p 5432 -U postgres -d bms -f psql_tests/02_status_code_mapping_test.sql
```

### 常用SQL查詢
```sql
-- 查詢特定狀態案件
SELECT * FROM buildcase WHERE caseopened = '231' AND s_empno = 'EMP001';

-- 查詢案件流程歷程
SELECT * FROM tbflow WHERE case_no = 'A113000001' ORDER BY flow_sdate;

-- 查詢狀態碼對應
SELECT internal_code, moi_stage, description 
FROM status_code_mapping 
WHERE internal_code IN ('235', '245', '255');
```

## 🔧 Agent並行處理架構

### 使用時機
- 大規模程式碼分析
- 多模組同時開發
- 複雜業務流程梳理
- MOI系統多元件同時開發

### 配置原則
- 最少4個Agent並行
- 最多10個Agent並行
- 使用TodoWrite管理任務

### 混合系統開發策略
- **Legacy系統**：單一Agent處理JSP/Java程式碼
- **MOI系統**：多Agent並行處理.NET專案
- **資料庫操作**：專門Agent處理PostgreSQL/SQL Server
- **文件維護**：專門Agent更新文件和註解

## 🛠️ 系統特徵與限制

### 技術債務
1. **CodeCharge Studio**：RAD工具已停止維護
2. **自動生成代碼**：難以維護和單元測試
3. **混合邏輯**：JSP混雜表現層與業務邏輯
4. **舊版規範**：Web Application 2.3（2001年）

### 反模式特徵
- 高度耦合的三層架構
- 頁面中心的事件驅動模式
- 缺乏明確的關注點分離
- 類似早期ASP.NET Web Forms

### 觸發器防護網
- 91個資料庫觸發器
- 自動編號、狀態驗證、審計追蹤
- 30年穩定運行的核心機制

## 📝 重要提醒

### 開發必知
1. **禁止推測**：必須實際查詢資料庫或程式碼
2. **協同退回**：235/245/255是新增功能
3. **品質控制**：92c機制用於資料繕校
4. **文件位置**：所有文件在/DOCS目錄下

### 安全注意
- site.properties含硬編碼密碼
- 需實施參數化查詢防SQL注入
- ContentSecurityPolicyFilter已部署

### 效能關鍵
- buildcase表37萬筆需索引優化
- tbflow表100萬筆需分頁查詢
- 連線池上限：主庫80、輔庫100

## 🚨 疑難排解

### 常見問題

#### 1. 資料庫連線問題
```bash
# 檢查PostgreSQL狀態
sudo systemctl status postgresql

# 檢查SQL Server連線
sqlcmd -S **************,2433 -U user_rams2 -P '$ystemOnlin168' -Q "SELECT @@VERSION"
```

#### 2. Tomcat部署問題
```bash
# 檢查Tomcat日誌
tail -f $CATALINA_HOME/logs/catalina.out

# 重新部署
rm -rf $CATALINA_HOME/webapps/ROOT/*
cp -r /path/to/source/* $CATALINA_HOME/webapps/ROOT/
```

#### 3. MOI系統問題
```bash
# 檢查Windows Service狀態
Get-Service -Name "NTPC.MOI.DataExporter"

# 重啟服務
Restart-Service -Name "NTPC.MOI.DataExporter"

# 檢查容器狀態
docker-compose ps
docker-compose logs -f
```

### 日誌位置
- **Tomcat日誌**：`$CATALINA_HOME/logs/`
- **PostgreSQL日誌**：`/var/log/postgresql/`
- **MOI Service日誌**：`C:\Services\NTPC.MOI.DataExporter\logs\`
- **Docker日誌**：`docker-compose logs`

# important-instruction-reminders
- 開發時必須查詢資料庫驗證，禁止推測
- 使用TodoWrite管理複雜任務
- 參考/DOCS下的完整文件
- MOI系統使用.NET 8開發，Legacy系統使用JSP
- 狀態碼235/245/255是協同退回功能（新增）
- 資料庫密碼需外部化，避免硬編碼
- CI/CD管道支援Azure DevOps和GitHub Actions