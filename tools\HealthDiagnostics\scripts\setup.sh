#!/bin/bash

# Health Diagnostics Tool 設定腳本
# 用於初始化診斷工具環境

echo "================================"
echo "Health Diagnostics Tool Setup"
echo "新北市違章建築管理系統"
echo "================================"
echo

# 設定變數
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_HOME="$(dirname "$SCRIPT_DIR")"

# 建立必要目錄
echo "建立目錄結構..."
mkdir -p "$APP_HOME/lib/extensions"
mkdir -p "$APP_HOME/config"
mkdir -p "$APP_HOME/reports"
mkdir -p "$APP_HOME/logs"
mkdir -p "$APP_HOME/temp"

# 設定執行權限
echo "設定執行權限..."
chmod +x "$SCRIPT_DIR/diagnostics.sh"
chmod +x "$SCRIPT_DIR/schedule.sh"

# 建立預設配置檔案
echo "建立配置檔案..."

# diagnostics.properties
cat > "$APP_HOME/config/diagnostics.properties" << 'EOF'
# Health Diagnostics Tool 主要配置
# 生成時間: $(date)

# 診斷引擎設定
diagnostics.threadpool.size=10
diagnostics.analyzer.timeout=300000
diagnostics.autofix.enabled=true
diagnostics.logging.detailed=false

# 報告設定
diagnostics.report.output.path=./reports
diagnostics.report.format=HTML,JSON
diagnostics.report.retention.days=30

# 效能閾值設定
performance.query.slow.threshold=5000
performance.memory.max.percent=85
performance.cpu.max.percent=90
performance.thread.max.count=500

# 安全掃描設定
security.scan.sql.injection=true
security.scan.xss=true
security.scan.sensitive.data=true
security.scan.weak.passwords=true

# 資源限制
resource.report.max.size.mb=100
resource.issues.max.per.analyzer=1000
resource.scans.max.concurrent=5
EOF

# database.properties
cat > "$APP_HOME/config/database.properties" << 'EOF'
# 資料庫連線配置
# 注意：生產環境應使用加密或環境變數

# PostgreSQL 主資料庫
db.primary.driver=org.postgresql.Driver
db.primary.url=************************************
db.primary.username=postgres
db.primary.password=S!@h@202203

# SQL Server GIS 資料庫
db.secondary.driver=net.sourceforge.jtds.jdbc.Driver
db.secondary.url=*************************************************
db.secondary.username=user_rams2
db.secondary.password=$ystemOnlin168

# 連線池設定
db.pool.min.connections=2
db.pool.max.connections=10
db.pool.timeout=30000
EOF

# thresholds.properties
cat > "$APP_HOME/config/thresholds.properties" << 'EOF'
# 診斷閾值配置

# 系統資源閾值
system.disk.usage.warning=80
system.disk.usage.critical=90
system.memory.usage.warning=75
system.memory.usage.critical=85

# 資料庫閾值
database.connection.time.warning=1000
database.connection.time.critical=5000
database.query.count.warning=1000
database.query.count.critical=5000

# 效能閾值
performance.response.time.warning=3000
performance.response.time.critical=10000
performance.gc.overhead.warning=5
performance.gc.overhead.critical=10

# 安全閾值
security.failed.login.warning=10
security.failed.login.critical=50
security.password.age.warning=90
security.password.age.critical=180
EOF

# logging.properties
cat > "$APP_HOME/config/logging.properties" << 'EOF'
# Java Logging 配置

# 全域日誌級別
.level=INFO

# 控制台處理器
handlers=java.util.logging.ConsoleHandler,java.util.logging.FileHandler

# 控制台設定
java.util.logging.ConsoleHandler.level=INFO
java.util.logging.ConsoleHandler.formatter=java.util.logging.SimpleFormatter

# 檔案處理器設定
java.util.logging.FileHandler.level=ALL
java.util.logging.FileHandler.formatter=java.util.logging.SimpleFormatter
java.util.logging.FileHandler.pattern=logs/diagnostics_%g.log
java.util.logging.FileHandler.limit=10485760
java.util.logging.FileHandler.count=10

# 格式設定
java.util.logging.SimpleFormatter.format=%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS %4$s %2$s %5$s%6$s%n

# 套件級別設定
com.ntpc.violation.diagnostics.level=INFO
com.ntpc.violation.diagnostics.analyzers.level=FINE
EOF

# 建立範例 cron 設定
cat > "$APP_HOME/config/cron.example" << 'EOF'
# Health Diagnostics Tool Cron 排程範例

# 每天凌晨 2:00 執行完整診斷
0 2 * * * /path/to/diagnostics.sh --full-scan --silent --output-format=HTML,JSON

# 每小時執行快速檢查
0 * * * * /path/to/diagnostics.sh --quick-check --silent

# 每週一早上 6:00 執行效能分析
0 6 * * 1 /path/to/diagnostics.sh --performance-analysis --output-format=HTML,PDF

# 每月 1 日執行安全掃描
0 3 1 * * /path/to/diagnostics.sh --security-scan --auto-fix --output-format=HTML,PDF,JSON
EOF

# 下載必要的 JAR 檔案（如果需要）
echo
echo "檢查必要的程式庫..."

# 檢查 PostgreSQL JDBC Driver
if [ ! -f "$APP_HOME/lib/postgresql-42.2.18.jar" ]; then
    echo "注意：需要下載 PostgreSQL JDBC Driver"
    echo "請從 https://jdbc.postgresql.org/download.html 下載並放置到 lib/ 目錄"
fi

# 檢查 JTDS Driver
if [ ! -f "$APP_HOME/lib/jtds-1.3.1.jar" ]; then
    echo "注意：需要下載 jTDS JDBC Driver"
    echo "請從 http://jtds.sourceforge.net/ 下載並放置到 lib/ 目錄"
fi

echo
echo "設定完成！"
echo
echo "下一步："
echo "1. 編輯 config/database.properties 設定資料庫連線"
echo "2. 根據需要調整 config/diagnostics.properties"
echo "3. 執行 ./diagnostics.sh --help 查看使用說明"
echo
echo "範例命令："
echo "  ./diagnostics.sh --quick-check"
echo "  ./diagnostics.sh --full-scan --output-format=HTML,PDF"
echo