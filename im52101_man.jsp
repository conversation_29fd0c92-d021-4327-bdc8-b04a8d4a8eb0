<%@ page import="com.codecharge.*,
                 com.codecharge.components.*,
                 com.codecharge.util.*,
                 com.codecharge.events.*,
                 com.codecharge.feature.*,
                 com.codecharge.db.*,
                 com.codecharge.validation.*,
                 java.util.*,
                 java.io.*,
                 com.codecharge.util.cache.CacheEvent,
                 com.codecharge.util.cache.ICache,
                 com.codecharge.template.*"
%>
<%@ page contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="/ccstags" prefix="ccs" %>

<% if ((new im52101_manServiceChecker()).check(request, response, getServletContext())) return; %>

<%-- Include handlers --%>
<%@ include file="im52101_manHandlers.jsp" %>

<%
    // Check visibility conditions
    if (!im52101_manModel.isVisible()) return;
    if (im52101_manParent != null) {
        if (!im52101_manParent.getChild(im52101_manModel.getName()).isVisible()) return;
    }

    // Set page attributes
    pageContext.setAttribute("parent", im52101_manModel);
    pageContext.setAttribute("page", im52101_manModel);
    
    // Fire events
    im52101_manModel.fireOnInitializeViewEvent(new Event());
    im52101_manModel.fireBeforeShowEvent(new Event());

    // Setup page variables
    Page curPage = (Page) im52101_manModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Configure scripts
    String scripts = "|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im52101_manModel.isVisible()) return;
%>

<!DOCTYPE HTML>
<html>
<head>
    <ccs:meta header="Content-Type"/>
    <title>批次案件Excel匯入作業</title> <%-- Modified title --%>
    
    <%-- CSS Includes --%>
    <link rel="stylesheet" type="text/css" href="javascript/viewer/viewer.min.css">
    <link rel="stylesheet" type="text/css" href="javascript/ezek/fancybox/jquery.fancybox.css" media="screen">
    <link rel="stylesheet" type="text/css" href="javascript/ezek/chosen/chosen.min.css">
    <link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="in_recordgridstyle.css?11009110002">
    <link rel="stylesheet" type="text/css" href="im52101_man.css?11009110001"> <%-- Potentially new CSS --%>

    <%-- JavaScript Includes --%>
    <script src="ClientI18N.jsp?file=Functions.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
    <script src="ClientI18N.jsp?file=DatePicker.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
    <script src="ClientI18N.jsp?file=Globalize.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
    <ccs:attribute owner='page' name='scriptIncludes'/>
    <script src="functions_synct.js"></script>
    <script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
    <script src="javascript/viewer/viewer.min.js"></script>
    <script src="javascript/jquery-1.11.2.min.js"></script>
    <script src="javascript/ezek/fancybox/jquery.fancybox.pack.js"></script>
    <script src="javascript/ezek/chosen/chosen.jquery.min.js"></script>
    <script src="javascript/ezek/jquery.ezek.min.js"></script>
    <script src="javascript/jquery.blockUI.min.js"></script>
    <script src="functions_synct_im.js?1100624"></script> 
    <%-- Custom Styles --%>
    <style>
        .icon-img { 
            width: 17px; 
            height: 17px; 
            border: 0; 
            text-decoration: none; 
            outline: none; 
        }
        .align-top { vertical-align: top; }
        .ul-file { 
            list-style-type: none; 
            margin: 0; 
            padding: 0; 
        }
        .download-label {
            color: #337ab7;
            text-decoration: none;
        }
        .download-label:hover {
            color: #23527c;
            text-decoration: underline;
        }
        .stp_tittle { /* Renamed from .stp_tittle for clarity if needed, or keep if generic */
            color: #555;
            font-weight: bold;
        }
    </style>

    <%-- Main Content JavaScript --%>
    <script>
        // Date picker configuration
        var datePickers = [];
        
        $(document).ready(function() {
                // Hide update button initially, show cancel (or "back")
                $("#ibmcaseButton_Update").hide();
                $("#ibmcaseButton_Cancel").show(); // This button might navigate back or clear

                $('#ibmcaseButton_Update').click(function(){
                    saveEditData();
                });
        });
        //------------------------------
        // call upload 上傳 (Excel only)
        //------------------------------
        function addUPD(id){ // Removed 'type' as it's always excel
            $.fancybox.open({
                    margin: 10,
                    padding: 2,
                    href: 'im52101_upload.jsp?id=' + id, // Hardcoded type or new parameterization
                    type: 'iframe',
                    helpers: {
                        overlay: {
                            closeClick: false
                        }
                    },
                    iframe: {
                        scrolling: false,
                        preload: true
                    },
                    titleShow: false,
                    overlayOpacity: 0.3,
                    width: 1000,
                    height: 688, 
                    minWidth: 688,
                    hideOnContentClick: false,
                    closeBtn: false,
                    afterClose: function() {
                        refleshNowPic(id); // Removed 'type'
                    }
                });
        }

        //------------------------------
        // reflesh now pic (Excel only)
        //------------------------------
        function refleshNowPic(id) { // Removed 'type'
            $.ajax({
                type: "POST",
                url: "im52101_refleshAno.jsp?id="+ id, // Hardcoded type or new parameterization
                data: {
                    id: id,
                    random: Math.floor(Math.random() * 1000000)
                }
            }).done(function(o) {
                var $excelDownloadBlock = $('#show_Excel_UPD');
                $excelDownloadBlock.html(" ");
                setTimeout(function() {
                    $excelDownloadBlock.html(o);
                    // Check if excel file name is present to show Update button
                    if( $("#showExcelName") &&  $("#showExcelName").length > 0){
                        $("#ibmcaseButton_Cancel").hide(); // Or repurposed for another action
                        $("#ibmcaseButton_Update").show();
                    }
                }, 500);                 
            }).always(function() {
                //closeLoading()
            });
        }

        function saveEditData() {
            var id = $('#import_id').val();
            var acc_memo = $("#acc_memo").val(); // Keep memo if still relevant
           $.ajax({
                    url: encodeURI("im52101_man_save.jsp"),
                    type: "POST",
                    data: {
                        id: id,
                        acc_memo : acc_memo, // Keep memo if still relevant
                    }
                }).done(function (_data) {
                    // Redirect to a relevant list page for im52101
                    window.location = encodeURI("im52101_lis.jsp"); 
                }).fail(function (_data) {
                    console.log(data);
                    alert("案件資料儲存失敗!"); // Generic error, can be customized
            });
        }
    </script>
</head>

<body>
    <div class="container">
        <div class="up_titte" style="margin-bottom: 0">
            <span class="tittle_span">&nbsp;&nbsp;&nbsp;</span>
            <span class="tittle_label">&nbsp;批次案件Excel匯入作業</span> <%-- Modified label --%>
        </div>
        
        <ccs:record name='ibmcase'> <%-- This record name might need to change to 'im52101case' or similar --%>
            <form id="ibmcase" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
              <input type="hidden" id="import_id" 
                                            value="<ccs:control name='import_id'/>" 
                                            name="<ccs:control name='import_id' property='name'/>">  
                <table class="table" style="margin-bottom: 0" cellspacing="0" cellpadding="0">
                    <ccs:error_block>
                        <tr id="ibmcaseErrorBlock" class="Error">
                            <td colspan="2"> <ccs:error_text/></td> <%-- colspan changed from 4 to 2 as we have fewer columns now --%>
                        </tr>
                    </ccs:error_block>
                    
                    <%-- Removed "Step 1: Upload sign-off document" section --%>
                    
                    <tr>
                        <td style="border-top: 0" colspan="2"> <%-- colspan changed --%>
                            <label class="stp_tittle">步驟：上傳批次案件Excel檔案和備註</label> <%-- Modified step description --%>
                        </td>
                    </tr>

                    <tr>
                        <td style="border-top: 0" colspan="2"> <%-- colspan changed --%>
                            <div id="excel-upload-part" class="tab-content-submit"> <%-- Changed id for clarity --%>
                                <table class="table table-bordered" style="margin-bottom: 0">
                                    <tr class="Controls">
                                        <td class="th requiredFild" style="width:180px"> <%-- Adjusted width if necessary --%>
                                            <label>*&nbsp;批次案件Excel檔案&nbsp;</label>
                                        </td>
                                        <td id="show_Excel_UPD" class="td-content-required">
                                            <%-- import_id might come from a different source or be generated differently for im52101 --%>
                                            <a src="#" class="pointer" onclick="addUPD('<ccs:control name='import_id'/>');">上傳檔案</a>
                                        </td>
                                    </tr>

                                    <tr class="Controls">
                                        <td class="th">
                                            <label>&nbsp;備註說明&nbsp;</label> <%-- Modified label --%>
                                        </td>
                                        <td class="td-content">
                                            <textarea name="<ccs:control name='acc_memo' property='name'/>" 
                                                      cols="10" rows="3" id="acc_memo" 
                                                      style="width:100%"><ccs:control name='acc_memo'/></textarea>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </td> <%-- This td was mismatched, removed extra <td> --%>
                    </tr>
                    <tr class="Bottom">
                        <td style="BORDER-TOP: 0px; BORDER-RIGHT: 0px; BORDER-BOTTOM: 0px; TEXT-ALIGN: right; BORDER-LEFT: 0px" colspan="2"> <%-- colspan changed --%>
                            <%-- Button_Cancel might redirect to im52101_lis.jsp or a dashboard --%>
                            <ccs:button name='Button_Cancel'><input type="submit" id="ibmcaseButton_Cancel" class="btn btn-success" style="FLOAT: left" alt="回&nbsp;上&nbsp;頁" value="回&nbsp;上&nbsp;頁" name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button>
                            <ccs:button name='Button_Update'><input type="button" id="ibmcaseButton_Update" class="btn btn-primary" alt="存&nbsp;&nbsp;&nbsp;&nbsp;檔" value="存&nbsp;&nbsp;&nbsp;&nbsp;檔" name="<ccs:control name='Button_Update' property='name'/>"></ccs:button>
                        </td> 
                    </tr>
                </table>
            </form>
        </ccs:record>
    </div>

    <%-- Fire page events --%>
    <% im52101_manModel.fireBeforeOutputEvent(); %>
    <% im52101_manModel.fireBeforeUnloadEvent(); %>
</body>
</html> 