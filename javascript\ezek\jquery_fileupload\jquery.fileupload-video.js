/*
 * jQuery File Upload Video Preview Plugin
 * https://github.com/blueimp/jQuery-File-Upload
 *
 * Copyright 2013, <PERSON>
 * https://blueimp.net
 *
 * Licensed under the MIT license:
 * https://opensource.org/licenses/MIT
 */

/* global define, require */

(function (factory) {
  'use strict';
  if (typeof define === 'function' && define.amd) {
    // Register as an anonymous AMD module:
    define(['jquery', 'load-image', './jquery.fileupload-process'], factory);
  } else if (typeof exports === 'object') {
    // Node/CommonJS:
    factory(
      require('jquery'),
      require('blueimp-load-image/js/load-image'),
      require('./jquery.fileupload-process')
    );
  } else {
    // Browser globals:
    factory(window.jQuery, window.loadImage);
  }
})(function ($, loadImage) {
  'use strict';

  // Prepend to the default processQueue:
  $.blueimp.fileupload.prototype.options.processQueue.unshift(
    {
      action: 'loadVideo',
      // Use the action as prefix for the "@" options:
      prefix: true,
      fileTypes: '@',
      maxFileSize: '@',
      disabled: '@disableVideoPreview'
    },
    {
      action: 'setVideo',
      name: '@videoPreviewName',
      disabled: '@disableVideoPreview'
    }
  );

  // The File Upload Video Preview plugin extends the fileupload widget
  // with video preview functionality:
  $.widget('blueimp.fileupload', $.blueimp.fileupload, {
    options: {
      // The regular expression for the types of video files to load,
      // matched against the file type:
      loadVideoFileTypes: /^video\/.*$/
    },

    _videoElement: document.createElement('video'),

    processActions: {
      // Loads the video file given via data.files and data.index
      // as video element if the browser supports playing it.
      // Accepts the options fileTypes (regular expression)
      // and maxFileSize (integer) to limit the files to load:
      loadVideo: function (data, options) {
        if (options.disabled) {
          return data;
        }
        var file = data.files[data.index],
          url,
          video;
        if (
          this._videoElement.canPlayType &&
          this._videoElement.canPlayType(file.type) &&
          ($.type(options.maxFileSize) !== 'number' ||
            file.size <= options.maxFileSize) &&
          (!options.fileTypes || options.fileTypes.test(file.type))
        ) {
          url = loadImage.createObjectURL(file);
          if (url) {
            video = this._videoElement.cloneNode(false);
            video.src = url;
            video.controls = true;
            data.video = video;
            return data;
          }
        }
        return data;
      },

      // Sets the video element as a property of the file object:
      setVideo: function (data, options) {
        if (data.video && !options.disabled) {
          data.files[data.index][options.name || 'preview'] = data.video;
        }
        return data;
      }
    }
  });
});
