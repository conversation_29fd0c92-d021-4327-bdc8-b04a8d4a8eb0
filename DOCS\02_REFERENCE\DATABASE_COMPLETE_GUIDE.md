# 新北市違章建築管理系統 - 資料庫完整指南

## 目錄
1. [資料庫架構總覽](#資料庫架構總覽)
2. [連線配置](#連線配置)
3. [核心資料表結構](#核心資料表結構)
4. [觸發器系統](#觸發器系統)
5. [預存程序](#預存程序)
6. [索引策略](#索引策略)
7. [IBMCODE參數系統](#ibmcode參數系統)
8. [資料完整性機制](#資料完整性機制)
9. [效能優化](#效能優化)
10. [維護指南](#維護指南)

## 資料庫架構總覽

### 雙資料庫系統架構

```
┌─────────────────────────────────────────────────────┐
│                 應用程式層                           │
│                (Tomcat + JDBC)                      │
└────────────────┬──────────────┬────────────────────┘
                 │              │
    ┌────────────┴───────┐  ┌───┴──────────────┐
    │   PostgreSQL       │  │   SQL Server     │
    │   (主要業務庫)      │  │   (GIS資料庫)    │
    ├────────────────────┤  ├──────────────────┤
    │ Host: localhost    │  │ Host: 192.168... │
    │ Port: 5432        │  │ Port: 2433       │
    │ DB: bms           │  │ DB: ramsGIS      │
    │ 連線池: 80        │  │ 連線池: 100      │
    └────────────────────┘  └──────────────────┘
```

### 資料規模統計

| 資料表 | 記錄數 | 年增長率 | 說明 |
|--------|--------|----------|------|
| buildcase | 371,081 | ~5% | 違建案件主檔 |
| tbflow | 1,004,853 | ~8% | 案件流程記錄 |
| casefile | 1,028,463 | ~10% | 案件附件檔案 |
| caselawfee | 252,685 | ~3% | 規費收納記錄 |
| ibmcode | 2,107 | <1% | 系統參數代碼 |
| workflowlog | 2,850,000+ | ~15% | 操作日誌記錄 |

## 連線配置

### 主要資料庫（PostgreSQL）

```properties
# site.properties 配置
DBConn.driver=org.postgresql.Driver
DBConn.url=*************************************************************************
DBConn.username=postgres
DBConn.password=S!@h@202203  # ⚠️ 需外部化配置
DBConn.initialPoolSize=10
DBConn.maxPoolSize=80
DBConn.checkoutTimeout=5000
```

### 次要資料庫（SQL Server）

```properties
# GIS系統整合資料庫
DBConn2.driver=net.sourceforge.jtds.jdbc.Driver
DBConn2.url=**************************************************************
DBConn2.username=user_rams2
DBConn2.password=$ystemOnlin168  # ⚠️ 需外部化配置
DBConn2.maxPoolSize=100
```

### 連線池管理

```java
// DBConnectionManager 單例模式
public class DBConnectionManager {
    private static DBConnectionManager instance;
    private Map<String, ComboPooledDataSource> dataSources;
    
    public Connection getConnection(String poolName) {
        ComboPooledDataSource ds = dataSources.get(poolName);
        return ds.getConnection();
    }
}
```

## 核心資料表結構

### 1. buildcase（案件主表）

```sql
CREATE TABLE buildcase (
    case_no VARCHAR(20) PRIMARY KEY,     -- 案件編號
    caseopened VARCHAR(3),               -- 當前狀態碼
    s_empno VARCHAR(10),                 -- 承辦人員編
    case_con_user VARCHAR(10),           -- 協同承辦人
    case_con_date TIMESTAMP,             -- 協同開始時間
    tbvio_name VARCHAR(100),             -- 違建人姓名
    tbvio_unid VARCHAR(20),              -- 違建人身分證
    rep_name VARCHAR(100),               -- 查報人姓名
    address1 VARCHAR(200),               -- 違建地址
    rpt_day DATE,                        -- 查報日期
    chk_day DATE,                        -- 勘查日期
    conf_day DATE,                       -- 認定日期
    build_type VARCHAR(10),              -- 建物類型
    area_occupy NUMERIC(10,2),           -- 佔用面積
    create_date TIMESTAMP,               -- 建立時間
    update_date TIMESTAMP,               -- 更新時間
    -- 其他業務欄位...
);

-- 關鍵索引
CREATE INDEX idx_buildcase_caseopened ON buildcase(caseopened);
CREATE INDEX idx_buildcase_empno ON buildcase(s_empno);
CREATE INDEX idx_buildcase_rptday ON buildcase(rpt_day);
```

### 2. tbflow（流程記錄表）

```sql
CREATE TABLE tbflow (
    case_no VARCHAR(20),                 -- 案件編號
    case_state VARCHAR(3),               -- 狀態碼
    s_empno VARCHAR(10),                 -- 處理人員
    flow_sdate TIMESTAMP,                -- 開始時間
    flow_edate TIMESTAMP,                -- 結束時間
    flow_desc TEXT,                      -- 處理說明
    PRIMARY KEY (case_no, case_state, flow_sdate),
    FOREIGN KEY (case_no) REFERENCES buildcase(case_no)
);

-- 效能優化索引
CREATE INDEX idx_tbflow_state ON tbflow(case_state);
CREATE INDEX idx_tbflow_empno ON tbflow(s_empno);
CREATE INDEX idx_tbflow_dates ON tbflow(flow_sdate, flow_edate);
```

### 3. ibmcode（系統代碼表）

```sql
CREATE TABLE ibmcode (
    code_type VARCHAR(10),               -- 代碼類型
    code_seq VARCHAR(10),                -- 代碼值
    code_desc VARCHAR(100),              -- 代碼說明
    code_enname VARCHAR(100),            -- 英文名稱
    code_expired VARCHAR(1) DEFAULT '0', -- 是否停用
    code_order INTEGER,                  -- 排序順序
    PRIMARY KEY (code_type, code_seq)
);

-- 查詢優化索引
CREATE INDEX idx_ibmcode_type ON ibmcode(code_type);
CREATE INDEX idx_ibmcode_expired ON ibmcode(code_expired);
```

### 4. casefile（案件附件表）

```sql
CREATE TABLE casefile (
    file_id SERIAL PRIMARY KEY,          -- 檔案序號
    case_no VARCHAR(20),                 -- 案件編號
    file_type VARCHAR(10),               -- 檔案類型
    file_name VARCHAR(200),              -- 檔案名稱
    file_path VARCHAR(500),              -- 檔案路徑
    file_size BIGINT,                    -- 檔案大小
    upload_date TIMESTAMP,               -- 上傳時間
    upload_user VARCHAR(10),             -- 上傳人員
    FOREIGN KEY (case_no) REFERENCES buildcase(case_no)
);

-- 檔案查詢索引
CREATE INDEX idx_casefile_caseno ON casefile(case_no);
CREATE INDEX idx_casefile_type ON casefile(file_type);
CREATE INDEX idx_casefile_date ON casefile(upload_date);
```

### 5. workflowlog（操作日誌表）

```sql
CREATE TABLE workflowlog (
    log_id SERIAL PRIMARY KEY,           -- 日誌序號
    case_no VARCHAR(20),                 -- 案件編號
    action_type VARCHAR(20),             -- 操作類型
    action_user VARCHAR(10),             -- 操作人員
    action_date TIMESTAMP,               -- 操作時間
    action_desc TEXT,                    -- 操作說明
    before_state VARCHAR(3),             -- 操作前狀態
    after_state VARCHAR(3),              -- 操作後狀態
    ip_address VARCHAR(45),              -- IP位址
    browser_info VARCHAR(200)            -- 瀏覽器資訊
);

-- 稽核查詢索引
CREATE INDEX idx_workflowlog_case ON workflowlog(case_no);
CREATE INDEX idx_workflowlog_user ON workflowlog(action_user);
CREATE INDEX idx_workflowlog_date ON workflowlog(action_date);
```

## 觸發器系統

### 91個觸發器防護網

系統使用91個觸發器構建完整的資料完整性防護網：

#### 1. 自動編號觸發器

```sql
-- 案件編號自動產生
CREATE OR REPLACE FUNCTION generate_case_no() 
RETURNS TRIGGER AS $$
DECLARE
    v_year VARCHAR(3);
    v_seq VARCHAR(6);
    v_prefix VARCHAR(1);
BEGIN
    -- 取得民國年
    v_year := LPAD((EXTRACT(YEAR FROM CURRENT_DATE) - 1911)::TEXT, 3, '0');
    
    -- 根據案件類型決定前綴
    v_prefix := CASE NEW.case_type
        WHEN 'NORMAL' THEN 'A'
        WHEN 'ADVERT' THEN 'B'
        WHEN 'SEWER' THEN 'C'
        ELSE 'A'
    END;
    
    -- 取得流水號
    SELECT LPAD(COALESCE(MAX(SUBSTRING(case_no, 5, 6)::INTEGER), 0) + 1, 6, '0')
    INTO v_seq
    FROM buildcase
    WHERE SUBSTRING(case_no, 1, 1) = v_prefix
    AND SUBSTRING(case_no, 2, 3) = v_year;
    
    NEW.case_no := v_prefix || v_year || v_seq;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_generate_case_no
BEFORE INSERT ON buildcase
FOR EACH ROW
EXECUTE FUNCTION generate_case_no();
```

#### 2. 狀態轉換驗證觸發器

```sql
-- 狀態碼合法性檢查
CREATE OR REPLACE FUNCTION validate_state_transition()
RETURNS TRIGGER AS $$
DECLARE
    v_valid BOOLEAN;
BEGIN
    -- 檢查狀態轉換是否合法
    SELECT EXISTS(
        SELECT 1 FROM state_transition_rules
        WHERE from_state = OLD.caseopened
        AND to_state = NEW.caseopened
    ) INTO v_valid;
    
    IF NOT v_valid THEN
        RAISE EXCEPTION '不合法的狀態轉換: % -> %', 
            OLD.caseopened, NEW.caseopened;
    END IF;
    
    -- 記錄狀態變更
    INSERT INTO state_change_log(
        case_no, from_state, to_state, 
        change_user, change_date
    ) VALUES (
        NEW.case_no, OLD.caseopened, NEW.caseopened,
        CURRENT_USER, CURRENT_TIMESTAMP
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_validate_state
BEFORE UPDATE OF caseopened ON buildcase
FOR EACH ROW
WHEN (OLD.caseopened IS DISTINCT FROM NEW.caseopened)
EXECUTE FUNCTION validate_state_transition();
```

#### 3. 審計追蹤觸發器

```sql
-- 通用審計觸發器
CREATE OR REPLACE FUNCTION audit_trigger_func()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log(
            table_name, operation, user_name, 
            timestamp, new_data
        ) VALUES (
            TG_TABLE_NAME, TG_OP, CURRENT_USER,
            CURRENT_TIMESTAMP, row_to_json(NEW)
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log(
            table_name, operation, user_name,
            timestamp, old_data, new_data
        ) VALUES (
            TG_TABLE_NAME, TG_OP, CURRENT_USER,
            CURRENT_TIMESTAMP, row_to_json(OLD), row_to_json(NEW)
        );
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log(
            table_name, operation, user_name,
            timestamp, old_data
        ) VALUES (
            TG_TABLE_NAME, TG_OP, CURRENT_USER,
            CURRENT_TIMESTAMP, row_to_json(OLD)
        );
        RETURN OLD;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 套用審計觸發器到關鍵資料表
CREATE TRIGGER audit_buildcase AFTER INSERT OR UPDATE OR DELETE ON buildcase
FOR EACH ROW EXECUTE FUNCTION audit_trigger_func();
```

### 觸發器分類統計

| 類別 | 數量 | 用途 |
|------|------|------|
| 自動編號 | 5 | 案件、文號等自動產生 |
| 狀態驗證 | 12 | 狀態轉換合法性檢查 |
| 資料同步 | 8 | 跨表資料一致性維護 |
| 審計追蹤 | 15 | 變更歷程記錄 |
| 業務規則 | 25 | 複雜業務邏輯實作 |
| 效能優化 | 10 | 統計資料自動更新 |
| 資料清理 | 16 | 過期資料自動處理 |

## 預存程序

### 1. 案件統計分析

```sql
-- 案件狀態統計預存程序
CREATE OR REPLACE FUNCTION sp_case_statistics(
    p_start_date DATE,
    p_end_date DATE
) RETURNS TABLE (
    status_code VARCHAR(3),
    status_name VARCHAR(100),
    case_count INTEGER,
    percentage NUMERIC(5,2)
) AS $$
DECLARE
    v_total INTEGER;
BEGIN
    -- 計算總案件數
    SELECT COUNT(*) INTO v_total
    FROM buildcase
    WHERE create_date BETWEEN p_start_date AND p_end_date;
    
    -- 返回統計結果
    RETURN QUERY
    SELECT 
        b.caseopened,
        i.code_desc,
        COUNT(*)::INTEGER,
        ROUND(COUNT(*) * 100.0 / v_total, 2)
    FROM buildcase b
    JOIN ibmcode i ON i.code_type = 'RLT' AND i.code_seq = b.caseopened
    WHERE b.create_date BETWEEN p_start_date AND p_end_date
    GROUP BY b.caseopened, i.code_desc
    ORDER BY COUNT(*) DESC;
END;
$$ LANGUAGE plpgsql;
```

### 2. 案件自動分案

```sql
-- 自動分案預存程序
CREATE OR REPLACE FUNCTION sp_auto_assign_case(
    p_case_no VARCHAR(20)
) RETURNS VARCHAR(10) AS $$
DECLARE
    v_area_code VARCHAR(10);
    v_assigned_user VARCHAR(10);
    v_min_workload INTEGER;
BEGIN
    -- 取得案件所在區域
    SELECT area_code INTO v_area_code
    FROM buildcase
    WHERE case_no = p_case_no;
    
    -- 找出該區域工作量最少的承辦人
    SELECT u.user_id, COUNT(b.case_no)
    INTO v_assigned_user, v_min_workload
    FROM users u
    LEFT JOIN buildcase b ON b.s_empno = u.user_id
        AND b.caseopened NOT IN ('400', '401', '402') -- 排除已結案
    WHERE u.area_code = v_area_code
    AND u.is_active = 'Y'
    GROUP BY u.user_id
    ORDER BY COUNT(b.case_no), RANDOM() -- 工作量相同時隨機
    LIMIT 1;
    
    -- 更新案件承辦人
    UPDATE buildcase
    SET s_empno = v_assigned_user,
        assign_date = CURRENT_TIMESTAMP
    WHERE case_no = p_case_no;
    
    RETURN v_assigned_user;
END;
$$ LANGUAGE plpgsql;
```

### 3. 批次狀態更新

```sql
-- 批次更新逾期案件狀態
CREATE OR REPLACE FUNCTION sp_batch_update_overdue()
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER := 0;
BEGIN
    -- 更新超過30天未處理的現勘案件
    UPDATE buildcase
    SET caseopened = '21e',  -- 現勘逾期
        update_date = CURRENT_TIMESTAMP
    WHERE caseopened = '211'  -- 現勘中
    AND chk_day + INTERVAL '30 days' < CURRENT_DATE;
    
    GET DIAGNOSTICS v_count = ROW_COUNT;
    
    -- 記錄批次作業
    INSERT INTO batch_log(
        batch_type, affected_count, 
        execution_date, status
    ) VALUES (
        'OVERDUE_UPDATE', v_count,
        CURRENT_TIMESTAMP, 'SUCCESS'
    );
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;
```

## 索引策略

### 索引設計原則

1. **主鍵索引**：自動建立，確保唯一性
2. **外鍵索引**：加速關聯查詢
3. **查詢索引**：基於常用查詢條件
4. **複合索引**：多條件查詢優化
5. **部分索引**：特定條件資料子集

### 關鍵索引清單

```sql
-- 1. 案件查詢優化索引
CREATE INDEX idx_buildcase_composite 
ON buildcase(caseopened, s_empno, create_date);

-- 2. 地址搜尋索引（支援模糊查詢）
CREATE INDEX idx_buildcase_address 
ON buildcase USING gin(address1 gin_trgm_ops);

-- 3. 狀態流程查詢索引
CREATE INDEX idx_tbflow_active 
ON tbflow(case_no, case_state) 
WHERE flow_edate IS NULL;

-- 4. 日期範圍查詢索引
CREATE INDEX idx_buildcase_dates 
ON buildcase(rpt_day, chk_day, conf_day);

-- 5. 部分索引（只索引活躍案件）
CREATE INDEX idx_buildcase_active 
ON buildcase(caseopened, s_empno) 
WHERE caseopened NOT IN ('400', '401', '402');
```

## IBMCODE參數系統

### 78種系統參數類型

IBMCODE是系統的核心參數管理機制，包含78種代碼類型，超過1,500個參數值：

#### 核心業務參數

| 代碼類型 | 說明 | 參數數量 | 重要性 |
|----------|------|----------|--------|
| RLT | 狀態碼定義 | 126 | ⭐⭐⭐⭐⭐ |
| BTP | 建物類型 | 45 | ⭐⭐⭐⭐⭐ |
| RCD | 行政區代碼 | 29 | ⭐⭐⭐⭐⭐ |
| IFT | 檢查項目 | 38 | ⭐⭐⭐⭐ |
| USG | 使用分區 | 22 | ⭐⭐⭐⭐ |
| SRC | 查報來源 | 15 | ⭐⭐⭐⭐ |

#### 參數管理範例

```sql
-- 查詢所有狀態碼
SELECT code_seq, code_desc, code_enname
FROM ibmcode
WHERE code_type = 'RLT'
AND code_expired = '0'
ORDER BY code_seq;

-- 新增參數
INSERT INTO ibmcode(code_type, code_seq, code_desc, code_order)
VALUES ('BTP', 'B99', '其他建物類型', 99);

-- 停用參數（軟刪除）
UPDATE ibmcode
SET code_expired = '1'
WHERE code_type = 'RLT' AND code_seq = '999';
```

### 狀態碼編碼規則

```
狀態碼格式：XYZ
- X: 業務類型
  - 2: 一般違建
  - 3: 廣告違建  
  - 5: 下水道違建
- Y: 處理階段（0-9）
- Z: 細部狀態（0-f）

特殊狀態碼：
- 4xx: 結案系列
- 9xx: 系統管理
- x2c: 品質控制
```

## 資料完整性機制

### 1. 外鍵約束

```sql
-- 案件與流程的關聯
ALTER TABLE tbflow
ADD CONSTRAINT fk_tbflow_buildcase
FOREIGN KEY (case_no) REFERENCES buildcase(case_no)
ON DELETE RESTRICT ON UPDATE CASCADE;

-- 案件與附件的關聯
ALTER TABLE casefile
ADD CONSTRAINT fk_casefile_buildcase
FOREIGN KEY (case_no) REFERENCES buildcase(case_no)
ON DELETE CASCADE;
```

### 2. 檢查約束

```sql
-- 日期邏輯檢查
ALTER TABLE buildcase
ADD CONSTRAINT chk_date_sequence
CHECK (rpt_day <= chk_day AND chk_day <= conf_day);

-- 狀態碼格式檢查
ALTER TABLE buildcase
ADD CONSTRAINT chk_caseopened_format
CHECK (caseopened ~ '^[2-5][0-9][0-9a-f]$');
```

### 3. 唯一性約束

```sql
-- 案件編號唯一性
ALTER TABLE buildcase
ADD CONSTRAINT uk_case_no UNIQUE (case_no);

-- 防止重複協同
ALTER TABLE buildcase
ADD CONSTRAINT uk_collaboration
UNIQUE (case_no, case_con_user)
WHERE case_con_user IS NOT NULL;
```

## 效能優化

### 1. 查詢優化建議

```sql
-- 使用 EXPLAIN ANALYZE 分析查詢計畫
EXPLAIN (ANALYZE, BUFFERS) 
SELECT b.*, i.code_desc as status_name
FROM buildcase b
JOIN ibmcode i ON i.code_type = 'RLT' AND i.code_seq = b.caseopened
WHERE b.s_empno = 'EMP001'
AND b.caseopened NOT IN ('400', '401', '402')
ORDER BY b.create_date DESC
LIMIT 20;

-- 建立覆蓋索引減少回表
CREATE INDEX idx_buildcase_covering
ON buildcase(s_empno, caseopened, create_date)
INCLUDE (case_no, tbvio_name, address1);
```

### 2. 資料表分區策略

```sql
-- 按年份分區管理歷史資料
CREATE TABLE buildcase_2024 PARTITION OF buildcase
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE buildcase_2025 PARTITION OF buildcase
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
```

### 3. 定期維護作業

```sql
-- 更新統計資訊
ANALYZE buildcase;
ANALYZE tbflow;

-- 重建索引
REINDEX INDEX CONCURRENTLY idx_buildcase_composite;

-- 清理過期資料
DELETE FROM workflowlog
WHERE action_date < CURRENT_DATE - INTERVAL '2 years';

-- VACUUM 回收空間
VACUUM (VERBOSE, ANALYZE) buildcase;
```

## 維護指南

### 日常維護檢查清單

#### 每日檢查
- [ ] 資料庫連線狀態
- [ ] 錯誤日誌檢查
- [ ] 長時間執行查詢
- [ ] 鎖定狀況監控

#### 每週維護
- [ ] 索引使用率分析
- [ ] 資料表膨脹檢查
- [ ] 統計資訊更新
- [ ] 備份完整性驗證

#### 每月維護
- [ ] 效能基準測試
- [ ] 索引重建評估
- [ ] 資料清理作業
- [ ] 容量規劃檢討

### 常用維護指令

```sql
-- 檢查資料庫大小
SELECT 
    pg_database.datname,
    pg_size_pretty(pg_database_size(pg_database.datname)) AS size
FROM pg_database
ORDER BY pg_database_size(pg_database.datname) DESC;

-- 找出最大的資料表
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;

-- 檢查索引使用情況
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_scan = 0
AND schemaname = 'public'
ORDER BY schemaname, tablename;

-- 找出需要 VACUUM 的資料表
SELECT 
    schemaname,
    tablename,
    n_dead_tup,
    n_live_tup,
    round(n_dead_tup::numeric / NULLIF(n_live_tup + n_dead_tup, 0) * 100, 2) AS dead_ratio
FROM pg_stat_user_tables
WHERE n_dead_tup > 1000
ORDER BY dead_ratio DESC;
```

### 緊急處理程序

#### 1. 連線池耗盡
```sql
-- 檢查當前連線
SELECT pid, usename, application_name, client_addr, state
FROM pg_stat_activity
WHERE datname = 'bms';

-- 終止閒置連線
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'bms'
AND state = 'idle'
AND state_change < CURRENT_TIMESTAMP - INTERVAL '10 minutes';
```

#### 2. 查詢效能問題
```sql
-- 找出慢查詢
SELECT 
    query,
    calls,
    mean_exec_time,
    total_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 1000  -- 超過1秒
ORDER BY mean_exec_time DESC
LIMIT 10;
```

#### 3. 表格鎖定處理
```sql
-- 查看鎖定狀況
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## 最佳實踐建議

### 1. 開發規範
- 使用參數化查詢防止 SQL Injection
- 實作適當的事務控制
- 避免 SELECT * 查詢
- 使用連線池管理連線

### 2. 安全建議
- 定期更換資料庫密碼
- 實施最小權限原則
- 啟用 SSL 加密連線
- 定期安全稽核

### 3. 效能建議
- 定期更新統計資訊
- 監控慢查詢並優化
- 適時建立索引
- 避免過度索引

### 4. 備份策略
- 每日完整備份
- 每小時增量備份
- 異地備援機制
- 定期還原測試

## 結論

新北市違章建築管理系統的資料庫架構展現了一個成熟且穩定的設計，透過：

- **91個觸發器**構建的完整防護網
- **78種參數類型**的靈活配置機制
- **雙資料庫架構**的系統整合
- **完善的索引策略**確保查詢效能

雖然系統已運行30年，但透過持續的優化和維護，仍能有效支撐日益增長的業務需求。建議持續關注效能優化、安全加固，並逐步進行現代化改造，以確保系統的永續發展。