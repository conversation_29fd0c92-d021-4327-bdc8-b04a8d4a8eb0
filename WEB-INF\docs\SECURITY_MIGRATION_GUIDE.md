# 新北市違章建築管理系統 - 安全性遷移指南

## 概述

本指南說明如何將現有的硬編碼資料庫密碼系統遷移到安全的環境變數配置系統。此次遷移解決了重大安全漏洞，提供了企業級的配置管理解決方案。

## 🔒 安全性問題分析

### 現有問題
- **硬編碼密碼**: 資料庫密碼明文存儲在 `site.properties` 檔案中
- **版本控制風險**: 敏感資訊可能意外提交到版本控制系統
- **權限管理困難**: 無法精細控制對敏感配置的訪問
- **環境隔離不足**: 開發、測試、生產環境使用相同配置

### 解決方案
- ✅ **環境變數支持**: 優先從環境變數讀取敏感配置
- ✅ **密碼加密**: 敏感資訊使用AES-256加密存儲
- ✅ **多環境配置**: 支援開發、測試、生產環境分離
- ✅ **向後相容**: 現有程式碼無需修改
- ✅ **配置驗證**: 提供完整的配置檢查工具

## 📋 遷移前準備

### 1. 備份現有配置
```bash
# 備份現有配置檔案
cp /path/to/WEB-INF/site.properties /path/to/backup/site.properties.$(date +%Y%m%d_%H%M%S)

# 備份整個WEB-INF目錄（可選）
tar -czf webinf_backup_$(date +%Y%m%d_%H%M%S).tar.gz WEB-INF/
```

### 2. 記錄現有密碼
在遷移過程中，您需要以下資訊：
- PostgreSQL 資料庫密碼: `S!@h@202203`
- SQL Server 資料庫密碼: `$ystemOnlin168`
- 其他敏感配置項

### 3. 檢查環境需求
- Java 8 或更高版本
- OpenSSL（用於生成加密金鑰）
- 適當的檔案系統權限

## 🚀 遷移步驟

### 第一階段：安裝安全配置系統

1. **執行安裝腳本**:
```bash
cd /path/to/WEB-INF/scripts
./setup-secure-config.sh production
```

2. **驗證安裝**:
```bash
./validate-config.sh
```

### 第二階段：配置環境變數

1. **編輯環境變數檔案**:
```bash
vi /path/to/WEB-INF/config/production.env
```

2. **設定實際密碼**:
```bash
# 編輯以下行，將 YOUR_PASSWORD 替換為實際密碼
export DB_PRIMARY_PASSWORD="S!@h@202203"
export DB_SECONDARY_PASSWORD="$ystemOnlin168"
```

3. **生成加密金鑰**:
```bash
# 金鑰已由安裝腳本自動生成，位於：
ls -la /path/to/WEB-INF/config/encryption.key
```

### 第三階段：編譯Java類別

1. **編譯新的配置類別**:
```bash
cd /path/to/WEB-INF
javac -cp "classes:lib/*" java/com/ezek/config/*.java
javac -cp "classes:lib/*" java/com/codecharge/db/JDBCConnectionFactory.java
```

2. **將編譯後的類別移至classes目錄**:
```bash
cp java/com/ezek/config/*.class classes/com/ezek/config/
cp java/com/codecharge/db/JDBCConnectionFactory.class classes/com/codecharge/db/
```

### 第四階段：載入環境變數

1. **載入環境變數**:
```bash
source /path/to/WEB-INF/config/production.env
```

2. **驗證環境變數**:
```bash
echo $DB_PRIMARY_PASSWORD
echo $DB_SECONDARY_PASSWORD
```

### 第五階段：重新啟動應用程式

1. **停止Tomcat**:
```bash
sudo systemctl stop tomcat
# 或
/path/to/tomcat/bin/shutdown.sh
```

2. **啟動Tomcat**:
```bash
sudo systemctl start tomcat
# 或
/path/to/tomcat/bin/startup.sh
```

3. **檢查日誌**:
```bash
tail -f /path/to/tomcat/logs/catalina.out
```

## 🔧 配置選項說明

### 環境變數優先級
系統按以下順序讀取配置：
1. 環境變數 (`DB_PRIMARY_PASSWORD`)
2. 系統屬性 (`-DDB_PRIMARY_PASSWORD`)
3. 配置檔案 (`site.properties`)

### 配置檔案格式
```properties
# 使用環境變數
DBConn.password=${DB_PRIMARY_PASSWORD}

# 使用預設值
DBConn.maxconn=${DB_PRIMARY_MAX_CONN:80}

# 固定值
DBConn.driver=org.postgresql.Driver
```

### 加密密碼存儲
如果需要將密碼加密存儲在配置檔案中：
```bash
# 加密密碼
/path/to/WEB-INF/config/encrypt-password.sh "your_password"

# 在配置檔案中使用加密值
DBConn.password=encrypted_password_here
```

## 🔍 驗證遷移結果

### 1. 執行配置驗證
```bash
cd /path/to/WEB-INF/scripts
./validate-config.sh
```

### 2. 測試資料庫連線
```bash
# 檢查應用程式日誌
grep -i "database\|connection" /path/to/tomcat/logs/catalina.out

# 測試應用程式功能
# 訪問應用程式並執行基本操作
```

### 3. 安全性檢查
```bash
# 檢查檔案權限
ls -la /path/to/WEB-INF/config/

# 檢查是否還有明文密碼
grep -r "S!@h@202203\|$ystemOnlin168" /path/to/WEB-INF/ || echo "未發現明文密碼"
```

## 📊 各環境配置差異

### 開發環境 (development)
- 使用本地資料庫
- 啟用除錯日誌
- 允許預設密碼
- 檔案路徑: `WEB-INF/config/development.properties`

### 測試環境 (staging)
- 使用測試資料庫
- 中等安全性設定
- 效能監控啟用
- 檔案路徑: `WEB-INF/config/staging.properties`

### 生產環境 (production)
- 最高安全性設定
- 強制使用環境變數
- 完整監控和日誌
- 檔案路徑: `WEB-INF/config/production.properties`

## 🛠️ 故障排除

### 常見問題

1. **應用程式無法啟動**
   - 檢查環境變數是否正確設定
   - 確認Java類別已正確編譯
   - 查看Tomcat日誌獲取詳細錯誤

2. **資料庫連線失敗**
   - 驗證資料庫密碼是否正確
   - 檢查資料庫服務是否運行
   - 確認網路連線

3. **配置檔案找不到**
   - 檢查檔案路徑是否正確
   - 確認檔案權限設定
   - 驗證應用程式工作目錄

### 緊急回滾程序

如果遇到嚴重問題，可以快速回滾：

1. **恢復原始配置**:
```bash
cp /path/to/backup/site.properties.YYYYMMDD_HHMMSS /path/to/WEB-INF/site.properties
```

2. **恢復原始類別**:
```bash
# 刪除新的JDBCConnectionFactory類別
rm /path/to/WEB-INF/classes/com/codecharge/db/JDBCConnectionFactory.class

# 如果有原始備份，恢復它
# cp /path/to/backup/JDBCConnectionFactory.class /path/to/WEB-INF/classes/com/codecharge/db/
```

3. **重新啟動應用程式**:
```bash
sudo systemctl restart tomcat
```

## 📋 遷移檢查清單

### 遷移前
- [ ] 備份現有配置檔案
- [ ] 記錄所有敏感資訊
- [ ] 停止應用程式服務
- [ ] 通知用戶維護時間

### 遷移中
- [ ] 執行安裝腳本
- [ ] 編譯Java類別
- [ ] 設定環境變數
- [ ] 測試配置

### 遷移後
- [ ] 啟動應用程式
- [ ] 驗證資料庫連線
- [ ] 測試核心功能
- [ ] 檢查安全性設定
- [ ] 更新文件

## 🔐 安全性最佳實踐

### 1. 密碼管理
- 使用強密碼（至少12個字元）
- 定期更換密碼
- 使用密碼管理器
- 避免重複使用密碼

### 2. 環境變數安全
- 限制環境變數訪問權限
- 使用專用服務帳戶
- 避免在腳本中顯示敏感資訊
- 定期檢查環境變數設定

### 3. 檔案權限
- 配置檔案權限設為 640
- 金鑰檔案權限設為 600
- 腳本檔案權限設為 750
- 定期檢查檔案權限

### 4. 監控和日誌
- 啟用配置變更日誌
- 監控異常連線行為
- 設定安全性警告
- 定期檢查日誌

## 📞 支援資訊

### 技術支援
- 內部技術團隊：ext-1234
- 系統管理員：ext-5678
- 緊急支援：ext-9999

### 相關文件
- [系統架構文件](../DOCS/02_REFERENCE/SYSTEM_ARCHITECTURE_OVERVIEW.md)
- [資料庫設定指南](../DOCS/02_REFERENCE/DATABASE_COMPLETE_GUIDE.md)
- [部署說明](../DOCS/02_REFERENCE/TECHNICAL_IMPLEMENTATION_GUIDE.md)

## 📝 變更記錄

| 日期 | 版本 | 變更內容 | 作者 |
|------|------|----------|------|
| 2025-07-09 | 1.0 | 初始版本 | BMS Security Team |

---

**重要提醒**: 本遷移涉及關鍵的安全性改善，請務必在非生產環境中充分測試後再應用於生產環境。如有任何疑問，請立即聯繫技術支援團隊。