# 協同作業退回機制實施計畫

## 📋 文件資訊
- **文件編號**: DEV-2025-001
- **文件目的**: 詳細規劃協同作業退回機制的技術實施方案
- **作成日期**: 2025-07-05
- **狀態**: 技術規劃完成

---

## 🔍 現況分析

### 系統現有功能
1. **協同作業流程（im10201）**
   - 列表頁面：im10201_lis.jsp
   - 詳細頁面：im10201_man.jsp
   - 業務邏輯：im10201_manHandlers.jsp
   - 狀態碼：234（一般）、244（廣告）、254（下水道）

2. **抽回機制（case_withdraw.jsp）**
   - 支援陳核中案件抽回
   - 自動轉換狀態碼
   - 記錄操作歷程

3. **資料表結構**
   - ibmfym：歷程記錄表（無需修改）
   - ibmsts：當前狀態表
   - record：操作記錄表

### 現有抽回機制分析
```java
// case_withdraw.jsp 的狀態碼轉換邏輯
private String getWithdrawTargetStatus(String currentStatus) {
    switch(currentStatus) {
        case "232": return "231"; // 一般認定陳核 → 一般認定初建
        case "252": return "251"; // 下水道認定陳核 → 下水道認定初建
        case "342": return "344"; // 一般排拆陳核 → 一般排拆辦理中
        case "352": return "354"; // 廣告排拆陳核 → 廣告排拆辦理中
        case "362": return "364"; // 下水道排拆陳核 → 下水道排拆辦理中
        case "442": return "441"; // 一般結案陳核 → 一般結案辦理中
        case "452": return "451"; // 廣告結案陳核 → 廣告結案辦理中
        case "462": return "461"; // 下水道結案陳核 → 下水道結案辦理中
        default: return currentStatus;
    }
}
```

---

## 🎯 實施方案

### 1. 新增退回狀態碼定義

#### 狀態碼規劃
- **234** → **231**：一般協同退回至認定初建
- **244** → **241**：廣告協同退回至廣告認定初建（新增）
- **254** → **251**：下水道協同退回至認定初建

#### 實施步驟
1. 確認ibmcode表中是否已有241狀態碼
2. 如無，新增241狀態碼定義

```sql
-- 檢查241狀態碼是否存在
SELECT * FROM public.ibmcode WHERE code_type = 'RLT' AND code_seq = '241';

-- 如不存在，新增狀態碼
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, sts, ib_prcs, prcs_kd)
VALUES ('RLT', '241', '[廣告物]認定初建', 'Y', 'B', '02');
```

### 2. 建立協同退回API

#### 新增檔案：case_collaboration_return.jsp
```jsp
<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*"%>
<%@page import="java.sql.*, java.util.UUID, org.json.simple.*"%>

<%!
/**
 * 協同作業退回狀態碼轉換
 */
private String getCollaborationReturnStatus(String currentStatus) {
    switch(currentStatus) {
        case "234": return "231"; // 一般協同 → 一般認定初建
        case "244": return "241"; // 廣告協同 → 廣告認定初建
        case "254": return "251"; // 下水道協同 → 下水道認定初建
        default: return currentStatus;
    }
}
%>

<%
    // 取得參數
    String caseId = request.getParameter("caseId");
    String currentStatus = request.getParameter("accRlt");
    String empNo = request.getParameter("empNo");
    String returnReason = request.getParameter("returnReason");
    
    JSONObject result = new JSONObject();
    
    // 驗證狀態碼
    if (!"234".equals(currentStatus) && !"244".equals(currentStatus) && !"254".equals(currentStatus)) {
        result.put("success", false);
        result.put("message", "非協同作業狀態，無法退回");
        out.println(result.toString());
        return;
    }
    
    JDBCConnection conn = null;
    
    try {
        conn = JDBCConnectionFactory.getJDBCConnection("DBConn");
        conn.setAutoCommit(false);
        
        // 取得目標狀態碼
        String targetStatus = getCollaborationReturnStatus(currentStatus);
        
        // 1. 更新ibmsts當前狀態
        String updateSts = "UPDATE public.ibmsts SET acc_rlt = ?, " +
                          "acc_date = to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000, " +
                          "acc_time = to_number(to_char(current_timestamp, 'hh24mi'), '9999') " +
                          "WHERE case_id = ? AND acc_rlt = ?";
        
        PreparedStatement pstmt1 = conn.prepareStatement(updateSts);
        pstmt1.setString(1, targetStatus);
        pstmt1.setString(2, caseId);
        pstmt1.setString(3, currentStatus);
        int updated = pstmt1.executeUpdate();
        
        if (updated == 0) {
            throw new Exception("案件狀態不符，無法退回");
        }
        
        // 2. 新增ibmfym歷程記錄
        String insertFym = "INSERT INTO public.ibmfym (case_id, acc_seq, acc_date, acc_time, " +
                          "acc_job, acc_rlt, acc_memo, op_user, cr_date) " +
                          "VALUES (?, (SELECT COALESCE(MAX(acc_seq), 0) + 1 FROM ibmfym WHERE case_id = ?), " +
                          "to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000, " +
                          "to_number(to_char(current_timestamp, 'hh24mi'), '9999'), " +
                          "(SELECT job_title FROM ibmuser WHERE empno = ?), ?, ?, ?, " +
                          "to_number(to_char(current_date, 'yyyymmdd'), '99999999') - 19110000)";
        
        PreparedStatement pstmt2 = conn.prepareStatement(insertFym);
        pstmt2.setString(1, caseId);
        pstmt2.setString(2, caseId);
        pstmt2.setString(3, empNo);
        pstmt2.setString(4, targetStatus);
        pstmt2.setString(5, "協同退回：" + returnReason);
        pstmt2.setString(6, empNo);
        pstmt2.executeUpdate();
        
        // 3. 新增record操作記錄
        String insertRecord = "INSERT INTO public.record (uuid, case_id, rec_type, " +
                             "org_rec, new_rec, empno, create_date) " +
                             "VALUES (?, ?, '協同退回', ?, ?, ?, current_timestamp)";
        
        PreparedStatement pstmt3 = conn.prepareStatement(insertRecord);
        pstmt3.setString(1, UUID.randomUUID().toString());
        pstmt3.setString(2, caseId);
        pstmt3.setString(3, currentStatus);
        pstmt3.setString(4, targetStatus);
        pstmt3.setString(5, empNo);
        pstmt3.executeUpdate();
        
        // 提交交易
        conn.commit();
        
        result.put("success", true);
        result.put("message", "案件已成功退回");
        
    } catch (Exception e) {
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException re) {
                System.err.println("Rollback failed: " + re.getMessage());
            }
        }
        result.put("success", false);
        result.put("message", "退回失敗：" + e.getMessage());
        System.err.println("case_collaboration_return error: " + e.toString());
        e.printStackTrace();
    } finally {
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
                conn.closeConnection();
            } catch (Exception e) {
                System.err.println("Connection close failed: " + e.getMessage());
            }
        }
    }
    
    out.println(result.toString());
%>
```

### 3. 修改im10201_man.jsp介面

#### 新增退回按鈕
```javascript
// 在按鈕區域新增
<ccs:button name='Button_Return'>
    <input type="button" id="BMSDISOBEY_DISTButton_Return" 
           class="btn btn-warning" 
           value="退回承辦" 
           name="Button_Return">
</ccs:button>

// JavaScript處理
$("#BMSDISOBEY_DISTButton_Return").on("click", function() {
    // 顯示退回確認對話框
    showReturnDialog();
});

function showReturnDialog() {
    var dialogHtml = '<div id="returnDialog" class="modal fade">' +
        '<div class="modal-dialog">' +
        '<div class="modal-content">' +
        '<div class="modal-header">' +
        '<h4 class="modal-title">退回案件</h4>' +
        '</div>' +
        '<div class="modal-body">' +
        '<p>確定要將此案件退回給原承辦人嗎？</p>' +
        '<div class="form-group">' +
        '<label>退回原因：</label>' +
        '<textarea id="returnReason" class="form-control" rows="3" required></textarea>' +
        '</div>' +
        '</div>' +
        '<div class="modal-footer">' +
        '<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>' +
        '<button type="button" class="btn btn-warning" onclick="confirmReturn()">確定退回</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>';
    
    $('body').append(dialogHtml);
    $('#returnDialog').modal('show');
}

function confirmReturn() {
    var returnReason = $("#returnReason").val();
    if (!returnReason) {
        alert("請輸入退回原因");
        return;
    }
    
    var caseId = $("#BMSDISOBEY_DISTcase_id").val();
    var accRlt = $("#BMSDISOBEY_DISTacc_rlt").val();
    var empNo = '<%= SessionStorage.getInstance(request).getAttribute("UserID") %>';
    
    $.ajax({
        url: 'case_collaboration_return.jsp',
        type: 'POST',
        data: {
            caseId: caseId,
            accRlt: accRlt,
            empNo: empNo,
            returnReason: returnReason
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                alert(response.message);
                // 返回列表頁
                window.location.href = 'im10201_lis.jsp';
            } else {
                alert(response.message);
            }
        },
        error: function() {
            alert('系統錯誤，請稍後再試');
        }
    });
    
    $('#returnDialog').modal('hide');
}
```

### 4. 權限控制實施

#### 修改im10201_manHandlers.jsp
```java
// 在BeforeShow事件中新增權限檢查
String currentEmpNo = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
String originalEmpNo = Utils.convertToString(e.getRecord().getControl("reg_emp").getValue());

// 判斷是否顯示退回按鈕
boolean showReturnButton = false;
String accRlt = Utils.convertToString(e.getRecord().getControl("acc_rlt").getValue());

// 只有協同狀態(234/244/254)且非原承辦人才能退回
if (("234".equals(accRlt) || "244".equals(accRlt) || "254".equals(accRlt)) 
    && !currentEmpNo.equals(originalEmpNo)) {
    showReturnButton = true;
}

e.getRecord().getControl("show_return_button").setValue(showReturnButton ? "Y" : "N");
```

### 5. 測試場景設計

#### 測試案例
1. **正常退回測試**
   - 協同狀態234退回至231
   - 協同狀態244退回至241
   - 協同狀態254退回至251

2. **權限測試**
   - 原承辦人無法看到退回按鈕
   - 協同承辦人可以看到退回按鈕

3. **資料完整性測試**
   - ibmsts狀態更新正確
   - ibmfym歷程記錄完整
   - record操作記錄正確

4. **異常測試**
   - 非協同狀態無法退回
   - 交易失敗時正確回滾

---

## 📊 影響評估

### 技術影響
- 新增1個JSP檔案
- 修改2個現有檔案
- 資料庫可能需新增1筆狀態碼

### 業務影響
- 提升協同作業效率
- 減少溝通成本
- 保持完整操作軌跡

### 風險評估
- **低風險**：使用現有架構模式
- **已有參考**：case_withdraw.jsp提供實作範例
- **資料安全**：使用交易確保一致性

---

## 🚀 實施步驟

### Phase 1：準備工作（1天）
1. 確認241狀態碼
2. 備份相關檔案
3. 準備測試環境

### Phase 2：開發實施（2天）
1. 建立case_collaboration_return.jsp
2. 修改im10201_man.jsp介面
3. 更新im10201_manHandlers.jsp邏輯

### Phase 3：測試驗證（1天）
1. 單元測試
2. 整合測試
3. UAT測試

### Phase 4：部署上線（0.5天）
1. 正式環境部署
2. 監控與驗證

---

## 📝 注意事項

1. **交易完整性**：所有資料庫操作必須在同一交易中
2. **錯誤處理**：參考EXCEPTION_HANDLING_SOP.md
3. **日誌記錄**：重要操作需記錄詳細日誌
4. **程式碼風格**：遵循現有CodeCharge架構

---

*文件撰寫：【D】Claude Code - DevOps與技術架構任務組*
*完成日期：2025-07-05*