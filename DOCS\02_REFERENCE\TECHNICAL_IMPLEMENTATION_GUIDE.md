# 新北市違章建築管理系統 - 技術實施指南

## 目錄
1. [開發環境設置](#開發環境設置)
2. [系統部署指南](#系統部署指南)
3. [CodeCharge開發規範](#codecharge開發規範)
4. [資料庫操作指南](#資料庫操作指南)
5. [API開發規範](#api開發規範)
6. [前端開發指南](#前端開發指南)
7. [安全實施規範](#安全實施規範)
8. [效能優化指南](#效能優化指南)
9. [測試規範](#測試規範)
10. [維護與監控](#維護與監控)

## 開發環境設置

### 基礎環境需求

| 元件 | 版本要求 | 說明 |
|------|----------|------|
| JDK | 8 或 11 | Java開發環境 |
| Apache Tomcat | 9.0.98 | Servlet容器 |
| PostgreSQL | 12+ | 主資料庫 |
| Git | 2.x | 版本控制 |
| IDE | IntelliJ/Eclipse | 開發工具 |

### 環境設置步驟

#### 1. Java環境配置
```bash
# 設置JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-11-openjdk
export PATH=$JAVA_HOME/bin:$PATH

# 驗證安裝
java -version
javac -version
```

#### 2. Tomcat配置
```bash
# 下載並解壓Tomcat
wget https://downloads.apache.org/tomcat/tomcat-9/v9.0.98/bin/apache-tomcat-9.0.98.tar.gz
tar -xzvf apache-tomcat-9.0.98.tar.gz

# 設置環境變數
export CATALINA_HOME=/opt/apache-tomcat-9.0.98
export CATALINA_BASE=$CATALINA_HOME

# 配置記憶體參數
echo "JAVA_OPTS='-Xms512m -Xmx2048m -XX:MaxPermSize=256m'" > $CATALINA_HOME/bin/setenv.sh
```

#### 3. 資料庫連線設定
```xml
<!-- context.xml -->
<Context>
    <Resource name="jdbc/bmsDB"
              auth="Container"
              type="javax.sql.DataSource"
              driverClassName="org.postgresql.Driver"
              url="************************************"
              username="postgres"
              password="${db.password}"
              maxTotal="80"
              maxIdle="20"
              maxWaitMillis="10000"/>
</Context>
```

### 開發工具配置

#### IDE設定（IntelliJ IDEA）
1. 安裝 Tomcat 插件
2. 配置專案 SDK（Java 11）
3. 設定 Tomcat Server
4. 配置資料庫連線

#### 必要插件
- Lombok Support
- Database Tools
- Git Integration
- JSP Support

## 系統部署指南

### 部署前檢查清單

- [ ] 資料庫備份完成
- [ ] 設定檔參數確認
- [ ] 依賴套件版本檢查
- [ ] 硬碟空間足夠（至少10GB）
- [ ] 連接埠未被佔用（8080, 5432）

### 標準部署流程

#### 1. 程式碼部署
```bash
# 從Git拉取最新程式碼
git clone https://github.com/your-repo/bms.git
cd bms

# 複製到Tomcat webapps
cp -r * $CATALINA_HOME/webapps/ROOT/

# 設定檔案權限
chmod -R 755 $CATALINA_HOME/webapps/ROOT/
chown -R tomcat:tomcat $CATALINA_HOME/webapps/ROOT/
```

#### 2. 資料庫初始化
```sql
-- 建立資料庫
CREATE DATABASE bms WITH ENCODING 'UTF8';

-- 建立使用者
CREATE USER bms_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE bms TO bms_user;

-- 執行初始化腳本
psql -U bms_user -d bms -f init_schema.sql
psql -U bms_user -d bms -f init_data.sql
```

#### 3. 設定檔配置
```properties
# site.properties
# 資料庫連線（使用環境變數）
DBConn.url=jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/bms
DBConn.username=${DB_USER:postgres}
DBConn.password=${DB_PASSWORD}

# 系統參數
system.environment=${ENV:development}
system.debug=${DEBUG:false}
upload.max.size=10485760
```

#### 4. 啟動服務
```bash
# 啟動Tomcat
$CATALINA_HOME/bin/startup.sh

# 檢查啟動日誌
tail -f $CATALINA_HOME/logs/catalina.out

# 驗證部署
curl http://localhost:8080/health
```

## CodeCharge開發規範

### 檔案命名規範

#### JSP檔案命名
```
功能代碼_操作類型.jsp
範例：
- im10101_man.jsp  # 管理頁面
- im10101_lis.jsp  # 列表頁面
- im10101_que.jsp  # 查詢頁面
- im10101_rpt.jsp  # 報表頁面
```

#### 三層架構檔案
```
功能模組/
├── im10101_man.jsp        # 表現層
├── im10101_man.xml        # 配置層
└── im10101_Handlers.jsp   # 邏輯層
```

### 開發模式

#### 1. 標準CRUD模式
```jsp
<%-- im10101_man.jsp --%>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ taglib uri="/WEB-INF/tld/codecharge.tld" prefix="cc" %>

<cc:Page id="im10101_man" language="Java">
    <cc:Form id="buildcaseForm" method="post">
        <cc:TextField name="case_no" caption="案件編號" required="true"/>
        <cc:TextField name="tbvio_name" caption="違建人姓名"/>
        <cc:Button name="Insert" caption="新增" operation="Insert"/>
        <cc:Button name="Update" caption="更新" operation="Update"/>
        <cc:Button name="Delete" caption="刪除" operation="Delete"/>
    </cc:Form>
</cc:Page>
```

#### 2. 自訂API模式
```jsp
<%-- case_api.jsp --%>
<%@ page contentType="application/json; charset=UTF-8" %>
<%@ page import="java.sql.*" %>
<%@ page import="org.json.JSONObject" %>
<%
    JSONObject result = new JSONObject();
    Connection conn = null;
    
    try {
        // 取得連線
        conn = DBConnectionManager.getInstance().getConnection("DBConn");
        
        // 業務邏輯
        String action = request.getParameter("action");
        if ("query".equals(action)) {
            // 查詢處理
        } else if ("update".equals(action)) {
            // 更新處理
        }
        
        result.put("success", true);
        result.put("data", data);
        
    } catch (Exception e) {
        result.put("success", false);
        result.put("message", e.getMessage());
    } finally {
        if (conn != null) conn.close();
    }
    
    out.print(result.toString());
%>
```

### Handler開發規範

```java
// im10101_Handlers.jsp
public class BuildcaseHandlers {
    
    // Before Insert 事件
    public void beforeInsert(Event e) {
        // 資料驗證
        if (!validateData(e)) {
            e.getPage().addError("資料驗證失敗");
            return;
        }
        
        // 設定預設值
        e.getRecord().setValue("create_date", new Date());
        e.getRecord().setValue("create_user", getCurrentUser());
    }
    
    // After Update 事件
    public void afterUpdate(Event e) {
        // 記錄操作日誌
        logOperation("UPDATE", e.getRecord().getValue("case_no"));
        
        // 更新相關資料
        updateRelatedData(e.getRecord());
    }
    
    // Custom Event
    public void onApprove(Event e) {
        String caseNo = e.getPage().getParameter("case_no");
        // 自訂業務邏輯
    }
}
```

## 資料庫操作指南

### 連線管理

#### 使用連線池
```java
public class DatabaseUtil {
    private static final String POOL_NAME = "DBConn";
    
    public static Connection getConnection() throws SQLException {
        return DBConnectionManager.getInstance().getConnection(POOL_NAME);
    }
    
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close(); // 返回連線池
            } catch (SQLException e) {
                logger.error("關閉連線失敗", e);
            }
        }
    }
}
```

### 交易管理

```java
public void complexBusinessOperation(String caseNo) {
    Connection conn = null;
    try {
        conn = DatabaseUtil.getConnection();
        conn.setAutoCommit(false);
        
        // 步驟1：更新主表
        updateBuildcase(conn, caseNo);
        
        // 步驟2：插入流程記錄
        insertFlowRecord(conn, caseNo);
        
        // 步驟3：更新狀態
        updateStatus(conn, caseNo);
        
        // 提交交易
        conn.commit();
        
    } catch (Exception e) {
        // 回滾交易
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException ex) {
                logger.error("回滾失敗", ex);
            }
        }
        throw new BusinessException("操作失敗：" + e.getMessage());
    } finally {
        DatabaseUtil.closeConnection(conn);
    }
}
```

### SQL最佳實踐

#### 參數化查詢
```java
// 正確：使用參數化查詢
String sql = "SELECT * FROM buildcase WHERE case_no = ? AND s_empno = ?";
PreparedStatement pstmt = conn.prepareStatement(sql);
pstmt.setString(1, caseNo);
pstmt.setString(2, empNo);
ResultSet rs = pstmt.executeQuery();

// 錯誤：SQL注入風險
String sql = "SELECT * FROM buildcase WHERE case_no = '" + caseNo + "'";
```

#### 批次操作
```java
public void batchInsert(List<CaseData> dataList) throws SQLException {
    String sql = "INSERT INTO casefile (case_no, file_name, file_size) VALUES (?, ?, ?)";
    
    try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
        for (CaseData data : dataList) {
            pstmt.setString(1, data.getCaseNo());
            pstmt.setString(2, data.getFileName());
            pstmt.setLong(3, data.getFileSize());
            pstmt.addBatch();
            
            // 每1000筆執行一次
            if (++count % 1000 == 0) {
                pstmt.executeBatch();
            }
        }
        pstmt.executeBatch(); // 執行剩餘的
    }
}
```

## API開發規範

### RESTful API設計

#### URL規範
```
GET    /api/cases              # 查詢案件列表
GET    /api/cases/{id}         # 查詢單一案件
POST   /api/cases              # 新增案件
PUT    /api/cases/{id}         # 更新案件
DELETE /api/cases/{id}         # 刪除案件

POST   /api/cases/{id}/approve # 審核案件
POST   /api/cases/{id}/return  # 退回案件
```

#### 回應格式
```json
{
    "success": true,
    "code": "200",
    "message": "操作成功",
    "data": {
        "caseNo": "A113000001",
        "status": "231",
        "createDate": "2024-01-01"
    },
    "timestamp": "2024-01-01T10:00:00Z"
}
```

### API實作範例

```java
@WebServlet("/api/cases/*")
public class CaseAPIServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) {
        String pathInfo = req.getPathInfo();
        
        try {
            if (pathInfo == null || "/".equals(pathInfo)) {
                // 列表查詢
                handleListQuery(req, resp);
            } else {
                // 單筆查詢
                String caseNo = pathInfo.substring(1);
                handleSingleQuery(caseNo, resp);
            }
        } catch (Exception e) {
            sendError(resp, e.getMessage());
        }
    }
    
    private void handleListQuery(HttpServletRequest req, HttpServletResponse resp) {
        // 分頁參數
        int page = getIntParam(req, "page", 1);
        int size = getIntParam(req, "size", 20);
        
        // 查詢條件
        String status = req.getParameter("status");
        String empno = req.getParameter("empno");
        
        // 執行查詢
        List<Case> cases = caseService.query(status, empno, page, size);
        
        // 回傳結果
        sendSuccess(resp, cases);
    }
}
```

## 前端開發指南

### 技術棧
- Bootstrap 5.3.3（UI框架）
- jQuery 3.7.1（DOM操作）
- Custom JavaScript（業務邏輯）

### 頁面結構規範

```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>違建管理系統 - <%= pageTitle %></title>
    
    <!-- CSS -->
    <link href="/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/custom.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <jsp:include page="/includes/header.jsp"/>
    
    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <jsp:include page="/includes/sidebar.jsp"/>
            
            <!-- Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <%= content %>
            </main>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="/js/jquery.min.js"></script>
    <script src="/js/bootstrap.bundle.min.js"></script>
    <script src="/js/custom.js"></script>
</body>
</html>
```

### JavaScript開發規範

```javascript
// 命名空間
var BMS = BMS || {};

// 模組化開發
BMS.CaseModule = (function($) {
    'use strict';
    
    // 私有變數
    var config = {
        apiUrl: '/api/cases',
        pageSize: 20
    };
    
    // 私有方法
    function validateForm(formData) {
        if (!formData.caseNo) {
            showError('案件編號不可為空');
            return false;
        }
        return true;
    }
    
    // 公開方法
    return {
        // 初始化
        init: function() {
            this.bindEvents();
            this.loadData();
        },
        
        // 綁定事件
        bindEvents: function() {
            $('#searchBtn').on('click', this.search);
            $('#saveBtn').on('click', this.save);
            $('#form').on('submit', this.submit);
        },
        
        // 載入資料
        loadData: function() {
            $.ajax({
                url: config.apiUrl,
                method: 'GET',
                success: function(data) {
                    renderTable(data);
                },
                error: function(xhr) {
                    showError('載入失敗：' + xhr.responseText);
                }
            });
        },
        
        // 儲存資料
        save: function() {
            var formData = $('#form').serialize();
            
            if (!validateForm(formData)) {
                return;
            }
            
            $.ajax({
                url: config.apiUrl,
                method: 'POST',
                data: formData,
                success: function(data) {
                    showSuccess('儲存成功');
                    BMS.CaseModule.loadData();
                },
                error: function(xhr) {
                    showError('儲存失敗：' + xhr.responseText);
                }
            });
        }
    };
})(jQuery);

// 頁面載入完成後初始化
$(document).ready(function() {
    BMS.CaseModule.init();
});
```

### 表單驗證

```javascript
// 前端驗證框架
BMS.Validator = {
    rules: {
        required: function(value) {
            return value && value.trim() !== '';
        },
        
        email: function(value) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
        },
        
        phone: function(value) {
            return /^09\d{8}$/.test(value);
        },
        
        date: function(value) {
            return !isNaN(Date.parse(value));
        }
    },
    
    validate: function(form) {
        var isValid = true;
        
        $(form).find('[data-validate]').each(function() {
            var $field = $(this);
            var rules = $field.data('validate').split('|');
            var value = $field.val();
            
            for (var i = 0; i < rules.length; i++) {
                var rule = rules[i];
                
                if (!BMS.Validator.rules[rule](value)) {
                    $field.addClass('is-invalid');
                    isValid = false;
                    break;
                } else {
                    $field.removeClass('is-invalid');
                }
            }
        });
        
        return isValid;
    }
};
```

## 安全實施規範

### 認證與授權

#### Session管理
```java
public class AuthenticationFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpReq = (HttpServletRequest) request;
        HttpServletResponse httpResp = (HttpServletResponse) response;
        HttpSession session = httpReq.getSession(false);
        
        // 檢查登入狀態
        if (session == null || session.getAttribute("user") == null) {
            // 排除不需認證的路徑
            String path = httpReq.getRequestURI();
            if (!isPublicResource(path)) {
                httpResp.sendRedirect("/login.jsp");
                return;
            }
        }
        
        // 更新最後活動時間
        if (session != null) {
            session.setAttribute("lastAccessTime", System.currentTimeMillis());
        }
        
        chain.doFilter(request, response);
    }
}
```

#### 權限控制
```java
public class AuthorizationUtil {
    
    public static boolean hasPermission(User user, String resource, String action) {
        // 取得使用者角色
        List<Role> roles = user.getRoles();
        
        // 檢查每個角色的權限
        for (Role role : roles) {
            List<Permission> permissions = role.getPermissions();
            
            for (Permission perm : permissions) {
                if (perm.getResource().equals(resource) && 
                    perm.getAction().equals(action)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    // 頁面層級權限檢查
    public static void checkPageAccess(String pageId, User user) {
        if (!hasPermission(user, "PAGE", pageId)) {
            throw new AccessDeniedException("無權限存取此頁面");
        }
    }
}
```

### 輸入驗證

```java
public class InputValidator {
    
    // XSS防護
    public static String sanitizeHtml(String input) {
        if (input == null) return null;
        
        return input.replaceAll("<", "&lt;")
                   .replaceAll(">", "&gt;")
                   .replaceAll("\"", "&quot;")
                   .replaceAll("'", "&#x27;")
                   .replaceAll("/", "&#x2F;");
    }
    
    // SQL注入防護（使用參數化查詢）
    public static boolean isValidCaseNo(String caseNo) {
        // A + 民國年3碼 + 流水號6碼
        return caseNo != null && caseNo.matches("^[ABC]\\d{9}$");
    }
    
    // 檔案上傳驗證
    public static boolean isValidFile(Part filePart) {
        // 檢查檔案大小
        if (filePart.getSize() > MAX_FILE_SIZE) {
            return false;
        }
        
        // 檢查檔案類型
        String contentType = filePart.getContentType();
        if (!ALLOWED_TYPES.contains(contentType)) {
            return false;
        }
        
        // 檢查檔案名稱
        String fileName = getFileName(filePart);
        if (!isValidFileName(fileName)) {
            return false;
        }
        
        return true;
    }
}
```

### 安全標頭設定

```java
public class SecurityHeaderFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletResponse httpResp = (HttpServletResponse) response;
        
        // 防止點擊劫持
        httpResp.setHeader("X-Frame-Options", "DENY");
        
        // 防止MIME類型嗅探
        httpResp.setHeader("X-Content-Type-Options", "nosniff");
        
        // 啟用XSS保護
        httpResp.setHeader("X-XSS-Protection", "1; mode=block");
        
        // 內容安全策略
        httpResp.setHeader("Content-Security-Policy", 
            "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
        
        // HSTS（僅HTTPS環境）
        if (request.isSecure()) {
            httpResp.setHeader("Strict-Transport-Security", 
                "max-age=31536000; includeSubDomains");
        }
        
        chain.doFilter(request, response);
    }
}
```

## 效能優化指南

### 資料庫優化

#### 查詢優化
```sql
-- 建立複合索引
CREATE INDEX idx_buildcase_search 
ON buildcase(caseopened, s_empno, create_date DESC);

-- 使用EXPLAIN分析查詢
EXPLAIN (ANALYZE, BUFFERS)
SELECT b.*, i.code_desc
FROM buildcase b
JOIN ibmcode i ON i.code_type = 'RLT' AND i.code_seq = b.caseopened
WHERE b.s_empno = 'EMP001'
AND b.create_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY b.create_date DESC
LIMIT 20;
```

#### 連線池調校
```xml
<!-- 連線池配置 -->
<Resource name="jdbc/bmsDB"
    initialSize="10"           <!-- 初始連線數 -->
    maxTotal="80"             <!-- 最大連線數 -->
    maxIdle="20"              <!-- 最大閒置連線 -->
    minIdle="10"              <!-- 最小閒置連線 -->
    maxWaitMillis="10000"     <!-- 最大等待時間 -->
    validationQuery="SELECT 1" <!-- 驗證查詢 -->
    testOnBorrow="true"       <!-- 借用時測試 -->
    removeAbandonedTimeout="60" <!-- 廢棄超時 -->
/>
```

### 快取策略

```java
public class CacheManager {
    private static final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private static final long DEFAULT_TTL = 300000; // 5分鐘
    
    public static void put(String key, Object value) {
        put(key, value, DEFAULT_TTL);
    }
    
    public static void put(String key, Object value, long ttl) {
        long expiryTime = System.currentTimeMillis() + ttl;
        cache.put(key, new CacheEntry(value, expiryTime));
    }
    
    public static Object get(String key) {
        CacheEntry entry = cache.get(key);
        
        if (entry == null) {
            return null;
        }
        
        if (entry.isExpired()) {
            cache.remove(key);
            return null;
        }
        
        return entry.getValue();
    }
    
    // 定期清理過期項目
    static {
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleAtFixedRate(() -> {
            cache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        }, 0, 60, TimeUnit.SECONDS);
    }
}
```

### 分頁查詢優化

```java
public class PaginationUtil {
    
    public static <T> PageResult<T> paginate(String sql, String countSql, 
                                            int page, int size, 
                                            RowMapper<T> mapper) {
        // 計算總筆數
        int total = jdbcTemplate.queryForObject(countSql, Integer.class);
        
        // 計算分頁
        int offset = (page - 1) * size;
        String paginatedSql = sql + " LIMIT ? OFFSET ?";
        
        // 執行查詢
        List<T> data = jdbcTemplate.query(paginatedSql, 
            ps -> {
                ps.setInt(1, size);
                ps.setInt(2, offset);
            }, mapper);
        
        // 建立結果
        return PageResult.<T>builder()
            .data(data)
            .total(total)
            .page(page)
            .size(size)
            .totalPages((int) Math.ceil((double) total / size))
            .build();
    }
}
```

## 測試規範

### 單元測試

```java
public class CaseServiceTest {
    
    @Mock
    private CaseDAO caseDAO;
    
    @InjectMocks
    private CaseService caseService;
    
    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }
    
    @Test
    public void testCreateCase_Success() {
        // Given
        CaseData input = new CaseData();
        input.setTbvioName("測試違建人");
        input.setAddress("測試地址");
        
        when(caseDAO.insert(any(CaseData.class)))
            .thenReturn("A113000001");
        
        // When
        String caseNo = caseService.createCase(input);
        
        // Then
        assertNotNull(caseNo);
        assertEquals("A113000001", caseNo);
        verify(caseDAO, times(1)).insert(any(CaseData.class));
    }
    
    @Test(expected = BusinessException.class)
    public void testCreateCase_MissingRequired() {
        // Given
        CaseData input = new CaseData();
        // 缺少必要欄位
        
        // When
        caseService.createCase(input);
        
        // Then
        // 應拋出 BusinessException
    }
}
```

### 整合測試

```java
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
public class CaseControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Test
    @Transactional
    @Rollback
    public void testCreateCaseAPI() throws Exception {
        // 準備測試資料
        String requestBody = "{\n" +
            "  \"tbvioName\": \"測試違建人\",\n" +
            "  \"address\": \"測試地址\"\n" +
            "}";
        
        // 執行測試
        mockMvc.perform(post("/api/cases")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.caseNo").exists());
    }
}
```

### 效能測試

```java
public class PerformanceTest {
    
    @Test
    public void testBatchInsertPerformance() {
        long startTime = System.currentTimeMillis();
        
        List<CaseData> testData = generateTestData(10000);
        caseService.batchInsert(testData);
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 驗證效能要求：10000筆資料應在10秒內完成
        assertTrue("批次插入效能不符預期", duration < 10000);
        
        System.out.println("批次插入10000筆耗時：" + duration + "ms");
    }
}
```

## 維護與監控

### 日誌管理

```java
public class LoggingAspect {
    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);
    
    @Around("@annotation(Loggable)")
    public Object logMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        // 記錄方法進入
        logger.info("進入方法: {} 參數: {}", methodName, Arrays.toString(args));
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 執行方法
            Object result = joinPoint.proceed();
            
            // 記錄成功
            long duration = System.currentTimeMillis() - startTime;
            logger.info("方法執行成功: {} 耗時: {}ms", methodName, duration);
            
            return result;
            
        } catch (Exception e) {
            // 記錄異常
            logger.error("方法執行失敗: {} 錯誤: {}", methodName, e.getMessage(), e);
            throw e;
        }
    }
}
```

### 系統監控

```java
@Component
public class SystemMonitor {
    
    @Scheduled(fixedDelay = 60000) // 每分鐘執行
    public void monitorSystem() {
        // JVM記憶體監控
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        logger.info("記憶體使用: {}MB / {}MB", usedMemory, totalMemory);
        
        // 資料庫連線監控
        int activeConnections = getActiveConnectionCount();
        int idleConnections = getIdleConnectionCount();
        
        logger.info("資料庫連線: 活躍={}, 閒置={}", 
                   activeConnections, idleConnections);
        
        // 執行緒監控
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        int threadCount = threadBean.getThreadCount();
        
        logger.info("執行緒數量: {}", threadCount);
        
        // 告警判斷
        if (usedMemory > totalMemory * 0.9) {
            alertAdmin("記憶體使用率超過90%");
        }
        
        if (activeConnections > 70) {
            alertAdmin("資料庫連線數接近上限");
        }
    }
}
```

### 備份策略

```bash
#!/bin/bash
# backup.sh - 每日備份腳本

# 設定變數
BACKUP_DIR="/backup/bms"
DB_NAME="bms"
DATE=$(date +%Y%m%d_%H%M%S)

# 建立備份目錄
mkdir -p $BACKUP_DIR/$DATE

# 備份資料庫
pg_dump -U postgres -d $DB_NAME > $BACKUP_DIR/$DATE/database.sql

# 備份應用程式
tar -czf $BACKUP_DIR/$DATE/application.tar.gz /opt/tomcat/webapps/ROOT

# 備份設定檔
cp -r /opt/tomcat/conf $BACKUP_DIR/$DATE/

# 清理舊備份（保留30天）
find $BACKUP_DIR -type d -mtime +30 -exec rm -rf {} \;

# 記錄備份結果
echo "備份完成: $DATE" >> $BACKUP_DIR/backup.log
```

## 結論

本技術實施指南涵蓋了新北市違章建築管理系統的完整技術棧，從開發環境設置到生產環境維護的各個面向。重點包括：

### 核心要點
- ✅ **標準化開發流程**：遵循CodeCharge三層架構
- ✅ **安全性優先**：完整的認證授權與輸入驗證
- ✅ **效能優化策略**：資料庫索引、連線池、快取機制
- ✅ **完善的測試規範**：單元測試、整合測試、效能測試
- ✅ **持續監控維護**：日誌管理、系統監控、定期備份

### 持續改進
- 🎯 逐步導入現代化框架
- 🎯 強化自動化測試覆蓋率
- 🎯 優化系統效能瓶頸
- 🎯 提升開發維護效率

透過遵循本指南的規範與最佳實踐，開發團隊能夠確保系統的穩定性、安全性與可維護性，為市民提供可靠的違建管理服務。