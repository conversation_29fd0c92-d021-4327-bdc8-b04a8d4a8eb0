﻿

/* HTML 統一風格區 */
html,
body{
	height: 100%;
    width: 100%;
}

html,
body,
button, input, optgroup, select, textarea
table {
    font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif !important;
	/*overflow-x: hidden;*/
	/*,div,ul,li,*/
}

input::-webkit-input-placeholder {
    font-size: 14px;
}

text{
	font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif !important;
}

label{
	font-size: 16px;
	font-weight: 500;
	margin-bottom: 0px;
}

.btn{
	font-size: 16px;
}


input[type=number],input[type=text]{
    line-height: 1.15;
}

input[type=range].transparent-range {
    display: inline-block;
    margin-top: 4px;
    width: 100%;
    margin-top: 9px;
}

input[type="range"] {
    -webkit-appearance: none;
    border-radius: 2px;
    width: 200px;
    height: 3px;
    background-image: -webkit-linear-gradient(left, #337ab7 0%, #337ab7 50%, #fff 50%, #fff 100%);
    box-shadow: inset #759dc0 0 0 5px;
    outline: none;
    transition: .1s;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 10px;
    height: 10px;
    background: #337ab7;
    border-radius: 50%;
    transition: .1s;
}

input[type="range"]::-webkit-slider-thumb:hover,
input[type="range"]::-webkit-slider-thumb:active {
    width: 16px;
    height: 16px;
}

::-webkit-scrollbar{
	width: 6px;
	background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb{
	background-color: #b5b5b5;;
}

::-webkit-scrollbar {
    /*display: none;*/
}

.display-none {
    display: none !important;
}

.error {
    color: red;
	font-size:16px;
	display: inherit;
}

/* Material Icons - Vertical Align */
div.mdl-layout__drawer > nav.mdl-navigation > a.mdl-navigation__link {
    display: inline-flex;
    vertical-align: middle;
}

/* 特殊功能區 */
@page { size: A3 landscape; }

/* ID 指定區 */

#mapDiv{
	height: 100%;
    width: 100%;
	z-index: 50;
}

#searchDiv{
	z-index: 51;
}

.map .container{
	overflow: initial !important;
	max-width: 2000px !important;
}

#ul_mapServerlistGroup{
	padding-left: 0px;
}

#resultInfo {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
	font-size: 14px;
}

#resultInfo td,
#customers th {
    border: 1px solid #ddd;
    padding: 4px 6px 4px 6px
}


#resultInfo th {
    padding-top: 5px;
    padding-bottom: 5px;
    text-align: left;
    background-color: #397bffa8;
    color: white;
    font-size: 14px;
    font-weight: 400;
	border: 1px solid #397bffa8;
}

#resultInfo td {
    background-color: #397bff05;
}

#layerInfoWindow {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

#layerInfoWindow td,
#customers th {
    border: 1px solid #ddd;
    padding: 7px;
}


#layerInfoWindow th {
    padding-top: 5px;
    padding-bottom: 5px;
    text-align: left;
    background-color: #19b9b2;
    color: white;
    font-size: 13px;
    font-weight: 400;
	border: 1px solid #19B9B2;
}

#layerInfoWindow td {
    background-color: #2eb9b208;
	width: 50px;
}

#swipeDiv div {
	left: 50%;
}

/* Class 自訂區 */

.padding-0{
	padding:0px;
}

.navBarCustom{
	background-color: #3f62f3 !important;
    border-bottom: 1px solid #3f62f3 !important;
	display: none;
}

.navSlideDiv {
	position: absolute;
    left: 0px;
    width: 408px;
	height:100%;
	z-index: 50;
	background-color: white;
	box-shadow: 2px 2px 2px rgba(100, 100, 100, 0.6), 4px 4px 6px rgba(100, 100, 100, 0.4), 6px 6px 12px rgba(100, 100, 100, 0.4);
	/*overflow-x: auto;*/
}

.moveAnimation,
#mapDiv_zoom_slider
{
	transition-duration: 0.7s;
}

.navbar{
	background-color: #f4f7f6;
    border-bottom: 1px solid #ddd;
}

.card {
    -moz-box-direction: normal;
    -moz-box-orient: vertical;
    background-color: #fff;
    border-radius: 0.25rem;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom:1px;
    border:none;
}
.card-header:first-child {
    border-radius: 0;
	background-color: #f1f1f1!important;
}
.card-header {
    margin-bottom: 0;
    padding: 14px;
    border:none;
	border-bottom: 1px solid #ddd;
    
}
.card-header a i{
    float:left;
    font-size:25px;
    color:#195C9D;
	padding: 0px;
}
.card-header i{
    float:right;        
    font-size:30px;
}
.card-header a{
    width:97%;
    float:left;
    color:#565656;
}
.card-header p{
    margin:0;
}

.card-header span{
    font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif !important;
    font-size: 16px;
    font-weight: 700;
}
.card-block {
    -moz-box-flex: 1;
    flex: 1 1 auto;
    padding: 20px;
    color:#232323;
	border-bottom: 1px solid #ddd;
    border-radius:0;
}

/*.transparent-range-div {
    float: right;
    display: flex;
    text-align: right;
    width: 140px;
    padding-right: 13px;
}*/

.li-Title-2 {
    font-weight: 700;
    font-size: 16px;
    color: #00698c;
}

.visibile-hidden {
    visibility: hidden;
}

.UG-colorRed {
    color: #fa4f4f;
}

/*.content-3 {
    padding-left: 38px !important; 
}*/

.li-Title-3{
	font-size: 15px;
	display: inline-block;
    text-overflow: ellipsis;
    width: 270px;
    white-space: nowrap;
    overflow: hidden;
}

.fancybox-margin
{
	/*margin-right: 0px !important;*/
}

.container-Lv1{
	padding:20px
}

.container-Lv2{
	padding-left:25px
}

.container-Lv3{
	padding-left:80px
}

.editTableCheckbox{
	margin-left: -18px;
    margin-top: -6px;
}

.layer-list-group{
	padding: 14px 0px;
}

.map-legend-div {
	position: absolute;
    right: 20px;
	bottom:36px;
    width: 116px;
	z-index: 50;
	background-color: white;
	box-shadow: 2px 2px 2px rgba(100, 100, 100, 0.6), 4px 4px 6px rgba(100, 100, 100, 0.4), 6px 6px 12px rgba(100, 100, 100, 0.4);
}

.map-legend-div ul li{
	padding: 10px 8px;
}

.map-legend-marker{
	width:20px;
}

.map-legend-check{
    margin-top: 6px;
}

.nav-link{
	color: rgba(255, 255, 255, 1) !important;
    font-size: 18px;
    padding: 8px 17px !important;
}

.dropdown-toggle::after {
    margin-left: 0px !important;
}

.legendList-icon {
	font-size: 20px; 
	color: #7b7b7b; 
	cursor: pointer; 
	padding-right: 4px;
}

.eye-icon{
	font-size: 20px; 
	color:#7b7b7b; 
	cursor: pointer; 
	padding-right: 4px;
}

.range-icon{
	background-image: -webkit-linear-gradient(left, rgb(51, 122, 183) 0%, rgb(51, 122, 183) 100%, rgb(255, 255, 255) 100%, rgb(255, 255, 255) 100%);
}

.excision-map-1{
	transition-duration:0.7s;
	width:100%; 
	height:100%;
	position:absolute;
	background-color: white;
}

.excision-map-2{
	transition-duration:0.7s;
	width:50%; 
	height:100%;
	position:absolute;
	background-color: white;
}

.excision-map-4{
	background-color:white;
	transition-duration:0.7s;
	width:50%; 
	height:50%;
	position:absolute;
	background-color: white;
}

.mapContainer{
	height:100%; 
	width:100%;
	position:absolute;
	top:0px;
	left:0px;
	z-index: 47;
	background-color:white;
}

.locationFeature{
	font-size: 20px;
	cursor: pointer; 
	padding-right: 4px;
}

.chosen-single{
	font-size:15px;
}

.locationFeature.highlight{
	color: #FF9800;
}
.locationFeature.normal{
	color: #ff980085;
}

.errorRed {
    color: red;
}

.stsPrintBtn {
	width: 82px;
    height: 38px;
    padding: 4px;
}

.esriPrint {
    padding: 0px !important;
	line-height: 28px;
}

.esriPrintout{
	color: #ffffff;
}

.darkMode {
	background-color: #3c3c3c;
}

.btn.btn-warning.dropdown-toggle
{
	width: 249px;
    opacity: 0.8;
    background-color: #FFC107;
    color: #252525;
	height: 30px;
	padding: 0px;
	border-color: #ffcc00;
}

.normalBtn
{
	height: 32px;
    padding-top: 3px;
}

.endCaseTable tr td:first-child {
	padding: 4px 0px 4px 0px;
	text-align: right;
	width: 110px;
}

.endCaseError{
	display: inline-block;
}

.icon-radius-background
{
    height: 23px;
    float: right;
    padding: 3px 3px;
    margin: 0px 5px;
    background-color: #3b78f3;
    border: 1px #3b78f3;
    border-radius: 5px;

}
   
.dijitTooltipContainer
{
    background-color: #ffffff !important;
    border: solid rgb(80, 80, 80) 1px;
}

#searchDiv_Button {
	/*
	position: absolute;
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -30px;
    width: 30px;
    height: 40px;
    background-color: #ffffff;
    z-index: 99;
    cursor: pointer;
	*/
	position: absolute;
    border-bottom-right-radius: 9px;
    border-top-right-radius: 9px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -13px;
    width: 13px;
    height: 140px;
    background-color: #0e0e0e;
    z-index: 0;
    cursor: pointer;
    top: 40%;
    opacity: 0.5;
}

#searchDiv_Button i {
	/*
	color: #4d4b5e;
    margin-top: 7px;
    margin-left: 2px;
	*/
	color: #FFFFFF;
    margin-top: 56px;
    margin-left: -6px;
}

#stsDiv_Button {
	/*
	position: absolute;
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -30px;
    width: 30px;
    height: 40px;
    background-color: #ffffff;
    z-index: 99;
    cursor: pointer;
	*/
	position: absolute;
    border-bottom-right-radius: 9px;
    border-top-right-radius: 9px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -13px;
    width: 13px;
    height: 140px;
    background-color: #0e0e0e;
    z-index: 0;
    cursor: pointer;
    top: 40%;
    opacity: 0.5;
}

#stsDiv_Button i {
	/*
	color: #4d4b5e;
    margin-top: 7px;
    margin-left: 2px;
	*/
	color: #FFFFFF;
    margin-top: 56px;
    margin-left: -6px;
}

#editDiv_Button {
	/*
	position: absolute;
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -30px;
    width: 30px;
    height: 40px;
    background-color: #ffffff;
    z-index: 99;
    cursor: pointer;
	*/
	position: absolute;
    border-bottom-right-radius: 9px;
    border-top-right-radius: 9px;
    box-shadow: 2px 2px 2px rgba(100,100,100,0.6), 4px 4px 6px rgba(100,100,100,0.4), 6px 6px 12px rgba(100,100,100,0.4);
    right: -13px;
    width: 13px;
    height: 140px;
    background-color: #0e0e0e;
    z-index: 0;
    cursor: pointer;
    top: 40%;
    opacity: 0.5;
}

#editDiv_Button i {
	/*
	color: #4d4b5e;
    margin-top: 7px;
    margin-left: 2px;
	*/
	color: #FFFFFF;
    margin-top: 56px;
    margin-left: -6px;
}

.transparent-range-div {
    float: right;
    display: flex;
    text-align: right;
    width: 156px;
    padding-right: 13px;
}

.transparent-range-div.base-map {
	width: 132px;
}

.row.toggle {
    text-align: left;
}

.content-3 {
    text-align: left;
    height: 30px;
    padding-left: 28px;
}

.tabs {
    text-align: left;
	overflow-x: hidden;
}

ul:not(.browser-default) {
  padding-left: 0;
  list-style-type: none;
}

.tabs {
  position: relative;
  overflow-y: hidden;
  height: 48px;
  width: 100%;
  background-color: #fff;
  margin: 0 auto;
  white-space: nowrap;
}

.tabs .tab {
  display: inline-block;
  text-align: center;
  line-height: 48px;
  height: 48px;
  padding: 0;
  margin: 0;
  text-transform: uppercase;
}

.menuTabDiv span {
    vertical-align: middle;
}

.menu-Title {
    font-weight: 500;
    font-size: 18px;
    padding-right: 4px;
}

.tabs .tab a:hover, .tabs .tab a.active {
  background-color: #eefafa;
  color: #003c39;
}

.tabs .tab a {
  color: #003c39b0;
  display: block;
  width: 100%;
  height: 100%;
  padding: 0 24px;
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-transition: color .28s ease, background-color .28s ease;
  transition: color .28s ease, background-color .28s ease;
}

.tabs .indicator {
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: #2fbeb8;
  will-change: left, right;
}

