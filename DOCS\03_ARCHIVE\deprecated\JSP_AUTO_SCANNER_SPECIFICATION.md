# JSP檔案自動掃描腳本設計規格文件

## 文件資訊
- **任務編號**: T1.1.1
- **負責人**: 【A】Claude Code - 全棧開發任務組
- **工時估算**: 4小時
- **建立日期**: 2025-07-05
- **最後更新**: 2025-07-05

## 概述

本文件定義了用於掃描分析新北市違章建築管理系統中484個JSP檔案的自動化腳本規格。該腳本將識別CodeCharge Studio三層架構模式，並建立檔案對應關係。

## 系統背景

### 目標檔案統計
- **JSP檔案總數**: 484個
- **XML配置檔案**: 195個
- **JavaScript檔案**: 233個
- **架構模式**: CodeCharge Studio三檔案分層架構

### CodeCharge Studio三檔案分層架構
```
標準模式:
├── {name}_man.jsp / {name}_lis.jsp  [呈現層] - 使用者介面與HTML
├── {name}_man.xml / {name}_lis.xml  [設定層] - XML格式設定檔
└── {name}_Handlers.jsp              [邏輯層] - Java業務邏輯

自訂API模式:
└── {name}.jsp                       [API端點] - 純後端邏輯，無HTML
```

## 腳本規格設計

### 1. 腳本基本資訊

**腳本名稱**: `jsp-scanner.py`  
**語言**: Python 3.8+  
**依賴**: `os`, `re`, `csv`, `json`, `xml.etree.ElementTree`  
**輸出格式**: CSV + JSON  

### 2. 掃描目標與範圍

#### 2.1 主要掃描對象
```
/mnt/d/apache-tomcat-9.0.98/webapps/src/
├── *.jsp (484個檔案)
├── *.xml (195個檔案) 
└── *.js  (233個檔案)
```

#### 2.2 檔案分類規則
1. **業務模組檔案**: `im[0-9]{5}_*.jsp` (如: im10101_man.jsp)
2. **查詢模組檔案**: `is[0-9]{5}_*.jsp` (如: is10101_lis.jsp)  
3. **內部模組檔案**: `in[0-9]{5}_*.jsp` (如: in10101_man.jsp)
4. **公用檔案**: `*.jsp` (不符合模組命名規則)
5. **API端點**: 純邏輯檔案，如 `case_empty_dis.jsp`

#### 2.3 檔案角色識別
- **_man.jsp**: 管理/編輯頁面 (Management)
- **_lis.jsp**: 列表/查詢頁面 (List) 
- **_prt.jsp**: 列印/報表頁面 (Print)
- **_upload.jsp**: 檔案上傳頁面
- **Handlers.jsp**: 業務邏輯處理器
- **其他**: 特殊功能頁面

### 3. 掃描功能需求

#### 3.1 基本檔案資訊收集
```python
file_info = {
    'filename': str,           # 檔案名稱
    'filepath': str,          # 完整路徑  
    'filesize': int,          # 檔案大小(bytes)
    'created_date': str,      # 建立時間
    'modified_date': str,     # 修改時間
    'line_count': int,        # 總行數
    'file_type': str          # 檔案類型 (jsp|xml|js)
}
```

#### 3.2 JSP檔案內容分析
```python
jsp_analysis = {
    'module_type': str,       # 模組類型 (im|is|in|other)
    'module_number': str,     # 模組編號 (如: 10101)
    'page_role': str,         # 頁面角色 (man|lis|prt|upload|handlers|api)
    'has_codecharge': bool,   # 是否為CodeCharge生成
    'includes_handlers': bool, # 是否包含Handlers
    'database_tables': list,  # 資料表名稱
    'main_bean_name': str,    # 主要Bean名稱
    'import_count': int,      # Import語句數量
    'scriptlet_count': int,   # Java Scriptlet數量
    'tag_libraries': list,    # 使用的標籤庫
    'external_scripts': list, # 外部JS檔案
    'form_count': int,        # 表單數量
    'button_count': int       # 按鈕數量
}
```

#### 3.3 XML配置檔案分析
```python
xml_analysis = {
    'page_name': str,         # 頁面名稱
    'connection_name': str,   # 資料庫連線名稱
    'record_count': int,      # Record元件數量
    'grid_count': int,        # Grid元件數量
    'button_count': int,      # 按鈕數量
    'textbox_count': int,     # TextBox數量
    'dropdown_count': int,    # DropDown數量
    'data_sources': list,     # 資料來源清單
    'validation_rules': int,  # 驗證規則數量
    'event_handlers': list    # 事件處理器清單
}
```

#### 3.4 三檔案關係對應
```python
file_relationship = {
    'base_name': str,         # 基礎名稱 (如: im10101_man)
    'jsp_file': str,          # JSP檔案路徑
    'xml_file': str,          # XML檔案路徑 (若存在)
    'handlers_file': str,     # Handlers檔案路徑 (若存在)
    'related_js': list,       # 相關JS檔案
    'is_complete_set': bool,  # 是否為完整三檔案組合
    'missing_files': list     # 缺失的檔案類型
}
```

### 4. 輸出檔案規格

#### 4.1 CSV報表 (`jsp_scan_summary.csv`)
```csv
檔案名稱,模組類型,模組編號,頁面角色,檔案大小,行數,修改時間,是否CodeCharge,關聯檔案數
im10101_man.jsp,im,10101,man,15420,423,2024-03-15,True,3
im10101_lis.jsp,im,10101,lis,12850,356,2024-03-15,True,3
```

#### 4.2 詳細JSON報表 (`jsp_scan_detail.json`)
```json
{
  "scan_summary": {
    "total_files": 484,
    "scan_date": "2025-07-05",
    "complete_sets": 156,
    "orphan_files": 12,
    "api_endpoints": 8
  },
  "file_analysis": [
    {
      "filename": "im10101_man.jsp",
      "basic_info": { ... },
      "jsp_analysis": { ... },
      "relationships": { ... }
    }
  ],
  "module_statistics": {
    "im_modules": 245,
    "is_modules": 89,
    "in_modules": 127,
    "other_files": 23
  }
}
```

#### 4.3 關係對應表 (`jsp_xml_handlers_mapping.csv`)
```csv
基礎名稱,JSP檔案,XML檔案,Handlers檔案,完整度,缺失檔案
im10101_man,✓,✓,✓,100%,無
im10101_lis,✓,✓,✓,100%,無
im20301_upload,✓,✓,✓,100%,無
```

### 5. 特殊處理規則

#### 5.1 檔案命名異常處理
- 檔案名包含特殊字符的處理
- 版本號碼檔案的識別 (如: `_2.jsp`, `_copy.jsp`)
- 備份檔案的過濾 (如: `*.bak`, `*_backup.*`)

#### 5.2 內容分析異常處理
- 空檔案的處理
- 編碼異常檔案的處理
- 損壞的XML檔案處理
- 超大檔案的分段讀取

#### 5.3 模組編號解析規則
```python
# 標準模組編號格式
pattern_standard = r'^(im|is|in)(\d{5})_(.+)\.jsp$'

# 特殊格式處理
special_cases = {
    'login.jsp': 'system',
    'main.jsp': 'system', 
    'timeout_err.jsp': 'system',
    'case_empty_dis.jsp': 'api'
}
```

### 6. 效能最佳化

#### 6.1 批次處理策略
- 每次處理50個檔案為一批次
- 使用多執行緒平行處理
- 記憶體使用監控與釋放

#### 6.2 快取機制
- 檔案修改時間快取
- 已分析檔案的結果快取
- 增量掃描模式支援

### 7. 錯誤處理與日誌

#### 7.1 錯誤分類
```python
error_types = {
    'FILE_NOT_FOUND': '檔案不存在',
    'PERMISSION_DENIED': '權限不足', 
    'ENCODING_ERROR': '編碼錯誤',
    'XML_PARSE_ERROR': 'XML解析錯誤',
    'CONTENT_ANALYSIS_ERROR': '內容分析錯誤'
}
```

#### 7.2 日誌格式
```
[2025-07-05 14:30:15] INFO: 開始掃描JSP檔案
[2025-07-05 14:30:16] DEBUG: 處理檔案 im10101_man.jsp
[2025-07-05 14:30:16] WARNING: 檔案 im20301_man.jsp 缺少對應XML
[2025-07-05 14:30:17] ERROR: 無法解析 im30101_man.xml (格式錯誤)
[2025-07-05 14:32:45] INFO: 掃描完成，共處理484個檔案
```

### 8. 執行環境需求

#### 8.1 系統需求
- **Python版本**: 3.8+
- **記憶體需求**: 最少512MB可用記憶體
- **磁碟空間**: 100MB暫存空間
- **處理時間**: 預估15-20分鐘

#### 8.2 相依套件
```bash
pip install chardet  # 字元編碼偵測
pip install lxml     # XML解析加速
pip install pandas   # 資料處理(可選)
```

## 產出交付清單

### 主要產出
1. **腳本檔案**: `tools/jsp-scanner.py`
2. **配置檔案**: `tools/scanner-config.json` 
3. **執行腳本**: `tools/run-scanner.sh`

### 分析報表
1. **摘要報表**: `jsp_scan_summary.csv`
2. **詳細報表**: `jsp_scan_detail.json`
3. **對應表**: `jsp_xml_handlers_mapping.csv`
4. **統計圖表**: `jsp_statistics.html`

### 文件
1. **使用說明**: `tools/README.md`
2. **故障排除**: `tools/TROUBLESHOOTING.md`

## 後續應用

### 與其他任務的關聯
- **T1.1.2**: 提供核心業務檔案清單
- **T1.1.3**: 建立JSP-XML-Handler對應關係
- **T1.1.4**: 支援功能分類索引建立

### 維護與更新
- 腳本版本控制策略
- 定期掃描排程設置
- 新增檔案自動偵測機制

---

**注意事項**:
- 本規格為文件撰寫階段產出，不涉及實際程式碼修改
- 所有掃描操作均為唯讀，不對原始檔案進行任何修改
- 腳本執行前需確保有足夠的檔案讀取權限

**核准**: 待【A】Claude Code實例完成實作與測試