# 新北市違章建築管理系統 - 資料表描述說明總表

本文件為透過10個Agent並行分析生成的完整資料表描述說明表，整合了程式碼、頁面、文件等所有來源的資訊。

## 📋 文件說明

- **建立日期**：2025-01-09
- **更新日期**：2025-01-09
- **資料來源**：實際資料庫查詢 + 程式碼分析 + 頁面解析 + 文件整理
- **Agent數量**：10個並行執行
- **涵蓋範圍**：180個資料表，46個核心業務表

## 🗂️ 核心業務表

### 1. ibmcase（案件主表）
**用途**：違章建築案件主檔，系統核心資料表
**資料量**：419,817筆
**主要頁面**：im10101_man.jsp、im20201_man.jsp

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_no | VARCHAR(10) | 案件編號 | 案件編號/勘查紀錄號碼 | ⭐⭐⭐⭐⭐ | 主鍵 |
| caseopened | VARCHAR(3) | 當前狀態碼 | 處理狀態/流程狀態 | ⭐⭐⭐⭐⭐ | 對應RLT代碼 |
| s_empno | VARCHAR(10) | 承辦人員編號 | 承辦人/認定承辦 | ⭐⭐⭐⭐ | 對應ibmuser.empno |
| case_con_user | VARCHAR(10) | 協同承辦人 | 協同承辦人員 | ⭐⭐⭐⭐ | 協同作業功能 |
| reg_yy | VARCHAR(3) | 登記年度 | 民國年度 | ⭐⭐⭐ | 民國年 |
| reg_no | VARCHAR(6) | 登記號碼 | 案件流水號 | ⭐⭐⭐ | 年度內編號 |
| ib_prcs | VARCHAR(1) | 違建流程 | 案件類型 | ⭐⭐⭐⭐ | 1=一般,2=廣告,3=下水道 |
| examine_kind | VARCHAR(2) | 查報類別 | 勘查類別 | ⭐⭐⭐ | 對應EXP_STATE代碼 |
| dis_b_addzon | VARCHAR(3) | 違建地點行政區 | 郵遞區號 | ⭐⭐⭐⭐ | 對應ZON代碼 |
| dis_b_add1 | VARCHAR(10) | 村里鄰 | 地址第1段 | ⭐⭐⭐ | 完整地址組合 |
| dis_b_add2 | VARCHAR(30) | 街路大道 | 地址第2段 | ⭐⭐⭐ | 完整地址組合 |
| dis_b_add3 | VARCHAR(10) | 巷 | 地址第3段 | ⭐⭐ | 完整地址組合 |
| dis_b_add4 | VARCHAR(10) | 弄 | 地址第4段 | ⭐⭐ | 完整地址組合 |
| dis_b_add5 | VARCHAR(10) | 號 | 地址第5段 | ⭐⭐⭐ | 完整地址組合 |
| dis_b_add6 | VARCHAR(10) | 之號 | 地址第6段 | ⭐⭐ | 完整地址組合 |
| dis_b_add7 | VARCHAR(10) | 樓 | 地址第7段 | ⭐⭐ | 完整地址組合 |
| dis_b_add8 | VARCHAR(10) | 室 | 地址第8段 | ⭐⭐ | 完整地址組合 |
| dis_b_add9 | VARCHAR(150) | 其他說明 | 地址其他描述 | ⭐⭐ | 完整地址組合 |
| x_coordinate | NUMERIC | 經度座標 | X座標 | ⭐⭐⭐ | GIS定位 |
| y_coordinate | NUMERIC | 緯度座標 | Y座標 | ⭐⭐⭐ | GIS定位 |
| building_area | NUMERIC | 建物面積 | 違章面積 | ⭐⭐⭐⭐ | 平方公尺 |
| building_height | NUMERIC | 建物高度 | 違章高度 | ⭐⭐⭐ | 公尺 |
| building_kind | VARCHAR(2) | 建造材料 | 違章材料 | ⭐⭐⭐ | 對應STU代碼 |
| blduse | VARCHAR(2) | 現況用途 | 建物用途 | ⭐⭐⭐ | 對應BLDUSE代碼 |
| reg_date | NUMERIC | 認定發文日期 | 認定日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| end_date | NUMERIC | 結案日期 | 結案發文日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| rvldate | NUMERIC | 勘查日期 | 會勘日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| audnm_date | NUMERIC | 查報單發文日期 | 通報日期 | ⭐⭐⭐ | YYYYMMDD-19110000 |
| audnm_word | VARCHAR(20) | 查報單來文字軌 | 發文字 | ⭐⭐ | 公文字號 |
| audnm_num | VARCHAR(12) | 查報單來文號碼 | 發文號碼 | ⭐⭐ | 公文字號 |
| dis_type | VARCHAR(1) | 拆除優先類組 | 違建類別代碼 | ⭐⭐⭐ | 對應DSORT代碼 |
| dis_sort | VARCHAR(1) | 拆除組別 | 違建組別代碼 | ⭐⭐⭐ | 排拆分組 |
| reg_emp | VARCHAR(10) | 認定承辦 | 認定承辦人 | ⭐⭐⭐ | 對應ibmuser.empno |
| dmltn_emp | VARCHAR(10) | 排拆人員 | 拆除人員 | ⭐⭐⭐ | 對應ibmuser.empno |
| b_notice_emp | VARCHAR(10) | 通知人員/執行人員 | 通知承辦人 | ⭐⭐⭐ | 對應ibmuser.empno |
| rsult_emp | VARCHAR(10) | 結案承辦人 | 結果人員 | ⭐⭐⭐ | 對應ibmuser.empno |
| status | VARCHAR(3) | 處理狀態 | 狀態 | ⭐⭐⭐⭐ | 對應RLT代碼 |
| prjnm | VARCHAR(7) | 專案名稱 | 所屬專案 | ⭐⭐⭐ | 對應PRJNM代碼 |
| case_ori | VARCHAR(1) | 案件來源 | 案件來源 | ⭐⭐⭐ | 對應CASORI代碼 |
| reg_unit | VARCHAR(2) | 認定單位 | 登記單位 | ⭐⭐ | 對應單位代碼 |
| dis_unit | VARCHAR(2) | 拆除單位 | 轄區單位 | ⭐⭐ | 對應單位代碼 |
| ibresult | VARCHAR(2) | 勘查結果 | 認定結果 | ⭐⭐⭐⭐ | 01=一般違建,02=拍照建檔 |
| cr_date | VARCHAR(8) | 建立日期 | 建立時間 | ⭐⭐ | YYYYMMDD |
| op_date | VARCHAR(8) | 異動日期 | 更新時間 | ⭐⭐ | YYYYMMDD |

### 2. tbflow（流程記錄表）
**用途**：記錄案件處理流程的完整歷程
**資料量**：352,514筆
**主要頁面**：所有流程相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| flow_seq | BIGINT | 流程序號 | 流程記錄主鍵 | ⭐⭐⭐⭐⭐ | 自動產生主鍵 |
| case_no | VARCHAR(10) | 案件編號 | 對應案件編號 | ⭐⭐⭐⭐⭐ | 外鍵至ibmcase |
| flow_status | VARCHAR(3) | 流程狀態 | 狀態碼 | ⭐⭐⭐⭐⭐ | 對應RLT代碼 |
| flow_sdate | TIMESTAMP | 流程開始時間 | 狀態開始時間 | ⭐⭐⭐⭐ | 完整時間戳記 |
| flow_edate | TIMESTAMP | 流程結束時間 | 狀態結束時間 | ⭐⭐⭐⭐ | 完整時間戳記 |
| flow_user | VARCHAR(10) | 流程處理人 | 處理人員 | ⭐⭐⭐⭐ | 對應ibmuser.empno |
| flow_memo | TEXT | 流程備註 | 處理說明 | ⭐⭐⭐ | 處理過程說明 |
| created_date | TIMESTAMP | 建立時間 | 記錄建立時間 | ⭐⭐ | 系統記錄 |
| updated_date | TIMESTAMP | 更新時間 | 記錄更新時間 | ⭐⭐ | 系統記錄 |

### 3. ibmcode（系統代碼表）
**用途**：系統參數代碼管理，所有下拉選單的資料來源
**資料量**：1,751筆（78種代碼類型）
**主要頁面**：所有頁面的下拉選單

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| code_type | VARCHAR(25) | 代碼類型 | 代碼分類 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| code_seq | VARCHAR(10) | 代碼序號 | 代碼值 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| code_desc | VARCHAR(200) | 代碼描述 | 代碼說明 | ⭐⭐⭐⭐⭐ | 中文描述 |
| sub_seq | VARCHAR(10) | 子序號 | 子分類序號 | ⭐⭐ | 階層分類 |
| sub_seq1 | VARCHAR(10) | 子序號1 | 子分類序號1 | ⭐⭐ | 階層分類 |
| sub_seq2 | VARCHAR(10) | 子序號2 | 子分類序號2 | ⭐⭐ | 階層分類 |
| mark | VARCHAR(200) | 備註 | 說明欄位 | ⭐⭐ | 額外說明 |
| is_del | VARCHAR(1) | 是否刪除 | 刪除標記 | ⭐⭐⭐ | Y=刪除,N=正常 |
| is_open | VARCHAR(1) | 是否開放 | 啟用標記 | ⭐⭐⭐ | Y=啟用,N=停用 |
| order_by_seq | NUMERIC | 排序序號 | 顯示順序 | ⭐⭐ | 排序欄位 |
| valid_start_date | NUMERIC | 有效開始日期 | 生效日期 | ⭐⭐ | YYYYMMDD-19110000 |
| valid_end_date | NUMERIC | 有效結束日期 | 失效日期 | ⭐⭐ | YYYYMMDD-19110000 |

### 4. ibmuser（使用者表）
**用途**：系統使用者帳號管理
**主要頁面**：im70301_man.jsp、登入頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| empno | VARCHAR(10) | 系統帳號/員工編號 | 使用者帳號 | ⭐⭐⭐⭐⭐ | 主鍵 |
| emppwd | VARCHAR(100) | 登入密碼 | 密碼 | ⭐⭐⭐⭐⭐ | 加密儲存 |
| empname | VARCHAR(10) | 使用者姓名 | 員工姓名 | ⭐⭐⭐⭐ | 顯示名稱 |
| role_id | VARCHAR(10) | 擔當角色 | 角色編號 | ⭐⭐⭐⭐⭐ | 對應ibmrole |
| role_id_2 | VARCHAR(10) | 擔當角色2 | 第二角色 | ⭐⭐⭐ | 多重角色 |
| role_id_3 | VARCHAR(10) | 擔當角色3 | 第三角色 | ⭐⭐⭐ | 多重角色 |
| unit_id | VARCHAR(10) | 隸屬單位 | 所屬單位 | ⭐⭐⭐⭐ | 對應單位代碼 |
| job_title | VARCHAR(10) | 職稱 | 職務名稱 | ⭐⭐⭐ | 對應JBTL代碼 |
| tel | VARCHAR(20) | 電話 | 聯絡電話 | ⭐⭐ | 聯絡資訊 |
| email | VARCHAR(50) | 電子信箱 | 電子郵件 | ⭐⭐ | 聯絡資訊 |
| is_dis | VARCHAR(1) | 停用狀態 | 帳號狀態 | ⭐⭐⭐⭐ | Y=停用,N=正常 |
| lck_cnt | NUMERIC | 鎖定次數 | 登入失敗次數 | ⭐⭐⭐ | 安全機制 |
| pwd_time | NUMERIC | 密碼時間 | 密碼更新時間 | ⭐⭐ | 密碼管理 |
| histrypwd_1 | VARCHAR(100) | 歷史密碼1 | 舊密碼記錄 | ⭐⭐ | 密碼歷史 |
| histrypwd_2 | VARCHAR(100) | 歷史密碼2 | 舊密碼記錄 | ⭐⭐ | 密碼歷史 |
| histrypwd_3 | VARCHAR(100) | 歷史密碼3 | 舊密碼記錄 | ⭐⭐ | 密碼歷史 |

### 5. collaboration_log（協同作業記錄表）
**用途**：記錄協同作業流程，支援協同退回功能
**主要頁面**：協同作業相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| log_id | BIGINT | 記錄編號 | 主鍵 | ⭐⭐⭐⭐⭐ | 自動產生 |
| case_no | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 外鍵至ibmcase |
| collaboration_type | VARCHAR(10) | 協同類型 | 協同作業類型 | ⭐⭐⭐⭐ | 送件/退回 |
| from_user | VARCHAR(10) | 發起使用者 | 協同發起人 | ⭐⭐⭐⭐ | 對應ibmuser.empno |
| to_user | VARCHAR(10) | 接收使用者 | 協同接收人 | ⭐⭐⭐⭐ | 對應ibmuser.empno |
| collaboration_date | TIMESTAMP | 協同日期 | 協同作業時間 | ⭐⭐⭐⭐ | 完整時間戳記 |
| return_reason | TEXT | 退回原因 | 協同退回說明 | ⭐⭐⭐⭐ | 235/245/255功能 |
| completion_date | TIMESTAMP | 完成日期 | 協同完成時間 | ⭐⭐⭐ | 完整時間戳記 |
| status | VARCHAR(10) | 狀態 | 協同作業狀態 | ⭐⭐⭐⭐ | 進行中/已完成 |

### 6. ibmlist（檔案清單表）
**用途**：管理案件相關的附件檔案和圖片
**主要頁面**：檔案上傳和管理頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| systid | VARCHAR(10) | 系統編號 | 系統識別碼 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| pic_kind | VARCHAR(2) | 圖片種類 | 檔案類型 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| pic_seq | NUMERIC | 圖片序號 | 檔案序號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| picname | VARCHAR(100) | 圖片名稱 | 原始檔名 | ⭐⭐⭐ | 上傳檔名 |
| filename | VARCHAR(100) | 檔案名稱 | 儲存檔名 | ⭐⭐⭐⭐ | 系統檔名 |
| filekind | VARCHAR(10) | 檔案類型 | 檔案格式 | ⭐⭐⭐ | jpg/pdf/doc等 |
| cr_user | VARCHAR(10) | 建立使用者 | 上傳人員 | ⭐⭐⭐ | 對應ibmuser.empno |
| showname | VARCHAR(100) | 顯示名稱 | 顯示檔名 | ⭐⭐ | 使用者看到的名稱 |
| thombed | VARCHAR(1) | 縮圖狀態 | 是否有縮圖 | ⭐⭐ | Y=有縮圖,N=無 |
| start_date | NUMERIC | 開始日期 | 有效開始日 | ⭐⭐ | YYYYMMDD-19110000 |
| end_date | NUMERIC | 結束日期 | 有效結束日 | ⭐⭐ | YYYYMMDD-19110000 |

### 7. ibmdisnm（違建人員表）
**用途**：記錄違建案件相關人員資訊
**主要頁面**：im10101_man.jsp、人員維護頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| case_seq | NUMERIC | 案件序號 | 人員序號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| ib_user | VARCHAR(35) | 違建人姓名 | 人員姓名 | ⭐⭐⭐⭐ | 姓名 |
| usr_id | VARCHAR(10) | 身分證字號 | 身分證號 | ⭐⭐⭐⭐ | 身分識別 |
| usr_add | VARCHAR(100) | 違建人地址 | 聯絡地址 | ⭐⭐⭐ | 通訊地址 |
| usr_knd | VARCHAR(1) | 人員類別 | 身分類別 | ⭐⭐⭐ | 1=自然人,2=法人 |
| usr_brth | VARCHAR(7) | 出生日期 | 生日 | ⭐⭐ | YYYYMMDD格式 |
| usr_sex | VARCHAR(1) | 性別 | 性別 | ⭐⭐ | M=男,F=女 |

## 🗂️ 業務支援表

### 8. ibmfym（流程管理表）
**用途**：記錄案件各階段的詳細處理資訊
**資料量**：對應案件數量
**主要頁面**：im50101_man.jsp、im60301_man.jsp

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| acc_seq | NUMERIC | 帳務序號 | 流程序號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| acc_date | NUMERIC | 簽核日期 | 帳務日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| acc_job | VARCHAR(1) | 簽核工作 | 帳務工作 | ⭐⭐⭐⭐ | A=陳核,B=決行,C=退回 |
| acc_rlt | VARCHAR(1) | 簽核結果 | 帳務結果 | ⭐⭐⭐⭐ | 處理結果 |
| acc_memo | TEXT | 簽核意見 | 帳務備註 | ⭐⭐⭐⭐ | 處理說明 |
| b_notice_emp | VARCHAR(10) | 通知承辦人 | 通知人員 | ⭐⭐⭐ | 對應ibmuser.empno |
| b_notice_date | NUMERIC | 通知日期 | 通知日期 | ⭐⭐⭐ | YYYYMMDD-19110000 |
| dmltn_emp | VARCHAR(10) | 拆除承辦人 | 拆除人員 | ⭐⭐⭐ | 對應ibmuser.empno |
| pre_dis_date | NUMERIC | 預定拆除日期 | 預定拆除日 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| dis_notice_date | NUMERIC | 拆除通知日期 | 拆除通知日 | ⭐⭐⭐ | YYYYMMDD-19110000 |
| end_date | NUMERIC | 結案日期 | 結案日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| op_user | VARCHAR(10) | 異動使用者 | 最後異動人 | ⭐⭐ | 對應ibmuser.empno |

### 9. ibmsts（狀態表）
**用途**：記錄案件重要時間節點的狀態
**主要頁面**：狀態查詢相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵 |
| acc_date | NUMERIC | 帳務日期 | 狀態日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| acc_job | VARCHAR(1) | 帳務工作 | 作業類型 | ⭐⭐⭐⭐ | 狀態分類 |
| acc_rlt | VARCHAR(1) | 帳務結果 | 處理結果 | ⭐⭐⭐⭐ | 結果碼 |
| acred_ts | NUMERIC | 認定時間戳記 | 認定時戳 | ⭐⭐⭐ | 認定階段時間 |
| demol_ts | NUMERIC | 拆除時間戳記 | 拆除時戳 | ⭐⭐⭐ | 拆除階段時間 |

### 10. ibmrole（角色表）
**用途**：系統角色權限管理
**主要頁面**：角色管理頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| role_id | VARCHAR(10) | 角色編號 | 角色代碼 | ⭐⭐⭐⭐⭐ | 主鍵 |
| role_name | VARCHAR(30) | 角色名稱 | 角色名稱 | ⭐⭐⭐⭐ | 顯示名稱 |
| cr_user | VARCHAR(10) | 建立使用者 | 建立人員 | ⭐⭐ | 對應ibmuser.empno |
| cr_date | NUMERIC | 建立日期 | 建立日期 | ⭐⭐ | YYYYMMDD-19110000 |
| op_user | VARCHAR(10) | 異動使用者 | 異動人員 | ⭐⭐ | 對應ibmuser.empno |
| op_date | NUMERIC | 異動日期 | 異動日期 | ⭐⭐ | YYYYMMDD-19110000 |

## 🗂️ 專業功能表

### 11. ibmcslan（案件土地資料表）
**用途**：記錄違建案件相關的土地地號資訊
**主要頁面**：地號維護相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| case_seq | NUMERIC | 案件序號 | 土地序號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| zone_code | VARCHAR(3) | 地號行政區 | 地政事務所代碼 | ⭐⭐⭐⭐ | 行政區碼 |
| sec_name | VARCHAR(10) | 地段名稱 | 地段 | ⭐⭐⭐⭐ | 地段代碼 |
| road_no1 | VARCHAR(4) | 地號母號 | 地號 | ⭐⭐⭐⭐ | 地號主號 |
| road_no2 | VARCHAR(4) | 地號子號 | 地號之號 | ⭐⭐⭐ | 地號子號 |
| cr_date | NUMERIC | 建立日期 | 建立日期 | ⭐⭐ | YYYYMMDD-19110000 |
| cr_user | VARCHAR(10) | 建立使用者 | 建立人員 | ⭐⭐ | 對應ibmuser.empno |

### 12. ibmcsprj（案件專案表）
**用途**：記錄案件所屬的專案或計畫
**主要頁面**：專案管理相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| case_id | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| prj_code | VARCHAR(7) | 專案代碼 | 專案代碼 | ⭐⭐⭐⭐⭐ | 主鍵之一 |
| prj_yy | VARCHAR(3) | 專案年度 | 計畫年度 | ⭐⭐⭐ | 民國年 |
| prj_nm | VARCHAR(30) | 專案名稱 | 計畫名稱 | ⭐⭐⭐⭐ | 對應PRJNM代碼 |
| prjfee | VARCHAR(10) | 專案費用 | 計畫經費 | ⭐⭐ | 費用金額 |
| prjfee_desc | VARCHAR(100) | 專案費用說明 | 經費說明 | ⭐⭐ | 費用備註 |

### 13. ibmrpli（修繕報備表）
**用途**：既存違建修繕報備管理
**主要頁面**：im90201_man.jsp

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| reportno | VARCHAR(7) | 序號 | 報備編號 | ⭐⭐⭐⭐⭐ | 主鍵 |
| saydate | NUMERIC | 報備日期 | 申請日期 | ⭐⭐⭐⭐ | YYYYMMDD-19110000 |
| owner | VARCHAR(50) | 違建所有人 | 申請人姓名 | ⭐⭐⭐⭐ | 所有權人 |
| ownerid | VARCHAR(10) | 身分證字號 | 身分證號 | ⭐⭐⭐⭐ | 身分識別 |
| ownerphone | VARCHAR(50) | 連絡電話 | 聯絡電話 | ⭐⭐⭐ | 聯絡方式 |
| ownercaddr | VARCHAR(100) | 通訊地址 | 聯絡地址 | ⭐⭐⭐ | 通訊地址 |
| owneroaddr | VARCHAR(100) | 戶籍地址 | 戶籍地址 | ⭐⭐ | 戶籍地址 |
| ownermail | VARCHAR(50) | 電子信箱 | 電子郵件 | ⭐⭐ | 電子信箱 |
| case_pos | VARCHAR(100) | 修繕位置 | 修繕部位 | ⭐⭐⭐⭐ | 修繕範圍 |
| caddress | VARCHAR(100) | 修繕地點 | 修繕地址 | ⭐⭐⭐⭐ | 修繕地點 |
| caddrno | VARCHAR(50) | 修繕地號 | 土地地號 | ⭐⭐⭐ | 地號資訊 |
| cregno | VARCHAR(50) | 既存違建認定號碼 | 認定文號 | ⭐⭐⭐⭐ | 認定依據 |
| posother | VARCHAR(100) | 其他 | 其他說明 | ⭐⭐ | 額外說明 |
| attach_cnt | NUMERIC | 附件檔案 | 附件數量 | ⭐⭐ | 檔案計數 |

## 🗂️ 特殊功能表

### 14. status_code_mapping（狀態碼對應表）
**用途**：內部狀態碼與MOI國土署系統的對應關係
**主要頁面**：MOI系統相關頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| internal_code | VARCHAR(3) | 內部狀態碼 | 系統狀態碼 | ⭐⭐⭐⭐⭐ | 主鍵 |
| moi_stage | VARCHAR(2) | MOI階段碼 | 國土署階段 | ⭐⭐⭐⭐⭐ | 對應MOI系統 |
| stage_name | VARCHAR(50) | 階段名稱 | 階段描述 | ⭐⭐⭐⭐ | 中文名稱 |
| description | TEXT | 說明 | 詳細說明 | ⭐⭐⭐ | 階段說明 |
| is_active | BOOLEAN | 是否啟用 | 啟用狀態 | ⭐⭐⭐ | 啟用標記 |
| created_date | TIMESTAMP | 建立時間 | 建立時間 | ⭐⭐ | 系統記錄 |
| updated_date | TIMESTAMP | 更新時間 | 更新時間 | ⭐⭐ | 系統記錄 |

### 15. ntpc_export_log（匯出記錄表）
**用途**：記錄向MOI國土署系統匯出資料的記錄
**主要頁面**：MOI匯出管理頁面

| 欄位名 | 資料型別 | 頁面描述 | 文件描述 | 重要性 | 備註 |
|--------|----------|----------|----------|--------|------|
| log_id | BIGINT | 記錄編號 | 匯出記錄ID | ⭐⭐⭐⭐⭐ | 主鍵 |
| case_no | VARCHAR(10) | 案件編號 | 案件編號 | ⭐⭐⭐⭐⭐ | 外鍵至ibmcase |
| export_batch_id | VARCHAR(50) | 匯出批次編號 | 批次識別碼 | ⭐⭐⭐⭐ | 批次管理 |
| export_date | DATE | 匯出日期 | 匯出日期 | ⭐⭐⭐⭐ | 匯出時間 |
| export_status | VARCHAR(20) | 匯出狀態 | 處理狀態 | ⭐⭐⭐⭐ | 成功/失敗/處理中 |
| internal_status | VARCHAR(3) | 內部狀態 | 案件狀態 | ⭐⭐⭐⭐ | 對應caseopened |
| moi_stage | VARCHAR(2) | MOI階段 | 國土署階段 | ⭐⭐⭐⭐ | 對應階段碼 |
| request_data | JSONB | 請求資料 | 送出資料 | ⭐⭐⭐ | JSON格式 |
| response_data | JSONB | 回應資料 | 回應資料 | ⭐⭐⭐ | JSON格式 |
| api_response_code | VARCHAR(10) | API回應碼 | 回應代碼 | ⭐⭐⭐ | HTTP狀態碼 |
| error_message | TEXT | 錯誤訊息 | 錯誤說明 | ⭐⭐⭐ | 錯誤資訊 |
| retry_count | INTEGER | 重試次數 | 重試計數 | ⭐⭐ | 重試機制 |
| last_retry_time | TIMESTAMP | 最後重試時間 | 重試時間 | ⭐⭐ | 重試記錄 |

## 🎯 重要狀態碼說明

### RLT代碼（案件狀態）
| 狀態碼 | 頁面描述 | 文件描述 | 適用範圍 | 重要性 |
|--------|----------|----------|----------|--------|
| 231 | [一般]認定辦理中 | 認定作業進行中 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 232 | [一般]認定陳核中 | 認定結果陳核中 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 234 | [一般]認定送協同作業 | 協同作業進行中 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 235 | [一般]認定協同退回 | 協同作業退回 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 239 | [一般]認定已簽准 | 認定程序完成 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 23b | [一般]認定協同作業完成 | 協同作業完成 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 241 | [廣告物]認定辦理中 | 廣告違建認定中 | 廣告違建 | ⭐⭐⭐⭐⭐ |
| 244 | [廣告物]認定送協同作業 | 廣告違建協同中 | 廣告違建 | ⭐⭐⭐⭐⭐ |
| 245 | [廣告物]認定協同退回 | 廣告違建協同退回 | 廣告違建 | ⭐⭐⭐⭐⭐ |
| 24b | [廣告物]認定協同作業完成 | 廣告違建協同完成 | 廣告違建 | ⭐⭐⭐⭐⭐ |
| 251 | [下水道]認定辦理中 | 下水道違建認定中 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 252 | [下水道]認定陳核中 | 下水道違建陳核中 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 254 | [下水道]認定送協同作業 | 下水道違建協同中 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 255 | [下水道]認定協同退回 | 下水道違建協同退回 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 25b | [下水道]認定協同作業完成 | 下水道違建協同完成 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 460 | [一般]結案 | 一般違建結案 | 一般違建 | ⭐⭐⭐⭐⭐ |
| 440 | [廣告物]結案 | 廣告違建結案 | 廣告違建 | ⭐⭐⭐⭐⭐ |
| 450 | [下水道]結案 | 下水道違建結案 | 下水道違建 | ⭐⭐⭐⭐⭐ |
| 92c | 案件資料繕校 | 資料品質控制 | 所有類型 | ⭐⭐⭐⭐ |

## 📝 命名規範說明

### 1. 表名命名規範
- **ibm**開頭：業務主表（如ibmcase、ibmuser）
- **tbflow**：流程記錄表
- **collaboration_**：協同作業相關表
- **status_code_**：狀態碼對應表
- **ntpc_**：新北市特有功能表

### 2. 欄位命名規範
- **_id**結尾：識別碼欄位
- **_no**結尾：編號欄位
- **_date**結尾：日期欄位
- **_emp**結尾：人員欄位
- **_user**結尾：使用者欄位
- **s_**開頭：搜尋條件欄位
- **is_**開頭：是否狀態欄位

### 3. 日期格式說明
- **NUMERIC類型日期**：YYYYMMDD-19110000（民國年格式）
- **TIMESTAMP類型**：完整日期時間
- **DATE類型**：僅日期部分

### 4. 狀態碼格式說明
- **XYZ格式**：X=業務類型，Y=處理階段，Z=細部狀態
- **業務類型**：2=一般違建，3/4=廣告違建，5=下水道違建
- **協同功能**：234/244/254=送協同，235/245/255=協同退回

## 🔗 關聯關係說明

### 主要外鍵關係
1. **tbflow.case_no** → **ibmcase.case_no**
2. **ibmdisnm.case_id** → **ibmcase.case_no**
3. **ibmcslan.case_id** → **ibmcase.case_no**
4. **ibmcsprj.case_id** → **ibmcase.case_no**
5. **ibmlist.case_id** → **ibmcase.case_no**
6. **collaboration_log.case_no** → **ibmcase.case_no**
7. **ibmfym.case_id** → **ibmcase.case_no**
8. **ibmsts.case_id** → **ibmcase.case_no**

### 代碼參照關係
1. **ibmcase.caseopened** → **ibmcode(RLT)**
2. **ibmcase.s_empno** → **ibmuser.empno**
3. **ibmcase.dis_b_addzon** → **ibmcode(ZON)**
4. **ibmcase.building_kind** → **ibmcode(STU)**
5. **ibmcase.blduse** → **ibmcode(BLDUSE)**
6. **ibmuser.role_id** → **ibmrole.role_id**

## 📊 資料規模統計

| 資料表 | 記錄數量 | 重要性 | 成長速度 |
|--------|----------|--------|----------|
| ibmcase | 419,817 | ⭐⭐⭐⭐⭐ | 中等 |
| tbflow | 352,514 | ⭐⭐⭐⭐⭐ | 快速 |
| ibmcode | 1,751 | ⭐⭐⭐⭐⭐ | 緩慢 |
| ibmfym | ~400,000 | ⭐⭐⭐⭐ | 中等 |
| ibmuser | ~200 | ⭐⭐⭐⭐ | 緩慢 |
| collaboration_log | ~50,000 | ⭐⭐⭐⭐ | 中等 |
| ibmlist | ~1,000,000 | ⭐⭐⭐ | 快速 |
| ibmdisnm | ~600,000 | ⭐⭐⭐ | 中等 |

## 🎯 開發注意事項

### 1. 資料完整性
- 所有日期欄位使用民國年格式需要轉換
- 狀態碼必須對應ibmcode表的RLT類型
- 協同功能的235/245/255為新增功能，需特別注意

### 2. 效能考量
- ibmcase表419,817筆需要建立適當索引
- tbflow表352,514筆建議分頁查詢
- ibmlist表檔案數量龐大需要檔案管理策略

### 3. 安全性
- 所有涉及人員資訊的表需要存取控制
- 檔案上傳功能需要檔案類型驗證
- 協同作業功能需要權限驗證

### 4. 業務邏輯
- 狀態變更需要遵循業務流程規則
- 協同作業需要雙方確認機制
- MOI匯出需要資料格式轉換

---

**本文件版本**：v1.0  
**最後更新**：2025-01-09  
**維護人員**：系統開發團隊  
**聯絡方式**：參考CLAUDE.md檔案