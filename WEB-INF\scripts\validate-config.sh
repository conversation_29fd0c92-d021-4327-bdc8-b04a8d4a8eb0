#!/bin/bash

# =====================================================
# 新北市違章建築管理系統 - 配置驗證腳本
# =====================================================
# 檔案名稱: validate-config.sh
# 用途: 驗證安全配置是否正確設定
# 使用方式: ./validate-config.sh
# =====================================================

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WEBINF_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$WEBINF_DIR/config"

# 計數器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

# 執行檢查
run_check() {
    ((TOTAL_CHECKS++))
    local check_name="$1"
    local check_command="$2"
    
    log_info "檢查: $check_name"
    
    if eval "$check_command"; then
        log_success "$check_name"
        return 0
    else
        log_error "$check_name"
        return 1
    fi
}

# 檢查檔案是否存在
check_file_exists() {
    local file_path="$1"
    local description="$2"
    
    run_check "$description" "[[ -f '$file_path' ]]"
}

# 檢查目錄是否存在
check_directory_exists() {
    local dir_path="$1"
    local description="$2"
    
    run_check "$description" "[[ -d '$dir_path' ]]"
}

# 檢查檔案權限
check_file_permissions() {
    local file_path="$1"
    local expected_perm="$2"
    local description="$3"
    
    run_check "$description" "[[ \$(stat -c %a '$file_path') == '$expected_perm' ]]"
}

# 檢查環境變數
check_environment_variable() {
    local var_name="$1"
    local description="$2"
    
    run_check "$description" "[[ -n \"\${$var_name}\" ]]"
}

# 檢查配置項
check_config_property() {
    local config_file="$1"
    local property_key="$2"
    local description="$3"
    
    run_check "$description" "grep -q '^$property_key=' '$config_file'"
}

# 檢查Java類別
check_java_class() {
    local class_name="$1"
    local description="$2"
    local classpath="$WEBINF_DIR/classes:$WEBINF_DIR/lib/*"
    
    run_check "$description" "java -cp '$classpath' -Djava.awt.headless=true '$class_name' --version >/dev/null 2>&1 || java -cp '$classpath' -Djava.awt.headless=true '$class_name' --help >/dev/null 2>&1 || true"
}

# 檢查資料庫連線
check_database_connection() {
    local connection_name="$1"
    local description="$2"
    
    # 這裡需要實際的資料庫連線測試
    # 暫時使用模擬檢查
    log_info "資料庫連線測試: $connection_name"
    log_warning "資料庫連線測試需要應用程式運行時才能執行"
}

# 主要檢查函數
main_checks() {
    log_info "開始配置驗證..."
    echo ""
    
    # 1. 檢查基本檔案結構
    log_info "=== 檢查基本檔案結構 ==="
    check_directory_exists "$CONFIG_DIR" "配置目錄存在"
    check_file_exists "$WEBINF_DIR/site.properties" "主配置檔案存在"
    check_file_exists "$WEBINF_DIR/java/com/ezek/config/SecureConfigManager.java" "SecureConfigManager 類別存在"
    check_file_exists "$WEBINF_DIR/java/com/ezek/config/PasswordEncryptor.java" "PasswordEncryptor 類別存在"
    check_file_exists "$WEBINF_DIR/java/com/ezek/config/SecureDBConnectionFactory.java" "SecureDBConnectionFactory 類別存在"
    check_file_exists "$WEBINF_DIR/java/com/codecharge/db/JDBCConnectionFactory.java" "JDBCConnectionFactory 替代類別存在"
    echo ""
    
    # 2. 檢查環境配置檔案
    log_info "=== 檢查環境配置檔案 ==="
    check_file_exists "$CONFIG_DIR/development.properties" "開發環境配置檔案"
    check_file_exists "$CONFIG_DIR/staging.properties" "測試環境配置檔案"
    check_file_exists "$CONFIG_DIR/production.properties" "生產環境配置檔案"
    echo ""
    
    # 3. 檢查安全性檔案
    log_info "=== 檢查安全性檔案 ==="
    if [[ -f "$CONFIG_DIR/encryption.key" ]]; then
        check_file_exists "$CONFIG_DIR/encryption.key" "加密金鑰檔案存在"
        check_file_permissions "$CONFIG_DIR/encryption.key" "600" "加密金鑰檔案權限正確"
    else
        log_warning "加密金鑰檔案不存在，請執行 setup-secure-config.sh"
    fi
    echo ""
    
    # 4. 檢查配置檔案內容
    log_info "=== 檢查配置檔案內容 ==="
    local config_file="$WEBINF_DIR/site.properties"
    if [[ -f "$config_file" ]]; then
        check_config_property "$config_file" "DBConn.url" "主資料庫URL配置"
        check_config_property "$config_file" "DBConn.user" "主資料庫使用者配置"
        check_config_property "$config_file" "DBConn.password" "主資料庫密碼配置"
        check_config_property "$config_file" "DBConn2.url" "次要資料庫URL配置"
        check_config_property "$config_file" "DBConn2.user" "次要資料庫使用者配置"
        check_config_property "$config_file" "DBConn2.password" "次要資料庫密碼配置"
    fi
    echo ""
    
    # 5. 檢查環境變數
    log_info "=== 檢查重要環境變數 ==="
    check_environment_variable "JAVA_HOME" "Java 環境變數"
    
    # 檢查資料庫相關環境變數（可選）
    if [[ -n "$DB_PRIMARY_PASSWORD" ]]; then
        check_environment_variable "DB_PRIMARY_PASSWORD" "主資料庫密碼環境變數"
    fi
    if [[ -n "$DB_SECONDARY_PASSWORD" ]]; then
        check_environment_variable "DB_SECONDARY_PASSWORD" "次要資料庫密碼環境變數"
    fi
    if [[ -n "$ENCRYPTION_KEY" ]]; then
        check_environment_variable "ENCRYPTION_KEY" "加密金鑰環境變數"
    fi
    echo ""
    
    # 6. 檢查工具腳本
    log_info "=== 檢查工具腳本 ==="
    check_file_exists "$SCRIPT_DIR/setup-secure-config.sh" "安全配置設定腳本"
    check_file_exists "$SCRIPT_DIR/validate-config.sh" "配置驗證腳本"
    if [[ -f "$CONFIG_DIR/encrypt-password.sh" ]]; then
        check_file_exists "$CONFIG_DIR/encrypt-password.sh" "密碼加密工具"
        check_file_permissions "$CONFIG_DIR/encrypt-password.sh" "750" "密碼加密工具權限"
    fi
    echo ""
    
    # 7. 檢查Java編譯狀態
    log_info "=== 檢查Java類別編譯狀態 ==="
    if [[ -f "$WEBINF_DIR/classes/com/ezek/config/SecureConfigManager.class" ]]; then
        log_success "SecureConfigManager 已編譯"
        ((PASSED_CHECKS++))
    else
        log_warning "SecureConfigManager 未編譯，請編譯Java類別"
    fi
    
    if [[ -f "$WEBINF_DIR/classes/com/ezek/config/PasswordEncryptor.class" ]]; then
        log_success "PasswordEncryptor 已編譯"
        ((PASSED_CHECKS++))
    else
        log_warning "PasswordEncryptor 未編譯，請編譯Java類別"
    fi
    
    ((TOTAL_CHECKS+=2))
    echo ""
}

# 安全性檢查
security_checks() {
    log_info "=== 執行安全性檢查 ==="
    
    # 檢查敏感檔案權限
    if [[ -f "$CONFIG_DIR/encryption.key" ]]; then
        local key_perm=$(stat -c %a "$CONFIG_DIR/encryption.key")
        if [[ "$key_perm" != "600" ]]; then
            log_error "加密金鑰檔案權限過於寬鬆: $key_perm"
        else
            log_success "加密金鑰檔案權限正確"
        fi
    fi
    
    # 檢查配置檔案是否包含明文密碼
    local config_file="$WEBINF_DIR/site.properties"
    if [[ -f "$config_file" ]]; then
        if grep -q "password.*=.*[^$]" "$config_file" && ! grep -q "password.*=.*\${" "$config_file"; then
            log_warning "配置檔案可能包含明文密碼，建議使用環境變數"
        else
            log_success "配置檔案未發現明文密碼"
        fi
    fi
    
    # 檢查是否有預設密碼
    if grep -q "dev_password\|test_password\|default_password" "$CONFIG_DIR"/*.properties 2>/dev/null; then
        log_warning "發現預設密碼，請在生產環境中更改"
    fi
    
    echo ""
}

# 建議改進
suggest_improvements() {
    log_info "=== 建議改進 ==="
    
    # 檢查環境模式
    local system_mode=$(grep "^system.mode=" "$WEBINF_DIR/site.properties" 2>/dev/null | cut -d'=' -f2)
    if [[ "$system_mode" == "development" ]]; then
        log_warning "當前為開發模式，生產環境請設定為 production"
    fi
    
    # 檢查日誌級別
    local log_level=$(grep "^logpriority=" "$WEBINF_DIR/site.properties" 2>/dev/null | cut -d'=' -f2)
    if [[ "$log_level" == "debug" ]]; then
        log_warning "日誌級別為 debug，生產環境建議設定為 info 或 warn"
    fi
    
    # 檢查SSL設定
    if ! grep -q "ssl.enabled=true" "$WEBINF_DIR/site.properties" 2>/dev/null; then
        log_warning "建議在生產環境啟用SSL"
    fi
    
    echo ""
}

# 生成報告
generate_report() {
    echo ""
    log_info "=== 驗證報告 ==="
    echo "總檢查項目: $TOTAL_CHECKS"
    echo "通過檢查: $PASSED_CHECKS"
    echo "失敗檢查: $FAILED_CHECKS"
    echo ""
    
    local success_rate=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        log_success "所有檢查通過！配置驗證成功"
        echo "成功率: ${success_rate}%"
        return 0
    elif [[ $success_rate -ge 80 ]]; then
        log_warning "大部分檢查通過，但仍有 $FAILED_CHECKS 項需要處理"
        echo "成功率: ${success_rate}%"
        return 1
    else
        log_error "多項檢查失敗，請檢查配置"
        echo "成功率: ${success_rate}%"
        return 2
    fi
}

# 主函數
main() {
    echo "新北市違章建築管理系統 - 配置驗證"
    echo "========================================"
    echo ""
    
    main_checks
    security_checks
    suggest_improvements
    generate_report
}

# 執行主函數
main "$@"