﻿//自行新增放大層級的數量(解決TGOS圖磚顯示最大比例尺為1/1128, 管線仍重疊無法清楚分辨問題)
var extraZoominAmt = 3;

// get app name from window.location.pathname
var appName = window.location.pathname.split("/")[1];

dojo.declare("SGSTileLayer", esri.layers.TiledMapServiceLayer, 
{
	constructor: function(sUrl, serviceRes, nLayer) 
	{
		this._url = sUrl;
		this._resource = null;
		this._layer = nLayer;
		
		var pThis = this;
		var url = sUrl + "/GetCacheConfig?FORMAT=JSON";
		
		// app is using HTTPS
		if (typeof(sys_HTTPS) !== "undefined" && sys_HTTPS) {
			url = "/" + appName + "/xml_proxy.jsp?reqUrl=" + url;
		}
		
		LoadScript(url, function()
		{
			var pNodeRes = result.Infomation;
			if (!pNodeRes)
				return;
			this._resource = pNodeRes.ResourceName;			//取得TGOS圖磚服務名稱
			var ImgWidth = parseInt(pNodeRes.TileWidth);
			var ImgHeight = parseInt(pNodeRes.TileHeight);
			var dCLeft = parseFloat(pNodeRes.CornerLeft);
			var dCLower = parseFloat(pNodeRes.CornerLower);
			
			var pEnv = pNodeRes.Envelope;
			var dCacheLeft = parseFloat(pEnv.Left);
			var dCacheTop = parseFloat(pEnv.Top);
			var dCacheRight = parseFloat(pEnv.Right);
			var dCacheBottom = parseFloat(pEnv.Bottom);
			
			pThis.spatialReference = new esri.SpatialReference({ wkid:3857 });
			
			pThis.initialExtent = (pThis.fullExtent = new esri.geometry.Extent(dCacheLeft, dCacheBottom, dCacheRight, dCacheTop, pThis.spatialReference));
			
			var resolutions = new Array();

			//依據extraZoominAmt值自行新增放大層級
			resolutions.push({level: 0, scale: 141, resolution: 0.037322767715912});
			resolutions.push({level: 1, scale: 282, resolution: 0.074645535431823});
			resolutions.push({level: 2, scale: 564, resolution: 0.149291070863646});

			var pSclss = pNodeRes.Scales;
			var pScls = pSclss.Scale;
			if (pScls)
			{
				if (pScls.length > 0)
				{
					for (var i = 0 ; i < pScls.length ; i++)
					{
						var pScl = pScls[i];
						var dem;
						if (pScl.Denominator)
							dem = parseFloat(pScl.Denominator);
						else
							dem = parseFloat(pScl._text);
						var fac = parseFloat(pScl.Factor);
						//原圖磚層級需變更
						//resolutions.push({ level: i, scale: dem, resolution: fac});
						resolutions.push({ level: i+extraZoominAmt, scale: dem, resolution: fac});
					}
				}
			}
			
			pThis.tileInfo = new esri.layers.TileInfo(
			{
				"dpi": "96",
				"format": "image/png",
				"compressionQuality": 0,
				"spatialReference": { "wkid": "3857" },							
				"rows": ImgWidth, 
				"cols": ImgHeight, 
				"origin": { "x": dCLeft, "y": dCLower }, 
				"lods": resolutions
			});
			pThis.loaded = true;
			pThis.onLoad(pThis);
		});
		
	},
	getTileUrl: function(level, row, col) 
	{
		var appId = 'x+JLVSx85Lk='; 
		var apiKey = 'in8W74q0ogpcfW/STwicK8D5QwCdddJf05/7nb+OtDh8R99YN3T0LurV4xato3TpL/fOfylvJ9Wv/khZEsXEWxsBmg+GEj4AuokiNXCh14Rei21U5GtJpIkO++Mq3AguFK/ISDEWn4hMzqgrkxNe1Q==';
		var scnt = this.tileInfo.lods.length;

		//自行新增放大層級圖磚不顯示
		//var sUrl = this._url + "/GetCacheImage?APPID=" + appId + "&APIKEY=" + apiKey + "&S=" + level + "&X=" + col + "&Y=" + (-row-1) + "&L=" + this._layer;
		var sUrl;
		if (level < extraZoominAmt) {
			sUrl = "";
		} else {
			// app is using HTTPS
			if (typeof(sys_HTTPS) !== "undefined" && sys_HTTPS) {
				sUrl = "/" + appName + "/img_proxy.jsp?S=" + (level-extraZoominAmt) + "&X=" + col + "&Y=" + (-row-1) + "&L=" + this._layer;
			} else {
				sUrl = this._url + "/GetCacheImage?APPID=" + appId + "&APIKEY=" + apiKey + "&S=" + (level-extraZoominAmt) + "&X=" + col + "&Y=" + (-row-1) + "&L=" + this._layer;
			}
		}

		return sUrl;
	}
});