# =====================================================
# 新北市違章建築管理系統 - 測試環境配置
# =====================================================
# 檔案名稱: staging.properties
# 環境: 測試環境
# 說明: 預生產環境配置，用於系統測試和驗收
# =====================================================

# ===== 環境設定 =====
system.mode=staging
system.debug=false
system.maintenance=false

# ===== 資料庫配置 =====
# 主要資料庫 (PostgreSQL) - 測試資料庫
DBConn.name=DBConn
DBConn.url=${DB_PRIMARY_URL:*********************************************}
DBConn.driver=org.postgresql.Driver
DBConn.user=${DB_PRIMARY_USER:bms_staging}
DBConn.password=${DB_PRIMARY_PASSWORD}
DBConn.maxconn=30
DBConn.timeout=300
DBConn.dbType=PostgreSQL

# 次要資料庫 (SQL Server) - 測試GIS資料庫
DBConn2.name=DBConn2
DBConn2.url=${DB_SECONDARY_URL:**************************************************************}
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.user=${DB_SECONDARY_USER:gis_staging}
DBConn2.password=${DB_SECONDARY_PASSWORD}
DBConn2.maxconn=15
DBConn2.timeout=300
DBConn2.dbType=SQLServer

# ===== 應用程式設定 =====
serverUrl=${APP_SERVER_URL:http://staging-bms.example.com}
securedUrl=${APP_SECURED_URL:https://staging-bms.example.com}
third.party.service.base.url=${THIRD_PARTY_SERVICE_URL:http://staging-third-party.example.com/}
google.maps.service.url=${GOOGLE_MAPS_SERVICE_URL:maps/index}

# ===== 安全性設定 =====
security.type=CCS
security.encryption.key=${ENCRYPTION_KEY}
security.auth.cookie.name=${AUTH_COOKIE_NAME:bmsStaging}
security.auth.cookie.expire=${AUTH_COOKIE_EXPIRE:720}
security.hash.algorithm=${HASH_ALGORITHM:SHA-256}
ALEncryptionKey=${AL_ENCRYPTION_KEY}

# ===== 日誌設定 =====
logpriority=${LOG_LEVEL:info}
logfile=${LOG_FILE:logs/bms-staging.log}
logsize=${LOG_MAX_SIZE:10240}

# ===== 本地化設定 =====
language=${SYSTEM_LANGUAGE:zh}
defaultLocale=${SYSTEM_LOCALE:zh_TW}
requestEncoding=${REQUEST_ENCODING:UTF-8}
encoding=${SYSTEM_ENCODING:UTF-8}
defaultDateFormat=${DATE_FORMAT:yyyy-MM-dd}
defaultBooleanFormat=${BOOLEAN_FORMAT:true;false}

# ===== 效能設定 =====
cache.enabled=${CACHE_ENABLED:true}
cache.size=${CACHE_SIZE:500}
session.timeout=${SESSION_TIMEOUT:30}
upload.max.size=${UPLOAD_MAX_SIZE:20971520}

# ===== 檔案路徑設定 =====
model.folder=${MODEL_FOLDER:}
designs.folder=${DESIGNS_FOLDER:Designs}
upload.folder=${UPLOAD_FOLDER:uploads/staging}
temp.folder=${TEMP_FOLDER:temp/staging}

# ===== 樣式與設計設定 =====
useDynamicStyles=${USE_DYNAMIC_STYLES:true}
defaultStyle=${DEFAULT_STYLE:Austere}
useDynamicDesigns=${USE_DYNAMIC_DESIGNS:true}
defaultDesign=${DEFAULT_DESIGN:Light}
useI18nFeatures=${USE_I18N_FEATURES:true}
isXHTMLUsed=${IS_XHTML_USED:false}
usedWarFile=${USED_WAR_FILE:false}
useAmp=${USE_AMP:true}

# ===== 測試環境專用設定 =====
test.data.enabled=true
mock.third.party.services=false
sql.debug.enabled=false
performance.monitoring.enabled=true

# ===== 安全性加強設定 =====
ssl.enabled=true
csrf.protection.enabled=true
xss.protection.enabled=true
content.security.policy.enabled=true

# ===== 監控設定 =====
health.check.enabled=true
metrics.enabled=true
audit.logging.enabled=true
error.notification.enabled=true

# ===== 整合測試設定 =====
integration.test.enabled=true
load.test.enabled=true
automated.backup.enabled=true
data.validation.enabled=true