# 資料庫觸發器和約束文件

> **任務編號**: T1.2.3  
> **文件建立**: 2025-07-05  
> **狀態**: 完成  
> **總工時**: 4小時

## 摘要

本文件記錄新北市違章建築管理系統中所有觸發器和約束條件，包括主鍵約束、外鍵約束、檢查約束和觸發器的完整清單與執行流程。

## 觸發器總覽

### 統計資訊
- **總觸發器數量**: 95個
- **涉及資料表**: 36個
- **觸發器類型**: 
  - BEFORE INSERT: 17個
  - BEFORE INSERT OR UPDATE: 4個
  - AFTER INSERT/UPDATE/DELETE: 74個

## 觸發器詳細清單

### 1. 主要業務表觸發器

#### 1.1 ibmcase (主案件表)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `uavdcp_bef_ins_tri` | BEFORE INSERT | `ibmcase_bef_ins` | 案件建立前處理 |
| `ibmcase_ins` | AFTER INSERT | `ibmcase_if_ins` | 案件新增後處理 |
| `ibmcase_upd` | AFTER UPDATE | `ibmcase_if_upd` | 案件更新後處理 |
| `ibmcase_del` | AFTER DELETE | `ibmcase_if_del` | 案件刪除後處理 |

#### 1.2 ibmcode (代碼表)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmcode_ins` | AFTER INSERT | `ibmcode_if_ins` | 代碼新增後處理 |
| `ibmcode_upd` | AFTER UPDATE | `ibmcode_if_upd` | 代碼更新後處理 |
| `ibmcode_del` | AFTER DELETE | `ibmcode_if_del` | 代碼刪除後處理 |

#### 1.3 ibmlawfee (法務收費表)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `get_new_case_id` | BEFORE INSERT | `ibmlawfee_bef_ins` | 自動產生案件編號 |
| `set_new_ibmlawfee_log` | BEFORE INSERT OR UPDATE | `ibmlawfee_log_ins` | 記錄異動日誌 |

### 2. 專案相關觸發器

#### 2.1 消防案件 (ibmfirecase)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `get_new_case_id` | BEFORE INSERT | `ibmfirecase_bef_ins` | 自動產生案件編號 |
| `set_new_ibmfirecase_log` | BEFORE INSERT OR UPDATE | `ibmfirecase_log_ins` | 記錄異動日誌 |

#### 2.2 勞工法案件 (ibmlaborlaw)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `get_new_case_id` | BEFORE INSERT | `ibmlaborlaw_bef_ins` | 自動產生案件編號 |
| `set_new_ibmlaborlaw_log` | BEFORE INSERT OR UPDATE | `ibmlaborlaw_log_ins` | 記錄異動日誌 |

### 3. 土地相關觸發器

#### 3.1 案件土地 (ibmcslan)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmcslan_bef_ins_tri` | BEFORE INSERT | `ibmcslan_bef_ins` | 土地資料插入前處理 |
| `ibmcslan_ins` | AFTER INSERT | `ibmcslan_if_ins` | 土地資料新增後處理 |
| `ibmcslan_upd` | AFTER UPDATE | `ibmcslan_if_upd` | 土地資料更新後處理 |
| `ibmcslan_del` | AFTER DELETE | `ibmcslan_if_del` | 土地資料刪除後處理 |

#### 3.2 違規土地 (ibmviolation_land)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmviolation_land_bef_ins` | BEFORE INSERT | `ibmviolation_land_bef_ins` | 違規土地插入前處理 |

### 4. 系統管理觸發器

#### 4.1 用戶管理 (ibmuser)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmuser_ins` | AFTER INSERT | `ibmuser_if_ins` | 用戶新增後處理 |
| `ibmuser_upd` | AFTER UPDATE | `ibmuser_if_upd` | 用戶更新後處理 |
| `ibmuser_del` | AFTER DELETE | `ibmuser_if_del` | 用戶刪除後處理 |

#### 4.2 角色權限 (ibmrole, ibmperm)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmrole_ins` | AFTER INSERT | `ibmrole_if_ins` | 角色新增後處理 |
| `ibmrole_upd` | AFTER UPDATE | `ibmrole_if_upd` | 角色更新後處理 |
| `ibmrole_del` | AFTER DELETE | `ibmrole_if_del` | 角色刪除後處理 |
| `ibmperm_ins` | AFTER INSERT | `ibmperm_if_ins` | 權限新增後處理 |
| `ibmperm_upd` | AFTER UPDATE | `ibmperm_if_upd` | 權限更新後處理 |
| `ibmperm_del` | AFTER DELETE | `ibmperm_if_del` | 權限刪除後處理 |

### 5. 報表系統觸發器

#### 5.1 報表框架 (ibmrpfr, ibmrplan 等)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmrpfr_bef_ins_tri` | BEFORE INSERT | `ibmrpfr_bef_ins` | 報表建立前處理 |
| `ibmrplan_bef_ins_tri` | BEFORE INSERT | `ibmrplan_bef_ins` | 報表計畫建立前處理 |
| `ibmrplb_bef_ins_tri` | BEFORE INSERT | `ibmrplb_bef_ins` | 報表標籤建立前處理 |
| `ibmrpli_bef_ins_tri` | BEFORE INSERT | `ibmrpli_bef_ins` | 報表項目建立前處理 |
| `ibmrpsm_bef_ins_tri` | BEFORE INSERT | `ibmrpsm_bef_ins` | 報表摘要建立前處理 |
| `ibmrpuc_bef_ins_tri` | BEFORE INSERT | `ibmrpuc_bef_ins` | 報表用戶建立前處理 |

### 6. UAV 系統觸發器

#### 6.1 UAV 數據管理
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `uavdcp_bef_ins_tri` | BEFORE INSERT | `uavdcp_bef_ins` | UAV DCP 插入前處理 |
| `uavdom_bef_ins_tri` | BEFORE INSERT | `uavdom_bef_ins` | UAV DOM 插入前處理 |
| `uavdcp_log_bef_ins_tri` | BEFORE INSERT | `uavdcp_log_bef_ins` | UAV DCP 記錄插入前處理 |
| `uavdom_log_bef_ins_tri` | BEFORE INSERT | `uavdom_log_bef_ins` | UAV DOM 記錄插入前處理 |

### 7. 檔案管理觸發器

#### 7.1 消防案件檔案 (ibmfirecase_file)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmfirecase_file_ins` | AFTER INSERT | `ibmfirecase_file_if_ins` | 消防檔案新增後處理 |
| `ibmfirecase_file_del` | AFTER DELETE | `ibmfirecase_file_if_del` | 消防檔案刪除後處理 |

#### 7.2 勞工法案件檔案 (ibmlaborlaw_file)
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `ibmlaborlaw_file_ins` | AFTER INSERT | `ibmlaborlaw_file_if_ins` | 勞工法檔案新增後處理 |
| `ibmlaborlaw_file_del` | AFTER DELETE | `ibmlaborlaw_file_if_del` | 勞工法檔案刪除後處理 |

### 8. 外鍵約束觸發器

#### 8.1 Excel 匯入外鍵約束
| 觸發器名稱 | 觸發時機 | 函數名稱 | 功能說明 |
|-----------|----------|----------|----------|
| `RI_ConstraintTrigger_c_21161` | AFTER INSERT | `RI_FKey_check_ins` | 外鍵檢查（插入） |
| `RI_ConstraintTrigger_c_21162` | AFTER UPDATE | `RI_FKey_check_upd` | 外鍵檢查（更新） |
| `RI_ConstraintTrigger_a_21159` | AFTER DELETE | `RI_FKey_noaction_del` | 外鍵無動作（刪除） |
| `RI_ConstraintTrigger_a_21160` | AFTER UPDATE | `RI_FKey_noaction_upd` | 外鍵無動作（更新） |

## 約束條件總覽

### 統計資訊
- **主鍵約束**: 48個
- **外鍵約束**: 1個
- **檢查約束**: 3個
- **唯一約束**: 0個

## 約束條件詳細清單

### 1. 主鍵約束 (PRIMARY KEY)

#### 1.1 核心業務表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| caseopened | caseopened_pk | case_id | 案件開啟記錄 |
| casesubmitsts | casesubmitsts_pk | case_id | 案件提交狀態 |
| ibmcase | ibmcase_pkey | case_id, reg_yy, reg_no | 主案件表（複合主鍵） |
| ibmcode | ibmcode_pkey | code_type, code_seq | 系統代碼表（複合主鍵） |
| ibmlawfee | 無主鍵約束 | case_id | 法務收費表（缺少主鍵約束） |

#### 1.2 專案相關表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| ibmfirecase | ibmfirecase_pkey | case_id | 消防案件 |
| ibmfirecase_address | ibmfirecase_address_pkey | case_id | 消防案件地址 |
| ibmfirecase_cslan | ibmfirecase_cslan_pkey | case_id, land_seq | 消防案件土地（複合主鍵） |
| ibmfirecase_file | ibmfirecase_file_pk | case_id, pic_kind, pic_seq | 消防案件檔案（複合主鍵） |
| ibmlaborlaw | ibmlaborlaw_pkey | case_id | 勞工法案件 |
| ibmlaborlaw_address | ibmlaborlaw_address_pkey | case_id | 勞工法案件地址 |
| ibmlaborlaw_cslan | ibmlaborlaw_cslan_pkey | case_id, land_seq | 勞工法案件土地（複合主鍵） |
| ibmlaborlaw_file | ibmlaborlaw_file_pk | case_id, pic_kind, pic_seq | 勞工法案件檔案（複合主鍵） |
| ibmlaborlaw_case | ibmlaborlaw_case_pkey | ibmlaborlaw_case_id, ibmcase_case_id | 勞工法關聯案件（複合主鍵） |

#### 1.3 系統管理表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| ibmuser | ibmuser_pk | empno | 用戶表 |
| ibmrole | ibmrole_pkey | role_id | 角色表 |
| ibmperm | ibmperm_pkey | role_id, item_id | 權限表（複合主鍵） |
| ibmmenu | ibmmenu_pkey | item_id | 選單表 |
| ibmparam | ibmparam_pkey | param_class, param_key | 參數表（複合主鍵） |

#### 1.4 土地相關表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| ibmcslan | ibmcslan_pk | case_id, land_seq | 案件土地（複合主鍵） |
| ibmviolation_land | ibmviolation_land_pkey | case_id, case_seq | 違規土地（複合主鍵） |

#### 1.5 報表系統表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| ibmrpfr | ibmrpfr_pkey | rpt_id | 報表框架 |
| ibmrplan | ibmrplan_pkey | rpt_id, land_seq | 報表計畫（複合主鍵） |
| ibmrplb | ibmrplb_pkey | rpt_id | 報表標籤 |
| ibmrpli | ibmrpli_pkey | rpt_id | 報表項目 |
| ibmrpsm | ibmrpsm_pkey | rpt_id | 報表摘要 |
| ibmrpuc | ibmrpuc_pkey | rpt_id | 報表用戶 |

#### 1.6 記錄日誌表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| log_ibmfirecase | log_ibmfirecase_pkey | id | 消防案件記錄（UUID） |
| log_ibmlaborlaw | ibmlaborlaw_log_pkey | id | 勞工法案件記錄（UUID） |
| log_ibmlawfee | ibmlawfee_log_pkey | id | 法務收費記錄（UUID） |

#### 1.7 其他表主鍵
| 表名 | 約束名稱 | 欄位 | 說明 |
|------|----------|------|------|
| ibmlawfee_installment | ibmlawfee_installment_pkey | id | 分期付款記錄 |
| ibmlist | ibmlist_pk | systid, case_id, pic_kind, pic_seq | 檔案清單（複合主鍵） |
| ibmsts | ibmsts_pkey | case_id | 狀態表 |
| ibmfym | ibmfym_pkey | case_id, acc_seq | FYM 表（複合主鍵） |
| ibmifap | ibmifap_pkey | ifap_id | IFAP 表 |
| ibmcprp | ibmcprp_pkey | t_yymm | 案件屬性表 |
| ibmcsprj | ibmcsprj_pk | case_id, prj_code | 案件專案（複合主鍵） |
| ibmdisnm | ibmdisnm_pk | case_id, case_seq | 災害名稱（複合主鍵） |
| ibmmnrp | ibmmnrp_pkey | t_yymm, zon_desc, kind | 月報表（複合主鍵） |
| ibmmnrpf | ibmmnrpf_pkey | t_yymm | 月報表框架 |

### 2. 外鍵約束 (FOREIGN KEY)

| 表名 | 約束名稱 | 欄位 | 參考表 | 參考欄位 | 說明 |
|------|----------|------|--------|----------|------|
| im52101_excel_caseid | im52101_excel_caseid_import_id_fkey | import_id | im52101_excel_imports | import_id | Excel 匯入外鍵 |

### 3. 檢查約束 (CHECK)

| 表名 | 約束名稱 | 約束條件 | 說明 |
|------|----------|----------|------|
| ibmcase | ibmcase_stop_work_record_check | stop_work_record IN ('N', 'Y') | 停工記錄檢查 |
| log_ibmcase | log_ibmcase_stop_work_record_check | stop_work_record IN ('N', 'Y') | 停工記錄檢查（記錄表） |
| ibmcasebatchimport | ibmcasebatchimport_status_check | status IN ('NEW', 'PROCESSING', 'PENDING', 'COMPLETED', 'ERROR') | 批次匯入狀態檢查 |

## 觸發器執行流程

### 1. 案件新增流程
```
1. INSERT INTO ibmcase
2. 觸發 uavdcp_bef_ins_tri (BEFORE INSERT)
   └── 執行 ibmcase_bef_ins() 函數
       └── 案件前置處理
3. 實際插入資料
4. 觸發 ibmcase_ins (AFTER INSERT)
   └── 執行 ibmcase_if_ins() 函數
       └── 案件後續處理
```

### 2. 法務收費案件新增流程
```
1. INSERT INTO ibmlawfee
2. 觸發 get_new_case_id (BEFORE INSERT)
   └── 執行 ibmlawfee_bef_ins() 函數
       └── 自動產生 case_id
3. 觸發 set_new_ibmlawfee_log (BEFORE INSERT)
   └── 執行 ibmlawfee_log_ins() 函數
       └── 記錄到 log_ibmlawfee
4. 實際插入資料
```

### 3. 外鍵約束檢查流程
```
1. INSERT INTO im52101_excel_caseid
2. 觸發 RI_ConstraintTrigger_c_21161 (AFTER INSERT)
   └── 執行 RI_FKey_check_ins() 函數
       └── 檢查 import_id 是否存在於 im52101_excel_imports
```

## 約束條件分析

### 1. 缺失的約束
- **ibmlawfee 表缺少主鍵約束**: 建議增加 PRIMARY KEY (case_id)
- **金額邏輯約束**: 建議增加 payable_amount = pay_amount + unpay_amount 檢查
- **日期邏輯約束**: 建議增加日期順序檢查

### 2. 建議增加的約束
```sql
-- 1. ibmlawfee 主鍵約束
ALTER TABLE ibmlawfee ADD CONSTRAINT ibmlawfee_pkey PRIMARY KEY (case_id);

-- 2. 金額邏輯約束
ALTER TABLE ibmlawfee ADD CONSTRAINT chk_amount_logic 
CHECK (payable_amount = COALESCE(pay_amount, 0) + COALESCE(unpay_amount, 0));

-- 3. 繳款日期邏輯約束
ALTER TABLE ibmlawfee ADD CONSTRAINT chk_payment_date_logic 
CHECK (payment_date >= paymentbook_date OR payment_date IS NULL);

-- 4. 分期付款標記約束
ALTER TABLE ibmlawfee ADD CONSTRAINT chk_installment_flag 
CHECK (is_installment IN ('Y', 'N', NULL));
```

## 效能影響分析

### 1. 觸發器效能影響
- **高頻觸發器**: ibmcase 相關觸發器每次案件操作都會執行
- **記錄日誌觸發器**: 每次異動都會產生 UUID 和記錄，可能影響效能
- **ID 生成觸發器**: 需要查詢 MAX 值，在高併發情況下可能成為瓶頸

### 2. 約束檢查效能
- **複合主鍵**: 需要建立適當的索引支援
- **外鍵約束**: 每次插入/更新都會檢查參考完整性
- **檢查約束**: 每次異動都會驗證條件

## 維護建議

### 1. 定期監控
- 監控觸發器執行時間
- 檢查約束違反記錄
- 分析 log_ 表的成長速度

### 2. 效能優化
- 為複合主鍵建立適當索引
- 考慮批次處理減少觸發器執行次數
- 定期清理舊的 log_ 記錄

### 3. 安全性
- 檢查觸發器函數的權限設定
- 確保約束條件不會被繞過
- 定期備份觸發器和約束定義

---

*此文件由【B】Claude Code - 後端開發任務組產出*