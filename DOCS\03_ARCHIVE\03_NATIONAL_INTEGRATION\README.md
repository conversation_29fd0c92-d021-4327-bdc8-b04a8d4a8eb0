# 違章建築資料拋送至國土署系統 - 文件索引

## 📁 專案概述
本目錄包含新北市違章建築管理系統與國土署資料整合專案的完整技術文件。

## 🗂️ 目錄結構

### 01_DESIGN - 系統設計
- **[資料拋送至國土署系統分析設計書.md](01_DESIGN/資料拋送至國土署系統分析設計書.md)**
  - 系統架構設計
  - 資料庫設計（5張核心表格）
  - 資料流程設計
  - 回滾機制實作

- **[開發TODO.md](01_DESIGN/開發TODO.md)**
  - 4階段開發計劃（8-10週）
  - 詳細任務清單
  - 風險管理計劃
  - 完成標準定義

### 02_API - API整合規格
- **[API整合規格書.md](02_API/API整合規格書.md)**
  - RESTful API端點定義
  - OAuth 2.0認證流程
  - 錯誤碼與處理策略
  - 批次處理與分頁機制
  - Python/Node.js整合範例

### 03_IMPLEMENTATION - 實作規格
- **[資料轉換對應規格書.md](03_IMPLEMENTATION/資料轉換對應規格書.md)**
  - 詳細欄位對應表
  - 126個狀態碼轉換
  - 資料驗證規則
  - 資料清理流程
  - C#實作範例

### 04_TESTING - 測試計劃
- **[測試計畫書.md](04_TESTING/測試計畫書.md)**
  - 單元測試案例
  - 整合測試案例
  - 壓力測試計劃
  - UAT驗收測試
  - JMeter測試配置

### 05_DEPLOYMENT - 部署與維運
- **[部署與維運手冊.md](05_DEPLOYMENT/部署與維運手冊.md)**
  - Windows Service部署SOP
  - 災難復原計劃（RTO:4hr）
  - 日常維運指南
  - 效能調校建議
  - 安全管理規範

### 06_MONITORING - 監控系統
- **[監控系統設計書.md](06_MONITORING/監控系統設計書.md)**
  - KPI指標定義
  - 告警規則設計
  - 異常偵測演算法
  - Prometheus + Grafana架構

- **[monitor_dashboard.html](06_MONITORING/monitor_dashboard.html)**
  - Bootstrap 5監控儀表板範例
  - 即時資料更新展示
  - 系統健康度評分
  - 互動式圖表

## 🔑 關鍵技術要點

### 技術架構
- **後端**：ASP.NET Core (.NET 8)
- **資料庫**：PostgreSQL + JSONB
- **服務**：Windows Service
- **ORM**：Dapper
- **監控**：Prometheus + Grafana

### 核心設計原則
- KISS（保持簡單）
- DRY（避免重複）
- YAGNI（只建必要功能）

### 安全機制
- OAuth 2.0 + API Key雙重認證
- 完整稽核追蹤（Audit Log）
- 資料加密與遮罩

## 📊 專案里程碑

| 階段 | 時程 | 主要交付項目 |
|------|------|-------------|
| Phase 1 | 第1-2週 | 基礎建設完成 |
| Phase 2 | 第3-5週 | 核心功能開發 |
| Phase 3 | 第6-7週 | 系統整合測試 |
| Phase 4 | 第8-10週 | 部署與上線 |

## 🚀 快速導航

### 開發人員
1. 先閱讀[系統分析設計書](01_DESIGN/資料拋送至國土署系統分析設計書.md)了解架構
2. 查看[開發TODO](01_DESIGN/開發TODO.md)掌握任務
3. 參考[API規格](02_API/API整合規格書.md)與[資料對應](03_IMPLEMENTATION/資料轉換對應規格書.md)

### 測試人員
1. 查看[測試計畫書](04_TESTING/測試計畫書.md)了解測試策略
2. 準備測試環境與資料

### 維運人員
1. 熟悉[部署手冊](05_DEPLOYMENT/部署與維運手冊.md)
2. 設定[監控系統](06_MONITORING/監控系統設計書.md)

## 📞 聯絡資訊

- 專案負責人：[待指定]
- 技術聯絡人：[待指定]
- 文件維護：開發團隊

---

最後更新：2024-01-08