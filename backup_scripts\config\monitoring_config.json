{"monitoring": {"enabled": true, "interval_minutes": 5, "continuous_mode": true, "health_check_interval_minutes": 15, "log_retention_days": 30}, "backup": {"base_path": "D:\\Backups\\BMS", "monitor_paths": ["D:\\Backups\\BMS\\full", "D:\\Backups\\BMS\\incremental", "D:\\Backups\\BMS\\differential", "D:\\Backups\\BMS\\sqlserver", "D:\\Backups\\BMS\\application"]}, "sla": {"postgresql": {"full_backup_max_age_hours": 24, "incremental_backup_max_age_hours": 6, "differential_backup_max_age_hours": 12, "backup_success_rate_threshold": 95}, "sqlserver": {"full_backup_max_age_hours": 24, "differential_backup_max_age_hours": 8, "log_backup_max_age_hours": 1, "backup_success_rate_threshold": 95}, "application": {"full_backup_max_age_hours": 168, "incremental_backup_max_age_hours": 4, "config_backup_max_age_hours": 2, "backup_success_rate_threshold": 90}}, "thresholds": {"disk_space": {"warning_percent": 20, "critical_percent": 10}, "memory": {"warning_percent": 80, "critical_percent": 90}, "cpu": {"warning_percent": 80, "critical_percent": 90}, "backup_size": {"warning_growth_percent": 50, "critical_growth_percent": 100}}, "alerts": {"enabled": true, "severity_levels": {"critical": 1, "warning": 2, "info": 3, "success": 4}, "rate_limiting": {"enabled": true, "max_alerts_per_hour": 10, "cooldown_minutes": 30}, "escalation": {"enabled": true, "escalation_time_minutes": 60, "escalation_levels": [{"level": 1, "recipients": ["<EMAIL>"]}, {"level": 2, "recipients": ["<EMAIL>", "<EMAIL>"]}, {"level": 3, "recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}]}}, "notifications": {"enabled": true, "email": {"enabled": true, "smtp_server": "smtp.example.com", "smtp_port": 587, "use_ssl": true, "username": "<EMAIL>", "password": "email_password", "from_address": "<EMAIL>", "to_address": "<EMAIL>", "subject_prefix": "[BMS Monitoring]"}, "slack": {"enabled": false, "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "channel": "#monitoring", "username": "BMS Monitor", "icon_emoji": ":warning:"}, "sms": {"enabled": false, "provider": "twi<PERSON>", "account_sid": "your_twilio_account_sid", "auth_token": "your_twilio_auth_token", "from_number": "+**********", "to_number": "+**********"}, "webhook": {"enabled": false, "url": "https://webhook.example.com/alerts", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your_token"}}}, "metrics": {"enabled": true, "retention_days": 90, "collection_interval_minutes": 5, "metrics_to_collect": ["backup_success_rate", "backup_duration", "backup_size", "storage_usage", "system_resources", "service_health"]}, "reporting": {"enabled": true, "daily_report": {"enabled": true, "schedule": "0 8 * * *", "recipients": ["<EMAIL>"]}, "weekly_report": {"enabled": true, "schedule": "0 8 * * 1", "recipients": ["<EMAIL>", "<EMAIL>"]}, "monthly_report": {"enabled": true, "schedule": "0 8 1 * *", "recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}}, "dashboards": {"enabled": true, "web_dashboard": {"enabled": true, "port": 8888, "refresh_interval_seconds": 30}, "export_formats": ["json", "csv", "html"], "charts": [{"name": "backup_success_rate", "type": "line", "time_range": "24h"}, {"name": "storage_usage", "type": "bar", "time_range": "7d"}, {"name": "backup_duration", "type": "scatter", "time_range": "7d"}]}, "health_checks": [{"name": "postgresql_connection", "type": "database", "enabled": true, "interval_minutes": 5, "timeout_seconds": 30, "parameters": {"host": "localhost", "port": 5432, "database": "bms", "username": "postgres", "password": "S!@h@202203"}}, {"name": "sqlserver_connection", "type": "database", "enabled": true, "interval_minutes": 5, "timeout_seconds": 30, "parameters": {"server": "**************", "port": 2433, "database": "ramsGIS", "username": "sa", "password": "$ystemOnlin168"}}, {"name": "tomcat_service", "type": "service", "enabled": true, "interval_minutes": 2, "parameters": {"service_name": "Apache Tomcat 9.0 Tomcat9"}}, {"name": "web_application", "type": "http", "enabled": true, "interval_minutes": 5, "timeout_seconds": 30, "parameters": {"url": "http://localhost:8080/", "expected_status": 200}}, {"name": "backup_storage", "type": "disk", "enabled": true, "interval_minutes": 15, "parameters": {"path": "D:\\Backups\\BMS", "min_free_space_gb": 10}}], "automation": {"enabled": true, "auto_remediation": {"enabled": false, "actions": [{"condition": "disk_space_low", "action": "cleanup_old_backups", "parameters": {"days_to_keep": 7}}, {"condition": "service_stopped", "action": "restart_service", "parameters": {"max_attempts": 3, "delay_seconds": 30}}]}, "scheduled_tasks": [{"name": "backup_verification", "schedule": "0 6 * * *", "action": "verify_latest_backups", "enabled": true}, {"name": "cleanup_old_logs", "schedule": "0 2 * * *", "action": "cleanup_logs", "parameters": {"days_to_keep": 30}, "enabled": true}]}, "security": {"audit_logging": true, "encrypt_communications": true, "authentication": {"enabled": false, "type": "windows", "required_groups": ["BMS_Administrators"]}, "access_control": {"enabled": true, "allowed_ips": ["127.0.0.1", "************/24"], "blocked_ips": []}}, "logging": {"level": "INFO", "max_file_size_mb": 10, "max_files": 10, "log_to_console": true, "log_to_file": true, "log_to_eventlog": true, "structured_logging": true}, "performance": {"max_concurrent_checks": 5, "check_timeout_seconds": 60, "batch_size": 100, "memory_limit_mb": 512}}