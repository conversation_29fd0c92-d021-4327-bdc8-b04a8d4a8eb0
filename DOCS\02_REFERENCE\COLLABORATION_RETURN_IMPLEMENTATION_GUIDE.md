# 協同退回實施手冊

## 目錄
1. [實施概要](#實施概要)
2. [資料庫變更](#資料庫變更)
3. [程式碼實作](#程式碼實作)
4. [測試指引](#測試指引)
5. [部署清單](#部署清單)
6. [操作手冊](#操作手冊)
7. [故障排除](#故障排除)

## 實施概要

### 功能說明
協同退回功能允許協同承辦人在發現案件誤送或需要原承辦人補正時，將案件退回給原承辦人處理。

### 技術規格
- **新增狀態碼**：235（一般）、245（廣告）、255（施工中）
- **API端點**：`/case_collaboration_return.jsp`
- **前端介面**：`im10201_man.jsp` 新增退回按鈕
- **資料庫事務**：確保狀態變更的原子性

### 實施時程
- 資料庫變更：30分鐘
- 程式部署：1小時
- 功能測試：2小時
- 總計：3.5小時

## 資料庫變更

### 1. 新增狀態碼定義

```sql
-- 新增協同退回補正狀態碼
INSERT INTO public.ibmcode (code_type, code_seq, code_desc, code_enname, code_expired)
VALUES 
    ('RLT', '235', '協同退回補正', 'Collaboration Return Correction', '0'),
    ('RLT', '245', '協同退回補正', 'Collaboration Return Correction', '0'),
    ('RLT', '255', '協同退回補正', 'Collaboration Return Correction', '0');

-- 驗證插入結果
SELECT code_type, code_seq, code_desc 
FROM public.ibmcode 
WHERE code_type = 'RLT' 
AND code_seq IN ('235', '245', '255')
ORDER BY code_seq;
```

### 2. 建立退回原因記錄表（可選）

```sql
-- 建立協同退回記錄表
CREATE TABLE IF NOT EXISTS public.collaboration_return_log (
    log_id SERIAL PRIMARY KEY,
    case_no VARCHAR(50) NOT NULL,
    return_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    return_user VARCHAR(50) NOT NULL,
    return_reason TEXT,
    from_state VARCHAR(3),
    to_state VARCHAR(3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 建立索引
CREATE INDEX idx_collab_return_case ON collaboration_return_log(case_no);
CREATE INDEX idx_collab_return_date ON collaboration_return_log(return_date);
```

### 3. 更新視圖（如有必要）

```sql
-- 如果有依賴狀態碼的視圖，需要更新
-- 範例：更新案件狀態統計視圖
CREATE OR REPLACE VIEW v_case_status_summary AS
SELECT 
    CASE 
        WHEN caseopened IN ('234', '244', '254') THEN '協同中'
        WHEN caseopened IN ('235', '245', '255') THEN '協同退回補正'
        -- 其他狀態判斷...
    END as status_group,
    COUNT(*) as case_count
FROM buildcase
GROUP BY status_group;
```

## 程式碼實作

### 1. 協同退回API（case_collaboration_return.jsp）

```jsp
<%@ page contentType="application/json; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.*" %>
<%@ page import="org.json.JSONObject" %>
<%@ page import="com.ezek.web.user" %>
<%@ page import="com.ezek.db.DBConnectionManager" %>

<%
    // 初始化回應
    JSONObject result = new JSONObject();
    Connection conn = null;
    PreparedStatement pstmt = null;
    
    try {
        // 1. 權限檢查
        user userInfo = (user) session.getAttribute("user");
        if (userInfo == null) {
            result.put("success", false);
            result.put("message", "使用者未登入");
            out.print(result.toString());
            return;
        }
        
        // 2. 參數驗證
        String caseNo = request.getParameter("case_no");
        String returnReason = request.getParameter("return_reason");
        
        if (caseNo == null || caseNo.trim().isEmpty()) {
            result.put("success", false);
            result.put("message", "案件編號不可為空");
            out.print(result.toString());
            return;
        }
        
        // 3. 取得資料庫連線
        DBConnectionManager dbManager = DBConnectionManager.getInstance();
        conn = dbManager.getConnection("DBConn");
        conn.setAutoCommit(false);
        
        // 4. 查詢案件當前狀態
        String checkSQL = "SELECT caseopened, case_con_user, s_empno " +
                         "FROM buildcase WHERE case_no = ?";
        pstmt = conn.prepareStatement(checkSQL);
        pstmt.setString(1, caseNo);
        ResultSet rs = pstmt.executeQuery();
        
        if (!rs.next()) {
            throw new Exception("案件不存在");
        }
        
        String currentState = rs.getString("caseopened");
        String collaborationUser = rs.getString("case_con_user");
        String originalHandler = rs.getString("s_empno");
        rs.close();
        pstmt.close();
        
        // 5. 驗證權限（只有協同承辦人可退回）
        if (!userInfo.getEmpno().equals(collaborationUser)) {
            throw new Exception("您沒有權限執行此操作");
        }
        
        // 6. 驗證狀態（只有協同中狀態可退回）
        if (!Arrays.asList("234", "244", "254").contains(currentState)) {
            throw new Exception("當前狀態無法執行退回操作");
        }
        
        // 7. 決定目標狀態
        String targetState = "";
        String nextState = "";
        switch (currentState) {
            case "234":
                targetState = "235";
                nextState = "231";
                break;
            case "244":
                targetState = "245";
                nextState = "241";
                break;
            case "254":
                targetState = "255";
                nextState = "251";
                break;
        }
        
        // 8. 更新案件狀態
        String updateSQL = "UPDATE buildcase SET " +
                          "caseopened = ?, " +
                          "case_con_user = NULL, " +
                          "case_con_date = NULL, " +
                          "s_empno = ?, " +
                          "update_date = CURRENT_TIMESTAMP " +
                          "WHERE case_no = ?";
        pstmt = conn.prepareStatement(updateSQL);
        pstmt.setString(1, targetState);
        pstmt.setString(2, originalHandler);
        pstmt.setString(3, caseNo);
        int updateCount = pstmt.executeUpdate();
        pstmt.close();
        
        if (updateCount != 1) {
            throw new Exception("更新案件狀態失敗");
        }
        
        // 9. 結束當前協同流程記錄
        String endFlowSQL = "UPDATE tbflow SET flow_edate = CURRENT_TIMESTAMP " +
                           "WHERE case_no = ? AND case_state = ? AND flow_edate IS NULL";
        pstmt = conn.prepareStatement(endFlowSQL);
        pstmt.setString(1, caseNo);
        pstmt.setString(2, currentState);
        pstmt.executeUpdate();
        pstmt.close();
        
        // 10. 新增退回補正流程記錄
        String insertFlowSQL = "INSERT INTO tbflow (case_no, case_state, s_empno, flow_sdate) " +
                              "VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
        pstmt = conn.prepareStatement(insertFlowSQL);
        pstmt.setString(1, caseNo);
        pstmt.setString(2, targetState);
        pstmt.setString(3, originalHandler);
        pstmt.executeUpdate();
        pstmt.close();
        
        // 11. 記錄退回原因（如果有退回記錄表）
        String logSQL = "INSERT INTO collaboration_return_log " +
                       "(case_no, return_user, return_reason, from_state, to_state) " +
                       "VALUES (?, ?, ?, ?, ?)";
        pstmt = conn.prepareStatement(logSQL);
        pstmt.setString(1, caseNo);
        pstmt.setString(2, userInfo.getEmpno());
        pstmt.setString(3, returnReason);
        pstmt.setString(4, currentState);
        pstmt.setString(5, targetState);
        pstmt.executeUpdate();
        pstmt.close();
        
        // 12. 提交事務
        conn.commit();
        
        // 13. 回傳成功結果
        result.put("success", true);
        result.put("message", "協同退回成功");
        result.put("case_no", caseNo);
        result.put("new_state", targetState);
        result.put("next_state", nextState);
        
    } catch (Exception e) {
        // 錯誤處理
        if (conn != null) {
            try {
                conn.rollback();
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
        }
        result.put("success", false);
        result.put("message", "操作失敗：" + e.getMessage());
        e.printStackTrace();
    } finally {
        // 釋放資源
        if (pstmt != null) try { pstmt.close(); } catch (SQLException e) {}
        if (conn != null) {
            try {
                conn.setAutoCommit(true);
                conn.close();
            } catch (SQLException e) {}
        }
    }
    
    // 輸出結果
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");
    out.print(result.toString());
%>
```

### 2. 前端介面修改（im10201_man.jsp）

```javascript
// 在頁面適當位置加入退回按鈕（僅協同承辦人可見）
<% if (isCollaborationUser && canReturn) { %>
<button type="button" class="btn btn-warning" onclick="showReturnDialog()">
    <i class="fas fa-undo"></i> 協同退回
</button>
<% } %>

<script>
// 退回對話框
function showReturnDialog() {
    const dialog = `
        <div class="modal fade" id="returnModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">協同退回確認</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>您確定要將此案件退回給原承辦人嗎？</p>
                        <div class="mb-3">
                            <label for="returnReason" class="form-label">退回原因：</label>
                            <textarea class="form-control" id="returnReason" rows="3" 
                                      placeholder="請說明退回原因（必填）" required></textarea>
                        </div>
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> 
                            退回後，案件將返回原承辦人進行補正，您將失去此案件的編輯權限。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-warning" onclick="confirmReturn()">確認退回</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // 插入對話框並顯示
    if (!document.getElementById('returnModal')) {
        document.body.insertAdjacentHTML('beforeend', dialog);
    }
    const modal = new bootstrap.Modal(document.getElementById('returnModal'));
    modal.show();
}

// 確認退回
function confirmReturn() {
    const returnReason = document.getElementById('returnReason').value.trim();
    
    if (!returnReason) {
        alert('請填寫退回原因');
        return;
    }
    
    // 顯示載入中
    const btn = event.target;
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> 處理中...';
    
    // 發送退回請求
    fetch('/case_collaboration_return.jsp', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            case_no: '<%= caseNo %>',
            return_reason: returnReason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('協同退回成功！案件已返回原承辦人。');
            // 關閉對話框並重新載入頁面
            bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
            window.location.reload();
        } else {
            alert('操作失敗：' + data.message);
            btn.disabled = false;
            btn.innerHTML = '確認退回';
        }
    })
    .catch(error => {
        alert('系統錯誤：' + error.message);
        btn.disabled = false;
        btn.innerHTML = '確認退回';
    });
}
</script>
```

### 3. 權限判斷邏輯（im10201_Handlers.jsp）

```java
// 在頁面載入時判斷是否顯示退回按鈕
boolean isCollaborationUser = false;
boolean canReturn = false;

// 檢查當前使用者是否為協同承辦人
if (caseInfo != null && userInfo != null) {
    String currentState = caseInfo.getCaseopened();
    String collaborationUser = caseInfo.getCase_con_user();
    String currentUser = userInfo.getEmpno();
    
    // 判斷是否在協同狀態
    if (Arrays.asList("234", "244", "254").contains(currentState)) {
        // 判斷是否為協同承辦人
        if (currentUser.equals(collaborationUser)) {
            isCollaborationUser = true;
            canReturn = true;
        }
    }
}

// 將變數傳遞到JSP頁面
request.setAttribute("isCollaborationUser", isCollaborationUser);
request.setAttribute("canReturn", canReturn);
```

## 測試指引

### 1. 單元測試

#### 測試案例1：正常退回流程
```
前置條件：
- 案件狀態：234（一般協同）
- 當前使用者：協同承辦人

測試步驟：
1. 登入協同承辦人帳號
2. 開啟案件詳情頁
3. 點擊「協同退回」按鈕
4. 輸入退回原因
5. 確認退回

預期結果：
- 案件狀態變更為235
- 協同資訊清除
- 原承辦人重新獲得編輯權限
```

#### 測試案例2：權限驗證
```
前置條件：
- 案件狀態：234（一般協同）
- 當前使用者：非協同承辦人

測試步驟：
1. 嘗試直接呼叫退回API

預期結果：
- 回傳權限錯誤訊息
- 案件狀態不變
```

### 2. 整合測試

```sql
-- 測試資料準備
-- 1. 建立測試案件
INSERT INTO buildcase (case_no, caseopened, s_empno, case_con_user, case_con_date)
VALUES ('TEST2024001', '234', 'EMP001', 'EMP002', CURRENT_TIMESTAMP);

-- 2. 建立流程記錄
INSERT INTO tbflow (case_no, case_state, s_empno, flow_sdate)
VALUES ('TEST2024001', '234', 'EMP002', CURRENT_TIMESTAMP);

-- 3. 執行退回測試後驗證
-- 預期：caseopened = 235, case_con_user = NULL, s_empno = 'EMP001'
SELECT case_no, caseopened, s_empno, case_con_user 
FROM buildcase 
WHERE case_no = 'TEST2024001';

-- 4. 清理測試資料
DELETE FROM tbflow WHERE case_no = 'TEST2024001';
DELETE FROM buildcase WHERE case_no = 'TEST2024001';
```

### 3. 效能測試

```bash
# 使用 Apache JMeter 進行壓力測試
# 測試並發退回請求的處理能力
jmeter -n -t collaboration_return_test.jmx -l results.jtl
```

## 部署清單

### 1. 部署前檢查
- [ ] 備份資料庫
- [ ] 確認測試環境部署成功
- [ ] 準備回滾方案

### 2. 資料庫部署
```bash
# 執行資料庫變更腳本
psql -h localhost -U postgres -d bms -f collaboration_return_ddl.sql
```

### 3. 程式部署
```bash
# 複製新增/修改的檔案到Tomcat目錄
cp case_collaboration_return.jsp /path/to/tomcat/webapps/ROOT/
cp im10201_man.jsp /path/to/tomcat/webapps/ROOT/
cp im10201_Handlers.jsp /path/to/tomcat/webapps/ROOT/WEB-INF/handlers/

# 重啟Tomcat
systemctl restart tomcat
```

### 4. 部署後驗證
- [ ] 確認新狀態碼正確顯示
- [ ] 測試退回功能正常運作
- [ ] 檢查系統日誌無錯誤

### 5. 通知相關人員
- [ ] 發送功能上線通知
- [ ] 提供操作說明文件
- [ ] 安排教育訓練

## 操作手冊

### 協同承辦人操作步驟

1. **識別需要退回的案件**
   - 發現案件資料不完整
   - 需要原承辦人補充資料
   - 案件誤送協同

2. **執行退回操作**
   - 開啟案件詳情頁面
   - 點擊「協同退回」按鈕
   - 填寫退回原因（必填）
   - 確認退回

3. **退回後續作業**
   - 系統自動通知原承辦人
   - 案件狀態變更為「協同退回補正」
   - 失去案件編輯權限

### 原承辦人操作步驟

1. **接收退回通知**
   - 查看系統通知或待辦事項
   - 了解退回原因

2. **處理補正作業**
   - 開啟退回的案件
   - 根據退回原因進行修正
   - 完成必要的資料補充

3. **重新提交**
   - 補正完成後更新狀態
   - 可選擇重新發起協同
   - 或繼續自行處理

## 故障排除

### 常見問題

#### 1. 退回按鈕不顯示
**可能原因**：
- 非協同承辦人
- 案件不在協同狀態
- 權限設定錯誤

**解決方法**：
```sql
-- 檢查案件狀態與權限
SELECT case_no, caseopened, case_con_user, s_empno
FROM buildcase
WHERE case_no = '案件編號';
```

#### 2. 退回失敗錯誤
**可能原因**：
- 資料庫連線問題
- 事務鎖定
- 狀態碼未正確設定

**解決方法**：
```sql
-- 檢查狀態碼是否存在
SELECT * FROM ibmcode 
WHERE code_type = 'RLT' 
AND code_seq IN ('235', '245', '255');

-- 檢查是否有鎖定
SELECT * FROM pg_locks WHERE NOT granted;
```

#### 3. 退回後狀態異常
**可能原因**：
- 事務未完整提交
- 流程記錄不一致

**解決方法**：
```sql
-- 手動修正狀態
BEGIN;
UPDATE buildcase SET 
    caseopened = '231',
    case_con_user = NULL,
    case_con_date = NULL
WHERE case_no = '案件編號';

UPDATE tbflow SET flow_edate = CURRENT_TIMESTAMP
WHERE case_no = '案件編號' AND flow_edate IS NULL;

COMMIT;
```

### 緊急聯絡

- 系統管理員：ext. 1234
- 資料庫管理員：ext. 5678
- 開發團隊：<EMAIL>

## 附錄

### 相關文件
- [協同作業機制完整指南](./COLLABORATION_MECHANISM_COMPLETE_GUIDE.md)
- [狀態碼定義文件](./STATUS_CODE_ENCYCLOPEDIA.md)
- [系統架構說明](./SYSTEM_ARCHITECTURE.md)

### 版本歷史
- v1.0 (2024-01-01)：初版發布
- v1.1 (2024-01-15)：新增退回原因記錄功能
- v1.2 (2024-02-01)：優化錯誤處理機制