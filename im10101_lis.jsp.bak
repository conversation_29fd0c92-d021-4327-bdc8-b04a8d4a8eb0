<%--JSP Page Init @1-0F970FAC--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im10101_lisServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-49FDA3DB--%>
<%@include file="im10101_lisHandlers.jsp"%>
<%
    if (!im10101_lisModel.isVisible()) return;
    if (im10101_lisParent != null) {
        if (!im10101_lisParent.getChild(im10101_lisModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im10101_lisModel);
    pageContext.setAttribute("page", im10101_lisModel);
    im10101_lisModel.fireOnInitializeViewEvent(new Event());
    im10101_lisModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im10101_lisModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im10101_lisModel.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-22D1C46C--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>im10101_lis</title>
<link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
<link rel="stylesheet" type="text/css" href="in_recordgridstyle.css?110100501">
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//Include Common JSFunctions @1-C04D94A2
</script>
<script src="ClientI18N.jsp?file=Functions.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script src="ClientI18N.jsp?file=DatePicker.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script src="ClientI18N.jsp?file=Globalize.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
//End Include Common JSFunctions

//Include User Scripts @1-92024ECE
</script>
<ccs:attribute owner='page' name='scriptIncludes'/>
<script type="text/javascript">
//End Include User Scripts

//Common Script Start @1-8BFA436B
jQuery(function ($) {
    var features = { };
    var actions = { };
    var params = { };
//End Common Script Start

//BMSDISOBEY_DISTSearchOnLoad Event Start @-3CF8D189
    actions["BMSDISOBEY_DISTSearchOnLoad"] = function (eventType, parameters) {
        var result = true;
//End BMSDISOBEY_DISTSearchOnLoad Event Start

//Custom Code @20-2A29BDB7


var fields = document.forms["BMSDISOBEY_DISTSearch"].elements["s_S_DATE"];
var fielde = document.forms["BMSDISOBEY_DISTSearch"].elements["s_E_DATE"];
        addSlash(fields);
        addSlash(fielde);
        
        var LINKAGE_FIELDS = [
        // 認定號碼
        ["BMSDISOBEY_DISTSearchs_REG_NUM", "BMSDISOBEY_DISTSearchsview_REG_NUM", "BMSDISOBEY_DISTSearchs_REG_YY", "BMSDISOBEY_DISTSearchs_REG_NO"]
        ];
        
    var linkageFieldsLen = LINKAGE_FIELDS.length, linkageField = [];
        var idx = 0, initiatorFieldId = "", $initiator = {};
        
        for (idx = 0; idx < linkageFieldsLen; idx++) {
                linkageField = LINKAGE_FIELDS[idx];
                initiatorFieldId = linkageField[0];
                
                inputsLinked(initiatorFieldId, linkageField[1], linkageField[2], linkageField[3]);
                
                $initiator = $("#" + initiatorFieldId);
                if ($initiator.val() !== "") {
                        $initiator.trigger("input"); 
                }
        }    
        
        
//End Custom Code

//BMSDISOBEY_DISTSearchOnLoad Event End @-A5B9ECB8
        return result;
    };
//End BMSDISOBEY_DISTSearchOnLoad Event End

//BMSDISOBEY_DISTSearchOnSubmit Event Start @-9C23A9CB
    actions["BMSDISOBEY_DISTSearchOnSubmit"] = function (eventType, parameters) {
        var result = true;
//End BMSDISOBEY_DISTSearchOnSubmit Event Start

//Custom Code @25-2A29BDB7

  var s_S_DATE = document.forms["BMSDISOBEY_DISTSearch"].s_S_DATE.value;
  var s_E_DATE = document.forms["BMSDISOBEY_DISTSearch"].s_E_DATE.value;
  document.forms["BMSDISOBEY_DISTSearch"].s_S_DATE.value = s_S_DATE.replace(/\D/g, "");
  document.forms["BMSDISOBEY_DISTSearch"].s_E_DATE.value = s_E_DATE.replace(/\D/g, "");

//End Custom Code

//BMSDISOBEY_DISTSearchOnSubmit Event End @-A5B9ECB8
        return result;
    };
//End BMSDISOBEY_DISTSearchOnSubmit Event End

//Event Binding @1-F534BA3E
    $('*:ccsControl(BMSDISOBEY_DISTSearch)').ccsBind(function() {
        this.each(function(){ actions["BMSDISOBEY_DISTSearchOnLoad"].call(this); });
    });
    $('*:ccsControl(BMSDISOBEY_DISTSearch)').ccsBind(function() {
        this.bind("submit", actions["BMSDISOBEY_DISTSearchOnSubmit"]);
    });
//End Event Binding

//Plugin Calls @1-CB513A0B
    BMSDISOBEY_DISTSearch_DatePicker_s_S_DATE1 = new Object(); 
    BMSDISOBEY_DISTSearch_DatePicker_s_S_DATE1.format           = "yyy/mm/dd";
    BMSDISOBEY_DISTSearch_DatePicker_s_S_DATE1.style            = "Styles/Blueprint/Style.css";
    BMSDISOBEY_DISTSearch_DatePicker_s_S_DATE1.relativePathPart = "";
    BMSDISOBEY_DISTSearch_DatePicker_s_S_DATE1.themeVersion     = "3.0";
    BMSDISOBEY_DISTSearch_DatePicker_s_E_DATE1 = new Object(); 
    BMSDISOBEY_DISTSearch_DatePicker_s_E_DATE1.format           = "yyy/mm/dd";
    BMSDISOBEY_DISTSearch_DatePicker_s_E_DATE1.style            = "Styles/Blueprint/Style.css";
    BMSDISOBEY_DISTSearch_DatePicker_s_E_DATE1.relativePathPart = "";
    BMSDISOBEY_DISTSearch_DatePicker_s_E_DATE1.themeVersion     = "3.0";
//End Plugin Calls

//Common Script End @1-562554DE
});
//End Common Script End

//End CCS script
</script>
<script src="functions_synct.js" type="text/javascript" charset="utf-8"></script>
<script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
<script src="functions_synct_im.js?110100501" type="text/javascript"></script>
<script type="text/javascript">
$(document).ready(function() {
  $(".class-link-detail-B").on("click", function(evt) {
    var rowNum = $(this).prop("id").split("_")[2], 
        case_id = $("#BMSDISOBEY_DISTCASE_ID_" + rowNum).val(), 
        acc_rlt = $("#BMSDISOBEY_DISTACC_RLT_" + rowNum).val(), 
        acc_rlt_name = $("#BMSDISOBEY_DISTacc_rlt_name_" + rowNum).val();
    
    if (acc_rlt === "344") {
      evt.preventDefault();
      
      if (confirm("勘查紀錄號碼 " + case_id + " 案件狀態為" + acc_rlt_name.substring(acc_rlt_name.lastIndexOf("]") + 1) + "\n確定要異動此表單資料？")) {
        window.location = $(this).attr("href");
      }
    }
  });
});

        function addSlash(obj){
            if (obj.value.length > 0 && obj.value.indexOf("/") == -1) {
                var padding0Str = "";
                    for (var loop = 0; loop < (7 - obj.value.length); loop++) padding0Str += "0";
                    obj.value = padding0Str + obj.value;
                    obj.value = RSplitChar(obj.value, "/", "2,2");
            }
        }

// 抽回
function caseWithdraw(case_id, acc_rlt) {
	if (!confirm('確定要抽回此案件嗎：'+case_id)) {
		return;
	}

	$.ajax({
        type: 'POST',
        url: 'case_withdraw.jsp',
        data: {
        	caseId: case_id,
        	accRlt: acc_rlt,
        	empNo: '<%= session.getAttribute("UserID") %>'
        },
        async: false,
        dataType: 'json',
        success: function(data) {
        	if (data.result == '') {
        		alert(case_id+'案件已抽回');
        		$('input[name=Button_DoSearch]').click();
        	}
        },
        error: function() {
            
        }
    });
}


</script>
</head>
<body>
<div class="container">
  <div class="up_titte">
    <span class="tittle_span">&nbsp;&nbsp;&nbsp;</span> <span class="tittle_label">&nbsp;違章認定作業</span> 
  </div>
 
  <ccs:record name='BMSDISOBEY_DISTSearch'>
  <form id="BMSDISOBEY_DISTSearch" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
    <table class="table" cellspacing="0" cellpadding="0">
      <ccs:error_block>
      <tr id="{@error-block}" class="Error">
        <td colspan="4"><ccs:error_text/></td> 
      </tr>
 </ccs:error_block>
      <tr class="Controls">
        <td class="th_lis" style="WIDTH: 10%; WHITE-SPACE: nowrap; BORDER-BOTTOM: 0px"><label>勘查紀錄號碼&nbsp;</label></td> 
        <td style="BORDER-TOP: 0px; WIDTH: 20%; VERTICAL-ALIGN: middle"><input type="text" id="BMSDISOBEY_DISTSearchs_CASE_ID" maxlength="10" size="10" value="<ccs:control name='s_CASE_ID'/>" name="<ccs:control name='s_CASE_ID' property='name'/>"></td> 
        <td class="th_lis" style="WIDTH: 10%; BORDER-BOTTOM: 0px"><label for="BMSDISOBEY_DISTSearchs_S_DATE">勘查日期&nbsp;</label></td> 
        <td style="BORDER-TOP: 0px; VERTICAL-ALIGN: middle; MIN-WIDTH: 400px"><input type="text" id="BMSDISOBEY_DISTSearchs_S_DATE" maxlength="9" size="7" value="<ccs:control name='s_S_DATE'/>" name="<ccs:control name='s_S_DATE' property='name'/>">
          <ccs:datepicker name='DatePicker_s_S_DATE1'><a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');" id="BMSDISOBEY_DISTSearchDatePicker_s_S_DATE1"><img id="BMSDISOBEY_DISTSearchDatePicker_s_S_DATE1_Image" border="0" alt="Show Date Picker" src="Styles/Blueprint/Images/DatePicker.gif"></a></ccs:datepicker>&nbsp;～ <input type="text" id="BMSDISOBEY_DISTSearchs_E_DATE" maxlength="9" size="7" value="<ccs:control name='s_E_DATE'/>" name="<ccs:control name='s_E_DATE' property='name'/>">
          <ccs:datepicker name='DatePicker_s_E_DATE1'><a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');" id="BMSDISOBEY_DISTSearchDatePicker_s_E_DATE1"><img id="BMSDISOBEY_DISTSearchDatePicker_s_E_DATE1_Image" border="0" alt="Show Date Picker" src="Styles/Blueprint/Images/DatePicker.gif"></a></ccs:datepicker></td> 
      </tr>
 
      <tr class="Controls">
        <td class="th_lis" style="BORDER-BOTTOM: 0px"><label for="BMSDISOBEY_DISTSearchs_STATUS">認定通知號碼&nbsp;</label></td> 
        <td style="white-space: nowrap;"><input type="text" id="BMSDISOBEY_DISTSearchs_REG_NUM" maxlength="10" size="10" value="<ccs:control name='s_REG_NUM'/>" name="<ccs:control name='s_REG_NUM' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTSearchs_REG_YY" value="<ccs:control name='s_REG_YY'/>" name="<ccs:control name='s_REG_YY' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTSearchs_REG_NO" value="<ccs:control name='s_REG_NO'/>" name="<ccs:control name='s_REG_NO' property='name'/>"><span class="regnum-viewer"><label id="BMSDISOBEY_DISTSearchsview_REG_NUM" class="regnum-incomplete">&nbsp;</label></span></td> 
        <td class="th_lis" style="BORDER-BOTTOM: 0px"><label for="BMSDISOBEY_DISTSearchs_STATUS">案件狀態&nbsp;</label></td> 
        <td style="VERTICAL-ALIGN: middle">
          <select id="BMSDISOBEY_DISTSearchs_STATUS" name="<ccs:control name='s_STATUS' property='name'/>">
            <option selected value="">請選擇</option>
 <ccs:control name='s_STATUS' property='options'/> 
          </select>
 </td> 
      </tr>
 
      <tr class="Bottom">
        <td style="TEXT-ALIGN: right" colspan="4">
          <ccs:button name='Button_DoSearch'><input type="submit" id="BMSDISOBEY_DISTSearchButton_DoSearch" class="btn btn-primary" alt="搜&nbsp;&nbsp;&nbsp;&nbsp;尋" value="搜&nbsp;&nbsp;&nbsp;&nbsp;尋" name="<ccs:control name='Button_DoSearch' property='name'/>"></ccs:button>
          <ccs:button name='Button_Clean'><input type="submit" id="BMSDISOBEY_DISTSearchButton_Clean" class="btn btn-success" alt="清&nbsp;&nbsp;&nbsp;&nbsp;除" value="清&nbsp;&nbsp;&nbsp;&nbsp;除" name="<ccs:control name='Button_Clean' property='name'/>"></ccs:button></td> 
      </tr>
 
    </table>
 
  </form>
 </ccs:record>
  <ccs:grid name='BMSDISOBEY_DIST'>
  <table class="table" cellspacing="0" cellpadding="0">
    <tr class="Row">
      <td style="BORDER-TOP: 0px" colspan="6">[認定作業中案件] 共&nbsp;<ccs:control name='BMSDISOBEY_DIST_TotalRecords'/>&nbsp;筆</td> 
    </tr>
 
    <tr class="Caption">
      <th style="WHITE-SPACE: nowrap" scope="col">
      <ccs:sorter name='Sorter1' column='ibmcase.case_id'><span class="Sorter"><a href="<ccs:sorter_href/>" id="BMSDISOBEY_DISTSorter1">勘查紀錄號碼</a> 
      <ccs:asc_on><img alt="Ascending" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
      <ccs:desc_on><img alt="Descending" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on></span></ccs:sorter></th>
 
      <th style="WHITE-SPACE: nowrap; TEXT-ALIGN: center" scope="col">
      <ccs:sorter name='Sorter_CR_DATE' column='audnm_date'><span class="Sorter"><a href="<ccs:sorter_href/>" id="BMSDISOBEY_DISTSorter_CR_DATE">勘查日期</a> 
      <ccs:asc_on><img alt="Ascending" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
      <ccs:desc_on><img alt="Descending" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on></span></ccs:sorter></th>
 
      <th style="WHITE-SPACE: nowrap" scope="col">
      <ccs:sorter name='Sorter3' column='caddress'><span class="Sorter"><a href="<ccs:sorter_href/>" id="BMSDISOBEY_DISTSorter3">違建地點</a> 
      <ccs:asc_on><img alt="Ascending" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
      <ccs:desc_on><img alt="Descending" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on></span></ccs:sorter></th>
 
      <th style="WHITE-SPACE: nowrap; TEXT-ALIGN: center" scope="col">
      <ccs:sorter name='Sorter4' column='reg_yy, reg_no'><span class="Sorter"><a href="<ccs:sorter_href/>" id="BMSDISOBEY_DISTSorter4">認定通知號碼</a> 
      <ccs:asc_on><img alt="Ascending" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
      <ccs:desc_on><img alt="Descending" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on></span></ccs:sorter></th>
 </th>
 
      <th style="WHITE-SPACE: nowrap" scope="col" colspan="2">
      <ccs:sorter name='Sorter2' column='ib_prcs'><span class="Sorter"><a href="<ccs:sorter_href/>" id="BMSDISOBEY_DISTSorter2">案件狀態</a> 
      <ccs:asc_on><img alt="Ascending" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
      <ccs:desc_on><img alt="Descending" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on></span></ccs:sorter></th>
 
      <th scope="col">&nbsp;</th>
 
    </tr>
 
    <ccs:repeater><ccs:row>
    <tr class="Row">
      <td style="WIDTH: 8%; WHITE-SPACE: nowrap"><ccs:control name='view_CASE_ID'/><input type="hidden" name="<ccs:control name='CASE_ID' property='name'/>" value="<ccs:control name='CASE_ID'/>" id="BMSDISOBEY_DISTCASE_ID_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />"></td> 
      <td style="WIDTH: 8%; WHITE-SPACE: nowrap; TEXT-ALIGN: center"><ccs:control name='AUDNM_DATE'/></td> 
      <td><ccs:control name='caddress'/></td> 
      <td style="WIDTH: 8%; WHITE-SPACE: nowrap"><ccs:control name='reg_num'/><input type="hidden" id="BMSDISOBEY_DISTreg_yy_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />" value="<ccs:control name='reg_yy'/>" name="<ccs:control name='reg_yy' property='name'/>"><input type="hidden" id="BMSDISOBEY_DISTreg_no_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />" value="<ccs:control name='reg_no'/>" name="<ccs:control name='reg_no' property='name'/>"></td> 
      <td style="WIDTH: 8%; WHITE-SPACE: nowrap"><ccs:control name='view_acc_rlt'/><input type="hidden" name="<ccs:control name='ACC_RLT' property='name'/>" value="<ccs:control name='ACC_RLT'/>" id="BMSDISOBEY_DISTACC_RLT_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />"><input type="hidden" name="<ccs:control name='acc_rlt_name' property='name'/>" value="<ccs:control name='acc_rlt_name'/>" id="BMSDISOBEY_DISTacc_rlt_name_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />"></td> 
      <td style="WIDTH: 50px; WHITE-SPACE: nowrap">
        <ccs:block name='LinkA'><a href="<ccs:control name='LinkA' property='href'/>" class="class-link-detail-A" id="BMSDISOBEY_DISTLinkA_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">表單填寫</a></ccs:block>
        <ccs:block name='LinkB'><a href="<ccs:control name='LinkB' property='href'/>" class="class-link-detail-B" id="BMSDISOBEY_DISTLinkB_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">表單填寫</a></ccs:block>
        <ccs:block name='LinkD'><a href="<ccs:control name='LinkD' property='href'/>" class="class-link-detail-C" id="BMSDISOBEY_DISTLinkD_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">表單填寫</a></ccs:block>&nbsp;<ccs:control name='EXP_TIME'/></td> 
      <td style="WIDTH: 8px; WHITE-SPACE: nowrap">
      	<ccs:block name='LinkW'><a onclick="caseWithdraw('<ccs:control name='CASE_ID'/>','<ccs:control name='ACC_RLT'/>');">抽回</a></ccs:block>
      </td>
    </tr>
 </ccs:row></ccs:repeater>
    <ccs:norecords>
    <tr class="NoRecords">
      <td colspan="6">無任何資料</td> 
    </tr>
 </ccs:norecords>
    <tr class="Footer">
      <td colspan="6">
        <ccs:block name='LinkA_insert'><a href="<ccs:control name='LinkA_insert' property='href'/>" id="BMSDISOBEY_DISTLinkA_insert_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">案件新增</a></ccs:block>
        <ccs:block name='LinkB_insert'><a href="<ccs:control name='LinkB_insert' property='href'/>" id="BMSDISOBEY_DISTLinkB_insert_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">案件新增</a></ccs:block>
        <ccs:block name='LinkD_insert'><a href="<ccs:control name='LinkD_insert' property='href'/>" id="BMSDISOBEY_DISTLinkD_insert_<ccs:attribute owner = 'BMSDISOBEY_DIST' name = 'rowNumber' />">案件新增</a></ccs:block>&nbsp;&nbsp; 
        <ccs:navigator name='Navigator' type='Simple' size='10'><span class="Navigator">
        <ccs:first_on><a href="<ccs:page_href/>"><img alt="<ccs:page_href/>" src="Styles/Blueprint/Images/First.gif"></a> </ccs:first_on>
        <ccs:prev_on><a href="<ccs:page_href/>"><img alt="<ccs:page_href/>" src="Styles/Blueprint/Images/Prev.gif"></a> </ccs:prev_on>第&nbsp;<ccs:page_number/>&nbsp;頁&nbsp;／&nbsp;共&nbsp;<ccs:total_pages/>&nbsp;頁 
        <ccs:next_on><a href="<ccs:page_href/>"><img alt="<ccs:page_href/>" src="Styles/Blueprint/Images/Next.gif"></a> </ccs:next_on>
        <ccs:last_on><a href="<ccs:page_href/>"><img alt="<ccs:page_href/>" src="Styles/Blueprint/Images/Last.gif"></a> </ccs:last_on></span></ccs:navigator>&nbsp;</td> 
    </tr>
 
  </table>
 </ccs:grid>
</div>
</body>
</html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-77CD6A51--%>
<%im10101_lisModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-98BB94AF--%>
<%im10101_lisModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

