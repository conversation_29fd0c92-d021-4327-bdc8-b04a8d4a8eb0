<%--== Handlers ==--%> <%--VerticalMenu Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

//Feature checker Head @1-721E76B3
    public class VerticalMenuServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//VerticalMenu Page Handler Head @1-0C9C1685
    public class VerticalMenuPageHandler implements PageListener {
//End VerticalMenu Page Handler Head

//VerticalMenu BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End VerticalMenu BeforeInitialize Method Head

//VerticalMenu BeforeInitialize Method Tail @1-FCB6E20C
        }
//End VerticalMenu BeforeInitialize Method Tail

//VerticalMenu AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End VerticalMenu AfterInitialize Method Head

//VerticalMenu AfterInitialize Method Tail @1-FCB6E20C
        }
//End VerticalMenu AfterInitialize Method Tail

//VerticalMenu OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End VerticalMenu OnInitializeView Method Head

//VerticalMenu OnInitializeView Method Tail @1-FCB6E20C
        }
//End VerticalMenu OnInitializeView Method Tail

//VerticalMenu BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End VerticalMenu BeforeShow Method Head

//VerticalMenu BeforeShow Method Tail @1-FCB6E20C
        }
//End VerticalMenu BeforeShow Method Tail

//VerticalMenu BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End VerticalMenu BeforeOutput Method Head

//VerticalMenu BeforeOutput Method Tail @1-FCB6E20C
        }
//End VerticalMenu BeforeOutput Method Tail

//VerticalMenu BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End VerticalMenu BeforeUnload Method Head

//VerticalMenu BeforeUnload Method Tail @1-FCB6E20C
        }
//End VerticalMenu BeforeUnload Method Tail

//VerticalMenu onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End VerticalMenu onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//VerticalMenu onCache Method Tail @1-FCB6E20C
        }
//End VerticalMenu onCache Method Tail

//VerticalMenu Page Handler Tail @1-FCB6E20C
    }
//End VerticalMenu Page Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-1DF2F526
    Page VerticalMenuModel = (Page)request.getAttribute("VerticalMenu_page");
    Page VerticalMenuParent = (Page)request.getAttribute("VerticalMenuParent");
    if (VerticalMenuModel == null) {
        PageController VerticalMenuCntr = new PageController(request, response, application, "/Designs/Light/VerticalMenu.xml" );
        VerticalMenuModel = VerticalMenuCntr.getPage();
        //if (VerticalMenuParent != null) {
            //if (!VerticalMenuParent.getChild(VerticalMenuModel.getName()).isVisible()) return;
        //}
        VerticalMenuModel.addPageListener(new VerticalMenuPageHandler());
        if (VerticalMenuParent != null)
            VerticalMenuModel.setAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT, (String)VerticalMenuParent.getRelativePath());
        else
            VerticalMenuModel.setAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT, "../../");
        VerticalMenuCntr.process();
%>
<%
        if (VerticalMenuParent == null) {
            VerticalMenuModel.setCookies();
            if (VerticalMenuModel.redirect()) return;
        } else {
            VerticalMenuModel.redirect();
        }
    }
//End Processing

%>
