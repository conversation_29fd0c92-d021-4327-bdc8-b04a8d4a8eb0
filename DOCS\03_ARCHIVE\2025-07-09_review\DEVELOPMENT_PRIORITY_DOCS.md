# 開發優先文件清單 - 新北市違章建築管理系統

## 🎯 核心目標：支援系統開發、擴增、修正、調整

---

## 🔴 第一優先級：立即需要完善的開發文件

### 1. **CodeCharge Studio 開發指南** ⚡️
**現狀**: CODECHARGE_ANALYSIS.md 只有40%完整
**需要補充**:
- 484個JSP檔案的完整清單和功能對照表
- XML配置與JSP的對應關係圖
- Handler事件處理機制詳解
- 如何新增/修改頁面的標準流程
- CodeCharge自動生成代碼的修改規則

### 2. **資料庫操作手冊** ⚡️
**現狀**: erd_and_schema.md 缺少關鍵表格
**需要補充**:
- `ibmlawfee` 表格完整結構
- 所有Stored Procedures和Triggers
- 資料庫事務處理模式
- DBConn vs DBConn2 使用時機
- SQL查詢優化建議

### 3. **狀態碼開發指南** ⚡️
**現狀**: STATUS_CODE_ENCYCLOPEDIA.md 已修正但缺少開發指引
**需要補充**:
- 如何新增狀態碼的步驟
- 狀態轉換邏輯的實作範例
- 狀態機錯誤處理機制
- 各Handler中的狀態碼使用模式

---

## 🟡 第二優先級：擴增功能必需文件

### 4. **API開發模式文件**
**範例**: case_empty_dis.jsp 風格
**需要建立**:
```javascript
// API端點開發模板
// 1. 事務控制模式
// 2. JSON回應格式
// 3. 錯誤處理標準
// 4. 認證驗證機制
```

### 5. **業務流程擴增指南**
**三大業務線 × 三個階段**:
- 如何新增業務類型（除了一般/廣告/下水道）
- 如何修改階段流程（認定→排拆→結案）
- 分案邏輯客製化方法
- 業務規則引擎架構

### 6. **整合介接開發文件**
**現有整合**:
- GIS整合（ezekArcgisToolBox.js）
- 國土署介接
- 未來擴充點預留設計

---

## 🟢 第三優先級：維護調整支援文件

### 7. **常見問題修正指南**
**包含**:
- Top 20 常見錯誤及解決方案
- 效能瓶頸診斷方法
- 資料庫連線池調整
- Session管理問題處理

### 8. **測試開發指南**
**需要建立**:
- 單元測試框架（針對JSP環境）
- 整合測試腳本
- 資料庫測試資料準備
- 自動化測試流程

---

## 📋 快速開發檢查清單

### 新功能開發前置作業
- [ ] 確認影響的JSP/XML/Handler檔案
- [ ] 檢查相關狀態碼定義
- [ ] 評估資料庫結構變更
- [ ] 確認權限和角色需求

### 修正問題標準流程
- [ ] 定位問題檔案（使用FOSSIL分析）
- [ ] 追蹤資料流（JSP→Handler→DB）
- [ ] 檢查狀態轉換邏輯
- [ ] 驗證多表同步（IBMCASE/IBMFYM/IBMSTS）

### 程式碼修改原則
- [ ] 保持CodeCharge結構完整性
- [ ] 遵循三層架構模式
- [ ] 維護XML與JSP同步
- [ ] 測試所有相關狀態轉換

---

## 🚀 立即行動項目

### 本週必須完成（開發阻塞項）
1. **建立JSP功能對照表**
   - 掃描484個JSP檔案
   - 建立功能分類索引
   - 標記核心業務檔案

2. **完善Handler事件文件**
   - 列出所有*Handlers.jsp
   - 記錄每個事件的觸發條件
   - 提供修改範例

3. **補充缺失的資料表文件**
   - ibmlawfee 完整結構
   - 關聯關係圖
   - 索引優化建議

---

## 💡 開發小抄（Quick Reference）

### 新增頁面
```bash
1. 建立 xxx_man.jsp (UI層)
2. 建立 xxx_man.xml (配置層)  
3. 建立 xxx_Handlers.jsp (邏輯層)
4. 在 web.xml 註冊（如需要）
```

### 修改業務流程
```java
// 1. 找到對應的Handler
// 2. 定位狀態轉換邏輯
// 3. 修改條件判斷
// 4. 更新IBMSTS和IBMFYM
```

### 資料庫操作
```java
// 使用DBTools工具類
String result = DBTools.dLookUp(...);
DBTools.executeUpdate(...);
// 記得處理事務
```

---

## 📌 重要提醒

1. **不要過度文件化** - 只建立開發真正需要的文件
2. **以範例為主** - 提供可複製的程式碼範例
3. **快速索引優先** - 讓開發者快速找到需要的資訊
4. **持續更新** - 隨著開發進展更新文件

---

*最後更新: 2024-01-07*
*目標: 支援快速開發、減少學習曲線、提高維護效率*