<%--== Handlers ==--%> <%--BlockTemplate Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

//Feature checker Head @1-7828FDAF
    public class BlockTemplateService<PERSON><PERSON><PERSON> implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//BlockTemplate Page Handler Head @1-8A5705B4
    public class BlockTemplatePageHandler implements PageListener {
//End BlockTemplate Page Handler Head

//BlockTemplate BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End BlockTemplate BeforeInitialize Method Head

//BlockTemplate BeforeInitialize Method Tail @1-FCB6E20C
        }
//End BlockTemplate BeforeInitialize Method Tail

//BlockTemplate AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End BlockTemplate AfterInitialize Method Head

//BlockTemplate AfterInitialize Method Tail @1-FCB6E20C
        }
//End BlockTemplate AfterInitialize Method Tail

//BlockTemplate OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End BlockTemplate OnInitializeView Method Head

//BlockTemplate OnInitializeView Method Tail @1-FCB6E20C
        }
//End BlockTemplate OnInitializeView Method Tail

//BlockTemplate BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End BlockTemplate BeforeShow Method Head

//BlockTemplate BeforeShow Method Tail @1-FCB6E20C
        }
//End BlockTemplate BeforeShow Method Tail

//BlockTemplate BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End BlockTemplate BeforeOutput Method Head

//BlockTemplate BeforeOutput Method Tail @1-FCB6E20C
        }
//End BlockTemplate BeforeOutput Method Tail

//BlockTemplate BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End BlockTemplate BeforeUnload Method Head

//BlockTemplate BeforeUnload Method Tail @1-FCB6E20C
        }
//End BlockTemplate BeforeUnload Method Tail

//BlockTemplate onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End BlockTemplate onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//BlockTemplate onCache Method Tail @1-FCB6E20C
        }
//End BlockTemplate onCache Method Tail

//BlockTemplate Page Handler Tail @1-FCB6E20C
    }
//End BlockTemplate Page Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-14F97947
    Page BlockTemplateModel = (Page)request.getAttribute("BlockTemplate_page");
    Page BlockTemplateParent = (Page)request.getAttribute("BlockTemplateParent");
    if (BlockTemplateModel == null) {
        PageController BlockTemplateCntr = new PageController(request, response, application, "/Designs/Light/BlockTemplate.xml" );
        BlockTemplateModel = BlockTemplateCntr.getPage();
        //if (BlockTemplateParent != null) {
            //if (!BlockTemplateParent.getChild(BlockTemplateModel.getName()).isVisible()) return;
        //}
        BlockTemplateModel.addPageListener(new BlockTemplatePageHandler());
        if (BlockTemplateParent != null)
            BlockTemplateModel.setAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT, (String)BlockTemplateParent.getRelativePath());
        else
            BlockTemplateModel.setAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT, "../../");
        BlockTemplateCntr.process();
%>
<%
        if (BlockTemplateParent == null) {
            BlockTemplateModel.setCookies();
            if (BlockTemplateModel.redirect()) return;
        } else {
            BlockTemplateModel.redirect();
        }
    }
//End Processing

%>
