# SQL Server GIS Database Backup Script for BMS System
# Author: System Administrator
# Purpose: Automated SQL Server backup for ramsGIS database

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("full", "differential", "log")]
    [string]$BackupType = "full",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\backup_config.json",
    
    [Parameter(Mandatory=$false)]
    [switch]$Compress = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verify = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$CrossRegionReplicate = $false
)

# Import required modules
Import-Module -Name ".\modules\BackupLogger.psm1" -Force
Import-Module -Name ".\modules\BackupMetrics.psm1" -Force
Import-Module -Name ".\modules\BackupNotification.psm1" -Force
Import-Module -Name "SqlServer" -Force

# Global variables
$script:BackupStartTime = Get-Date
$script:BackupMetrics = @{}
$script:Logger = $null

# Initialize logging
function Initialize-BackupLogging {
    param(
        [string]$LogPath = ".\logs\sqlserver_backup_$(Get-Date -Format 'yyyyMMdd').log"
    )
    
    $script:Logger = New-BackupLogger -LogPath $LogPath -LogLevel "INFO"
    $script:Logger.Info("SQL Server backup script started - Type: $BackupType")
}

# Load configuration
function Get-BackupConfiguration {
    param(
        [string]$ConfigFile
    )
    
    if (!(Test-Path $ConfigFile)) {
        throw "Configuration file not found: $ConfigFile"
    }
    
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $script:Logger.Info("Configuration loaded successfully")
        return $config
    }
    catch {
        throw "Failed to load configuration: $_"
    }
}

# Test SQL Server connection
function Test-SqlServerConnection {
    param(
        [hashtable]$DbConfig
    )
    
    $script:Logger.Info("Testing SQL Server connection...")
    
    try {
        $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=$($DbConfig.Database);User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT @@VERSION"
        $result = $command.ExecuteScalar()
        
        $connection.Close()
        
        $script:Logger.Info("SQL Server connection successful")
        $script:Logger.Info("Server version: $($result.ToString().Split("`n")[0])")
        
        return $true
    }
    catch {
        $script:Logger.Error("SQL Server connection failed: $_")
        throw
    }
}

# Create backup directory structure
function Initialize-BackupDirectories {
    param(
        [string]$BackupBasePath
    )
    
    $directories = @(
        "$BackupBasePath\sqlserver\full",
        "$BackupBasePath\sqlserver\differential",
        "$BackupBasePath\sqlserver\log",
        "$BackupBasePath\sqlserver\metadata",
        "$BackupBasePath\sqlserver\compressed",
        "$BackupBasePath\sqlserver\cross_region"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            $script:Logger.Info("Created backup directory: $dir")
        }
    }
}

# Get database file information
function Get-DatabaseFileInfo {
    param(
        [string]$ConnectionString,
        [string]$DatabaseName
    )
    
    $query = @"
SELECT 
    name,
    physical_name,
    type_desc,
    size * 8 / 1024 AS size_mb,
    growth,
    is_percent_growth
FROM sys.database_files
"@
    
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $ConnectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $query
        $command.CommandTimeout = 60
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset)
        
        $connection.Close()
        
        return $dataset.Tables[0]
    }
    catch {
        throw "Failed to get database file information: $_"
    }
}

# Perform full backup
function Invoke-FullBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting full backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\sqlserver\full\ramsGIS_full_$timestamp.bak"
    
    $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=$($DbConfig.Database);User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
    
    try {
        $startTime = Get-Date
        
        # Get database file info before backup
        $fileInfo = Get-DatabaseFileInfo -ConnectionString $connectionString -DatabaseName $DbConfig.Database
        
        # Build backup command
        $backupCommand = @"
BACKUP DATABASE [$($DbConfig.Database)] 
TO DISK = N'$backupFile'
WITH FORMAT, 
     INIT,
     NAME = N'ramsGIS-Full Database Backup',
     SKIP,
     NOREWIND,
     NOUNLOAD,
     COMPRESSION,
     STATS = 10,
     CHECKSUM
"@
        
        # Execute backup
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $backupCommand
        $command.CommandTimeout = 3600  # 1 hour timeout
        
        # Add progress tracking
        $command.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $connection.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $script:Logger.Info("Executing full backup...")
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        # Verify backup file exists
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Full backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["full_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "file_path" = $backupFile
                "timestamp" = $timestamp
                "database_files" = $fileInfo
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Full backup failed: $_")
        throw
    }
}

# Perform differential backup
function Invoke-DifferentialBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting differential backup...")
    
    # Check if there's a recent full backup
    $fullBackupPath = "$BackupPath\sqlserver\full"
    $latestFullBackup = Get-ChildItem -Path $fullBackupPath -Filter "*.bak" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if (!$latestFullBackup) {
        throw "No full backup found. Differential backup requires a full backup first."
    }
    
    $script:Logger.Info("Base full backup: $($latestFullBackup.Name)")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\sqlserver\differential\ramsGIS_diff_$timestamp.bak"
    
    $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=$($DbConfig.Database);User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
    
    try {
        $startTime = Get-Date
        
        # Build differential backup command
        $backupCommand = @"
BACKUP DATABASE [$($DbConfig.Database)] 
TO DISK = N'$backupFile'
WITH DIFFERENTIAL,
     FORMAT,
     INIT,
     NAME = N'ramsGIS-Differential Database Backup',
     SKIP,
     NOREWIND,
     NOUNLOAD,
     COMPRESSION,
     STATS = 10,
     CHECKSUM
"@
        
        # Execute backup
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $backupCommand
        $command.CommandTimeout = 3600
        
        # Add progress tracking
        $command.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $connection.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $script:Logger.Info("Executing differential backup...")
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        # Verify backup file exists
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Differential backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["differential_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "file_path" = $backupFile
                "base_backup" = $latestFullBackup.Name
                "timestamp" = $timestamp
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Differential backup failed: $_")
        throw
    }
}

# Perform transaction log backup
function Invoke-LogBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting transaction log backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\sqlserver\log\ramsGIS_log_$timestamp.trn"
    
    $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=$($DbConfig.Database);User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
    
    try {
        $startTime = Get-Date
        
        # Check if database is in full recovery mode
        $recoveryModeQuery = "SELECT recovery_model_desc FROM sys.databases WHERE name = '$($DbConfig.Database)'"
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $recoveryModeQuery
        $recoveryMode = $command.ExecuteScalar()
        
        if ($recoveryMode -ne "FULL") {
            $script:Logger.Warning("Database is not in FULL recovery mode. Current mode: $recoveryMode")
            $script:Logger.Warning("Transaction log backup may not be available")
        }
        
        # Build transaction log backup command
        $backupCommand = @"
BACKUP LOG [$($DbConfig.Database)] 
TO DISK = N'$backupFile'
WITH FORMAT,
     INIT,
     NAME = N'ramsGIS-Transaction Log Backup',
     SKIP,
     NOREWIND,
     NOUNLOAD,
     COMPRESSION,
     STATS = 10,
     CHECKSUM
"@
        
        $command.CommandText = $backupCommand
        $command.CommandTimeout = 1800  # 30 minutes timeout
        
        # Add progress tracking
        $command.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $connection.add_InfoMessage({
            param($sender, $e)
            $script:Logger.Info("SQL Server: $($e.Message)")
        })
        
        $script:Logger.Info("Executing transaction log backup...")
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        # Verify backup file exists
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Transaction log backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["log_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "file_path" = $backupFile
                "recovery_mode" = $recoveryMode
                "timestamp" = $timestamp
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Transaction log backup failed: $_")
        throw
    }
}

# Verify backup integrity
function Test-BackupIntegrity {
    param(
        [string]$BackupFile,
        [hashtable]$DbConfig
    )
    
    $script:Logger.Info("Verifying backup integrity: $BackupFile")
    
    $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=master;User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
    
    try {
        # Use RESTORE VERIFYONLY to check backup integrity
        $verifyCommand = @"
RESTORE VERIFYONLY 
FROM DISK = N'$BackupFile'
WITH CHECKSUM
"@
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $verifyCommand
        $command.CommandTimeout = 1800
        
        # Add message handling
        $verificationMessages = @()
        $command.add_InfoMessage({
            param($sender, $e)
            $message = $e.Message
            $verificationMessages += $message
            $script:Logger.Info("SQL Server Verify: $message")
        })
        
        $connection.add_InfoMessage({
            param($sender, $e)
            $message = $e.Message
            $verificationMessages += $message
            $script:Logger.Info("SQL Server Verify: $message")
        })
        
        $script:Logger.Info("Executing backup verification...")
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        # Check for successful verification
        $successMessage = $verificationMessages | Where-Object { $_ -match "successfully processed" }
        
        if ($successMessage) {
            $script:Logger.Info("Backup integrity verification passed")
            
            # Store verification metrics
            $script:BackupMetrics["verification"] = @{
                "success" = $true
                "verification_messages" = $verificationMessages
            }
            
            return $true
        } else {
            throw "Backup verification did not complete successfully"
        }
    }
    catch {
        $script:Logger.Error("Backup integrity verification failed: $_")
        $script:BackupMetrics["verification"] = @{
            "success" = $false
            "error" = $_.Exception.Message
        }
        throw
    }
}

# Get backup header information
function Get-BackupHeaderInfo {
    param(
        [string]$BackupFile,
        [hashtable]$DbConfig
    )
    
    $script:Logger.Info("Reading backup header information...")
    
    $connectionString = "Server=$($DbConfig.Server),$($DbConfig.Port);Database=master;User ID=$($DbConfig.Username);Password=$($DbConfig.Password);TrustServerCertificate=True;"
    
    try {
        $headerQuery = @"
RESTORE HEADERONLY 
FROM DISK = N'$BackupFile'
"@
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $headerQuery
        $command.CommandTimeout = 300
        
        $adapter = New-Object System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object System.Data.DataSet
        $adapter.Fill($dataset)
        
        $connection.Close()
        
        if ($dataset.Tables[0].Rows.Count -gt 0) {
            $headerInfo = @{}
            $row = $dataset.Tables[0].Rows[0]
            
            foreach ($column in $dataset.Tables[0].Columns) {
                $headerInfo[$column.ColumnName] = $row[$column.ColumnName]
            }
            
            $script:Logger.Info("Backup header information retrieved successfully")
            return $headerInfo
        } else {
            throw "No backup header information found"
        }
    }
    catch {
        $script:Logger.Error("Failed to read backup header: $_")
        throw
    }
}

# Compress backup file using SQL Server compression or external tools
function Compress-BackupFile {
    param(
        [string]$BackupFile,
        [string]$CompressedPath
    )
    
    # SQL Server backup already includes compression, but we can add additional compression
    $script:Logger.Info("Applying additional compression to backup file...")
    
    $fileName = [System.IO.Path]::GetFileNameWithoutExtension($BackupFile)
    $compressedFile = "$CompressedPath\$fileName.zip"
    
    try {
        $startTime = Get-Date
        
        # Use 7-Zip for better compression if available
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", $compressedFile, $BackupFile) -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path $BackupFile -DestinationPath $compressedFile -CompressionLevel Optimal
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        $originalSize = (Get-Item $BackupFile).Length
        $compressedSize = (Get-Item $compressedFile).Length
        $compressionRatio = [math]::Round((1 - ($compressedSize / $originalSize)) * 100, 2)
        
        $script:Logger.Info("Additional compression completed in $([math]::Round($duration, 2)) minutes")
        $script:Logger.Info("Original size: $([math]::Round($originalSize / 1MB, 2)) MB")
        $script:Logger.Info("Compressed size: $([math]::Round($compressedSize / 1MB, 2)) MB")
        $script:Logger.Info("Compression ratio: $compressionRatio%")
        
        # Store compression metrics
        $script:BackupMetrics["compression"] = @{
            "success" = $true
            "duration_minutes" = $duration
            "original_size_mb" = [math]::Round($originalSize / 1MB, 2)
            "compressed_size_mb" = [math]::Round($compressedSize / 1MB, 2)
            "compression_ratio" = $compressionRatio
            "compressed_file" = $compressedFile
        }
        
        return $compressedFile
    }
    catch {
        $script:Logger.Error("Compression failed: $_")
        throw
    }
}

# Cleanup old backups
function Remove-OldBackups {
    param(
        [string]$BackupPath,
        [int]$RetentionDays
    )
    
    $script:Logger.Info("Cleaning up SQL Server backups older than $RetentionDays days...")
    
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $deletedCount = 0
    $freedSpace = 0
    
    $backupTypes = @("full", "differential", "log", "compressed")
    
    foreach ($type in $backupTypes) {
        $typePath = "$BackupPath\sqlserver\$type"
        
        if (Test-Path $typePath) {
            $oldFiles = Get-ChildItem -Path $typePath -Recurse -File | Where-Object { $_.LastWriteTime -lt $cutoffDate }
            
            foreach ($file in $oldFiles) {
                try {
                    $fileSize = $file.Length
                    Remove-Item -Path $file.FullName -Force
                    $deletedCount++
                    $freedSpace += $fileSize
                    $script:Logger.Info("Deleted old backup: $($file.Name)")
                }
                catch {
                    $script:Logger.Warning("Failed to delete file: $($file.FullName) - $_")
                }
            }
        }
    }
    
    $script:Logger.Info("Cleanup completed: $deletedCount files deleted, $([math]::Round($freedSpace / 1MB, 2)) MB freed")
}

# Generate backup report
function New-BackupReport {
    param(
        [string]$ReportPath
    )
    
    $script:Logger.Info("Generating SQL Server backup report...")
    
    $report = @{
        "backup_summary" = @{
            "start_time" = $script:BackupStartTime
            "end_time" = Get-Date
            "duration_minutes" = ((Get-Date) - $script:BackupStartTime).TotalMinutes
            "backup_type" = $BackupType
            "database_name" = "ramsGIS"
            "overall_success" = $true
        }
        "metrics" = $script:BackupMetrics
        "system_info" = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "sqlserver_version" = Get-SqlServerVersion
            "disk_space" = Get-DiskSpace
        }
    }
    
    # Check overall success
    foreach ($metric in $script:BackupMetrics.Values) {
        if ($metric.success -eq $false) {
            $report.backup_summary.overall_success = $false
            break
        }
    }
    
    # Save report as JSON
    $reportJson = $report | ConvertTo-Json -Depth 10
    $reportFile = "$ReportPath\sqlserver_backup_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8
    
    $script:Logger.Info("SQL Server backup report saved: $reportFile")
    
    return $report
}

# Get SQL Server version information
function Get-SqlServerVersion {
    try {
        $version = (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction SilentlyContinue).MSSQLSERVER
        if ($version) {
            return (Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\$version\Setup" -ErrorAction SilentlyContinue).Version
        }
        return "Unknown"
    }
    catch {
        return "Unknown"
    }
}

# Get disk space information
function Get-DiskSpace {
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    $driveInfo = @()
    foreach ($drive in $drives) {
        $driveInfo += @{
            "drive" = $drive.DeviceID
            "total_size_gb" = [math]::Round($drive.Size / 1GB, 2)
            "free_space_gb" = [math]::Round($drive.FreeSpace / 1GB, 2)
            "used_space_gb" = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
            "free_space_percent" = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        }
    }
    
    return $driveInfo
}

# Main execution
try {
    # Initialize logging
    Initialize-BackupLogging
    
    # Load configuration
    $config = Get-BackupConfiguration -ConfigFile $ConfigFile
    
    # Test database connection
    Test-SqlServerConnection -DbConfig $config.sqlserver
    
    # Initialize backup directories
    Initialize-BackupDirectories -BackupBasePath $config.backup.base_path
    
    # Execute backup based on type
    $backupFile = $null
    
    switch ($BackupType) {
        "full" {
            $backupFile = Invoke-FullBackup -DbConfig $config.sqlserver -BackupPath $config.backup.base_path
        }
        "differential" {
            $backupFile = Invoke-DifferentialBackup -DbConfig $config.sqlserver -BackupPath $config.backup.base_path
        }
        "log" {
            $backupFile = Invoke-LogBackup -DbConfig $config.sqlserver -BackupPath $config.backup.base_path
        }
    }
    
    # Get backup header information
    if ($backupFile) {
        $headerInfo = Get-BackupHeaderInfo -BackupFile $backupFile -DbConfig $config.sqlserver
        $script:BackupMetrics["backup_header"] = $headerInfo
    }
    
    # Compress backup if requested and file exists
    if ($Compress -and $backupFile -and (Test-Path $backupFile)) {
        $compressedFile = Compress-BackupFile -BackupFile $backupFile -CompressedPath "$($config.backup.base_path)\sqlserver\compressed"
        
        # Remove original uncompressed file
        Remove-Item -Path $backupFile -Force
        $backupFile = $compressedFile
    }
    
    # Verify backup integrity if requested
    if ($Verify -and $backupFile) {
        # For compressed files, we need to extract first for verification
        if ($backupFile.EndsWith(".zip")) {
            $tempDir = "$($config.backup.base_path)\sqlserver\temp"
            if (!(Test-Path $tempDir)) {
                New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
            }
            
            Expand-Archive -Path $backupFile -DestinationPath $tempDir -Force
            $extractedFile = Get-ChildItem -Path $tempDir -Filter "*.bak" | Select-Object -First 1
            
            if ($extractedFile) {
                Test-BackupIntegrity -BackupFile $extractedFile.FullName -DbConfig $config.sqlserver
                Remove-Item -Path $tempDir -Recurse -Force
            }
        } else {
            Test-BackupIntegrity -BackupFile $backupFile -DbConfig $config.sqlserver
        }
    }
    
    # Cross-region replication if requested
    if ($CrossRegionReplicate -and $backupFile) {
        Invoke-CrossRegionReplication -BackupFile $backupFile -ReplicationConfig $config.cross_region_replication
    }
    
    # Cleanup old backups
    Remove-OldBackups -BackupPath $config.backup.base_path -RetentionDays $config.backup.retention_days
    
    # Generate backup report
    $report = New-BackupReport -ReportPath "$($config.backup.base_path)\sqlserver\metadata"
    
    # Send notifications
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report $report
    }
    
    $script:Logger.Info("SQL Server backup completed successfully")
    
    # Set exit code based on overall success
    if ($report.backup_summary.overall_success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    $script:Logger.Error("SQL Server backup script failed: $_")
    
    # Send failure notification
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report @{
            "backup_summary" = @{
                "overall_success" = $false
                "error_message" = $_.Exception.Message
                "backup_type" = $BackupType
                "database_name" = "ramsGIS"
            }
        }
    }
    
    exit 1
}
finally {
    if ($script:Logger) {
        $script:Logger.Close()
    }
}