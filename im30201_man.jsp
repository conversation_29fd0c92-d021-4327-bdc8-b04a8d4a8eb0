<%--JSP Page Init @1-3D59AC7C--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im30201_manServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-AC99DF3D--%>
<%@include file="im30201_manHandlers.jsp"%>
<%
    if (!im30201_manModel.isVisible()) return;
    if (im30201_manParent != null) {
        if (!im30201_manParent.getChild(im30201_manModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im30201_manModel);
    pageContext.setAttribute("page", im30201_manModel);
    im30201_manModel.fireOnInitializeViewEvent(new Event());
    im30201_manModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im30201_manModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im30201_manModel.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-135CBB06--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>空拍照片綁定作業</title>
<link rel="stylesheet" type="text/css" href="javascript/bootstrap3.4.1/css/bootstrap.min.css">
<link rel="stylesheet" href="//js.arcgis.com/3.24/esri/css/esri.css">
<link rel="stylesheet" type="text/css" href="in_recordgridstyle.css">
<script src="javascript/jquery-2.1.1.min.js" type="text/javascript"></script>
<script language="JavaScript" src="javascript/jquery.blockUI.min.js" type="text/javascript"></script>
<script src="javascript/ezek/tgos/Framework.js" type="text/javascript"></script>
<script src="javascript/ezek/tgos/AjaxAgent.js" type="text/javascript"></script>
<script src="javascript/bootstrap3.4.1/js/bootstrap.min.js"></script>
<script language="JavaScript" src="javascript/jquery.fancybox.js?v=2.1.5" type="text/javascript"></script>
<script src="//js.arcgis.com/3.24/"></script>
<script>
var uav_tycgisMap, dom_id,prdct_yy ;
var tiledMap = null,TgosLayer_F2 = null;
var sys_arcgisMapServiceURL = location.protocol+"//limit.ntpc.gov.tw/arcgis/rest/services/";
var token = "17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
var mapServerUrlArray = [sys_arcgisMapServiceURL + "rams_main/MapServer", "rm_getMapLayer.jsp", "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer?token="+token, 
"https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer?token="+token,
sys_arcgisMapServiceURL + "rams_main/FeatureServer", 
sys_arcgisMapServiceURL + "bcmsMap_I30/MapServer",
"https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer?token="+token,
"https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer?token="+token];
var normalTileName2 = "新北市政府電子地圖(比例尺1/500)";
var normalTileName1 = "新北市政府電子地圖";
var uav_ntpcinMaps_URL = [],   uav_ntpcinMaps_NM = [], uav_ntpcinMaps_Layer =[];

   window.onload = function (){
   	
  // 	  dom_id = $("[name='dom_id']").val().trim();   
//	   prdct_yy = $("[name='prdct_yy']").val().trim();   
   //uav_tycgisMap = "https://icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_"+prdct_yy+"/MapServer" ;
   uav_tycgisMap = "https://icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_110/MapServer" ; 
   
   var UAVYY = $("input[name='UAVYY']").val();
   var UAVYY_Array = UAVYY.split(",");
   for(var i=0; i <UAVYY_Array.length; i++ ){
     uav_ntpcinMaps_URL.push("https://icdc.ntpc.gov.tw/arcgis/rest/services/uav_ntpcinMap_"+UAVYY_Array[i]+"/MapServer");
     uav_ntpcinMaps_NM.push(UAVYY_Array[i]);
   }
  // ezek_InitMap();  
   
   
   
        ezek_InitMap();
        iniList();
   }  
   
   var temp_date = Date.now();
  
    
var ezekMap, currentCoordinate = [];
var _lng , _lat;
var gsvc, wgs84SR, twd97SR, webMercatorSR;
var  markerPic, savePoint, landNumMarker, areaMarker;
var UAVMap ;
function ezek_InitMap() {
        require([
                 "esri/map", 
                "esri/dijit/Print",             
                "esri/geometry/Point", 
                "esri/geometry/webMercatorUtils", 
                "esri/graphic", 
                "esri/symbols/PictureMarkerSymbol", 
                "esri/symbols/SimpleMarkerSymbol",
                "esri/toolbars/edit", 
                "esri/dijit/Scalebar",
                'esri/tasks/PrintTemplate',
                "esri/SpatialReference",
                "esri/tasks/GeometryService",
                "dojo/_base/event",
                "dojo/dom", 
                "dojo/on", 
                "dojo/parser",
                "dojo/domReady!"
        ], function(Map,Print, Point
                , WebMercatorUtils, Graphic, PictureMarkerSymbol, SimpleMarkerSymbol , Edit,Scalebar,PrintTemplate
               /* , PrintParameters, PrintTask, lang*/
                ,SpatialReference ,GeometryService , event, dom, on, parser) {
               parser.parse();
                                
                        var WGS84_point = new Point();
                        //markerPic = new PictureMarkerSymbol('http://building.tycg.gov.tw/tycgim/img/focus.png?1071030', 30, 30);
                        markerPic = new esri.symbol.SimpleMarkerSymbol(esri.symbol.SimpleMarkerSymbol.STYLE_CIRCLE, 12,null, new dojo.Color([255, 0, 0, 1]));
                        areaMarker = new esri.symbol.SimpleFillSymbol(esri.symbol.SimpleFillSymbol.STYLE_SOLID, new esri.symbol.SimpleLineSymbol(esri.symbol.SimpleLineSymbol.STYLE_SOLID, new dojo.Color([255,0,0,0.8]), 3),new dojo.Color([125,125,125,0.35]));
                        esriConfig.defaults.io.proxyUrl = "/remoteArcGISProxy/proxy.jsp";
                
                        //設定座標轉換服務
                        
                        var sys_arcgisMapService = location.protocol+"//icdc.ntpc.gov.tw/arcgis/rest/services/";
                        
                        gsvc = new GeometryService(sys_arcgisMapService + "Utilities/Geometry/GeometryServer");
                        wgs84SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '4326' />);            //WGS84
                        twd97SR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102443' />);          //TWD_97_TM2
                        webMercatorSR = new SpatialReference(<ccs:attribute owner = 'wkid' name = '102100' />);    //WGS 1984 Web Mercator Projection
                        
                ezekMap = new Map("map_canvas", {
                            logo: false,
                            slider: true,
                            sliderStyle: "small",
                            sliderPosition: "top-left",
                            spatialReference: {
                                    wkid: 102443
                            },
                     zoom: 10,
                     minZoom:2, 
                        maxZoom: 13

                    });
                  
			//把 ajax sync變為同步，防止亂插隊的情形發生
          // $.ajaxSettings.async = false;
                        var map_index = 0;
                        tiledMap = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[7], {
							"opacity": 1,
							id: normalTileName2
						});
				        ezekMap.addLayer(tiledMap, 0);
					
						
						// 1: 1/1000以上 電子地圖
						TgosLayer_F2 = new esri.layers.ArcGISTiledMapServiceLayer(mapServerUrlArray[6], {
							"opacity": 1,
							id: normalTileName1
						});
						ezekMap.addLayer(TgosLayer_F2, 1);
                        map_index = 2;
                      
                       // UAVMap = new esri.layers.ArcGISDynamicMapServiceLayer(uav_tycgisMap , { id: "UAVMap" });
                       // tempMap.setVisibleLayers([0,1]);
                       // ezekMap.addLayer(UAVMap, 2);
                 
                  for(var uav_i = 0 ; uav_i < uav_ntpcinMaps_URL.length ; uav_i++){
                  	uav_ntpcinMaps_Layer.push( new esri.layers.ArcGISDynamicMapServiceLayer(uav_ntpcinMaps_URL[uav_i] , { id: ("UAVMap_"+uav_ntpcinMaps_NM[uav_i]) }));
                  }
                   for(var uav_i = 0 ; uav_i < uav_ntpcinMaps_Layer.length ; uav_i++){
                     ezekMap.addLayer(uav_ntpcinMaps_Layer[uav_i]  ,  map_index++);
                   }
                  	
                  	
				// $.ajaxSettings.async = true;
   

                on(ezekMap, "load", function() {
                      //REG_YY
                      var _name = dom_id + ".tif";
                      console.log(ezekMap);
                     // zoomToLayer_UAV(_name , year)
                      
                });
               
                 //加入比例尺顯示
                                var scalebar = new Scalebar({
                                        map: ezekMap,
                                        scalebarUnit: "metric"
                                });
                                esri.config.defaults.map.slider = {
                                        right: "165px",
                                        bottom: null,
                                        width: "200px",
                                        height: null
                                };
                                $(".esriScalebar").css("bottom", "50px").css("z-index", "50").css("position", "relative");       
                       
        });//END  require
}//END ezek_InitMap()


 //------------------------------------------------------------------------
        // 縮放置圖層
        //------------------------------------------------------------------------
        function zoomToLayer_UAV(_name, _year ) {
        	 showLoading();
		// console.log("~~zoomToLayer_UAV~~" + _name);
			var mapIndex = uav_ntpcinMaps_NM.indexOf(_year)
        
        
       // console.log("~~mapIndex~~" + mapIndex);
       // console.log( uav_ntpcinMaps_Layer);
            var layerDefinitions = [];
            layerDefinitions[0] = "Name = '"+_name+"'";
       		//UAVMap.setLayerDefinitions(layerDefinitions);
       		uav_ntpcinMaps_Layer[mapIndex].setLayerDefinitions(layerDefinitions);
               // ezekMap.graphics.clear();
            var query = new esri.tasks.Query();
            //var queryTask =  new esri.tasks.QueryTask(uav_tycgisMap+"/0");
            var queryTask =  new esri.tasks.QueryTask(uav_ntpcinMaps_URL[mapIndex]+"/0");
            query.returnGeometry = true;
            query.where = "Name='" +_name + "' "; 
        	queryTask.execute(query, addrQueryResults);
        }
        //------------------------------------------------------------------------
        // 縮放置圖層 結果
        //------------------------------------------------------------------------
        function addrQueryResults(featureSet) {
   
                ezekMap.graphics.clear();
                //啟動單次查詢
                
                if(featureSet.features.length == 0){
                        //alertify.alert('查詢結果', '查無此地號', function(){ }).set('labels', '確認');
                        //goAlertify( '查詢結果', '查無此地號','確認' );
                        //showSearchErr(2 ,"&nbsp;&nbsp;&nbsp;&nbsp;查無此地號");
                }else{
                        dojo.forEach(featureSet.features, function(feature) {
                                
                                landNumMarker = feature;
                                //landNumMarker.setSymbol(areaMarker);
                                //
                                if (feature.geometry.getExtent().spatialReference.wkid == null || feature.geometry.getExtent().spatialReference.wkid != ezekMap.spatialReference.wkid) {
                                        landNumMarker.geometry.setSpatialReference(twd97SR);
                                        coordToMapSR_geometry(landNumMarker.geometry, function(outGeometry) {

                                                                                         
                                                //landNumMarker.setGeometry(outGeometry);
                                                //ezekMap.graphics.add(landNumMarker);
                                                //地圖zoom至該feature
                                                ezekMap.setExtent(landNumMarker.geometry.getExtent(), true);
                                        });
                                } else {
                                        ezekMap.graphics.add(landNumMarker);
                                        //地圖zoom至該feature
                                        ezekMap.setExtent(landNumMarker.geometry.getExtent(), true);
                                        
                                }       
                        });
                        
                }
                closeLoading();
        
        
        }

function zoomToCenter(lng,lat) 
{
        require([
                "esri/geometry/Point",
                "esri/symbols/PictureMarkerSymbol"
        ], function(Point,PictureMarkerSymbol) {
                
                var _CallBack_Zoom = function(outputpoint) 
                {
                       // var point = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        savePoint = new esri.geometry.Point(outputpoint.x, outputpoint.y,new esri.SpatialReference({wkid: 102443}));
                        //console.log(outputpoint.x, outputpoint.y);
                        //picLocMarker = new PictureMarkerSymbol('img/searchLocation.png', 20, 20);
                        ezekMap.graphics.add(new esri.Graphic(savePoint, markerPic));
                       
                        ezekMap.centerAndZoom(savePoint, 10); 
                     
                };
                convertLatLng(lat,lng,4326,102443,_CallBack_Zoom);
        });
}
//------------------------------------------------------------------------
// 將geometry物件轉為底圖使用的座標系統
//   MEMO: 使用後端arcGIS Server的GeometryService
//------------------------------------------------------------------------
function coordToMapSR_geometry(inGeometry, callback) {
        require(["esri/geometry/Geometry", "esri/tasks/ProjectParameters", "esri/tasks/GeometryService"], function(Geometry, ProjectParameters, GeometryService) {
                var prjParams = new ProjectParameters();
                prjParams.geometries = [inGeometry];
                prjParams.outSR = ezekMap.spatialReference;
                gsvc.project(prjParams, function (et) {
                        callback(et[0]);
                }, showErr);
        });
} 

function convertLatLng(_lat,_lng,_incoord,_outcoord,_callback) 
{
        require([
                "esri/tasks/ProjectParameters",
                "esri/symbols/PictureMarkerSymbol"
        ],function(ProjectParameters,PictureMarkerSymbol) { 

                var inlat = _lat;
                var inlon = _lng;
                var incoord = _incoord;
                var outcoord = _outcoord;

                if (isNaN(inlat) || isNaN(inlon)) {
                        //alert("Please enter valid numbers");
                } else {
                        var inSR = new esri.SpatialReference({
                                wkid: incoord
                        });
                        var outSR = new esri.SpatialReference({
                                wkid: outcoord
                        });
                        var geometryService = new esri.tasks.GeometryService("https://utility.arcgisonline.com/ArcGIS/rest/services/Geometry/GeometryServer");
                        var inputpoint = new esri.geometry.Point(inlon, inlat, inSR);
                        var PrjParams = new esri.tasks.ProjectParameters();
                        PrjParams.geometries = [inputpoint];
                        PrjParams.outSR = outSR;

                        geometryService.project(PrjParams, function (outputpoint) {
                                // console.log('Conversion completed. Input SR: ' + incoord + '. Output SR: ' + outcoord);
                                _callback(outputpoint[0]);
                        });
                }
        });
}


function showLoading(){
        $.ajaxSettings.async = false;                   
        $.blockUI({message: '<img src="img/busy.gif" style="vertical-align:middle;"/>&nbsp;&nbsp;請稍候...', css:{border:'none', padding:'6px', backgroundColor:'#000', '-webkit-border-radius': '10px', '-moz-border-radius': '10px', opacity: .5, color:'#FFF'}});
        $.ajaxSettings.async = true;
}

function closeLoading(){
	     setTimeout(() => { $.unblockUI(); }, 500);
        //$.unblockUI(); 
        //setTimeout( function(){ $.unblockUI(); }, 1500);
}
        
        

   
   // 定位 
  function iniClick(){
  $(".findUAV").click(function(){
  	//console.log("~~iniClick~~");
        var mapName = $(this).attr("name"), dataYear = $(this).attr("data-year");
        
        zoomToLayer_UAV(mapName,dataYear );
        
  });
  }
  
    // 修改為  一案對一空拍圖
  function iniChkClick(){
  $(".updCB").click(function(){
  	 $(".updCB").not(this).attr("checked", false);
  });
  }
  
   
       
function iniList(){
  var _data = getUAV_Jeson();
  var CNT = "<tr style='background-color:#f0f8ff;'><td class='uavLisTt'><label>轉檔時間</label></td><td class='uavLisTt'><label>空拍年度</label></td><td class='uavLisTt'><label>專案名稱</label></td>";
CNT +="<td></td><td><button id='bindUAV' type='button' class='btn btn-primary' onclick='updgogo();'>綁定</button></td></tr>";

var _reg_num = $("input[name=reg_num]").val() ;
//console.log(_data);
        if(_data && _data.result && _data.result =="1"){
            if( _data.uavList && _data.uavList.length > 0 ){
                var listData = _data.uavList;
                listData.forEach(function(elmt) {
                  CNT +=  buildDataTR( elmt.SCD_TIME, elmt.DOM_NAME, elmt.PRDCT_YY, elmt.CASE_ID,elmt.DOM_ID  );
                });
            }else{
                  console.log(" iniList() err + ", _data);
            }
        }else{
          CNT = "<tr><td><label>無任何資料</label></td></tr>"
        }
$("#ListTable").html(CNT);
$("#reg_toto").html( "認定通知號碼：" + _reg_num);

iniClick();
iniChkClick();
}

        function getUAV_Jeson() {       
            var case_showtype;
         	var UAVPRJNM = $("input[name='UAVPRJNM']").val();
            $.ajaxSettings.async = false;
                $.post("im30201_getUAVJeson.jsp", 
                    {
                        FILENAME: '00',
                        UAVPRJNM: UAVPRJNM                                           
                    }, function(jData) {
                        case_showtype = jData;
                    }, "json"
                ).always(function() {
                        
                });
            $.ajaxSettings.async = true;
            return case_showtype;
        }
		//function buildDataTR( elmt.SCD_TIME, elmt.DOM_NAME, elmt.PRDCT_YY, elmt.CASE_ID );
        function buildDataTR( v_SCD_TIME,v_DOM_NAME, v_PRDCT_YY, v_CASE_ID, v_DOM_ID ){
            var case_id = $("input[name='case_id']").val();
			var _timeFormate = "", _TF_STATE_NM="";
			if(v_SCD_TIME) _timeFormate = TF_timeFormate(v_SCD_TIME);
       	 	resultQ = "<tr class='tr_color'>";
	        resultQ = " <td style= 'text-align:center;'><label>" + _timeFormate + "</label></td>";
	        resultQ += "<td><label>" + v_PRDCT_YY + "年</label></td>";
            resultQ += "<td><label>" + v_DOM_NAME + "</label></td>";
            resultQ += "<td><img src= 'img/icon_loc.png' name = '"+v_DOM_ID+".tif' data-year='"+v_PRDCT_YY+"' class='findUAV' ></td>";
            // resultQ += "<td style= 'text-align:center;'> <input class='updCB' type='checkbox' name='"+v_BMS_KEY+"' value='"+v_ORTHO_NAME+"'> </td>";
            if(v_CASE_ID.indexOf(case_id ) != -1){
            	resultQ += "<td style= 'text-align:center;'> <input class='updCB' type='checkbox' name='"+v_DOM_ID+"' value='"+v_DOM_ID+"' checked> </td>";
            }else{
            	resultQ += "<td style= 'text-align:center;'> <input class='updCB' type='checkbox' name='"+v_DOM_ID+"' value='"+v_DOM_ID+"'> </td>";
            }
            resultQ += "</tr>";
            return resultQ;
        }
        
        // UAV table 日期轉換
        function TF_timeFormate( _TF ){
                var AA = "";
                if(_TF.length == 13){
                	AA = _TF.substring(0,3) + "/" + _TF.substring(3,5)  + "/" + _TF.substring(5,7) +  "<br>"+  _TF.substring(7,9)+  ":" + _TF.substring(9,11) ;
                }
                return AA;
        }
        
// 綁定案件
function updgogo(){
	 showLoading();
        var case_NM = "", checkUPD = 0, _first=1, checkAlert = 0;
        $(".updCB").each(function (){
                //var name = $(this).attr("name");

                if($(this).prop('checked')){
                        var name_c = $(this).attr("name");
                        var _key = $(this).val();
                        
                        if(_first){
                                _first =0;
                        }else{
                                case_NM +=";"
                        }
                        case_NM += _key;
                        /*
                        if(name_c){
                                checkAlert =1;
                        }else{
                                checkUPD = 1;
                        }
                        */
                        checkUPD = 1;
                }

        });
/*
        if( checkAlert ){
                var err = "是否更新已綁定案件";
                var r=confirm(err)
                if (r==true){
                        checkUPD = 1;
                }else {
                        checkUPD = 0;
                }
        }*/
        if( checkUPD ){
                var case_id = $("input[name='case_id']").val();
                
                var case_showtype;
                $.ajaxSettings.async = false;
                $.ajax({
                        type: "POST",
                        url: "im30201_updateUAV.jsp",
                        data: {  
                                v_KIND :'from_CASE',
                                v_CASE_ID:case_id.trim(),
                                v_CASE :case_NM,
                                random:Math.floor(Math.random()*1000000)
                        }
                }).done(function(o) {
        			iniList();
        			closeLoading();
                    alert(o.MSG);
                }).always(function( qq ) { 
        
                    
                                 
                });
                $.ajaxSettings.async = true;

        }
}
        // 返回
        function closeFancybox() {   parent.$.fancybox.close();   }
</script>
<style type="text/css">
html body table{margin:0;}
.esriSimpleSlider { left: 10px; top: 10px; }
#map_canvas { width: 560px; height: 640px; border: 2px solid #bbb; }
.dijitButton .dijitButtonNode, #drawingWrapper, #printButton { width: 70px;}
.esriPrint {padding: 0;}
#infoBoxSwitch { left:19px; top:90px; position:absolute; z-index:10; }
#infoBoxSwitch:hover img { opacity: 0.7;}
#infoBoxSwitch2 { left:19px; top:124px; position:absolute; z-index:10; }
#infoBoxSwitch2:hover img { opacity: 0.7;}   
body{font-size: 16px; font-family: Arial, 黑體-繁, "Heiti TC", 微軟正黑體, "Microsoft JhengHei", sans-serif; }
.divShowLatLng{float:left;margin-top: -6px;font-size:14px;}
.findUAV{cursor:pointer;}
.uavLisTt{text-align:center;}
.list-div{padding:4px;}
</style>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//End CCS script
</script>
</head>
<body>
<ccs:record name='uavdom'>
<form id="uavdom" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
  <table class="table" style="MARGIN-BOTTOM: 0px" cellspacing="0" cellpadding="0">
    <ccs:error_block>
    <tr id="uavdomErrorBlock" class="Error">
      <td colspan="2"><ccs:error_text/></td> 
    </tr>
 </ccs:error_block>
    <tr class="Controls">
      <td style="VERTICAL-ALIGN: top">
        <div class="list-div">
          <label id="reg_toto">&nbsp;</label> 
          <div>
            <label for="uavdomUAVPRJNM">　　專案名稱：</label><input type="text" name="<ccs:control name='UAVPRJNM' property='name'/>" value="<ccs:control name='UAVPRJNM'/>" id="uavdomUAVPRJNM">&nbsp;&nbsp;<label id="illSearch" onclick="iniList();" class="btn btn-primary">搜&nbsp;&nbsp;&nbsp;&nbsp;尋</label>
          </div>
 
        </div>
 
        <div class="list-div" style="overflow-y: auto;height: 600px;">
          <table id="ListTable" class="table" style="MARGIN-BOTTOM: 0px">
          </table>
 
        </div>
 </td> 
      <td style="VERTICAL-ALIGN: top">
        <div>
          <div id="map_canvas">
          </div>
 
        </div>
 </td> 
      <tr class="Bottom">
        <td style="TEXT-ALIGN: right" colspan="2"><input type="hidden" id="uavdomcase_id" value="<ccs:control name='case_id'/>" name="<ccs:control name='case_id' property='name'/>"><input type="hidden" id="uavdomreg_num" value="<ccs:control name='reg_num'/>" name="<ccs:control name='reg_num' property='name'/>"><input type="hidden" name="<ccs:control name='UAVYY' property='name'/>" value="<ccs:control name='UAVYY'/>" id="uavdomUAVYY">
          <ccs:button name='Button_Cancel'><input type="submit" onclick="closeFancybox();" id="uavdomButton_Cancel" class="btn btn-success" alt="返&nbsp;&nbsp;&nbsp;&nbsp;回" value="返&nbsp;&nbsp;&nbsp;&nbsp;回" name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button><input type="hidden" id="uavdomEXP_NO" value="<ccs:control name='EXP_NO'/>" name="<ccs:control name='EXP_NO' property='name'/>"><input type="hidden" id="uavdomlng" value="<ccs:control name='lng'/>" name="<ccs:control name='lng' property='name'/>"><input type="hidden" id="uavdomlat" value="<ccs:control name='lat'/>" name="<ccs:control name='lat' property='name'/>"></td> 
      </tr>
 
    </table>
 
  </form>
 </ccs:record>
  </body>
 
  </html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-C7E398C4--%>
<%im30201_manModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-2895663A--%>
<%im30201_manModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

