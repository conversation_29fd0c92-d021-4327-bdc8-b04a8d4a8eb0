# 新北市違章建築管理系統 - 系統架構總覽

## 目錄
1. [系統概述](#系統概述)
2. [技術架構](#技術架構)
3. [應用架構](#應用架構)
4. [資料架構](#資料架構)
5. [業務架構](#業務架構)
6. [部署架構](#部署架構)
7. [安全架構](#安全架構)
8. [整合架構](#整合架構)
9. [系統規模](#系統規模)
10. [架構決策記錄](#架構決策記錄)

## 系統概述

### 系統定位
新北市違章建築管理系統是一個支撐城市管理的核心業務系統，處理違章建築從發現、認定、拆除到結案的完整生命週期。

### 核心價值
- **業務支撐**：管理超過37萬個違建案件
- **流程管控**：支援複雜的多階段、多部門協同作業
- **決策支援**：提供各類統計報表與GIS地圖整合

### 技術特徵
- **開發工具**：CodeCharge Studio（已停止維護的RAD工具）
- **架構模式**：傳統三層式架構（JSP + XML + Handlers）
- **技術年代**：基於2001年的Web Application 2.3規範

## 技術架構

### 技術棧全景

```
┌─────────────────────────────────────────────────────────┐
│                    前端層 (Frontend)                      │
├─────────────────────────────────────────────────────────┤
│ HTML/CSS │ Bootstrap 5.3.3 │ jQuery 3.7.1 │ Custom JS  │
├─────────────────────────────────────────────────────────┤
│                    表現層 (Presentation)                  │
├─────────────────────────────────────────────────────────┤
│         JSP Pages (484個) + CodeCharge Tags              │
├─────────────────────────────────────────────────────────┤
│                    業務邏輯層 (Business)                  │
├─────────────────────────────────────────────────────────┤
│    JSP Handlers + Java Classes + XML Configuration      │
├─────────────────────────────────────────────────────────┤
│                    資料存取層 (Data Access)               │
├─────────────────────────────────────────────────────────┤
│  JDBC │ DBConnectionManager │ PostgreSQL │ SQL Server   │
└─────────────────────────────────────────────────────────┘
```

### CodeCharge Studio 架構模式

#### 1. 標準三檔案模式
```
功能模組/
├── *_man.jsp      # 表現層：UI呈現
├── *_man.xml      # 配置層：資料綁定、驗證規則
└── *_Handlers.jsp # 邏輯層：業務處理
```

#### 2. 自訂API模式
```java
// 範例：case_empty_dis.jsp
<%@ page contentType="application/json" %>
<%
    // 純後端API，無UI元件
    // 手動事務控制
    // 輸出JSON響應
%>
```

### 核心技術元件

| 類別 | 技術 | 版本 | 用途 |
|------|------|------|------|
| 容器 | Apache Tomcat | 9.0.98 | Servlet容器 |
| 框架 | CodeCharge | Custom | RAD開發框架 |
| 前端 | Bootstrap | 5.3.3 | UI框架 |
| 前端 | jQuery | 3.7.1 | DOM操作 |
| 資料庫 | PostgreSQL | - | 主資料庫 |
| 資料庫 | SQL Server | - | GIS資料庫 |
| 報表 | JasperReports | 5.0.1 | PDF報表 |
| 排程 | Quartz | 2.3.2 | 定時任務 |

## 應用架構

### 請求處理流程

```
使用者請求
    ↓
Tomcat容器
    ↓
Filter Chain
├── ContentSecurityPolicyFilter (安全標頭)
├── PanelFilter (面板處理)
└── IncludeFilter (檔案包含)
    ↓
JSP處理引擎
    ↓
┌─────────────────┐
│ 1. 載入XML配置  │
│ 2. 初始化Bean   │
│ 3. 執行Handler  │
│ 4. 渲染頁面     │
└─────────────────┘
    ↓
響應輸出
```

### 模組架構

```
系統功能模組
├── 掛號通報 (im101xx系列)
│   ├── 案件登錄
│   ├── 查報人管理
│   └── 資料驗證
├── 現勘管理 (im102xx系列)
│   ├── 勘查排程
│   ├── 結果登錄
│   └── 照片上傳
├── 認定處理 (im103xx系列)
│   ├── 違建認定
│   ├── 審核流程
│   └── 公文產生
├── 排拆作業 (im104xx系列)
│   ├── 拆除通知
│   ├── 排程管理
│   └── 執行記錄
└── 系統管理 (im999xx系列)
    ├── 使用者管理
    ├── 權限設定
    └── 參數維護
```

### 檔案組織結構

```
webapps/
├── WEB-INF/
│   ├── web.xml          # Web應用配置
│   ├── site.properties  # 系統設定
│   ├── lib/            # JAR依賴(50個)
│   ├── java/           # Java原始碼
│   └── tld/            # 標籤庫定義
├── css/                # 樣式檔案
├── js/                 # JavaScript(233個)
├── images/             # 圖片資源
├── reports/            # JasperReports模板
└── [業務JSP檔案]      # 484個JSP頁面
```

## 資料架構

### 資料庫系統

#### 1. 主資料庫（PostgreSQL）
- **連線**：localhost:5432/bms
- **編碼**：UTF-8
- **連線池**：最大80個連線

#### 2. GIS資料庫（SQL Server）
- **連線**：**************:2433/ramsGIS
- **用途**：地理資訊整合
- **連線池**：最大100個連線

### 核心資料表架構

```
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│  buildcase  │────→│   tbflow     │────→│  caselawfee │
│  (案件主表)  │     │  (流程記錄)   │     │  (規費記錄)  │
└─────────────┘     └──────────────┘     └─────────────┘
       │                    │                      │
       ↓                    ↓                      ↓
┌─────────────┐     ┌──────────────┐     ┌─────────────┐
│  casefile   │     │  workflowlog │     │  ibmcode    │
│  (附件管理)  │     │  (操作日誌)   │     │  (代碼表)    │
└─────────────┘     └──────────────┘     └─────────────┘
```

### 關鍵資料表說明

| 資料表 | 記錄數 | 用途 | 關鍵欄位 |
|--------|--------|------|----------|
| buildcase | 371,081 | 案件主檔 | case_no, caseopened |
| tbflow | 1,004,853 | 流程歷程 | case_no, case_state |
| casefile | 1,028,463 | 檔案附件 | case_no, file_type |
| caselawfee | 252,685 | 規費記錄 | case_no, fee_amount |
| ibmcode | 2,107 | 系統代碼 | code_type, code_seq |

### IBMCODE代碼系統

系統使用78種代碼類型管理各類參數：

```
關鍵代碼類型：
- RLT: 狀態碼定義（違建處理階段）
- BTP: 建物類型
- RCD: 行政區代碼
- IFT: 檢查項目類型
- USG: 使用分區
```

## 業務架構

### 三迷宮業務體系

```
                    違章建築管理系統
                          │
      ┌───────────────────┼───────────────────┐
      │                   │                   │
  一般違建迷宮          廣告違建迷宮        下水道違建迷宮
   (2xx系列)            (3xx系列)           (5xx系列)
      │                   │                   │
  ┌───┼───┐          ┌───┼───┐          ┌───┼───┐
  │   │   │          │   │   │          │   │   │
認定 排拆 結案      認定 排拆 結案      認定 排拆 結案
```

### 業務流程階段

#### 第一階段：掛號通報（初始化）
1. 案件登錄與編號
2. 查報人員指派
3. 基本資料建檔

#### 第二階段：現場勘查（調查）
1. 現場實地勘查
2. 違建事實確認
3. 證據資料收集

#### 第三階段：認定審核（判定）
1. 違建類型認定
2. 處理方式決定
3. 行政程序啟動

#### 第四階段：排拆執行（處置）
1. 拆除通知發送
2. 自行拆除期限
3. 強制拆除排程

#### 第五階段：結案歸檔（完結）
1. 拆除完成確認
2. 結案條件檢核
3. 案件歸檔封存

### 狀態碼體系

```
狀態碼規則：XYZ
- X: 業務類型（2=一般, 3=廣告, 5=下水道）
- Y: 處理階段（0-9）
- Z: 細部狀態（0-f）

範例：
- 211: 一般違建現勘中
- 234: 一般違建協同處理
- 311: 廣告違建現勘中
- 511: 下水道違建現勘中
```

## 部署架構

### 系統部署圖

```
┌─────────────────────────────────────┐
│         負載平衡器 (可選)             │
└────────────────┬────────────────────┘
                 │
┌────────────────┴────────────────────┐
│        Apache Tomcat 9.0.98         │
│            (應用伺服器)              │
├─────────────────────────────────────┤
│  - 記憶體: -Xmx2048m               │
│  - 連接埠: 8080                    │
│  - 編碼: UTF-8                     │
└────────────┬───────────┬───────────┘
             │           │
    ┌────────┴───────┐ ┌─┴──────────┐
    │  PostgreSQL    │ │ SQL Server │
    │  (主資料庫)     │ │ (GIS資料)  │
    └────────────────┘ └────────────┘
```

### 環境配置

| 環境 | 用途 | 配置差異 |
|------|------|----------|
| 開發環境 | 功能開發 | 除錯模式開啟 |
| 測試環境 | 功能測試 | 模擬生產資料 |
| 生產環境 | 正式運行 | 效能優化配置 |

## 安全架構

### 安全機制

#### 1. 認證授權
- Session-based認證
- 角色權限控制（RBAC）
- 功能級別權限檢查

#### 2. 安全過濾器
```java
ContentSecurityPolicyFilter:
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
```

#### 3. 已知安全議題
- ⚠️ 資料庫密碼硬編碼
- ⚠️ 明文FTP配置
- ⚠️ 舊版依賴套件

### 安全建議
1. 密碼外部化配置
2. 升級過時依賴
3. 實施HTTPS
4. 定期安全掃描

## 整合架構

### 外部系統整合

```
違建管理系統
    │
    ├── GIS系統整合
    │   └── ArcGIS地圖服務
    │
    ├── 公文系統整合
    │   └── 電子公文交換
    │
    ├── 財務系統整合
    │   └── 規費收納管理
    │
    └── 報表系統整合
        └── JasperReports
```

### 整合方式
- **資料庫整合**：共用資料表或視圖
- **API整合**：RESTful服務調用
- **檔案整合**：批次檔案交換
- **訊息整合**：非同步訊息佇列

## 系統規模

### 數據規模統計

| 指標 | 數量 | 說明 |
|------|------|------|
| 案件總數 | 371,081 | 歷年累積案件 |
| 流程記錄 | 1,004,853 | 案件處理歷程 |
| 附件檔案 | 1,028,463 | 照片與文件 |
| 使用者數 | ~500 | 系統使用者 |
| 日處理量 | ~1,000 | 每日新增與更新 |

### 程式碼規模

| 類型 | 數量 | 說明 |
|------|------|------|
| JSP檔案 | 484 | 頁面與API |
| XML配置 | 195 | CodeCharge配置 |
| JavaScript | 233 | 前端邏輯 |
| Java類別 | ~100 | 後端元件 |
| 資料表 | 50+ | 資料庫物件 |

## 架構決策記錄

### ADR-001: 採用CodeCharge Studio
- **決策**：使用CodeCharge Studio作為RAD工具
- **原因**：快速開發、降低技術門檻
- **後果**：技術債務累積、維護困難

### ADR-002: 狀態碼設計
- **決策**：採用3位數編碼系統
- **原因**：業務複雜度要求精細控制
- **後果**：狀態多達100+種，理解困難

### ADR-003: 雙資料庫架構
- **決策**：PostgreSQL主庫 + SQL Server GIS庫
- **原因**：歷史系統整合需求
- **後果**：增加維護複雜度

### ADR-004: 前端補償策略
- **決策**：大量JavaScript補償後端不足
- **原因**：CodeCharge功能限制
- **後果**：前後端職責混亂

## 架構改進建議

### 短期改進（3-6個月）
1. **安全加固**
   - 外部化敏感配置
   - 升級安全依賴
   - 實施HTTPS

2. **效能優化**
   - 資料庫索引優化
   - 查詢效能調校
   - 快取機制實施

### 中期改進（6-12個月）
1. **技術升級**
   - Servlet規範升級
   - 依賴套件更新
   - 前端框架現代化

2. **架構重構**
   - API化改造
   - 業務邏輯抽離
   - 資料存取層統一

### 長期改進（1-2年）
1. **系統重建**
   - 微服務架構
   - 前後端分離
   - 雲原生部署

2. **資料治理**
   - 資料倉儲建設
   - 主數據管理
   - 資料品質提升

## 結論

新北市違章建築管理系統雖基於過時技術，但透過精心設計的業務架構和完善的流程控制，成功支撐了城市違建管理的核心業務。系統展現了高度的業務適配性和運行穩定性，但也累積了相當的技術債務。

### 核心優勢
- ✅ 業務覆蓋完整
- ✅ 流程控制嚴謹
- ✅ 資料累積豐富
- ✅ 系統運行穩定

### 主要挑戰
- ❌ 技術棧過時
- ❌ 維護成本高
- ❌ 安全風險存在
- ❌ 擴展性受限

透過漸進式的架構改進和技術升級，可在保持業務連續性的同時，逐步實現系統的現代化轉型。