# 協同作業機制分析 (Collaboration Mechanism Analysis)

## 概述 (Overview)
本文檔詳細分析新北市違章建築管理系統中的「協同作業」(Collaboration) 機制，該機制用於處理跨部門協作的違建認定案件。

## 協同作業狀態碼 (Collaboration Status Codes)

### 主要協同狀態碼
根據資料庫查詢結果，系統定義了三個協同作業狀態碼：

```sql
-- 查詢結果
code_type | code_seq |       code_desc        
-----------+----------+------------------------
 RLT       | 234      | [一　般]認定送協同作業
 RLT       | 244      | [廣告物]認定送協同作業
 RLT       | 254      | [下水道]認定送協同作業
```

### 狀態碼與部門對應關係
從 `im10201_lisHandlers.jsp` 的程式碼分析：

```java
// 認定科          234
// 勞安科(下水道)  254
// 廣拆科          244
String ACC_RLT_FOR_AFFIRM = "'234'";  // 認定科
String ACC_RLT_FOR_OHS = "'254'";     // 勞安科（處理下水道違建）
String ACC_RLT_FOR_ADS = "'244'";     // 廣拆科（處理廣告物違建）
```

## 協同作業流程 (Collaboration Workflow)

### 1. 觸發協同作業
在 `im10101_man_A.jsp`（一般違建認定）頁面中，提供了「送協同作業」按鈕：

```html
<ccs:button name='Button_Synergy'>
  <input type="submit" onclick="setState('synergy');" 
         id="BMSDISOBEY_DISTButton_Synergy" 
         class="btn btn-warning" 
         alt="送協同作業" 
         value="送協同作業" 
         name="<ccs:control name='Button_Synergy' property='name'/>">
</ccs:button>
```

### 2. 狀態更新邏輯
從 `im10101_man_AHandlers.jsp` 的分析，當用戶點擊「送協同作業」按鈕時：

```java
if("synergy".equals(SUBMIT_STATE)){
    acc_rlt = "234";  // 設定為一般違建協同作業狀態
}

// 插入歷程記錄
INSERT_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE) ";
INSERT_SQL += " VALUES('" + CASE_ID + "', " + currentDate + ", " + currentTime + 
              ", '" + jobTitle + "', '" + acc_rlt + "', '" + op_user + "', " + currentDate + ") ";

// 更新案件狀態
UPDATE_SQL = "UPDATE IBMSTS SET ACC_DATE = "+currentDate+",ACC_TIME = "+currentTime+
             ", ACC_JOB = '"+jobTitle+"',ACC_RLT = '"+acc_rlt+"' WHERE CASE_ID = '"+CASE_ID+"'";
```

### 3. 協同作業清單管理
`im10201_lis.jsp` 提供了「認定協同作業」管理介面：

```html
<div class="up_titte">
  <span class="tittle_span">&nbsp;&nbsp;&nbsp;</span> 
  <span class="tittle_label">&nbsp;認定協同作業</span> 
</div>
```

該頁面顯示所有待協同作業的案件，並根據用戶權限過濾可見案件。

## 權限控制機制 (Permission Control)

### 1. 單位權限過濾
從 `im10201_lisHandlers.jsp` 的查詢邏輯：

```java
if (viewAllCases(USER_ID)) {
    // 管理者或督導可看所有協同案件
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_AFFIRM)
       .append(", ").append(ACC_RLT_FOR_OHS)
       .append(", ").append(ACC_RLT_FOR_ADS).append(")");
} else if (UNIT_ID.startsWith("01")) { // 認定科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_AFFIRM).append(")");
    sql.append(" AND reg_unit = '").append(UNIT_ID).append("'");
} else if(UNIT_ID.startsWith("03")) { // 勞安科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_OHS).append(")");
} else if (UNIT_ID.startsWith("04")) { // 廣拆科
    sql.append(" AND acc_rlt IN (").append(ACC_RLT_FOR_ADS).append(")");
} else{ // 其他單位: not allow to view any records
    sql.append(" AND 1 = 2");
}
```

### 2. 角色權限檢查
```java
private boolean viewAllCases(String empno) {
    boolean flag = false;
    int cnt = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "ibmuser", 
              "empno = '" + empno + "' AND ((role_id = 'sysManager' OR role_id_2 = 'sysManager' OR role_id_3 = 'sysManager') " +
              "OR (role_id = 'supervisor' OR role_id_2 = 'supervisor' OR role_id_3 = 'supervisor'))", 
              CONNECTION_NAME)).intValue();
    if (cnt > 0) {
        flag = true;
    }
    return flag;
}
```

## 協同作業後續流程

### 1. 案件編輯限制
當案件進入協同作業狀態後，原承辦人無法編輯：

```java
// 認定協同與陳核狀態碼
String UNEDITABLE_ACC_RLT = "234,244,254,232,252";

// Not allow to edit when status is 認定協同 or 陳核
if (UNEDITABLE_ACC_RLT.indexOf(ACC_RLT) > -1) {
    e.getGrid().getControl("LinkA").setVisible(false);
    e.getGrid().getControl("LinkB").setVisible(false);
}
```

### 2. 刪除限制
```java
String ACC_RLT = Utils.convertToString(e.getRecord().getControl("ACC_RLT").getValue());
if("231".equals(ACC_RLT) || "23b".equals(ACC_RLT)){
    //can delete
}
else{
    e.getRecord().addError("此案件已送協同作業/陳核, 不允許刪除.");
}
```

### 3. 後續可能的狀態
從資料庫查詢，協同作業後可能的狀態包括：
- `237`: [一　般]認定退回補正
- `257`: [下水道]認定退回補正

## 技術架構特點

### 1. 狀態機制
- 使用 `IBMSTS` 表記錄當前狀態
- 使用 `IBMFYM` 表記錄狀態變更歷程
- 狀態碼儲存在 `acc_rlt` 欄位

### 2. 跨部門協作設計
- 不同部門有專屬的協同狀態碼
- 根據違建類型自動分派到對應部門
- 權限系統確保部門只能看到相關案件

### 3. 工作流程特性
- 單向流程：進入協同後無法直接退回
- 必須經過協同部門處理後才能繼續
- 保留完整的操作歷程記錄

## 系統整合點

### 1. 相關模組
- `im10101`: 違建認定作業（觸發協同）
- `im10201`: 認定協同作業（處理協同）
- `im10301`: 可能是協同後的下一步處理

### 2. 資料表關聯
- `IBMCASE`: 案件主檔
- `IBMSTS`: 案件狀態檔
- `IBMFYM`: 狀態變更歷程檔
- `IBMCODE`: 代碼檔（狀態碼定義）
- `IBMUSER`: 使用者檔（權限控制）

## 建議與發現

### 1. 優點
- 清晰的部門職責劃分
- 完整的歷程記錄機制
- 嚴格的權限控制

### 2. 潛在改進空間
- 缺少協同作業的時限控制
- 沒有協同案件的優先順序機制
- 可考慮加入協同作業的意見交換功能
- 建議增加協同進度追蹤機制

### 3. 安全性考量
- SQL 查詢使用字串串接，存在 SQL Injection 風險
- 建議改用 PreparedStatement 進行參數化查詢

## 結論
系統的協同作業機制實現了跨部門的案件協作處理，透過狀態碼控制和權限管理，確保案件在不同部門間有序流轉。整體設計符合政府部門的作業需求，但在安全性和功能擴展性上仍有改進空間。