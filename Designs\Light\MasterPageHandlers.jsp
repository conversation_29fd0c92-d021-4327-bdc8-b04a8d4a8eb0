<%--== Handlers ==--%> <%--MasterPage Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

//Feature checker Head @1-DD5AB00D
    public class MasterPageServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//MasterPage Page Handler Head @1-2E0766D0
    public class MasterPagePageHandler implements PageListener {
//End MasterPage Page Handler Head

//MasterPage BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End MasterPage BeforeInitialize Method Head

//MasterPage BeforeInitialize Method Tail @1-FCB6E20C
        }
//End MasterPage BeforeInitialize Method Tail

//MasterPage AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End MasterPage AfterInitialize Method Head

//MasterPage AfterInitialize Method Tail @1-FCB6E20C
        }
//End MasterPage AfterInitialize Method Tail

//MasterPage OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End MasterPage OnInitializeView Method Head

//MasterPage OnInitializeView Method Tail @1-FCB6E20C
        }
//End MasterPage OnInitializeView Method Tail

//MasterPage BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End MasterPage BeforeShow Method Head

//MasterPage BeforeShow Method Tail @1-FCB6E20C
        }
//End MasterPage BeforeShow Method Tail

//MasterPage BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End MasterPage BeforeOutput Method Head

//MasterPage BeforeOutput Method Tail @1-FCB6E20C
        }
//End MasterPage BeforeOutput Method Tail

//MasterPage BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End MasterPage BeforeUnload Method Head

//MasterPage BeforeUnload Method Tail @1-FCB6E20C
        }
//End MasterPage BeforeUnload Method Tail

//MasterPage onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End MasterPage onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//MasterPage onCache Method Tail @1-FCB6E20C
        }
//End MasterPage onCache Method Tail

//MasterPage Page Handler Tail @1-FCB6E20C
    }
//End MasterPage Page Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-24D7A2B2
    Page MasterPageModel = (Page)request.getAttribute("MasterPage_page");
    Page MasterPageParent = (Page)request.getAttribute("MasterPageParent");
    if (MasterPageModel == null) {
        PageController MasterPageCntr = new PageController(request, response, application, "/Designs/Light/MasterPage.xml" );
        MasterPageModel = MasterPageCntr.getPage();
        MasterPageModel.setRelativePath("../../");
        //if (MasterPageParent != null) {
            //if (!MasterPageParent.getChild(MasterPageModel.getName()).isVisible()) return;
        //}
        MasterPageModel.addPageListener(new MasterPagePageHandler());
        MasterPageCntr.process();
%>
<%
        if (MasterPageParent == null) {
            MasterPageModel.setCookies();
            if (MasterPageModel.redirect()) return;
        } else {
            MasterPageModel.redirect();
        }
    }
//End Processing

%>
