# Recovery Testing Automation Script for BMS System
# Author: System Administrator
# Purpose: Automated testing of backup and recovery procedures

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("database_restore", "application_restore", "full_recovery", "scheduled_test")]
    [string]$TestType = "full_recovery",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\dr_config.json",
    
    [Parameter(Mandatory=$false)]
    [string]$TestEnvironment = "test",
    
    [Parameter(Mandatory=$false)]
    [switch]$IsolatedNetwork = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanupAfterTest = $true
)

# Import required modules
Import-Module -Name ".\modules\BackupLogger.psm1" -Force
Import-Module -Name ".\modules\BackupMetrics.psm1" -Force
Import-Module -Name ".\modules\BackupNotification.psm1" -Force

# Global variables
$script:TestStartTime = Get-Date
$script:TestMetrics = @{}
$script:Logger = $null
$script:TestResults = @()
$script:TestEnvironmentInfo = @{}

# Initialize logging
function Initialize-TestLogging {
    param(
        [string]$LogPath = ".\logs\recovery_testing_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
    )
    
    $script:Logger = New-BackupLogger -LogPath $LogPath -LogLevel "INFO"
    $script:Logger.Info("Recovery testing script started - Test Type: $TestType")
}

# Load configuration
function Get-TestConfiguration {
    param(
        [string]$ConfigFile
    )
    
    if (!(Test-Path $ConfigFile)) {
        throw "Test configuration file not found: $ConfigFile"
    }
    
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $script:Logger.Info("Test configuration loaded successfully")
        return $config
    }
    catch {
        throw "Failed to load test configuration: $_"
    }
}

# Initialize test environment
function Initialize-TestEnvironment {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Initializing test environment...")
    
    try {
        # Create test directories
        $testDirs = @(
            "$($Config.recovery.staging_path)\test",
            "$($Config.recovery.temp_path)\test",
            "$($Config.recovery.log_path)\test"
        )
        
        foreach ($dir in $testDirs) {
            if (!(Test-Path $dir)) {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
            }
        }
        
        # Set test environment variables
        $env:BMS_TEST_MODE = "true"
        $env:BMS_TEST_ENVIRONMENT = $TestEnvironment
        $env:BMS_TEST_TIMESTAMP = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
        
        # Record environment info
        $script:TestEnvironmentInfo = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "test_environment" = $TestEnvironment
            "isolated_network" = $IsolatedNetwork
            "start_time" = $script:TestStartTime
            "disk_space_before" = Get-DiskSpace
            "memory_before" = Get-MemoryUsage
        }
        
        $script:Logger.Info("Test environment initialized successfully")
        
        return $true
    }
    catch {
        $script:Logger.Error("Failed to initialize test environment: $_")
        throw
    }
}

# Test database restore
function Test-DatabaseRestore {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Starting database restore test...")
    
    $testResult = @{
        "test_name" = "database_restore"
        "start_time" = Get-Date
        "status" = "running"
        "sub_tests" = @()
    }
    
    try {
        # Test PostgreSQL restore
        $pgTest = Test-PostgreSQLRestore -Config $Config
        $testResult.sub_tests += $pgTest
        
        # Test SQL Server restore
        $sqlTest = Test-SqlServerRestore -Config $Config
        $testResult.sub_tests += $sqlTest
        
        # Determine overall result
        $allSuccess = $testResult.sub_tests | ForEach-Object { $_.success } | Where-Object { $_ -eq $false }
        $testResult.success = $allSuccess.Count -eq 0
        $testResult.status = if ($testResult.success) { "passed" } else { "failed" }
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        $script:Logger.Info("Database restore test completed - Status: $($testResult.status)")
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("Database restore test failed: $_")
        return $testResult
    }
}

# Test PostgreSQL restore
function Test-PostgreSQLRestore {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Testing PostgreSQL restore...")
    
    $testResult = @{
        "test_name" = "postgresql_restore"
        "start_time" = Get-Date
        "status" = "running"
        "success" = $false
    }
    
    try {
        # Find latest backup
        $backupPath = "$($Config.backup.base_path)\full"
        $latestBackup = Get-ChildItem -Path $backupPath -Filter "*.sql*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if (!$latestBackup) {
            throw "No PostgreSQL backup found for testing"
        }
        
        $testResult.backup_file = $latestBackup.FullName
        $testResult.backup_size_mb = [math]::Round($latestBackup.Length / 1MB, 2)
        $testResult.backup_age_hours = [math]::Round(((Get-Date) - $latestBackup.LastWriteTime).TotalHours, 2)
        
        # Create test database
        $testDbName = "bms_test_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $testResult.test_database = $testDbName
        
        $env:PGPASSWORD = $Config.databases.postgresql.primary.password
        
        # Create test database
        $createDbCommand = "& `"$($Config.databases.postgresql.primary.pg_path)\psql.exe`" -h $($Config.databases.postgresql.primary.host) -p $($Config.databases.postgresql.primary.port) -U $($Config.databases.postgresql.primary.username) -d postgres -c `"CREATE DATABASE $testDbName;`""
        
        $script:Logger.Info("Creating test database: $testDbName")
        Invoke-Expression $createDbCommand
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create test database"
        }
        
        # Restore backup to test database
        $restoreStartTime = Get-Date
        $restoreCommand = "& `"$($Config.databases.postgresql.primary.pg_path)\psql.exe`" -h $($Config.databases.postgresql.primary.host) -p $($Config.databases.postgresql.primary.port) -U $($Config.databases.postgresql.primary.username) -d $testDbName -f `"$($latestBackup.FullName)`""
        
        $script:Logger.Info("Restoring backup to test database...")
        Invoke-Expression $restoreCommand
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to restore backup to test database"
        }
        
        $restoreEndTime = Get-Date
        $testResult.restore_duration_minutes = ($restoreEndTime - $restoreStartTime).TotalMinutes
        
        # Verify restore
        $verifyTests = @(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';",
            "SELECT COUNT(*) FROM buildcase;",
            "SELECT COUNT(*) FROM tbflow;",
            "SELECT COUNT(*) FROM ibmcode;"
        )
        
        $verifyResults = @()
        foreach ($query in $verifyTests) {
            try {
                $result = & "$($Config.databases.postgresql.primary.pg_path)\psql.exe" -h $Config.databases.postgresql.primary.host -p $Config.databases.postgresql.primary.port -U $Config.databases.postgresql.primary.username -d $testDbName -c $query -t -A
                $verifyResults += @{
                    "query" = $query
                    "result" = $result.Trim()
                    "success" = $LASTEXITCODE -eq 0
                }
            }
            catch {
                $verifyResults += @{
                    "query" = $query
                    "error" = $_.Exception.Message
                    "success" = $false
                }
            }
        }
        
        $testResult.verification_results = $verifyResults
        
        # Check if all verifications passed
        $allVerificationsPassed = ($verifyResults | Where-Object { $_.success -eq $false }).Count -eq 0
        
        if ($allVerificationsPassed) {
            $testResult.success = $true
            $testResult.status = "passed"
            $script:Logger.Info("PostgreSQL restore test passed")
        } else {
            $testResult.success = $false
            $testResult.status = "failed"
            $testResult.error = "One or more verification tests failed"
            $script:Logger.Warning("PostgreSQL restore test failed verification")
        }
        
        # Cleanup test database
        if ($CleanupAfterTest) {
            try {
                $dropDbCommand = "& `"$($Config.databases.postgresql.primary.pg_path)\psql.exe`" -h $($Config.databases.postgresql.primary.host) -p $($Config.databases.postgresql.primary.port) -U $($Config.databases.postgresql.primary.username) -d postgres -c `"DROP DATABASE $testDbName;`""
                Invoke-Expression $dropDbCommand
                $script:Logger.Info("Test database cleaned up: $testDbName")
            }
            catch {
                $script:Logger.Warning("Failed to cleanup test database: $testDbName")
            }
        }
        
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("PostgreSQL restore test failed: $_")
        return $testResult
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Test SQL Server restore
function Test-SqlServerRestore {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Testing SQL Server restore...")
    
    $testResult = @{
        "test_name" = "sqlserver_restore"
        "start_time" = Get-Date
        "status" = "running"
        "success" = $false
    }
    
    try {
        # Find latest backup
        $backupPath = "$($Config.backup.base_path)\sqlserver\full"
        $latestBackup = Get-ChildItem -Path $backupPath -Filter "*.bak*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if (!$latestBackup) {
            throw "No SQL Server backup found for testing"
        }
        
        $testResult.backup_file = $latestBackup.FullName
        $testResult.backup_size_mb = [math]::Round($latestBackup.Length / 1MB, 2)
        $testResult.backup_age_hours = [math]::Round(((Get-Date) - $latestBackup.LastWriteTime).TotalHours, 2)
        
        # Create test database
        $testDbName = "ramsGIS_test_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $testResult.test_database = $testDbName
        
        $connectionString = "Server=$($Config.databases.sqlserver.primary.server),$($Config.databases.sqlserver.primary.port);Database=master;User ID=$($Config.databases.sqlserver.primary.username);Password=$($Config.databases.sqlserver.primary.password);TrustServerCertificate=True;"
        
        # Restore backup to test database
        $restoreStartTime = Get-Date
        
        $restoreCommand = @"
RESTORE DATABASE [$testDbName] 
FROM DISK = N'$($latestBackup.FullName)'
WITH REPLACE,
     MOVE 'ramsGIS' TO 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\${testDbName}.mdf',
     MOVE 'ramsGIS_Log' TO 'C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\${testDbName}_Log.ldf',
     STATS = 10
"@
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        $command = $connection.CreateCommand()
        $command.CommandText = $restoreCommand
        $command.CommandTimeout = 1800
        
        $script:Logger.Info("Restoring backup to test database: $testDbName")
        $command.ExecuteNonQuery()
        
        $connection.Close()
        
        $restoreEndTime = Get-Date
        $testResult.restore_duration_minutes = ($restoreEndTime - $restoreStartTime).TotalMinutes
        
        # Verify restore
        $testConnectionString = "Server=$($Config.databases.sqlserver.primary.server),$($Config.databases.sqlserver.primary.port);Database=$testDbName;User ID=$($Config.databases.sqlserver.primary.username);Password=$($Config.databases.sqlserver.primary.password);TrustServerCertificate=True;"
        
        $verifyTests = @(
            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES",
            "SELECT COUNT(*) FROM sys.tables",
            "SELECT DB_NAME()",
            "SELECT @@VERSION"
        )
        
        $verifyResults = @()
        foreach ($query in $verifyTests) {
            try {
                $verifyConnection = New-Object System.Data.SqlClient.SqlConnection
                $verifyConnection.ConnectionString = $testConnectionString
                $verifyConnection.Open()
                
                $verifyCommand = $verifyConnection.CreateCommand()
                $verifyCommand.CommandText = $query
                $result = $verifyCommand.ExecuteScalar()
                
                $verifyConnection.Close()
                
                $verifyResults += @{
                    "query" = $query
                    "result" = $result.ToString()
                    "success" = $true
                }
            }
            catch {
                $verifyResults += @{
                    "query" = $query
                    "error" = $_.Exception.Message
                    "success" = $false
                }
            }
        }
        
        $testResult.verification_results = $verifyResults
        
        # Check if all verifications passed
        $allVerificationsPassed = ($verifyResults | Where-Object { $_.success -eq $false }).Count -eq 0
        
        if ($allVerificationsPassed) {
            $testResult.success = $true
            $testResult.status = "passed"
            $script:Logger.Info("SQL Server restore test passed")
        } else {
            $testResult.success = $false
            $testResult.status = "failed"
            $testResult.error = "One or more verification tests failed"
            $script:Logger.Warning("SQL Server restore test failed verification")
        }
        
        # Cleanup test database
        if ($CleanupAfterTest) {
            try {
                $dropConnection = New-Object System.Data.SqlClient.SqlConnection
                $dropConnection.ConnectionString = $connectionString
                $dropConnection.Open()
                
                $dropCommand = $dropConnection.CreateCommand()
                $dropCommand.CommandText = "DROP DATABASE [$testDbName]"
                $dropCommand.ExecuteNonQuery()
                
                $dropConnection.Close()
                
                $script:Logger.Info("Test database cleaned up: $testDbName")
            }
            catch {
                $script:Logger.Warning("Failed to cleanup test database: $testDbName")
            }
        }
        
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("SQL Server restore test failed: $_")
        return $testResult
    }
}

# Test application restore
function Test-ApplicationRestore {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Starting application restore test...")
    
    $testResult = @{
        "test_name" = "application_restore"
        "start_time" = Get-Date
        "status" = "running"
        "success" = $false
    }
    
    try {
        # Find latest application backup
        $backupPath = "$($Config.backup.base_path)\application\full"
        $latestBackup = Get-ChildItem -Path $backupPath -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if (!$latestBackup) {
            throw "No application backup found for testing"
        }
        
        $testResult.backup_file = $latestBackup.FullName
        $testResult.backup_size_mb = [math]::Round($latestBackup.Length / 1MB, 2)
        $testResult.backup_age_hours = [math]::Round(((Get-Date) - $latestBackup.LastWriteTime).TotalHours, 2)
        
        # Create test restore directory
        $testRestorePath = "$($Config.recovery.staging_path)\test\app_restore_$(Get-Date -Format 'yyyyMMddHHmmss')"
        $testResult.restore_path = $testRestorePath
        
        if (!(Test-Path $testRestorePath)) {
            New-Item -ItemType Directory -Path $testRestorePath -Force | Out-Null
        }
        
        # Extract backup
        $restoreStartTime = Get-Date
        
        $script:Logger.Info("Extracting application backup to test directory...")
        
        # Use 7-Zip if available, otherwise use PowerShell
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("x", $latestBackup.FullName, "-o$testRestorePath") -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip extraction failed with exit code: $($process.ExitCode)"
            }
        } else {
            Expand-Archive -Path $latestBackup.FullName -DestinationPath $testRestorePath -Force
        }
        
        $restoreEndTime = Get-Date
        $testResult.restore_duration_minutes = ($restoreEndTime - $restoreStartTime).TotalMinutes
        
        # Verify restore
        $verifyTests = @(
            @{
                "name" = "web_inf_exists"
                "test" = { Test-Path "$testRestorePath\tomcat_webapps\src\WEB-INF" }
                "description" = "WEB-INF directory exists"
            },
            @{
                "name" = "site_properties_exists"
                "test" = { Test-Path "$testRestorePath\tomcat_webapps\src\WEB-INF\site.properties" }
                "description" = "site.properties file exists"
            },
            @{
                "name" = "jsp_files_exist"
                "test" = { (Get-ChildItem -Path "$testRestorePath\tomcat_webapps\src" -Filter "*.jsp" -Recurse).Count -gt 0 }
                "description" = "JSP files exist"
            },
            @{
                "name" = "config_files_exist"
                "test" = { Test-Path "$testRestorePath\tomcat_conf\server.xml" }
                "description" = "Configuration files exist"
            },
            @{
                "name" = "lib_files_exist"
                "test" = { (Get-ChildItem -Path "$testRestorePath\tomcat_lib" -Filter "*.jar" -Recurse).Count -gt 0 }
                "description" = "Library files exist"
            }
        )
        
        $verifyResults = @()
        foreach ($test in $verifyTests) {
            try {
                $result = & $test.test
                $verifyResults += @{
                    "name" = $test.name
                    "description" = $test.description
                    "result" = $result
                    "success" = $result -eq $true
                }
            }
            catch {
                $verifyResults += @{
                    "name" = $test.name
                    "description" = $test.description
                    "error" = $_.Exception.Message
                    "success" = $false
                }
            }
        }
        
        $testResult.verification_results = $verifyResults
        
        # Check if all verifications passed
        $allVerificationsPassed = ($verifyResults | Where-Object { $_.success -eq $false }).Count -eq 0
        
        if ($allVerificationsPassed) {
            $testResult.success = $true
            $testResult.status = "passed"
            $script:Logger.Info("Application restore test passed")
        } else {
            $testResult.success = $false
            $testResult.status = "failed"
            $testResult.error = "One or more verification tests failed"
            $script:Logger.Warning("Application restore test failed verification")
        }
        
        # Cleanup test directory
        if ($CleanupAfterTest) {
            try {
                Remove-Item -Path $testRestorePath -Recurse -Force
                $script:Logger.Info("Test restore directory cleaned up: $testRestorePath")
            }
            catch {
                $script:Logger.Warning("Failed to cleanup test restore directory: $testRestorePath")
            }
        }
        
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("Application restore test failed: $_")
        return $testResult
    }
}

# Test full recovery
function Test-FullRecovery {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Starting full recovery test...")
    
    $testResult = @{
        "test_name" = "full_recovery"
        "start_time" = Get-Date
        "status" = "running"
        "success" = $false
        "sub_tests" = @()
    }
    
    try {
        # Test database restore
        $dbTest = Test-DatabaseRestore -Config $Config
        $testResult.sub_tests += $dbTest
        
        # Test application restore
        $appTest = Test-ApplicationRestore -Config $Config
        $testResult.sub_tests += $appTest
        
        # Test integrated functionality (if both passed)
        if ($dbTest.success -and $appTest.success) {
            $integrationTest = Test-IntegratedFunctionality -Config $Config
            $testResult.sub_tests += $integrationTest
        }
        
        # Determine overall result
        $allSuccess = $testResult.sub_tests | ForEach-Object { $_.success } | Where-Object { $_ -eq $false }
        $testResult.success = $allSuccess.Count -eq 0
        $testResult.status = if ($testResult.success) { "passed" } else { "failed" }
        
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        $script:Logger.Info("Full recovery test completed - Status: $($testResult.status)")
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("Full recovery test failed: $_")
        return $testResult
    }
}

# Test integrated functionality
function Test-IntegratedFunctionality {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Testing integrated functionality...")
    
    $testResult = @{
        "test_name" = "integrated_functionality"
        "start_time" = Get-Date
        "status" = "running"
        "success" = $false
    }
    
    try {
        # This would test the integration between restored databases and application
        # For now, we'll simulate basic connectivity tests
        
        $integrationTests = @(
            @{
                "name" = "postgresql_connectivity"
                "description" = "Test PostgreSQL connection from application"
                "test" = { Test-PostgreSQLConnection -Config $Config.databases.postgresql.primary }
            },
            @{
                "name" = "sqlserver_connectivity"
                "description" = "Test SQL Server connection from application"
                "test" = { Test-SqlServerConnection -Config $Config.databases.sqlserver.primary }
            }
        )
        
        $verifyResults = @()
        foreach ($test in $integrationTests) {
            try {
                $result = & $test.test
                $verifyResults += @{
                    "name" = $test.name
                    "description" = $test.description
                    "result" = $result
                    "success" = $result.status -eq "healthy"
                }
            }
            catch {
                $verifyResults += @{
                    "name" = $test.name
                    "description" = $test.description
                    "error" = $_.Exception.Message
                    "success" = $false
                }
            }
        }
        
        $testResult.verification_results = $verifyResults
        
        # Check if all verifications passed
        $allVerificationsPassed = ($verifyResults | Where-Object { $_.success -eq $false }).Count -eq 0
        
        if ($allVerificationsPassed) {
            $testResult.success = $true
            $testResult.status = "passed"
            $script:Logger.Info("Integrated functionality test passed")
        } else {
            $testResult.success = $false
            $testResult.status = "failed"
            $testResult.error = "One or more integration tests failed"
            $script:Logger.Warning("Integrated functionality test failed")
        }
        
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        
        return $testResult
    }
    catch {
        $testResult.status = "error"
        $testResult.error = $_.Exception.Message
        $testResult.end_time = Get-Date
        $testResult.duration_minutes = ($testResult.end_time - $testResult.start_time).TotalMinutes
        $script:Logger.Error("Integrated functionality test failed: $_")
        return $testResult
    }
}

# Helper functions (simplified versions of connection tests)
function Test-PostgreSQLConnection {
    param([hashtable]$Config)
    
    try {
        $env:PGPASSWORD = $Config.Password
        $testCommand = "& `"$($Config.pg_path)\psql.exe`" -h $($Config.host) -p $($Config.port) -U $($Config.username) -d $($Config.database) -c `"SELECT 1;`" -t"
        
        $result = Invoke-Expression $testCommand
        
        if ($LASTEXITCODE -eq 0) {
            return @{ "status" = "healthy" }
        } else {
            return @{ "status" = "failed"; "error" = "Connection failed" }
        }
    }
    catch {
        return @{ "status" = "failed"; "error" = $_.Exception.Message }
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

function Test-SqlServerConnection {
    param([hashtable]$Config)
    
    try {
        $connectionString = "Server=$($Config.server),$($Config.port);Database=$($Config.database);User ID=$($Config.username);Password=$($Config.password);TrustServerCertificate=True;Connection Timeout=5;"
        
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        $connection.Close()
        
        return @{ "status" = "healthy" }
    }
    catch {
        return @{ "status" = "failed"; "error" = $_.Exception.Message }
    }
}

# Generate test report
function New-TestReport {
    param(
        [string]$ReportPath,
        [hashtable]$Config
    )
    
    $script:Logger.Info("Generating recovery test report...")
    
    $report = @{
        "test_summary" = @{
            "test_type" = $TestType
            "test_environment" = $TestEnvironment
            "start_time" = $script:TestStartTime
            "end_time" = Get-Date
            "duration_minutes" = ((Get-Date) - $script:TestStartTime).TotalMinutes
            "overall_success" = $true
            "isolated_network" = $IsolatedNetwork
            "cleanup_after_test" = $CleanupAfterTest
        }
        "test_results" = $script:TestResults
        "environment_info" = $script:TestEnvironmentInfo
        "metrics" = $script:TestMetrics
        "system_info" = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "disk_space_after" = Get-DiskSpace
            "memory_after" = Get-MemoryUsage
        }
    }
    
    # Determine overall success
    foreach ($testResult in $script:TestResults) {
        if ($testResult.success -eq $false) {
            $report.test_summary.overall_success = $false
            break
        }
    }
    
    # Save report as JSON
    $reportJson = $report | ConvertTo-Json -Depth 10
    $reportFile = "$ReportPath\recovery_test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8
    
    $script:Logger.Info("Recovery test report saved: $reportFile")
    
    return $report
}

# Get system resource information
function Get-DiskSpace {
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    $driveInfo = @()
    foreach ($drive in $drives) {
        $driveInfo += @{
            "drive" = $drive.DeviceID
            "total_size_gb" = [math]::Round($drive.Size / 1GB, 2)
            "free_space_gb" = [math]::Round($drive.FreeSpace / 1GB, 2)
            "used_space_gb" = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
            "free_space_percent" = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        }
    }
    
    return $driveInfo
}

function Get-MemoryUsage {
    $os = Get-WmiObject -Class Win32_OperatingSystem
    $totalMemory = [math]::Round($os.TotalVisibleMemorySize / 1MB, 2)
    $freeMemory = [math]::Round($os.FreePhysicalMemory / 1MB, 2)
    $usedMemory = $totalMemory - $freeMemory
    
    return @{
        "total_gb" = $totalMemory
        "free_gb" = $freeMemory
        "used_gb" = $usedMemory
        "used_percent" = [math]::Round(($usedMemory / $totalMemory) * 100, 2)
    }
}

# Main execution
try {
    # Initialize logging
    Initialize-TestLogging
    
    # Load configuration
    $config = Get-TestConfiguration -ConfigFile $ConfigFile
    
    # Initialize test environment
    Initialize-TestEnvironment -Config $config
    
    # Execute test based on type
    switch ($TestType) {
        "database_restore" {
            $result = Test-DatabaseRestore -Config $config
            $script:TestResults += $result
        }
        "application_restore" {
            $result = Test-ApplicationRestore -Config $config
            $script:TestResults += $result
        }
        "full_recovery" {
            $result = Test-FullRecovery -Config $config
            $script:TestResults += $result
        }
        "scheduled_test" {
            # Run all test types for scheduled testing
            $dbResult = Test-DatabaseRestore -Config $config
            $appResult = Test-ApplicationRestore -Config $config
            $fullResult = Test-FullRecovery -Config $config
            
            $script:TestResults += @($dbResult, $appResult, $fullResult)
        }
    }
    
    # Generate test report
    $report = New-TestReport -ReportPath ".\reports" -Config $config
    
    # Send notifications
    if ($config.notifications.enabled) {
        Send-TestNotification -Config $config.notifications -Report $report
    }
    
    $script:Logger.Info("Recovery testing completed successfully")
    
    # Set exit code based on overall success
    if ($report.test_summary.overall_success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    $script:Logger.Error("Recovery testing failed: $_")
    
    # Send failure notification
    if ($config.notifications.enabled) {
        Send-TestNotification -Config $config.notifications -Report @{
            "test_summary" = @{
                "test_type" = $TestType
                "overall_success" = $false
                "error_message" = $_.Exception.Message
            }
        }
    }
    
    exit 1
}
finally {
    # Cleanup environment variables
    Remove-Item env:BMS_TEST_MODE -ErrorAction SilentlyContinue
    Remove-Item env:BMS_TEST_ENVIRONMENT -ErrorAction SilentlyContinue
    Remove-Item env:BMS_TEST_TIMESTAMP -ErrorAction SilentlyContinue
    
    if ($script:Logger) {
        $script:Logger.Close()
    }
}