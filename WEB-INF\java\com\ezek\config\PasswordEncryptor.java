package com.ezek.config;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import com.codecharge.util.CCLogger;

/**
 * 密碼加密器 - 負責敏感資訊的加密和解密
 * 
 * 功能：
 * 1. 使用AES-256-CBC加密演算法
 * 2. 隨機生成初始化向量(IV)
 * 3. 支持Base64編碼存儲
 * 4. 提供金鑰管理功能
 * 
 * 安全特性：
 * - 使用安全的隨機數生成器
 * - 每次加密使用不同的IV
 * - 支援金鑰輪換
 * - 防止時間攻擊
 * 
 * <AUTHOR> Security Team
 * @version 1.0
 * @since 2025-07-09
 */
public class PasswordEncryptor {
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final String KEY_ENV_VAR = "BMS_ENCRYPTION_KEY";
    private static final String DEFAULT_KEY = "BMS_DEFAULT_KEY_2025"; // 僅用於開發環境
    
    private static final int IV_LENGTH = 16;
    private static final int KEY_LENGTH = 32; // 256 bits
    
    private final CCLogger logger;
    private final SecureRandom secureRandom;
    
    public PasswordEncryptor() {
        this.logger = CCLogger.getInstance();
        this.secureRandom = new SecureRandom();
    }
    
    /**
     * 加密密碼
     * 
     * @param plainText 明文密碼
     * @return Base64編碼的加密字串 (IV + 密文)
     */
    public String encrypt(String plainText) throws Exception {
        if (plainText == null || plainText.isEmpty()) {
            throw new IllegalArgumentException("明文不能為空");
        }
        
        try {
            // 1. 獲取金鑰
            SecretKey key = getEncryptionKey();
            
            // 2. 生成隨機IV
            byte[] iv = generateIV();
            
            // 3. 初始化加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(iv));
            
            // 4. 加密
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 5. 組合IV和密文
            byte[] combined = new byte[IV_LENGTH + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, IV_LENGTH);
            System.arraycopy(encryptedBytes, 0, combined, IV_LENGTH, encryptedBytes.length);
            
            // 6. Base64編碼
            return Base64.getEncoder().encodeToString(combined);
            
        } catch (Exception e) {
            logger.error("PasswordEncryptor: 加密失敗", e);
            throw new Exception("密碼加密失敗", e);
        }
    }
    
    /**
     * 解密密碼
     * 
     * @param encryptedText Base64編碼的加密字串
     * @return 明文密碼
     */
    public String decrypt(String encryptedText) throws Exception {
        if (encryptedText == null || encryptedText.isEmpty()) {
            throw new IllegalArgumentException("加密文本不能為空");
        }
        
        try {
            // 1. Base64解碼
            byte[] combined = Base64.getDecoder().decode(encryptedText);
            
            if (combined.length < IV_LENGTH) {
                throw new IllegalArgumentException("加密文本格式不正確");
            }
            
            // 2. 分離IV和密文
            byte[] iv = new byte[IV_LENGTH];
            byte[] encryptedBytes = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, IV_LENGTH);
            System.arraycopy(combined, IV_LENGTH, encryptedBytes, 0, encryptedBytes.length);
            
            // 3. 獲取金鑰
            SecretKey key = getEncryptionKey();
            
            // 4. 初始化解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(iv));
            
            // 5. 解密
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            
            return new String(decryptedBytes, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            logger.error("PasswordEncryptor: 解密失敗", e);
            throw new Exception("密碼解密失敗", e);
        }
    }
    
    /**
     * 檢查文本是否為加密格式
     */
    public boolean isEncrypted(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        try {
            // 嘗試Base64解碼
            byte[] decoded = Base64.getDecoder().decode(text);
            // 檢查長度是否合理（至少包含IV）
            return decoded.length >= IV_LENGTH;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * 生成隨機IV
     */
    private byte[] generateIV() {
        byte[] iv = new byte[IV_LENGTH];
        secureRandom.nextBytes(iv);
        return iv;
    }
    
    /**
     * 獲取加密金鑰
     */
    private SecretKey getEncryptionKey() throws Exception {
        // 1. 優先從環境變數獲取
        String keyString = System.getenv(KEY_ENV_VAR);
        
        // 2. 從系統屬性獲取
        if (keyString == null || keyString.isEmpty()) {
            keyString = System.getProperty(KEY_ENV_VAR);
        }
        
        // 3. 開發環境使用預設金鑰（生產環境必須設定環境變數）
        if (keyString == null || keyString.isEmpty()) {
            String environment = System.getenv("SYSTEM_MODE");
            if ("development".equals(environment)) {
                keyString = DEFAULT_KEY;
                logger.warn("PasswordEncryptor: 使用預設金鑰，請在生產環境設定環境變數 " + KEY_ENV_VAR);
            } else {
                throw new Exception("未設定加密金鑰，請設定環境變數: " + KEY_ENV_VAR);
            }
        }
        
        // 4. 確保金鑰長度
        byte[] keyBytes = keyString.getBytes(StandardCharsets.UTF_8);
        if (keyBytes.length < KEY_LENGTH) {
            // 如果金鑰長度不足，使用PBKDF2擴展
            keyBytes = expandKey(keyBytes);
        } else if (keyBytes.length > KEY_LENGTH) {
            // 如果金鑰過長，截取前32位元組
            byte[] truncated = new byte[KEY_LENGTH];
            System.arraycopy(keyBytes, 0, truncated, 0, KEY_LENGTH);
            keyBytes = truncated;
        }
        
        return new SecretKeySpec(keyBytes, ALGORITHM);
    }
    
    /**
     * 擴展金鑰長度
     */
    private byte[] expandKey(byte[] original) throws Exception {
        byte[] expanded = new byte[KEY_LENGTH];
        
        // 簡單的金鑰擴展：重複原始金鑰
        for (int i = 0; i < KEY_LENGTH; i++) {
            expanded[i] = original[i % original.length];
        }
        
        return expanded;
    }
    
    /**
     * 生成新的加密金鑰（用於金鑰輪換）
     */
    public static String generateNewKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance(ALGORITHM);
        keyGen.init(256);
        SecretKey key = keyGen.generateKey();
        return Base64.getEncoder().encodeToString(key.getEncoded());
    }
    
    /**
     * 驗證密碼加密/解密是否正常
     */
    public boolean validateEncryption() {
        try {
            String testText = "test_password_" + System.currentTimeMillis();
            String encrypted = encrypt(testText);
            String decrypted = decrypt(encrypted);
            
            boolean isValid = testText.equals(decrypted);
            if (isValid) {
                logger.info("PasswordEncryptor: 加密驗證成功");
            } else {
                logger.error("PasswordEncryptor: 加密驗證失敗");
            }
            
            return isValid;
        } catch (Exception e) {
            logger.error("PasswordEncryptor: 加密驗證異常", e);
            return false;
        }
    }
    
    /**
     * 清理敏感資料
     */
    public void clearSensitiveData() {
        // 在實際實現中，這裡可以清理快取的金鑰等敏感資料
        logger.debug("PasswordEncryptor: 清理敏感資料");
    }
}