# 資料庫效能掃描與修復系統 - 技術設計文檔

## Overview 概述

資料庫效能掃描與修復系統是一個專為新北市違章建築管理系統設計的智能化效能監控與最佳化平台。系統採用微服務架構，整合 PostgreSQL 深度效能分析、CodeCharge Studio 架構相容性和自動化安全修復機制，確保37萬筆案件資料和100萬筆流程記錄的高效能存取。

### 核心設計原則

1. **非侵入性** - 透過代理模式和切面編程實現零程式碼修改監控
2. **安全優先** - 多層級安全驗證確保修復操作的可逆性和安全性
3. **智能分析** - 基於機器學習的查詢模式識別和效能預測
4. **Legacy相容** - 完全相容CodeCharge Studio三層架構模式

## Architecture 系統架構

### 整體架構圖

```mermaid
graph TB
    subgraph "資料庫效能掃描與修復系統"
        subgraph "前端層 Frontend Layer"
            WEB[Web管理介面]
            API[REST API Gateway]
            DASH[效能監控儀表板]
        end
        
        subgraph "服務層 Service Layer"
            SCAN[掃描引擎服務]
            NPONE[N+1查詢檢測服務]
            INDEX[索引分析服務]
            REPAIR[安全修復服務]
            ALERT[告警通知服務]
        end
        
        subgraph "核心引擎層 Core Engine Layer"
            PERF[效能分析引擎]
            ML[機器學習引擎]
            RULE[規則引擎]
            AUDIT[審計引擎]
        end
        
        subgraph "資料存儲層 Data Storage Layer"
            METRICS[效能指標庫]
            CONFIG[配置資料庫]
            LOGS[操作日誌庫]
            CACHE[Redis快取]
        end
    end
    
    subgraph "目標系統 Target System"
        subgraph "應用層"
            JSP[JSP頁面]
            XML[XML配置]
            HANDLERS[Handlers邏輯]
        end
        
        subgraph "資料層"
            PG[(PostgreSQL主庫)]
            SQL[(SQL Server輔庫)]
        end
    end
    
    WEB --> API
    API --> SCAN
    API --> NPONE
    API --> INDEX
    API --> REPAIR
    
    SCAN --> PERF
    NPONE --> ML
    INDEX --> RULE
    REPAIR --> AUDIT
    
    PERF --> METRICS
    ML --> CONFIG
    RULE --> LOGS
    AUDIT --> CACHE
    
    SCAN -.->|監控| JSP
    NPONE -.->|分析| HANDLERS
    INDEX -.->|掃描| PG
    REPAIR -.->|最佳化| SQL
```

### 部署架構

```mermaid
graph LR
    subgraph "Tomcat 應用伺服器"
        subgraph "違章建築管理系統"
            APP[Legacy Application]
            FILTER[監控過濾器]
        end
        
        subgraph "效能監控系統"
            MONITOR[監控服務]
            AGENT[監控代理]
        end
    end
    
    subgraph "獨立服務層"
        ANALYSIS[分析服務]
        REPAIR_SVC[修復服務]
        WEB_UI[Web介面]
    end
    
    subgraph "資料存儲"
        PG_MAIN[(主資料庫)]
        PG_METRICS[(監控資料庫)]
        REDIS[(Redis快取)]
    end
    
    APP --> FILTER
    FILTER --> AGENT
    AGENT --> MONITOR
    MONITOR --> ANALYSIS
    ANALYSIS --> REPAIR_SVC
    REPAIR_SVC --> WEB_UI
    
    MONITOR --> PG_METRICS
    REPAIR_SVC --> PG_MAIN
    ANALYSIS --> REDIS
```

## Components and Interfaces 元件與介面

### 1. 資料庫效能掃描引擎 (Database Performance Scanner)

#### 核心類別架構

```java
public class DatabasePerformanceScanner {
    private final PostgreSQLAnalyzer pgAnalyzer;
    private final SQLServerAnalyzer sqlAnalyzer;
    private final QueryStatCollector statCollector;
    private final PerformanceReportGenerator reportGenerator;
    
    public ScanResult performFullScan(ScanConfiguration config) {
        // 執行全面效能掃描
    }
    
    public List<PerformanceIssue> identifyIssues(ScanResult result) {
        // 識別效能問題並分級
    }
}
```

#### 主要介面定義

```java
public interface PerformanceAnalyzer {
    AnalysisResult analyzeDatabase(DatabaseConnection conn);
    List<QueryPattern> extractQueryPatterns();
    PerformanceMetrics collectMetrics();
    List<OptimizationSuggestion> generateSuggestions();
}

public interface ScanConfiguration {
    DatabaseConnectionConfig getDatabaseConfig();
    ScanScope getScanScope();
    PerformanceThresholds getThresholds();
    boolean isRealTimeMonitoring();
}
```

### 2. N+1查詢檢測服務 (N+1 Query Detection Service)

#### 檢測引擎架構

```java
public class NPlusOneQueryDetector {
    private final StaticCodeAnalyzer staticAnalyzer;
    private final DynamicQueryTracker dynamicTracker;
    private final CodeChargePatternMatcher patternMatcher;
    
    public List<NPlusOnePattern> detectInHandlers(String handlerPath) {
        // 靜態分析Handlers中的N+1模式
    }
    
    public List<RuntimeQueryIssue> trackRuntimeQueries(HttpServletRequest request) {
        // 動態追蹤執行時查詢模式
    }
}
```

#### CodeCharge專用分析器

```java
public class CodeChargeAnalyzer implements StaticCodeAnalyzer {
    public List<NPlusOnePattern> analyzeJSPFile(File jspFile) {
        // 分析JSP中的資料綁定模式
    }
    
    public List<NPlusOnePattern> analyzeHandlerFile(File handlerFile) {
        // 分析Handler中的迴圈查詢模式
    }
    
    public List<QueryOptimization> generateJOINSuggestions(NPlusOnePattern pattern) {
        // 生成JOIN查詢建議
    }
}
```

### 3. 索引分析與建議服務 (Index Analysis Service)

#### 索引分析器

```java
public class IndexAnalyzer {
    private final PostgreSQLIndexAnalyzer pgIndexAnalyzer;
    private final QueryPlanAnalyzer planAnalyzer;
    private final IndexCostCalculator costCalculator;
    
    public List<MissingIndex> findMissingIndexes() {
        // 基於pg_stat_statements分析缺失索引
    }
    
    public List<UnusedIndex> findUnusedIndexes() {
        // 基於pg_stat_user_indexes找出未使用索引
    }
    
    public IndexCreationPlan generateIndexPlan(List<MissingIndex> missing) {
        // 生成安全的索引建立計劃
    }
}
```

#### 智能索引建議算法

```java
public class IntelligentIndexRecommendation {
    public List<IndexRecommendation> analyzeQueryPatterns(
        List<QueryStatistics> queryStats,
        List<TableStatistics> tableStats
    ) {
        return queryStats.stream()
            .filter(q -> q.getExecutionCount() > FREQUENCY_THRESHOLD)
            .filter(q -> q.getAverageTime() > TIME_THRESHOLD)
            .map(this::generateIndexRecommendation)
            .sorted(Comparator.comparing(IndexRecommendation::getPriority))
            .collect(Collectors.toList());
    }
    
    private IndexRecommendation generateIndexRecommendation(QueryStatistics query) {
        IndexRecommendation recommendation = new IndexRecommendation();
        recommendation.setTableName(extractTableName(query.getQuery()));
        recommendation.setColumns(extractWhereColumns(query.getQuery()));
        recommendation.setPriority(calculatePriority(query));
        recommendation.setEstimatedImprovement(estimateImprovement(query));
        return recommendation;
    }
}
```

### 4. 安全自動修復服務 (Safe Automated Repair Service)

#### 安全修復引擎

```java
public class SafeAutomatedRepairEngine {
    private final TransactionManager transactionManager;
    private final BackupManager backupManager;
    private final RiskAssessment riskAssessment;
    private final RollbackManager rollbackManager;
    
    public RepairResult executeRepair(RepairPlan plan) {
        // 建立安全點
        SafetyCheckpoint checkpoint = createSafetyCheckpoint();
        
        try {
            // 風險評估
            RiskLevel risk = riskAssessment.assess(plan);
            if (risk.isHighRisk()) {
                return RepairResult.rejected("高風險操作需要人工確認");
            }
            
            // 執行修復
            RepairResult result = executeRepairPlan(plan);
            
            // 驗證修復效果
            if (validateRepairResult(result)) {
                checkpoint.commit();
                return result;
            } else {
                rollbackManager.rollback(checkpoint);
                return RepairResult.failed("修復驗證失敗，已回復");
            }
        } catch (Exception e) {
            rollbackManager.rollback(checkpoint);
            throw new RepairException("修復過程發生錯誤", e);
        }
    }
}
```

#### 風險評估系統

```java
public class DatabaseRiskAssessment {
    public RiskLevel assessIndexCreation(IndexCreationPlan plan) {
        RiskLevel risk = RiskLevel.LOW;
        
        // 評估資料表大小
        if (plan.getTableSize() > LARGE_TABLE_THRESHOLD) {
            risk = risk.increase();
        }
        
        // 評估索引數量
        if (plan.getExistingIndexCount() > MAX_INDEX_THRESHOLD) {
            risk = risk.increase();
        }
        
        // 評估業務時間
        if (isBusinessHours()) {
            risk = risk.increase();
        }
        
        // 評估觸發器影響
        if (hasComplexTriggers(plan.getTableName())) {
            risk = risk.increase();
        }
        
        return risk;
    }
}
```

### 5. 效能監控儀表板服務

#### Web API 控制器

```java
@RestController
@RequestMapping("/api/monitoring")
public class MonitoringController {
    
    @GetMapping("/dashboard")
    public ResponseEntity<DashboardData> getDashboard() {
        DashboardData data = monitoringService.getCurrentDashboardData();
        return ResponseEntity.ok(data);
    }
    
    @GetMapping("/performance/trends")
    public ResponseEntity<List<PerformanceTrend>> getPerformanceTrends(
        @RequestParam String timeRange,
        @RequestParam(required = false) String module
    ) {
        List<PerformanceTrend> trends = performanceService.getTrends(timeRange, module);
        return ResponseEntity.ok(trends);
    }
    
    @PostMapping("/scan/start")
    public ResponseEntity<ScanJobResponse> startScan(@RequestBody ScanRequest request) {
        ScanJob job = scanService.startScan(request);
        return ResponseEntity.ok(new ScanJobResponse(job.getId(), job.getStatus()));
    }
}
```

## Data Models 資料模型

### 核心資料結構

#### 效能監控資料模型

```sql
-- 效能指標快照表
CREATE TABLE performance_snapshots (
    id BIGSERIAL PRIMARY KEY,
    snapshot_time TIMESTAMP NOT NULL,
    database_name VARCHAR(100) NOT NULL,
    active_connections INTEGER,
    cache_hit_ratio DECIMAL(5,2),
    avg_query_time DECIMAL(10,3),
    slow_query_count INTEGER,
    index_usage_ratio DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 查詢統計表
CREATE TABLE query_statistics (
    id BIGSERIAL PRIMARY KEY,
    query_hash VARCHAR(64) NOT NULL,
    query_text TEXT NOT NULL,
    execution_count BIGINT DEFAULT 0,
    total_execution_time DECIMAL(15,3),
    avg_execution_time DECIMAL(10,3),
    max_execution_time DECIMAL(10,3),
    last_execution_time TIMESTAMP,
    database_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- N+1查詢檢測結果表
CREATE TABLE nplus_one_detections (
    id BIGSERIAL PRIMARY KEY,
    detection_time TIMESTAMP NOT NULL,
    jsp_file_path VARCHAR(500),
    handler_file_path VARCHAR(500),
    query_pattern TEXT NOT NULL,
    execution_count INTEGER,
    total_time_ms DECIMAL(10,3),
    severity_level VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    suggested_optimization TEXT,
    status VARCHAR(20) DEFAULT 'DETECTED', -- 'DETECTED', 'ACKNOWLEDGED', 'FIXED'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 索引分析資料模型

```sql
-- 索引建議表
CREATE TABLE index_recommendations (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(200) NOT NULL,
    column_names TEXT[] NOT NULL,
    index_type VARCHAR(50) DEFAULT 'btree',
    estimated_improvement_percent DECIMAL(5,2),
    creation_cost_estimate DECIMAL(10,2),
    maintenance_cost_estimate DECIMAL(8,2),
    priority_score INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING', -- 'PENDING', 'APPROVED', 'CREATED', 'REJECTED'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 索引使用統計表
CREATE TABLE index_usage_statistics (
    id BIGSERIAL PRIMARY KEY,
    schema_name VARCHAR(100) NOT NULL,
    table_name VARCHAR(200) NOT NULL,
    index_name VARCHAR(200) NOT NULL,
    scan_count BIGINT DEFAULT 0,
    tuples_read BIGINT DEFAULT 0,
    tuples_fetched BIGINT DEFAULT 0,
    index_size_bytes BIGINT,
    last_used TIMESTAMP,
    usage_ratio DECIMAL(5,2),
    snapshot_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 修復操作審計模型

```sql
-- 修復操作記錄表
CREATE TABLE repair_operations (
    id BIGSERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL, -- 'INDEX_CREATION', 'QUERY_OPTIMIZATION', 'CONFIGURATION_CHANGE'
    operation_description TEXT NOT NULL,
    target_object VARCHAR(500), -- 目標表名、查詢等
    executed_by VARCHAR(100) NOT NULL,
    execution_time TIMESTAMP NOT NULL,
    pre_execution_backup TEXT, -- 執行前狀態備份
    execution_sql TEXT, -- 執行的SQL語句
    execution_result TEXT, -- 執行結果
    rollback_sql TEXT, -- 回復SQL
    status VARCHAR(20) NOT NULL, -- 'SUCCESS', 'FAILED', 'ROLLED_BACK'
    risk_level VARCHAR(20) NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH'
    approval_required BOOLEAN DEFAULT FALSE,
    approved_by VARCHAR(100),
    approved_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 效能改善追蹤表
CREATE TABLE performance_improvements (
    id BIGSERIAL PRIMARY KEY,
    repair_operation_id BIGINT REFERENCES repair_operations(id),
    metric_name VARCHAR(100) NOT NULL,
    before_value DECIMAL(15,3),
    after_value DECIMAL(15,3),
    improvement_percent DECIMAL(5,2),
    measurement_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Java資料傳輸物件 (DTOs)

```java
public class PerformanceSnapshot {
    private Long id;
    private LocalDateTime snapshotTime;
    private String databaseName;
    private Integer activeConnections;
    private BigDecimal cacheHitRatio;
    private BigDecimal avgQueryTime;
    private Integer slowQueryCount;
    private BigDecimal indexUsageRatio;
    
    // getters, setters, constructors...
}

public class NPlusOneDetection {
    private Long id;
    private LocalDateTime detectionTime;
    private String jspFilePath;
    private String handlerFilePath;
    private String queryPattern;
    private Integer executionCount;
    private BigDecimal totalTimeMs;
    private SeverityLevel severityLevel;
    private String suggestedOptimization;
    private DetectionStatus status;
    
    public enum SeverityLevel {
        LOW, MEDIUM, HIGH, CRITICAL
    }
    
    public enum DetectionStatus {
        DETECTED, ACKNOWLEDGED, FIXED
    }
}
```

## Error Handling 錯誤處理策略

### 分層錯誤處理架構

```java
public class DatabasePerformanceException extends Exception {
    private final ErrorCode errorCode;
    private final ErrorSeverity severity;
    private final Map<String, Object> context;
    
    public DatabasePerformanceException(ErrorCode code, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = code;
        this.severity = code.getDefaultSeverity();
        this.context = new HashMap<>();
    }
}

public enum ErrorCode {
    DATABASE_CONNECTION_FAILED(ErrorSeverity.HIGH, "DATABASE_001"),
    QUERY_ANALYSIS_FAILED(ErrorSeverity.MEDIUM, "ANALYSIS_001"),
    INDEX_CREATION_FAILED(ErrorSeverity.HIGH, "INDEX_001"),
    NPLUS_ONE_DETECTION_FAILED(ErrorSeverity.LOW, "DETECTION_001"),
    REPAIR_OPERATION_FAILED(ErrorSeverity.CRITICAL, "REPAIR_001"),
    ROLLBACK_FAILED(ErrorSeverity.CRITICAL, "ROLLBACK_001");
    
    private final ErrorSeverity defaultSeverity;
    private final String errorId;
}
```

### 全域錯誤處理器

```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(DatabasePerformanceException.class)
    public ResponseEntity<ErrorResponse> handleDatabasePerformanceException(
        DatabasePerformanceException ex
    ) {
        ErrorResponse response = ErrorResponse.builder()
            .errorCode(ex.getErrorCode().getErrorId())
            .message(ex.getMessage())
            .severity(ex.getSeverity().toString())
            .timestamp(LocalDateTime.now())
            .context(ex.getContext())
            .build();
            
        // 記錄錯誤日誌
        logError(ex);
        
        // 發送告警（高嚴重性錯誤）
        if (ex.getSeverity().ordinal() >= ErrorSeverity.HIGH.ordinal()) {
            alertService.sendAlert(ex);
        }
        
        return ResponseEntity.status(getHttpStatus(ex.getSeverity())).body(response);
    }
    
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        // 處理未預期的錯誤
        ErrorResponse response = ErrorResponse.builder()
            .errorCode("UNKNOWN_ERROR")
            .message("系統發生未預期錯誤")
            .severity(ErrorSeverity.HIGH.toString())
            .timestamp(LocalDateTime.now())
            .build();
            
        logger.error("未預期錯誤", ex);
        alertService.sendCriticalAlert("未預期錯誤", ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
```

### 修復操作錯誤處理

```java
public class RepairOperationErrorHandler {
    
    public RepairResult handleRepairFailure(RepairOperation operation, Exception error) {
        try {
            // 嘗試自動回復
            rollbackManager.rollback(operation.getCheckpoint());
            
            // 記錄失敗原因
            auditService.logRepairFailure(operation, error);
            
            // 評估是否需要手動干預
            if (requiresManualIntervention(error)) {
                alertService.sendUrgentAlert(
                    "修復操作失敗需要手動干預",
                    operation,
                    error
                );
                return RepairResult.failed("需要手動干預", error);
            }
            
            return RepairResult.rolledBack("自動回復成功", error);
            
        } catch (RollbackException rollbackError) {
            // 回復失敗 - 最嚴重的情況
            alertService.sendCriticalAlert(
                "修復操作失敗且回復失敗",
                operation,
                Arrays.asList(error, rollbackError)
            );
            
            // 標記需要緊急人工處理
            emergencyService.createEmergencyTicket(operation, error, rollbackError);
            
            return RepairResult.critical("回復失敗，需要緊急處理", rollbackError);
        }
    }
}
```

## Testing Strategy 測試策略

### 測試架構分層

```mermaid
graph TD
    subgraph "測試金字塔"
        E2E[端到端測試<br/>Integration Tests]
        INT[整合測試<br/>Service Integration]
        UNIT[單元測試<br/>Unit Tests]
    end
    
    subgraph "專項測試"
        PERF[效能測試<br/>Performance Tests]
        SEC[安全測試<br/>Security Tests]
        LOAD[負載測試<br/>Load Tests]
    end
    
    subgraph "資料測試"
        DB[資料庫測試<br/>Database Tests]
        DATA[資料完整性<br/>Data Integrity]
        MIGR[遷移測試<br/>Migration Tests]
    end
```

### 單元測試策略

```java
@ExtendWith(MockitoExtension.class)
class DatabasePerformanceScannerTest {
    
    @Mock
    private PostgreSQLAnalyzer pgAnalyzer;
    
    @Mock
    private QueryStatCollector statCollector;
    
    @InjectMocks
    private DatabasePerformanceScanner scanner;
    
    @Test
    void shouldDetectSlowQueries() {
        // Given
        ScanConfiguration config = ScanConfiguration.builder()
            .slowQueryThreshold(Duration.ofSeconds(1))
            .build();
        
        List<QueryStatistics> mockStats = Arrays.asList(
            QueryStatistics.builder()
                .query("SELECT * FROM buildcase WHERE caseopened = ?")
                .averageTime(Duration.ofSeconds(2))
                .executionCount(100)
                .build()
        );
        
        when(statCollector.collectStatistics()).thenReturn(mockStats);
        
        // When
        ScanResult result = scanner.performFullScan(config);
        
        // Then
        assertThat(result.getSlowQueries()).hasSize(1);
        assertThat(result.getSlowQueries().get(0).getAverageTime())
            .isGreaterThan(config.getSlowQueryThreshold());
    }
    
    @Test
    void shouldHandleDatabaseConnectionFailure() {
        // Given
        when(pgAnalyzer.connect()).thenThrow(new DatabaseConnectionException("連線失敗"));
        
        // When & Then
        assertThatThrownBy(() -> scanner.performFullScan(defaultConfig()))
            .isInstanceOf(DatabasePerformanceException.class)
            .hasMessageContaining("連線失敗");
    }
}
```

### 整合測試策略

```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.properties")
@Testcontainers
class DatabasePerformanceIntegrationTest {
    
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15")
            .withDatabaseName("bms_test")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private DatabasePerformanceScanner scanner;
    
    @Autowired
    private TestDataSetup testDataSetup;
    
    @Test
    void shouldDetectMissingIndexesInRealDatabase() {
        // Given - 建立測試資料
        testDataSetup.createBuildcaseTable();
        testDataSetup.insertTestData(10000);
        testDataSetup.executeSlowQueries();
        
        // When - 執行掃描
        ScanResult result = scanner.performFullScan(realDatabaseConfig());
        
        // Then - 驗證結果
        assertThat(result.getMissingIndexes()).isNotEmpty();
        assertThat(result.getMissingIndexes().get(0).getTableName()).isEqualTo("buildcase");
        assertThat(result.getMissingIndexes().get(0).getColumns()).contains("caseopened");
    }
}
```

### 效能測試策略

```java
@ExtendWith(BenchmarkExtension.class)
class PerformanceTest {
    
    @Benchmark
    @BenchmarkMode(Mode.AverageTime)
    @OutputTimeUnit(TimeUnit.MILLISECONDS)
    public void benchmarkFullDatabaseScan(Blackhole bh) {
        ScanResult result = scanner.performFullScan(standardConfig());
        bh.consume(result);
    }
    
    @Test
    void shouldCompleteFullScanWithin30Seconds() {
        assertTimeout(Duration.ofSeconds(30), () -> {
            ScanResult result = scanner.performFullScan(realDatabaseConfig());
            assertThat(result.getCompletionStatus()).isEqualTo(ScanStatus.COMPLETED);
        });
    }
    
    @Test
    void shouldNotExceedMemoryLimit() {
        // 監控記憶體使用
        MemoryUsageMonitor monitor = new MemoryUsageMonitor();
        monitor.start();
        
        scanner.performFullScan(largeDatasetConfig());
        
        long maxMemoryUsed = monitor.getMaxMemoryUsed();
        assertThat(maxMemoryUsed).isLessThan(Size.megabytes(200).toBytes());
    }
}
```

### 安全測試策略

```java
@TestMethodOrder(OrderAnnotation.class)
class SecurityTest {
    
    @Test
    @Order(1)
    void shouldRejectUnauthorizedRepairOperations() {
        // Given - 未授權使用者
        SecurityContext unauthorizedContext = createUnauthorizedContext();
        
        // When & Then
        assertThatThrownBy(() -> 
            repairService.executeRepair(indexCreationPlan(), unauthorizedContext)
        ).isInstanceOf(AccessDeniedException.class);
    }
    
    @Test
    @Order(2)
    void shouldPreventSQLInjectionInQueryAnalysis() {
        // Given - 惡意查詢字串
        String maliciousQuery = "'; DROP TABLE buildcase; --";
        
        // When & Then
        assertThatThrownBy(() -> 
            queryAnalyzer.analyzeQuery(maliciousQuery)
        ).isInstanceOf(SecurityException.class)
         .hasMessageContaining("檢測到潛在的SQL注入攻擊");
    }
    
    @Test
    @Order(3)
    void shouldAuditAllRepairOperations() {
        // Given
        RepairPlan plan = createSafeIndexPlan();
        SecurityContext adminContext = createAdminContext();
        
        // When
        RepairResult result = repairService.executeRepair(plan, adminContext);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        
        // 驗證審計記錄
        List<AuditRecord> auditRecords = auditService.getRecentRecords();
        assertThat(auditRecords).hasSize(1);
        assertThat(auditRecords.get(0).getOperationType()).isEqualTo("INDEX_CREATION");
        assertThat(auditRecords.get(0).getExecutedBy()).isEqualTo("admin");
    }
}
```

### 資料完整性測試

```java
class DataIntegrityTest {
    
    @Test
    void shouldMaintainTriggerFunctionalityAfterIndexCreation() {
        // Given - 現有觸發器
        testDataSetup.createTriggersForBuildcase();
        
        // When - 建立索引
        IndexCreationPlan plan = createIndexPlan("buildcase", Arrays.asList("caseopened"));
        RepairResult result = repairService.executeRepair(plan, adminContext());
        
        // Then - 驗證觸發器仍正常運作
        assertThat(result.isSuccess()).isTrue();
        
        // 插入測試資料觸發器
        testDataSetup.insertBuildcaseRecord("TEST001", "231");
        
        // 驗證觸發器建立的相關記錄
        List<TbflowRecord> flowRecords = tbflowRepository.findByCaseNo("TEST001");
        assertThat(flowRecords).hasSize(1);
        assertThat(flowRecords.get(0).getFlowStatus()).isEqualTo("231");
    }
    
    @Test
    void shouldRollbackCompletelyOnFailure() {
        // Given - 建立檢查點
        testDataSetup.createSnapshotOfCurrentState();
        
        // When - 執行會失敗的修復操作
        RepairPlan failingPlan = createFailingRepairPlan();
        RepairResult result = repairService.executeRepair(failingPlan, adminContext());
        
        // Then - 驗證完全回復
        assertThat(result.isSuccess()).isFalse();
        assertThat(result.getStatus()).isEqualTo(RepairStatus.ROLLED_BACK);
        
        // 驗證資料庫狀態完全回復
        DatabaseState currentState = testDataSetup.getCurrentDatabaseState();
        DatabaseState originalState = testDataSetup.getOriginalDatabaseState();
        assertThat(currentState).isEqualTo(originalState);
    }
}
```

### 測試資料管理

```java
@Component
public class TestDataSetup {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public void createBuildcaseTable() {
        jdbcTemplate.execute("""
            CREATE TABLE IF NOT EXISTS buildcase (
                case_no VARCHAR(20) PRIMARY KEY,
                caseopened VARCHAR(10) NOT NULL,
                s_empno VARCHAR(20),
                case_con_user VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """);
    }
    
    public void insertTestData(int recordCount) {
        String[] statuses = {"231", "232", "234", "331", "334"};
        String[] employees = {"EMP001", "EMP002", "EMP003", "EMP004", "EMP005"};
        
        for (int i = 1; i <= recordCount; i++) {
            jdbcTemplate.update("""
                INSERT INTO buildcase (case_no, caseopened, s_empno) 
                VALUES (?, ?, ?)
            """, 
            String.format("TEST%06d", i),
            statuses[i % statuses.length],
            employees[i % employees.length]
            );
        }
    }
    
    public void executeSlowQueries() {
        // 執行會產生全表掃描的查詢
        jdbcTemplate.queryForList("""
            SELECT * FROM buildcase 
            WHERE caseopened LIKE '%31%' 
            AND s_empno IS NOT NULL
        """);
    }
    
    @PreDestroy
    public void cleanup() {
        jdbcTemplate.execute("DROP TABLE IF EXISTS buildcase CASCADE");
    }
}
```

### 持續整合測試管道

```yaml
# .github/workflows/performance-testing.yml
name: Performance Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 11
        uses: actions/setup-java@v3
        with:
          java-version: '11'
      - name: Run unit tests
        run: ./mvnw test

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: ./mvnw test -Dtest.profile=integration

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - name: Run performance benchmarks
        run: ./mvnw test -Dtest.profile=performance
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: target/performance-results/
```

這個測試策略確保了系統的可靠性、效能和安全性，特別針對違章建築管理系統的大量資料和複雜業務邏輯進行了全面的測試覆蓋。