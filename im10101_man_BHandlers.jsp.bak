<%@page pageEncoding="utf-8"%>
<%@ page import="java.sql.PreparedStatement" %> <%-- 仍然需要導入 --%>
<%@ page import="java.sql.Timestamp" %>
<%@ page import="java.sql.SQLException" %>
<%@ page import="java.sql.ResultSet" %>
<%--== Handlers ==--%> <%--im10101_man_B Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

public void deleteAll(File path) {
    if (!path.exists()) {
        return;
    }
    if (path.isFile()) {
        path.delete();
        return;
    }
    File[] files = path.listFiles();
    for (int i = 0; i < files.length; i++) {
        deleteAll(files[i]);
    }
    path.delete();
}

//Feature checker Head @1-BDA3B34B
    public class im10101_man_BServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//im10101_man_B Page Handler Head @1-621CE19F
    public class im10101_man_BPageHandler implements PageListener {
//End im10101_man_B Page Handler Head

//im10101_man_B BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End im10101_man_B BeforeInitialize Method Head

//im10101_man_B BeforeInitialize Method Tail @1-FCB6E20C
        }
//End im10101_man_B BeforeInitialize Method Tail

//im10101_man_B AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End im10101_man_B AfterInitialize Method Head

//Event AfterInitialize Action Custom Code @119-44795B7A

			//中文
 			String currentProgramId = "im10101";
            String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID"));          
            if (!StringUtils.isEmpty(PROGRAM_ID) && !PROGRAM_ID.equals(currentProgramId)) {
	            for (int i = 1; i <= 15; i++) SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("SEARCHPARAM_" + String.valueOf(i), "");
            }
            SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PRINT_BACK", "in10101_man");
            SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PROGRAM_ID", currentProgramId);

			String CASE_ID = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("case_id"));
			SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("CASE_ID", CASE_ID);
			
			String c_CASE_ID = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("saveInsert"));
			
			// 新增案件
			if( StringUtils.isEmpty(CASE_ID) ){
				JDBCConnection jdbcConn = null;
				PreparedStatement  pstmt = null;
				String CONNECTION_NAME = "DBConn";
				String reg_emp = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));  
				String reg_unit = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));  
				String current_ymd =  Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_date , 'yyyymmdd'), '99999999') -19110000", "", "", CONNECTION_NAME));
				String currentTime = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_timestamp , 'hh24mi'), '9999')", "", "", "DBConn"));
				String currentTimeSS = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_timestamp , 'hh24miss'), '999999')", "", "", "DBConn"));
				String acc_job = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));  
				String current_ym = current_ymd.substring(0,5);
				String current_ymdhmi = current_ymd + currentTimeSS;
				long currentYmdhmiLong = Long.parseLong(current_ymdhmi);

				try{
					jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
					String servletPath, queryString;
					String INSERT_SQL = "";
					String returningClause = " RETURNING CASE_ID"; 
					
					if(!StringUtils.isEmpty(c_CASE_ID)){ //存檔(畫面資料留用)-複製IBMCASE資料
						 INSERT_SQL = "INSERT INTO IBMCASE(AD_TYP, RVLDATE, AD_NAME, DIS_B_ADDZON, DIS_B_ADD1, DIS_B_ADD2, DIS_B_ADD3, DIS_B_ADD4, DIS_B_ADD5"
                        + ", DIS_B_ADD6, DIS_B_ADD6_1, DIS_B_ADD7, DIS_B_ADD7_1, DIS_B_ADD8, DIS_B_ADD9, DIS_B_FL, DIS_B_WAY"
                        + ", DIS_B_ADD_DESC, BUILDING_KIND, BUILDING_HEIGHT, AD_KIND, AD_KIND_MEMO, AD_CHK_LAW_MM, AD_CHK_LAW, AD_CHK_RSLT"
                        + ", FINISH_STATE, DIS_TYPE, DIS_TYPE_DESC, DIS_SORT, DIS_SORT_ITEM, REG_DATE, IBM_ITEM, IBM_ITEM_MEMO, CASE_ORI"
                        + ", REG_EMP, REG_UNIT, IB_PRCS, STATUS, CR_DATE, OP_DATE, CHK_CASE_DATE)" // 這些是新設定的欄位
                        + " SELECT AD_TYP, RVLDATE, AD_NAME, DIS_B_ADDZON, DIS_B_ADD1, DIS_B_ADD2, DIS_B_ADD3, DIS_B_ADD4, DIS_B_ADD5"
                        + ", DIS_B_ADD6, DIS_B_ADD6_1, DIS_B_ADD7, DIS_B_ADD7_1, DIS_B_ADD8, DIS_B_ADD9, DIS_B_FL, DIS_B_WAY"
                        + ", DIS_B_ADD_DESC, BUILDING_KIND, BUILDING_HEIGHT, AD_KIND, AD_KIND_MEMO, AD_CHK_LAW_MM, AD_CHK_LAW, AD_CHK_RSLT"
                        + ", FINISH_STATE, DIS_TYPE, DIS_TYPE_DESC, DIS_SORT, DIS_SORT_ITEM, REG_DATE, IBM_ITEM, IBM_ITEM_MEMO, CASE_ORI"
                        + ", ?, ?, 'B', '01', ?, ?, ?" // <-- 對應 REG_EMP, REG_UNIT, CR_DATE, OP_DATE, CHK_CASE_DATE
                        + " FROM IBMCASE WHERE CASE_ID = ?" // <-- 對應 c_CASE_ID
                        + returningClause;

						pstmt = jdbcConn.createPreparedStatement(INSERT_SQL);

						// 依照 ? 的順序設定參數
						int paramIndex = 1;
						pstmt.setString(paramIndex++, reg_emp);           // REG_EMP
						pstmt.setString(paramIndex++, reg_unit);          // REG_UNIT
						// 根據 Sample 的邏輯，current_ymd 傳遞為 String
						pstmt.setString(paramIndex++, current_ymd);       // CR_DATE
						pstmt.setString(paramIndex++, current_ymd);       // OP_DATE
						// CHK_CASE_DATE 使用解析後的 long
						pstmt.setLong(paramIndex++, currentYmdhmiLong); // CHK_CASE_DATE
						pstmt.setString(paramIndex++, c_CASE_ID);         // WHERE CASE_ID = ?
					}
					else{
						INSERT_SQL = "INSERT INTO IBMCASE(REG_EMP, REG_UNIT, IB_PRCS, STATUS, CR_DATE, OP_DATE, CHK_CASE_DATE )"
                        + " VALUES(?, ?, 'B', '01', ?, ?, ?)" // 使用 ? 佔位符
                        + returningClause; // 附加 RETURNING 子句

						pstmt = jdbcConn.createPreparedStatement(INSERT_SQL);

						// 依照 ? 的順序設定參數
						int paramIndex = 1;
						pstmt.setString(paramIndex++, reg_emp);           // REG_EMP
						pstmt.setString(paramIndex++, reg_unit);          // REG_UNIT
						// 根據 Sample 的邏輯，current_ymd 傳遞為 String
						pstmt.setString(paramIndex++, current_ymd);       // CR_DATE
						pstmt.setString(paramIndex++, current_ymd);       // OP_DATE
						// CHK_CASE_DATE 使用解析後的 long
						pstmt.setLong(paramIndex++, currentYmdhmiLong); // CHK_CASE_DATE
					}

					// --- 執行 INSERT 並嘗試獲取返回的 CASE_ID ---
					String caseID = null;
					ResultSet  rs = pstmt.executeQuery();

					if (rs.next()) {
						// 成功從 RETURNING 子句獲取
						caseID = rs.getString("CASE_ID");
						System.err.println("成功取得返回的 CASE_ID: " + caseID); // 正式環境建議使用日誌框架
					} else {
						System.err.println("警告：未從 RETURNING 子句取得任何資料。將嘗試使用舊方法獲取 CASE_ID。");
						caseID = Utils.convertToString(DBTools.dLookUp("MAX(CASE_ID)", "IBMCASE", "CASE_ID like '" + current_ym + "%' and REG_EMP = '"+reg_emp+"' and CHK_CASE_DATE = "+current_ymdhmi, CONNECTION_NAME));
						if(StringUtils.isEmpty(caseID)){
							caseID = current_ym + "00001"; // 最後的備用生成邏輯
							System.err.println("警告：舊方法 dLookUp 也失敗，使用預設生成邏輯: " + caseID);
						} else {
							System.err.println("舊方法成功獲取 CASE_ID: " + caseID);
						}
					}
					
					if(!StringUtils.isEmpty(c_CASE_ID)){ //存檔(畫面資料留用)-複製地號以及違建人資料
						INSERT_SQL = "INSERT INTO IBMCSLAN(CASE_ID, LAND_SEQ, DIST, DIST_DESC, SECTION, SECTION_NM, ROAD_NO1, ROAD_NO2)";
						INSERT_SQL += " SELECT '"+caseID+"', LAND_SEQ, DIST, DIST_DESC, SECTION, SECTION_NM, ROAD_NO1, ROAD_NO2";
						INSERT_SQL += " FROM IBMCSLAN WHERE CASE_ID = '"+c_CASE_ID+"'";
						
						jdbcConn.executeUpdate(INSERT_SQL);
						
						INSERT_SQL = "INSERT INTO IBMDISNM(CASE_ID, CASE_SEQ, IB_USER, USR_ID, USR_ADD, USR_KND, USR_SEX)";
						INSERT_SQL += " SELECT '"+caseID+"', CASE_SEQ, IB_USER, USR_ID, USR_ADD, USR_KND, USR_SEX";
						INSERT_SQL += " FROM IBMDISNM WHERE CASE_ID = '"+c_CASE_ID+"'";
						
						jdbcConn.executeUpdate(INSERT_SQL);
					}
					
					//先預新增一筆ibmsts
					INSERT_SQL = "INSERT INTO IBMSTS(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT)";
					INSERT_SQL += " VALUES('"+caseID+"', "+current_ymd+", "+currentTime+", '"+acc_job+"', '241')";
					jdbcConn.executeUpdate(INSERT_SQL);
					//先預新增一筆ibmfym
					INSERT_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE) ";
					INSERT_SQL += " VALUES('" + caseID + "', " + current_ymd + ", " + currentTime + ", '" + acc_job + "', '241', '" + reg_emp + "', " + current_ymd + ") ";
					jdbcConn.executeUpdate(INSERT_SQL);
					
					//加入參數後重導網頁 
					servletPath = e.getPage().getRequest().getRequestURI();
					queryString = "case_id=" + caseID + "&action_mod=insert";
					e.getPage().setRedirectString(servletPath + "?" + queryString);
				}catch (Exception localException){
					System.err.println("in10101_man_B: AfterInitialize error is " + localException.toString());		
				}finally{
					if( jdbcConn != null )jdbcConn.closeConnection();
				}
			}


//End Event AfterInitialize Action Custom Code

//Event AfterInitialize Action Validate onTimeout_Synct @129-4B8A4259
          if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
//End Event AfterInitialize Action Validate onTimeout_Synct

//im10101_man_B AfterInitialize Method Tail @1-FCB6E20C
        }
//End im10101_man_B AfterInitialize Method Tail

//im10101_man_B OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End im10101_man_B OnInitializeView Method Head

//im10101_man_B OnInitializeView Method Tail @1-FCB6E20C
        }
//End im10101_man_B OnInitializeView Method Tail

//im10101_man_B BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End im10101_man_B BeforeShow Method Head

//im10101_man_B BeforeShow Method Tail @1-FCB6E20C
        }
//End im10101_man_B BeforeShow Method Tail

//im10101_man_B BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End im10101_man_B BeforeOutput Method Head

//im10101_man_B BeforeOutput Method Tail @1-FCB6E20C
        }
//End im10101_man_B BeforeOutput Method Tail

//im10101_man_B BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End im10101_man_B BeforeUnload Method Head

//im10101_man_B BeforeUnload Method Tail @1-FCB6E20C
        }
//End im10101_man_B BeforeUnload Method Tail

//im10101_man_B onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End im10101_man_B onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//im10101_man_B onCache Method Tail @1-FCB6E20C
        }
//End im10101_man_B onCache Method Tail

//im10101_man_B Page Handler Tail @1-FCB6E20C
    }
//End im10101_man_B Page Handler Tail

//BMSDISOBEY_DIST Record Handler Head @2-E0DB5D9D
    public class im10101_man_BBMSDISOBEY_DISTRecordHandler implements RecordListener, RecordDataObjectListener {
//End BMSDISOBEY_DIST Record Handler Head

//BMSDISOBEY_DIST afterInitialize Method Head @2-89E84600
        public void afterInitialize(Event e) {
//End BMSDISOBEY_DIST afterInitialize Method Head

//BMSDISOBEY_DIST afterInitialize Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST afterInitialize Method Tail

//BMSDISOBEY_DIST OnSetDataSource Method Head @2-9B7FBFCF
        public void onSetDataSource(DataObjectEvent e) {
//End BMSDISOBEY_DIST OnSetDataSource Method Head

//BMSDISOBEY_DIST OnSetDataSource Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST OnSetDataSource Method Tail

//BMSDISOBEY_DIST BeforeShow Method Head @2-46046458
        public void beforeShow(Event e) {
//End BMSDISOBEY_DIST BeforeShow Method Head

//BMSDISOBEY_DIST Default value @2-78AAA5BB
            e.getComponent().getControl("zoom_level").setDefaultValue(11);
//End BMSDISOBEY_DIST Default value

//Event BeforeShow Action Custom Code @45-44795B7A



	String CONNECTION_NAME = "DBConn";
	String SEC = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("SEC"));
	String user_id = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID")); 
	String UserJob = "";
	//String UserJob =  Utils.convertToString(DBTools.dLookUp(" CODE_DESC ", "BLDCODE"," CODE_SEQ= '" + SEC + "' and code_type = 'DIV' ", CONNECTION_NAME));
	//e.getRecord().getControl("UserJob").setValue(UserJob);	
	String CASE_ID = Utils.convertToString(e.getRecord().getControl("CASE_ID").getValue());
	String current_ym = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_date , 'yyyymmdd'), '99999999') -19110000", "", "", "DBConn"));;
	e.getRecord().getControl("current_ym").setValue(current_ym);
	
	String DSORT_H = Utils.convertToString(e.getRecord().getControl("DSORT_H").getValue());
	String DSORT2_H = Utils.convertToString(e.getRecord().getControl("DSORT2_H").getValue());
	
	//認定通知單下載預覽顯示必要欄位
	//拆除優先類組
	String dsort = Utils.convertToString(e.getRecord().getControl("DSORT").getValue());
	//違規項目
	String ibm_item = Utils.convertToString(e.getRecord().getControl("IBM_ITEM").getValue());
	//案件來源
	String case_ori = Utils.convertToString(e.getRecord().getControl("CASE_ORI").getValue());
	//改善期限 
	String add_deadline = Utils.convertToString(e.getRecord().getControl("AD_DEADLINE").getValue());

	//show img
	String Img_html = "", Img_html2 = "", zon_area_list_html = "", prjlist_html = "", ANO_TG_html = "", FYMGRID_html = "";
	String dataYear = CASE_ID.substring(3,6);
	String SQL = "", imgSrc = "", setimgHtml = "";
	
	JDBCConnection jdbcConn = null;
	DbRow singleRowData = null;
	DbRow dataRow = null;
	Enumeration dataRows = null; 
	int pic_count = 0;
	try{
		jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		
		//20231211-2需求單 姚佐奇要求新增勘查紀錄表在未有認定通知書之前不顯示下載認定通知書預覽
		
		if(StringUtils.isEmpty(dsort) && StringUtils.isEmpty(ibm_item) && StringUtils.isEmpty(case_ori) && StringUtils.isEmpty(add_deadline))
		{
			e.getRecord().getButton("Button_Print2").setVisible(false);
		}
		
		//show img
		String picType[] = {
			"nowPic",
			"adPic"
		};
		
		int picType_length = picType.length;
		
		for(int i = 0 ; i < picType_length ; i++ ){
			if("pic".equals(picType[i])){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='SKC' order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_img";
			}else if("nowPic".equals(picType[i])){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='NOW' and PIC_SEQ > 2 order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_NOWimg2";
			}else if("strPic".equals(picType[i])){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='STR' order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_STRimg";
			}else if( "adPic".equals(picType[i]) ){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND,SHOWNAME from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='ANO' order by FILENAME";
				imgSrc = "src='img/addattachment.png'  alt='新增附件'";
				setimgHtml = "show_ADimg";
			}else if( "partPic".equals(picType[i]) ){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='PAT' order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_PATimg";
			}else if( "ilegalPic".equals(picType[i]) ){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='ILGPIC' order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_ILGimg";
			}	
			
			dataRows = jdbcConn.getRows(SQL);
			
			if("nowPic".equals(picType[i])){
				Img_html = "<div id = '"+picType[i]+"_title' style='text-align: center;'>";
				Img_html += "<label>其他照片</label>";
				Img_html += "<a src='#' class='pointer' style='font-size: 16px;float: right;' onclick=\"addPic('"+CASE_ID+"','"+picType[i]+"');\">上傳</a>";
				Img_html += "</div>";
			}
			else{
				Img_html = "";
			}
			
			Img_html += "<div class='docs-galley' >";
			Img_html += "<ul style='padding:0;margin:0;list-style-type:none;' class='docs-pictures' id='viewImages'>";
			
			while (dataRows != null && dataRows.hasMoreElements()) {
				
				dataRow = (DbRow)dataRows.nextElement();		
				String FILENAME = Utils.convertToString(dataRow.get("FILENAME")== null ? "" :dataRow.get("FILENAME"));	
				
				if(FILENAME.lastIndexOf("_MAP") == -1  && FILENAME.lastIndexOf("_PIT")  == -1){

					pic_count++;

					String Img_type = FILENAME.substring(FILENAME.lastIndexOf("."));
					String Img_index = Utils.convertToString(dataRow.get("PIC_SEQ"));
					String del_ID = FILENAME.substring(0,FILENAME.lastIndexOf("."));
					String Img_kind = Utils.convertToString(dataRow.get("PIC_KIND"));

					String img_src = "";
					
					boolean isPNG = false;					
					if( ".png".equals(Img_type.toLowerCase()) || ".jpg".equals(Img_type.toLowerCase()) || ".jpeg".equals(Img_type.toLowerCase())) isPNG = true;
					if( !isPNG ){
						img_src ="img/PDF.jpg"  ;
					}else{
						img_src ="in10101_getImage.jsp?EXP_NO=" + CASE_ID + "&Zzip=Y&Img_kind="+Img_kind+"&Img_index="+Img_index + "&time="+ System.currentTimeMillis() ;
					}		
							
					Img_html += "<div id = '"+del_ID+"'  class = 'col-xs-6 col-sm-3 col-md-3 col-lg-2' style='text-align: center; padding-left: 0px;padding-right: 0px;margin: 0 5px;'>";
					Img_html += "<div> <li class='pointer'>";
					
					
					if( !isPNG ){
						Img_html += "<div class='vertical-container' style=\"height:200px;\"><div><img class='all_pic pointer'  style='max-width: 150px;max-height:200px;' onclick=\"window.open('in10101_getImage.jsp?EXP_NO=" + CASE_ID + "&Img_kind=ANO&Img_index="+Img_index + "&time="+ System.currentTimeMillis()+ "');\"  src='"+img_src+"'  alt = '附件_"+Img_index+"'  ></div></div>";
					}else{
						Img_html += "<div  class='vertical-container' style=\"height:200px;\"><div><img class='all_pic pointer'  style='max-width: 150px;max-height:200px;' onclick=\"showPicQ('"+Img_index+"', '"+Img_kind+"', '"+CASE_ID+"');\" src='"+img_src+"'  alt = '照片_"+Img_index+"'  ></div></div>";
					}	
					
					if("adPic".equals(picType[i])){
						String showname = Utils.convertToString(dataRow.get("SHOWNAME"));
						Img_html += "<div>"+showname+"</div>";
						Img_html += "<div><a src='#' class='pointer' onclick=\"del_ADpic('"+CASE_ID+"','"+FILENAME+"');\">刪除</a></div>";
					}else{
						Img_html += "<div><a src='#' class='pointer' onclick=\"editeMode('"+CASE_ID+"','"+Img_index+"','"+Img_kind+"');\">編輯</a></div>";
					}

					Img_html += "</li></div></div>";
					
				}	

			}
					
			

			Img_html += "</ul>";
			Img_html += "</div>";
			
			e.getRecord().getControl(setimgHtml).setValue(Img_html);
		}	
		
		//現勘照片分為主要與局部-分開做
		String editText = "";   //編輯圖片的文字
		String titleText = "";  //圖片的標題文字
		String titleHint = "";  //圖片的標題提示文字
		String upload_seq = ""; //紀錄應該上傳的seq
		
		String picType2[] = {
			"nowPic",
			"partPic"
		};
		
		long count_NOW_PIC1 = 0;
		long count_NOW_PIC2 = 0;
		
		int picType2_length = picType2.length;
		
		for(int i = 0 ; i < picType2_length ; i++ ){
			if("nowPic".equals(picType2[i])){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='NOW' and PIC_SEQ < 3 order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_NOWimg";
				editText = "編輯";
				titleText = "全景照片";
				titleHint = "(限2張, 至少1張)";
				count_NOW_PIC1 = Utils.convertToLong(DBTools.dLookUp("count(*)", "IBMLIST", "SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='NOW' and PIC_SEQ = 1", "DBConn"));
				count_NOW_PIC2 = Utils.convertToLong(DBTools.dLookUp("count(*)", "IBMLIST", "SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='NOW' and PIC_SEQ = 2", "DBConn"));
			}
			else if( "partPic".equals(picType2[i]) ){
				SQL = "select FILENAME,PIC_SEQ,PIC_KIND from IBMLIST where SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='PAT' and PIC_SEQ < 3 order by FILENAME";
				imgSrc = "src='img/addPhoto.png' alt='新增照片'";
				setimgHtml = "show_PATimg";
				editText = "編輯";
				titleText = "局部照片";
				titleHint = "(限2張)";
				count_NOW_PIC1 = Utils.convertToLong(DBTools.dLookUp("count(*)", "IBMLIST", "SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='PAT' and PIC_SEQ = 1", "DBConn"));
				count_NOW_PIC2 = Utils.convertToLong(DBTools.dLookUp("count(*)", "IBMLIST", "SYSTID = 'IBM' and  CASE_ID = '"+CASE_ID+"' and PIC_KIND ='PAT' and PIC_SEQ = 2", "DBConn"));
			}
			
			//依據目前照片存在數量控制上傳的型態
			if(count_NOW_PIC1 == 0 && count_NOW_PIC2 == 0){
				upload_seq = "Y";
			}
			else{
				if(count_NOW_PIC1 > 0 && count_NOW_PIC2 > 0){
					upload_seq = "N";
				}
				else{
					if(count_NOW_PIC1 > 0){
						upload_seq = "2";
					}
					else if(count_NOW_PIC2 > 0){
						upload_seq = "1";
					}
				}
			}	
			
			//重置html
			Img_html2 = "<div id = '"+picType2[i]+"_title' style='text-align: center;'>";
			Img_html2 += "<label>"+titleText+"</label>";
			Img_html2 += "<label style='font-size: 8px;'>"+titleHint+"</label>";
			Img_html2 += "<a src='#' class='pointer' style='font-size: 16px;float: right;' onclick=\"addNowPic('"+CASE_ID+"','"+picType2[i]+"','"+upload_seq+"');\">上傳</a>";
			Img_html2 += "</div>";
			
			Img_html2 += "<div class='docs-galley' >";
			Img_html2 += "<ul style='padding:0;margin:0;list-style-type:none;' class='docs-pictures' id='viewImages'>";
			
			dataRows = jdbcConn.getRows(SQL);
			
			while (dataRows != null && dataRows.hasMoreElements()) {
				dataRow = (DbRow)dataRows.nextElement();	
				
				String FILENAME = Utils.convertToString(dataRow.get("FILENAME")== null ? "" :dataRow.get("FILENAME"));	
				
				if(FILENAME.lastIndexOf("_MAP") == -1  && FILENAME.lastIndexOf("_PIT")  == -1){
					String Img_index = Utils.convertToString(dataRow.get("PIC_SEQ"));
					String Img_kind = Utils.convertToString(dataRow.get("PIC_KIND"));
					String del_ID = FILENAME.substring(0,FILENAME.lastIndexOf("."));
					
					String img_src = "in10101_getImage.jsp?EXP_NO=" + CASE_ID + "&Zzip=Y&Img_kind="+Img_kind+"&Img_index="+Img_index + "&time="+ System.currentTimeMillis();
					
					Img_html2 += "<div id = '"+del_ID+"'  class = 'col-xs-6 col-sm-3 col-md-3 col-lg-2' style='text-align: center; padding-left: 0px;padding-right: 0px;margin-left: 32px; width: 150px;'>";
					Img_html2 += "<div> <li class='pointer'>";
					Img_html2 += "<div  class='vertical-container' style=\"height:200px;\"><div><img class='all_pic pointer'  style='max-width: 150px;max-height:200px;' onclick=\"showPicQ('"+Img_index+"', '"+Img_kind+"', '"+CASE_ID+"');\" src='"+img_src+"'  alt = '照片_"+Img_index+"'  ></div></div>";
					Img_html2 += "<div><a src='#' class='pointer' onclick=\"editeMode('"+CASE_ID+"','"+Img_index+"','"+Img_kind+"');\">"+editText+"</a></div>";
					Img_html2 += "</li></div></div>";
				}
			}
			
			Img_html2 += "</ul>";
			Img_html2 += "</div>";
			
			e.getRecord().getControl(setimgHtml).setValue(Img_html2);
		}
		
		//地段
		int landindex = 0;
		SQL = "select * from IBMCSLAN where CASE_ID = '"+CASE_ID+"' order by LAND_SEQ";
		dataRows = jdbcConn.getRows(SQL);
			
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			landindex++;
			String DIST = Utils.convertToString(dataRow.get("DIST"));
			String DIST_DESC = Utils.convertToString(dataRow.get("DIST_DESC"));
			String SECTION = Utils.convertToString(dataRow.get("SECTION"));
			String SECTION_NM = Utils.convertToString(dataRow.get("SECTION_NM"));
			String ROAD_NO1 = Utils.convertToString(dataRow.get("ROAD_NO1"));
			String ROAD_NO2 = Utils.convertToString(dataRow.get("ROAD_NO2"));
			
			zon_area_list_html += "<div class='ZONE_AREA_LIST' landindex='"+landindex+"' dist='"+DIST+"' dist_desc='"+DIST_DESC+"' section='"+SECTION+"' section_nm='"+SECTION_NM+"' road_no1='"+ROAD_NO1+"' road_no2='"+ROAD_NO2+"'>" + DIST_DESC + SECTION_NM + ROAD_NO1 + "-" +ROAD_NO2 + "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='LAND_DELETE' onclick=\"delLan('"+landindex+"');\"></div>";
		}
		
		if(StringUtils.isEmpty(zon_area_list_html)){
			zon_area_list_html = "<label style='font-size: 6px; color: #625B57;'>(請於上方輸入地號後按+按鈕來加入資料, 系統會協助檢核該地號是否已曾立案)</label>";
		}
		
		e.getRecord().getControl("ZONE_AREA_LIST").setValue(zon_area_list_html);
		
		//專案
		SQL = "select * from IBMCSPRJ where CASE_ID = '"+CASE_ID+"' order by PRJ_CODE";
		dataRows = jdbcConn.getRows(SQL);
		
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			String PRJ_CODE = Utils.convertToString(dataRow.get("PRJ_CODE"));
			String PRJ_NM = Utils.convertToString(dataRow.get("PRJ_NM"));
			
			prjlist_html += "<div class='PROJECT_DATA' prj_code='"+PRJ_CODE+"' prj_nm='"+PRJ_NM+"'>"+ PRJ_NM +  "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='PROJECT_DELETE' onclick=\"delProject('"+PRJ_CODE+"');\"></div>"; 
		}
		e.getRecord().getControl("PRJLIST").setValue(prjlist_html);
		
		//-------------------------
		// 組 違建人
		//---------------------------
	
        
        String showDisnmDiv = "";
        long count_IBMDISNM = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMDISNM", " CASE_ID = '"+CASE_ID+"' ", "DBConn")).longValue();
		SQL = "select IB_USER,USR_ID,USR_ADD,USR_KND, USR_BRTH, USR_SEX, CASE_SEQ from IBMDISNM where CASE_ID = '"+CASE_ID+"' order by CASE_SEQ";
		dataRows = jdbcConn.getRows(SQL);
		int t_seq = 1;
		while (dataRows != null && dataRows.hasMoreElements()) {
					
			dataRow = (DbRow)dataRows.nextElement();		
			String USR_KND = Utils.convertToString(dataRow.get("USR_KND")== null ? "" :dataRow.get("USR_KND"));	
			String USR_SEX = Utils.convertToString(dataRow.get("USR_SEX")== null ? "" :dataRow.get("USR_SEX"));	
			String IB_USER = Utils.convertToString(dataRow.get("IB_USER")== null ? "" :dataRow.get("IB_USER"));	
			String USR_ID = Utils.convertToString(dataRow.get("USR_ID")== null ? "" :dataRow.get("USR_ID"));	
			String USR_ADD = Utils.convertToString(dataRow.get("USR_ADD")== null ? "" :dataRow.get("USR_ADD"));	
			
			String USR_BRTH = Utils.convertToString(dataRow.get("USR_BRTH")== null ? "" :dataRow.get("USR_BRTH"));	
			
			String kindName = "ID_KIND" + t_seq;
			
			showDisnmDiv += "<div class=\"IBMDISNM\" data-seq="+t_seq+">";
			if( "1".equals(USR_KND) ){
				showDisnmDiv += "身分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\" checked >&nbsp;自然人</label><label><input type=\"radio\" value=\"2\" name=\""+kindName+"\">&nbsp;法人</label><br>";
			}else if( "2".equals(USR_KND)){
				showDisnmDiv += "身分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\">&nbsp;自然人</label><label><input type=\"radio\" value=\"2\" name=\""+kindName+"\" checked>&nbsp;法人</label><br>";
			}else{
				showDisnmDiv += "身分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\">&nbsp;自然人</label><label><input type=\"radio\" value=\"2\" name=\""+kindName+"\">&nbsp;法人</label><br>";
			}
			
			kindName = "ID_SEX" + t_seq;
			if( "M".equals(USR_SEX) ){
				showDisnmDiv += "性別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\" checked>&nbsp;男性</label><label><input type=\"radio\" value=\"F\" name=\""+kindName+"\">&nbsp;女性</label><br>";
			}else if( "F".equals(USR_SEX)){
				showDisnmDiv += "性別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\">&nbsp;男性</label><label><input type=\"radio\" value=\"F\" name=\""+kindName+"\" checked>&nbsp;女性</label><br>";
			}else{
				showDisnmDiv += "性別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\">&nbsp;男性</label><label><input type=\"radio\" value=\"F\" name=\""+kindName+"\">&nbsp;女性</label><br>";
			}
	        
	        showDisnmDiv += "身分證字號:&nbsp;<input id=\"BMSDISOBEY_DISTID_NUM\" name=\"ID_NUM\" maxlength=\"10\" size=\"10\" value=\""+USR_ID+"\">&nbsp;(法人請填統編)<br>";
	        showDisnmDiv += "姓名:&nbsp;<input name=\"DIS_U_NAME\" maxlength=\"35\" size=\"40\" placeholder=\"姓名\" class=\"DIS_U_NAME\" value=\""+IB_USER+"\" style=\"MARGIN-TOP: 6px\"><br>";
	        //showDisnmDiv += "地址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"30\" size=\"40\" placeholder=\"地址\" value=\""+USR_ADD+"\">&nbsp;<img alt=\"刪除資料\" class=\"img_add\" src=\"img/DeleteQ.png\" onclick=\"delIbmdisnm('del', this)\">";
	        showDisnmDiv += "地址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"30\" size=\"40\" placeholder=\"地址\" value=\""+USR_ADD+"\" style=\"MARGIN-TOP: 6px\">";
	        showDisnmDiv += "</div>";
			t_seq++;

		}
		String oriDisnmDiv = "<div class=\"IBMDISNM\" data-seq="+t_seq+" >";
        oriDisnmDiv+="身分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\"ID_KIND\">&nbsp;自然人</label><label><input type=\"radio\" value=\"2\" name=\"ID_KIND\">&nbsp;法人</label><br>";
        oriDisnmDiv+="性別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\"ID_SEX\">&nbsp;男性</label><label><input type=\"radio\" value=\"F\" name=\"ID_SEX\">&nbsp;女性</label><br>";
        oriDisnmDiv+="身分證字號:&nbsp;<input id=\"BMSDISOBEY_DISTID_NUM\" name=\"ID_NUM\" maxlength=\"10\" size=\"10\">&nbsp;(法人請填統編)<br>";
        oriDisnmDiv+="姓名:&nbsp;<input name=\"DIS_U_NAME\" maxlength=\"35\" size=\"40\" placeholder=\"姓名\" class=\"DIS_U_NAME\" value=\"廣告物所有人\" style=\"MARGIN-TOP: 6px\"><br>";
        //oriDisnmDiv+="地址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"30\" size=\"40\" placeholder=\"地址\">&nbsp;<img class=\"img_add addMod\" src=\"img/Add2.png\" onclick=\"addNewIbmdisnm(this)\" alt=\"新增資料\">&nbsp;<img alt=\"清除資料\" class=\"img_add addMod\" src=\"img/DeleteQ.png\" onclick=\"delIbmdisnm('clearn', this)\">";
        oriDisnmDiv+="地址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"30\" size=\"40\" placeholder=\"地址\" style=\"MARGIN-TOP: 6px\">";
        oriDisnmDiv+="</div>";
        
        if(count_IBMDISNM == 0){
        	showDisnmDiv += oriDisnmDiv;
        }
		
		e.getRecord().getControl("show_ibmdisnm").setValue(showDisnmDiv);
		
		String REG_EMP = Utils.convertToString(e.getRecord().getControl("REG_EMP").getValue()); 
		String REG_EMP_NAME = Utils.convertToString(DBTools.dLookUp(" EMPNAME ", "IBMUSER"," EMPNO= '" + REG_EMP + "'", CONNECTION_NAME));
		e.getRecord().getControl("REG_EMP").setValue(REG_EMP_NAME);
		
		//違建人清冊連結ANO_TG
		SQL = "select * from IBMLIST where CASE_ID = '"+CASE_ID+"' and PIC_KIND ='ANO_TG' order by pic_seq";
		
		dataRows = jdbcConn.getRows(SQL);
		
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();

			String picname = Utils.convertToString(dataRow.get("PICNAME"));
			String pic_seq = Utils.convertToString(dataRow.get("PIC_SEQ"));
			ANO_TG_html += "<a src='#' class='pointer' onclick=\"window.open('in10101_getImage.jsp?EXP_NO=" + CASE_ID + "&Img_kind=ANO_TG&Img_index=" + pic_seq + "&time="+ System.currentTimeMillis()+ "');\">"+picname+"</a><br>";
		}
		
		if(StringUtils.isEmpty(ANO_TG_html)){
			ANO_TG_html = "<label>無上傳附件</label><br>";
		}
		
		e.getRecord().getControl("ANO_TG").setValue(ANO_TG_html);
		
		//案件狀態
		String ACC_RLT = Utils.convertToString(DBTools.dLookUp("ACC_RLT", "IBMSTS", "CASE_ID = '"+CASE_ID+"'", "DBConn"));
		e.getRecord().getControl("ACC_RLT").setValue(ACC_RLT);
		
	// Find "347" in ibmfym. If find it/them, show 案件歷程
	e.getRecord().getControl("ibmfym_347").setValue( Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "ibmfym", "case_id = '" + CASE_ID + "' AND acc_rlt = '347'", CONNECTION_NAME)).intValue() );
		
		
		//組案件歷程Grid
		FYMGRID_html = "<tr class='Caption'>";
		FYMGRID_html += "<th style='WHITE-SPACE: nowrap' scope='col'>項次</th>";
		FYMGRID_html += "<th style='WHITE-SPACE: nowrap' scope='col'>作業日期</th>";
		FYMGRID_html += "<th style='WHITE-SPACE: nowrap' scope='col'>作業層級</th>";
		FYMGRID_html += "<th style='WHITE-SPACE: nowrap' scope='col'>作業狀態</th>";
		FYMGRID_html += "<th style='WHITE-SPACE: nowrap' scope='col'>備註說明</th>";
		FYMGRID_html += "</tr>";
		FYMGRID_html += "<tr class='Row'>";
		FYMGRID_html += "</tr>";
		
		SQL = "select * from IBMFYM where CASE_ID = '"+CASE_ID+"' order by ACC_SEQ";
		dataRows = jdbcConn.getRows(SQL);
		
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			String acc_seq = Utils.convertToString(dataRow.get("ACC_SEQ"));
			String acc_date = Utils.convertToString(dataRow.get("ACC_DATE"));
			String acc_date_show = acc_date.length() >= 8 ? acc_date.substring(0, (acc_date.length()-4)) + "/" + acc_date.substring(3, 5) + "/" + acc_date.substring(5, 7) : "";		
			String acc_job = Utils.convertToString(dataRow.get("ACC_JOB"));
			String acc_job_show = Utils.convertToString(DBTools.dLookUp("CODE_DESC", "IBMCODE", "CODE_TYPE = 'JBTL' and CODE_SEQ = '"+acc_job+"'", "DBConn"));
			String op_user = Utils.convertToString(dataRow.get("OP_USER"));
			String op_user_show = Utils.convertToString(DBTools.dLookUp("EMPNAME", "IBMUSER", "EMPNO = '"+op_user+"'", "DBConn"));
			String acc_rlt = Utils.convertToString(dataRow.get("ACC_RLT"));
			String acc_rlt_show = Utils.convertToString(DBTools.dLookUp("CODE_DESC", "IBMCODE", "CODE_TYPE = 'RLT' and CODE_SEQ = '"+acc_rlt+"'", "DBConn"));
			String acc_memo = Utils.convertToString(dataRow.get("ACC_MEMO"));
			FYMGRID_html += "<tr class='Row'>";
			FYMGRID_html += "<td style='TEXT-ALIGN: center' width='6%'>"+acc_seq+"</td>";
			FYMGRID_html += "<td style='TEXT-ALIGN: center' width='10%'>"+acc_date_show+"</td>";
			FYMGRID_html += "<td nowrap width='10%'>"+acc_job_show+" - "+op_user_show+"</td>";
			FYMGRID_html += "<td nowrap width='10%'>"+acc_rlt_show+"</td>";
			FYMGRID_html += "<td nowrap width='50%'>"+acc_memo+"</td>";
			FYMGRID_html += "</tr>";
		}
		FYMGRID_html += "<tr class='Footer'>";
		FYMGRID_html += "<td colspan='5'></td>";
		FYMGRID_html += "</tr>";
		
		e.getRecord().getControl("FYMGRID").setValue(FYMGRID_html);
		//判斷有無複製資料
		long count_IBMCASE_CP = Utils.convertToLong(DBTools.dLookUp("count(*)", "IBMCASE_CP", "EMPNO = '"+user_id+"'", "DBConn"));
		
		if(count_IBMCASE_CP > 0){
			e.getRecord().getControl("canPasteCase").setValue("Y");
		}
		else{
			e.getRecord().getControl("canPasteCase").setValue("N");
		}
		
		//認定號碼組合
		String REG_YY = Utils.convertToString(e.getRecord().getControl("REG_YY").getValue()); 
		String REG_NO = Utils.convertToString(e.getRecord().getControl("REG_NO").getValue());
		
		String current_yy = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_date , 'yyyy'), '9999') -1911", "", "", "DBConn"));
		
		if(!StringUtils.isEmpty(REG_YY) || !StringUtils.isEmpty(REG_NO)){
			e.getRecord().getControl("REG_NUM").setValue(REG_YY+REG_NO);
		}
		else{
			//e.getRecord().getControl("REG_NUM").setValue(current_yy);
		}
		
		//判斷目前狀態是否為新增
		String ACTION_MOD = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("action_mod"));
		
		if(!StringUtils.isEmpty(ACTION_MOD)){
			e.getRecord().getControl("ACTION_MOD").setValue(ACTION_MOD);
		}
		
		//新增顯示勘查紀錄號碼
		e.getRecord().getControl("CASE_ID_MEMO").setValue(CASE_ID);
		
		//新增廣告物拆除優先類組為A2
		if(StringUtils.isEmpty(DSORT_H) && StringUtils.isEmpty(DSORT2_H)){
			e.getRecord().getControl("DSORT_H").setValue("A");
			e.getRecord().getControl("DSORT2_H").setValue("2");
		}
		
		String reg_yy = Utils.convertToString(DBTools.dLookUp("reg_yy", "IBMCASE", "case_id = '"+CASE_ID+"'", "DBConn"));
		String reg_no = Utils.convertToString(DBTools.dLookUp("reg_no", "IBMCASE", "case_id = '"+CASE_ID+"'", "DBConn"));
		
		//是否為火災案件
		String notify_date = "", notify_hour = "", notify_minute = "", casualty_quantity = "", notify_num = "", notify_str = "";
		String dis_b_addzon = "", dis_b_add1 = "", dis_b_add2 = "", dis_b_add3 = "", dis_b_add4 = "", dis_b_add5 = "", dis_b_add6 = "", dis_b_add6_1 = "";
		
		SQL = "SELECT * FROM ibmfirecase WHERE reg_yy = '"+reg_yy+"' AND reg_no = '"+reg_no+"' ORDER BY notify_date DESC, notify_hour DESC, notify_minute DESC LIMIT 1";
		singleRowData = jdbcConn.getOneRow(SQL);
		
		if (singleRowData != null) 
		{
			notify_date = Utils.convertToString(singleRowData.get("notify_date") == null ? "" : singleRowData.get("notify_date"));
			notify_hour = Utils.convertToString(singleRowData.get("notify_hour") == null ? "" : singleRowData.get("notify_hour"));
			notify_minute = Utils.convertToString(singleRowData.get("notify_minute") == null ? "" : singleRowData.get("notify_minute"));
			casualty_quantity = Utils.convertToString(singleRowData.get("casualty_quantity") == null ? "" : singleRowData.get("casualty_quantity"));
		}
		
		//通報次數
		SQL = "SELECT * FROM ibmcase WHERE case_id = '"+CASE_ID+"'";
		singleRowData = jdbcConn.getOneRow(SQL);
		
		if (singleRowData != null) 
		{
			dis_b_addzon = Utils.convertToString(singleRowData.get("dis_b_addzon"));
			dis_b_add1 = Utils.convertToString(singleRowData.get("dis_b_add1"));
			dis_b_add2 = Utils.convertToString(singleRowData.get("dis_b_add2"));
			dis_b_add3 = Utils.convertToString(singleRowData.get("dis_b_add3"));
			dis_b_add4 = Utils.convertToString(singleRowData.get("dis_b_add4"));
			dis_b_add5 = Utils.convertToString(singleRowData.get("dis_b_add5"));
			dis_b_add6 = Utils.convertToString(singleRowData.get("dis_b_add6"));
			dis_b_add6_1 = Utils.convertToString(singleRowData.get("dis_b_add6_1"));
		}
		
		SQL = "SELECT reg_yy, reg_no, ROW_NUMBER() OVER(ORDER BY notify_date) as notify_num FROM IBMFIRECASE INNER JOIN IBMFIRECASE_ADDRESS ON IBMFIRECASE.case_id = IBMFIRECASE_ADDRESS.case_id";
		SQL += " "+(StringUtils.isEmpty(dis_b_addzon)? "" : "WHERE dis_b_addzon = '"+dis_b_addzon+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add1)? "" : "AND dis_b_add1 = '"+dis_b_add1+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add2)? "" : "AND dis_b_add2 = '"+dis_b_add2+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add3)? "" : "AND dis_b_add3 = '"+dis_b_add3+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add4)? "" : "AND dis_b_add4 = '"+dis_b_add4+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add5)? "" : "AND dis_b_add5 = '"+dis_b_add5+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add6)? "" : "AND dis_b_add6 = '"+dis_b_add6+"'")+"";
		SQL += " "+(StringUtils.isEmpty(dis_b_add6_1)? "" : "AND dis_b_add6_1 = '"+dis_b_add6_1+"'")+"";
		SQL += " ORDER BY notify_date DESC, notify_hour DESC, notify_minute DESC LIMIT 1";
		
		singleRowData = jdbcConn.getOneRow(SQL);
		if (singleRowData != null) 
		{
			if(reg_yy.equals(Utils.convertToString(singleRowData.get("reg_yy"))) && reg_no.equals(Utils.convertToString(singleRowData.get("reg_no"))))
			{
				notify_str += "第" + Utils.convertToString(singleRowData.get("notify_num")) + "次";
			}
		}
		
		if(!StringUtils.isEmpty(notify_date) && !StringUtils.isEmpty(notify_hour) && !StringUtils.isEmpty(notify_minute))
		{
			notify_str += " 通報時間: " + notify_date.substring(0, 3) + "年" + notify_date.substring(3, 5) + "月" + notify_date.substring(5, 7) + "號";
			notify_str += " 時間: " + notify_hour + "時" + notify_minute + "分";
		}
		
		if(!StringUtils.isEmpty(casualty_quantity))
		{
			notify_str += "         傷亡人數: " + casualty_quantity + "人";
		}
		
		e.getRecord().getControl("IBMFIRECASE").setValue(notify_str);
				
	}catch (Exception localException){
		System.err.println("im10101_man:BeforeShow error is " + localException.toString());		
	}finally{
		if( jdbcConn != null )jdbcConn.closeConnection();
	}

	
	
	/*
	
	String FILENAME = Utils.convertToString(DBTools.dLookUp("FILENAME","BDMLIST"," SYSID ='"+SYSID+"' and INDEX_KEY ='"+GUID+"'  and FILENAME like '%PIT%' ", "DBConn"));
	
	if (!StringUtils.isEmpty(FILENAME)) {
		e.getRecord().getControl("checkImage").setValue("Y");
	}
	*/
	
	//列印
	String PRINT_STATE = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PRINT_STATE"));
	
	if (!StringUtils.isEmpty(PRINT_STATE)) {
	
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PRINT_STATE", "");
		
		e.getRecord().getControl("PRINT_STATE").setValue(PRINT_STATE);
	}
	
	//編輯照片
	String editPicUrl = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("editPicUrl"));
	String img_kind = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("img_kind"));
	String currentScroll = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("currentScroll"));
	if (!StringUtils.isEmpty(editPicUrl) && !StringUtils.isEmpty(img_kind) && !StringUtils.isEmpty(currentScroll)) {
	
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("editPicUrl", "");
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("img_kind", "");
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("currentScroll", "");
		
		e.getRecord().getControl("editPicUrl").setValue(editPicUrl);
		e.getRecord().getControl("editPicParameter").setValue(img_kind);
		e.getRecord().getControl("currentScroll").setValue(currentScroll);
	}
	
	String nowScroll = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("currentScroll"));
	if (!StringUtils.isEmpty(nowScroll)) {
		e.getRecord().getControl("currentScroll").setValue(nowScroll);
	}
	
//End Event BeforeShow Action Custom Code

//BMSDISOBEY_DIST BeforeShow Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeShow Method Tail

//BMSDISOBEY_DIST OnValidate Method Head @2-5F430F8E
        public void onValidate(Event e) {
//End BMSDISOBEY_DIST OnValidate Method Head

//Event OnValidate Action Custom Code @130-44795B7A


	String SUBMIT_STATE = Utils.convertToString(e.getRecord().getControl("SUBMIT_STATE").getValue());
	String CASE_ID = Utils.convertToString(e.getRecord().getControl("CASE_ID").getValue());
	int error = 0;
	boolean printCheck = true;
	if("submit".equals(SUBMIT_STATE) || "synergy".equals(SUBMIT_STATE)){
		//檢查經緯度
		String X_COORDINATE = Utils.convertToString(e.getRecord().getControl("X_COORDINATE").getValue()); 
		String Y_COORDINATE = Utils.convertToString(e.getRecord().getControl("Y_COORDINATE").getValue()); 
		String RVLDATE = Utils.convertToString(e.getRecord().getControl("RVLDATE").getValue()); 
		String AD_TYP = Utils.convertToString(e.getRecord().getControl("AD_TYP").getValue());
		String AD_KIND = Utils.convertToString(e.getRecord().getControl("AD_KIND").getValue());
		String AD_KIND_TYPE = Utils.convertToString(e.getRecord().getControl("AD_KIND_TYPE").getValue());
		String BUILDING_KIND_SHOW = Utils.convertToString(e.getRecord().getControl("BUILDING_KIND_SHOW").getValue());
		String BUILDING_HEIGHT = Utils.convertToString(e.getRecord().getControl("BUILDING_HEIGHT").getValue());
		String AD_CHK_LAW = Utils.convertToString(e.getRecord().getControl("AD_CHK_LAW").getValue());
		//String AD_CHK_RSLT = Utils.convertToString(e.getRecord().getControl("AD_CHK_RSLT").getValue());
		String FINISH_STATE = Utils.convertToString(e.getRecord().getControl("FINISH_STATE").getValue());
		String REG_YY = Utils.convertToString(e.getRecord().getControl("REG_YY").getValue()); 
		String REG_NO = Utils.convertToString(e.getRecord().getControl("REG_NO").getValue());
		String REG_NUM = Utils.convertToString(e.getRecord().getControl("REG_NUM").getValue());
		String REG_DATE = Utils.convertToString(e.getRecord().getControl("REG_DATE").getValue());
		String disnm_all =  Utils.convertToString(e.getRecord().getControl("disnm_all").getValue());
		String DSORT = Utils.convertToString(e.getRecord().getControl("DSORT").getValue());
		String DSORT2 = Utils.convertToString(e.getRecord().getControl("DSORT2").getValue());
		String IBM_ITEM = Utils.convertToString(e.getRecord().getControl("IBM_ITEM").getValue());
		String CASE_ORI = Utils.convertToString(e.getRecord().getControl("CASE_ORI").getValue());
		String AD_NAME = Utils.convertToString(e.getRecord().getControl("AD_NAME").getValue());
		String AD_DEADLINE = Utils.convertToString(e.getRecord().getControl("AD_DEADLINE").getValue());
		String DIS_B_ADDZON = Utils.convertToString(e.getRecord().getControl("DIS_B_ADDZON").getValue());
		long count_nowPic = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMLIST", " CASE_ID = '"+CASE_ID+"' and PIC_KIND = 'NOW' and PIC_SEQ <= 2 ", "DBConn")).longValue();
		
		//String checkImage = Utils.convertToString(e.getRecord().getControl("checkImage").getValue());
		
		/*if(StringUtils.isEmpty(checkImage) || !"Y".equals(checkImage)){
			printCheck = false;	
			e.getRecord().addError("請認確平面示意圖位是否正確.");
		}*/
		
		if( StringUtils.isEmpty(AD_TYP)){		
			e.getRecord().addError("勘查紀錄表 > 欄位 違建流程 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(RVLDATE)){		
			e.getRecord().addError("勘查紀錄表 > 欄位 會勘日期 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(AD_NAME)){		
			e.getRecord().addError("勘查紀錄表 > 欄位 廣告名稱及內容 是必須的.");
			error++;
		}
		
		//檢查地址
		String key[] = {
			"ZONE_CODE",
			"DIS_B_ADDZON",
			"DIS_B_ADD1",
			"DIS_B_ADD2",
			"DIS_B_ADD3",
			"DIS_B_ADD4",
			"DIS_B_ADD5",
			"DIS_B_ADD6",
			"DIS_B_ADD6_1",
			"DIS_B_ADD7",
			"DIS_B_ADD7_1",
			"DIS_B_ADD8",
			"DIS_B_ADD9"
		
		};
		int key_length = key.length;
		String value[] = new String[key_length];
		boolean check_addr =false;
		
		for(int i = 0 ; i < key_length ; i++ ){
			value[i] = Utils.convertToString(e.getRecord().getControl( key[i] ).getValue()); 
			if(!StringUtils.isEmpty(value[i]))check_addr = true;	
		}
		if(!check_addr){
			e.getRecord().addError("勘查紀錄表 > 欄位 座落地點 是必須的.");
			error++;
		}
		else{
			if(StringUtils.isEmpty(DIS_B_ADDZON)){
				e.getRecord().addError("勘查紀錄表 > 欄位 座落地點-行政區 是必須的.");
				error++;
			}
		}
		
		if( !StringUtils.isEmpty(AD_KIND_TYPE) ){
			if( StringUtils.isEmpty(AD_KIND)){		
				if("A".equals(AD_KIND_TYPE)){
					e.getRecord().addError("勘查紀錄表 > 欄位 勘查結果-樹立廣告(屋頂)或(地面) 是必須的.");
					error++;	
				}
				else if("B".equals(AD_KIND_TYPE)){
					e.getRecord().addError("勘查紀錄表 > 欄位 勘查結果-招牌廣告(屋頂)或(地面) 是必須的.");
					error++;
				}
			}
		}
		else{
			e.getRecord().addError("勘查紀錄表 > 欄位 勘查結果-型式 是必須的.");
			error++;	
		}
		
		if( StringUtils.isEmpty(BUILDING_KIND_SHOW) && StringUtils.isEmpty(BUILDING_HEIGHT)){		
			e.getRecord().addError("勘查紀錄表 > 欄位 勘查結果-材質尺寸 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(AD_CHK_LAW)){		
			e.getRecord().addError("勘查紀錄表 > 欄位 勘查結果-涉及違反法條 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(FINISH_STATE) && Integer.parseInt(CASE_ID.substring(0, 3)) < 114){		
			e.getRecord().addError("勘查紀錄表 > 欄位 施工狀態 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(X_COORDINATE) || StringUtils.isEmpty(Y_COORDINATE)  ){
			//20221222 測試機無法連接外部server，因此先註解此檢查 -- begin
			/*e.getRecord().addError("勘查紀錄表 > 欄位 違建位置略圖 是必須的.");
			error++;*/
			//20221222 測試機無法連接外部server，因此先註解此檢查 -- end
		}
		
		// 現勘照片至少上傳一張
		if(count_nowPic < 1){
			e.getRecord().addError("勘查紀錄表 > 欄位 現勘照片(全景照片) 至少上傳一張.");
			error++;
		}
		
		/*if( StringUtils.isEmpty(REG_NUM) || StringUtils.isEmpty(REG_YY) || StringUtils.isEmpty(REG_NO)){		
			e.getRecord().addError("認定通知書 > 欄位 認定號碼 是必須的.");
			error++;
		}*/
		
		if( !StringUtils.isEmpty(REG_NUM)){
			if( REG_NUM.length() != 10 ){
				e.getRecord().addError("認定通知書 > 欄位 認定號碼 長度必須為10碼.");
				error++;
			}
			else{
				long reg_num_exist = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMCASE", " REG_YY || REG_NO = '"+REG_NUM+"' and CASE_ID <> '"+CASE_ID+"' ", "DBConn")).longValue();
				// 認定號碼已存在
				if(reg_num_exist > 0){
					e.getRecord().addError("認定通知書 > 欄位 認定號碼 該認定號碼已存在，請重新輸入.");
					error++;
				}
			}
		}
		
		/*if( StringUtils.isEmpty(REG_DATE)){		
			e.getRecord().addError("認定通知書 > 欄位 認定發文日期字號 是必須的.");
			error++;
		}*/
		
		if( StringUtils.isEmpty(disnm_all)){		
			e.getRecord().addError("認定通知書 > 欄位 違建人 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(DSORT) || StringUtils.isEmpty(DSORT2)){		
			e.getRecord().addError("認定通知書 > 欄位 拆除優先類組 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(IBM_ITEM)){		
			e.getRecord().addError("認定通知書 > 欄位 違規項目 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(AD_DEADLINE)){		
			e.getRecord().addError("認定通知書 > 欄位 改善期限 是必須的.");
			error++;
		}
		
		if( StringUtils.isEmpty(CASE_ORI)){		
			e.getRecord().addError("認定通知書 > 欄位 案件來源 是必須的.");
			error++;
		}
	}
	//如果是列印的話回傳值去前端
	else if("prt1".equals(SUBMIT_STATE) || "prt2".equals(SUBMIT_STATE)){
		if(error == 0){
			if("prt1".equals(SUBMIT_STATE)){
				SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PRINT_STATE", "gogodownload");
			}
			else if("prt2".equals(SUBMIT_STATE)){
				SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("PRINT_STATE", "gogodownload2");
			}
		}
	}
	//組成in10101_man_4.jsp?EXP_NO=' + _EXP_NO + '&Img_index=' + _pic_count + '&Img_kind=' + _pic_kind + '&program_id=im10101_man
	else if("editPic".equals(SUBMIT_STATE)){
		String editPicPar = Utils.convertToString(e.getRecord().getControl("editPicParameter").getValue());
		String currentScroll = Utils.convertToString(e.getRecord().getControl("currentScroll").getValue());
		String[] parArray = editPicPar.split("-");
		String case_id = "", img_index = "", img_kind = "";
		
		case_id = parArray[0];
		img_index = parArray[1];
		img_kind = parArray[2];
			
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("editPicUrl", "in10101_man_4.jsp?EXP_NO=" + case_id + "&Img_index=" + img_index + "&Img_kind=" + img_kind + "&currentScroll=" + currentScroll +"&program_id=im10101_man");
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("img_kind",img_kind);
		SessionStorage.getInstance(e.getPage().getRequest()).setAttribute("currentScroll", currentScroll);					
	}
	else if("delete".equals(SUBMIT_STATE)){
		String ACC_RLT = Utils.convertToString(e.getRecord().getControl("ACC_RLT").getValue());
		// 允許刪除的狀態：初建、已完成、協同中、陳核中 (實現作廢機制)
		if("241".equals(ACC_RLT) || "24b".equals(ACC_RLT) || "244".equals(ACC_RLT) || "242".equals(ACC_RLT)){
			//can delete (void)
		}
		else{
			e.getRecord().addError("此案件狀態不允許刪除.");
		}
	}

//End Event OnValidate Action Custom Code

//BMSDISOBEY_DIST OnValidate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST OnValidate Method Tail

//BMSDISOBEY_DIST BeforeSelect Method Head @2-E5EC9AD3
        public void beforeSelect(Event e) {
//End BMSDISOBEY_DIST BeforeSelect Method Head

//BMSDISOBEY_DIST BeforeSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeSelect Method Tail

//BMSDISOBEY_DIST BeforeBuildSelect Method Head @2-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeBuildSelect Method Head

//BMSDISOBEY_DIST BeforeBuildSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeBuildSelect Method Tail

//BMSDISOBEY_DIST BeforeExecuteSelect Method Head @2-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeExecuteSelect Method Head

//BMSDISOBEY_DIST BeforeExecuteSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeExecuteSelect Method Tail

//BMSDISOBEY_DIST AfterExecuteSelect Method Head @2-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End BMSDISOBEY_DIST AfterExecuteSelect Method Head

//BMSDISOBEY_DIST AfterExecuteSelect Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterExecuteSelect Method Tail

//BMSDISOBEY_DIST BeforeInsert Method Head @2-75B62B83
        public void beforeInsert(Event e) {
//End BMSDISOBEY_DIST BeforeInsert Method Head

//BMSDISOBEY_DIST BeforeInsert Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeInsert Method Tail

//BMSDISOBEY_DIST BeforeBuildInsert Method Head @2-FD6471B0
        public void beforeBuildInsert(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeBuildInsert Method Head

//BMSDISOBEY_DIST BeforeBuildInsert Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeBuildInsert Method Tail

//BMSDISOBEY_DIST BeforeExecuteInsert Method Head @2-4EB41272
        public void beforeExecuteInsert(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeExecuteInsert Method Head

//BMSDISOBEY_DIST BeforeExecuteInsert Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeExecuteInsert Method Tail

//BMSDISOBEY_DIST AfterExecuteInsert Method Head @2-C4572C5E
        public void afterExecuteInsert(DataObjectEvent e) {
//End BMSDISOBEY_DIST AfterExecuteInsert Method Head

//BMSDISOBEY_DIST AfterExecuteInsert Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterExecuteInsert Method Tail

//BMSDISOBEY_DIST AfterInsert Method Head @2-767A9165
        public void afterInsert(Event e) {
//End BMSDISOBEY_DIST AfterInsert Method Head

//BMSDISOBEY_DIST AfterInsert Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterInsert Method Tail

//BMSDISOBEY_DIST BeforeUpdate Method Head @2-33A3CFAC
        public void beforeUpdate(Event e) {
//End BMSDISOBEY_DIST BeforeUpdate Method Head

//BMSDISOBEY_DIST BeforeUpdate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeUpdate Method Tail

//BMSDISOBEY_DIST BeforeBuildUpdate Method Head @2-37688606
        public void beforeBuildUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeBuildUpdate Method Head

//BMSDISOBEY_DIST Default Values for Update Query (Table) @2-4620B87C
            try {
                ((SqlParameter)e.getParameter("reg_yy")).setDefaultValue(" ");
            } catch(java.text.ParseException ignore) {}
            try {
                ((SqlParameter)e.getParameter("reg_no")).setDefaultValue(" ");
            } catch(java.text.ParseException ignore) {}
            try {
                ((SqlParameter)e.getParameter("reg_rsult")).setValue("01");
            } catch(java.text.ParseException ignore) {}
//End BMSDISOBEY_DIST Default Values for Update Query (Table)

//BMSDISOBEY_DIST BeforeBuildUpdate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeBuildUpdate Method Tail

//BMSDISOBEY_DIST BeforeExecuteUpdate Method Head @2-84B8E5C4
        public void beforeExecuteUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeExecuteUpdate Method Head

//BMSDISOBEY_DIST BeforeExecuteUpdate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeExecuteUpdate Method Tail

//BMSDISOBEY_DIST AfterExecuteUpdate Method Head @2-0E5BDBE8
        public void afterExecuteUpdate(DataObjectEvent e) {
//End BMSDISOBEY_DIST AfterExecuteUpdate Method Head

//BMSDISOBEY_DIST AfterExecuteUpdate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterExecuteUpdate Method Tail

//BMSDISOBEY_DIST AfterUpdate Method Head @2-306F754A
        public void afterUpdate(Event e) {
//End BMSDISOBEY_DIST AfterUpdate Method Head

//Event AfterUpdate Action Custom Code @101-44795B7A

	String SUBMIT_STATE = Utils.convertToString(e.getRecord().getControl("SUBMIT_STATE").getValue());
	String CASE_ID = Utils.convertToString(e.getRecord().getControl("CASE_ID").getValue()); 
	String currentDate = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_date , 'yyyymmdd'), '99999999') -19110000", "", "", "DBConn"));
	String currentTime = Utils.convertToString(DBTools.dLookUp("to_number(to_char(current_timestamp , 'hh24mi'), '9999')", "", "", "DBConn"));
	String currentDateTime = Utils.convertToString(DBTools.dLookUp("to_char(current_timestamp, 'YYYYMMDDHH24MISS')::bigint - 19110000000000", "", "", "DBConn"));
	String op_user = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));  
	String jobTitle = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));  
	String UPDATE_SQL = "", DELETE_SQL = "", INSERT_SQL = "", acc_rlt = "";
	JDBCConnection jdbcConn = null;
	
	if("save".equals(SUBMIT_STATE) ){
		try{
			String max_acc_seq = Utils.convertToString(DBTools.dLookUp("MAX(ACC_SEQ)", "IBMFYM", "CASE_ID = '"+CASE_ID+"'", "DBConn"));
			
			UPDATE_SQL = "UPDATE IBMFYM SET UP_DATE = "+currentDate+", UP_USER = '"+op_user+"' WHERE CASE_ID = '"+CASE_ID+"' and ACC_SEQ = "+max_acc_seq;
			//System.err.println("im10101_man_A UPDATE_SQL = "+UPDATE_SQL);
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
			jdbcConn.executeUpdate(UPDATE_SQL);
		}catch (Exception localException){
			System.err.println("im10101_man_B: error is " + localException.toString());		
		}finally{
			if( jdbcConn != null )jdbcConn.closeConnection();
		}
	}
	
	if("submit".equals(SUBMIT_STATE) ){
		try{
			UPDATE_SQL = "UPDATE IBMCASE SET STATUS = '02' WHERE CASE_ID = '"+CASE_ID+"'";
			//System.err.println("im10101_man_A UPDATE_SQL = "+UPDATE_SQL);
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
			jdbcConn.executeUpdate(UPDATE_SQL);
			
			String DELETE_OPENED_SQL = " DELETE from public.caseopened WHERE case_id=";
			DELETE_OPENED_SQL += "'" + CASE_ID + "' ";
			jdbcConn.executeUpdate(DELETE_OPENED_SQL);
		}catch (Exception localException){
			System.err.println("im10101_man_A: error is " + localException.toString());		
		}finally{
			if( jdbcConn != null )jdbcConn.closeConnection();
		}
	}
	
	if("submit".equals(SUBMIT_STATE) || "synergy".equals(SUBMIT_STATE)){
		if("submit".equals(SUBMIT_STATE)){
			acc_rlt = "344";
		}
		else if("synergy".equals(SUBMIT_STATE)){
			acc_rlt = "244";
		}
		
		try{
			INSERT_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE) ";
			INSERT_SQL += " VALUES('" + CASE_ID + "', " + currentDate + ", " + currentTime + ", '" + jobTitle + "', '" + acc_rlt + "', '" + op_user + "', " + currentDate + ") ";
			//System.err.println("im10101_man_B INSERT_SQL = "+INSERT_SQL);
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
			jdbcConn.executeUpdate(INSERT_SQL);
			
			UPDATE_SQL = "UPDATE IBMSTS SET ACC_DATE = "+currentDate+",ACC_TIME = "+currentTime+", ACC_JOB = '"+jobTitle+"',ACC_RLT = '"+acc_rlt+"'  WHERE CASE_ID = '"+CASE_ID+"'";
			jdbcConn.executeUpdate(UPDATE_SQL);
			
		}catch (Exception localException){
			System.err.println("im10101_man_B: error is " + localException.toString());		
		}finally{
			if( jdbcConn != null )jdbcConn.closeConnection();
		}
	}
	
	//塞違建人Table
	try{
		jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		
		//違反土管紀錄
		String new_violation = e.getRecord().getControl("VIOLATION_LAND").getValue() == null ? "" :  Utils.convertToString(e.getRecord().getControl("VIOLATION_LAND").getValue());
		String old_violation = DBTools.dLookUp("violation_land", "ibmviolation_land", "case_id = '"+CASE_ID+"' ORDER BY cr_date DESC LIMIT 1", "DBConn") == null ? "" : Utils.convertToString(DBTools.dLookUp("violation_land", "ibmviolation_land", "case_id = '"+CASE_ID+"' ORDER BY cr_date DESC LIMIT 1", "DBConn"));
		
		if(!old_violation.equals(new_violation))
		{
			UPDATE_SQL = "INSERT INTO ibmviolation_land(case_id, violation_land, cr_date, cr_user) VALUES ('"+CASE_ID+"', "+(!StringUtils.isEmpty(new_violation) ? "'"+new_violation+"'" : null)+", '"+currentDateTime+"', '"+op_user+"')";
			jdbcConn.executeUpdate(UPDATE_SQL);
		}
		
		String disnm_all =  Utils.convertToString(e.getRecord().getControl("disnm_all").getValue());
		long count_IBMDISNM = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMDISNM", " CASE_ID = '"+CASE_ID+"' ", "DBConn")).longValue();
		
		//有填寫違建人姓名才新增
		if(!StringUtils.isEmpty(disnm_all)){
			
			if(count_IBMDISNM > 0){
				DELETE_SQL = "DELETE FROM ibmdisnm WHERE case_id = '"+CASE_ID+"'";
				jdbcConn.executeUpdate(DELETE_SQL);
			}
			
			
			String[] disnm_all_sp = disnm_all.split("!@#");	
			//System.err.println("im10201_lis :   disnm_all_sp.length = " + disnm_all_sp.length);
			for(int i = 0 ; i  < disnm_all_sp.length ; i++){

				String[] CONTENT = disnm_all_sp[i].split("#@!");

				String USR_KND = CONTENT[0];
				String USR_SEX = CONTENT[1];
				String USR_ID = CONTENT[2];
				String USR_ADD = CONTENT[3];
				String IB_USER = CONTENT[4];

				INSERT_SQL = "INSERT INTO ibmdisnm(CASE_ID,IB_USER, USR_ID,USR_ADD,USR_KND, USR_SEX   ) ";
				INSERT_SQL += " VALUES ('"+CASE_ID+"','"+IB_USER+"', '"+USR_ID+"', '"+USR_ADD+"', '"+USR_KND+"', '"+USR_SEX+"' )";
				jdbcConn.executeUpdate(INSERT_SQL);
			}
			
		}
		
	}catch (Exception localException){
			System.err.println("im10101_man_A ibmdisnm: error is " + localException.toString());		
	}finally{
		if( jdbcConn != null )jdbcConn.closeConnection();
	}
	
	//塞地號Table以及專案Table
	try{
		jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		String CSLAN = Utils.convertToString(e.getRecord().getControl("CSLAN").getValue());
		if(!StringUtils.isEmpty(CSLAN)){
			String[] lanArray = CSLAN.split(";");
			String[] lanData = null;
			String DIST = "", DIST_DESC = "", SECTION = "", SECTION_NM = "", ROAD_NO1 = "", ROAD_NO2 = "";
			
			long count_IBMCSLAN = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMCSLAN", " CASE_ID = '"+CASE_ID+"' ", "DBConn")).longValue();
			
			
			//地號Table - IBMCSLAN
			//有資料的話先刪除再加入
			if(count_IBMCSLAN > 0){
				DELETE_SQL = "DELETE FROM IBMCSLAN WHERE CASE_ID = '"+CASE_ID+"'";
				jdbcConn.executeUpdate(DELETE_SQL);
			}
		
			for (int i = 0; i < lanArray.length; i++) {
				lanData = lanArray[i].split("-");
				DIST_DESC = lanData[0];
				SECTION = lanData[1];
				SECTION_NM = lanData[2];
				ROAD_NO1 = lanData[3];
				ROAD_NO2 = lanData[4];
				DIST = lanData[5];
				
				UPDATE_SQL = "INSERT INTO IBMCSLAN(CASE_ID, DIST, DIST_DESC, SECTION, SECTION_NM, ROAD_NO1, ROAD_NO2) ";
				UPDATE_SQL += "VALUES('"+CASE_ID+"', '"+DIST+"', '"+DIST_DESC+"', '"+SECTION+"', '"+SECTION_NM+"', '"+ROAD_NO1+"', '"+ROAD_NO2+"')";
				System.err.println("im10101_man_A UPDATE_SQL = "+UPDATE_SQL);
				jdbcConn.executeUpdate(UPDATE_SQL);
			}
		}
		else{
			DELETE_SQL = "DELETE FROM IBMCSLAN WHERE CASE_ID = '"+CASE_ID+"'";
			jdbcConn.executeUpdate(DELETE_SQL);
		}
		
		
		//專案Table - IBMCSPRJ
		//有資料的話先刪除再加入
		String CSPRJ = Utils.convertToString(e.getRecord().getControl("CSPRJ").getValue());
		
		//System.out.println("CSPRJ: " + CSPRJ);
		
		if(!StringUtils.isEmpty(CSPRJ)){
			String[] prjArray = CSPRJ.split(";");
			String[] prjData = null;
			String PRJ_CODE = "", PRJ_YY = "", PRJ_NM = "", PRJFEE = "", PRJFEE_DESC = "";
			long count_IBMCSPRJ = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMCSPRJ", " CASE_ID = '"+CASE_ID+"' ", "DBConn")).longValue();
			
			if(count_IBMCSPRJ > 0){
				DELETE_SQL = "DELETE FROM IBMCSPRJ WHERE CASE_ID = '"+CASE_ID+"'";
				jdbcConn.executeUpdate(DELETE_SQL);
			}
			for (int i = 0; i < prjArray.length; i++) {
				prjData = prjArray[i].split("-");
				
				PRJ_CODE = prjData[0];
				PRJ_NM = prjData[1];
				
				if(prjData.length != 2)
				{
					PRJFEE = prjData[2];
					PRJFEE_DESC = prjData[3];
				}
				
				UPDATE_SQL = "INSERT INTO IBMCSPRJ(CASE_ID, PRJ_CODE, PRJ_NM, PRJFEE, PRJFEE_DESC) ";
				UPDATE_SQL += "VALUES('"+CASE_ID+"', '"+PRJ_CODE+"', '"+PRJ_NM+"', '"+PRJFEE+"', '"+PRJFEE_DESC+"')";
				//System.err.println("im10101_man_B UPDATE_SQL = "+UPDATE_SQL);
				jdbcConn.executeUpdate(UPDATE_SQL);
			}
		}
		else{
			DELETE_SQL = "DELETE FROM IBMCSPRJ WHERE CASE_ID = '"+CASE_ID+"'";
			jdbcConn.executeUpdate(DELETE_SQL);
		}
		
		// 新增塞入完整地址以及查詢用地址
		String comb_addr = Utils.convertToString(DBTools.dLookUp("comb_addr_ntpc(case_id)", " IBMCASE", " CASE_ID = '"+CASE_ID+"' ", "DBConn"));
		// 將完整地址修改為查詢用地址(替換掉特定字元)
		String[] SPECIFIC_CHARACTERS = {"鄉", "市", "鎮", "區", "村", "里", "鄰", "街", "路"};
		int specificCharactersLen = SPECIFIC_CHARACTERS.length;
		String search_addr = "";
		
		if (!StringUtils.isEmpty(comb_addr)) {
			search_addr = comb_addr;
			
			for (int idx = 0; idx < specificCharactersLen; idx++) {
				search_addr = StringUtils.replace(search_addr, SPECIFIC_CHARACTERS[idx], "");
			}
		}
		
		UPDATE_SQL = "UPDATE IBMCASE SET CADDRESS = '"+comb_addr+"', CADD_SRCH = '"+search_addr+"' WHERE CASE_ID = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(UPDATE_SQL);
		
		//存檔(畫面資料留用)
		if("saveInsert".equals(SUBMIT_STATE)){
			String servletPath, queryString;
			//加入參數後重導網頁 
			servletPath = e.getPage().getRequest().getRequestURI();
			queryString = "saveInsert="+CASE_ID;
			e.getPage().setRedirectString(servletPath + "?" + queryString);
		}
		
	}catch (Exception localException){
			System.err.println("im10101_man_B: error is " + localException.toString());		
	}finally{
		if( jdbcConn != null )jdbcConn.closeConnection();
	}
	
	

	
	

//End Event AfterUpdate Action Custom Code

//BMSDISOBEY_DIST AfterUpdate Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterUpdate Method Tail

//BMSDISOBEY_DIST BeforeDelete Method Head @2-752E3118
        public void beforeDelete(Event e) {
//End BMSDISOBEY_DIST BeforeDelete Method Head

//BMSDISOBEY_DIST BeforeDelete Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeDelete Method Tail

//BMSDISOBEY_DIST BeforeBuildDelete Method Head @2-01A46505
        public void beforeBuildDelete(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeBuildDelete Method Head

//BMSDISOBEY_DIST BeforeBuildDelete Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeBuildDelete Method Tail

//BMSDISOBEY_DIST BeforeExecuteDelete Method Head @2-B27406C7
        public void beforeExecuteDelete(DataObjectEvent e) {
//End BMSDISOBEY_DIST BeforeExecuteDelete Method Head

//BMSDISOBEY_DIST BeforeExecuteDelete Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST BeforeExecuteDelete Method Tail

//BMSDISOBEY_DIST AfterExecuteDelete Method Head @2-389738EB
        public void afterExecuteDelete(DataObjectEvent e) {
//End BMSDISOBEY_DIST AfterExecuteDelete Method Head

//BMSDISOBEY_DIST AfterExecuteDelete Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterExecuteDelete Method Tail

//BMSDISOBEY_DIST AfterDelete Method Head @2-76E28BFE
        public void afterDelete(Event e) {
//End BMSDISOBEY_DIST AfterDelete Method Head

//Event AfterDelete Action Custom Code @102-44795B7A

	String CASE_ID = Utils.convertToString(e.getRecord().getControl("CASE_ID").getValue()); 
	String SEPARATOR = System.getProperty("file.separator");
	String picPath = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
	String annFileUploadPath = picPath + CASE_ID.substring(0,3) + SEPARATOR + CASE_ID + SEPARATOR;
	//System.err.println("im10101_man_B annFileUploadPath = "+annFileUploadPath);
	
	String DEL_SQL = "";
	
	JDBCConnection jdbcConn = null;
	DbRow singleRowData = null;
	
	try{
		jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
		
		DEL_SQL = "delete from ibmlist where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		DEL_SQL = "delete from IBMDISNM where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		DEL_SQL = "delete from IBMCSLAN where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		DEL_SQL = "delete from IBMCSPRJ where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		DEL_SQL = "delete from IBMFYM where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		DEL_SQL = "delete from IBMSTS where case_id = '"+CASE_ID+"'";
		jdbcConn.executeUpdate(DEL_SQL);
		
		File fileForDeletion = new File( annFileUploadPath );
		
		deleteAll(fileForDeletion);
		
		
	}catch (Exception localException){
		System.err.println("im10101_man_B: AfterDelete error is " + localException.toString());		
	}finally{
		if( jdbcConn != null )jdbcConn.closeConnection();
	}


//End Event AfterDelete Action Custom Code

//BMSDISOBEY_DIST AfterDelete Method Tail @2-FCB6E20C
        }
//End BMSDISOBEY_DIST AfterDelete Method Tail

//BMSDISOBEY_DIST Record Handler Tail @2-FCB6E20C
    }
//End BMSDISOBEY_DIST Record Handler Tail

//Button_Delete Button Handler Head @6-4384993F
    public class BMSDISOBEY_DISTButton_DeleteButtonHandler implements ButtonListener {
//End Button_Delete Button Handler Head

//Button_Delete OnClick Method Head @6-A9885EEC
        public void onClick(Event e) {
//End Button_Delete OnClick Method Head

//Button_Delete OnClick Method Tail @6-FCB6E20C
        }
//End Button_Delete OnClick Method Tail

//Button_Delete BeforeShow Method Head @6-46046458
        public void beforeShow(Event e) {
//End Button_Delete BeforeShow Method Head

//Button_Delete BeforeShow Method Tail @6-FCB6E20C
        }
//End Button_Delete BeforeShow Method Tail

//Button_Delete Button Handler Tail @6-FCB6E20C
    }
//End Button_Delete Button Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-AF3A9A63
    Page im10101_man_BModel = (Page)request.getAttribute("im10101_man_B_page");
    Page im10101_man_BParent = (Page)request.getAttribute("im10101_man_BParent");
    if (im10101_man_BModel == null) {
        PageController im10101_man_BCntr = new PageController(request, response, application, "/im10101_man_B.xml" );
        im10101_man_BModel = im10101_man_BCntr.getPage();
        im10101_man_BModel.setRelativePath("./");
        //if (im10101_man_BParent != null) {
            //if (!im10101_man_BParent.getChild(im10101_man_BModel.getName()).isVisible()) return;
        //}
        im10101_man_BModel.addPageListener(new im10101_man_BPageHandler());
        ((Record)im10101_man_BModel.getChild("BMSDISOBEY_DIST")).addRecordListener(new im10101_man_BBMSDISOBEY_DISTRecordHandler());
        im10101_man_BCntr.process();
%>
<%
        if (im10101_man_BParent == null) {
            im10101_man_BModel.setCookies();
            if (im10101_man_BModel.redirect()) return;
        } else {
            im10101_man_BModel.redirect();
        }
    }
//End Processing

%>
