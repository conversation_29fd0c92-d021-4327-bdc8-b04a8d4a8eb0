package com.ezek.report;

import com.ezek.utils.EzekUtils;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;

public class IM50401 extends JReportAdapter {
	String[] conditionList = null;

	public synchronized void produceReport(HashMap<String, Object> mParameters) {
		super.produceReport(mParameters);
		try {
			setCONNECTION_NAME("DBConn");

			this.conditionList = ((String[]) this.parameters.get("conditionList"));
			String yyyStr = this.conditionList[0].substring(0, this.conditionList[0].length() - 4);
			int yyy = Integer.parseInt(yyyStr);
			
			this.jrFileNames.add("im50401_114.jasper");
			
			this.hDataNums = new int[] { 1 };
			this.dDataNums = new int[] { 34 };
			this.imgNums = new int[1];
			this.hasHeaderF = new boolean[] { true };
			this.hasImageF = new boolean[1];

			init();
			for (this.index = 0; this.index < this.jrFileNames.size(); this.index += 1) {
				this.jReportData = ((JReportData) this.jReportDataList.get(this.index));
				if (this.jReportData.isHasHeader()) {
					genHeaderSQL();
					this.jReportData.genHeaderData();
					processHeaderData();
				}
				this.data = new ArrayList();
				this.jReportData.setData(this.data);
				genDetailSQL();
				this.jReportData.genDetailData();
				processDetailData();
				if (this.jReportData.isHasImage()) {
					genImageSQL();
					this.jReportData.genImage();
				}
			}
			exportReport();
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(this.CLASS_NAME + ".produceReport Error is " + e);
			if (this.fileOut != null) {
				try {
					this.fileOut.close();
				} catch (IOException ee) {
					ee.printStackTrace();
				}
			}
		} finally {
			if (this.fileOut != null) {
				try {
					this.fileOut.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	protected void genHeaderSQL() {
		this.sql.setLength(0);

		this.sql.append("SELECT '' AS H1");

		this.jReportData.setSql(this.sql);
	}

	protected void processHeaderData() {
		this.jReportData.getHeaderData().put("H1", EzekUtils.formatDate(this.conditionList[0], "YYYMMDD", "MANDARIN")
				+ "至" + EzekUtils.formatDate(this.conditionList[1], "YYYMMDD", "MANDARIN"));
	}

	protected void genDetailSQL() {
		String yyymm = this.conditionList[0].substring(0, this.conditionList[0].length() - 2);

		this.sql.setLength(0);

		this.sql.append(
				"SELECT (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '1' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D1");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '2' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D2");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'B' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D3");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'C' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D4");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'D' AND reg_rsult = '01' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D5");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND (is_closed IS NULL OR is_closed <> '1') AND ibm_item = 'A' AND dis_type = 'D' AND reg_rsult = '02' AND (idntfy_rec_time >= ")
				.append(this.conditionList[0]).append("000000 AND idntfy_rec_time <= ").append(this.conditionList[1])
				.append("240000)) AS D6");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '1' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D7");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '2' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D8");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'B' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D9");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'C' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D10");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'D' AND reg_rsult = '01' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D11");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND (is_closed IS NULL OR is_closed <> '1') AND ibm_item = 'B' AND dis_type = 'D' AND reg_rsult = '02' AND (idntfy_rec_time >= ")
				.append(this.conditionList[0]).append("000000 AND idntfy_rec_time <= ").append(this.conditionList[1])
				.append("240000)) AS D12");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '1' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D13");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'A' AND SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state = '2' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D14");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'B' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D15");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'C' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D16");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'D' AND reg_rsult = '01' AND (reg_rec_date >= ")
				.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
				.append("240000)) AS D17");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND (is_closed IS NULL OR is_closed <> '1') AND ibm_item = 'Z' AND dis_type = 'D' AND reg_rsult = '02' AND (idntfy_rec_time >= ")
				.append(this.conditionList[0]).append("000000 AND idntfy_rec_time <= ").append(this.conditionList[1])
				.append("240000)) AS D18");
		this.sql.append(", (to_char(to_date((('").append(yyymm).append(
				"' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMM')::int - 191100)::text AS D19");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'A' AND (SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state IN ('1', '2') OR SUBSTRING(case_id, 1, 3)::numeric >= 114) AND (reg_rec_date >= (to_char(to_date((('")
			.append(yyymm)
			.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
			.append(yyymm)
			.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D20");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'B' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D21");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'C' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D22");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'D' AND reg_rsult = '01' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D23");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'D' AND reg_rsult = '02' AND (idntfy_rec_time >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND idntfy_rec_time <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '1 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D24");
		this.sql.append(", (to_char(to_date((('").append(yyymm).append(
				"' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMM')::int - 191100)::text AS D25");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'A' AND (SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state IN ('1', '2') OR SUBSTRING(case_id, 1, 3)::numeric >= 114) AND (reg_rec_date >= (to_char(to_date((('")
			.append(yyymm)
			.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
			.append(yyymm)
			.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D26");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'B' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D27");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'C' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D28");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'D' AND reg_rsult = '01' AND (reg_rec_date >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND reg_rec_date <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D29");
		this.sql.append(
				", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'D' AND reg_rsult = '02' AND (idntfy_rec_time >= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 AND idntfy_rec_time <= (to_char(to_date((('")
				.append(yyymm)
				.append("' || '01')::int + 19110000)::text, 'YYYYMMDD') - '2 month'::interval + '1 month'::interval - '1 day'::interval, 'YYYYMMDD')::bigint - 19110000) * 1000000 + 240000)) AS D30");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item IN ('A', 'B', 'Z') AND dis_type = 'A' AND (SUBSTRING(case_id, 1, 3)::numeric < 114 AND finish_state IN ('1', '2') OR SUBSTRING(case_id, 1, 3)::numeric >= 114) AND (reg_rec_date >=")
			.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
			.append("240000)) AS D31");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'A' AND dis_type = 'A' AND (reg_rec_date >= ")
			.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
			.append("240000)) AS D32");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'B' AND dis_type = 'A' AND (reg_rec_date >= ")
			.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
			.append("240000)) AS D33");
		this.sql.append(
			", (SELECT COUNT(*) FROM ibmcase WHERE ib_prcs = 'A' AND status <> '01' AND ibm_item = 'Z' AND dis_type = 'A' AND (reg_rec_date >= ")
			.append(this.conditionList[0]).append("000000 AND reg_rec_date <= ").append(this.conditionList[1])
			.append("240000)) AS D34");			
			
		this.jReportData.setSql(this.sql);
	}

	protected void processDetailData() {
		int idx = 0;
		int detailSize = this.jReportData.getDetailData().size();
		String yyymm = "";
		for (idx = 0; idx < detailSize; idx++) {
			HashMap<String, Object> item = (HashMap) this.jReportData.getDetailData().get(idx);

			yyymm = (String) item.get("D19");
			item.put("D19", EzekUtils.formatDate(yyymm, "YYYMM", "MANDARIN"));

			yyymm = (String) item.get("D25");
			item.put("D25", EzekUtils.formatDate(yyymm, "YYYMM", "MANDARIN"));
			
			this.data.add(new IM50401_Bean((String) item.get("D1"), (String) item.get("D2"), (String) item.get("D3"),
					(String) item.get("D4"), (String) item.get("D5"), (String) item.get("D6"), (String) item.get("D7"),
					(String) item.get("D8"), (String) item.get("D9"), (String) item.get("D10"),
					(String) item.get("D11"), (String) item.get("D12"), (String) item.get("D13"),
					(String) item.get("D14"), (String) item.get("D15"), (String) item.get("D16"),
					(String) item.get("D17"), (String) item.get("D18"), (String) item.get("D19"),
					(String) item.get("D20"), (String) item.get("D21"), (String) item.get("D22"),
					(String) item.get("D23"), (String) item.get("D24"), (String) item.get("D25"),
					(String) item.get("D26"), (String) item.get("D27"), (String) item.get("D28"),
					(String) item.get("D29"), (String) item.get("D30"),(String) item.get("D31"),
					(String) item.get("D32"),(String) item.get("D33"),(String) item.get("D34")));
		}
	}

	public static class IM50401_Bean {
		private String d1;
		private String d2;
		private String d3;
		private String d4;
		private String d5;
		private String d6;
		private String d7;
		private String d8;
		private String d9;
		private String d10;
		private String d11;
		private String d12;
		private String d13;
		private String d14;
		private String d15;
		private String d16;
		private String d17;
		private String d18;
		private String d19;
		private String d20;
		private String d21;
		private String d22;
		private String d23;
		private String d24;
		private String d25;
		private String d26;
		private String d27;
		private String d28;
		private String d29;
		private String d30;
		private String d31;
		private String d32;
		private String d33;
		private String d34;

		public IM50401_Bean(String d1, String d2, String d3, String d4, String d5, String d6, String d7, String d8,
				String d9, String d10, String d11, String d12, String d13, String d14, String d15, String d16,
				String d17, String d18, String d19, String d20, String d21, String d22, String d23, String d24,
				String d25, String d26, String d27, String d28, String d29, String d30,String d31,String d32,String d33,String d34) {
			setD1(d1);
			setD2(d2);
			setD3(d3);
			setD4(d4);
			setD5(d5);
			setD6(d6);
			setD7(d7);
			setD8(d8);
			setD9(d9);
			setD10(d10);
			setD11(d11);
			setD12(d12);
			setD13(d13);
			setD14(d14);
			setD15(d15);
			setD16(d16);
			setD17(d17);
			setD18(d18);
			setD19(d19);
			setD20(d20);
			setD21(d21);
			setD22(d22);
			setD23(d23);
			setD24(d24);
			setD25(d25);
			setD26(d26);
			setD27(d27);
			setD28(d28);
			setD29(d29);
			setD30(d30);
			setD31(d31);
			setD32(d32);
			setD33(d33);
			setD34(d34);
		}

		public void setD1(String d1) {
			this.d1 = d1;
		}

		public String getD1() {
			return this.d1;
		}

		public void setD2(String d2) {
			this.d2 = d2;
		}

		public String getD2() {
			return this.d2;
		}

		public void setD3(String d3) {
			this.d3 = d3;
		}

		public String getD3() {
			return this.d3;
		}

		public void setD4(String d4) {
			this.d4 = d4;
		}

		public String getD4() {
			return this.d4;
		}

		public void setD5(String d5) {
			this.d5 = d5;
		}

		public String getD5() {
			return this.d5;
		}

		public void setD6(String d6) {
			this.d6 = d6;
		}

		public String getD6() {
			return this.d6;
		}

		public void setD7(String d7) {
			this.d7 = d7;
		}

		public String getD7() {
			return this.d7;
		}

		public void setD8(String d8) {
			this.d8 = d8;
		}

		public String getD8() {
			return this.d8;
		}

		public void setD9(String d9) {
			this.d9 = d9;
		}

		public String getD9() {
			return this.d9;
		}

		public void setD10(String d10) {
			this.d10 = d10;
		}

		public String getD10() {
			return this.d10;
		}

		public void setD11(String d11) {
			this.d11 = d11;
		}

		public String getD11() {
			return this.d11;
		}

		public void setD12(String d12) {
			this.d12 = d12;
		}

		public String getD12() {
			return this.d12;
		}

		public void setD13(String d13) {
			this.d13 = d13;
		}

		public String getD13() {
			return this.d13;
		}

		public void setD14(String d14) {
			this.d14 = d14;
		}

		public String getD14() {
			return this.d14;
		}

		public void setD15(String d15) {
			this.d15 = d15;
		}

		public String getD15() {
			return this.d15;
		}

		public void setD16(String d16) {
			this.d16 = d16;
		}

		public String getD16() {
			return this.d16;
		}

		public void setD17(String d17) {
			this.d17 = d17;
		}

		public String getD17() {
			return this.d17;
		}

		public void setD18(String d18) {
			this.d18 = d18;
		}

		public String getD18() {
			return this.d18;
		}

		public void setD19(String d19) {
			this.d19 = d19;
		}

		public String getD19() {
			return this.d19;
		}

		public void setD20(String d20) {
			this.d20 = d20;
		}

		public String getD20() {
			return this.d20;
		}

		public void setD21(String d21) {
			this.d21 = d21;
		}

		public String getD21() {
			return this.d21;
		}

		public void setD22(String d22) {
			this.d22 = d22;
		}

		public String getD22() {
			return this.d22;
		}

		public void setD23(String d23) {
			this.d23 = d23;
		}

		public String getD23() {
			return this.d23;
		}

		public void setD24(String d24) {
			this.d24 = d24;
		}

		public String getD24() {
			return this.d24;
		}

		public void setD25(String d25) {
			this.d25 = d25;
		}

		public String getD25() {
			return this.d25;
		}

		public void setD26(String d26) {
			this.d26 = d26;
		}

		public String getD26() {
			return this.d26;
		}

		public void setD27(String d27) {
			this.d27 = d27;
		}

		public String getD27() {
			return this.d27;
		}

		public void setD28(String d28) {
			this.d28 = d28;
		}

		public String getD28() {
			return this.d28;
		}

		public void setD29(String d29) {
			this.d29 = d29;
		}

		public String getD29() {
			return this.d29;
		}

		public void setD30(String d30) {
			this.d30 = d30;
		}

		public String getD30() {
			return this.d30;
		}

		public void setD31(String d31) {
            this.d31 = d31;
        }

        public String getD31() {
            return this.d31;
		}
		
        public void setD32(String d32) {
            this.d32 = d32;
        }
		
        public String getD32() {
            return this.d32;
        }
		
        public void setD33(String d33) {
            this.d33 = d33;
        }
		
        public String getD33() {
            return this.d33;
        }
		
        public void setD34(String d34) {
            this.d34 = d34;
        }
		
        public String getD34() {
            return this.d34;
        }
	}
}
