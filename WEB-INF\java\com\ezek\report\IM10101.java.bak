package com.ezek.report;

import com.codecharge.db.DBTools;
import com.codecharge.db.DbRow;
import com.codecharge.db.JDBCConnection;
import com.codecharge.db.JDBCConnectionFactory;
import com.codecharge.util.StringUtils;
import com.codecharge.util.Utils;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.HeadlessException;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.Toolkit;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRPdfExporterParameter;
import net.sf.jasperreports.engine.util.JRLoader;
import oracle.net.aso.f;

public class IM10101 {
	protected final String CLASS_NAME = getClass().getSimpleName();
	protected String reportPath;
	protected String LeaderImageRootPath;
	protected String appPath;
	protected String sourceFunc = "";
	protected String ZIP_TAG;
	protected String templeteFolderName = "template";
	File reportFile_1 = null;
	File reportFile_2 = null;
	File reportFile_3 = null;
	File reportFile_4 = null;
	File reportFile_5 = null;
	File reportFile_6 = null;
	File reportFile_7 = null;
	File reportFile_8 = null;
	File reportFile_9 = null;
	File reportFile_10 = null;
	File reportFile_11 = null;
	File reportFile_12 = null;
	File reportFile_13 = null;
	File reportFile_14 = null;
	File reportFile_15 = null;
	File reportFile_16 = null;
	File reportFile_17 = null;
	File reportFile_usrlst = null;
	File reportFile_18 = null;
	File reportFile_19 = null;
	File reportFile_20 = null;

	JasperReport jasperReport_1 = null;
	JasperReport jasperReport_2 = null;
	JasperReport jasperReport_3 = null;
	JasperReport jasperReport_4 = null;
	JasperReport jasperReport_5 = null;
	JasperReport jasperReport_6 = null;
	JasperReport jasperReport_7 = null;
	JasperReport jasperReport_8 = null;
	JasperReport jasperReport_9 = null;
	JasperReport jasperReport_10 = null;
	JasperReport jasperReport_11 = null;
	JasperReport jasperReport_12 = null;
	JasperReport jasperReport_13 = null;
	JasperReport jasperReport_14 = null;
	JasperReport jasperReport_15 = null;
	JasperReport jasperReport_16 = null;
	JasperReport jasperReport_17 = null;
	JasperReport jasperReport_18 = null;
	JasperReport jasperReport_19 = null;
	JasperReport jasperReport_20 = null;

	JasperReport jasperReport_usrlst = null;
	String outExt = "";
	protected StringBuffer sql = new StringBuffer();
	private List<JasperPrint> JasperPrintList = new ArrayList();
	protected static final String PDF_EXT = "PDF";
	protected static final String EXCEL_EXT = "EXL";
	protected static final String RTF_EXT = "RTF";
	protected static final String WORD_EXT = "WRD";
	protected static final String CSV_EXT = "CSV";
	protected static final String ODT_EXT = "ODT";
	protected static final String SEPARATOR = System.getProperty("file.separator");
	protected HashMap<String, Object> parameters;
	FileOutputStream fileOut = null;
	private boolean timeDebug = false;

	public String getReportPath() {
		return this.reportPath;
	}

	public void setReportPath(String reportPath) {
		this.reportPath = reportPath;
	}

	public String getAppPath() {
		return this.appPath;
	}

	public void setAppPath(String appPath) {
		this.appPath = appPath;
	}
	
	public void setSourceFunc(String sourceFunc) { 
		this.sourceFunc = sourceFunc; 
	}
	
	/**
	 * 產生報表（只產生第二聯）
	 * @param mParameters 報表參數
	 */
	public synchronized void produceSecondCopyOnly(HashMap<String, Object> mParameters) {
		// 設定只產生第二聯的參數
		mParameters.put("onlySecondCopy", "Y");
		produceReport(mParameters);
	}

	protected String genTempleteFilePath(String fName) {
		return this.reportPath + "template" + SEPARATOR + fName;
	}

	public void setReset(String paramString) {
		this.appPath = "";
		this.reportPath = "";
	}

	private void iniReportTemplate() {
		try {
			this.reportFile_1 = new File(genTempleteFilePath("im10101_1.jasper"));
			this.reportFile_2 = new File(genTempleteFilePath("im10101_2.jasper"));
			this.reportFile_3 = new File(genTempleteFilePath("im10101_3.jasper"));
			this.reportFile_4 = new File(genTempleteFilePath("im10101_4.jasper"));
			this.reportFile_5 = new File(genTempleteFilePath("im10101_5.jasper"));
			this.reportFile_6 = new File(genTempleteFilePath("im10101_6.jasper"));
			this.reportFile_7 = new File(genTempleteFilePath("im10101_7.jasper"));
			this.reportFile_8 = new File(genTempleteFilePath("im10101_8.jasper"));
			this.reportFile_9 = new File(genTempleteFilePath("im10101_9.jasper"));
			this.reportFile_10 = new File(genTempleteFilePath("im10101_10.jasper"));
			this.reportFile_11 = new File(genTempleteFilePath("im10101_11.jasper"));
			this.reportFile_usrlst = new File(genTempleteFilePath("im10101_usrlst.jasper"));
			this.reportFile_12 = new File(genTempleteFilePath("im10101_12.jasper"));
			this.reportFile_13 = new File(genTempleteFilePath("im10101_13.jasper"));
			this.reportFile_14 = new File(genTempleteFilePath("im10101_5_114.jasper"));
			this.reportFile_15 = new File(genTempleteFilePath("im10101_13_114.jasper"));
			this.reportFile_16 = new File(genTempleteFilePath("im10101_12_114.jasper"));
			this.reportFile_17 = new File(genTempleteFilePath("im10101_10_114.jasper"));
			this.reportFile_18 = new File(genTempleteFilePath("im10101_4_114.jasper"));
			this.reportFile_19 = new File(genTempleteFilePath("im10101_7_114.jasper"));
			this.reportFile_20 = new File(genTempleteFilePath("im10101_8_114.jasper"));

			this.jasperReport_1 = ((JasperReport) JRLoader.loadObject(this.reportFile_1));
			this.jasperReport_2 = ((JasperReport) JRLoader.loadObject(this.reportFile_2));
			this.jasperReport_3 = ((JasperReport) JRLoader.loadObject(this.reportFile_3));
			this.jasperReport_4 = ((JasperReport) JRLoader.loadObject(this.reportFile_4));
			this.jasperReport_5 = ((JasperReport) JRLoader.loadObject(this.reportFile_5));
			this.jasperReport_6 = ((JasperReport) JRLoader.loadObject(this.reportFile_6));
			this.jasperReport_7 = ((JasperReport) JRLoader.loadObject(this.reportFile_7));
			this.jasperReport_8 = ((JasperReport) JRLoader.loadObject(this.reportFile_8));
			this.jasperReport_9 = ((JasperReport) JRLoader.loadObject(this.reportFile_9));
			this.jasperReport_10 = ((JasperReport) JRLoader.loadObject(this.reportFile_10));
			this.jasperReport_11 = ((JasperReport) JRLoader.loadObject(this.reportFile_11));
			this.jasperReport_usrlst = ((JasperReport) JRLoader.loadObject(this.reportFile_usrlst));
			this.jasperReport_12 = ((JasperReport) JRLoader.loadObject(this.reportFile_12));
			this.jasperReport_13 = ((JasperReport) JRLoader.loadObject(this.reportFile_13));
			this.jasperReport_14 = ((JasperReport) JRLoader.loadObject(this.reportFile_14));
			this.jasperReport_15 = ((JasperReport) JRLoader.loadObject(this.reportFile_15));
			this.jasperReport_16 = ((JasperReport) JRLoader.loadObject(this.reportFile_16));
			this.jasperReport_17 = ((JasperReport) JRLoader.loadObject(this.reportFile_17));
			this.jasperReport_18 = ((JasperReport) JRLoader.loadObject(this.reportFile_18));
			this.jasperReport_19 = ((JasperReport) JRLoader.loadObject(this.reportFile_19));
			this.jasperReport_20 = ((JasperReport) JRLoader.loadObject(this.reportFile_20));

		} catch (Exception err) {
			System.err.println("IM10101.class iniReportTemplate()" + err.toString());
		}
	}

	private void addSurveySheet(String case_id, String prtType) {
		addSurveySheet(case_id, prtType, false);
	}
	
	private void addSurveySheet(String case_id, String prtType, boolean onlySecondCopy) {
		System.err.println("IM10101 addSurveySheet: 開始執行，onlySecondCopy = " + onlySecondCopy);
		int casePrefix = Integer.parseInt(case_id.substring(0, 3));
		JasperPrint jasperPrint_T = null;

		int gow = 1;

		String IB_PRCS = Utils
				.convertToString(DBTools.dLookUp("IB_PRCS", "ibmcase", "case_id = '" + case_id + "'", "DBConn"));
		this.sql.setLength(0);
		if (this.timeDebug) {
			prtTime("~addSurveySheet~~1~~~");
		}
		this.sql.append(
				" SELECT A.NEED_PAY AS H1, '242065 新北市新莊區樹新路222號7樓  02-22075911' AS H2, C.code_desc AS H3, A.REG_DATE AS H4");
		if ("C".equals(IB_PRCS)) {
			this.sql.append(" , '新北市政府違章建築拆除大隊' AS H5");
		} else {
			this.sql.append(" , '新北市政府違章建築拆除大隊' AS H5");
		}
		this.sql.append(
				" , '' AS H6, B.IB_USER AS H7, '' AS H8, '' AS H9, B.USR_BRTH AS H10, B.USR_ID AS H11, B.USR_KND as USR_KND");
		this.sql.append(" , B.USR_ADD AS H12");

		this.sql.append(" , CADDRESS AS H13");
		this.sql.append(" , '' as H14, A.X_COORDINATE ||', '|| A.Y_COORDINATE AS H15");
		this.sql.append(" , D.code_desc || '及' ||E.code_desc AS H16");
		this.sql.append(
				" ,'' AS H17, '' AS H18, '' AS H19, A.IBM_ITEM_MEMO AS H20, A.case_id AS H21, A.REG_YY AS H22, A.REG_NO AS H23");
		this.sql.append(" , B.USR_SEX AS H24, A.IBM_ITEM AS H25, '' AS H26, F.code_desc AS H27, G.code_desc AS H28");
		this.sql.append(" , B.IB_USER AS H29");
		this.sql.append(" , '' AS H30, '' AS H31, '' AS H32, '' AS H33, '' AS H34, '' AS H35, '' AS H36");
		this.sql.append(" , '本案違章係屬' || A.DIS_TYPE || '類' || A.DIS_SORT || '組' AS H37");
		this.sql.append(
				" , A.AUDNM_DATE AS H38, A.AUDNM_NUM AS H39, A.BUILDING_CATEGORY AS H40, A.FINISH_STATE AS H41");
		this.sql.append(" , '' AS H42, A.RVLDATE AS H43, '類組：' || A.DIS_TYPE || '類' AS H44, A.BUILDING_COAT AS H45");
		this.sql.append(
				" , A.BUILDING_HEIGHT AS H46, A.BUILDING_AREA AS H47, A.BUILDING_KIND AS H48, '' AS H49, '' AS H50, '' AS H51, A.BUILDING_LENGTH AS H52, A.BUILDING_MEMO AS H53");
		this.sql.append(" , A.BUILDING_LENGTH AS H52, A.BUILDING_MEMO AS H53, '' AS H54, '' AS H55");

		this.sql.append(
				" , A.AD_NAME as H56, '' as H57, '' as H58, A.AD_KIND_MEMO as H59, '' as H60, AD_CHK_LAW_MM as H61 ");
		this.sql.append(" , A.AD_CHK_RSLT as H62, A.AD_KIND as H63, AD_CHK_LAW as H64, A.REG_YY || A.REG_NO as H65");
		this.sql.append(" , H.code_desc as H66");
		this.sql.append(
				" , A.AD_TYP as AD_TYP, A.bid_name as BID_NAME, A.REG_UNIT as REG_UNIT, A.PRJSHOW as PRJSHOW, A.REG_RSULT as REG_RSULT");

		this.sql.append(" , A.ad_deadline as ad_deadline, A.ad_height_up as ad_height_up");
		this.sql.append(" from IBMCASE A left join IBMDISNM B on A.case_id = B.case_id ");
		this.sql.append(" left join IBMCODE C on A.REG_UNIT = C.code_seq and C.code_type = 'IM_UNIT' ");
		this.sql.append(" left join IBMCODE D on A.BUILDING_CATEGORY = D.code_seq and D.code_type = 'BUDCGY' ");
		this.sql.append(" left join IBMCODE E on A.FINISH_STATE = E.code_seq and E.code_type = 'FNHSTT' ");
		this.sql.append(" left join IBMCODE H on A.DIS_B_ADDZON = H.code_seq and H.code_type = 'ZON' ");
		if ("A".equals(IB_PRCS)) {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0101' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0101' ");
		} else if ("B".equals(IB_PRCS)) {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0401' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0401' ");
		} else {
			this.sql.append(" left join IBMCODE F on F.code_type = 'FLNUM' AND F.code_seq = '0301' ");
			this.sql.append(" left join IBMCODE G on G.code_type = 'FLNYY' AND G.code_seq = '0301' ");
		}
		this.sql.append(" where A.case_id = '" + case_id + "' ");

		JDBCConnection jdbcConn = null;
		DbRow dataRow = null;
		DbRow imgDataRow = null;
		Enumeration dataRows = null;
		Enumeration imgDataRows = null;

		LinkedList<HashMap> imageDataList_NOW = new LinkedList();
		LinkedList<HashMap> imageDataList_SKC = null;
		LinkedList<HashMap> imageDataList_ILGPIC = null;
		LinkedList<HashMap> imageDataList_STR = null;
		LinkedList<HashMap> imageDataList_PAT = null;

		String Img_kind = "";
		String actualDirectory = "";
		String imageSQL = " select PICNAME, FILENAME from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
				+ "' and PIC_KIND= 'NOW' order by PIC_SEQ ";

		String imagePIT = "";
		String imageMAP = "";

		String picRootPath = Utils
				.convertToString(DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICTEMPPATH'", "DBConn"));
		this.LeaderImageRootPath = Utils.convertToString(
				DBTools.dLookUp("code_desc", "ibmcode", "code_type = 'PICPATH' and code_seq = 'DOH' ", "DBConn"));
		try {
			jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
			HashMap<String, Object> tmap = null;
			HashMap<String, Object> disnm_map = null;
			HashMap<String, Object> gow_tmap = new HashMap();

			int disnm_cnt = 0;
			String disnm_show = "";
			dataRows = jdbcConn
					.getRows("select IB_USER, USR_ID, USR_ADD from IBMDISNM where  case_id = '" + case_id + "' ");
			disnm_map = new HashMap();
			while ((dataRows != null) && (dataRows.hasMoreElements())) {
				dataRow = (DbRow) dataRows.nextElement();
				String IB_USER = Utils.convertToString(dataRow.get("IB_USER"));
				String USR_ID = Utils.convertToString(dataRow.get("USR_ID"));
				String USR_ADD = Utils.convertToString(dataRow.get("USR_ADD"));
				if (disnm_cnt > 0) {
					disnm_show = disnm_show + "<br>";
				}
				disnm_cnt++;
				disnm_show = disnm_show + "<span>" + disnm_cnt + ".姓名：" + IB_USER + "</span><span>    ，地址：" + USR_ADD
						+ "</span>";
			}
			disnm_map.put("disnm", disnm_show);
			dataRows = null;

			Img_kind = "GEO";
			actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
					+ SEPARATOR;
			imageSQL = " select PICNAME, FILENAME from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
					+ "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
			imgDataRows = jdbcConn.getRows(imageSQL);
			while ((imgDataRows != null) && (imgDataRows.hasMoreElements())) {
				imgDataRow = (DbRow) imgDataRows.nextElement();
				String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
				if ((FILENAME != null) && (FILENAME.contains("_PIT"))) {
					imagePIT = actualDirectory + FILENAME;
				}
				if ((FILENAME != null) && (FILENAME.contains("_MAP"))) {
					imageMAP = actualDirectory + FILENAME;
				}
			}
			Img_kind = "NOW";
			actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
					+ SEPARATOR;
			imageSQL = " select PICNAME, FILENAME  from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
					+ "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
			imgDataRows = jdbcConn.getRows(imageSQL);
			while ((imgDataRows != null) && (imgDataRows.hasMoreElements())) {
				tmap = new HashMap();
				imgDataRow = (DbRow) imgDataRows.nextElement();
				String PICNAME = Utils.convertToString(imgDataRow.get("PICNAME"));
				String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
				tmap.put("PICNAME", PICNAME);
				tmap.put("FILENAME", actualDirectory + FILENAME);
				imageDataList_NOW.add(tmap);
			}
			if ("A".equals(IB_PRCS)) {
				imageDataList_SKC = new LinkedList();

				Img_kind = "SKC";
				actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
						+ SEPARATOR;
				imageSQL = " select PICNAME, FILENAME  from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
						+ "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
				imgDataRows = jdbcConn.getRows(imageSQL);
				while ((imgDataRows != null) && (imgDataRows.hasMoreElements())) {
					tmap = new HashMap();
					imgDataRow = (DbRow) imgDataRows.nextElement();
					String PICNAME = Utils.convertToString(imgDataRow.get("PICNAME"));
					String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
					tmap.put("PICNAME", PICNAME);
					tmap.put("FILENAME", actualDirectory + FILENAME);
					imageDataList_SKC.add(tmap);
				}
				imageDataList_ILGPIC = new LinkedList();

				Img_kind = "ILGPIC";
				actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
						+ SEPARATOR;
				imageSQL = " select PICNAME, FILENAME  from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
						+ "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
				imgDataRows = jdbcConn.getRows(imageSQL);
				while ((imgDataRows != null) && (imgDataRows.hasMoreElements())) {
					tmap = new HashMap();
					imgDataRow = (DbRow) imgDataRows.nextElement();
					String PICNAME = Utils.convertToString(imgDataRow.get("PICNAME"));
					String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
					tmap.put("PICNAME", PICNAME);
					tmap.put("FILENAME", actualDirectory + FILENAME);
					imageDataList_ILGPIC.add(tmap);
				}
				imageDataList_STR = new LinkedList();

				Img_kind = "STR";
				actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
						+ SEPARATOR;
				imageSQL = " select PICNAME, FILENAME, SHOWNAME  from IBMLIST  where SYSTID = 'IBM' and case_id = '"
						+ case_id + "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
				imgDataRows = jdbcConn.getRows(imageSQL);
				while (imgDataRows != null && imgDataRows.hasMoreElements()) {
					tmap = new HashMap();
					imgDataRow = (DbRow) imgDataRows.nextElement();
					String PICNAME = Utils.convertToString(imgDataRow.get("PICNAME"));
					String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
					String SHOWNAME = Utils.convertToString(imgDataRow.get("SHOWNAME"));
					tmap.put("PICNAME", PICNAME);
					tmap.put("FILENAME", actualDirectory + FILENAME);
					tmap.put("SHOWNAME", SHOWNAME);
					imageDataList_STR.add(tmap);
				}
			} else if ("B".equals(IB_PRCS)) {
				imageDataList_PAT = new LinkedList();

				Img_kind = "PAT";
				actualDirectory = picRootPath + case_id.substring(0, 3) + SEPARATOR + case_id + SEPARATOR + Img_kind
						+ SEPARATOR;
				imageSQL = " select PICNAME, FILENAME  from IBMLIST  where SYSTID = 'IBM' and case_id = '" + case_id
						+ "' and PIC_KIND= '" + Img_kind + "' order by PIC_SEQ ";
				imgDataRows = jdbcConn.getRows(imageSQL);
				while ((imgDataRows != null) && (imgDataRows.hasMoreElements())) {
					tmap = new HashMap();
					imgDataRow = (DbRow) imgDataRows.nextElement();
					String PICNAME = Utils.convertToString(imgDataRow.get("PICNAME"));
					String FILENAME = Utils.convertToString(imgDataRow.get("FILENAME"));
					tmap.put("PICNAME", PICNAME);
					tmap.put("FILENAME", actualDirectory + FILENAME);
					imageDataList_PAT.add(tmap);
				}
			}
			
			dataRows = jdbcConn.getRows(this.sql.toString());

			String gowTittle = "";
			String gow_prj_nm = "";
			while ((dataRows != null) && (dataRows.hasMoreElements())) {
				tmap = new HashMap();
				dataRow = (DbRow) dataRows.nextElement();
				for (int i = 1; i <= 66; i++) {
					tmap.put("H" + i, Utils.convertToString(dataRow.get("H" + i)) == null ? " "
							: Utils.convertToString(dataRow.get("H" + i)));
				}
				String AD_TYP = Utils.convertToString(dataRow.get("AD_TYP"));
				String REG_UNIT = Utils.convertToString(dataRow.get("REG_UNIT"));
				String ad_deadline = Utils.convertToString(dataRow.get("ad_deadline"));
				String ad_height_up = Utils.convertToString(dataRow.get("ad_height_up"));
				String BID_NAME = Utils.convertToString(dataRow.get("BID_NAME"));
				String PRJSHOW = Utils.convertToString(dataRow.get("PRJSHOW"));
				String REG_RSULT = Utils.convertToString(dataRow.get("REG_RSULT"));
				

				tmap.put("AD_TYP", AD_TYP);
				tmap.put("IB_PRCS", IB_PRCS);
				tmap.put("REG_UNIT", REG_UNIT);

				tmap.put("ad_deadline", ad_deadline);
				tmap.put("ad_height_up", ad_height_up);
				tmap.put("BID_NAME", BID_NAME);
				tmap.put("PRJSHOW", PRJSHOW);
				tmap.put("REG_RSULT", REG_RSULT);

				tmap = formateData(tmap);
				if (("A".equals(prtType)) || ("ALL".equals(prtType))) {
					String H29 = tmap.get("H29").toString();
					String H7 = tmap.get("H7").toString();
					String H12 = tmap.get("H12").toString();
					gow_prj_nm = tmap.get("H37") == null ? "" : tmap.get("H37").toString();
					if (gow == 1) {
						if (disnm_cnt > 1) {
							gowTittle = H29 + "等 " + disnm_cnt + " 人   詳如違建人清單";
							tmap.put("H29", gowTittle);
							tmap.put("H7", gowTittle);
							tmap.put("H12", H12 + " 等 ");
						} else {
							gowTittle = H29;
						}
					}
					if ("A".equals(IB_PRCS)) {
						if (gow == 1) {
							gow++;

							gow_tmap.putAll(tmap);
							
							// 只產生第二聯時，跳過第一次的處理（主通知書）
							System.err.println("IM10101 addSurveySheet: IB_PRCS=A, gow=1, 檢查是否跳過第一次處理，onlySecondCopy = " + onlySecondCopy);
							if (!onlySecondCopy) {
								if(casePrefix >= 114) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_18, tmap);
								} else {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_4, tmap);
								}
								
								this.JasperPrintList.add(jasperPrint_T);
								if (disnm_cnt > 1) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_usrlst, disnm_map);
									this.JasperPrintList.add(jasperPrint_T);
								}
							}
						}
						if (disnm_cnt > 1) {
							tmap.put("H29", H29);
							tmap.put("H7", H7);
							tmap.put("H12", H12);
						}
						tmap.put("H37", "");
						
						// 只產生第二聯時，跳過第一聯的處理
						System.err.println("IM10101 addSurveySheet: IB_PRCS=A, 檢查是否跳過第一聯處理，onlySecondCopy = " + onlySecondCopy);
						if (!onlySecondCopy) {
							tmap.put("H26", "第一聯（寄違建人）");
							
							if (casePrefix >= 114) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_14, tmap);
							} else {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_5, tmap);
							}
							
							this.JasperPrintList.add(jasperPrint_T);

							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_3, tmap);
							this.JasperPrintList.add(jasperPrint_T);
						}
					} else if ("B".equals(IB_PRCS)) {
						if (gow == 1) {
							gow++;

							gow_tmap.putAll(tmap);
							
							// 只產生第二聯時，跳過第一次的處理（主通知書）
							System.err.println("IM10101 addSurveySheet: IB_PRCS=B, gow=1, 檢查是否跳過第一次處理，onlySecondCopy = " + onlySecondCopy);
							if (!onlySecondCopy) {
								if (casePrefix >= 114) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_16, tmap);
								} else {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_12, tmap);
								}

								this.JasperPrintList.add(jasperPrint_T);
								if (disnm_cnt > 1) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_usrlst, disnm_map);
									this.JasperPrintList.add(jasperPrint_T);
								}
							}
						}
						if (disnm_cnt > 1) {
							tmap.put("H29", H29);
							tmap.put("H7", H7);
							tmap.put("H12", H12);
						}
						tmap.put("H37", "");
						
						// 只產生第二聯時，跳過第一聯的處理
						System.err.println("IM10101 addSurveySheet: IB_PRCS=B, 檢查是否跳過第一聯處理，onlySecondCopy = " + onlySecondCopy);
						if (!onlySecondCopy) {
							tmap.put("H26", "第一聯（寄違建人）");
							
							if (casePrefix >= 114) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_15, tmap);
							} else {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_13, tmap);
							}
							
							this.JasperPrintList.add(jasperPrint_T);

							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_3, tmap);
							this.JasperPrintList.add(jasperPrint_T);
						}
					} else if ("C".equals(IB_PRCS)) {
						if (gow == 1) {
							gow++;

							gow_tmap.putAll(tmap);
							
							// 只產生第二聯時，跳過第一次的處理（主通知書）
							System.err.println("IM10101 addSurveySheet: IB_PRCS=C, gow=1, 檢查是否跳過第一次處理，onlySecondCopy = " + onlySecondCopy);
							if (!onlySecondCopy) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_1, tmap);
								this.JasperPrintList.add(jasperPrint_T);
								if (disnm_cnt > 1) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_usrlst, disnm_map);
									this.JasperPrintList.add(jasperPrint_T);
								}
							}
						}
						if (disnm_cnt > 1) {
							tmap.put("H29", H29);
							tmap.put("H7", H7);
							tmap.put("H12", H12);
						}
						
						// 只產生第二聯時，跳過第一聯的處理
						System.err.println("IM10101 addSurveySheet: IB_PRCS=C, 檢查是否跳過第一聯處理，onlySecondCopy = " + onlySecondCopy);
						if (!onlySecondCopy) {
							tmap.put("H26", "第一聯（寄違建人）");
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_2, tmap);
							this.JasperPrintList.add(jasperPrint_T);
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_3, tmap);
							this.JasperPrintList.add(jasperPrint_T);
						}
					}
				}
				
				if (!onlySecondCopy && (("B".equals(prtType)) || ("ALL".equals(prtType)))) {
					System.err.println("IM10101 addSurveySheet: IB_PRCS="+prtType+", 檢查是否跳過勘查紀錄處理，onlySecondCopy = " + onlySecondCopy);
					if ("A".equals(IB_PRCS)) {
						tmap.put("G1", null);
						tmap.put("G2", null);
						tmap.put("G3", null);
						tmap.put("G4", null);
						if ((imageDataList_ILGPIC.size() > 0) && (imageDataList_SKC.size() > 0)) {
							String tmp_G = ((HashMap) imageDataList_ILGPIC.get(0)).get("FILENAME").toString();
							tmap.put("G1", getImageStream(tmp_G));
							tmp_G = ((HashMap) imageDataList_SKC.get(0)).get("FILENAME").toString();
							tmap.put("G4", getImageStream(tmp_G));
						} else if (imageDataList_ILGPIC.size() > 0) {
							String tmp_G = ((HashMap) imageDataList_ILGPIC.get(0)).get("FILENAME").toString();
							tmap.put("G1", getImageStream(tmp_G));
						} else if (imageDataList_SKC.size() > 0) {
							String tmp_G = ((HashMap) imageDataList_SKC.get(0)).get("FILENAME").toString();
							tmap.put("G1", getImageStream(tmp_G));
						}
						if (!"".equals(imagePIT)) {
							tmap.put("G2", getImageStream(imagePIT));
						}
						if (imageDataList_NOW.size() > 0) {
							String tmp_G = ((HashMap) imageDataList_NOW.get(0)).get("FILENAME").toString();
							tmap.put("G3", getImageStream(tmp_G));
						}
						if ((imageDataList_ILGPIC.size() > 0) && (imageDataList_SKC.size() > 0)) {
							if (casePrefix >= 114) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_20, tmap);
							} else {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_8, tmap);
							}
						
							this.JasperPrintList.add(jasperPrint_T);
						} else {
							if (casePrefix >= 114) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_19, tmap);
							} else {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_7, tmap);
							}
							
							this.JasperPrintList.add(jasperPrint_T);
						}
						tmap.put("H54", "現勘照片");
						tmap.put("H55", "現勘照片");

						tmap.put("G1", null);
						tmap.put("G2", null);
						for (int ingInx = 1; ingInx < imageDataList_NOW.size(); ingInx++) {
							String tmp_G = imageDataList_NOW.get(ingInx) == null ? ""
									: ((HashMap) imageDataList_NOW.get(ingInx)).get("FILENAME").toString();

							tmap.put("G" + (ingInx % 2 == 0 ? 2 : 1), getImageStream(tmp_G));
							if ((ingInx % 2 == 0) || (ingInx + 1 == imageDataList_NOW.size())) {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_9, tmap);
								this.JasperPrintList.add(jasperPrint_T);
								tmap.put("G1", null);
								tmap.put("G2", null);
							}
						}
						prtTime("~A ~~ 接景圖~~~" + imageDataList_STR.size());
						tmap.put("H54", "");
						tmap.put("H55", "");
						tmap.put("G1", null);
						tmap.put("G2", null);
						
						if (imageDataList_STR.size() > 0 && !"IM10401".equals(this.sourceFunc)) {
							System.out.println("SourceFunc:" + this.sourceFunc);
							
							for (int ingInx = 0; ingInx < imageDataList_STR.size(); ingInx++) {
								String tmp_PICNAME = imageDataList_STR.get(ingInx) == null ? ""
										: ((HashMap) imageDataList_STR.get(ingInx)).get("SHOWNAME").toString();

								String tmp_G = imageDataList_STR.get(ingInx) == null ? ""
										: ((HashMap) imageDataList_STR.get(ingInx)).get("FILENAME").toString();

								tmap.put("G" + (ingInx % 2 == 0 ? 1 : 2), getImageStream(tmp_G));

								tmap.put(ingInx % 2 == 0 ? "H54" : "H55", tmp_PICNAME);
								if ((ingInx % 2 == 1) || (ingInx + 1 == imageDataList_STR.size())) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_9, tmap);
									this.JasperPrintList.add(jasperPrint_T);
									tmap.put("G1", null);
									tmap.put("G2", null);
									tmap.put("H54", "");
									tmap.put("H55", "");
								}
							}
						}
					} else if ("B".equals(IB_PRCS)) {
						if (imageDataList_PAT.size() > 0) {
							String G1 = ((HashMap) imageDataList_PAT.get(0)).get("FILENAME").toString();
							tmap.put("G1", getImageStream(G1));
						}
						if (imageDataList_NOW.size() > 0) {
							String G2 = ((HashMap) imageDataList_NOW.get(0)).get("FILENAME").toString();
							tmap.put("G2", getImageStream(G2));
						}
						if ("A".equals(AD_TYP)) {
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_11, tmap);
							this.JasperPrintList.add(jasperPrint_T);
						} else {

							if (casePrefix >= 114) {
								//tmap.put("H56","高風險廣告物"); //需求有寫，廣告名稱為高風險廣告物，而這個欄位為 AD_NAME 依據使用者填寫帶入，如未來有變動，請自行調整
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_17, tmap);
							} else {
								jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_10, tmap);
							}
							
							this.JasperPrintList.add(jasperPrint_T);
						}
					} else if ("C".equals(IB_PRCS)) {
						tmap.put("G1", getImageStream(imagePIT));
						tmap.put("G2", getImageStream(imageMAP));
						String imgPath = "";
						if (imageDataList_NOW.size() > 0) {
							imgPath = ((HashMap) imageDataList_NOW.get(0)).get("FILENAME").toString();
						}
						tmap.put("G3", getImageStream(imgPath));
						jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_6, tmap);
						this.JasperPrintList.add(jasperPrint_T);
					}
					if (imageDataList_NOW.size() > 1) {
						if (("B".equals(IB_PRCS)) || ("C".equals(IB_PRCS))) {
							tmap.put("H54", "現況照片");
							tmap.put("H55", "現況照片");
							tmap.put("G1", null);
							tmap.put("G2", null);
							for (int ingInx = 1; ingInx < imageDataList_NOW.size(); ingInx++) {
								String tmp_G = imageDataList_NOW.get(ingInx) == null ? ""
										: ((HashMap) imageDataList_NOW.get(ingInx)).get("FILENAME").toString();

								tmap.put("G" + (ingInx % 2 == 0 ? 2 : 1), getImageStream(tmp_G));
								if ((ingInx % 2 == 0) || (ingInx + 1 == imageDataList_NOW.size())) {
									jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_9, tmap);
									this.JasperPrintList.add(jasperPrint_T);
									tmap.put("G1", null);
									tmap.put("G2", null);
								}
							}
						}
					}
				}
			}
			if (("A".equals(prtType)) || ("ALL".equals(prtType))) {
				if ("A".equals(IB_PRCS)) {
					// 產生第二聯（移拆除科）
					gow_tmap.put("H26", "第二聯（移拆除科）");
					if (casePrefix >= 114) {
						jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_14, gow_tmap);
					} else {
						jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_5, gow_tmap);
					}
					
					this.JasperPrintList.add(jasperPrint_T);
					
					// 只產生第二聯時，不產生其他聯
					System.err.println("IM10101 addSurveySheet: IB_PRCS=A, prtType=A或ALL, 檢查是否產生其他聯，onlySecondCopy = " + onlySecondCopy);
					if (!onlySecondCopy) {
						gow_tmap.put("H26", "第三聯（郵寄公所）");
						if (casePrefix >= 114) {
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_14, gow_tmap);
						} else {
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_5, gow_tmap);
						}
						
						this.JasperPrintList.add(jasperPrint_T);
					}
				} else if ("B".equals(IB_PRCS)) {
					// 產生第二聯（移拆除科）
					gow_tmap.put("H26", "第二聯（移拆除科）");
					if (casePrefix >= 114) {
						jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_15, gow_tmap);
					} else {
						jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_13, gow_tmap);
					}
					
					this.JasperPrintList.add(jasperPrint_T);
					
					// 只產生第二聯時，不產生其他聯
					System.err.println("IM10101 addSurveySheet: IB_PRCS=B, prtType=A或ALL, 檢查是否產生其他聯，onlySecondCopy = " + onlySecondCopy);
					if (!onlySecondCopy) {
						gow_tmap.put("H26", "第三聯（郵寄公所）");
						if (casePrefix >= 114) {
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_15, gow_tmap);
						} else {
							jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_13, gow_tmap);
						}
						
						this.JasperPrintList.add(jasperPrint_T);
					}
				} else if ("C".equals(IB_PRCS)) {
					// 產生第二聯（勞安科）
					gow_tmap.put("H26", "第二聯（勞安科）");
					jasperPrint_T = JasperFillManager.fillReport(this.jasperReport_2, gow_tmap);
					this.JasperPrintList.add(jasperPrint_T);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(this.CLASS_NAME + ".getRealReg exception is " + e.toString());
		} finally {
			if (jdbcConn != null) {
				jdbcConn.closeConnection();
			}
		}
	}

	private HashMap<String, Object> DbRowToHashMap(DbRow singleRowData, HashMap<String, Object> resultData) {
		if (singleRowData != null) {
			Set QQ1 = singleRowData.keySet();
			try {
				for (Iterator it = QQ1.iterator(); it.hasNext();) {
					Object itNext = it.next();
					if ((itNext instanceof String)) {
						String check_name = Utils.convertToString(itNext);
						if ((check_name.length() > 0) && (!".".equals(check_name.substring(0, 1)))) {
							String name = check_name;
							String value = Utils.convertToString(singleRowData.get(name));
							resultData.put(name, value);
						}
					}
				}
			} catch (Exception e) {
				System.err.println(e.toString());
				e.printStackTrace();
			}
		}
		return resultData;
	}

	private HashMap formateData(HashMap hhmap) {
		try {
			String CASE_ID = hhmap.get("H21").toString();

			String IB_PRCS = hhmap.get("IB_PRCS").toString();

			String AD_TYP = hhmap.get("AD_TYP").toString();

			String NEED_PAY = hhmap.get("H1") == null ? "" : hhmap.get("H1").toString();

			String sd_string = "";

			String BID_NAME = hhmap.get("BID_NAME") == null ? "" : hhmap.get("BID_NAME").toString();

			String PRJSHOW = hhmap.get("PRJSHOW") == null ? "" : hhmap.get("PRJSHOW").toString();

			String bid_name_DESC = "";

			String tmp_Zip_result = "";
			if ("ZIP_TAG".equals(this.ZIP_TAG)) {
				String date = Utils.convertToString(DBTools.dLookUp("acc_date", "ibmfym",
						" CASE_ID = '" + CASE_ID + "' and acc_rlt in ('239', '349', '259') order by acc_seq desc",
						"DBConn"));
				String acc_time = Utils.convertToString(DBTools.dLookUp("acc_time", "ibmfym",
						" CASE_ID = '" + CASE_ID + "' and acc_rlt in ('239', '349', '259') order by acc_seq desc",
						"DBConn"));
				if ((!StringUtils.isEmpty(date)) && (date.length() > 5)) {
					int date_length = date.length();
					date = date.substring(0, date_length - 4) + "年" + date.substring(date_length - 4, date_length - 2)
							+ "月" + date.substring(date_length - 2) + "日";
					date = "中華民國" + date;
					tmp_Zip_result = tmp_Zip_result + date;
				}
				if (!StringUtils.isEmpty(acc_time)) {
					for (int i = 4; i < acc_time.length(); i--) {
						acc_time = "0" + acc_time;
					}
					acc_time = acc_time.substring(0, 2) + "時" + acc_time.substring(2) + "分";
					tmp_Zip_result = tmp_Zip_result + acc_time;
				}
				if (!StringUtils.isEmpty(tmp_Zip_result)) {
					tmp_Zip_result = tmp_Zip_result + "已決行";
				}
			}
			hhmap.put("ZIP_TAG", tmp_Zip_result);
			if ("C".equals(IB_PRCS)) {
				sd_string = sd_string + "違建認定通知書";
				bid_name_DESC = Utils.convertToString(DBTools.dLookUp("code_desc", "ibmcode",
						"code_seq = '" + BID_NAME + "' and code_type= 'BIDNM'", "DBConn"));
				if (!StringUtils.isEmpty(bid_name_DESC)) {
					sd_string = sd_string + "(" + bid_name_DESC + ")";
				}
			} else {
				String H66 = hhmap.get("H66") == null ? "" : hhmap.get("H66").toString();
				String H5 = hhmap.get("H5") == null ? "" : hhmap.get("H5").toString();
				if (!StringUtils.isEmpty(H66)) {
					hhmap.put("H5", "新北市" + H66 + "公所、" + H5);
				}
			}
			if ((!StringUtils.isEmpty(NEED_PAY)) && ("Y".equals(NEED_PAY))) {
				sd_string = sd_string + "含「新北市政府違章建築大隊預估代拆收費通知」";
			}
			hhmap.put("H1", sd_string);

			String REG_RSULT = hhmap.get("REG_RSULT") == null ? "" : hhmap.get("REG_RSULT").toString();
			String H27 = hhmap.get("H27") == null ? "" : hhmap.get("H27").toString();
			if ("02".equals(REG_RSULT)) {
				H27 = "010204";
			}
			if (!StringUtils.isEmpty(H27)) {
				hhmap.put("H27", "檔　　號：" + H27);
			}
			String H28 = hhmap.get("H28") == null ? "" : hhmap.get("H28").toString();
			if (!StringUtils.isEmpty(H28)) {
				if ("02".equals(REG_RSULT)) {
					hhmap.put("H28", "保存年限：永久");
				} else {
					hhmap.put("H28", "保存年限：" + H28 + "年");
				}
			}
			String H14 = Utils.convertToString(DBTools.dLookUp(
					" string_agg(dist_desc || section_nm ||  road_no1 || '－' || road_no2, ';' ORDER BY land_seq) ",
					"ibmcslan ", " CASE_ID = '" + CASE_ID + "' ", "DBConn"));

			hhmap.put("H14", H14);

			String USR_KND = hhmap.get("USR_KND") == null ? "" : hhmap.get("USR_KND").toString();
			String USR_ID = hhmap.get("H11") == null ? "" : hhmap.get("H11").toString();
			if ((!StringUtils.isEmpty(USR_KND)) && ("1".equals(USR_KND)) && (!StringUtils.isEmpty(USR_ID))
					&& (USR_ID.length() > 5)) {
				String USR_ID_hide = "";
				String[] USR_ID_arr = USR_ID.split("");
				for (int i = 0; i < USR_ID_arr.length; i++) {
					if (i < 5) {
						USR_ID_hide = USR_ID_hide + USR_ID_arr[i];
					} else {
						USR_ID_hide = USR_ID_hide + "*";
					}
				}
				hhmap.put("H11", USR_ID_hide);
			}
			String reg_yy = hhmap.get("H22") == null ? "" : hhmap.get("H22").toString();
			String reg_no = hhmap.get("H23") == null ? "" : hhmap.get("H23").toString();
			String REG_UNIT = hhmap.get("REG_UNIT") == null ? "" : hhmap.get("REG_UNIT").toString();
			if (this.timeDebug) {
				prtTime("~AD_TYP~~~~" + AD_TYP);
			}
			if ("C".equals(IB_PRCS)) {
				hhmap.put("H6", "新北拆勞字第" + reg_yy + reg_no + "號");

				String prjNM = Utils.convertToString(DBTools.dLookUp("B.code_desc",
						"ibmcsprj A left join ibmcode B on A.prj_code = B.code_seq and B.code_type = 'PRJNM'",
						"CASE_ID = '" + CASE_ID + "' ", "DBConn"));
				hhmap.put("H21", prjNM);
			} else if ("A".equals(IB_PRCS)) {
				if ("012".equals(REG_UNIT)) {
					hhmap.put("H6", "新北拆認二字第" + reg_yy + reg_no + "號");
				} else {
					hhmap.put("H6", "新北拆認一字第" + reg_yy + reg_no + "號");
				}
				String H37_show = hhmap.get("H37") == null ? "" : hhmap.get("H37").toString();
				String prjNM = Utils.convertToString(DBTools.dLookUp(
						"string_agg(ibmcode.code_desc, '、' ORDER BY ibmcsprj.prj_code)",
						"ibmcsprj LEFT JOIN ibmcode ON (ibmcode.code_type = 'PRJNM' AND ibmcode.code_seq = ibmcsprj.prj_code)",
						"ibmcsprj.case_id ='" + CASE_ID + "'", "DBConn"));
				if ((!StringUtils.isEmpty(prjNM)) && ("Y".equals(PRJSHOW))) {
					H37_show = H37_show + "<br>專案名稱：" + prjNM;
				}
				hhmap.put("H37", H37_show);
			} else if ("B".equals(IB_PRCS)) {
				hhmap.put("H6", "新北拆廣字第" + reg_yy + reg_no + "號");
				String H37_show = hhmap.get("H37") == null ? "" : hhmap.get("H37").toString();
				String prjNM = Utils.convertToString(DBTools.dLookUp(
						"string_agg(ibmcode.code_desc, '、' ORDER BY ibmcsprj.prj_code)",
						"ibmcsprj LEFT JOIN ibmcode ON (ibmcode.code_type = 'PRJNM' AND ibmcode.code_seq = ibmcsprj.prj_code)",
						"ibmcsprj.case_id ='" + CASE_ID + "'", "DBConn"));
				if ((!StringUtils.isEmpty(prjNM)) && ("Y".equals(PRJSHOW))) {
					H37_show = H37_show + "<br>專案名稱：" + prjNM;
				}
				hhmap.put("H37", H37_show);
			}
			String date = hhmap.get("H4") == null ? "" : hhmap.get("H4").toString();
			if (!StringUtils.isEmpty(date)) {
				String LeaderImagePathName = Utils.convertToString(DBTools.dLookUp("filename", "IBMLIST",
						"pic_kind ='DOH' and start_date < " + date + " and " + date + " <= END_DATE order by CR_DATE desc ", "DBConn"));
				BufferedImage LeaderImage = getImageStream(this.LeaderImageRootPath + LeaderImagePathName);
				hhmap.put("LeaderImage", LeaderImage);

				int date_length = date.length();
				date = date.substring(0, date_length - 4) + "年" + date.substring(date_length - 4, date_length - 2) + "月"
						+ date.substring(date_length - 2) + "日";
				date = "中華民國" + date;

				hhmap.put("H4", date);
			} 
			/*
			else {
				String LeaderImagePathName = Utils.convertToString(
						DBTools.dLookUp("filename", "IBMLIST", "pic_kind ='DOH'  order by END_DATE desc ", "DBConn"));
				BufferedImage LeaderImage = getImageStream(this.LeaderImageRootPath + LeaderImagePathName);
				hhmap.put("LeaderImage", LeaderImage);
			}
			*/
			String birthday = hhmap.get("H10") == null ? "" : hhmap.get("H10").toString();
			if (!StringUtils.isEmpty(birthday)) {
				int birthday_length = birthday.length();
				birthday = birthday.substring(0, birthday_length - 4) + "/"
						+ birthday.substring(birthday_length - 4, birthday_length - 2) + "/"
						+ birthday.substring(birthday_length - 2);

				hhmap.put("H10", birthday);
			}
			String usr_id = hhmap.get("H11") == null ? "" : hhmap.get("H11").toString();
			if ((!StringUtils.isEmpty(usr_id)) && (usr_id.length() > 4)) {
				String tmp_usr_id = usr_id.substring(0, 5);
				for (int indx_id = 5; indx_id < usr_id.length(); indx_id++) {
					tmp_usr_id = tmp_usr_id + "*";
				}
				hhmap.put("H11", tmp_usr_id);
			}
			String usr_sex = hhmap.get("H24") == null ? "" : hhmap.get("H24").toString();
			if (!StringUtils.isEmpty(usr_sex)) {
				if ("M".equals(usr_sex)) {
					hhmap.put("H8", "ˇ");
				} else if ("F".equals(usr_sex)) {
					hhmap.put("H9", "ˇ");
				}
			}
			String ibm_item = hhmap.get("H25") == null ? "" : hhmap.get("H25").toString();
			if (!StringUtils.isEmpty(ibm_item)) {
				if ("A".equals(ibm_item)) {
					hhmap.put("H17", "ˇ");
					hhmap.put("H20", "");
				} else if ("B".equals(ibm_item)) {
					hhmap.put("H18", "ˇ");
					hhmap.put("H20", "");
				} else if ("Z".equals(ibm_item)) {
					hhmap.put("H19", "ˇ");
				}
			}
			String audnm_date = hhmap.get("H38") == null ? "" : hhmap.get("H38").toString();
			String audnm_num = hhmap.get("H39") == null ? "" : hhmap.get("H39").toString();
			String audnm_dateNum = "";
			if (!StringUtils.isEmpty(audnm_date)) {
				int dateNum_length = audnm_date.length();
				audnm_date = "中華民國" + audnm_date.substring(0, dateNum_length - 4) + "年"
						+ audnm_date.substring(dateNum_length - 4, dateNum_length - 2) + "月"
						+ audnm_date.substring(dateNum_length - 2) + "日";
			}
			if ((!StringUtils.isEmpty(audnm_date)) && (!StringUtils.isEmpty(audnm_num))) {
				audnm_dateNum = audnm_date + "\n" + audnm_num;
			} else {
				audnm_dateNum = audnm_date + audnm_num;
			}
			hhmap.put("H30", audnm_dateNum);

			String build_category = hhmap.get("H40") == null ? "" : hhmap.get("H40").toString();
			if (!StringUtils.isEmpty(build_category)) {
				if ("1".equals(build_category)) {
					hhmap.put("H31", "ˇ");
				} else if ("2".equals(build_category)) {
					hhmap.put("H33", "ˇ");
				} else if ("3".equals(build_category)) {
					hhmap.put("H32", "ˇ");
				} else if ("4".equals(build_category)) {
					hhmap.put("H34", "ˇ");
				}
			}
			String finish_state = hhmap.get("H41") == null ? "" : hhmap.get("H41").toString();
			if (!StringUtils.isEmpty(finish_state)) {
				if ("1".equals(finish_state)) {
					hhmap.put("H35", "ˇ");
					hhmap.put("H50", "■");
					hhmap.put("H51", "□");
				} else if ("2".equals(finish_state)) {
					hhmap.put("H36", "ˇ");
					hhmap.put("H50", "□");
					hhmap.put("H51", "■");
				}
			} else {
				hhmap.put("H50", "□");
				hhmap.put("H51", "□");
			}
			String rvldate = hhmap.get("H43") == null ? "" : hhmap.get("H43").toString();
			if (!StringUtils.isEmpty(rvldate)) {
				int date_length = rvldate.length();
				rvldate = rvldate.substring(0, date_length - 4) + "年"
						+ rvldate.substring(date_length - 4, date_length - 2) + "月" + rvldate.substring(date_length - 2)
						+ "日";
				hhmap.put("H43", rvldate);
			}
			String addr = hhmap.get("H13") == null ? "" : hhmap.get("H13").toString();

			String H42_str = "";
			if ("C".equals(IB_PRCS)) {
				if (StringUtils.isEmpty(bid_name_DESC)) {
					bid_name_DESC = "";
				}
				H42_str = "勘查日期：" + rvldate + "\n" + "污水下水道用戶接管工程工區：" + bid_name_DESC + "\n" + "勘查地點：" + addr;
			} else if ("A".equals(IB_PRCS)) {
				H42_str = "1.勘查地點：" + addr + "\n" + "2.勘查日期：" + rvldate;
			}
			hhmap.put("H42", H42_str);

			String building_coat = hhmap.get("H45") == null ? "" : hhmap.get("H45").toString();
			String building_height = hhmap.get("H46") == null ? "" : hhmap.get("H46").toString();
			String building_LENGTH = hhmap.get("H52") == null ? "" : hhmap.get("H52").toString();
			String ad_height_up = hhmap.get("ad_height_up") == null ? "" : hhmap.get("ad_height_up").toString();
			String ad_deadline = hhmap.get("ad_deadline") == null ? "" : hhmap.get("ad_deadline").toString();
			if (!StringUtils.isEmpty(building_coat)) {
				building_coat = building_coat + " 層";
			} else {
				building_coat = "  層";
			}
			if (!StringUtils.isEmpty(building_height)) {
				building_height = "約 " + StringTofloat(building_height) + " M";
			} else {
				building_height = "約    M";
			}
			if ((!StringUtils.isEmpty(ad_height_up)) && ("Y".equals(ad_height_up))) {
				building_height = building_height + "以上";
			}
			String lawMemo = "臺端應於文到次日起□日內自行拆除或改善，逾期未改善完成或拆除者，本大隊將強制拆除（行政執行法第二十七條、第二十八條及第三十二條）；違規廣告物拆除時，敷設於違規廣告物上之燈具等配件一併拆除之；違規廣告物拆除後之建築材料應自行清除，逾期不清除或無適當安全堆放空間者，視同廢棄物，依廢棄物清理法規定處理（違章建築處理辦法第七條、第八條）。\r\n上列違規廣告物倘經拆除後，不得違反規定重建；違者依建築法第九十五條規定，處一年以下有期徒刑、拘役或科或併科新臺幣三十萬元以下罰金。\r\n臺端若對本處分書有任何疑義、陳情事項，請檢附陳情書函向本大隊提出申訴、陳情。\r\n對本處分書不服者，於處分書送達次日起三十日內檢送訴願書及本處分書影本至本大隊，層轉訴願管轄機關\r\n提起訴願。";
			if (!StringUtils.isEmpty(ad_deadline)) {
				lawMemo = "臺端應於文到次日起" + convertToChineseNumerals(ad_deadline)
						+ "日內自行拆除或改善，逾期未改善完成或拆除者，本大隊將強制拆除（行政執行法第二十七條、第二十八條及第三十二條）；違規廣告物拆除時，敷設於違規廣告物上之燈具等配件一併拆除之；違規廣告物拆除後之建築材料應自行清除，逾期不清除或無適當安全堆放空間者，視同廢棄物，依廢棄物清理法規定處理（違章建築處理辦法第七條、第八條）。\r\n"
						+ "上列違規廣告物倘經拆除後，不得違反規定重建；違者依建築法第九十五條規定，處一年以下有期徒刑、拘役或科或併科新臺幣三十萬元以下罰金。\r\n"
						+ "臺端若對本處分書有任何疑義、陳情事項，請檢附陳情書函向本大隊提出申訴、陳情。\r\n"
						+ "對本處分書不服者，於處分書送達次日起三十日內檢送訴願書及本處分書影本至本大隊，層轉訴願管轄機關\r\n" + "提起訴願。";
			}
			hhmap.put("lawMemo", lawMemo);
			if (!StringUtils.isEmpty(building_LENGTH)) {
				float f_building_LENGTH = Float.parseFloat(building_LENGTH);
				building_LENGTH = "約 " + StringTofloat(building_LENGTH) + " m";
			} else {
				building_LENGTH = "約    m";
			}
			hhmap.put("H49", building_coat + " " + building_height);
			hhmap.put("H52", building_LENGTH);

			String building_area = hhmap.get("H47") == null ? "" : hhmap.get("H47").toString();
			if (!StringUtils.isEmpty(building_area)) {
				building_area = "約 " + StringTofloat(building_area) + " ㎡";
			} else {
				building_area = "約    ㎡";
			}
			hhmap.put("H45", building_coat);
			hhmap.put("H46", building_height);
			hhmap.put("H47", building_area);

			String AD_KIND = hhmap.get("H63") == null ? "" : hhmap.get("H63").toString();
			if (!StringUtils.isEmpty(AD_KIND)) {
				String H57 = "樹立廣告";
				String H58 = "招牌廣告";
				String H59 = "其　他";
				if (AD_KIND.contains("A")) {
					H57 = "■" + H57;
					H58 = "□" + H58;
					H59 = "□" + H59;

					H57 = H57 + "(";
					boolean AD_KIND_FIRST = true;
					if (AD_KIND.contains("1")) {
						AD_KIND_FIRST = false;
						H57 = H57 + "屋頂";
					}
					if (AD_KIND.contains("2")) {
						if (!AD_KIND_FIRST) {
							H57 = H57 + "、";
						}
						H57 = H57 + "地面";
					}
					H57 = H57 + ")";
				} else if (AD_KIND.contains("B")) {
					H57 = "□" + H57;
					H58 = "■" + H58;
					H59 = "□" + H59;

					H58 = H58 + "(";
					boolean AD_KIND_FIRST = true;
					if (AD_KIND.contains("1")) {
						AD_KIND_FIRST = false;
						H58 = H58 + "正面";
					}
					if (AD_KIND.contains("2")) {
						if (!AD_KIND_FIRST) {
							H58 = H58 + "、";
						}
						H58 = H58 + "側懸";
					}
					H58 = H58 + ")";
				} else if (AD_KIND.contains("Z")) {
					String AD_KIND_MEMO = hhmap.get("H59").toString();
					if (!StringUtils.isEmpty(AD_KIND_MEMO)) {
						AD_KIND_MEMO = "(" + AD_KIND_MEMO + ")";
					} else {
						AD_KIND_MEMO = "";
					}
					H57 = "□" + H57;
					H58 = "□" + H58;
					H59 = "■" + H59 + AD_KIND_MEMO;
				}
				hhmap.put("H57", H57);
				hhmap.put("H58", H58);
				hhmap.put("H59", H59);
			} else {
				hhmap.put("H57", "□樹立廣告(屋頂、地面)");
				hhmap.put("H58", "□招牌廣告(正面、側懸)");
				hhmap.put("H59", "□其　他");
			}
			String AD_CHK_LAW = hhmap.get("H64") == null ? "" : hhmap.get("H64").toString();
			String AD_CHK_LAW_MM = hhmap.get("H61").toString();
			if ("A".equals(AD_TYP)) {
				hhmap.put("H31", "ˇ");
				hhmap.put("H32", "");

				hhmap.put("H60", "□建築法等相關規定。");
				hhmap.put("H61", "□其　他：");
				if (!StringUtils.isEmpty(AD_CHK_LAW)) {
					if ((AD_CHK_LAW.contains("A")) || (AD_CHK_LAW.contains("B"))) {
						hhmap.put("H60", "■建築法等相關規定。");
					}
					if (AD_CHK_LAW.contains("Z")) {
						if (StringUtils.isEmpty(AD_CHK_LAW_MM)) {
							AD_CHK_LAW_MM = "";
						}
						hhmap.put("H61", "■其　他：" + AD_CHK_LAW_MM);
					}
				}
			} else if ("B".equals(AD_TYP)) {
				hhmap.put("H31", "");
				hhmap.put("H32", "ˇ");
				if (!StringUtils.isEmpty(AD_CHK_LAW)) {
					if (AD_CHK_LAW.contains("A")) {
						hhmap.put("H60", "■");
					} else {
						hhmap.put("H60", "□");
					}
					if (AD_CHK_LAW.contains("B") || AD_CHK_LAW.contains("C")) {
						hhmap.put("H61", "■");
					} else {
						hhmap.put("H61", "□");
					}
				} else {
					hhmap.put("H60", "□");
					hhmap.put("H61", "□");
				}
			}else if ("C".equals(AD_TYP)) {
				hhmap.put("H31", "");
				hhmap.put("H32", "ˇ");
				if (!StringUtils.isEmpty(AD_CHK_LAW)) {
					if (AD_CHK_LAW.contains("A")) {
						hhmap.put("H60", "■");
					} else {
						hhmap.put("H60", "□");
					}
					if (AD_CHK_LAW.contains("B")|| AD_CHK_LAW.contains("C")) {
						hhmap.put("H61", "■");
					} else {
						hhmap.put("H61", "□");
					}
				} else {
					hhmap.put("H60", "□");
					hhmap.put("H61", "□");
				}
			}
			
		} catch (Exception errr) {
			System.err.println(" formateData " + errr.toString());
		}
		return hhmap;
	}

	public synchronized void produceReport(HashMap<String, Object> mParameters) {
		this.parameters = mParameters;

		JDBCConnection jdbcConn = null;
		DbRow dataRow = null;
		Enumeration dataRows = null;
		try {
			String case_id = "";
			String case_id_all = "";
			String[] conditionList = (String[]) this.parameters.get("conditionList");
			String dataNeedType = (String) this.parameters.get("dataNeedType");
			this.ZIP_TAG = ((String) this.parameters.get("ZIP_TAG"));
			
			// 新增參數：只產生第二聯
			String onlySecondCopyParam = (String) this.parameters.get("onlySecondCopy");
			boolean onlySecondCopy = "Y".equals(onlySecondCopyParam) || "true".equalsIgnoreCase(onlySecondCopyParam);

			this.outExt = ((String) this.parameters.get("outExt"));
			case_id_all = conditionList[0];
			if (this.timeDebug) {
				prtTime("~~~1~~~" + case_id_all);
			}
			String[] case_id_ary = case_id_all.split(";");

			iniReportTemplate();
			for (int caseIdx = 0; caseIdx < case_id_ary.length; caseIdx++) {
				case_id = case_id_ary[caseIdx];
				if ("Notice".equals(dataNeedType)) {
					addSurveySheet(case_id, "A", onlySecondCopy);
				} else if ("SurveySheet".equals(dataNeedType)) {
					addSurveySheet(case_id, "B", onlySecondCopy);
				} else {
					addSurveySheet(case_id, "ALL", onlySecondCopy);
				}
			}
			JRPdfExporter exporter = new JRPdfExporter();

			String desFilePath = this.reportPath + "output" + SEPARATOR + this.parameters.get("outFileName");

			this.fileOut = new FileOutputStream(desFilePath);
			exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, this.JasperPrintList);
			exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, this.fileOut);
			exporter.exportReport();
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(this.CLASS_NAME + ".produceReport Error is " + e);
		} finally {
			if (this.fileOut != null) {
				try {
					this.fileOut.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (jdbcConn != null) {
				jdbcConn.closeConnection();
			}
		}
	}

	public BufferedImage getImageStream3(String picPath) {
		File imageFile = null;
		BufferedImage image = null;
		InputStream stream = null;
		try {
			imageFile = new File(picPath);
			if (imageFile.exists()) {
				stream = new FileInputStream(imageFile);
				image = ImageIO.read(stream);
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.err.println(this.CLASS_NAME + ".getImageStream exception is " + e.toString());
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException ex) {
					System.err.println(this.CLASS_NAME + "ERROR getImageStream closing image err: " + ex.toString());
				}
			}
		} finally {
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException ex) {
					System.err.println(this.CLASS_NAME + "ERROR getImageStream closing image err: " + ex.toString());
				}
			}
		}
		return image;
	}

	private BufferedImage getImageStream(String picPath) {
		OutputStream o = null;
		FileInputStream fis = null;

		BufferedImage oriimage = null;
		BufferedImage resultImg = null;

		byte[] imgData1 = null;
		File f = new File(picPath);
		if (f.exists()) {
			try {
				Image image = Toolkit.getDefaultToolkit().getImage(picPath);
				if ((image instanceof BufferedImage)) {
					System.err.println(this.CLASS_NAME + "~~image  is BufferedImage~~");
				} else {
					image = new ImageIcon(image).getImage();
					BufferedImage bimage = null;
					GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
					try {
						int transparency = 1;
						GraphicsDevice gs = ge.getDefaultScreenDevice();
						GraphicsConfiguration gc = gs.getDefaultConfiguration();
						bimage = gc.createCompatibleImage(image.getWidth(null), image.getHeight(null), transparency);
					} catch (HeadlessException localHeadlessException) {
					}
					if (bimage == null) {
						int type = 1;
						bimage = new BufferedImage(image.getWidth(null), image.getHeight(null), type);
					}
					Graphics g = bimage.createGraphics();

					g.drawImage(image, 0, 0, null);
					g.dispose();
					image.flush();

					oriimage = bimage;
				}
				oriimage.isAlphaPremultiplied();

				boolean isMoreThan1000 = true;
				boolean isNeedZip = false;
				int x = 0;
				double rate = 0.9D;
				int maxWH = 1200;
				int imageW = oriimage.getWidth();
				int imageH = oriimage.getHeight();
				while (isMoreThan1000) {
					if ((imageW < maxWH) || (imageH < maxWH)) {
						isMoreThan1000 = false;
					} else {
						isNeedZip = true;
						imageW = (int) (imageW * rate);
						imageH = (int) (imageH * rate);
					}
				}
				if (isNeedZip) {
					int img_type = oriimage.getType();
					if (img_type == 0) {
						img_type = 2;
					}
					BufferedImage outputImage = new BufferedImage(imageW, imageH, img_type);

					Graphics2D g2d = outputImage.createGraphics();

					g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

					g2d.drawImage(oriimage, 0, 0, imageW, imageH, null);
					g2d.dispose();

					ByteArrayOutputStream baos = new ByteArrayOutputStream();
					ImageIO.write(outputImage, "jpg", baos);
					imgData1 = baos.toByteArray();
					InputStream is = new ByteArrayInputStream(imgData1);
					resultImg = ImageIO.read(is);
					baos.close();
				} else {
					InputStream stream = new FileInputStream(f);
					resultImg = ImageIO.read(stream);
				}
			} catch (Exception err) {
				System.err.println("getImageStream::" + err.toString());
				if (o != null) {
					try {
						o.flush();
						o.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				if (fis != null) {
					try {
						fis.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			} finally {
				if (o != null) {
					try {
						o.flush();
						o.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
				if (fis != null) {
					try {
						fis.close();
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			}
		}
		return resultImg;
	}

	private String StringTofloat(String ff) {
		String result = ff;
		try {
			if (!StringUtils.isEmpty(ff)) {
				float f = Float.parseFloat(ff);
				result = "" + f;
			}
		} catch (Exception ee) {
			System.err.println(this.CLASS_NAME + "[StringTofloat err]" + ee.toString());
		}
		return result;
	}

	private void prtTime(String QQ) {
		// SimpleDateFormat sdFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss.SSS");
		// Date current = new Date();
		// System.err.println(QQ);
		// System.err.println(sdFormat.format(current));
	}

	private String convertToChineseNumerals(String input) {
		// 定義中文數字
		String[] cnNums = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
		// 定義位數單位
		String[] cnDecs = { "", "十", "百", "千" };
		// 定義大數單位
		String[] cnUnits = { "", "萬", "億", "兆" };
		
		// 檢查輸入是否為空
		if (input == null || input.isEmpty()) {
			return "";
		}
		
		// 將輸入轉換為整數
		int num = Integer.parseInt(input);
		
		// 特殊情況：0
		if (num == 0) {
			return "零";
		}
		
		// 特殊情況：10-19
		if (num >= 10 && num < 20) {
			if (num == 10) {
				return "十";
			} else {
				return "十" + cnNums[num % 10];
			}
		}
		
		String result = "";
		int len = input.length();
		
		// 處理每一個數字
		for (int i = 0; i < len; i++) {
			int n = input.charAt(i) - '0';
			
			// 計算當前位數
			int pos = len - i - 1;
			
			// 處理零的情況
			if (n == 0) {
				// 避免連續的零
				if (i < len - 1 && input.charAt(i + 1) != '0') {
					result += "零";
				}
			} else {
				// 處理個位數
				result += cnNums[n];
				
				// 添加位數單位（個位不需要單位）
				if (pos % 4 != 0) {
					result += cnDecs[pos % 4];
				}
			}
			
			// 添加大數單位（萬、億、兆等）
			if (pos != 0 && pos % 4 == 0) {
				result += cnUnits[pos / 4];
			}
		}
		
		return result;
	}
}
