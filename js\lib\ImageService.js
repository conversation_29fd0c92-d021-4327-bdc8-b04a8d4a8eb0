
function imageEditor (caseid, cruser, pictype) {
	img_service.load_editor(caseid, cruser, pictype);
}


function img_service () {}

img_service.editor_url = "image_editor.jsp";
img_service.editor_win = null;

img_service.init_events = function(){
	
	$("#image-editor-link").click(function(){
		img_service.load_editor();
	});
	
	/*	
	if (window.addEventListener) {
        window.addEventListener("message", onMessage, false);        
    }else if (window.attachEvent) {
        window.attachEvent("onmessage", onMessage, false);
    }

    function onMessage(event) {
         var data = event.data; 
         console.log(data);
    } 
    */
}

img_service.load_editor = function(caseid, cruser, pictype){
	
	 var width  = 1040;
	 var height = 1040;
	 var left   = screen.width-width;
	 var top    = 100;
	 var params = 'width='+width+', height='+height;
	 params += ', top='+top+', left='+left;
	 params += ', directories=no';
	 params += ', location=yes';
	 params += ', menubar=no';
	 params += ', resizable=no';
	 params += ', scrollbars=yes';
	 params += ', status=no';
	 params += ', toolbar=no';
	 
	if(img_service.editor_win!=null){
		img_service.editor_win.close();
	}
	
	img_service.editor_win = window.open(img_service.editor_url+"?caseId="+caseid+"&crUser="+cruser+"&picType="+pictype , "_blank", params);
	
	if (window.focus) {img_service.editor_win.focus();}
}

img_service.lc = null;

img_service.handle_paste = function(pasteEvent){
	$(".paste-container .fa-spinner").attr("style","display:inline-block");
	var item = pasteEvent.clipboardData.items[0];

	if (item.type.indexOf("image") === 0) {
	   var blob = item.getAsFile();

	   var reader = new FileReader();
	   reader.onload = function(event) { 
	     document.getElementById("imageResult").src = event.target.result; 
		 img_service.addimage($("#imageResult").attr("src"));
	   };

	   reader.readAsDataURL(blob);
	}
	setTimeout(function(){
		$(".paste-container .fa-spinner").attr("style","display:none");	
	},500);
		
}

img_service.init = function(){
	 if(img_service.lc == null){  
	    img_service.lc = LC.init(
	        document.getElementsByClassName('literally backgrounds')[0],
	        {
	          imageURLPrefix : 'img/literallycanvas/lib/img'  ,
	          defaultTools : [LC.tools.SelectShape],
	          primaryColor : '#FF0000',
	          secondaryColor : 'transparent',
	          defaultStrokeWidth: 30
	        }); 
 
	    setTimeout(function(){ 
		    $("canvas").each(function(){
		    	var canvas = $(this)[0]; 
			   // canvas.style.width='100%';
			   // canvas.style.height='100%';
			   // canvas.width  = canvas.offsetWidth;
			   // canvas.height = canvas.offsetHeight;		    	
		    }); 
		    //Select Line with arrow button
		    $(".horz-toolbar .square-toolbar-button img[title='Line with arrow']").trigger('click');
	    },200); 

	 }
	 img_service.init_upload();
}

img_service.addimage = function(img_src){
	 $(".image-area").removeClass("empty-message");
	 var newImage = new Image()
	 newImage.src = img_src;
	 img_service.lc.clear();
	 img_service.lc.saveShape(LC.createShape('Image', {x: 0, y: 0, image: newImage, scale : 1}))	;  
	 img_service.lc.pan(0,0);
}

img_service.init_upload = function(){
	//Init file upload
    $('#upload').on('change', function () {
    	img_service.read_url(input);
    });
    
    var input = document.getElementById( 'upload' );
    var infoArea = document.getElementById( 'upload-label' );

    input.addEventListener( 'change', showFileName );
    function showFileName( event ) {
      var input = event.srcElement;
      var fileName = input.files[0].name;
      infoArea.textContent = 'File name: ' + fileName;
    }  
    
    //Tab events 
	$("#nav-uploadimage-tab").click(function(){ 
		setTimeout(function(){ 
			img_service.init();
		},1000);
		
	});	
}


img_service.read_url = function(input){
    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function (e) { 
            img_service.addimage(e.target.result);  
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function dataURItoBlob(dataURI) {
var byteString = atob(dataURI.split(',')[1]);
var ab = new ArrayBuffer(byteString.length);
var ia = new Uint8Array(ab);
for (var i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
}
return new Blob([ab], { type: 'image/jpg' });
}
function supportFile (){
return typeof window.Blob !== "undefined" &&
        typeof window.ArrayBuffer !== "undefined" &&
        typeof window.Uint8Array !== "undefined" &&
        typeof window.atob !== "undefined";
}

