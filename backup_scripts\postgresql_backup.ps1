# PostgreSQL Database Backup Script for BMS System
# Author: System Administrator
# Purpose: Automated PostgreSQL backup with full, incremental, and differential options

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("full", "incremental", "differential")]
    [string]$BackupType = "full",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\backup_config.json",
    
    [Parameter(Mandatory=$false)]
    [switch]$Compress = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verify = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$CrossRegionReplicate = $false
)

# Import required modules
Import-Module -Name ".\modules\BackupLogger.psm1" -Force
Import-Module -Name ".\modules\BackupMetrics.psm1" -Force
Import-Module -Name ".\modules\BackupNotification.psm1" -Force

# Global variables
$script:BackupStartTime = Get-Date
$script:BackupMetrics = @{}
$script:Logger = $null

# Initialize logging
function Initialize-BackupLogging {
    param(
        [string]$LogPath = ".\logs\postgresql_backup_$(Get-Date -Format 'yyyyMMdd').log"
    )
    
    $script:Logger = New-BackupLogger -LogPath $LogPath -LogLevel "INFO"
    $script:Logger.Info("PostgreSQL backup script started - Type: $BackupType")
}

# Load configuration
function Get-BackupConfiguration {
    param(
        [string]$ConfigFile
    )
    
    if (!(Test-Path $ConfigFile)) {
        throw "Configuration file not found: $ConfigFile"
    }
    
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $script:Logger.Info("Configuration loaded successfully")
        return $config
    }
    catch {
        throw "Failed to load configuration: $_"
    }
}

# Validate PostgreSQL connection
function Test-PostgreSQLConnection {
    param(
        [hashtable]$DbConfig
    )
    
    $script:Logger.Info("Testing PostgreSQL connection...")
    
    $env:PGPASSWORD = $DbConfig.Password
    $testCommand = "& `"$($DbConfig.PgPath)\psql.exe`" -h $($DbConfig.Host) -p $($DbConfig.Port) -U $($DbConfig.Username) -d $($DbConfig.Database) -c `"SELECT version();`" -t"
    
    try {
        $result = Invoke-Expression $testCommand
        if ($LASTEXITCODE -eq 0) {
            $script:Logger.Info("PostgreSQL connection successful")
            return $true
        } else {
            throw "Connection failed with exit code: $LASTEXITCODE"
        }
    }
    catch {
        $script:Logger.Error("PostgreSQL connection failed: $_")
        throw
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Create backup directory structure
function Initialize-BackupDirectories {
    param(
        [string]$BackupBasePath
    )
    
    $directories = @(
        "$BackupBasePath\full",
        "$BackupBasePath\incremental", 
        "$BackupBasePath\differential",
        "$BackupBasePath\wal",
        "$BackupBasePath\metadata",
        "$BackupBasePath\compressed",
        "$BackupBasePath\cross_region"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            $script:Logger.Info("Created backup directory: $dir")
        }
    }
}

# Perform full backup
function Invoke-FullBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting full backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\full\bms_full_$timestamp.sql"
    
    $env:PGPASSWORD = $DbConfig.Password
    
    $pgDumpArgs = @(
        "-h", $DbConfig.Host,
        "-p", $DbConfig.Port,
        "-U", $DbConfig.Username,
        "-d", $DbConfig.Database,
        "-f", $backupFile,
        "--verbose",
        "--create",
        "--clean",
        "--if-exists",
        "--no-password"
    )
    
    try {
        $script:Logger.Info("Executing pg_dump for full backup...")
        $startTime = Get-Date
        
        $process = Start-Process -FilePath "$($DbConfig.PgPath)\pg_dump.exe" -ArgumentList $pgDumpArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$BackupPath\metadata\full_backup_$timestamp.log" -RedirectStandardError "$BackupPath\metadata\full_backup_error_$timestamp.log"
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        if ($process.ExitCode -eq 0) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Full backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["full_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "file_path" = $backupFile
                "timestamp" = $timestamp
            }
            
            return $backupFile
        } else {
            throw "pg_dump failed with exit code: $($process.ExitCode)"
        }
    }
    catch {
        $script:Logger.Error("Full backup failed: $_")
        throw
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Perform incremental backup (WAL-based)
function Invoke-IncrementalBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting incremental backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $walBackupPath = "$BackupPath\wal\$timestamp"
    
    if (!(Test-Path $walBackupPath)) {
        New-Item -ItemType Directory -Path $walBackupPath -Force | Out-Null
    }
    
    $env:PGPASSWORD = $DbConfig.Password
    
    try {
        # Get current WAL file
        $walQuery = "SELECT pg_current_wal_file();"
        $currentWal = & "$($DbConfig.PgPath)\psql.exe" -h $DbConfig.Host -p $DbConfig.Port -U $DbConfig.Username -d $DbConfig.Database -c $walQuery -t -A
        
        # Archive WAL files
        $pgReceiveWalArgs = @(
            "-h", $DbConfig.Host,
            "-p", $DbConfig.Port,
            "-U", $DbConfig.Username,
            "-D", $walBackupPath,
            "--synchronous",
            "--verbose"
        )
        
        $script:Logger.Info("Archiving WAL files...")
        $startTime = Get-Date
        
        # Run pg_receivewal for a specified duration (5 minutes)
        $process = Start-Process -FilePath "$($DbConfig.PgPath)\pg_receivewal.exe" -ArgumentList $pgReceiveWalArgs -PassThru -NoNewWindow
        
        Start-Sleep -Seconds 300  # Archive for 5 minutes
        $process.Kill()
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        # Check if any WAL files were archived
        $walFiles = Get-ChildItem -Path $walBackupPath -Filter "*.wal" -ErrorAction SilentlyContinue
        
        if ($walFiles.Count -gt 0) {
            $totalSize = ($walFiles | Measure-Object -Property Length -Sum).Sum
            $script:Logger.Info("Incremental backup completed - $($walFiles.Count) WAL files archived")
            $script:Logger.Info("Total WAL size: $([math]::Round($totalSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["incremental_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "wal_files_count" = $walFiles.Count
                "total_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "backup_path" = $walBackupPath
                "timestamp" = $timestamp
            }
            
            return $walBackupPath
        } else {
            throw "No WAL files were archived"
        }
    }
    catch {
        $script:Logger.Error("Incremental backup failed: $_")
        throw
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Perform differential backup
function Invoke-DifferentialBackup {
    param(
        [hashtable]$DbConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting differential backup...")
    
    # Find the latest full backup
    $latestFullBackup = Get-ChildItem -Path "$BackupPath\full" -Filter "*.sql" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if (!$latestFullBackup) {
        throw "No full backup found. Differential backup requires a full backup first."
    }
    
    $script:Logger.Info("Base full backup: $($latestFullBackup.Name)")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $diffBackupFile = "$BackupPath\differential\bms_diff_$timestamp.sql"
    
    $env:PGPASSWORD = $DbConfig.Password
    
    try {
        # Get list of modified tables since last full backup
        $baseBackupTime = $latestFullBackup.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
        
        $modifiedTablesQuery = @"
SELECT schemaname, tablename 
FROM pg_stat_user_tables 
WHERE n_tup_ins + n_tup_upd + n_tup_del > 0
ORDER BY schemaname, tablename;
"@
        
        $modifiedTables = & "$($DbConfig.PgPath)\psql.exe" -h $DbConfig.Host -p $DbConfig.Port -U $DbConfig.Username -d $DbConfig.Database -c $modifiedTablesQuery -t -A
        
        if ($modifiedTables) {
            # Create differential backup with only modified tables
            $tableList = $modifiedTables -split "`n" | Where-Object { $_ -ne "" } | ForEach-Object { 
                $parts = $_ -split "\|"
                "--table=$($parts[0]).$($parts[1])"
            }
            
            $pgDumpArgs = @(
                "-h", $DbConfig.Host,
                "-p", $DbConfig.Port,
                "-U", $DbConfig.Username,
                "-d", $DbConfig.Database,
                "-f", $diffBackupFile,
                "--verbose",
                "--data-only",
                "--no-password"
            ) + $tableList
            
            $script:Logger.Info("Executing pg_dump for differential backup...")
            $startTime = Get-Date
            
            $process = Start-Process -FilePath "$($DbConfig.PgPath)\pg_dump.exe" -ArgumentList $pgDumpArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "$BackupPath\metadata\diff_backup_$timestamp.log" -RedirectStandardError "$BackupPath\metadata\diff_backup_error_$timestamp.log"
            
            $endTime = Get-Date
            $duration = ($endTime - $startTime).TotalMinutes
            
            if ($process.ExitCode -eq 0) {
                $fileSize = (Get-Item $diffBackupFile).Length
                $script:Logger.Info("Differential backup completed successfully in $([math]::Round($duration, 2)) minutes")
                $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
                
                # Store metrics
                $script:BackupMetrics["differential_backup"] = @{
                    "success" = $true
                    "duration_minutes" = $duration
                    "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                    "file_path" = $diffBackupFile
                    "base_backup" = $latestFullBackup.Name
                    "timestamp" = $timestamp
                }
                
                return $diffBackupFile
            } else {
                throw "pg_dump failed with exit code: $($process.ExitCode)"
            }
        } else {
            $script:Logger.Info("No modified tables found, skipping differential backup")
            return $null
        }
    }
    catch {
        $script:Logger.Error("Differential backup failed: $_")
        throw
    }
    finally {
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Compress backup file
function Compress-BackupFile {
    param(
        [string]$BackupFile,
        [string]$CompressedPath
    )
    
    if (!(Test-Path $BackupFile)) {
        throw "Backup file not found: $BackupFile"
    }
    
    $script:Logger.Info("Compressing backup file: $BackupFile")
    
    $fileName = [System.IO.Path]::GetFileNameWithoutExtension($BackupFile)
    $compressedFile = "$CompressedPath\$fileName.zip"
    
    try {
        $startTime = Get-Date
        
        # Use 7-Zip for better compression
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", $compressedFile, $BackupFile) -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path $BackupFile -DestinationPath $compressedFile -CompressionLevel Optimal
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        $originalSize = (Get-Item $BackupFile).Length
        $compressedSize = (Get-Item $compressedFile).Length
        $compressionRatio = [math]::Round((1 - ($compressedSize / $originalSize)) * 100, 2)
        
        $script:Logger.Info("Compression completed in $([math]::Round($duration, 2)) minutes")
        $script:Logger.Info("Original size: $([math]::Round($originalSize / 1MB, 2)) MB")
        $script:Logger.Info("Compressed size: $([math]::Round($compressedSize / 1MB, 2)) MB")
        $script:Logger.Info("Compression ratio: $compressionRatio%")
        
        # Store compression metrics
        $script:BackupMetrics["compression"] = @{
            "success" = $true
            "duration_minutes" = $duration
            "original_size_mb" = [math]::Round($originalSize / 1MB, 2)
            "compressed_size_mb" = [math]::Round($compressedSize / 1MB, 2)
            "compression_ratio" = $compressionRatio
            "compressed_file" = $compressedFile
        }
        
        return $compressedFile
    }
    catch {
        $script:Logger.Error("Compression failed: $_")
        throw
    }
}

# Verify backup integrity
function Test-BackupIntegrity {
    param(
        [string]$BackupFile,
        [hashtable]$DbConfig
    )
    
    $script:Logger.Info("Verifying backup integrity: $BackupFile")
    
    $env:PGPASSWORD = $DbConfig.Password
    
    try {
        # Create a temporary test database
        $testDbName = "bms_backup_test_$(Get-Date -Format 'yyyyMMddHHmmss')"
        
        $createDbCommand = "& `"$($DbConfig.PgPath)\psql.exe`" -h $($DbConfig.Host) -p $($DbConfig.Port) -U $($DbConfig.Username) -d postgres -c `"CREATE DATABASE $testDbName;`""
        Invoke-Expression $createDbCommand
        
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to create test database"
        }
        
        # Restore backup to test database
        $restoreCommand = "& `"$($DbConfig.PgPath)\psql.exe`" -h $($DbConfig.Host) -p $($DbConfig.Port) -U $($DbConfig.Username) -d $testDbName -f `"$BackupFile`""
        Invoke-Expression $restoreCommand
        
        if ($LASTEXITCODE -eq 0) {
            # Verify key tables exist
            $tableCheckQuery = "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';"
            $tableCount = & "$($DbConfig.PgPath)\psql.exe" -h $DbConfig.Host -p $DbConfig.Port -U $DbConfig.Username -d $testDbName -c $tableCheckQuery -t -A
            
            if ([int]$tableCount -gt 0) {
                $script:Logger.Info("Backup integrity verification passed - $tableCount tables found")
                
                # Store verification metrics
                $script:BackupMetrics["verification"] = @{
                    "success" = $true
                    "table_count" = [int]$tableCount
                    "test_database" = $testDbName
                }
                
                return $true
            } else {
                throw "No tables found in restored database"
            }
        } else {
            throw "Failed to restore backup to test database"
        }
    }
    catch {
        $script:Logger.Error("Backup integrity verification failed: $_")
        $script:BackupMetrics["verification"] = @{
            "success" = $false
            "error" = $_.Exception.Message
        }
        throw
    }
    finally {
        # Clean up test database
        try {
            $dropDbCommand = "& `"$($DbConfig.PgPath)\psql.exe`" -h $($DbConfig.Host) -p $($DbConfig.Port) -U $($DbConfig.Username) -d postgres -c `"DROP DATABASE IF EXISTS $testDbName;`""
            Invoke-Expression $dropDbCommand
        }
        catch {
            $script:Logger.Warning("Failed to clean up test database: $testDbName")
        }
        
        Remove-Item env:PGPASSWORD -ErrorAction SilentlyContinue
    }
}

# Replicate to cross-region storage
function Invoke-CrossRegionReplication {
    param(
        [string]$BackupFile,
        [hashtable]$ReplicationConfig
    )
    
    $script:Logger.Info("Starting cross-region replication...")
    
    try {
        foreach ($target in $ReplicationConfig.Targets) {
            $script:Logger.Info("Replicating to: $($target.Name)")
            
            switch ($target.Type) {
                "azure_blob" {
                    $result = Copy-ToAzureBlob -BackupFile $BackupFile -Config $target
                }
                "aws_s3" {
                    $result = Copy-ToAwsS3 -BackupFile $BackupFile -Config $target
                }
                "ftp" {
                    $result = Copy-ToFtp -BackupFile $BackupFile -Config $target
                }
                "network_share" {
                    $result = Copy-ToNetworkShare -BackupFile $BackupFile -Config $target
                }
                default {
                    throw "Unsupported replication target type: $($target.Type)"
                }
            }
            
            if ($result.Success) {
                $script:Logger.Info("Replication to $($target.Name) completed successfully")
            } else {
                $script:Logger.Error("Replication to $($target.Name) failed: $($result.Error)")
            }
        }
    }
    catch {
        $script:Logger.Error("Cross-region replication failed: $_")
        throw
    }
}

# Helper function for Azure Blob replication
function Copy-ToAzureBlob {
    param(
        [string]$BackupFile,
        [hashtable]$Config
    )
    
    try {
        # Install Azure PowerShell module if not present
        if (!(Get-Module -Name "Az.Storage" -ListAvailable)) {
            Install-Module -Name "Az.Storage" -Force -Scope CurrentUser
        }
        
        Import-Module -Name "Az.Storage"
        
        # Connect to Azure Storage
        $ctx = New-AzStorageContext -StorageAccountName $Config.StorageAccountName -StorageAccountKey $Config.StorageAccountKey
        
        # Upload file
        $blobName = [System.IO.Path]::GetFileName($BackupFile)
        Set-AzStorageBlobContent -File $BackupFile -Container $Config.ContainerName -Blob $blobName -Context $ctx
        
        return @{ Success = $true }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Helper function for AWS S3 replication
function Copy-ToAwsS3 {
    param(
        [string]$BackupFile,
        [hashtable]$Config
    )
    
    try {
        # Install AWS PowerShell module if not present
        if (!(Get-Module -Name "AWS.Tools.S3" -ListAvailable)) {
            Install-Module -Name "AWS.Tools.S3" -Force -Scope CurrentUser
        }
        
        Import-Module -Name "AWS.Tools.S3"
        
        # Set AWS credentials
        Set-AWSCredential -AccessKey $Config.AccessKey -SecretKey $Config.SecretKey -StoreAs "default"
        
        # Upload file
        $keyName = [System.IO.Path]::GetFileName($BackupFile)
        Write-S3Object -BucketName $Config.BucketName -Key $keyName -File $BackupFile -Region $Config.Region
        
        return @{ Success = $true }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Helper function for FTP replication
function Copy-ToFtp {
    param(
        [string]$BackupFile,
        [hashtable]$Config
    )
    
    try {
        $ftpRequest = [System.Net.FtpWebRequest]::Create("ftp://$($Config.Server)/$([System.IO.Path]::GetFileName($BackupFile))")
        $ftpRequest.Credentials = New-Object System.Net.NetworkCredential($Config.Username, $Config.Password)
        $ftpRequest.Method = [System.Net.WebRequestMethods+Ftp]::UploadFile
        $ftpRequest.UseBinary = $true
        $ftpRequest.KeepAlive = $false
        
        $fileContents = [System.IO.File]::ReadAllBytes($BackupFile)
        $ftpRequest.ContentLength = $fileContents.Length
        
        $requestStream = $ftpRequest.GetRequestStream()
        $requestStream.Write($fileContents, 0, $fileContents.Length)
        $requestStream.Close()
        
        $response = $ftpRequest.GetResponse()
        $response.Close()
        
        return @{ Success = $true }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Helper function for network share replication
function Copy-ToNetworkShare {
    param(
        [string]$BackupFile,
        [hashtable]$Config
    )
    
    try {
        $fileName = [System.IO.Path]::GetFileName($BackupFile)
        $destinationPath = "$($Config.SharePath)\$fileName"
        
        # Map network drive if credentials provided
        if ($Config.Username -and $Config.Password) {
            $credential = New-Object System.Management.Automation.PSCredential($Config.Username, (ConvertTo-SecureString $Config.Password -AsPlainText -Force))
            New-PSDrive -Name "BackupShare" -PSProvider FileSystem -Root $Config.SharePath -Credential $credential
            $destinationPath = "BackupShare:\$fileName"
        }
        
        Copy-Item -Path $BackupFile -Destination $destinationPath -Force
        
        return @{ Success = $true }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Cleanup old backups
function Remove-OldBackups {
    param(
        [string]$BackupPath,
        [int]$RetentionDays
    )
    
    $script:Logger.Info("Cleaning up backups older than $RetentionDays days...")
    
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $deletedCount = 0
    $freedSpace = 0
    
    $backupTypes = @("full", "incremental", "differential", "compressed")
    
    foreach ($type in $backupTypes) {
        $typePath = "$BackupPath\$type"
        
        if (Test-Path $typePath) {
            $oldFiles = Get-ChildItem -Path $typePath -Recurse -File | Where-Object { $_.LastWriteTime -lt $cutoffDate }
            
            foreach ($file in $oldFiles) {
                try {
                    $fileSize = $file.Length
                    Remove-Item -Path $file.FullName -Force
                    $deletedCount++
                    $freedSpace += $fileSize
                    $script:Logger.Info("Deleted old backup: $($file.Name)")
                }
                catch {
                    $script:Logger.Warning("Failed to delete file: $($file.FullName) - $_")
                }
            }
        }
    }
    
    $script:Logger.Info("Cleanup completed: $deletedCount files deleted, $([math]::Round($freedSpace / 1MB, 2)) MB freed")
}

# Generate backup report
function New-BackupReport {
    param(
        [string]$ReportPath
    )
    
    $script:Logger.Info("Generating backup report...")
    
    $report = @{
        "backup_summary" = @{
            "start_time" = $script:BackupStartTime
            "end_time" = Get-Date
            "duration_minutes" = ((Get-Date) - $script:BackupStartTime).TotalMinutes
            "backup_type" = $BackupType
            "overall_success" = $true
        }
        "metrics" = $script:BackupMetrics
        "system_info" = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "disk_space" = Get-DiskSpace
        }
    }
    
    # Check overall success
    foreach ($metric in $script:BackupMetrics.Values) {
        if ($metric.success -eq $false) {
            $report.backup_summary.overall_success = $false
            break
        }
    }
    
    # Save report as JSON
    $reportJson = $report | ConvertTo-Json -Depth 10
    $reportFile = "$ReportPath\backup_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8
    
    $script:Logger.Info("Backup report saved: $reportFile")
    
    return $report
}

# Get disk space information
function Get-DiskSpace {
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    $driveInfo = @()
    foreach ($drive in $drives) {
        $driveInfo += @{
            "drive" = $drive.DeviceID
            "total_size_gb" = [math]::Round($drive.Size / 1GB, 2)
            "free_space_gb" = [math]::Round($drive.FreeSpace / 1GB, 2)
            "used_space_gb" = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
            "free_space_percent" = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        }
    }
    
    return $driveInfo
}

# Main execution
try {
    # Initialize logging
    Initialize-BackupLogging
    
    # Load configuration
    $config = Get-BackupConfiguration -ConfigFile $ConfigFile
    
    # Test database connection
    Test-PostgreSQLConnection -DbConfig $config.postgresql
    
    # Initialize backup directories
    Initialize-BackupDirectories -BackupBasePath $config.backup.base_path
    
    # Execute backup based on type
    $backupFile = $null
    
    switch ($BackupType) {
        "full" {
            $backupFile = Invoke-FullBackup -DbConfig $config.postgresql -BackupPath $config.backup.base_path
        }
        "incremental" {
            $backupFile = Invoke-IncrementalBackup -DbConfig $config.postgresql -BackupPath $config.backup.base_path
        }
        "differential" {
            $backupFile = Invoke-DifferentialBackup -DbConfig $config.postgresql -BackupPath $config.backup.base_path
        }
    }
    
    # Compress backup if requested and file exists
    if ($Compress -and $backupFile -and (Test-Path $backupFile)) {
        $compressedFile = Compress-BackupFile -BackupFile $backupFile -CompressedPath "$($config.backup.base_path)\compressed"
        
        # Remove original uncompressed file
        Remove-Item -Path $backupFile -Force
        $backupFile = $compressedFile
    }
    
    # Verify backup integrity if requested
    if ($Verify -and $backupFile) {
        Test-BackupIntegrity -BackupFile $backupFile -DbConfig $config.postgresql
    }
    
    # Cross-region replication if requested
    if ($CrossRegionReplicate -and $backupFile) {
        Invoke-CrossRegionReplication -BackupFile $backupFile -ReplicationConfig $config.cross_region_replication
    }
    
    # Cleanup old backups
    Remove-OldBackups -BackupPath $config.backup.base_path -RetentionDays $config.backup.retention_days
    
    # Generate backup report
    $report = New-BackupReport -ReportPath "$($config.backup.base_path)\metadata"
    
    # Send notifications
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report $report
    }
    
    $script:Logger.Info("PostgreSQL backup completed successfully")
    
    # Set exit code based on overall success
    if ($report.backup_summary.overall_success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    $script:Logger.Error("Backup script failed: $_")
    
    # Send failure notification
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report @{
            "backup_summary" = @{
                "overall_success" = $false
                "error_message" = $_.Exception.Message
                "backup_type" = $BackupType
            }
        }
    }
    
    exit 1
}
finally {
    if ($script:Logger) {
        $script:Logger.Close()
    }
}