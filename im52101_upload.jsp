<%--JSP Page Init @1-8D68A82F--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new im52101_uploadServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-13A212B5--%>
<%@include file="im52101_uploadHandlers.jsp"%>
<%
    if (!im52101_uploadModel.isVisible()) return;
    if (im52101_uploadParent != null) {
        if (!im52101_uploadParent.getChild(im52101_uploadModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", im52101_uploadModel);
    pageContext.setAttribute("page", im52101_uploadModel);
    im52101_uploadModel.fireOnInitializeViewEvent(new Event());
    im52101_uploadModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im52101_uploadModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im52101_uploadModel.isVisible()) return;
%>
<%--End Page Body--%>

<%--JSP Page Content @1-464DA8CD--%>
<!DOCTYPE HTML>
<html>
<head>
<ccs:meta header="Content-Type"/>
<title>im52101_upload</title> <%-- Title updated --%>
<script src="javascript/jquery-1.11.2.min.js"></script>
<link rel="stylesheet" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css" id="bscss">
<script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
<!-- CSS to style the file input field as button and adjust the Bootstrap progress bars -->
<link rel="stylesheet" href="javascript/ezek/jquery_fileupload/css/jquery.fileupload.css">
<link rel="stylesheet" href="javascript/ezek/jquery_fileupload/css/jquery.fileupload-ui.css">
<link rel="stylesheet" href="in_recordgridstyle.css?1100919">
<script language="JavaScript" src="javascript/jquery.fancybox.js?v=2.1.5" type="text/javascript"></script>

<script language="JavaScript" type="text/javascript">
 function closeFancybox() {
        parent.$.fancybox.close();     
 }
</script>
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//End CCS script
</script>
</head>
<body>
<ccs:record name='fileupload'>
<form id="fileupload" enctype="multipart/form-data" method="post" name="<ccs:form_name/>" action="im52101_upload_man.jsp"> <%-- Action updated --%>
  <table class="table" cellspacing="0" cellpadding="0">
    <ccs:error_block>
    <tr id="fileuploadErrorBlock" class="Error">
      <td colspan="2"><ccs:error_text/></td> 
    </tr>
 </ccs:error_block>
    <tr class="Controls">
      <td></td> 
    </tr>
 
    <tr class="Row Count">
      <td colspan="2"><ccs:control name='uploadTitle'/>&nbsp;</td> 
    </tr>
 
    <tr>
      <td colspan="2">
        <div class="row fileupload-buttonbar">
          <div class="col-lg-7">
            <span title="加入Excel檔案" class="btn ui_button fileinput-button" style="MARGIN: 5px">
                <i class="glyphicon glyphicon-plus ui_icon" style="TOP: 2px"></i>
                <span>&nbsp;加入檔案</span> 
                <input type="file" name="files[]" accept=".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" multiple>
            </span>
            <button class="btn ui_button start" style="MARGIN: 5px" type="submit">
                <i title="全部上傳" class="glyphicon glyphicon-upload ui_icon" style="TOP: 2px"></i>
                <span>&nbsp;上傳</span>
            </button>
            <label class="upload_hint" style="display:none;">&nbsp;&nbsp;&nbsp;&nbsp;點擊「加入檔案」或直接將檔案拖曳至下方</label>
            <button title="全部取消" class="btn ui_button cancel" style="MARGIN: 5px; DISPLAY: none" type="reset">
                <i class="glyphicon glyphicon-ban-circle ui_icon" style="TOP: 2px"></i>
                <span>&nbsp;全部取消</span>
            </button>
            <button title="刪除" class="btn ui_button delete" style="MARGIN: 5px; DISPLAY: none">
                <i class="glyphicon glyphicon-trash ui_icon" style="TOP: 2px"></i>
                <span>&nbsp;刪除</span>
            </button> 
            <input type="checkbox" title="全選刪除" class="toggle" style="DISPLAY: none">
            <span class="fileupload-process">&nbsp;</span> 
          </div>
 
          <div class="col-lg-5 fileupload-progress fade">
            <div role="progressbar" class="progress progress-striped active" aria-valuemin="0" aria-valuemax="100">
              <div class="progress-bar progress-bar-success" style="WIDTH: 0%">
              </div>
            </div>
            <div class="progress-extended">
            </div>
          </div>
        </div>
 
        <table role="presentation" class="table table-striped">
          <tbody class="files">
          </tbody>
        </table>
      </td> 
    </tr>
 
    <tr class="Bottom">
      <td style="TEXT-ALIGN: right" colspan="2">
        <input type="hidden" id="fileuploadId" value="<ccs:control name='fileId'/>" name="<ccs:control name='fileId' property='name'/>">
        <%-- fileType is now implicitly 'excel', so this hidden input might be optional or used for server-side confirmation --%>
        <input type="hidden" id="fileuploadType" value="excel" name="<ccs:control name='fileType' property='name'/>">
        <ccs:button name='Button_Cancel'>
        <input type="button" onclick="closeFancybox();" id="fileuploadButton_Cancel" class="btn btn-success" alt="返&nbsp;&nbsp;&nbsp;&nbsp;回" value="返&nbsp;&nbsp;&nbsp;&nbsp;回" name="<ccs:control name='Button_Cancel' property='name'/>"></ccs:button>
      </td> 
    </tr>
  </table>
</form>
</ccs:record>

<!-- Templates for jQuery File Upload (largely unchanged from im60503_upload.jsp) -->
<script id="template-upload" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-upload fade">
        <td style="display:none">
            <span class="preview"></span>
        </td>
        <td>
            <p class="name">{%=file.name%}</p>
            <strong class="error text-danger"></strong>
        </td>
        <td>
            <p class="size">Processing...</p>
            <div class="progress progress-striped active" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><div class="progress-bar progress-bar-success" style="width:0%;"></div></div>
        </td>
        <td>
            {% if (!i && !o.options.autoUpload) { %}
                <button style="display:none;"class="btn ui_button start" disabled>
                    <i class="glyphicon glyphicon-upload ui_icon"></i>
                    <span>&nbsp;上傳</span>
                </button>
            {% } %}
            {% if (!i) { %}
                <button class="btn ui_button cancel">
                    <i class="glyphicon glyphicon-ban-circle ui_icon"></i>
                    <span>&nbsp;取消</span>
                </button>
            {% } %}
        </td>
    </tr>
{% } %}
</script>
<script id="template-download" type="text/x-tmpl">
{% for (var i=0, file; file=o.files[i]; i++) { %}
    <tr class="template-download fade">
        <td style="display:none">
            <span class="preview">
                {% if (file.thumbnailUrl) { %}
                    <a href="{%=file.url%}" title="{%=file.name%}" download="{%=file.name%}" data-gallery><img src="{%=file.thumbnailUrl%}"></a>
                {% } %}
            </span>
        </td>
        <td>
            <p class="name">
                {% if (file.url) { %}
                    <a href="{%=file.url%}" title="{%=file.name%}" download="{%=file.name%}" {%=file.thumbnailUrl?'data-gallery':''%}>{%=file.name%}</a>
                {% } else { %}
                    <span>{%=file.name%}</span>
                {% } %}
            </p>
            {% if (file.error) { %}
                <div><span class="label label-danger">&nbsp;錯誤訊息</span> {%=file.error%}</div>
            {% } %}
        </td>
        <td>
            <span class="size">{%=o.formatFileSize(file.size)%}</span>
        </td>
        <td>
            {% if (file.deleteUrl) { %}
                <button style="display:none;" class="btn ui_button delete" data-type="{%=file.deleteType%}" data-url="{%=file.deleteUrl%}"{% if (file.deleteWithCredentials) { %} data-xhr-fields='{&amp;quot;withCredentials&amp;quot;:true}'{% } %}>
                    <i class="glyphicon glyphicon-trash ui_icon"></i>
                    <span>&nbsp;刪除</span>
                </button>
                <input style="display:none;" type="checkbox" name="delete" value="1" class="toggle">
            {% } else { %}
                <button style="display:none;" class="btn ui_button cancel">
                    <i class="glyphicon glyphicon-ban-circle ui_icon"></i>
                    <span>&nbsp;取消</span>
                </button>
            {% } %}
        </td>
    </tr>
{% } %}
</script>

<!-- jQuery File Upload dependencies (assuming these are available in the project) -->
<script src="javascript/ezek/jquery_fileupload/vendor/jquery.ui.widget.js"></script>
<script src="javascript/ezek/jquery_fileupload/tmpl.min.js"></script>
<script src="javascript/ezek/jquery_fileupload/load-image.all.min.js"></script>
<script src="javascript/ezek/jquery_fileupload/canvas-to-blob.min.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.blueimp-gallery.min.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.iframe-transport.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload.js?v=9.28.0"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-process.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-image.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-audio.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-video.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-validate_chinese.js"></script>
<script src="javascript/ezek/jquery_fileupload/jquery.fileupload-ui.js"></script>

<script language="JavaScript" type="text/javascript">
 $(function () {
    'use strict';

    var fileId = getUrlParameter("id");

    // Only allow .xlsx and .xls files
    var acceptFileTypes = /(\.|\/)(xlsx?)$/i;
        
    $('#fileupload').fileupload({
        url: 'im52101_upload_man.jsp', // Updated URL
        sequentialUploads: true,
        maxNumberOfFiles: 1, // Or more if multiple Excel files per ID are allowed
        maxFileSize: 20 * 1024 * 1024, // 20 MB, adjust if needed
        acceptFileTypes: acceptFileTypes,
        formData: { // Pass id and type as additional form data
            id: fileId,
        }
    });
 
    // Load existing files (if any for this id and type):
    $('#fileupload').addClass('fileupload-processing');
    $.ajax({
        url: $('#fileupload').fileupload('option', 'url'), // im52101_upload_man.jsp
        dataType: 'json',
        data: { // Send id and type to get specific existing files
             id: fileId,
             get_existing: true // Add a flag to indicate this is a request for existing files
        },
        context: $('#fileupload')[0]
    }).always(function () {
        $(this).removeClass('fileupload-processing');
    }).done(function (result) {
        
        if(result && result.files && result.files.length) {
            $(this).fileupload('option', 'done')
                .call(this, $.Event('done'), {result: result});
        }
    });
    
});

function getUrlParameter(parameterName) {
    parameterName = parameterName.replace(/[\[]/,"\\\[").replace(/[\]]/,"\\\]");  
    var regexS = "[\\?&]" + parameterName + "=([^&" + "#]*)";    
    var regex = new RegExp(regexS);
    var results = regex.exec(window.location.href);
    if (results == null) {
        return "";
    } else {
        return results[1];
    }
}
</script>
</body>
</html>

<%--JSP Page BeforeOutput @1-95796369--%>
<%im52101_uploadModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-7A0F9D97--%>
<%im52101_uploadModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%> 