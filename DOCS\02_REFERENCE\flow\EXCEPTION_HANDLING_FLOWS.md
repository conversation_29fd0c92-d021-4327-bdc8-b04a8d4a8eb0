# 異常處理流程整理 (Exception Handling Flows)

## 📋 文件資訊
- **文件編號**: T2.8.3
- **文件目的**: 整理新北市違章建築管理系統的所有異常處理流程
- **分析日期**: 2025-07-05
- **負責單位**: 【D】Claude Code - DevOps與技術架構任務組

---

## 🎯 異常處理架構概述

### 現況摘要
- **無集中式錯誤處理**：每個頁面獨立處理異常
- **基礎日誌機制**：使用 System.err.println() 輸出錯誤
- **手動交易管理**：明確的 commit/rollback 控制
- **內嵌錯誤訊息**：錯誤訊息硬編碼在 JSP 檔案中

---

## 🔍 異常處理模式分析

### 1. 資料庫異常處理模式

#### 標準模式
```java
// 標準資料庫操作異常處理
DBConnectionManager dbcm = null;
Connection conn = null;
PreparedStatement pstmt = null;

try {
    dbcm = DBConnectionManager.getInstance();
    conn = dbcm.getConnection(CONNECTION_NAME);
    conn.setAutoCommit(false);  // 開始交易
    
    // 執行資料庫操作
    pstmt = conn.prepareStatement(sql);
    pstmt.executeUpdate();
    
    conn.commit();  // 提交交易
    
} catch (SQLException e) {
    System.err.println("資料庫錯誤：" + e.getMessage());
    e.printStackTrace();
    
    // 回滾交易
    if (conn != null) {
        try {
            conn.rollback();
        } catch (SQLException rollbackEx) {
            System.err.println("回滾失敗：" + rollbackEx.getMessage());
        }
    }
    
} catch (Exception e) {
    System.err.println("系統錯誤：" + e.getMessage());
    e.printStackTrace();
    
} finally {
    // 資源清理
    try {
        if (pstmt != null) pstmt.close();
        if (conn != null) {
            conn.setAutoCommit(true);
            dbcm.freeConnection(CONNECTION_NAME, conn);
        }
    } catch (Exception cleanupEx) {
        System.err.println("資源清理錯誤：" + cleanupEx.getMessage());
    }
}
```

#### 批次操作模式
```java
// case_empty_dis.jsp 中的批次處理
private void executeSqlCmd(ArrayList<String> sqlCmds) throws Exception {
    Connection conn = null;
    PreparedStatement pstmt = null;
    
    try {
        conn = getConnection();
        conn.setAutoCommit(false);
        
        for (String sqlCmd : sqlCmds) {
            pstmt = conn.prepareStatement(sqlCmd);
            pstmt.executeUpdate();
            pstmt.close();
        }
        
        conn.commit();
        
    } catch (Exception e) {
        if (conn != null) conn.rollback();
        throw e;  // 重新拋出異常
    } finally {
        if (conn != null) {
            conn.setAutoCommit(true);
            freeConnection(conn);
        }
    }
}
```

### 2. 業務邏輯異常處理

#### 欄位驗證錯誤
```java
// 必填欄位驗證
if (StringUtils.isEmpty(fieldValue)) {
    e.getRecord().getControl("fieldName").addError(
        "欄位 " + e.getRecord().getControl("fieldName").getCaption() + " 是必須的."
    );
}

// 格式驗證
if (!EzekUtils.checkRegNum(reg_num)) {
    e.getRecord().getControl("reg_num").addError(
        "欄位 " + e.getRecord().getControl("reg_num").getCaption() + " 的長度必須為10碼"
    );
}

// 重複性檢查
if (isDuplicate) {
    e.getRecord().getControl("dis_notice_yy").addError(
        "拆除通知號碼己存在，請重新輸入!!"
    );
}
```

#### 業務規則違反
```java
// 狀態轉換驗證
if ("234,244,254,232,252".indexOf(ACC_RLT) > -1) {
    e.getRecord().addError("此案件已送協同作業/陳核, 不允許編輯.");
}

// 權限檢查
if (!hasPermission) {
    e.getRecord().addError("您沒有權限執行此操作");
}

// 資料完整性檢查
if (relatedRecordNotFound) {
    e.getRecord().addError("相關資料不存在，無法繼續操作");
}
```

### 3. 會話管理異常

#### 會話超時處理
```java
// 標準會話檢查模式
if (SessionStorage.getInstance(e.getPage().getRequest())
    .getAttributeAsString("LoginPass") != "True") {
    e.getPage().setRedirectString("timeout_err.jsp");
}

// timeout_err.jsp 內容
<%@ page language="java" errorPage="" pageEncoding="UTF-8" %>
<html>
<head>
    <title>登入時間逾時</title>
    <script>
        function reLogin() {
            <% if ("sso".equals(session.getAttribute("loginSource"))) { %>
                window.location.href = "ezekSSO.jsp";
            <% } else { %>
                window.location.href = "login.jsp";
            <% } %>
        }
    </script>
</head>
<body onload="reLogin()">
    <h3>您已經停頓很久未操作系統了，為了系統安全起見，您需要重新登入系統。</h3>
</body>
</html>
```

### 4. 檔案處理異常

#### 檔案上傳錯誤
```java
try {
    // 檔案上傳處理
    MultipartRequest multi = new MultipartRequest(
        request, uploadPath, maxFileSize, "UTF-8"
    );
    
} catch (IOException e) {
    System.err.println("檔案上傳失敗：" + e.getMessage());
    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    response.getWriter().write("{\"error\":\"檔案上傳失敗\"}");
    
} catch (FileSizeLimitExceededException e) {
    System.err.println("檔案大小超過限制：" + e.getMessage());
    response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
    response.getWriter().write("{\"error\":\"檔案大小超過限制\"}");
}
```

### 5. 系統層級異常

#### 資料庫連線池異常
```java
// DBConnectionManager 中的連線池管理
public Connection getConnection(String name) {
    DBConnectionPool pool = pools.get(name);
    if (pool != null) {
        try {
            return pool.getConnection();
        } catch (SQLException e) {
            System.err.println("無法取得資料庫連線：" + e.getMessage());
            return null;
        }
    }
    return null;
}
```

#### 配置載入異常
```java
try {
    Properties props = new Properties();
    props.load(new FileInputStream("WEB-INF/site.properties"));
    
} catch (FileNotFoundException e) {
    System.err.println("找不到配置檔案：site.properties");
    // 使用預設配置
    
} catch (IOException e) {
    System.err.println("讀取配置檔案失敗：" + e.getMessage());
    // 使用預設配置
}
```

---

## 📊 異常處理流程圖

### 整體異常處理流程

```mermaid
graph TD
    A[使用者操作] --> B{操作類型}
    
    B -->|資料操作| C[資料庫操作]
    B -->|檔案操作| D[檔案處理]
    B -->|業務邏輯| E[業務驗證]
    B -->|系統功能| F[系統檢查]
    
    C --> G{是否成功?}
    D --> H{是否成功?}
    E --> I{是否通過?}
    F --> J{是否正常?}
    
    G -->|否| K[SQL異常處理]
    H -->|否| L[IO異常處理]
    I -->|否| M[驗證錯誤處理]
    J -->|否| N[系統異常處理]
    
    K --> O[回滾交易]
    K --> P[記錄錯誤]
    K --> Q[顯示錯誤訊息]
    
    L --> P
    L --> Q
    
    M --> R[收集錯誤]
    M --> Q
    
    N --> S[導向錯誤頁]
    
    O --> P
    R --> Q
    
    style K fill:#faa,stroke:#333,stroke-width:2px
    style L fill:#faa,stroke:#333,stroke-width:2px
    style M fill:#faa,stroke:#333,stroke-width:2px
    style N fill:#faa,stroke:#333,stroke-width:2px
```

### 資料庫交易異常處理流程

```mermaid
graph TD
    A[開始交易] --> B[setAutoCommit false]
    B --> C[執行SQL操作]
    
    C --> D{操作成功?}
    
    D -->|是| E[更多操作?]
    D -->|否| F[捕獲異常]
    
    E -->|是| C
    E -->|否| G[commit交易]
    
    F --> H[rollback交易]
    H --> I[記錄錯誤]
    
    G --> J[setAutoCommit true]
    H --> J
    
    J --> K[釋放連線]
    I --> K
    
    K --> L[結束]
    
    style F fill:#faa,stroke:#333,stroke-width:2px
    style H fill:#faa,stroke:#333,stroke-width:2px
    style I fill:#faa,stroke:#333,stroke-width:2px
```

---

## 🚨 常見異常類型與處理方式

### 1. SQLException 系列

| 異常類型 | 處理方式 | 使用者看到的訊息 |
|---------|---------|----------------|
| 主鍵重複 | 回滾交易 + 顯示錯誤 | "資料已存在，請勿重複新增" |
| 外鍵約束 | 回滾交易 + 顯示錯誤 | "相關資料不存在" |
| 連線逾時 | 重試或顯示錯誤 | "系統忙碌中，請稍後再試" |
| 死結 | 回滾交易 + 重試 | "操作失敗，請重新嘗試" |

### 2. 業務邏輯異常

| 異常類型 | 處理方式 | 使用者看到的訊息 |
|---------|---------|----------------|
| 必填欄位空白 | addError | "欄位 X 是必須的" |
| 格式錯誤 | addError | "欄位 X 格式不正確" |
| 權限不足 | addError | "您沒有權限執行此操作" |
| 狀態不允許 | addError | "目前狀態不允許此操作" |

### 3. 系統異常

| 異常類型 | 處理方式 | 使用者看到的訊息 |
|---------|---------|----------------|
| 會話逾時 | 導向登入頁 | "請重新登入系統" |
| 檔案過大 | 顯示錯誤 | "檔案大小超過限制" |
| 系統錯誤 | 記錄 + 通用訊息 | "系統發生錯誤，請聯絡管理員" |

---

## 🛠️ 異常處理最佳實踐建議

### 1. 集中式異常處理

```xml
<!-- web.xml 配置建議 -->
<error-page>
    <error-code>404</error-code>
    <location>/error/404.jsp</location>
</error-page>
<error-page>
    <error-code>500</error-code>
    <location>/error/500.jsp</location>
</error-page>
<error-page>
    <exception-type>java.lang.Exception</exception-type>
    <location>/error/error.jsp</location>
</error-page>
```

### 2. 統一錯誤回應格式

```java
public class ErrorResponse {
    private String code;
    private String message;
    private String detail;
    private String timestamp;
    
    // JSON 格式輸出
    public String toJson() {
        return String.format(
            "{\"code\":\"%s\",\"message\":\"%s\",\"detail\":\"%s\",\"timestamp\":\"%s\"}",
            code, message, detail, timestamp
        );
    }
}
```

### 3. 日誌框架整合

```java
// 使用 SLF4J 取代 System.err.println
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ExampleHandler {
    private static final Logger logger = LoggerFactory.getLogger(ExampleHandler.class);
    
    public void process() {
        try {
            // 業務邏輯
        } catch (SQLException e) {
            logger.error("資料庫操作失敗", e);
        } catch (Exception e) {
            logger.error("未預期的錯誤", e);
        }
    }
}
```

### 4. 錯誤訊息國際化

```properties
# messages_zh_TW.properties
error.field.required=欄位 {0} 是必須的
error.duplicate.record=資料已存在：{0}
error.invalid.format=欄位 {0} 格式不正確
error.permission.denied=您沒有權限執行此操作
error.system.error=系統發生錯誤，錯誤代碼：{0}
```

---

## 📋 異常處理檢查清單

### 開發階段
- [ ] 所有資料庫操作都有 try-catch 包覆
- [ ] 交易都有適當的 rollback 處理
- [ ] 資源都在 finally 區塊釋放
- [ ] 錯誤訊息對使用者友善
- [ ] 敏感資訊不會洩露在錯誤訊息中

### 測試階段
- [ ] 測試各種異常情況
- [ ] 確認錯誤訊息正確顯示
- [ ] 驗證交易回滾機制
- [ ] 檢查日誌記錄完整性

### 部署階段
- [ ] 配置適當的錯誤頁面
- [ ] 設定日誌輸出層級
- [ ] 監控異常發生頻率
- [ ] 建立異常通知機制

---

## 🔚 結論

目前系統的異常處理機制雖然基本功能完整，但缺乏現代化的集中管理和監控能力。建議逐步導入：

1. **集中式錯誤處理**：減少重複代碼，統一錯誤處理邏輯
2. **結構化日誌**：便於問題追蹤和分析
3. **錯誤監控**：主動發現和處理系統異常
4. **使用者體驗優化**：提供更友善的錯誤訊息和引導

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*任務編號：T2.8.3*
*完成日期：2025-07-05*