{"disaster_recovery": {"rto_target_hours": 4, "rpo_target_hours": 1, "recovery_site": "production", "staging_path": "D:\\Recovery\\Staging", "test_mode": false}, "databases": {"postgresql": {"primary": {"host": "localhost", "port": "5432", "database": "bms", "username": "postgres", "password": "S!@h@202203", "pg_path": "C:\\Program Files\\PostgreSQL\\15\\bin"}, "backup": {"host": "backup-server", "port": "5432", "database": "bms_backup", "username": "postgres", "password": "S!@h@202203", "pg_path": "C:\\Program Files\\PostgreSQL\\15\\bin"}, "recovery": {"parallel_restore": true, "max_connections": 10, "timeout_minutes": 60}}, "sqlserver": {"primary": {"server": "**************", "port": "2433", "database": "ramsGIS", "username": "sa", "password": "$ystemOnlin168"}, "backup": {"server": "backup-server", "port": "1433", "database": "ramsGIS_backup", "username": "sa", "password": "$ystemOnlin168"}, "recovery": {"verify_checksums": true, "restore_timeout_minutes": 120, "recovery_model": "FULL"}}}, "application": {"critical_services": ["Apache Tomcat 9.0 Tomcat9", "PostgreSQL Database Server 15", "World Wide Web Publishing Service"], "health_endpoints": [{"name": "application_health", "url": "http://localhost:8080/health", "timeout_seconds": 30}, {"name": "database_health", "url": "http://localhost:8080/db-health", "timeout_seconds": 30}], "critical_paths": ["D:\\apache-tomcat-9.0.98\\webapps\\src", "D:\\apache-tomcat-9.0.98\\conf", "D:\\apache-tomcat-9.0.98\\lib"], "recovery": {"application_path": "D:\\apache-tomcat-9.0.98", "backup_path": "D:\\Backups\\BMS\\application", "config_path": "D:\\apache-tomcat-9.0.98\\conf", "stop_services_timeout": 60, "start_services_timeout": 120}}, "backup": {"base_path": "D:\\Backups\\BMS", "retention_days": 30, "verify_before_restore": true, "parallel_restore": true}, "network": {"internal_endpoints": [{"name": "postgresql_db", "host": "localhost", "port": 5432}, {"name": "sqlserver_db", "host": "**************", "port": 2433}, {"name": "web_server", "host": "localhost", "port": 8080}], "external_endpoints": [{"name": "backup_server", "host": "backup-server.example.com", "port": 443}, {"name": "dns_server", "host": "*******", "port": 53}], "dns_test_hosts": ["google.com", "backup-server.example.com", "localhost"]}, "recovery": {"staging_path": "D:\\Recovery\\Staging", "temp_path": "D:\\Recovery\\Temp", "log_path": "D:\\Recovery\\Logs", "max_recovery_time_hours": 4, "verification_tests": [{"name": "database_connectivity", "type": "database", "timeout_seconds": 60}, {"name": "application_startup", "type": "service", "timeout_seconds": 120}, {"name": "web_response", "type": "http", "timeout_seconds": 30}]}, "failover": {"enabled": true, "automatic_failover": false, "failover_threshold_minutes": 15, "failover_checks": [{"name": "primary_database", "type": "database", "critical": true}, {"name": "application_services", "type": "service", "critical": true}, {"name": "web_endpoints", "type": "http", "critical": false}]}, "testing": {"enabled": true, "test_schedule": "0 2 * * 6", "test_types": ["database_restore", "application_restore", "full_recovery"], "test_environment": {"database_server": "test-db-server", "application_server": "test-app-server", "isolated_network": true}}, "notifications": {"enabled": true, "email": {"enabled": true, "smtp_server": "smtp.example.com", "smtp_port": 587, "use_ssl": true, "username": "<EMAIL>", "password": "email_password", "from_address": "<EMAIL>", "to_addresses": ["<EMAIL>", "<EMAIL>"]}, "slack": {"enabled": false, "webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "channel": "#disaster-recovery"}, "sms": {"enabled": true, "provider": "twi<PERSON>", "account_sid": "your_twilio_account_sid", "auth_token": "your_twilio_auth_token", "from_number": "+**********", "to_numbers": ["+**********", "+**********"]}}, "security": {"encrypt_staging": true, "secure_communications": true, "audit_all_actions": true, "require_approval": true, "approvers": ["<EMAIL>", "<EMAIL>"]}, "compliance": {"audit_retention_days": 365, "compliance_checks": ["backup_encryption", "access_logging", "recovery_testing"], "reporting": {"enabled": true, "schedule": "0 8 1 * *", "recipients": ["<EMAIL>", "<EMAIL>"]}}}