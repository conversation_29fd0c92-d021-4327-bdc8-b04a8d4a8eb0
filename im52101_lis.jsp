<%-- JSP Page Init --%>
<%@page import="com.codecharge.*,
                com.codecharge.components.*,
                com.codecharge.util.*,
                com.codecharge.events.*,
                com.codecharge.feature.*,
                com.codecharge.db.*,
                com.codecharge.validation.*,
                java.util.*,
                java.io.*,
                com.codecharge.util.cache.CacheEvent,
                com.codecharge.util.cache.ICache,
                com.codecharge.template.*"%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>

<%if ((new im52101_lisServiceChecker()).check(request, response, getServletContext())) return;%>

<%-- Page Body --%>
<%@include file="im52101_lisHandlers.jsp"%>
<%
    if (!im52101_lisModel.isVisible()) return;
    if (im52101_lisParent != null) {
        if (!im52101_lisParent.getChild(im52101_lisModel.getName()).isVisible()) return;
    }
    
    pageContext.setAttribute("parent", im52101_lisModel);
    pageContext.setAttribute("page", im52101_lisModel);
    im52101_lisModel.fireOnInitializeViewEvent(new Event());
    im52101_lisModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) im52101_lisModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!im52101_lisModel.isVisible()) return;
%>

<!DOCTYPE HTML>
<html>
<head>
    <ccs:meta header="Content-Type"/>  
    <title>批次Excel匯入列表</title>
    <ccs:attribute owner='page' name='scriptIncludes'/>
    
    <%-- Scripts --%>
    <script src="ClientI18N.jsp?file=Functions.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
    <script src="ClientI18N.jsp?file=DatePicker.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>
    <script src="ClientI18N.jsp?file=Globalize.js&amp;locale=<ccs:message key="CCS_LocaleID"/>" type="text/javascript" charset="utf-8"></script>

    <%-- Bootstrap --%>
    <link rel="stylesheet" type="text/css" href="javascript/bootstrap-3.3.7-dist/css/bootstrap.min.css">
    <script src="javascript/bootstrap-3.3.7-dist/js/bootstrap.min.js" type="text/javascript"></script>
    
    <%-- Custom Styles and Scripts --%>
    <link rel="stylesheet" type="text/css" href="in_recordgridstyle.css?11009110001">
    <link rel="stylesheet" type="text/css" href="im52101_lis.css"> <%-- Custom CSS for this page --%>
    <script src="functions_synct_im.js?1101006" type="text/javascript"></script>
    <script src="functions_synct.js?110100501" type="text/javascript"></script>

    <script type="text/javascript">
        jQuery(function ($) {
            var features = {};
            var actions = {};
            var params = {};

            actions["SearchOnLoad"] = function (eventType, parameters) {
                var result = true;
                var message = $("#Searchwatchdog").val();
                
                if (message.length > 0) {
                    $(".NoRecords").hide();
                    $("#Searchwatchdog").val("");
                    
                    if (message === "exceedLimit") {
                        $(".exceed-limit").removeClass("hide");
                    } else if (message === "noSearchParameter") {
                        $(".no-search-parameter").removeClass("hide");
                    }
                }
                addSlash(document.forms["Search"].elements["s_upload_date_from"]);
                addSlash(document.forms["Search"].elements["s_upload_date_to"]);
            };

            actions["SearchOnSubmit"] = function (eventType, parameters) {
                var result = true;
                var S_DATE = document.forms["Search"].s_upload_date_from.value;
                var E_DATE = document.forms["Search"].s_upload_date_to.value;

                document.forms["Search"].s_upload_date_from.value = S_DATE.replace(/\D/g, "");
                document.forms["Search"].s_upload_date_to.value = E_DATE.replace(/\D/g, "");
                return result;
            };

            $('*:ccsControl(Search)').ccsBind(function() {
                this.bind("submit", actions["SearchOnSubmit"]);
            });

            $('*:ccsControl(Search)').ccsBind(function() {
                this.each(function() { 
                    actions["SearchOnLoad"].call(this); 
                });
            });

            $('*:ccsControl(Search, Button_Clean)').ccsBind(function() {
                this.bind("click", function() { 
                    $("body").data("disableValidation", true); 
                });
            });
        });

        Search_DatePicker_s_upload_date_from = {
            format: "yyy/mm/dd", style: "Styles/Blueprint/Style.css", relativePathPart: "", themeVersion: "3.0"
        };
        Search_DatePicker_s_upload_date_to = {
            format: "yyy/mm/dd", style: "Styles/Blueprint/Style.css", relativePathPart: "", themeVersion: "3.0"
        };

        function addSlash(obj){
            if (obj.value.length > 0 && obj.value.indexOf("/") == -1) {
                var padding0Str = "";
                for (var loop = 0; loop < (7 - obj.value.length); loop++) padding0Str += "0";
                obj.value = padding0Str + obj.value;
                obj.value = RSplitChar(obj.value, "/", "2,2");
            }
        }

       jQuery(function($) {
            function updateStatusCells() {
                $('.status-cell').each(function() { // Changed class selector
                    const statusCell = $(this);
                    const row = statusCell.closest('tr');
                    const importId = row.find('input[id^="listimport_id_"]').val(); // Assuming hidden input for import_id
                    const currentStatusText = statusCell.find('.status-text').text().trim();
                    
                    // Only update if status is typically pending or in progress
                    if (['待處理', '處理中', '排程中'].includes(currentStatusText) && importId) {
                        $.ajax({
                            url: 'im52101_ajax.jsp', // URL updated
                            method: 'GET',
                            data: { id: importId },
                            dataType: 'json',
                            success: function(response) {
                                if (response && response.status) {
                                    statusCell.find('.status-text').html(response.status);
                                    
                                    // 根據狀態和處理記錄顯示對應連結
                                    updateProcessingLinks(statusCell, importId, response);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Status update failed for import ID ' + importId + ':', error);
                            }
                        });
                    } 
                });
            }

            // 初始載入時也要更新處理連結
            updateAllProcessingLinks();
            
            updateStatusCells();
            setInterval(updateStatusCells, 30000); // Update every 30 seconds
        });

        // 更新處理連結顯示邏輯
        function updateProcessingLinks(statusCell, importId, statusData) {
            const status = statusData.status || statusCell.find('.status-text').text().trim();
            const processingDetailsLink = statusCell.find('.processing-details-link');
            const exceptionReportLink = statusCell.find('.exception-report-link');
            
            // 預設隱藏所有連結
            processingDetailsLink.hide();
            exceptionReportLink.hide();
            
            // 使用 AJAX 回傳的詳細資訊來決定顯示哪些連結
            if (statusData.canViewDetails) {
                processingDetailsLink.show();
            }
            
            if (statusData.canViewExceptions) {
                exceptionReportLink.show();
            }
            
            // 如果沒有 AJAX 資料，使用舊的邏輯作為備援
            if (!statusData.hasOwnProperty('canViewDetails')) {
                if (['匯入完成', 'COMPLETED', '匯入失敗', 'FAILED', '部分成功'].includes(status)) {
                    processingDetailsLink.show();
                    
                    // 檢查是否有異常記錄（舊邏輯）
                    if (['匯入失敗', 'FAILED', '部分成功'].includes(status)) {
                        exceptionReportLink.show();
                    }
                }
            }
        }
        
        // 初始化所有行的處理連結
        function updateAllProcessingLinks() {
            $('.status-cell').each(function() {
                const statusCell = $(this);
                const row = statusCell.closest('tr');
                const importId = row.find('input[id^="listimport_id_"]').val();
                
                if (importId) {
                    updateProcessingLinks(statusCell, importId, {});
                }
            });
        }
        
        // 查看處理記錄詳情
        function viewProcessingDetails(importId) {
            window.open('processing_log_frontend.jsp?import_id=' + importId, '_blank', 'width=1400,height=900,scrollbars=yes');
        }
        
        // 查看異常報告
        function viewExceptionReport(importId) {
            window.open('processing_log_frontend.jsp?import_id=' + importId + '&view=exceptions', '_blank', 'width=1400,height=900,scrollbars=yes');
        }
        
    </script>
</head>

<body>
    <div class="container">
        <div class="up_titte">
            <span class="tittle_span">&nbsp;&nbsp;&nbsp;</span>
            <span class="tittle_label">&nbsp;批次Excel匯入列表</span> 
        </div>
        
        <%-- <ccs:record name='Search'>
        <form id="Search" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
            <table class="table" cellspacing="0" cellpadding="0">
                <ccs:error_block>
                    <tr id="{@error-block}" class="Error">
                        <td colspan="2"><ccs:error_text/></td> 
                    </tr>
                </ccs:error_block>
                
                <tr class="Controls">
                    <td class="th_lis" style="width: 10%; border-bottom: 0">
                        <label for="Searchs_S_DATE">日期&nbsp;</label>
                    </td>
                    <td style="border-top: 0; vertical-align: middle; min-width: 400px">
                        <input type="text" id="Searchs_S_DATE" maxlength="9"
                               size="7" value="<ccs:control name='S_DATE'/>"
                               name="<ccs:control name='S_DATE' property='name'/>"> 
                       <a href="javascript:showDatePicker('Search_DatePicker_S_DATE1','Search','S_DATE');"
                               id="SearchDatePicker_S_DATE1">
                                <img id="SearchDatePicker_S_DATE1_Image" border="0"
                                     alt="Show Date Picker"
                                     src="Styles/Blueprint/Images/DatePicker.gif">
                            </a>
                        &nbsp;～&nbsp;
                        <input type="text" id="Searchs_E_DATE"
                               maxlength="9" size="7" value="<ccs:control name='E_DATE'/>"
                               name="<ccs:control name='E_DATE' property='name'/>"> 
                        <ccs:datepicker name='DatePicker_E_DATE1'>
                            <a href="javascript:showDatePicker('<ccs:dpvalue property='Name'/>','<ccs:dpvalue property='FormName'/>','<ccs:dpvalue property='DateControl'/>');"
                               id="SearchDatePicker_E_DATE1">
                                <img id="SearchDatePicker_E_DATE1_Image" border="0"
                                     alt="Show Date Picker"
                                     src="Styles/Blueprint/Images/DatePicker.gif">
                            </a>
                        </ccs:datepicker>
                    </td>
                </tr>
                
                <tr class="Bottom">
                    <td style="text-align: right" colspan="2">
                        <input type="hidden" id="Searchwatchdog" 
                               value="<ccs:control name='watchdog'/>" 
                               name="<ccs:control name='watchdog' property='name'/>">                            
                        <ccs:button name='Button_DoSearch'>
                            <input type="submit" id="SearchButton_DoSearch" 
                                   class="btn btn-primary" alt="搜尋" 
                                   value="搜尋" 
                                   name="<ccs:control name='Button_DoSearch' property='name'/>">
                        </ccs:button>
                        <ccs:button name='Button_Clean'>
                            <input type="submit" id="SearchButton_Clean" 
                                   class="btn btn-success" alt="清除" 
                                   value="清除" 
                                   name="<ccs:control name='Button_Clean' property='name'/>">
                        </ccs:button>
                    </td> 
                </tr>
            </table>
        </form>
        </ccs:record> --%>

        <ccs:grid name='list'>
        <table class="table table-hover" cellspacing="0" cellpadding="0"> <%-- Added table-hover for better UX --%>
            <tr class="Row">
                <td colspan="7">[搜尋結果] 共&nbsp;<ccs:control name='list_TotalRecords'/>&nbsp;筆</td> 
            </tr>

            <tr class="Caption">
                <th scope="col" style="width: 15%;">
                    <ccs:sorter name='Sorter_import_id' column='import_id'> <span class="Sorter">
                        <a href="<ccs:sorter_href/>" id="listSorter_import_id">項次</a> 
                        <ccs:asc_on><img alt="ASC" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
                        <ccs:desc_on><img alt="DESC" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on>
                    </span></ccs:sorter>
                </th>
                 <th scope="col" style="width: 15%;">
                    <ccs:sorter name='Sorter_upload_timestamp' column='upload_timestamp'> <span class="Sorter">
                        <a href="<ccs:sorter_href/>" id="listSorter_upload_timestamp">上傳日期</a> 
                        <ccs:asc_on><img alt="ASC" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
                        <ccs:desc_on><img alt="DESC" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on>
                    </span></ccs:sorter>
                </th>

                <th scope="col">
                    <ccs:sorter name='Sorter_original_file_name' column='original_file_name'><span class="Sorter">
                        <a href="<ccs:sorter_href/>" id="listSorter_original_file_name">原始檔名</a> 
                        <ccs:asc_on><img alt="ASC" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
                        <ccs:desc_on><img alt="DESC" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on>
                    </span></ccs:sorter>
                </th>
                 <th scope="col" style="width: 20%;">
                    <ccs:sorter name='Sorter_acc_memo' column='acc_memo'><span class="Sorter">
                        <a href="<ccs:sorter_href/>" id="listSorter_acc_memo">備註</a> 
                        <ccs:asc_on><img alt="ASC" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
                        <ccs:desc_on><img alt="DESC" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on>
                    </span></ccs:sorter>
                </th>
                <th scope="col" style="width: 10%;">
                    <ccs:sorter name='Sorter_status' column='status'><span class="Sorter">
                        <a href="<ccs:sorter_href/>" id="listSorter_status">處理狀態</a> 
                        <ccs:asc_on><img alt="ASC" src="Styles/Blueprint/Images/Asc.gif"></ccs:asc_on>
                        <ccs:desc_on><img alt="DESC" src="Styles/Blueprint/Images/Desc.gif"></ccs:desc_on>
                    </span></ccs:sorter>
                </th>
            </tr>

            <ccs:repeater>
                <ccs:row>
                    <tr class="Row">
                        <td style="WHITE-SPACE: nowrap"><ccs:control name='number' />
                            <a style="display: none" href="im52101_man.jsp?import_id=<ccs:control name='import_id_link'  property='href'/>">TODO</a>
                             <input type="hidden" id="listimport_id_<ccs:attribute owner='list' name='rowNumber'/>" value="<ccs:control name='import_id_hidden'/>" name="<ccs:control name='import_id_hidden' property='name'/>">
                        </td> 
                        <td><ccs:control name='upload_timestamp'/>&nbsp;</td> 
                        
                        <td><ccs:control name='original_file_name'/>&nbsp;</td> 
                        <td><ccs:control name='acc_memo'/>&nbsp;</td> 
                        <td class="status-cell">
                            <span class="status-text"><ccs:control name='status'/></span>
                            <div class="processing-links">
                                <!-- 這些連結將由 JavaScript 和 Handlers 動態控制顯示 -->
                                <a href="#" class="btn btn-sm btn-info processing-details-link" style="display:none;" 
                                   onclick="viewProcessingDetails('<ccs:control name='import_id_hidden'/>');">查看處理記錄</a>
                                <a href="#" class="btn btn-sm btn-warning exception-report-link" style="display:none;" 
                                   onclick="viewExceptionReport('<ccs:control name='import_id_hidden'/>');">查看異常報告</a>
                            </div>
                        </td>
                    </tr>
                </ccs:row>
            </ccs:repeater>

            <ccs:norecords>
                <tr class="NoRecords">
                    <td colspan="7">無任何資料</td> 
                </tr>
                <tr class="exceed-limit Error hide">
                    <td colspan="7">符合搜尋條件的資料超過30,000筆，請輸入更明確的條件</td> 
                </tr>
                <tr class="no-search-parameter Statement hide">
                    <td colspan="7">請先輸入搜尋條件</td> 
                </tr>
            </ccs:norecords>

            <tr class="Footer">
                <td colspan="7" style="text-align: center;">
                    <ccs:block name='Link_insert'><a style="float: left;" class="btn btn-primary" href="im52101_man.jsp" id="SearchLink_insert_<ccs:attribute owner = 'list' name = 'rowNumber' />">新增</a></ccs:block>
                    <%-- Optional: Add a download all/selected button if applicable --%>
                    <ccs:navigator name='Navigator' type='Simple' size='10'><span class="Navigator">
                        <ccs:first_on><a href="<ccs:page_href/>"><img alt="First" src="Styles/Blueprint/Images/First.gif"></a></ccs:first_on>
                        <ccs:prev_on><a href="<ccs:page_href/>"><img alt="Prev" src="Styles/Blueprint/Images/Prev.gif"></a></ccs:prev_on>
                        第&nbsp;<ccs:page_number/>&nbsp;頁&nbsp;／&nbsp;共&nbsp;<ccs:total_pages/>&nbsp;頁 
                        <ccs:next_on><a href="<ccs:page_href/>"><img alt="Next" src="Styles/Blueprint/Images/Next.gif"></a></ccs:next_on>
                        <ccs:last_on><a href="<ccs:page_href/>"><img alt="Last" src="Styles/Blueprint/Images/Last.gif"></a></ccs:last_on>
                    </span></ccs:navigator>&nbsp;
                </td> 
            </tr>
        </table>
        </ccs:grid>
    </div>
</body>
</html>

<%-- JSP Page BeforeOutput --%>
<%im52101_lisModel.fireBeforeOutputEvent();%>

<%-- JSP Page Unload --%>
<%im52101_lisModel.fireBeforeUnloadEvent();%> 