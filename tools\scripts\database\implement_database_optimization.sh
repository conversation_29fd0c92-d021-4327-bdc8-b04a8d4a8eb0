#!/bin/bash
# ========================================================================
# 新北市違章建築管理系統 - 資料庫效能優化實施腳本
# ========================================================================
# 
# 目的：安全地實施資料庫效能優化
# 作者：Claude Code Assistant
# 日期：2025-07-09
# 
# 使用方式：
# chmod +x implement_database_optimization.sh
# ./implement_database_optimization.sh
# ========================================================================

# 設定環境變數
export PGPASSWORD='S!@h@202203'
export PGHOST='localhost'
export PGPORT='5432'
export PGUSER='postgres'
export PGDATABASE='bms'

# 設定顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌檔案
LOG_FILE="/tmp/db_optimization_$(date +%Y%m%d_%H%M%S).log"

# 函數：列印訊息
log_message() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_FILE"
}

# 函數：檢查資料庫連線
check_database_connection() {
    log_message "檢查資料庫連線..."
    
    if ! psql -c "SELECT 1;" > /dev/null 2>&1; then
        log_error "無法連線到資料庫，請檢查連線設定"
        exit 1
    fi
    
    log_message "資料庫連線正常"
}

# 函數：備份現有索引資訊
backup_current_indexes() {
    log_message "備份現有索引資訊..."
    
    psql -c "
    SELECT 
        'CREATE INDEX ' || indexname || ' ON ' || tablename || ' ' || indexdef
    FROM pg_indexes 
    WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
    ORDER BY tablename, indexname;
    " > "index_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    log_message "索引備份完成"
}

# 函數：檢查磁碟空間
check_disk_space() {
    log_message "檢查磁碟空間..."
    
    # 獲取目前資料庫大小
    DB_SIZE=$(psql -t -c "SELECT pg_size_pretty(pg_database_size('$PGDATABASE'));")
    log_message "目前資料庫大小：$DB_SIZE"
    
    # 檢查可用空間
    AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
    log_message "可用磁碟空間：$AVAILABLE_SPACE"
    
    # 簡單的空間檢查（實際實施時可能需要更精確的計算）
    log_warning "請確保至少有 2GB 可用空間用於索引建立"
}

# 函數：建立索引前的準備工作
pre_optimization_tasks() {
    log_message "執行優化前的準備工作..."
    
    # 更新統計資訊
    log_message "更新統計資訊..."
    psql -c "
    ANALYZE ibmcase;
    ANALYZE ibmfym;
    ANALYZE ibmlist;
    ANALYZE ibmsts;
    " >> "$LOG_FILE" 2>&1
    
    # 記錄優化前的效能基準
    log_message "記錄優化前的效能基準..."
    psql -c "
    SELECT 
        'Performance Baseline' as metric_type,
        schemaname,
        tablename,
        seq_scan,
        seq_tup_read,
        idx_scan,
        idx_tup_fetch,
        n_tup_ins,
        n_tup_upd,
        n_tup_del
    FROM pg_stat_user_tables 
    WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
    ORDER BY tablename;
    " > "performance_baseline_$(date +%Y%m%d_%H%M%S).txt"
}

# 函數：建立索引
create_indexes() {
    log_message "開始建立優化索引..."
    
    # 讀取並執行索引建立腳本
    if [ -f "database_optimization_indexes.sql" ]; then
        log_message "執行索引建立腳本..."
        
        # 分段執行，避免長時間鎖定
        log_message "建立 ibmcase 表索引..."
        psql -c "
        -- ibmcase 表關鍵索引
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmcase_status_process 
        ON ibmcase (status, ib_prcs, dis_type, dis_sort);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmcase_dates 
        ON ibmcase (reg_date, audnm_date, reg_rec_date);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmcase_employees 
        ON ibmcase (reg_emp, b_notice_emp, rsult_emp, dmltn_emp);
        " >> "$LOG_FILE" 2>&1
        
        if [ $? -eq 0 ]; then
            log_message "ibmcase 表索引建立完成"
        else
            log_error "ibmcase 表索引建立失敗"
            return 1
        fi
        
        log_message "建立 ibmfym 表索引..."
        psql -c "
        -- ibmfym 表關鍵索引
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmfym_case_flow 
        ON ibmfym (case_id, acc_date, acc_time, acc_job);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmfym_results 
        ON ibmfym (acc_rlt, acc_rlt2, acc_date);
        " >> "$LOG_FILE" 2>&1
        
        if [ $? -eq 0 ]; then
            log_message "ibmfym 表索引建立完成"
        else
            log_error "ibmfym 表索引建立失敗"
            return 1
        fi
        
        log_message "建立 ibmlist 表索引..."
        psql -c "
        -- ibmlist 表關鍵索引
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmlist_case_files 
        ON ibmlist (case_id, pic_kind, pic_seq, cr_date);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmlist_file_types 
        ON ibmlist (filekind, pic_kind, thombed);
        " >> "$LOG_FILE" 2>&1
        
        if [ $? -eq 0 ]; then
            log_message "ibmlist 表索引建立完成"
        else
            log_error "ibmlist 表索引建立失敗"
            return 1
        fi
        
        log_message "建立 ibmsts 表索引..."
        psql -c "
        -- ibmsts 表關鍵索引
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmsts_case_status 
        ON ibmsts (case_id, acc_rlt, acc_date);
        
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ibmsts_status_time 
        ON ibmsts (acc_date, acc_time, acc_job);
        " >> "$LOG_FILE" 2>&1
        
        if [ $? -eq 0 ]; then
            log_message "ibmsts 表索引建立完成"
        else
            log_error "ibmsts 表索引建立失敗"
            return 1
        fi
        
    else
        log_error "找不到索引建立腳本 database_optimization_indexes.sql"
        return 1
    fi
    
    log_message "所有索引建立完成"
    return 0
}

# 函數：驗證索引建立結果
verify_indexes() {
    log_message "驗證索引建立結果..."
    
    # 檢查新建立的索引
    INDEX_COUNT=$(psql -t -c "
    SELECT COUNT(*) FROM pg_indexes 
    WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
    AND indexname LIKE 'idx_%';
    ")
    
    log_message "已建立的優化索引數量：$INDEX_COUNT"
    
    # 檢查索引大小
    psql -c "
    SELECT 
        'Index Size Check' as check_type,
        schemaname,
        tablename,
        indexname,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size
    FROM pg_stat_user_indexes 
    WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
    AND indexname LIKE 'idx_%'
    ORDER BY pg_relation_size(indexrelid) DESC;
    " > "index_verification_$(date +%Y%m%d_%H%M%S).txt"
    
    log_message "索引驗證完成，結果已儲存"
}

# 函數：優化後的效能測試
performance_test() {
    log_message "執行效能測試..."
    
    # 清除查詢計劃快取
    psql -c "SELECT pg_stat_reset();" >> "$LOG_FILE" 2>&1
    
    # 執行一些典型查詢並測量時間
    log_message "測試案件狀態查詢效能..."
    psql -c "
    EXPLAIN (ANALYZE, BUFFERS) 
    SELECT case_id, status, reg_date, ib_prcs 
    FROM ibmcase 
    WHERE status = '231' 
    AND reg_date >= 1130101 
    ORDER BY reg_date DESC 
    LIMIT 100;
    " > "performance_test_$(date +%Y%m%d_%H%M%S).txt"
    
    log_message "測試案件流程查詢效能..."
    psql -c "
    EXPLAIN (ANALYZE, BUFFERS) 
    SELECT case_id, acc_date, acc_rlt, acc_job 
    FROM ibmfym 
    WHERE case_id LIKE 'A113%' 
    AND acc_date >= 1130101 
    ORDER BY acc_date DESC 
    LIMIT 50;
    " >> "performance_test_$(date +%Y%m%d_%H%M%S).txt"
    
    log_message "效能測試完成"
}

# 函數：設定維護任務
setup_maintenance() {
    log_message "設定維護任務..."
    
    # 建立維護腳本目錄
    mkdir -p ./maintenance_scripts
    
    # 建立每日維護腳本
    cat > ./maintenance_scripts/daily_maintenance.sh << 'EOF'
#!/bin/bash
export PGPASSWORD='S!@h@202203'
export PGHOST='localhost'
export PGPORT='5432'
export PGUSER='postgres'
export PGDATABASE='bms'

echo "$(date): Starting daily maintenance"
psql -c "ANALYZE ibmcase; ANALYZE ibmfym; ANALYZE ibmlist; ANALYZE ibmsts;"
echo "$(date): Daily maintenance completed"
EOF
    
    chmod +x ./maintenance_scripts/daily_maintenance.sh
    
    # 建立監控腳本
    cat > ./maintenance_scripts/monitor_performance.sh << 'EOF'
#!/bin/bash
export PGPASSWORD='S!@h@202203'
export PGHOST='localhost'
export PGPORT='5432'
export PGUSER='postgres'
export PGDATABASE='bms'

echo "$(date): Performance monitoring report"
psql -c "
SELECT 
    'Index Usage' as metric_type,
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
AND indexname LIKE 'idx_%'
ORDER BY idx_scan DESC
LIMIT 20;
"
EOF
    
    chmod +x ./maintenance_scripts/monitor_performance.sh
    
    log_message "維護腳本已建立在 ./maintenance_scripts/ 目錄"
}

# 函數：生成優化報告
generate_report() {
    log_message "生成優化報告..."
    
    REPORT_FILE="database_optimization_report_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$REPORT_FILE" << EOF
# 資料庫效能優化實施報告

## 實施日期
$(date '+%Y-%m-%d %H:%M:%S')

## 優化前狀態
- 資料庫大小：$(psql -t -c "SELECT pg_size_pretty(pg_database_size('$PGDATABASE'));")
- 主要表記錄數：
$(psql -c "
SELECT 
    'ibmcase' as table_name, COUNT(*) as row_count FROM ibmcase
UNION SELECT 'ibmfym' as table_name, COUNT(*) as row_count FROM ibmfym
UNION SELECT 'ibmlist' as table_name, COUNT(*) as row_count FROM ibmlist
UNION SELECT 'ibmsts' as table_name, COUNT(*) as row_count FROM ibmsts
ORDER BY row_count DESC;
")

## 已建立的索引
$(psql -c "
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
WHERE tablename IN ('ibmcase', 'ibmfym', 'ibmlist', 'ibmsts')
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
")

## 建議的後續步驟
1. 監控索引使用情況
2. 執行每日維護腳本
3. 定期檢查效能指標
4. 根據查詢模式調整索引策略

## 維護腳本位置
- 每日維護：./maintenance_scripts/daily_maintenance.sh
- 效能監控：./maintenance_scripts/monitor_performance.sh

## 日誌檔案
- 實施日誌：$LOG_FILE
- 效能測試：performance_test_*.txt
- 索引驗證：index_verification_*.txt
EOF
    
    log_message "優化報告已生成：$REPORT_FILE"
}

# 主要執行流程
main() {
    log_message "開始資料庫效能優化實施"
    log_message "========================================="
    
    # 檢查前置條件
    check_database_connection
    check_disk_space
    
    # 詢問使用者確認
    echo
    echo -e "${YELLOW}即將開始資料庫效能優化，這個過程可能需要 30-60 分鐘。${NC}"
    echo -e "${YELLOW}優化期間資料庫效能可能會受到影響。${NC}"
    echo
    read -p "是否繼續？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_message "使用者取消操作"
        exit 0
    fi
    
    # 執行優化步驟
    backup_current_indexes
    if [ $? -ne 0 ]; then
        log_error "備份失敗，中止操作"
        exit 1
    fi
    
    pre_optimization_tasks
    if [ $? -ne 0 ]; then
        log_error "前置作業失敗，中止操作"
        exit 1
    fi
    
    create_indexes
    if [ $? -ne 0 ]; then
        log_error "索引建立失敗，請檢查日誌檔案"
        exit 1
    fi
    
    verify_indexes
    performance_test
    setup_maintenance
    generate_report
    
    log_message "========================================="
    log_message "資料庫效能優化實施完成！"
    log_message "詳細日誌請查看：$LOG_FILE"
    log_message "優化報告：database_optimization_report_*.md"
    log_message "維護腳本：./maintenance_scripts/"
    
    # 顯示後續建議
    echo
    echo -e "${GREEN}後續建議：${NC}"
    echo -e "${BLUE}1. 執行監控腳本檢查效能改善情況${NC}"
    echo -e "${BLUE}2. 設定 cron 作業執行每日維護${NC}"
    echo -e "${BLUE}3. 觀察應用程式效能變化${NC}"
    echo -e "${BLUE}4. 根據使用情況調整索引策略${NC}"
    echo
}

# 執行主程式
main "$@"