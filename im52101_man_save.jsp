<%@page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="com.codecharge.util.*, com.codecharge.db.*"%>
<%@page import="java.sql.Connection, java.sql.PreparedStatement, java.sql.SQLException"%>

<%
    // Constants
    final String CONNECTION_NAME = "DBConn";
    final String DB_TABLE_NAME = "public.im52101_excel_imports";
    final String ID_COLUMN = "import_id";
    final String MEMO_COLUMN = "acc_memo";
    final String STATUS_COLUMN = "status";
    final String MEMO_UPDATE_TIMESTAMP_COLUMN = "memo_update_timestamp"; // Optional: if you have this column

    // Input parameters
    String importId = request.getParameter("id");
    String accMemo = request.getParameter("acc_memo");

    String redirectURL = "im52101_lis.jsp"; // Default redirect
    String errorMessage = null;

    if (StringUtils.isEmpty(importId)) {
        errorMessage = "錯誤：缺少必要的 'import_id' 參數。";
        // Consider redirecting to an error page or back to man page with error
        // For now, we'll log and redirect to list, but this isn't ideal.
        System.out.println("Error in im52101_man_save.jsp: " + importId);
        //response.sendRedirect(redirectURL + "?error=" + Utils.encodeURL(errorMessage));
        return;
    }

    // accMemo can be null or empty, trim if not null
    if (accMemo != null) {
        accMemo = accMemo.trim();
    }

    DBConnectionManager dbcm = null;
    Connection conn = null;
    PreparedStatement pstmt = null;
    String result = "1";
    try {
        dbcm = DBConnectionManager.getInstance();
        conn = dbcm.getConnection(CONNECTION_NAME);
        conn.setAutoCommit(false); // Start transaction

        // Construct the SQL query
        // Update both memo and status columns
        String sql = "UPDATE " + DB_TABLE_NAME + " SET " + MEMO_COLUMN + " = ?, " + STATUS_COLUMN + " = 'NEW_UPLOAD'";
        // Example: Add timestamp update if column exists
        // sql += ", " + MEMO_UPDATE_TIMESTAMP_COLUMN + " = NOW()";
        sql += " WHERE " + ID_COLUMN + " = ?";

        pstmt = conn.prepareStatement(sql);
        pstmt.setString(1, accMemo);
        pstmt.setString(2, importId);

        int rowsAffected = pstmt.executeUpdate();

        if (rowsAffected > 0) {
            conn.commit(); // Commit transaction
           
            // Successfully updated, redirect to list page (perhaps with a success message)
            //redirectURL = "im52101_lis.jsp?save_status=success&id=" + Utils.encodeURL(importId);
        } else {
            // No rows updated, could mean import_id not found or memo was already the same.
            // For simplicity, we treat as success if no error, or you can add specific handling.
            conn.rollback(); // Rollback if no rows affected, or if you prefer to signal this differently
            application.log("Warning in im52101_man_save.jsp: No rows updated for import_id: " + importId);
            // redirectURL = "im52101_man.jsp?id=" + Utils.encodeURL(importId) + "&error=" + Utils.encodeURL("找不到對應的匯入ID或備註未變更。");
        }

    } catch (SQLException sqle) {
        result = "0";
        if (conn != null) try { conn.rollback(); } catch (SQLException ex) { application.log("Error rolling back transaction: " + ex.getMessage(), ex); }
        errorMessage = "資料庫儲存備註時發生錯誤: " + sqle.getMessage();
    } catch (Exception e) {
        result = "0";
        if (conn != null) try { conn.rollback(); } catch (SQLException ex) { application.log("Error rolling back transaction: " + ex.getMessage(), ex); }
        errorMessage = "儲存備註時發生未預期錯誤: " + e.getMessage();
    } finally {
        if (pstmt != null) try { pstmt.close(); } catch (SQLException e) { /* ignore */ }
        if (conn != null) {
            try {
                dbcm.freeConnection(CONNECTION_NAME, conn);
            } catch (Exception e) {
                application.log("Error closing connection in im52101_man_save.jsp: " + e.getMessage(), e);
            }
        }
    }

    	out.println("{\"success\":\"" + result + "\"}");
%> 