# Application Backup Script for BMS System
# Author: System Administrator
# Purpose: Automated backup of Tomcat application, configuration files, and system components

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("full", "incremental", "configuration", "logs")]
    [string]$BackupType = "full",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\backup_config.json",
    
    [Parameter(Mandatory=$false)]
    [switch]$Compress = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$IncludeLogs = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$StopServices = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$CrossRegionReplicate = $false
)

# Import required modules
Import-Module -Name ".\modules\BackupLogger.psm1" -Force
Import-Module -Name ".\modules\BackupMetrics.psm1" -Force
Import-Module -Name ".\modules\BackupNotification.psm1" -Force

# Global variables
$script:BackupStartTime = Get-Date
$script:BackupMetrics = @{}
$script:Logger = $null
$script:ServicesStopped = @()

# Initialize logging
function Initialize-BackupLogging {
    param(
        [string]$LogPath = ".\logs\application_backup_$(Get-Date -Format 'yyyyMMdd').log"
    )
    
    $script:Logger = New-BackupLogger -LogPath $LogPath -LogLevel "INFO"
    $script:Logger.Info("Application backup script started - Type: $BackupType")
}

# Load configuration
function Get-BackupConfiguration {
    param(
        [string]$ConfigFile
    )
    
    if (!(Test-Path $ConfigFile)) {
        throw "Configuration file not found: $ConfigFile"
    }
    
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $script:Logger.Info("Configuration loaded successfully")
        return $config
    }
    catch {
        throw "Failed to load configuration: $_"
    }
}

# Create backup directory structure
function Initialize-BackupDirectories {
    param(
        [string]$BackupBasePath
    )
    
    $directories = @(
        "$BackupBasePath\application\full",
        "$BackupBasePath\application\incremental",
        "$BackupBasePath\application\configuration",
        "$BackupBasePath\application\logs",
        "$BackupBasePath\application\metadata",
        "$BackupBasePath\application\compressed",
        "$BackupBasePath\application\cross_region",
        "$BackupBasePath\application\temp"
    )
    
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            $script:Logger.Info("Created backup directory: $dir")
        }
    }
}

# Get service status
function Get-ServiceStatus {
    param(
        [string[]]$ServiceNames
    )
    
    $serviceStatus = @{}
    
    foreach ($serviceName in $ServiceNames) {
        try {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            if ($service) {
                $serviceStatus[$serviceName] = @{
                    "name" = $service.Name
                    "display_name" = $service.DisplayName
                    "status" = $service.Status.ToString()
                    "startup_type" = $service.StartType.ToString()
                }
            } else {
                $serviceStatus[$serviceName] = @{
                    "name" = $serviceName
                    "status" = "NotFound"
                }
            }
        }
        catch {
            $serviceStatus[$serviceName] = @{
                "name" = $serviceName
                "status" = "Error"
                "error" = $_.Exception.Message
            }
        }
    }
    
    return $serviceStatus
}

# Stop services if requested
function Stop-RequiredServices {
    param(
        [string[]]$ServiceNames
    )
    
    if (!$StopServices) {
        $script:Logger.Info("Service stopping is disabled")
        return
    }
    
    $script:Logger.Info("Stopping required services...")
    
    foreach ($serviceName in $ServiceNames) {
        try {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            
            if ($service -and $service.Status -eq "Running") {
                $script:Logger.Info("Stopping service: $($service.DisplayName)")
                
                Stop-Service -Name $serviceName -Force -ErrorAction Stop
                
                # Wait for service to stop
                $timeout = 0
                while ($service.Status -ne "Stopped" -and $timeout -lt 30) {
                    Start-Sleep -Seconds 2
                    $service.Refresh()
                    $timeout += 2
                }
                
                if ($service.Status -eq "Stopped") {
                    $script:ServicesStopped += $serviceName
                    $script:Logger.Info("Service stopped successfully: $($service.DisplayName)")
                } else {
                    $script:Logger.Warning("Service did not stop within timeout: $($service.DisplayName)")
                }
            }
        }
        catch {
            $script:Logger.Warning("Failed to stop service $serviceName`: $_")
        }
    }
}

# Start services that were stopped
function Start-RequiredServices {
    if ($script:ServicesStopped.Count -eq 0) {
        return
    }
    
    $script:Logger.Info("Starting services that were stopped...")
    
    foreach ($serviceName in $script:ServicesStopped) {
        try {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            
            if ($service -and $service.Status -eq "Stopped") {
                $script:Logger.Info("Starting service: $($service.DisplayName)")
                
                Start-Service -Name $serviceName -ErrorAction Stop
                
                # Wait for service to start
                $timeout = 0
                while ($service.Status -ne "Running" -and $timeout -lt 30) {
                    Start-Sleep -Seconds 2
                    $service.Refresh()
                    $timeout += 2
                }
                
                if ($service.Status -eq "Running") {
                    $script:Logger.Info("Service started successfully: $($service.DisplayName)")
                } else {
                    $script:Logger.Warning("Service did not start within timeout: $($service.DisplayName)")
                }
            }
        }
        catch {
            $script:Logger.Warning("Failed to start service $serviceName`: $_")
        }
    }
}

# Get file system information
function Get-FileSystemInfo {
    param(
        [string]$Path
    )
    
    try {
        if (Test-Path $Path) {
            $items = Get-ChildItem -Path $Path -Recurse -Force -ErrorAction SilentlyContinue
            $fileCount = ($items | Where-Object { !$_.PSIsContainer }).Count
            $folderCount = ($items | Where-Object { $_.PSIsContainer }).Count
            $totalSize = ($items | Where-Object { !$_.PSIsContainer } | Measure-Object -Property Length -Sum).Sum
            
            return @{
                "path" = $Path
                "file_count" = $fileCount
                "folder_count" = $folderCount
                "total_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "last_modified" = (Get-Item $Path).LastWriteTime
            }
        } else {
            return @{
                "path" = $Path
                "exists" = $false
            }
        }
    }
    catch {
        return @{
            "path" = $Path
            "error" = $_.Exception.Message
        }
    }
}

# Perform full application backup
function Invoke-FullApplicationBackup {
    param(
        [hashtable]$AppConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting full application backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\application\full\bms_app_full_$timestamp.zip"
    
    try {
        $startTime = Get-Date
        
        # Get application paths from configuration
        $appPaths = $AppConfig.backup_paths
        
        # Create temporary directory for staging
        $tempDir = "$BackupPath\application\temp\$timestamp"
        if (Test-Path $tempDir) {
            Remove-Item -Path $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Copy application files
        $copiedFiles = @()
        $totalSize = 0
        
        foreach ($pathConfig in $appPaths) {
            $sourcePath = $pathConfig.source
            $destinationPath = "$tempDir\$($pathConfig.name)"
            
            if (Test-Path $sourcePath) {
                $script:Logger.Info("Copying: $sourcePath -> $destinationPath")
                
                # Create destination directory
                New-Item -ItemType Directory -Path $destinationPath -Force | Out-Null
                
                # Copy files with exclusions
                $excludePatterns = $pathConfig.exclude_patterns
                
                $items = Get-ChildItem -Path $sourcePath -Recurse -Force -ErrorAction SilentlyContinue
                
                foreach ($item in $items) {
                    $relativePath = $item.FullName.Substring($sourcePath.Length + 1)
                    $shouldExclude = $false
                    
                    # Check exclusion patterns
                    foreach ($pattern in $excludePatterns) {
                        if ($relativePath -match $pattern) {
                            $shouldExclude = $true
                            break
                        }
                    }
                    
                    if (!$shouldExclude) {
                        $destItemPath = "$destinationPath\$relativePath"
                        
                        if ($item.PSIsContainer) {
                            if (!(Test-Path $destItemPath)) {
                                New-Item -ItemType Directory -Path $destItemPath -Force | Out-Null
                            }
                        } else {
                            try {
                                $destItemDir = Split-Path -Path $destItemPath -Parent
                                if (!(Test-Path $destItemDir)) {
                                    New-Item -ItemType Directory -Path $destItemDir -Force | Out-Null
                                }
                                
                                Copy-Item -Path $item.FullName -Destination $destItemPath -Force
                                $copiedFiles += $relativePath
                                $totalSize += $item.Length
                            }
                            catch {
                                $script:Logger.Warning("Failed to copy file: $($item.FullName) - $_")
                            }
                        }
                    }
                }
                
                $script:Logger.Info("Copied $($copiedFiles.Count) files from $sourcePath")
            } else {
                $script:Logger.Warning("Source path does not exist: $sourcePath")
            }
        }
        
        # Create ZIP archive
        $script:Logger.Info("Creating ZIP archive: $backupFile")
        
        # Use 7-Zip for better compression if available
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", "-r", $backupFile, "$tempDir\*") -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path "$tempDir\*" -DestinationPath $backupFile -CompressionLevel Optimal
        }
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Full application backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            $script:Logger.Info("Files copied: $($copiedFiles.Count)")
            $script:Logger.Info("Total original size: $([math]::Round($totalSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["full_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "original_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "files_copied" = $copiedFiles.Count
                "file_path" = $backupFile
                "timestamp" = $timestamp
                "compression_ratio" = [math]::Round((1 - ($fileSize / $totalSize)) * 100, 2)
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Full application backup failed: $_")
        throw
    }
}

# Perform incremental backup
function Invoke-IncrementalApplicationBackup {
    param(
        [hashtable]$AppConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting incremental application backup...")
    
    # Find the latest full backup
    $fullBackupPath = "$BackupPath\application\full"
    $latestFullBackup = Get-ChildItem -Path $fullBackupPath -Filter "*.zip" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if (!$latestFullBackup) {
        throw "No full backup found. Incremental backup requires a full backup first."
    }
    
    $script:Logger.Info("Base full backup: $($latestFullBackup.Name)")
    $baseBackupTime = $latestFullBackup.LastWriteTime
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\application\incremental\bms_app_inc_$timestamp.zip"
    
    try {
        $startTime = Get-Date
        
        # Get application paths from configuration
        $appPaths = $AppConfig.backup_paths
        
        # Create temporary directory for staging
        $tempDir = "$BackupPath\application\temp\$timestamp"
        if (Test-Path $tempDir) {
            Remove-Item -Path $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Find modified files since last full backup
        $modifiedFiles = @()
        $totalSize = 0
        
        foreach ($pathConfig in $appPaths) {
            $sourcePath = $pathConfig.source
            $destinationPath = "$tempDir\$($pathConfig.name)"
            
            if (Test-Path $sourcePath) {
                $script:Logger.Info("Scanning for modified files in: $sourcePath")
                
                # Create destination directory
                New-Item -ItemType Directory -Path $destinationPath -Force | Out-Null
                
                # Get files modified since base backup
                $excludePatterns = $pathConfig.exclude_patterns
                
                $items = Get-ChildItem -Path $sourcePath -Recurse -Force -ErrorAction SilentlyContinue | Where-Object { 
                    !$_.PSIsContainer -and $_.LastWriteTime -gt $baseBackupTime
                }
                
                foreach ($item in $items) {
                    $relativePath = $item.FullName.Substring($sourcePath.Length + 1)
                    $shouldExclude = $false
                    
                    # Check exclusion patterns
                    foreach ($pattern in $excludePatterns) {
                        if ($relativePath -match $pattern) {
                            $shouldExclude = $true
                            break
                        }
                    }
                    
                    if (!$shouldExclude) {
                        $destItemPath = "$destinationPath\$relativePath"
                        
                        try {
                            $destItemDir = Split-Path -Path $destItemPath -Parent
                            if (!(Test-Path $destItemDir)) {
                                New-Item -ItemType Directory -Path $destItemDir -Force | Out-Null
                            }
                            
                            Copy-Item -Path $item.FullName -Destination $destItemPath -Force
                            $modifiedFiles += $relativePath
                            $totalSize += $item.Length
                        }
                        catch {
                            $script:Logger.Warning("Failed to copy file: $($item.FullName) - $_")
                        }
                    }
                }
                
                $script:Logger.Info("Found $($modifiedFiles.Count) modified files in $sourcePath")
            }
        }
        
        if ($modifiedFiles.Count -eq 0) {
            $script:Logger.Info("No modified files found since last full backup")
            Remove-Item -Path $tempDir -Recurse -Force
            
            # Store metrics
            $script:BackupMetrics["incremental_backup"] = @{
                "success" = $true
                "duration_minutes" = 0
                "file_size_mb" = 0
                "files_copied" = 0
                "base_backup" = $latestFullBackup.Name
                "timestamp" = $timestamp
                "no_changes" = $true
            }
            
            return $null
        }
        
        # Create ZIP archive
        $script:Logger.Info("Creating incremental backup ZIP: $backupFile")
        
        # Use 7-Zip for better compression if available
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", "-r", $backupFile, "$tempDir\*") -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path "$tempDir\*" -DestinationPath $backupFile -CompressionLevel Optimal
        }
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Incremental backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            $script:Logger.Info("Modified files: $($modifiedFiles.Count)")
            $script:Logger.Info("Total original size: $([math]::Round($totalSize / 1MB, 2)) MB")
            
            # Store metrics
            $script:BackupMetrics["incremental_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "original_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "files_copied" = $modifiedFiles.Count
                "file_path" = $backupFile
                "base_backup" = $latestFullBackup.Name
                "timestamp" = $timestamp
                "compression_ratio" = [math]::Round((1 - ($fileSize / $totalSize)) * 100, 2)
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Incremental backup failed: $_")
        throw
    }
}

# Perform configuration backup
function Invoke-ConfigurationBackup {
    param(
        [hashtable]$AppConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting configuration backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\application\configuration\bms_config_$timestamp.zip"
    
    try {
        $startTime = Get-Date
        
        # Get configuration paths
        $configPaths = $AppConfig.config_paths
        
        # Create temporary directory for staging
        $tempDir = "$BackupPath\application\temp\config_$timestamp"
        if (Test-Path $tempDir) {
            Remove-Item -Path $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Copy configuration files
        $copiedFiles = @()
        $totalSize = 0
        
        foreach ($configPath in $configPaths) {
            if (Test-Path $configPath) {
                $fileName = Split-Path -Path $configPath -Leaf
                $destPath = "$tempDir\$fileName"
                
                try {
                    Copy-Item -Path $configPath -Destination $destPath -Force
                    $copiedFiles += $fileName
                    $totalSize += (Get-Item $configPath).Length
                    $script:Logger.Info("Copied configuration file: $fileName")
                }
                catch {
                    $script:Logger.Warning("Failed to copy configuration file: $configPath - $_")
                }
            } else {
                $script:Logger.Warning("Configuration file not found: $configPath")
            }
        }
        
        if ($copiedFiles.Count -eq 0) {
            $script:Logger.Warning("No configuration files were copied")
            Remove-Item -Path $tempDir -Recurse -Force
            return $null
        }
        
        # Create ZIP archive
        $script:Logger.Info("Creating configuration backup ZIP: $backupFile")
        
        # Use 7-Zip for better compression if available
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", $backupFile, "$tempDir\*") -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path "$tempDir\*" -DestinationPath $backupFile -CompressionLevel Optimal
        }
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Configuration backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            $script:Logger.Info("Files copied: $($copiedFiles.Count)")
            
            # Store metrics
            $script:BackupMetrics["configuration_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "original_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "files_copied" = $copiedFiles.Count
                "file_path" = $backupFile
                "timestamp" = $timestamp
                "files_list" = $copiedFiles
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Configuration backup failed: $_")
        throw
    }
}

# Perform logs backup
function Invoke-LogsBackup {
    param(
        [hashtable]$AppConfig,
        [string]$BackupPath
    )
    
    $script:Logger.Info("Starting logs backup...")
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BackupPath\application\logs\bms_logs_$timestamp.zip"
    
    try {
        $startTime = Get-Date
        
        # Get log paths
        $logPaths = $AppConfig.log_paths
        
        # Create temporary directory for staging
        $tempDir = "$BackupPath\application\temp\logs_$timestamp"
        if (Test-Path $tempDir) {
            Remove-Item -Path $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
        
        # Copy log files
        $copiedFiles = @()
        $totalSize = 0
        
        foreach ($logPathConfig in $logPaths) {
            $logPath = $logPathConfig.path
            $maxAge = $logPathConfig.max_age_days
            $pattern = $logPathConfig.pattern
            
            if (Test-Path $logPath) {
                $cutoffDate = (Get-Date).AddDays(-$maxAge)
                $logFiles = Get-ChildItem -Path $logPath -Filter $pattern -Recurse -ErrorAction SilentlyContinue | Where-Object { 
                    $_.LastWriteTime -gt $cutoffDate
                }
                
                foreach ($logFile in $logFiles) {
                    $relativePath = $logFile.FullName.Substring($logPath.Length + 1)
                    $destPath = "$tempDir\$($logPathConfig.name)\$relativePath"
                    
                    try {
                        $destDir = Split-Path -Path $destPath -Parent
                        if (!(Test-Path $destDir)) {
                            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
                        }
                        
                        Copy-Item -Path $logFile.FullName -Destination $destPath -Force
                        $copiedFiles += $relativePath
                        $totalSize += $logFile.Length
                    }
                    catch {
                        $script:Logger.Warning("Failed to copy log file: $($logFile.FullName) - $_")
                    }
                }
                
                $script:Logger.Info("Copied $($logFiles.Count) log files from $logPath")
            } else {
                $script:Logger.Warning("Log path not found: $logPath")
            }
        }
        
        if ($copiedFiles.Count -eq 0) {
            $script:Logger.Warning("No log files were copied")
            Remove-Item -Path $tempDir -Recurse -Force
            return $null
        }
        
        # Create ZIP archive
        $script:Logger.Info("Creating logs backup ZIP: $backupFile")
        
        # Use 7-Zip for better compression if available
        if (Get-Command "7z.exe" -ErrorAction SilentlyContinue) {
            $process = Start-Process -FilePath "7z.exe" -ArgumentList @("a", "-tzip", "-mx=9", "-r", $backupFile, "$tempDir\*") -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -ne 0) {
                throw "7-Zip compression failed with exit code: $($process.ExitCode)"
            }
        } else {
            # Fallback to PowerShell compression
            Compress-Archive -Path "$tempDir\*" -DestinationPath $backupFile -CompressionLevel Optimal
        }
        
        # Clean up temporary directory
        Remove-Item -Path $tempDir -Recurse -Force
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMinutes
        
        if (Test-Path $backupFile) {
            $fileSize = (Get-Item $backupFile).Length
            $script:Logger.Info("Logs backup completed successfully in $([math]::Round($duration, 2)) minutes")
            $script:Logger.Info("Backup file size: $([math]::Round($fileSize / 1MB, 2)) MB")
            $script:Logger.Info("Files copied: $($copiedFiles.Count)")
            
            # Store metrics
            $script:BackupMetrics["logs_backup"] = @{
                "success" = $true
                "duration_minutes" = $duration
                "file_size_mb" = [math]::Round($fileSize / 1MB, 2)
                "original_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "files_copied" = $copiedFiles.Count
                "file_path" = $backupFile
                "timestamp" = $timestamp
                "compression_ratio" = [math]::Round((1 - ($fileSize / $totalSize)) * 100, 2)
            }
            
            return $backupFile
        } else {
            throw "Backup file was not created"
        }
    }
    catch {
        $script:Logger.Error("Logs backup failed: $_")
        throw
    }
}

# Cleanup old backups
function Remove-OldBackups {
    param(
        [string]$BackupPath,
        [int]$RetentionDays
    )
    
    $script:Logger.Info("Cleaning up application backups older than $RetentionDays days...")
    
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $deletedCount = 0
    $freedSpace = 0
    
    $backupTypes = @("full", "incremental", "configuration", "logs", "compressed")
    
    foreach ($type in $backupTypes) {
        $typePath = "$BackupPath\application\$type"
        
        if (Test-Path $typePath) {
            $oldFiles = Get-ChildItem -Path $typePath -Recurse -File | Where-Object { $_.LastWriteTime -lt $cutoffDate }
            
            foreach ($file in $oldFiles) {
                try {
                    $fileSize = $file.Length
                    Remove-Item -Path $file.FullName -Force
                    $deletedCount++
                    $freedSpace += $fileSize
                    $script:Logger.Info("Deleted old backup: $($file.Name)")
                }
                catch {
                    $script:Logger.Warning("Failed to delete file: $($file.FullName) - $_")
                }
            }
        }
    }
    
    $script:Logger.Info("Cleanup completed: $deletedCount files deleted, $([math]::Round($freedSpace / 1MB, 2)) MB freed")
}

# Generate backup report
function New-BackupReport {
    param(
        [string]$ReportPath,
        [hashtable]$AppConfig
    )
    
    $script:Logger.Info("Generating application backup report...")
    
    # Get service status
    $serviceStatus = Get-ServiceStatus -ServiceNames $AppConfig.monitored_services
    
    # Get file system information
    $fileSystemInfo = @()
    foreach ($pathConfig in $AppConfig.backup_paths) {
        $fileSystemInfo += Get-FileSystemInfo -Path $pathConfig.source
    }
    
    $report = @{
        "backup_summary" = @{
            "start_time" = $script:BackupStartTime
            "end_time" = Get-Date
            "duration_minutes" = ((Get-Date) - $script:BackupStartTime).TotalMinutes
            "backup_type" = $BackupType
            "overall_success" = $true
            "services_stopped" = $script:ServicesStopped
        }
        "metrics" = $script:BackupMetrics
        "system_info" = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "disk_space" = Get-DiskSpace
            "service_status" = $serviceStatus
            "file_system_info" = $fileSystemInfo
        }
    }
    
    # Check overall success
    foreach ($metric in $script:BackupMetrics.Values) {
        if ($metric.success -eq $false) {
            $report.backup_summary.overall_success = $false
            break
        }
    }
    
    # Save report as JSON
    $reportJson = $report | ConvertTo-Json -Depth 10
    $reportFile = "$ReportPath\application_backup_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8
    
    $script:Logger.Info("Application backup report saved: $reportFile")
    
    return $report
}

# Get disk space information
function Get-DiskSpace {
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    $driveInfo = @()
    foreach ($drive in $drives) {
        $driveInfo += @{
            "drive" = $drive.DeviceID
            "total_size_gb" = [math]::Round($drive.Size / 1GB, 2)
            "free_space_gb" = [math]::Round($drive.FreeSpace / 1GB, 2)
            "used_space_gb" = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
            "free_space_percent" = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        }
    }
    
    return $driveInfo
}

# Main execution
try {
    # Initialize logging
    Initialize-BackupLogging
    
    # Load configuration
    $config = Get-BackupConfiguration -ConfigFile $ConfigFile
    
    # Initialize backup directories
    Initialize-BackupDirectories -BackupBasePath $config.backup.base_path
    
    # Stop services if requested
    if ($StopServices) {
        Stop-RequiredServices -ServiceNames $config.application.monitored_services
    }
    
    # Execute backup based on type
    $backupFile = $null
    
    try {
        switch ($BackupType) {
            "full" {
                $backupFile = Invoke-FullApplicationBackup -AppConfig $config.application -BackupPath $config.backup.base_path
            }
            "incremental" {
                $backupFile = Invoke-IncrementalApplicationBackup -AppConfig $config.application -BackupPath $config.backup.base_path
            }
            "configuration" {
                $backupFile = Invoke-ConfigurationBackup -AppConfig $config.application -BackupPath $config.backup.base_path
            }
            "logs" {
                $backupFile = Invoke-LogsBackup -AppConfig $config.application -BackupPath $config.backup.base_path
            }
        }
    }
    finally {
        # Always restart services if they were stopped
        if ($StopServices) {
            Start-RequiredServices
        }
    }
    
    # Cross-region replication if requested
    if ($CrossRegionReplicate -and $backupFile) {
        Invoke-CrossRegionReplication -BackupFile $backupFile -ReplicationConfig $config.cross_region_replication
    }
    
    # Cleanup old backups
    Remove-OldBackups -BackupPath $config.backup.base_path -RetentionDays $config.backup.retention_days
    
    # Generate backup report
    $report = New-BackupReport -ReportPath "$($config.backup.base_path)\application\metadata" -AppConfig $config.application
    
    # Send notifications
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report $report
    }
    
    $script:Logger.Info("Application backup completed successfully")
    
    # Set exit code based on overall success
    if ($report.backup_summary.overall_success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    $script:Logger.Error("Application backup script failed: $_")
    
    # Always restart services if they were stopped
    if ($StopServices) {
        Start-RequiredServices
    }
    
    # Send failure notification
    if ($config.notifications.enabled) {
        Send-BackupNotification -Config $config.notifications -Report @{
            "backup_summary" = @{
                "overall_success" = $false
                "error_message" = $_.Exception.Message
                "backup_type" = $BackupType
            }
        }
    }
    
    exit 1
}
finally {
    if ($script:Logger) {
        $script:Logger.Close()
    }
}