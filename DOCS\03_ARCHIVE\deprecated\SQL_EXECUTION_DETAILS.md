# 強制抽回後刪除 - SQL執行詳細文件

## 📋 SQL執行清單

**文件目的**: 詳細記錄抽回刪除功能中所有執行的SQL語句  
**執行環境**: PostgreSQL 資料庫  
**主要資料表**: ibmcase, ibmsts, casesubmitsts, caseopened, record  

---

## 🔍 Phase 1: 初始驗證查詢

### 1.1 協同狀態檢查
```sql
-- 檢查案件是否正在協同處理中
-- 執行位置: case_withdraw_delete.jsp 第88行
-- 目的: 防止協同處理中的案件被誤操作

SELECT case_id 
FROM public.caseopened 
WHERE CASE_ID = '${caseId}';

-- 實際執行範例:
SELECT case_id 
FROM public.caseopened 
WHERE CASE_ID = 'IM202307050001';

-- 預期結果:
-- - 如果回傳記錄: 案件正在協同處理，拒絕操作
-- - 如果無記錄: 案件可以執行抽回操作
```

### 1.2 案件狀態驗證
```sql
-- 驗證案件當前狀態 (在業務邏輯中執行，非直接SQL查詢)
-- 允許抽回的狀態碼: 232,252,342,352,362,442,452,462

-- 對應的業務邏輯驗證 (Java):
if (!WITHDRAWABLE_STATUSES.contains(currentStatus)) {
    return error("狀態碼不允許抽回");
}
```

---

## 🔄 Phase 2: 抽回操作執行

### 2.1 主要狀態表更新 (ibmsts)
```sql
-- 更新案件狀態至目標抽回狀態
-- 執行位置: case_withdraw_delete.jsp 第105行
-- 事務性: 是 (包含在 JDBCConnection 事務中)

UPDATE public.ibmsts 
SET acc_rlt = '${withdrawTargetStatus}' 
WHERE case_id = '${caseId}' AND acc_rlt = '${currentStatus}';

-- 實際執行範例 1: 認定陳核 → 認定初建
UPDATE public.ibmsts 
SET acc_rlt = '231' 
WHERE case_id = 'IM202307050001' AND acc_rlt = '232';

-- 實際執行範例 2: 排拆陳核 → 排拆辦理中  
UPDATE public.ibmsts 
SET acc_rlt = '344' 
WHERE case_id = 'IM202307050002' AND acc_rlt = '342';

-- 實際執行範例 3: 結案陳核 → 結案辦理中
UPDATE public.ibmsts 
SET acc_rlt = '441' 
WHERE case_id = 'IM202307050003' AND acc_rlt = '442';

-- 影響行數檢查:
-- - 如果 updateRows = 0: 狀態更新失敗，可能狀態已變更
-- - 如果 updateRows = 1: 狀態更新成功
```

### 2.2 提交狀態表同步更新 (casesubmitsts)
```sql
-- 同步更新提交狀態表，確保雙表機制一致性
-- 執行位置: case_withdraw_delete.jsp 第116行
-- 說明: 更新狀態並刷新時間戳記

UPDATE public.casesubmitsts 
SET acc_rlt = '${withdrawTargetStatus}', rec_time = CURRENT_TIMESTAMP 
WHERE case_id = '${caseId}';

-- 實際執行範例:
UPDATE public.casesubmitsts 
SET acc_rlt = '231', rec_time = CURRENT_TIMESTAMP 
WHERE case_id = 'IM202307050001';

-- 注意事項:
-- - 此表可能不存在對應記錄，不影響主流程
-- - rec_time 自動更新為當前時間戳記
```

### 2.3 協同記錄清理 (caseopened)
```sql
-- 清理協同處理記錄，釋放案件鎖定
-- 執行位置: case_withdraw_delete.jsp 第120行
-- 目的: 確保案件不再被標記為協同處理中

DELETE FROM public.caseopened 
WHERE case_id = '${caseId}';

-- 實際執行範例:
DELETE FROM public.caseopened 
WHERE case_id = 'IM202307050001';

-- 預期影響:
-- - 可能刪除 0-1 筆記錄
-- - 刪除後案件解除協同狀態限制
```

### 2.4 抽回操作日誌記錄 (record)
```sql
-- 記錄抽回操作至系統日誌
-- 執行位置: case_withdraw_delete.jsp 第124行 (透過 logOperation 函數)
-- 目的: 審計追蹤和問題排查

INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES ('${uuid}', '${caseId}', '案件抽回', '${description}', '已執行', '${empNo}');

-- 實際執行範例:
INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES (
    '550e8400-e29b-41d4-a716-************', 
    'IM202307050001', 
    '案件抽回', 
    '從狀態 232 抽回至 231', 
    '已執行', 
    'EMP20230705001'
);

-- 欄位說明:
-- uuid: 唯一識別碼 (UUID.randomUUID())
-- case_id: 案件編號
-- rec_type: 記錄類型 (固定值: '案件抽回')
-- org_rec: 操作描述
-- new_rec: 執行狀態 (固定值: '已執行')
-- empno: 執行人員編號
```

---

## 🗑️ Phase 3: 條件性刪除執行

### 3.1 可刪除狀態判斷
```sql
-- 業務邏輯判斷 (非SQL查詢)
-- 可直接刪除的狀態: 231, 241, 251 (初建狀態)

-- Java邏輯:
private boolean isDeletableStatus(String status) {
    String DELETABLE_STATUSES = "231,241,251";
    return DELETABLE_STATUSES.indexOf(status) > -1;
}
```

### 3.2 主案件記錄刪除 (ibmcase) - 觸發級聯刪除
```sql
-- 刪除主案件記錄 (觸發 CASCADE 刪除相關記錄)
-- 執行位置: case_withdraw_delete.jsp 第129行
-- 重要: 此操作會觸發資料庫級聯刪除相關表記錄

DELETE FROM public.ibmcase 
WHERE case_id = '${caseId}';

-- 實際執行範例:
DELETE FROM public.ibmcase 
WHERE case_id = 'IM202307050001';

-- 級聯影響範圍 (基於 Foreign Key Constraints):
-- 1. ibmsts 表中相關記錄會被自動刪除
-- 2. 其他有 FK 關聯的表記錄會被刪除
-- 3. 具體影響表需查看資料庫 CONSTRAINT 定義
```

### 3.3 狀態記錄刪除 (ibmsts) - 確保清理
```sql
-- 明確刪除狀態記錄 (補強性操作，防止級聯失效)
-- 執行位置: case_withdraw_delete.jsp 第134行

DELETE FROM public.ibmsts 
WHERE case_id = '${caseId}';

-- 實際執行範例:
DELETE FROM public.ibmsts 
WHERE case_id = 'IM202307050001';

-- 說明:
-- - 正常情況下此記錄已被級聯刪除
-- - 此操作為確保性刪除，防止部分資料殘留
```

### 3.4 提交狀態記錄刪除 (casesubmitsts)
```sql
-- 刪除提交狀態記錄
-- 執行位置: case_withdraw_delete.jsp 第137行

DELETE FROM public.casesubmitsts 
WHERE case_id = '${caseId}';

-- 實際執行範例:
DELETE FROM public.casesubmitsts 
WHERE case_id = 'IM202307050001';

-- 說明:
-- - 此表為雙表機制的一部分
-- - 確保提交狀態記錄完全清除
```

### 3.5 刪除操作日誌記錄 (record)
```sql
-- 記錄刪除操作至系統日誌
-- 執行位置: case_withdraw_delete.jsp 第141行 (透過 logOperation 函數)

INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES ('${uuid}', '${caseId}', '案件刪除', '抽回後執行刪除', '已執行', '${empNo}');

-- 實際執行範例:
INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES (
    '550e8400-e29b-41d4-a716-************', 
    'IM202307050001', 
    '案件刪除', 
    '抽回後執行刪除', 
    '已執行', 
    'EMP20230705001'
);
```

---

## 📊 完整執行順序與事務控制

### 成功案例: 認定陳核案件 (232 → 231 → 刪除)
```sql
BEGIN TRANSACTION;

-- Step 1: 檢查協同狀態
SELECT case_id FROM public.caseopened WHERE CASE_ID = 'IM202307050001';
-- 結果: 無記錄，可以繼續

-- Step 2: 更新狀態 232 → 231
UPDATE public.ibmsts SET acc_rlt = '231' WHERE case_id = 'IM202307050001' AND acc_rlt = '232';
-- 影響行數: 1

-- Step 3: 同步更新提交狀態表
UPDATE public.casesubmitsts SET acc_rlt = '231', rec_time = CURRENT_TIMESTAMP WHERE case_id = 'IM202307050001';

-- Step 4: 清理協同記錄
DELETE FROM public.caseopened WHERE case_id = 'IM202307050001';

-- Step 5: 記錄抽回操作
INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES ('uuid1', 'IM202307050001', '案件抽回', '從狀態 232 抽回至 231', '已執行', 'EMP001');

-- Step 6: 判斷 231 為可刪除狀態，執行刪除
DELETE FROM public.ibmcase WHERE case_id = 'IM202307050001';
-- 觸發級聯刪除相關記錄

-- Step 7: 確保性清理
DELETE FROM public.ibmsts WHERE case_id = 'IM202307050001';
DELETE FROM public.casesubmitsts WHERE case_id = 'IM202307050001';

-- Step 8: 記錄刪除操作
INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES ('uuid2', 'IM202307050001', '案件刪除', '抽回後執行刪除', '已執行', 'EMP001');

COMMIT;
```

### 成功案例: 排拆陳核案件 (342 → 344，僅抽回)
```sql
BEGIN TRANSACTION;

-- Step 1: 檢查協同狀態
SELECT case_id FROM public.caseopened WHERE CASE_ID = 'IM202307050002';
-- 結果: 無記錄，可以繼續

-- Step 2: 更新狀態 342 → 344
UPDATE public.ibmsts SET acc_rlt = '344' WHERE case_id = 'IM202307050002' AND acc_rlt = '342';
-- 影響行數: 1

-- Step 3: 同步更新提交狀態表
UPDATE public.casesubmitsts SET acc_rlt = '344', rec_time = CURRENT_TIMESTAMP WHERE case_id = 'IM202307050002';

-- Step 4: 清理協同記錄
DELETE FROM public.caseopened WHERE case_id = 'IM202307050002';

-- Step 5: 記錄抽回操作
INSERT INTO public.record (uuid, case_id, rec_type, org_rec, new_rec, empno) 
VALUES ('uuid3', 'IM202307050002', '案件抽回', '從狀態 342 抽回至 344', '已執行', 'EMP001');

-- Step 6: 判斷 344 非可刪除狀態，僅抽回，不刪除

COMMIT;
```

### 錯誤案例: 協同中案件被拒絕
```sql
-- Step 1: 檢查協同狀態
SELECT case_id FROM public.caseopened WHERE CASE_ID = 'IM202307050003';
-- 結果: 回傳記錄，表示正在協同處理

-- 流程終止，回傳錯誤: "案件正在協同處理中，無法執行抽回操作"
-- 不執行任何 DML 操作
```

---

## 🔒 資料庫約束與觸發器影響

### Foreign Key 約束影響
```sql
-- 查詢相關外鍵約束
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND (ccu.table_name = 'ibmcase' OR tc.table_name = 'ibmcase');
```

### 觸發器執行順序
```sql
-- 查詢 ibmcase 表相關觸發器
SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE event_object_table = 'ibmcase'
ORDER BY action_timing, event_manipulation;
```

---

## 📈 效能指標與監控

### 執行時間監控
```sql
-- 查詢最近的抽回刪除操作記錄
SELECT 
    case_id,
    rec_type,
    org_rec,
    empno,
    created_time
FROM public.record 
WHERE rec_type IN ('案件抽回', '案件刪除')
ORDER BY created_time DESC 
LIMIT 20;
```

### 資料量統計
```sql
-- 統計各狀態碼的案件數量
SELECT 
    acc_rlt,
    COUNT(*) as case_count
FROM public.ibmsts 
WHERE acc_rlt IN ('232','252','342','352','362','442','452','462')
GROUP BY acc_rlt
ORDER BY acc_rlt;
```

### 協同處理監控
```sql
-- 監控當前協同處理中的案件
SELECT 
    co.case_id,
    co.empno,
    st.acc_rlt,
    co.created_time
FROM public.caseopened co
JOIN public.ibmsts st ON co.case_id = st.case_id
ORDER BY co.created_time DESC;
```

---

## 🛠️ 故障排除 SQL

### 檢查案件完整性
```sql
-- 檢查案件是否在所有相關表中都有記錄
SELECT 
    'ibmcase' as table_name,
    COUNT(*) as record_count
FROM public.ibmcase 
WHERE case_id = 'IM202307050001'

UNION ALL

SELECT 
    'ibmsts' as table_name,
    COUNT(*) as record_count
FROM public.ibmsts 
WHERE case_id = 'IM202307050001'

UNION ALL

SELECT 
    'casesubmitsts' as table_name,
    COUNT(*) as record_count
FROM public.casesubmitsts 
WHERE case_id = 'IM202307050001'

UNION ALL

SELECT 
    'caseopened' as table_name,
    COUNT(*) as record_count
FROM public.caseopened 
WHERE case_id = 'IM202307050001';
```

### 操作歷史追蹤
```sql
-- 追蹤特定案件的所有操作歷史
SELECT 
    case_id,
    rec_type,
    org_rec,
    new_rec,
    empno,
    created_time
FROM public.record 
WHERE case_id = 'IM202307050001'
ORDER BY created_time ASC;
```

### 狀態異常檢查
```sql
-- 檢查狀態不一致的案件
SELECT 
    ic.case_id,
    ist.acc_rlt as ibmsts_status,
    css.acc_rlt as casesubmitsts_status
FROM public.ibmcase ic
LEFT JOIN public.ibmsts ist ON ic.case_id = ist.case_id
LEFT JOIN public.casesubmitsts css ON ic.case_id = css.case_id
WHERE ist.acc_rlt != css.acc_rlt OR ist.acc_rlt IS NULL OR css.acc_rlt IS NULL;
```

---

**文件版本**: 1.0  
**建立日期**: 2025-07-05  
**資料庫版本**: PostgreSQL  
**執行環境**: 新北市違章建築管理系統  
**維護人員**: Claude Code 開發團隊  