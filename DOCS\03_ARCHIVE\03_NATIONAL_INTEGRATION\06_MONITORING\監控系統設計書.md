# 違章建築管理系統監控儀表板設計書

## 一、系統概述

### 1.1 專案背景
新北市違章建築管理系統需要一個完整的監控系統，以即時掌握系統運行狀態、效能指標、資料同步狀況，並在異常發生時能夠即時告警，確保系統穩定運行。

### 1.2 監控目標
- **即時監控**：系統健康度、效能指標、資料流量
- **預警機制**：異常偵測、效能瓶頸預測、資源使用警告
- **歷史分析**：趨勢分析、問題追蹤、容量規劃
- **自動化告警**：多管道通知、分級告警、智慧化派工

### 1.3 技術架構
- **前端框架**：Bootstrap 5.3.3 + Chart.js + DataTables
- **即時通訊**：WebSocket (SignalR)
- **資料收集**：Prometheus + Grafana
- **日誌管理**：ELK Stack (Elasticsearch + Logstash + Kibana)
- **告警系統**：AlertManager + LINE Notify

## 二、關鍵效能指標（KPI）設計

### 2.1 系統層級 KPI

#### 2.1.1 可用性指標
```yaml
availability_metrics:
  - name: system_uptime
    description: 系統持續運行時間
    unit: percentage
    target: 99.9%
    calculation: (total_time - downtime) / total_time * 100
    
  - name: service_availability
    description: 各服務可用性
    services:
      - web_server: 99.95%
      - database: 99.99%
      - api_gateway: 99.9%
      - sync_service: 99.5%
```

#### 2.1.2 效能指標
```yaml
performance_metrics:
  - name: response_time
    description: API回應時間
    unit: milliseconds
    thresholds:
      excellent: < 100ms
      good: 100-500ms
      warning: 500-1000ms
      critical: > 1000ms
      
  - name: throughput
    description: 系統處理量
    unit: requests/second
    target: 1000
    
  - name: concurrent_users
    description: 同時在線使用者數
    unit: count
    capacity: 500
```

#### 2.1.3 資源使用指標
```yaml
resource_metrics:
  - name: cpu_usage
    thresholds:
      normal: < 60%
      warning: 60-80%
      critical: > 80%
      
  - name: memory_usage
    thresholds:
      normal: < 70%
      warning: 70-85%
      critical: > 85%
      
  - name: disk_usage
    thresholds:
      normal: < 75%
      warning: 75-90%
      critical: > 90%
      
  - name: database_connections
    thresholds:
      normal: < 60%
      warning: 60-80%
      critical: > 80%
```

### 2.2 業務層級 KPI

#### 2.2.1 案件處理指標
```yaml
case_metrics:
  - name: daily_new_cases
    description: 每日新增案件數
    unit: count
    baseline: 50-100
    
  - name: case_processing_time
    description: 案件平均處理時間
    unit: days
    target: < 7
    
  - name: case_backlog
    description: 待處理案件數
    unit: count
    warning: > 1000
    critical: > 2000
    
  - name: case_completion_rate
    description: 案件結案率
    unit: percentage
    target: > 95%
```

#### 2.2.2 資料同步指標
```yaml
sync_metrics:
  - name: sync_success_rate
    description: 同步成功率
    unit: percentage
    target: > 99%
    
  - name: sync_queue_depth
    description: 同步佇列深度
    unit: count
    warning: > 500
    critical: > 1000
    
  - name: sync_latency
    description: 同步延遲時間
    unit: minutes
    target: < 30
    
  - name: failed_sync_count
    description: 同步失敗次數
    unit: count/hour
    warning: > 10
    critical: > 50
```

## 三、監控儀表板 UI 設計

### 3.1 儀表板布局架構
```
┌─────────────────────────────────────────────────────────────┐
│                        頂部導航列                            │
├─────────────────────────────────────────────────────────────┤
│  系統健康度  │  即時告警  │  快速指標  │  時間範圍選擇器    │
├──────────────┴──────────────┴───────────┴───────────────────┤
│                                                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ 系統總覽    │  │ 效能指標    │  │ 業務指標    │        │
│  │ Dashboard   │  │ Metrics     │  │ Business    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                              │
│  ┌─────────────────────────────────────────────────┐        │
│  │                  主要內容區域                    │        │
│  │                                                  │        │
│  │  - 即時圖表                                      │        │
│  │  - 資料表格                                      │        │
│  │  - 告警清單                                      │        │
│  └─────────────────────────────────────────────────┘        │
└──────────────────────────────────────────────────────────────┘
```

### 3.2 關鍵元件設計

#### 3.2.1 系統健康度評分卡
```javascript
// 健康度評分計算
class HealthScoreCalculator {
    constructor() {
        this.weights = {
            availability: 0.3,
            performance: 0.25,
            resource: 0.25,
            business: 0.2
        };
    }
    
    calculate(metrics) {
        const scores = {
            availability: this.calculateAvailability(metrics.uptime),
            performance: this.calculatePerformance(metrics.responseTime),
            resource: this.calculateResource(metrics.resources),
            business: this.calculateBusiness(metrics.caseMetrics)
        };
        
        // 加權計算總分
        const totalScore = Object.keys(scores).reduce((sum, key) => {
            return sum + (scores[key] * this.weights[key]);
        }, 0);
        
        return {
            total: Math.round(totalScore),
            breakdown: scores,
            grade: this.getGrade(totalScore),
            color: this.getColor(totalScore)
        };
    }
    
    getGrade(score) {
        if (score >= 95) return 'A+';
        if (score >= 90) return 'A';
        if (score >= 85) return 'B+';
        if (score >= 80) return 'B';
        if (score >= 75) return 'C';
        return 'D';
    }
    
    getColor(score) {
        if (score >= 90) return '#28a745'; // 綠色
        if (score >= 80) return '#17a2b8'; // 藍色
        if (score >= 70) return '#ffc107'; // 黃色
        return '#dc3545'; // 紅色
    }
}
```

#### 3.2.2 即時資料流儀表板
```javascript
// WebSocket 即時數據訂閱
class RealtimeMonitor {
    constructor(dashboardId) {
        this.connection = new signalR.HubConnectionBuilder()
            .withUrl("/monitorHub")
            .withAutomaticReconnect()
            .build();
            
        this.charts = new Map();
        this.gauges = new Map();
    }
    
    async start() {
        await this.connection.start();
        
        // 訂閱即時指標
        this.connection.on("MetricUpdate", (metric) => {
            this.updateMetric(metric);
        });
        
        // 訂閱告警
        this.connection.on("AlertTriggered", (alert) => {
            this.showAlert(alert);
        });
    }
    
    updateMetric(metric) {
        // 更新圖表
        if (this.charts.has(metric.name)) {
            const chart = this.charts.get(metric.name);
            chart.data.datasets[0].data.push({
                x: metric.timestamp,
                y: metric.value
            });
            
            // 保持最近30分鐘的資料
            if (chart.data.datasets[0].data.length > 180) {
                chart.data.datasets[0].data.shift();
            }
            
            chart.update('none'); // 無動畫更新
        }
        
        // 更新儀表
        if (this.gauges.has(metric.name)) {
            const gauge = this.gauges.get(metric.name);
            gauge.set(metric.value);
        }
    }
}
```

### 3.3 視覺化圖表設計

#### 3.3.1 系統總覽圖表
```javascript
// 系統健康度環形圖
const healthDoughnutChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['正常', '警告', '異常', '離線'],
        datasets: [{
            data: [85, 10, 3, 2],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed + '%';
                    }
                }
            }
        }
    }
});

// 效能趨勢圖
const performanceTrendChart = new Chart(ctx, {
    type: 'line',
    data: {
        datasets: [{
            label: 'API 回應時間',
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1,
            data: [] // 即時更新
        }, {
            label: 'CPU 使用率',
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1,
            data: [] // 即時更新
        }]
    },
    options: {
        responsive: true,
        scales: {
            x: {
                type: 'time',
                time: {
                    unit: 'minute'
                }
            },
            y: {
                beginAtZero: true
            }
        }
    }
});
```

## 四、告警規則與通知機制

### 4.1 告警規則定義

#### 4.1.1 規則配置結構
```yaml
alert_rules:
  - name: high_cpu_usage
    condition: cpu_usage > 80
    duration: 5m
    severity: warning
    annotations:
      summary: "CPU 使用率過高"
      description: "CPU 使用率已超過 80% 持續 5 分鐘"
    actions:
      - notify_ops_team
      - scale_up_instances
      
  - name: database_connection_pool_exhausted
    condition: db_active_connections / db_max_connections > 0.9
    duration: 1m
    severity: critical
    annotations:
      summary: "資料庫連線池即將耗盡"
      description: "資料庫連線使用率已達 90%"
    actions:
      - notify_dba
      - increase_connection_limit
      
  - name: sync_queue_backlog
    condition: sync_queue_depth > 1000
    duration: 10m
    severity: warning
    annotations:
      summary: "同步佇列積壓"
      description: "待同步資料已累積超過 1000 筆"
    actions:
      - notify_dev_team
      - trigger_manual_sync
```

#### 4.1.2 告警分級機制
```javascript
class AlertSeverityManager {
    constructor() {
        this.severityLevels = {
            info: {
                priority: 1,
                color: '#17a2b8',
                icon: 'info-circle',
                sound: false,
                channels: ['dashboard']
            },
            warning: {
                priority: 2,
                color: '#ffc107',
                icon: 'exclamation-triangle',
                sound: true,
                channels: ['dashboard', 'email']
            },
            critical: {
                priority: 3,
                color: '#dc3545',
                icon: 'exclamation-circle',
                sound: true,
                channels: ['dashboard', 'email', 'line', 'phone']
            },
            emergency: {
                priority: 4,
                color: '#721c24',
                icon: 'fire',
                sound: true,
                channels: ['all']
            }
        };
    }
    
    async processAlert(alert) {
        const severity = this.severityLevels[alert.severity];
        
        // 根據嚴重程度發送通知
        for (const channel of severity.channels) {
            await this.notify(channel, alert);
        }
        
        // 記錄告警
        await this.logAlert(alert);
        
        // 觸發自動化響應
        if (alert.actions) {
            await this.executeActions(alert.actions);
        }
    }
}
```

### 4.2 通知管道整合

#### 4.2.1 LINE Notify 整合
```javascript
class LineNotifyService {
    constructor(accessToken) {
        this.accessToken = accessToken;
        this.apiUrl = 'https://notify-api.line.me/api/notify';
    }
    
    async sendAlert(alert) {
        const message = this.formatMessage(alert);
        
        const formData = new URLSearchParams();
        formData.append('message', message);
        
        // 加入圖片（如果有的話）
        if (alert.screenshot) {
            formData.append('imageFile', alert.screenshot);
        }
        
        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });
        
        return response.json();
    }
    
    formatMessage(alert) {
        const emoji = this.getEmoji(alert.severity);
        const timestamp = new Date().toLocaleString('zh-TW');
        
        return `
${emoji} 【${alert.severity.toUpperCase()}】${alert.title}

📋 詳細資訊：
${alert.description}

📊 影響範圍：${alert.impact || '評估中'}
🕐 發生時間：${timestamp}
🔗 詳細資訊：${alert.dashboardUrl}

請相關人員立即處理！
        `.trim();
    }
    
    getEmoji(severity) {
        const emojiMap = {
            info: 'ℹ️',
            warning: '⚠️',
            critical: '🚨',
            emergency: '🆘'
        };
        return emojiMap[severity] || '📢';
    }
}
```

## 五、異常偵測演算法

### 5.1 基於統計的異常偵測

#### 5.1.1 移動平均異常偵測
```javascript
class MovingAverageAnomalyDetector {
    constructor(windowSize = 20, threshold = 3) {
        this.windowSize = windowSize;
        this.threshold = threshold; // 標準差倍數
        this.dataWindow = [];
    }
    
    detect(value) {
        this.dataWindow.push(value);
        
        // 保持窗口大小
        if (this.dataWindow.length > this.windowSize) {
            this.dataWindow.shift();
        }
        
        // 需要足夠的資料才能計算
        if (this.dataWindow.length < this.windowSize) {
            return { isAnomaly: false };
        }
        
        // 計算統計值
        const stats = this.calculateStatistics();
        const deviation = Math.abs(value - stats.mean);
        const score = deviation / stats.stdDev;
        
        return {
            isAnomaly: score > this.threshold,
            score: score,
            expectedRange: {
                lower: stats.mean - (this.threshold * stats.stdDev),
                upper: stats.mean + (this.threshold * stats.stdDev)
            },
            actualValue: value,
            statistics: stats
        };
    }
    
    calculateStatistics() {
        const sum = this.dataWindow.reduce((a, b) => a + b, 0);
        const mean = sum / this.dataWindow.length;
        
        const squaredDiffs = this.dataWindow.map(x => Math.pow(x - mean, 2));
        const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / this.dataWindow.length;
        const stdDev = Math.sqrt(avgSquaredDiff);
        
        return { mean, stdDev, min: Math.min(...this.dataWindow), max: Math.max(...this.dataWindow) };
    }
}
```

#### 5.1.2 預測模型異常偵測
```javascript
class PredictiveAnomalyDetector {
    constructor() {
        this.model = new LinearRegression();
        this.trainingData = [];
        this.predictionWindow = 5; // 預測未來5個時間點
    }
    
    async train(historicalData) {
        // 準備訓練資料
        const features = [];
        const labels = [];
        
        for (let i = 0; i < historicalData.length - 1; i++) {
            features.push([
                i,                          // 時間索引
                historicalData[i].value,    // 當前值
                this.getDayOfWeek(historicalData[i].timestamp),
                this.getHourOfDay(historicalData[i].timestamp)
            ]);
            labels.push(historicalData[i + 1].value);
        }
        
        // 訓練模型
        await this.model.fit(features, labels);
    }
    
    predict(currentData) {
        const predictions = [];
        let lastValue = currentData.value;
        
        for (let i = 1; i <= this.predictionWindow; i++) {
            const features = [
                currentData.index + i,
                lastValue,
                this.getDayOfWeek(new Date(currentData.timestamp.getTime() + i * 60000)),
                this.getHourOfDay(new Date(currentData.timestamp.getTime() + i * 60000))
            ];
            
            const prediction = this.model.predict([features])[0];
            predictions.push({
                timestamp: new Date(currentData.timestamp.getTime() + i * 60000),
                predictedValue: prediction,
                confidenceInterval: this.calculateConfidenceInterval(prediction)
            });
            
            lastValue = prediction;
        }
        
        return predictions;
    }
    
    detectAnomaly(actual, predicted) {
        const difference = Math.abs(actual - predicted.predictedValue);
        const threshold = predicted.confidenceInterval.upper - predicted.predictedValue;
        
        return {
            isAnomaly: difference > threshold * 2,
            severity: this.calculateSeverity(difference, threshold),
            actual: actual,
            predicted: predicted.predictedValue,
            confidence: predicted.confidenceInterval
        };
    }
}
```

### 5.2 模式識別異常偵測

#### 5.2.1 週期性模式偵測
```javascript
class PeriodicPatternDetector {
    constructor() {
        this.patterns = new Map();
        this.anomalyThreshold = 0.3; // 30% 偏差
    }
    
    learnPattern(metricName, timeSeriesData) {
        // 使用 FFT 找出週期性
        const fftResult = this.performFFT(timeSeriesData);
        const dominantFrequencies = this.findDominantFrequencies(fftResult);
        
        // 儲存學習到的模式
        this.patterns.set(metricName, {
            frequencies: dominantFrequencies,
            baseline: this.extractBaseline(timeSeriesData, dominantFrequencies),
            lastUpdate: new Date()
        });
    }
    
    detectAnomaly(metricName, currentValue, timestamp) {
        const pattern = this.patterns.get(metricName);
        if (!pattern) {
            return { isAnomaly: false, reason: 'No pattern learned' };
        }
        
        // 根據時間計算預期值
        const expectedValue = this.calculateExpectedValue(pattern, timestamp);
        const deviation = Math.abs(currentValue - expectedValue) / expectedValue;
        
        return {
            isAnomaly: deviation > this.anomalyThreshold,
            deviation: deviation,
            expectedValue: expectedValue,
            actualValue: currentValue,
            pattern: pattern
        };
    }
    
    calculateExpectedValue(pattern, timestamp) {
        let value = pattern.baseline.mean;
        
        // 加入週期性成分
        pattern.frequencies.forEach(freq => {
            const phase = (timestamp.getTime() / 1000) * freq.frequency * 2 * Math.PI;
            value += freq.amplitude * Math.sin(phase + freq.phase);
        });
        
        return value;
    }
}
```

## 六、歷史資料分析

### 6.1 趨勢分析引擎

#### 6.1.1 長期趨勢分析
```javascript
class TrendAnalyzer {
    constructor() {
        this.trendTypes = ['linear', 'exponential', 'logarithmic', 'polynomial'];
    }
    
    analyzeTrend(timeSeriesData, options = {}) {
        const results = {};
        
        // 嘗試不同的趨勢模型
        this.trendTypes.forEach(type => {
            const model = this.fitModel(type, timeSeriesData);
            results[type] = {
                model: model,
                r2: this.calculateR2(model, timeSeriesData),
                forecast: this.forecast(model, options.forecastPeriods || 30)
            };
        });
        
        // 選擇最佳模型
        const bestModel = this.selectBestModel(results);
        
        return {
            bestFit: bestModel,
            allModels: results,
            insights: this.generateInsights(bestModel, timeSeriesData)
        };
    }
    
    generateInsights(model, data) {
        const insights = [];
        
        // 成長率分析
        const growthRate = this.calculateGrowthRate(model);
        if (Math.abs(growthRate) > 0.1) {
            insights.push({
                type: growthRate > 0 ? 'growth' : 'decline',
                severity: Math.abs(growthRate) > 0.3 ? 'high' : 'moderate',
                message: `系統負載呈現${growthRate > 0 ? '上升' : '下降'}趨勢，月成長率為 ${(growthRate * 100).toFixed(1)}%`
            });
        }
        
        // 容量預測
        const capacityForecast = this.predictCapacityLimit(model, 0.8); // 80% 閾值
        if (capacityForecast.willExceed) {
            insights.push({
                type: 'capacity_warning',
                severity: 'high',
                message: `依目前趨勢，系統將在 ${capacityForecast.daysUntil} 天後達到容量上限`,
                recommendation: '建議提前進行擴容規劃'
            });
        }
        
        // 季節性模式
        const seasonality = this.detectSeasonality(data);
        if (seasonality.isPresent) {
            insights.push({
                type: 'seasonality',
                severity: 'info',
                message: `發現 ${seasonality.period} 天的週期性模式，峰值通常出現在 ${seasonality.peakTime}`,
                recommendation: '可根據此模式優化資源調度'
            });
        }
        
        return insights;
    }
}
```

### 6.2 容量規劃模型

#### 6.2.1 資源容量預測
```javascript
class CapacityPlanner {
    constructor() {
        this.resourceLimits = {
            cpu: { max: 100, unit: '%' },
            memory: { max: 64, unit: 'GB' },
            storage: { max: 1000, unit: 'GB' },
            connections: { max: 1000, unit: 'count' }
        };
    }
    
    planCapacity(historicalData, planningHorizon = 90) {
        const plans = {};
        
        Object.keys(this.resourceLimits).forEach(resource => {
            const resourceData = historicalData[resource];
            if (!resourceData) return;
            
            // 分析使用趨勢
            const trend = new TrendAnalyzer().analyzeTrend(resourceData);
            
            // 計算容量需求
            const capacityNeeds = this.calculateCapacityNeeds(
                trend.bestFit,
                this.resourceLimits[resource],
                planningHorizon
            );
            
            plans[resource] = {
                current: {
                    usage: resourceData[resourceData.length - 1].value,
                    utilization: (resourceData[resourceData.length - 1].value / this.resourceLimits[resource].max) * 100
                },
                forecast: capacityNeeds,
                recommendations: this.generateRecommendations(resource, capacityNeeds)
            };
        });
        
        return {
            plans: plans,
            summary: this.generateSummary(plans),
            costEstimate: this.estimateCost(plans)
        };
    }
    
    generateRecommendations(resource, needs) {
        const recommendations = [];
        
        if (needs.willExceedIn30Days) {
            recommendations.push({
                priority: 'high',
                action: `立即增加 ${resource} 容量`,
                reason: '30天內將達到容量上限',
                suggestedIncrease: needs.requiredCapacity * 0.3 // 增加30%緩衝
            });
        } else if (needs.willExceedIn60Days) {
            recommendations.push({
                priority: 'medium',
                action: `規劃 ${resource} 擴容`,
                reason: '60天內將達到容量上限',
                timeline: '建議在30天內完成'
            });
        }
        
        // 優化建議
        if (needs.utilizationPattern === 'spiky') {
            recommendations.push({
                priority: 'low',
                action: '考慮使用自動擴縮容',
                reason: '使用率呈現尖峰模式',
                potentialSaving: '預估可節省 20-30% 成本'
            });
        }
        
        return recommendations;
    }
}
```

## 七、監控系統整合架構

### 7.1 資料收集層

#### 7.1.1 Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

rule_files:
  - "rules/*.yml"

scrape_configs:
  - job_name: 'tomcat'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']
      
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
      
  - job_name: 'custom_app'
    static_configs:
      - targets: ['localhost:9091']
```

#### 7.1.2 自訂指標輸出
```java
// Java Micrometer 整合
@RestController
@RequestMapping("/metrics")
public class MetricsController {
    
    private final MeterRegistry registry;
    
    @Autowired
    public MetricsController(MeterRegistry registry) {
        this.registry = registry;
        
        // 註冊自訂指標
        Gauge.builder("case.backlog", this, MetricsController::getCaseBacklog)
            .description("待處理案件數")
            .register(registry);
            
        Gauge.builder("sync.queue.size", this, MetricsController::getSyncQueueSize)
            .description("同步佇列大小")
            .register(registry);
    }
    
    private double getCaseBacklog() {
        // 查詢待處理案件數
        return caseRepository.countByStatus("PENDING");
    }
    
    private double getSyncQueueSize() {
        // 查詢同步佇列大小
        return syncQueueRepository.countByStatus("WAITING");
    }
    
    @GetMapping(produces = "text/plain")
    public String metrics() {
        return registry.scrape();
    }
}
```

### 7.2 日誌聚合層

#### 7.2.1 Logstash 配置
```ruby
# logstash.conf
input {
  file {
    path => "/var/log/tomcat9/*.log"
    start_position => "beginning"
    codec => multiline {
      pattern => "^\d{4}-\d{2}-\d{2}"
      negate => true
      what => "previous"
    }
  }
  
  jdbc {
    jdbc_driver_library => "/path/to/postgresql-42.2.5.jar"
    jdbc_driver_class => "org.postgresql.Driver"
    jdbc_connection_string => "************************************"
    jdbc_user => "postgres"
    jdbc_password => "${DB_PASSWORD}"
    schedule => "* * * * *"
    statement => "SELECT * FROM audit_log WHERE created_at > :sql_last_value"
    use_column_value => true
    tracking_column => "created_at"
  }
}

filter {
  if [path] =~ "access" {
    grok {
      match => { 
        "message" => "%{COMMONAPACHELOG} %{QS:referrer} %{QS:agent} %{NUMBER:response_time}" 
      }
    }
    
    mutate {
      convert => { 
        "response_time" => "float" 
        "bytes" => "integer"
      }
    }
  }
  
  if [type] == "audit_log" {
    json {
      source => "old_data"
      target => "old_data_parsed"
    }
    json {
      source => "new_data"
      target => "new_data_parsed"
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "monitor-%{+YYYY.MM.dd}"
  }
}
```

## 八、安全性與權限管理

### 8.1 監控系統存取控制

#### 8.1.1 角色權限定義
```javascript
const monitoringRoles = {
    viewer: {
        name: '監控檢視者',
        permissions: [
            'dashboard.view',
            'metrics.read',
            'alerts.view'
        ]
    },
    operator: {
        name: '系統操作員',
        permissions: [
            ...monitoringRoles.viewer.permissions,
            'alerts.acknowledge',
            'alerts.silence',
            'reports.generate'
        ]
    },
    admin: {
        name: '監控管理員',
        permissions: [
            ...monitoringRoles.operator.permissions,
            'alerts.configure',
            'dashboard.edit',
            'users.manage',
            'settings.modify'
        ]
    }
};
```

### 8.2 稽核追蹤

#### 8.2.1 監控操作記錄
```javascript
class MonitoringAuditLogger {
    logAccess(user, resource, action) {
        const auditEntry = {
            timestamp: new Date(),
            user: {
                id: user.id,
                name: user.name,
                role: user.role,
                ip: user.ipAddress
            },
            resource: resource,
            action: action,
            result: 'success',
            sessionId: user.sessionId
        };
        
        // 寫入稽核日誌
        this.writeToAuditLog(auditEntry);
        
        // 敏感操作即時告警
        if (this.isSensitiveAction(action)) {
            this.notifySecurityTeam(auditEntry);
        }
    }
    
    isSensitiveAction(action) {
        const sensitiveActions = [
            'alerts.delete',
            'users.create',
            'users.delete',
            'settings.security.modify'
        ];
        return sensitiveActions.includes(action);
    }
}
```

## 九、效能優化策略

### 9.1 資料儲存優化

#### 9.1.1 時序資料壓縮
```sql
-- 使用 TimescaleDB 優化時序資料
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- 轉換為 hypertable
SELECT create_hypertable('metrics', 'timestamp');

-- 設定資料保留策略
SELECT add_retention_policy('metrics', INTERVAL '30 days');

-- 建立連續聚合視圖
CREATE MATERIALIZED VIEW metrics_hourly
WITH (timescaledb.continuous) AS
SELECT
    time_bucket('1 hour', timestamp) AS hour,
    metric_name,
    avg(value) as avg_value,
    max(value) as max_value,
    min(value) as min_value,
    count(*) as sample_count
FROM metrics
GROUP BY hour, metric_name;

-- 自動更新策略
SELECT add_continuous_aggregate_policy('metrics_hourly',
    start_offset => INTERVAL '3 hours',
    end_offset => INTERVAL '1 hour',
    schedule_interval => INTERVAL '1 hour');
```

### 9.2 查詢效能優化

#### 9.2.1 快取策略
```javascript
class MetricsCacheManager {
    constructor() {
        this.cache = new Map();
        this.cacheConfig = {
            realtime: { ttl: 10 },      // 10秒
            recent: { ttl: 300 },       // 5分鐘
            historical: { ttl: 3600 }   // 1小時
        };
    }
    
    async getMetrics(query) {
        const cacheKey = this.generateCacheKey(query);
        const cached = this.cache.get(cacheKey);
        
        if (cached && !this.isExpired(cached)) {
            return cached.data;
        }
        
        // 從資料庫查詢
        const data = await this.fetchFromDatabase(query);
        
        // 根據查詢類型設定快取
        const ttl = this.determineTTL(query);
        this.cache.set(cacheKey, {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        });
        
        return data;
    }
    
    determineTTL(query) {
        const now = Date.now();
        const queryAge = now - query.endTime;
        
        if (queryAge < 60000) { // 1分鐘內
            return this.cacheConfig.realtime.ttl;
        } else if (queryAge < 3600000) { // 1小時內
            return this.cacheConfig.recent.ttl;
        } else {
            return this.cacheConfig.historical.ttl;
        }
    }
}
```

## 十、災難復原計畫

### 10.1 監控系統備援

#### 10.1.1 高可用架構
```yaml
monitoring_ha:
  prometheus:
    instances: 2
    replication: federated
    storage: shared_nfs
    
  grafana:
    instances: 2
    load_balancer: nginx
    session_storage: redis
    
  alertmanager:
    instances: 3
    clustering: gossip_protocol
    
  elasticsearch:
    nodes: 3
    minimum_master_nodes: 2
    replicas: 1
```

### 10.2 資料備份策略

#### 10.2.1 自動備份排程
```javascript
class MonitoringBackupService {
    constructor() {
        this.backupSchedule = {
            prometheus: { frequency: 'daily', retention: 7 },
            grafana: { frequency: 'daily', retention: 30 },
            elasticsearch: { frequency: 'hourly', retention: 3 },
            configurations: { frequency: 'on_change', retention: 'unlimited' }
        };
    }
    
    async performBackup(component) {
        const config = this.backupSchedule[component];
        const backupPath = `/backup/${component}/${Date.now()}`;
        
        try {
            // 執行備份
            await this.executeBackup(component, backupPath);
            
            // 驗證備份完整性
            const isValid = await this.verifyBackup(backupPath);
            
            if (isValid) {
                // 清理舊備份
                await this.cleanOldBackups(component, config.retention);
                
                // 更新備份目錄
                await this.updateBackupCatalog(component, backupPath);
            }
            
            return { success: true, path: backupPath };
        } catch (error) {
            // 備份失敗告警
            await this.alertBackupFailure(component, error);
            throw error;
        }
    }
}
```

## 十一、總結與實施建議

### 11.1 實施階段規劃

#### 階段一：基礎建設（1-2週）
- 部署 Prometheus + Grafana
- 設定基本系統指標收集
- 建立簡單儀表板

#### 階段二：業務整合（2-3週）
- 整合應用程式指標
- 開發自訂指標收集器
- 設計業務儀表板

#### 階段三：智慧化監控（3-4週）
- 實施異常偵測演算法
- 設定自動化告警規則
- 整合通知管道

#### 階段四：優化與擴展（持續）
- 效能調優
- 容量規劃
- 災難復原演練

### 11.2 關鍵成功要素

1. **循序漸進**：從基礎監控開始，逐步增加複雜功能
2. **持續優化**：根據使用經驗調整告警閾值和規則
3. **團隊培訓**：確保相關人員熟悉監控系統操作
4. **定期演練**：進行故障模擬和復原演練
5. **文件完備**：維護詳細的操作手冊和故障排除指南