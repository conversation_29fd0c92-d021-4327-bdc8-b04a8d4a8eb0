# IM10101 報表功能增強說明

## 概述
針對 `IM10101.java` 類別新增了「只產生第二聯」的參數控制功能，讓使用者可以選擇性地只產生第二聯報表，而不產生第一聯和第三聯。

## 修改內容

### 1. 新增方法重載
```java
// 原有方法保持不變，增加預設參數的重載方法
private void addSurveySheet(String case_id, String prtType) {
    addSurveySheet(case_id, prtType, false);
}

// 新增帶有 onlySecondCopy 參數的方法
private void addSurveySheet(String case_id, String prtType, boolean onlySecondCopy)
```

### 2. 新增公共方法
```java
/**
 * 產生報表（只產生第二聯）
 * @param mParameters 報表參數
 */
public synchronized void produceSecondCopyOnly(HashMap<String, Object> mParameters) {
    // 設定只產生第二聯的參數
    mParameters.put("onlySecondCopy", "Y");
    produceReport(mParameters);
}
```

### 3. 修改主要邏輯
在 `addSurveySheet` 方法中增加條件判斷：
- 當 `onlySecondCopy = true` 時，跳過第一聯和主通知書的產生
- 仍然產生第二聯報表
- 不產生第三聯（僅適用於 A 類和 B 類流程）

### 4. 參數支援
在 `produceReport` 方法中新增參數處理：
```java
// 新增參數：只產生第二聯
String onlySecondCopyParam = (String) this.parameters.get("onlySecondCopy");
boolean onlySecondCopy = "Y".equals(onlySecondCopyParam) || "true".equalsIgnoreCase(onlySecondCopyParam);
```

## 使用方式

### 方法一：使用專用方法（推薦）
```java
IM10101 report = new IM10101();
HashMap<String, Object> parameters = new HashMap<>();
// 設定基本參數...
report.produceSecondCopyOnly(parameters);
```

### 方法二：使用參數控制
```java
IM10101 report = new IM10101();
HashMap<String, Object> parameters = new HashMap<>();
parameters.put("onlySecondCopy", "Y");  // 或 "true"
// 設定其他參數...
report.produceReport(parameters);
```

## 各流程類型的第二聯說明

### A 類流程（違章建築認定）
- **第二聯名稱**: 第二聯（移拆除科）
- **報表模板**: 
  - 案件編號 >= 114: `jasperReport_14`
  - 案件編號 < 114: `jasperReport_5`

### B 類流程（廣告物）
- **第二聯名稱**: 第二聯（移拆除科）
- **報表模板**: 
  - 案件編號 >= 114: `jasperReport_15`
  - 案件編號 < 114: `jasperReport_13`

### C 類流程（違建認定通知書）
- **第二聯名稱**: 第二聯（勞安科）
- **報表模板**: `jasperReport_2`

## 參數說明

| 參數名稱 | 類型 | 說明 | 預設值 |
|---------|------|------|--------|
| `onlySecondCopy` | String | 控制是否只產生第二聯<br/>可接受值: "Y", "true" (不區分大小寫) | 未設定 (false) |
| `conditionList` | String[] | 案件編號陣列 | 必填 |
| `dataNeedType` | String | 報表類型<br/>- "Notice": 通知書<br/>- "SurveySheet": 勘查表<br/>- "ALL": 全部 | 必填 |
| `outExt` | String | 輸出格式 (通常為 "PDF") | 必填 |
| `outFileName` | String | 輸出檔案名稱 | 必填 |

## 完整使用範例

```java
public void generateSecondCopyExample() {
    IM10101 report = new IM10101();
    
    // 設定基本路徑
    report.setAppPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\");
    report.setReportPath("D:\\apache-tomcat-9.0.98\\webapps\\src\\WEB-INF\\reports\\");
    
    // 準備參數
    HashMap<String, Object> parameters = new HashMap<>();
    String[] conditionList = {"123001234"};
    parameters.put("conditionList", conditionList);
    parameters.put("dataNeedType", "Notice");
    parameters.put("outExt", "PDF");
    parameters.put("outFileName", "SecondCopy_Only.pdf");
    parameters.put("ZIP_TAG", "");
    
    try {
        // 只產生第二聯
        report.produceSecondCopyOnly(parameters);
        System.out.println("第二聯報表產生完成！");
    } catch (Exception e) {
        System.err.println("產生報表時發生錯誤：" + e.getMessage());
    }
}
```

## 注意事項

1. **向後相容性**: 原有的 `produceReport` 方法功能完全保持不變，不會影響現有程式碼
2. **參數驗證**: `onlySecondCopy` 參數支援多種格式 ("Y", "true", 不區分大小寫)
3. **檔案輸出**: 第二聯報表仍會輸出到相同的路徑和檔案格式
4. **錯誤處理**: 保持原有的錯誤處理機制

## 測試建議

1. 測試不同流程類型 (A, B, C) 的第二聯產生
2. 測試案件編號 >= 114 和 < 114 的模板選擇
3. 測試批量處理多個案件
4. 驗證原有功能不受影響

## 檔案位置

- **主要類別**: `D:\apache-tomcat-9.0.98\webapps\src\WEB-INF\java\com\ezek\report\IM10101.java`
- **使用範例**: `D:\apache-tomcat-9.0.98\webapps\src\WEB-INF\java\com\ezek\report\IM10101Usage.java`
