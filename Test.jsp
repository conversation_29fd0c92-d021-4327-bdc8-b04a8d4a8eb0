﻿<%@page language="java" contentType="text/html; charset=UTF-8"
        pageEncoding="UTF-8" trimDirectiveWhitespaces="true"%>

<%@page import="com.codecharge.*,com.codecharge.util.*,com.codecharge.db.*"%>
<%@page import="java.io.*,java.net.*,java.nio.file.*"%>
<%@page import="org.apache.commons.io.FileUtils"%>
<%@page import="org.json.simple.JSONObject"%>
<%@page import="java.sql.*"%>
<%@page import="java.awt.*,java.awt.image.BufferedImage,javax.imageio.ImageIO"%>
<%@page import="javax.net.ssl.*,java.security.cert.X509Certificate"%>
<%@page import="java.net.URL,java.net.HttpURLConnection"%>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArcGIS 服務連線測試</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .test-result {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>


<%!
/* ---------- SSL 連線測試方法 ---------- */
private JSONObject testSSLConnection(String urlStr, boolean useSecure) {
    JSONObject result = new JSONObject();
    HttpURLConnection conn = null;
    
    try {
        // 如果要測試不安全模式，才啟用 trustAllHosts
        if (!useSecure) {
            // 不安全模式：信任所有憑證
            TrustManager[] trustAll = {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] c,String a){}
                    public void checkServerTrusted(X509Certificate[] c,String a){}
                }
            };
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAll, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String h, SSLSession s) { return true; }
            });
        }
        // 如果 useSecure=true，則使用系統預設的憑證驗證
        
        URL url = new URL(urlStr);
        conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("HEAD");
        conn.setConnectTimeout(8000);
        conn.setReadTimeout(8000);
        
        int responseCode = conn.getResponseCode();
        String contentType = conn.getContentType();
        
        result.put("success", true);
        result.put("responseCode", responseCode);
        result.put("contentType", contentType);
        result.put("secureMode", useSecure);
        result.put("url", urlStr);
        
    } catch (Exception ex) {
        result.put("success", false);
        result.put("error", ex.getMessage());
        result.put("secureMode", useSecure);
        result.put("url", urlStr);
    } finally {
        if (conn != null) conn.disconnect();
    }
    
    return result;
}
%>

<%
/* ================================================================
   ArcGIS 服務連線測試主程式
   ================================================================ */
try {
    // 測試參數
    String testType = request.getParameter("test");
    String testUrl = request.getParameter("url");
    String mode = request.getParameter("mode");
    
    JSONObject testResults = new JSONObject();
    
    if ("arcgis".equals(testType)) {
        // ArcGIS 服務測試
        out.println("<div class='container mt-4'>");
        out.println("<h2 class='text-primary mb-4'>ArcGIS 服務連線測試</h2>");
        
        // 測試關鍵服務 URL
        String[] arcgisUrls = {
            "https://gis1.ntpc.gov.tw/gis/rest/services/",
            "https://icdc.ntpc.gov.tw/arcgis/rest/services/",
            "https://icdc.ntpc.gov.tw/arcgis/rest/services/Utilities/Geometry/GeometryServer",
            "https://gis1.ntpc.gov.tw/gis/rest/services/Satellite/MapServer",
            "https://gis1.ntpc.gov.tw/gis/rest/services/Land/MapServer",
            "https://gis1.ntpc.gov.tw/gis/rest/services/map/MapServer",
            "https://gis1.ntpc.gov.tw/gis/rest/services/map_2/MapServer"
        };
        
        JSONObject arcgisResults = new JSONObject();
        
        for (String url : arcgisUrls) {
            out.println("<div class='card mb-3'>");
            out.println("<div class='card-header'>");
            out.println("<h5 class='card-title mb-0'>測試: " + url + "</h5>");
            out.println("</div>");
            out.println("<div class='card-body'>");
            
            // 測試不帶 token
            JSONObject result1 = testSSLConnection(url, true);
            out.println("<div class='row mb-2'>");
            out.println("<div class='col-md-2'><strong>不帶token:</strong></div>");
            out.println("<div class='col-md-10'><code>" + result1.toJSONString() + "</code></div>");
            out.println("</div>");
            
            // 測試帶 token
            String tokenUrl = url + (url.contains("?") ? "&" : "?") + "token=17GIlTDS2ye2sOYY7M1JMSebKHbi80n2uXJW7po5heXTL8gna5MaFnDthw92G3jM";
            JSONObject result2 = testSSLConnection(tokenUrl, true);
            out.println("<div class='row'>");
            out.println("<div class='col-md-2'><strong>帶token:</strong></div>");
            out.println("<div class='col-md-10'><code>" + result2.toJSONString() + "</code></div>");
            out.println("</div>");
            
            out.println("</div>");
            out.println("</div>");
            
            JSONObject urlResult = new JSONObject();
            urlResult.put("withoutToken", result1);
            urlResult.put("withToken", result2);
            arcgisResults.put(url, urlResult);
        }
        
        out.println("</div>");
        
        testResults.put("arcgis", arcgisResults);
        
    } else if ("proxy".equals(testType)) {
        // 代理服務測試
        out.println("<div class='container mt-4'>");
        out.println("<h2 class='text-success mb-4'>代理服務測試</h2>");
        
        // 測試代理 JSP 是否存在
        String proxyPath = application.getRealPath("/remoteArcGISProxy/proxy.jsp");
        JSONObject proxyResult = new JSONObject();
        
        out.println("<div class='card'>");
        out.println("<div class='card-header'>");
        out.println("<h5 class='card-title mb-0'>代理服務檢查</h5>");
        out.println("</div>");
        out.println("<div class='card-body'>");
        
        if (proxyPath != null) {
            java.io.File proxyFile = new java.io.File(proxyPath);
            proxyResult.put("proxyExists", proxyFile.exists());
            proxyResult.put("proxyPath", proxyPath);
            
            out.println("<div class='row mb-2'>");
            out.println("<div class='col-md-3'><strong>代理文件路徑:</strong></div>");
            out.println("<div class='col-md-9'><code>" + proxyPath + "</code></div>");
            out.println("</div>");
            
            out.println("<div class='row'>");
            out.println("<div class='col-md-3'><strong>代理文件存在:</strong></div>");
            out.println("<div class='col-md-9'>");
            if (proxyFile.exists()) {
                out.println("<span class='badge bg-success'>是</span>");
            } else {
                out.println("<span class='badge bg-danger'>否</span>");
            }
            out.println("</div>");
            out.println("</div>");
        } else {
            proxyResult.put("proxyExists", false);
            proxyResult.put("error", "無法取得代理文件路徑");
            out.println("<div class='alert alert-danger'>無法取得代理文件路徑</div>");
        }
        
        out.println("</div>");
        out.println("</div>");
        out.println("</div>");
        
        testResults.put("proxy", proxyResult);
        
    } else {
        // 原始 SSL 連線測試
        if (testUrl == null) {
            testUrl = "https://icdc.ntpc.gov.tw";  // 預設測試網址
        }
        
        out.println("<div class='container mt-4'>");
        out.println("<h2 class='text-info mb-4'>SSL 連線測試</h2>");
        out.println("<div class='alert alert-info'>測試 URL: <strong>" + testUrl + "</strong></div>");
        
        // 測試1: 安全模式（使用系統憑證驗證）
        if (mode == null || "secure".equals(mode)) {
            out.println("<div class='card mb-3'>");
            out.println("<div class='card-header bg-success text-white'>");
            out.println("<h5 class='card-title mb-0'>安全模式測試</h5>");
            out.println("</div>");
            out.println("<div class='card-body'>");
            
            JSONObject secureResult = testSSLConnection(testUrl, true);
            out.println("<div class='row'>");
            out.println("<div class='col-md-2'><strong>結果:</strong></div>");
            out.println("<div class='col-md-10'><code>" + secureResult.toJSONString() + "</code></div>");
            out.println("</div>");
            out.println("</div>");
            out.println("</div>");
            
            testResults.put("secure", secureResult);
        }
        
        // 測試2: 不安全模式（信任所有憑證）
        if (mode == null || "insecure".equals(mode)) {
            out.println("<div class='card mb-3'>");
            out.println("<div class='card-header bg-warning text-dark'>");
            out.println("<h5 class='card-title mb-0'>不安全模式測試</h5>");
            out.println("</div>");
            out.println("<div class='card-body'>");
            
            JSONObject insecureResult = testSSLConnection(testUrl, false);
            out.println("<div class='row'>");
            out.println("<div class='col-md-2'><strong>結果:</strong></div>");
            out.println("<div class='col-md-10'><code>" + insecureResult.toJSONString() + "</code></div>");
            out.println("</div>");
            out.println("</div>");
            out.println("</div>");
            
            testResults.put("insecure", insecureResult);
        }
        
        out.println("</div>");
    }
    
    // 只輸出 JSON 格式（用於 API 呼叫）
    if ("json".equals(request.getParameter("format"))) {
        out.clear();
        response.setContentType("application/json; charset=UTF-8");
        out.println(testResults.toJSONString());
        return;
    }
    
} catch (Exception ex) {
    out.println("<div class='alert alert-danger'>");
    out.println("<h4><i class='fas fa-exclamation-triangle'></i> 錯誤</h4>");
    out.println("<p>" + ex.getMessage() + "</p>");
    out.println("</div>");
}
%>

<!-- 測試說明 -->
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <hr class="mb-4">
            <h3 class="text-primary mb-4">
                <i class="fas fa-info-circle"></i> ArcGIS 服務連線測試說明
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-play-circle"></i> 使用方式
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <code class="text-primary">Test.jsp</code> - 原始 SSL 連線測試
                                </li>
                                <li class="mb-2">
                                    <code class="text-primary">Test.jsp?test=arcgis</code> - 測試 ArcGIS 服務連線
                                </li>
                                <li class="mb-2">
                                    <code class="text-primary">Test.jsp?test=proxy</code> - 測試代理服務配置
                                </li>
                                <li class="mb-2">
                                    <code class="text-primary">Test.jsp?test=arcgis&format=json</code> - 輸出純 JSON 格式
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-check-circle"></i> 測試項目
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-server text-success"></i> 新北市 GIS 服務 (gis1.ntpc.gov.tw)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-server text-success"></i> ICDC ArcGIS 服務 (icdc.ntpc.gov.tw)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-shapes text-success"></i> 幾何服務 (GeometryServer)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-map text-success"></i> 地圖服務 (MapServer)
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-key text-success"></i> Token 驗證測試
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-thumbs-up"></i> 預期結果
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge bg-success">HTTP 200</span> 服務可用
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-primary">Token 有效</span> 帶 token 的請求成功
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-info">代理配置</span> proxy.jsp 文件存在
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle"></i> 常見錯誤排除
                            </h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <span class="badge bg-danger">Server Access Error</span> 服務不可用或 Token 過期
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-danger">HTTP 403</span> Token 無效或權限不足
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-danger">HTTP 404</span> 服務端點不存在
                                </li>
                                <li class="mb-2">
                                    <span class="badge bg-danger">Timeout</span> 網路問題或防火牆阻擋
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
