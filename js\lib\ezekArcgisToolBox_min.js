//NOTE:------------------------------------------------------------------------
//NOTE:  來源： 易展數位科技 , 2018/02/08 , By Martin.
//NOTE:  名稱： ArcgisToolBox.js
//NOTE:  敘述： 使用Arcgis時，可透過此JS來快速新增簡易工具列.
//NOTE:  使用方法： (1)在ArcgisMap Onload時呼叫ezek_setToolBoxParameter("toolBoxSort",<int Array>) , 第二個參數的數字陣列代表想出現哪幾種toolbox的子功能;
//NOTE:           (2)在ArcgisMap Onload時呼叫初始化函數initEzekArcgisToolBox() , 且傳入地圖元素的div
//NOTE:	 範例： var ezekMap;  //此變數為ArcgisMap
//NOTE:	       var mapDivId; //此變數為Map元素的id值
//NOTE:	       var toolBoxTypeArray = [0,1,2,3,4,5,6]; //此變數為設定想在toolbox出現的種類
//NOTE:	       ezekMap.on("load", ezek_setToolBoxParameter("toolBoxSort",toolBoxTypeArray));  //使用方法1 
//NOTE:	       ezekMap.on("load", initEzekArcgisToolBox(mapDivId)); //使用方法2
//NOTE:	 Callback函數表：
//NOTE:		   (1) ezek_setToolBoxParameter("didFinishPolyLineCirculate",function(length){ //此為測量距離後會呼叫的函數，並傳入距離測量結果的參數
//NOTE:				   console.log(length);  //印出距離測量結果 , ex: 2018.308公里
//NOTE:		 	   });
//NOTE:	 	   (2) ezek_setToolBoxParameter("didFinishPolyGonCirculate",function(area){ //此為測量面積後會呼叫的函數，並傳入面積測量結果的參數
//NOTE:				   console.log(area);	//印出面積測量結果 , ex: 2018.308平方公里
//NOTE:		 	   });
//NOTE:	 函數表：
//NOTE:  	(1) ezek_setToolBoxParameter  設定基本工具的順序
//NOTE:  	(2) ezek_getIsToolUsing  	   取得目前是否正在使用測量工具
//NOTE:  	(3) initEzekArcgisToolBox     初始化工具列
//NOTE:  	(4) initCirculateTool         初始化所有基本工具
//NOTE:  	(5) changeToolType            切換ToolBox的種類
//NOTE:  	(6) clearToolType             取消所有的toolBox
//NOTE:  	(7) initToolBoxClickEvent     設定ToolBox的HighLight模式
//NOTE:  	(8) openOrCloseToolBox        切換即顯示toolBox的開關
//NOTE:  	(9) changeSrcFromImg          設定img物件圖片切換
//NOTE:  工具列種類(toolBoxTypeArray)：
//NOTE:  	(0) 開關工具列
//NOTE:  	(1) 縮放至全圖
//NOTE:  	(2) 顯示前一圖面
//NOTE:  	(3) 顯示後一圖面
//NOTE:  	(4) 測量距離
//NOTE:  	(5) 測量面積
//NOTE:  	(6) 清除圖面標記
//NOTE:  	(7) 圖層控制 (都市計畫書圖查詢系統使用)
//NOTE:------------------------------------------------------------------------


//NOTE:------------------------------------------------------------------------
//NOTE: 全域變數
//NOTE:------------------------------------------------------------------------
var tb = null; //Toolbox物件
var initMarkerOnClick = null;  //map觸發點擊的物件，測量時新增結果文字時會用到
var outputText = "";  //測量後的結果文字


const toolBoxParameter = {  
	isToolUsing: true, //測量工具是否正在使用
	toolBoxSort: [], //基本工具的順序
	didFinishPolyLineCirculate: null, //此變數的值為fuction , 測量 距離 後會觸發的callback
	didFinishPolyGonCirculate: null   //此變數的值為fuction , 測量 面積 後會觸發的callback
};
const layoutXY = {  
	searchDiv_left: "0px", 
	slider_left: "0px", 
	toolBox_left: "0px", 
	toolBox_view_left: "0px"   
};

//NOTE:------------------------------------------------------------------------
//NOTE: 設定基本工具的順序
//NOTE:------------------------------------------------------------------------
function ezek_setToolBoxParameter(key,value){
	toolBoxParameter[key] = value;
}

//NOTE:------------------------------------------------------------------------
//NOTE: 取得目前是否正在使用測量工具
//NOTE:------------------------------------------------------------------------
function ezek_getIsToolUsing(){
	return toolBoxParameter.isToolUsing;
}

//NOTE:------------------------------------------------------------------------
//NOTE: 設定是否正在使用測量工具
//NOTE:------------------------------------------------------------------------
function ezek_setIsToolUsing(isusing){
	toolBoxParameter.isToolUsing = isusing;
}
						 
//NOTE:------------------------------------------------------------------------
//NOTE: 初始化工具列
//NOTE:------------------------------------------------------------------------
function initEzekArcgisToolBox(mapDivId) {
	//NOTE: 所有基本工具
	//******  這理想寫一個可以讓使用者自行新增的方法  *******/
	var tooltypeStr = [
		'<img  style="margin-top:5px;" src="img/mapButton_fullScr_off.png" onclick="changeToolType(1)" title="縮放至全圖" alt="縮放至全圖" class="mapButtonType1">',
		'<img  style="margin-top:5px;" src="img/mapButton_preScr_off.png" onclick="changeToolType(2)" title="回上一圖面" alt="回上一圖面" class="mapButtonType1">',
		'<img  style="margin-top:5px;" src="img/mapButton_nextScr_off.png" onclick="changeToolType(3)" title="回下一圖面" alt="回下一圖面" class="mapButtonType1">',
		'<img  style="margin-top:5px;" src="img/mapButton_distance_off.png" onclick="changeToolType(4)" title="測量距離" alt="測量距離" class="mapButtonType2" id="mapBtnLine">',
		'<img  style="margin-top:5px;" src="img/mapButton_area_off.png" onclick="changeToolType(5)" title="測量面積" 測量面積 class="mapButtonType2" id="mapBtnGon">',
		'<img  style="margin-top:5px;" src="img/mapButton_clean_off.png" onclick="changeToolType(6)" title="清除圖面標記" alt="清除圖面標記" class="mapButtonType1">',
		'<img  style="margin-top:5px;" src="img/mapButton_layer_off.png" onclick="changeToolType(7)" title="設定圖層顯示與透明度" alt="設定圖層顯示與透明度" class="mapButtonType2" id="mapBtnLayer">',
		'<img  style="margin-top:5px;" src="img/mapButton_info_off.png" onclick="changeToolType(8)" title="查詢圖層資訊" alt="查詢圖層資訊" class="mapButtonType2" id="mapBtnInfoWindow">',
		'<img  style="margin-top:5px;" src="img/mapButton_imgDL_off.png" onclick="changeToolType(9)" title="列印圖面" alt="列印圖面" class="mapButtonType1" id="mapBtnDownloadMapImg">',
	];
	//NOTE: 取得基本工具的順序
	var toolBoxSort = toolBoxParameter.toolBoxSort;
	//NOTE: 準備加入基本工具的字串
	//var toolBoxHtml = '<div id="toolBox" style="position: absolute;top: 90px;left: 350px;z-index: 50;">';	
	var toolBoxHtml = '<div id="toolBox" style="position: absolute;top:135px;right:6px;z-index:50;">';
	toolBoxHtml += '<img id="toolsMenu"style="width:32px;height:32px;" src="img/mapButton_more.png" onclick="openOrCloseToolBox()" title="工具列" alt="工具列">&nbsp;';
	toolBoxHtml += '<div id="toolBox_view" style=" width:32px;margin-top:-4px;padding:5px 4px; position: absolute;border: 1px solid #888; background-color:rgba(255,255,255,0.7);z-index:-1;display:none;left: 0px;">';	
	for(var x=0 ; x<toolBoxSort.length ; x++){
		toolBoxHtml += tooltypeStr[toolBoxSort[x]];
	}
	//加入圖層控制清單
	toolBoxHtml += '<div id="layerControl" style="position:absolute; top: -68px; right:55px; display:inline-block;">';
		//加入MapServer清單
		toolBoxHtml += '<div id="mapServerList" class="shadow_style" style="border-radius: 4px;display:inline-block; background-color: rgba(255,255,255,0.8);vertical-align: top;padding:6px;width: max-content;">'; //inline-block;		
			toolBoxHtml += '<table>';
			toolBoxHtml += '<thead>';
			toolBoxHtml += '<tr>';
			toolBoxHtml += '<td style="text-align:center;"colspan="4"><label>圖層設定</label> <img  style="width:20px;float:right;cursor: pointer;"alt="取消" src="img/DeleteQ.png" class="mapButtonType2"  onclick="changeToolType(7)"></td>';
			toolBoxHtml += '</tr>';
			toolBoxHtml += '</thead>';
			// 加入圖層ul - ul_mapServerlistGroup
			toolBoxHtml += '<tr><td colspan="4"><ul id="ul_mapServerlistGroup" style="width: 700px; height: 380px; overflow: auto; padding-left: 0px"></ul></td></tr>';
			toolBoxHtml += '</table>';
		toolBoxHtml += '</div>';		
	toolBoxHtml += '</div>';
	toolBoxHtml += '</div>';
	$('#'+mapDivId+'_root').prepend($(toolBoxHtml));
	initAllLayerContent();
	
	//關圖層透明度
	$("#layerControl").hide();
	//NOTE: 初始化ToolBox的HighLight模式
	initToolBoxClickEvent();
	//NOTE: 初始化所有基本工具
	initCirculateTool(-1);
	//NOTE: 設定移動動畫時間
	$("#searchDiv,#mapDiv_zoom_slider,#toolBox,#toolBox_view").css("transition-duration","0.7s");
	//NOTE: 儲存初始值
	layoutXY.searchDiv_left = $("#searchDiv").css("left");
	layoutXY.slider_left = $("#mapDiv_zoom_slider").css("left");
	layoutXY.toolBox_left = $("#toolBox").css("left");
	layoutXY.toolBox_view_left = $("#toolBox_view").css("left");
	openOrCloseToolBox();
}


//NOTE:------------------------------------------------------------------------
//NOTE: 列印取消或完畢時，回復到初始位置
//NOTE:------------------------------------------------------------------------
window.onload=function(){
	document.getElementsByTagName("BODY")[0].onafterprint = function() {resetLayout()};
}
var searchDivHide;
function resetLayout(){
	//NOTE: 回復到初始位置
	if(searchDivHide){
		var w = $("#searchDiv").width(); 
		$("#searchDiv").css('left','0px' );
		$(".esriScalebar").css('left', ((w+35)+'px' ) );	

		in_out = true;
		$("#alert_info").animate({ left: (( w + margin_left) + 'px' ),width:( getWidth_alert_info(w,in_out) )}, 600 );	 
		$("#alert_info_2").animate({ left: (( w + margin_left+alert_info_1_width ) + 'px' )  ,width:( getWidth_alert_info_2(w ,in_out) ) }, 600 );	
		
	}
	$(".esriPopup.esriPopupVisible").css("visibility","visible");
	$("#ezek_latlngInfo").css("visibility", "visible");
}
function hidePrintLayout(){
	var _checkSearchDiv = $("#searchDiv").find("em").attr("title")  ;
				
	if ( _checkSearchDiv == "收合"){
		var w = $("#searchDiv").width(); 
		$("#searchDiv").css("left",'-'+w+'px');				
		$(".esriScalebar").css( "left",'35px' );				
		$("#alert_info").css("left",( 0 + margin_left) + 'px' ).css("width" ,( getWidth_alert_info(0 ,false) ) );
		$("#alert_info_2").css("left",( 0 + margin_left+alert_info_1_width) + 'px' ).css("width" ,( getWidth_alert_info_2(0 ,false) ) );
		
		$("#upareaDivIcon").css("display", "inline-block");
		change_alert_info_height();
		searchDivHide = 1;
	}else{
		searchDivHide = 0;
	}
	$("#upareaDiv").hide();
	$(".esriPopup.esriPopupVisible").css("visibility","hidden");
	$("#ezek_latlngInfo").css("visibility", "hidden");
}
//NOTE:------------------------------------------------------------------------
//NOTE: 初始化所有基本工具
//NOTE:------------------------------------------------------------------------
function initCirculateTool(toolTypeNumber){
	ezekMap.enableScrollWheelZoom();	
	require([
		  "dojo/_base/lang",
		  "dojo/_base/array",
		  "esri/symbols/SimpleLineSymbol",
		  "esri/Color",
		  "dojo/number",
          "esri/config",
		  "esri/map",
          "esri/graphic",
          "esri/geometry/Geometry",
          "esri/geometry/Extent",
          "esri/tasks/GeometryService",
          "esri/tasks/AreasAndLengthsParameters",
		  "esri/tasks/LengthsParameters",
          "esri/toolbars/draw",
          "esri/symbols/SimpleFillSymbol",
		  "esri/symbols/PictureMarkerSymbol",
		  "esri/symbols/Font",
		  "esri/symbols/TextSymbol",
		  "esri/tasks/PrintTask",
		  "esri/tasks/PrintParameters",
		  "esri/tasks/PrintTemplate",
		  "esri/geometry/Point",
		  "esri/toolbars/navigation"],
	function(lang, array, SimpleLineSymbol, Color, number, esriConfig, Map, Graphic, Geometry, Extent, GeometryService, AreasAndLengthsParameters, LengthsParameters, Draw, SimpleFillSymbol, PictureMarkerSymbol, Font, TextSymbol, PrintTask, PrintParameters,PrintTemplate, Point, Navigation)
	{
		//NOTE: 基本變數定義區
		var PointColor = new Color([219, 0, 32]); //紅色
		var PointFont = new Font("24px", Font.STYLE_NORMAL, Font.VARIANT_NORMAL, Font.WEIGHT_BOLDER);
		var LinePointData = {
			loc: 0,
			lng: 0,
			spatialReference: null
		};
		//NOTE: 清空目前使用的工具
		if(initMarkerOnClick) initMarkerOnClick.remove();
		//NOTE: 初始化測量工具tb
		tb = new Draw(ezekMap);
		var lengthParams = new esri.tasks.LengthsParameters();
		switch(toolTypeNumber)
		{
			case -1: //不使用任何測量工具
				break;
			case 1: //縮放至全地圖
				ezekMap.setExtent(initExtent, true);
				break;
			case 2: //縮放至前一個範圍
				navToolbar.zoomToPrevExtent();
				break;
			case 3: //縮放至後一個範圍
				navToolbar.zoomToNextExtent();
				break;
			case 4: //測量距離
				$("#layerControl").hide();
				dojo.connect(gsvc, "onLengthsComplete", outputDistance);
				dojo.connect(tb, "onDrawEnd", function(geometry){ polyLineDrawEnd(geometry); });
				initMarkerOnClick = dojo.connect(ezekMap, "onClick", function(geometry){ initMarker(geometry); });
				tb.activate(Draw.POLYLINE);
				ezek_setIsToolUsing(true);
				break;
			case 5: //測量面積
				$("#layerControl").hide();
				gsvc.on("areas-and-lengths-complete", outputAreaAndLength);
				tb.on("draw-end", lang.hitch(ezekMap, getAreaAndLength));
				initMarkerOnClick = dojo.connect(ezekMap, "onClick", function(geometry){ initMarker(geometry); });
				tb.activate(Draw.POLYGON);
				ezek_setIsToolUsing(true);
				break;
			case 6: //清除所有圖層標記
				ezekMap.graphics.clear();
				ezek_setIsToolUsing(true);
				break;
			case 7: // 圖層設定
				if($("#layerControl").is(":hidden")){
					$("#layerControl").show();					
					$("#layerControl").css("display", "inline-block");
				}else{
					$("#layerControl").hide();
				}
				break;
			case 8: //點擊查詢地點，且顯示infoWindow
				if(ezek_getIsToolUsing()){
					ezek_setIsToolUsing(false);
				}else{
					ezek_setIsToolUsing(true);
				}
				break;
			case 9: //下載地圖圖片
				hidePrintLayout();
				// 橫式列印
				var css = '@page { size: landscape; }',
				head = document.head || document.getElementsByTagName('head')[0],
				style = document.createElement('style');

				style.type = 'text/css';
				style.media = 'print';

				if (style.styleSheet){
				  style.styleSheet.cssText = css;
				} else {
				  style.appendChild(document.createTextNode(css));
				}

				head.appendChild(style);
				setTimeout(function(){ //Timeout避免畫面移動時跳出列印，造成卡住
					window.print();
				},500);
				break;
			case 10: // 劃設新區域
				var msg = "";	
				// 新增
				var errMeg="「已挑選地號」至少需設置一筆區域。";
				msg = "確定要新增此筆資料？";
							
				// 判斷是否有圖型
				if(addAreaDate && addAreaDate.length > 0){
					if (confirm(msg)==true){
						attrFildSave();
					}
				}else{
					alert(errMeg);
				}
				break;
		}
		//新增測量標記
		function initMarker(geometry){
			var pictureMarkerSymbol = new PictureMarkerSymbol('img/pin70101.png', 20, 20);
			pictureMarkerSymbol.setOffset(0, 0);
			ezekMap.graphics.add(new esri.Graphic(geometry.mapPoint, pictureMarkerSymbol));
		}
		//量測距離 - 結束繪圖
		function polyLineDrawEnd(geometry){
			//繪製線段
			lengthParams.polylines = [geometry];
			lengthParams.lengthUnit = esri.tasks.GeometryService.UNIT_METER;
			lengthParams.geodesic = true;
			//geometryService.lengths(lengthParams);
			gsvc.lengths(lengthParams);
			var graphic = ezekMap.graphics.add(new esri.Graphic(geometry, new esri.symbol.SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID, PointColor, 2)));
			//儲存線段的最後一點，標記文字時會使用到
			if (geometry.paths.length > 0) {
				var loclng = geometry.paths[0][geometry.paths[0].length-1];
				LinePointData.loc = loclng[0];
				LinePointData.lng = loclng[1];
				LinePointData.spatialReference = geometry.spatialReference;
			}
		}
		//量測距離 - 開始計算
		function outputDistance(result){
			//計算距離結果
			var output = "";
			var length = result.lengths[0];
			if(length < 1000){ 
				length = (length).toFixed(2)+'公尺';
			}else{
				length = (length/1000).toFixed(2)+'公里';
			}
			if(toolBoxParameter.didFinishPolyLineCirculate){
				toolBoxParameter.didFinishPolyLineCirculate(length);
			}
			//加上量測結果文字
			var textSymbol = new TextSymbol(length,	PointFont, PointColor);
			var labelPoint = new Point(LinePointData.loc, LinePointData.lng, LinePointData.spatialReference);
			var labelPointGraphic = new Graphic(labelPoint, textSymbol);
			ezekMap.graphics.add(labelPointGraphic);
		}
		//量測面積 - 結束繪圖
		function getAreaAndLength(evtObj) {
			var map = this, geometry = evtObj.geometry;
			var sfs = new SimpleFillSymbol(SimpleFillSymbol.STYLE_SOLID,
				new SimpleLineSymbol(SimpleLineSymbol.STYLE_SOLID, PointColor, 2), new Color([0,0,0,0.25]), 
			  );
			var graphic = map.graphics.add(new esri.Graphic(geometry, sfs));
			var areasAndLengthParams = new AreasAndLengthsParameters();
			areasAndLengthParams.lengthUnit = GeometryService.UNIT_FOOT;
			areasAndLengthParams.areaUnit = GeometryService.UNIT_ACRES;
			areasAndLengthParams.calculationType = "geodesic";
			gsvc.simplify([geometry], function(simplifiedGeometries) {
				areasAndLengthParams.polygons = simplifiedGeometries;
				gsvc.areasAndLengths(areasAndLengthParams);
				gsvc.simplify([geometry], getLabelPoints);
			});
		}
		//量測面積 - 開始計算
		function outputAreaAndLength(evtObj) {
			var result = evtObj.result;
			var output = "";
			var area = result.areas[0];
			if(area < 247){ // 0.000247 英畝 = 1 平方公尺
				output = (result.areas[0]/0.000247).toFixed(2)+'平方公尺';
			}else{
				output = (result.areas[0]/247).toFixed(2)+'平方公里';
			}
			if(toolBoxParameter.didFinishPolyGonCirculate){
				toolBoxParameter.didFinishPolyGonCirculate(output);
			}
			outputText = output;
		}
		//量測面積 - 標記面積文字
		function getLabelPoints(geometries){
			if (geometries[0].rings.length > 0) {
				gsvc.labelPoints(geometries, function(labelPoints) { // callback
					array.forEach(labelPoints, function(labelPoint) {
						var textSymbol = new TextSymbol(outputText, PointFont, PointColor);
						var labelPointGraphic = new Graphic(labelPoint, textSymbol);
						ezekMap.graphics.add(labelPointGraphic);
					});
				});
			} else {
				alert("測量面積時，至少需要選擇三個量測點。");
			}
		}

		//下載地圖圖片
		function printResult(data){
			$("#mapJpgDownload").attr("href",data.url);
			$("#mapJpgDownload")[0].click();
			$.unblockUI();
		}
	});
}

//------------------------------------------------------------------------
// 劃設新區域
//------------------------------------------------------------------------
function attrFildSave(){
	var _sendAreaDate = addAreaDate;
	for(var i = 0; i < _sendAreaDate.length ; i++){
		var _ad = {};
		_sendAreaDate[i].attributes = _ad;
	}
	rmFtLayer.applyEdits(_sendAreaDate, null, null).then(
	  function() {
		// 管制圖層重新整理
		rmFtLayer.refresh();
		updateLayerDate(editLid);
	  }, function() {
		 console.log( "劃設新區域新增失敗!" );
	  });
}

//NOTE:------------------------------------------------------------------------
//NOTE: 更新圖資日期
//NOTE:------------------------------------------------------------------------
function updateLayerDate(lid){
	$.post("rm10201_man_4.jsp", {lid: lid, login_check: "PASS"}, function(data) {
		if (!data || data.result=="fail"){
			alert("更新圖資日期失敗!");
		}else if(data && data.result=="success"){
			removeALLGeo();
			clean_all();
		}
	});	
}

//NOTE:------------------------------------------------------------------------
//NOTE: 移除選擇區域
//NOTE:------------------------------------------------------------------------
function removeALLGeo(){
	$("#attrGeo").children().each(function(i, obj){
		obj.remove();
		removeRMGraphic($(obj).attr("id"));
	});
}

//NOTE:------------------------------------------------------------------------
//NOTE: 切換ToolBox的種類
//NOTE:------------------------------------------------------------------------
function changeToolType(typeNumber){
	ezek_setIsToolUsing(true);
	tb.deactivate();
	initCirculateTool(typeNumber);
}

//NOTE:------------------------------------------------------------------------
//NOTE: 取消所有的toolBox
//NOTE:------------------------------------------------------------------------
function clearToolType(){
	ezek_setIsToolUsing(true);
	tb.deactivate();
	initCirculateTool(-1);
}

//NOTE:------------------------------------------------------------------------
//NOTE: 設定ToolBox的HighLight模式
//NOTE:------------------------------------------------------------------------
function initToolBoxClickEvent() 
{
	//NOTE: 僅點選一下的功能
	$(".mapButtonType1").mousedown(function() {
		//NOTE: 取消測量距離與測量範圍
		$.each($(".mapButtonType2"), function( index, value ) {
			$(value).attr("src",$(value).attr("src").replace("_on", "_off"));
		});
		//NOTE: 設定img物件圖片切換
		changeSrcFromImg($(this));
	});
	$(".mapButtonType1").mouseup(function() {
		//NOTE: 設定img物件圖片切換
		changeSrcFromImg($(this));
	});
	//NOTE: 點選後會進行其他動作的功能
	$(".mapButtonType2").click(function() {
		//NOTE: 把其他測量的圖示取消
		$(".mapButtonType2").not($(this)).each(function() {			
			var src = $(this).attr('src');					
			src = src.replace("_on", "_off");				
			$(this).attr("src", src);
		});
		//NOTE: 設定img物件圖片切換
		var src = $(this).attr('src');
		if(src.indexOf("_on") != -1){
			src = src.replace("_on", "_off");
			clearToolType();
		}else{
			src = src.replace("_off", "_on");
		}
		$(this).attr("src", src);
	});
}

//NOTE:------------------------------------------------------------------------
//NOTE: 切換即顯示toolBox的開關
//NOTE:------------------------------------------------------------------------
function openOrCloseToolBox(){
	if($("#toolBox_view").is(":hidden")){
		$("#toolsMenu").attr("src", "img/mapButton_unexpand.png");
		$("#toolBox_view").show();
	}else{
		$("#toolsMenu").attr("src", "img/mapButton_more.png");
		$("#toolBox_view").hide();
	}
}

//------------------------------------------------------------------------
// 設定img物件圖片切換
//------------------------------------------------------------------------
function changeSrcFromImg(img) {
	var src = img.attr('src');
		if(src.indexOf("_on") != -1){
			src = src.replace("_on", "_off");
		}else{
			src = src.replace("_off", "_on");
		}
    img.attr("src", src);
}