<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>違章建築管理系統 - 即時監控儀表板</title>
    
    <!-- Bootstrap 5.3.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #0d6efd;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --dark-color: #343a40;
            --light-bg: #f8f9fa;
        }
        
        body {
            background-color: var(--light-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        /* 頂部導航列 */
        .navbar-custom {
            background-color: var(--dark-color);
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        /* 健康度評分卡 */
        .health-score-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,.08);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .health-score {
            font-size: 48px;
            font-weight: 700;
            margin: 16px 0;
        }
        
        .health-grade {
            display: inline-block;
            padding: 4px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }
        
        /* 指標卡片 */
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,.08);
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,.12);
        }
        
        .metric-card.success { border-left-color: var(--success-color); }
        .metric-card.warning { border-left-color: var(--warning-color); }
        .metric-card.danger { border-left-color: var(--danger-color); }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            line-height: 1;
        }
        
        .metric-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 8px;
        }
        
        .metric-trend {
            font-size: 14px;
            margin-top: 12px;
        }
        
        /* 圖表容器 */
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,.08);
            margin-bottom: 24px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        /* 告警清單 */
        .alert-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-left: 4px solid transparent;
            transition: all 0.2s ease;
        }
        
        .alert-item:hover {
            background: #e9ecef;
        }
        
        .alert-item.warning { border-left-color: var(--warning-color); }
        .alert-item.critical { border-left-color: var(--danger-color); }
        
        .alert-icon {
            font-size: 20px;
            margin-right: 12px;
        }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 2px;
        }
        
        .alert-time {
            font-size: 12px;
            color: #6c757d;
        }
        
        /* 即時指標動畫 */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }
        
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: var(--success-color);
            border-radius: 50%;
            margin-right: 6px;
            animation: pulse 2s infinite;
        }
        
        /* 響應式調整 */
        @media (max-width: 768px) {
            .metric-value { font-size: 24px; }
            .health-score { font-size: 36px; }
        }
        
        /* 載入動畫 */
        .spinner-grow-sm {
            width: 1rem;
            height: 1rem;
        }
        
        /* 自訂捲軸 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <!-- 頂部導航列 -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-speedometer2"></i>
                違章建築管理系統監控中心
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white me-3">
                    <span class="live-indicator"></span>
                    即時監控中
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="toggleFullScreen()">
                    <i class="bi bi-fullscreen"></i>
                </button>
            </div>
        </div>
    </nav>
    
    <!-- 主要內容區 -->
    <div class="container-fluid mt-3">
        <!-- 系統健康度總覽 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="health-score-card">
                    <h5 class="text-muted mb-0">系統健康度</h5>
                    <div class="health-score" id="systemHealthScore">92</div>
                    <span class="health-grade bg-success">A</span>
                    <div class="mt-3">
                        <small class="text-muted">最後更新: <span id="lastUpdate">10秒前</span></small>
                    </div>
                </div>
            </div>
            
            <!-- 快速指標 -->
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card success">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="metric-value" id="uptimeValue">99.95%</div>
                            <div class="metric-label">系統可用性</div>
                            <div class="metric-trend text-success">
                                <i class="bi bi-arrow-up"></i> 0.02%
                            </div>
                        </div>
                        <i class="bi bi-check-circle text-success" style="font-size: 36px;"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card warning">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="metric-value" id="responseTime">287ms</div>
                            <div class="metric-label">平均回應時間</div>
                            <div class="metric-trend text-warning">
                                <i class="bi bi-arrow-up"></i> 15ms
                            </div>
                        </div>
                        <i class="bi bi-speedometer2 text-warning" style="font-size: 36px;"></i>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="metric-card success">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <div class="metric-value" id="activeUsers">142</div>
                            <div class="metric-label">在線使用者</div>
                            <div class="metric-trend text-muted">
                                <i class="bi bi-dash"></i> 持平
                            </div>
                        </div>
                        <i class="bi bi-people text-primary" style="font-size: 36px;"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要圖表區 -->
        <div class="row">
            <!-- 系統效能趨勢圖 -->
            <div class="col-lg-8 mb-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="chart-title">系統效能趨勢</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary active" data-range="1h">1小時</button>
                            <button type="button" class="btn btn-outline-secondary" data-range="6h">6小時</button>
                            <button type="button" class="btn btn-outline-secondary" data-range="1d">1天</button>
                            <button type="button" class="btn btn-outline-secondary" data-range="7d">7天</button>
                        </div>
                    </div>
                    <canvas id="performanceChart" height="100"></canvas>
                </div>
            </div>
            
            <!-- 即時告警 -->
            <div class="col-lg-4 mb-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="chart-title">
                            即時告警 
                            <span class="badge bg-danger" id="alertCount">3</span>
                        </h5>
                        <button class="btn btn-sm btn-link text-decoration-none">
                            查看全部
                        </button>
                    </div>
                    <div id="alertList" style="max-height: 300px; overflow-y: auto;">
                        <div class="alert-item critical">
                            <i class="bi bi-exclamation-circle-fill alert-icon text-danger"></i>
                            <div class="alert-content">
                                <div class="alert-title">資料庫連線池使用率過高</div>
                                <div class="alert-time">2分鐘前</div>
                            </div>
                        </div>
                        <div class="alert-item warning">
                            <i class="bi bi-exclamation-triangle-fill alert-icon text-warning"></i>
                            <div class="alert-content">
                                <div class="alert-title">同步佇列積壓超過閾值</div>
                                <div class="alert-time">15分鐘前</div>
                            </div>
                        </div>
                        <div class="alert-item warning">
                            <i class="bi bi-exclamation-triangle-fill alert-icon text-warning"></i>
                            <div class="alert-content">
                                <div class="alert-title">CPU使用率持續偏高</div>
                                <div class="alert-time">30分鐘前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 資源使用狀況 -->
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="chart-container">
                    <h6 class="text-center mb-3">CPU 使用率</h6>
                    <canvas id="cpuGauge"></canvas>
                    <div class="text-center mt-2">
                        <span class="fs-4 fw-bold">72%</span>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="chart-container">
                    <h6 class="text-center mb-3">記憶體使用率</h6>
                    <canvas id="memoryGauge"></canvas>
                    <div class="text-center mt-2">
                        <span class="fs-4 fw-bold">68%</span>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="chart-container">
                    <h6 class="text-center mb-3">磁碟使用率</h6>
                    <canvas id="diskGauge"></canvas>
                    <div class="text-center mt-2">
                        <span class="fs-4 fw-bold">45%</span>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="chart-container">
                    <h6 class="text-center mb-3">網路流量</h6>
                    <canvas id="networkChart" height="150"></canvas>
                </div>
            </div>
        </div>
        
        <!-- 業務指標 -->
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="chart-title">案件處理統計</h5>
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>今日</option>
                            <option>本週</option>
                            <option>本月</option>
                        </select>
                    </div>
                    <canvas id="caseChart" height="80"></canvas>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="chart-container">
                    <div class="chart-header">
                        <h5 class="chart-title">資料同步狀態</h5>
                        <span class="badge bg-success">正常運行</span>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-success">1,245</h3>
                                <p class="text-muted mb-0">同步成功</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h3 class="text-danger">3</h3>
                                <p class="text-muted mb-0">同步失敗</p>
                            </div>
                        </div>
                    </div>
                    <div class="progress mt-3" style="height: 20px;">
                        <div class="progress-bar bg-success" style="width: 99.7%">99.7%</div>
                    </div>
                    <p class="text-muted text-center mt-2 mb-0">同步成功率</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自訂 JavaScript -->
    <script>
        // 全螢幕切換
        function toggleFullScreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            }
        }
        
        // Chart.js 預設設定
        Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        Chart.defaults.color = '#495057';
        
        // 系統效能趨勢圖
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        const performanceChart = new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: generateTimeLabels(60),
                datasets: [{
                    label: 'API 回應時間 (ms)',
                    data: generateRandomData(60, 200, 400),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.3,
                    fill: true
                }, {
                    label: 'CPU 使用率 (%)',
                    data: generateRandomData(60, 60, 80),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        display: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // CPU 使用率儀表
        const cpuCtx = document.getElementById('cpuGauge').getContext('2d');
        const cpuGauge = new Chart(cpuCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [72, 28],
                    backgroundColor: ['#ffc107', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
        
        // 記憶體使用率儀表
        const memoryCtx = document.getElementById('memoryGauge').getContext('2d');
        const memoryGauge = new Chart(memoryCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [68, 32],
                    backgroundColor: ['#28a745', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
        
        // 磁碟使用率儀表
        const diskCtx = document.getElementById('diskGauge').getContext('2d');
        const diskGauge = new Chart(diskCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [45, 55],
                    backgroundColor: ['#17a2b8', '#e9ecef'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                }
            }
        });
        
        // 網路流量圖
        const networkCtx = document.getElementById('networkChart').getContext('2d');
        const networkChart = new Chart(networkCtx, {
            type: 'line',
            data: {
                labels: generateTimeLabels(20, 3),
                datasets: [{
                    label: '下載',
                    data: generateRandomData(20, 50, 150),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.3,
                    fill: true,
                    pointRadius: 0
                }, {
                    label: '上傳',
                    data: generateRandomData(20, 20, 80),
                    borderColor: 'rgb(255, 159, 64)',
                    backgroundColor: 'rgba(255, 159, 64, 0.1)',
                    tension: 0.3,
                    fill: true,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                }
            }
        });
        
        // 案件處理統計
        const caseCtx = document.getElementById('caseChart').getContext('2d');
        const caseChart = new Chart(caseCtx, {
            type: 'bar',
            data: {
                labels: ['新收案', '審查中', '現勘中', '排拆中', '已結案'],
                datasets: [{
                    label: '一般違建',
                    data: [12, 19, 3, 5, 22],
                    backgroundColor: 'rgba(54, 162, 235, 0.8)'
                }, {
                    label: '廣告違建',
                    data: [8, 11, 5, 3, 15],
                    backgroundColor: 'rgba(255, 206, 86, 0.8)'
                }, {
                    label: '下水道違建',
                    data: [3, 5, 2, 1, 8],
                    backgroundColor: 'rgba(255, 99, 132, 0.8)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        stacked: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    }
                }
            }
        });
        
        // 輔助函數：生成時間標籤
        function generateTimeLabels(count, interval = 1) {
            const labels = [];
            const now = new Date();
            for (let i = count - 1; i >= 0; i--) {
                const time = new Date(now - i * interval * 60000);
                labels.push(time.toLocaleTimeString('zh-TW', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                }));
            }
            return labels;
        }
        
        // 輔助函數：生成隨機資料
        function generateRandomData(count, min, max) {
            const data = [];
            for (let i = 0; i < count; i++) {
                data.push(Math.floor(Math.random() * (max - min + 1)) + min);
            }
            return data;
        }
        
        // 模擬即時資料更新
        setInterval(() => {
            // 更新系統健康度
            const healthScore = document.getElementById('systemHealthScore');
            const currentScore = parseInt(healthScore.textContent);
            const newScore = Math.max(85, Math.min(100, currentScore + Math.floor(Math.random() * 5) - 2));
            healthScore.textContent = newScore;
            
            // 更新健康度等級
            const gradeElement = document.querySelector('.health-grade');
            if (newScore >= 95) {
                gradeElement.textContent = 'A+';
                gradeElement.className = 'health-grade bg-success';
            } else if (newScore >= 90) {
                gradeElement.textContent = 'A';
                gradeElement.className = 'health-grade bg-success';
            } else if (newScore >= 85) {
                gradeElement.textContent = 'B+';
                gradeElement.className = 'health-grade bg-info';
            }
            
            // 更新回應時間
            const responseTime = document.getElementById('responseTime');
            responseTime.textContent = Math.floor(Math.random() * 200 + 200) + 'ms';
            
            // 更新在線使用者
            const activeUsers = document.getElementById('activeUsers');
            const currentUsers = parseInt(activeUsers.textContent);
            activeUsers.textContent = Math.max(100, Math.min(200, currentUsers + Math.floor(Math.random() * 11) - 5));
            
            // 更新最後更新時間
            document.getElementById('lastUpdate').textContent = '剛剛';
            
            // 更新效能圖表
            performanceChart.data.labels.push(new Date().toLocaleTimeString('zh-TW', { 
                hour: '2-digit', 
                minute: '2-digit' 
            }));
            performanceChart.data.labels.shift();
            
            performanceChart.data.datasets[0].data.push(Math.floor(Math.random() * 200 + 200));
            performanceChart.data.datasets[0].data.shift();
            
            performanceChart.data.datasets[1].data.push(Math.floor(Math.random() * 20 + 60));
            performanceChart.data.datasets[1].data.shift();
            
            performanceChart.update('none');
            
            // 更新網路流量圖
            networkChart.data.datasets[0].data.push(Math.floor(Math.random() * 100 + 50));
            networkChart.data.datasets[0].data.shift();
            
            networkChart.data.datasets[1].data.push(Math.floor(Math.random() * 60 + 20));
            networkChart.data.datasets[1].data.shift();
            
            networkChart.update('none');
            
        }, 5000); // 每5秒更新一次
        
        // 時間範圍選擇器
        document.querySelectorAll('[data-range]').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有按鈕的 active 類別
                this.parentElement.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                // 為當前按鈕添加 active 類別
                this.classList.add('active');
                
                // 這裡可以根據選擇的時間範圍重新載入資料
                console.log('選擇時間範圍:', this.dataset.range);
            });
        });
        
        // 模擬新告警
        let alertCounter = 3;
        setInterval(() => {
            if (Math.random() > 0.8) { // 20% 機率產生新告警
                alertCounter++;
                document.getElementById('alertCount').textContent = alertCounter;
                
                // 添加新告警到列表
                const alertTypes = [
                    { level: 'warning', icon: 'exclamation-triangle-fill', color: 'warning', title: 'API 回應時間增加' },
                    { level: 'critical', icon: 'exclamation-circle-fill', color: 'danger', title: '資料同步失敗率上升' },
                    { level: 'warning', icon: 'exclamation-triangle-fill', color: 'warning', title: '磁碟空間即將不足' }
                ];
                
                const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
                const alertHtml = `
                    <div class="alert-item ${alert.level}" style="display: none;">
                        <i class="bi bi-${alert.icon} alert-icon text-${alert.color}"></i>
                        <div class="alert-content">
                            <div class="alert-title">${alert.title}</div>
                            <div class="alert-time">剛剛</div>
                        </div>
                    </div>
                `;
                
                const alertList = document.getElementById('alertList');
                alertList.insertAdjacentHTML('afterbegin', alertHtml);
                
                // 顯示新告警並帶動畫效果
                const newAlert = alertList.firstElementChild;
                setTimeout(() => {
                    newAlert.style.display = 'flex';
                    newAlert.style.opacity = '0';
                    newAlert.style.transform = 'translateX(-20px)';
                    
                    setTimeout(() => {
                        newAlert.style.transition = 'all 0.3s ease';
                        newAlert.style.opacity = '1';
                        newAlert.style.transform = 'translateX(0)';
                    }, 10);
                }, 10);
                
                // 限制告警數量
                if (alertList.children.length > 5) {
                    alertList.removeChild(alertList.lastElementChild);
                }
            }
        }, 30000); // 每30秒檢查一次
        
        // 頁面載入完成後的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 顯示載入完成提示
            console.log('監控儀表板載入完成');
            
            // 可以在這裡添加 WebSocket 連線等即時通訊功能
            // initWebSocket();
        });
        
        // 模擬 WebSocket 連線（實際應用中應連線到真實的 WebSocket 服務）
        function initWebSocket() {
            // const ws = new WebSocket('ws://localhost:8080/monitor');
            // ws.onmessage = function(event) {
            //     const data = JSON.parse(event.data);
            //     updateMetrics(data);
            // };
        }
    </script>
</body>
</html>