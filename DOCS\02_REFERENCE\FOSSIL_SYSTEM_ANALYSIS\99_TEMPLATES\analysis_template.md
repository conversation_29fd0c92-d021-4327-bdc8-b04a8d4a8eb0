# 分析範本集 (Analysis Templates)

## 📋 範本目錄

本文檔提供化石化系統分析過程中使用的各種範本，確保分析過程的標準化和一致性。

### 範本分類
- **分析記錄範本** - 系統分析過程記錄
- **風險評估範本** - 風險識別和評估
- **維護記錄範本** - 系統維護活動記錄
- **測試範本** - 測試計劃和結果記錄
- **文檔範本** - 技術文檔撰寫

---

## 🔍 分析記錄範本

### 模組分析範本
```markdown
# 模組分析記錄

## 基本資訊
- **模組ID**: [例：im10101]
- **模組名稱**: [例：違章建築A表單]
- **分析日期**: YYYY/MM/DD
- **分析人員**: [姓名]
- **分析版本**: v1.0

## 檔案組成
- **主檔案**: 
  - [ ] [module]_man.jsp (頁面檔案)
  - [ ] [module]_man.xml (配置檔案)
  - [ ] [module]_manHandlers.jsp (處理器)
- **輔助檔案**:
  - [ ] [module].js (JavaScript)
  - [ ] [module].css (樣式)
  - [ ] [其他相關檔案]

## 功能分析
### 主要功能
1. [功能1描述]
2. [功能2描述]
3. [功能3描述]

### 業務流程
```mermaid
graph TD
    A[開始] --> B[步驟1]
    B --> C[步驟2]
    C --> D[結束]
```

### 資料流分析
- **輸入資料**: [資料來源和格式]
- **處理邏輯**: [主要處理邏輯]
- **輸出結果**: [輸出格式和目標]

## 技術實作
### 資料庫操作
- **主要資料表**: [表名1, 表名2]
- **關鍵SQL**: 
  ```sql
  SELECT ... FROM ... WHERE ...
  ```
- **事務處理**: [事務邊界和處理方式]

### 前端技術
- **JavaScript庫**: [使用的JS庫]
- **Ajax操作**: [是否使用Ajax]
- **UI元件**: [特殊UI元件]

## 依賴關係
### 內部依賴
- **依賴模組**: [依賴的其他模組]
- **共用元件**: [使用的共用元件]
- **資料依賴**: [資料表依賴關係]

### 外部依賴
- **第三方庫**: [依賴的第三方庫]
- **系統介面**: [外部系統介面]
- **檔案資源**: [依賴的檔案資源]

## 風險評估
### 技術風險
- **風險等級**: [低/中/高]
- **主要風險點**: 
  1. [風險點1]
  2. [風險點2]
- **緩解措施**: [對應的緩解措施]

### 維護風險
- **複雜度**: [低/中/高]
- **文檔完整性**: [完整/部分/缺乏]
- **測試覆蓋**: [高/中/低]

## 改進建議
### 短期改進
1. [改進建議1]
2. [改進建議2]

### 長期優化
1. [優化建議1]
2. [優化建議2]

## 附錄
### 相關檔案清單
- [檔案1路徑]
- [檔案2路徑]

### 參考文檔
- [相關文檔連結]

---
**分析完成日期**: YYYY/MM/DD
**下次檢視日期**: YYYY/MM/DD
```

### 複製模式分析範本
```markdown
# 複製模式分析記錄

## 基本資訊
- **來源模組**: [原始模組ID]
- **目標模組**: [複製目標ID]
- **分析日期**: YYYY/MM/DD
- **分析人員**: [姓名]

## 相似度分析
### 檔案比較
| 檔案類型 | 來源檔案 | 目標檔案 | 相似度 | 主要差異 |
|---------|---------|---------|---------|---------|
| JSP | source.jsp | target.jsp | XX% | [差異描述] |
| XML | source.xml | target.xml | XX% | [差異描述] |
| Handler | sourceHandlers.jsp | targetHandlers.jsp | XX% | [差異描述] |

### 程式碼差異
```diff
# 主要差異內容
- 原始內容
+ 修改內容
```

## 複製模式分類
- **模式類型**: [完全克隆/參數替換/邏輯擴展/前端補償]
- **修改範圍**: [局部/中等/大幅]
- **風險等級**: [低/中/高]

## 參數替換分析
### 系統性替換
| 原始參數 | 目標參數 | 替換位置 | 是否完整 |
|---------|---------|---------|---------|
| im10101 | im10102 | 所有檔案 | ✓/✗ |
| tableA | tableB | Handler檔案 | ✓/✗ |

### 邏輯修改
1. [邏輯修改點1]
2. [邏輯修改點2]

## 前端差異分析
### JavaScript變更
- **新增函數**: [函數列表]
- **修改邏輯**: [修改描述]
- **刪除功能**: [刪除描述]

### CSS樣式變更
- **新增樣式**: [樣式描述]
- **修改樣式**: [修改描述]

## 複製品質評估
### 正確性檢查
- [ ] 所有參數替換完整
- [ ] 功能邏輯正確
- [ ] 無死連結或錯誤引用
- [ ] 資料庫操作正確

### 最佳實踐檢查
- [ ] 遵循命名規範
- [ ] 程式碼風格一致
- [ ] 註解充分
- [ ] 錯誤處理完整

## 建議改進
### 複製改進
1. [改進建議1]
2. [改進建議2]

### 標準化建議
1. [標準化建議1]
2. [標準化建議2]

---
**分析完成**: YYYY/MM/DD
```

---

## 🚨 風險評估範本

### 風險識別範本
```markdown
# 風險識別記錄

## 風險基本資訊
- **風險ID**: RISK-YYYY-XXX
- **識別日期**: YYYY/MM/DD
- **識別人員**: [姓名]
- **風險分類**: [技術/業務/操作/安全]

## 風險描述
### 風險名稱
[風險的簡短名稱]

### 風險詳述
[詳細描述風險的具體內容、原因和可能的觸發條件]

### 影響範圍
- **影響系統**: [受影響的系統或模組]
- **影響使用者**: [受影響的使用者群體]
- **影響業務**: [對業務流程的影響]

## 風險評估
### 機率評估
- **發生機率**: [低(1-30%)/中(31-70%)/高(71-100%)]
- **機率依據**: [評估機率的依據]

### 影響評估
- **影響程度**: [低/中/高/極高]
- **財務影響**: [評估金額或範圍]
- **時間影響**: [影響的時間範圍]
- **聲譽影響**: [對組織聲譽的影響]

### 綜合風險等級
- **風險等級**: [🟢低/🟡中/🟠高/🔴極高]
- **優先級**: [P1緊急/P2重要/P3普通/P4低]

## 應對策略
### 預防措施
1. [預防措施1]
2. [預防措施2]

### 緩解措施
1. [緩解措施1]
2. [緩解措施2]

### 應急預案
1. [應急措施1]
2. [應急措施2]

### 轉移措施
[風險轉移的方式，如保險、外包等]

## 監控指標
### 預警指標
- **指標1**: [具體指標和閾值]
- **指標2**: [具體指標和閾值]

### 監控頻率
- **檢查頻率**: [每日/每週/每月]
- **報告頻率**: [即時/每週/每月]

## 責任分工
- **風險負責人**: [姓名和職責]
- **監控負責人**: [姓名和職責]
- **應急負責人**: [姓名和職責]

## 後續行動
### 立即行動
- [ ] [行動項目1] - 負責人: [姓名] - 期限: YYYY/MM/DD
- [ ] [行動項目2] - 負責人: [姓名] - 期限: YYYY/MM/DD

### 持續監控
- [ ] [監控項目1] - 頻率: [頻率]
- [ ] [監控項目2] - 頻率: [頻率]

---
**建立日期**: YYYY/MM/DD
**下次檢視**: YYYY/MM/DD
**狀態**: [識別/評估/處理中/已緩解]
```

---

## 🔧 維護記錄範本

### 日常維護記錄
```markdown
# 日常維護記錄

## 維護基本資訊
- **維護日期**: YYYY/MM/DD
- **維護人員**: [姓名]
- **維護類型**: [例行檢查/故障處理/預防維護/緊急維護]
- **維護時間**: [開始時間] - [結束時間]

## 維護內容
### 檢查項目
- [ ] 系統狀態檢查
- [ ] 資料庫連接檢查
- [ ] 日誌檔案檢查
- [ ] 磁碟空間檢查
- [ ] 記憶體使用檢查
- [ ] 備份狀態檢查

### 發現問題
| 問題編號 | 問題描述 | 嚴重程度 | 處理狀態 |
|---------|---------|---------|---------|
| M001 | [問題描述] | [低/中/高] | [已處理/處理中/待處理] |
| M002 | [問題描述] | [低/中/高] | [已處理/處理中/待處理] |

### 處理措施
1. **問題M001處理**:
   - 處理方法: [具體處理步驟]
   - 處理結果: [處理結果]
   - 驗證方法: [如何驗證處理效果]

2. **問題M002處理**:
   - 處理方法: [具體處理步驟]
   - 處理結果: [處理結果]
   - 驗證方法: [如何驗證處理效果]

## 系統狀態
### 效能指標
- **CPU使用率**: XX%
- **記憶體使用率**: XX%
- **磁碟使用率**: XX%
- **資料庫連接數**: XX/XX

### 業務指標
- **使用者在線數**: XX人
- **交易成功率**: XX%
- **頁面載入時間**: XX秒
- **錯誤發生率**: XX%

## 建議事項
### 立即建議
1. [建議1]
2. [建議2]

### 長期建議
1. [建議1]
2. [建議2]

## 下次維護
- **下次例行維護**: YYYY/MM/DD
- **特別關注事項**: [需要特別關注的項目]

---
**維護完成**: YYYY/MM/DD HH:MM
**報告提交**: YYYY/MM/DD HH:MM
```

---

## 🧪 測試範本

### 功能測試範本
```markdown
# 功能測試報告

## 測試基本資訊
- **測試模組**: [模組名稱]
- **測試版本**: [版本號]
- **測試日期**: YYYY/MM/DD
- **測試人員**: [姓名]
- **測試環境**: [測試環境描述]

## 測試範圍
### 測試功能點
- [ ] 功能點1: [描述]
- [ ] 功能點2: [描述]
- [ ] 功能點3: [描述]

### 測試場景
| 場景編號 | 場景描述 | 優先級 | 測試結果 |
|---------|---------|--------|----------|
| TC001 | [場景描述] | [高/中/低] | [通過/失敗/阻塞] |
| TC002 | [場景描述] | [高/中/低] | [通過/失敗/阻塞] |

## 詳細測試結果
### TC001: [場景描述]
**測試步驟**:
1. [步驟1]
2. [步驟2]
3. [步驟3]

**預期結果**: [預期的結果]
**實際結果**: [實際的結果]
**測試結果**: ✅通過 / ❌失敗 / ⏸️阻塞

**問題記錄** (如果失敗):
- 問題描述: [具體問題]
- 重現步驟: [如何重現]
- 錯誤截圖: [截圖路徑]

## 缺陷統計
### 缺陷分布
| 嚴重程度 | 數量 | 佔比 |
|---------|------|------|
| 嚴重 | X個 | XX% |
| 一般 | X個 | XX% |
| 輕微 | X個 | XX% |

### 缺陷詳情
| 缺陷ID | 缺陷描述 | 嚴重程度 | 狀態 |
|--------|----------|----------|------|
| BUG001 | [缺陷描述] | [嚴重程度] | [開放/修復/關閉] |

## 測試結論
### 測試總結
- **總測試案例**: XX個
- **通過案例**: XX個 (XX%)
- **失敗案例**: XX個 (XX%)
- **阻塞案例**: XX個 (XX%)

### 品質評估
- **功能完整性**: [評估結果]
- **穩定性**: [評估結果]
- **效能**: [評估結果]
- **易用性**: [評估結果]

### 發布建議
- [ ] ✅ 建議發布
- [ ] ⚠️ 有條件發布 (說明條件)
- [ ] ❌ 不建議發布 (說明原因)

---
**測試完成**: YYYY/MM/DD
**報告審核**: [審核人姓名]
```

---

## 📚 使用指南

### 範本使用原則
1. **選擇適當範本**: 根據分析目的選擇對應範本
2. **完整填寫**: 不要遺漏任何必填項目
3. **客觀記錄**: 基於事實進行記錄和評估
4. **及時更新**: 資訊變更時及時更新文檔

### 範本客製化
- 可根據專案需求調整範本內容
- 保持範本的基本結構和邏輯
- 新增項目時保持命名一致性
- 定期檢視和優化範本

### 版本控制
- 重要分析文檔納入版本控制
- 使用有意義的commit訊息
- 定期備份分析文檔
- 建立分析文檔的檢索機制

---

**建立日期**: 2025-01-05  
**負責人**: 專案經理  
**版本**: v1.0