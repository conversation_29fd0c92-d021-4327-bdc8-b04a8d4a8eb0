/**
 * @author: <PERSON>
 * @created: 2021/08/31
 * 
 * @modifier: 
 * @updated: YYYY/MM/DD
 * @issue: 
 * @solution: 
 **/

/**
 * Show im20101_man_3 in plugin:fancybox.
 * 
 * @param {String} caseId Case ID.
 **/
function im_showCaseDetailInPopup(caseId) {
	var url = "im20101_man_3.jsp?vwtype=fancybox";
	url += "&case_id=" + caseId;

	$.fancybox.open({
		href: encodeURI(url), 
		type: "iframe", 
		width: 1200, 
		minWidth: 1020, // The content will show location in fancybox style, therefore setting this fancybox width to 1000px with 20px as padding
		closeBtn: false, 
		helpers: { overlay: { closeClick: false } } // Disable the close functionality by clicking on the overlay
	});
}

function im_showCaseDetailInPopup2(caseId) {
	var url = "im20101_man_3.jsp?vwtype=fancybox";
	url += "&case_id=" + caseId;
	url += "&showPostIt=Y";

	$.fancybox.open({
		href: encodeURI(url), 
		type: "iframe", 
		width: 1200, 
		minWidth: 1020, // The content will show location in fancybox style, therefore setting this fancybox width to 1000px with 20px as padding
		closeBtn: false, 
		helpers: { overlay: { closeClick: false } } // Disable the close functionality by clicking on the overlay
	});
}