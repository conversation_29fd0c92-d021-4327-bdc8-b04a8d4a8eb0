# =====================================================
# 新北市違章建築管理系統 - 生產環境配置
# =====================================================
# 檔案名稱: production.properties
# 環境: 生產環境
# 說明: 正式環境配置，所有敏感資訊必須使用環境變數
# =====================================================

# ===== 環境設定 =====
system.mode=production
system.debug=false
system.maintenance=false

# ===== 資料庫配置 =====
# 主要資料庫 (PostgreSQL) - 生產資料庫
DBConn.name=DBConn
DBConn.url=${DB_PRIMARY_URL}
DBConn.driver=org.postgresql.Driver
DBConn.user=${DB_PRIMARY_USER}
DBConn.password=${DB_PRIMARY_PASSWORD}
DBConn.maxconn=${DB_PRIMARY_MAX_CONN:80}
DBConn.timeout=${DB_PRIMARY_TIMEOUT:300}
DBConn.dbType=PostgreSQL

# 次要資料庫 (SQL Server) - 生產GIS資料庫
DBConn2.name=DBConn2
DBConn2.url=${DB_SECONDARY_URL}
DBConn2.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
DBConn2.user=${DB_SECONDARY_USER}
DBConn2.password=${DB_SECONDARY_PASSWORD}
DBConn2.maxconn=${DB_SECONDARY_MAX_CONN:100}
DBConn2.timeout=${DB_SECONDARY_TIMEOUT:300}
DBConn2.dbType=SQLServer

# ===== 應用程式設定 =====
serverUrl=${APP_SERVER_URL}
securedUrl=${APP_SECURED_URL}
third.party.service.base.url=${THIRD_PARTY_SERVICE_URL}
google.maps.service.url=${GOOGLE_MAPS_SERVICE_URL}

# ===== 安全性設定 =====
security.type=CCS
security.encryption.key=${ENCRYPTION_KEY}
security.auth.cookie.name=${AUTH_COOKIE_NAME}
security.auth.cookie.expire=${AUTH_COOKIE_EXPIRE}
security.hash.algorithm=${HASH_ALGORITHM}
ALEncryptionKey=${AL_ENCRYPTION_KEY}

# ===== 日誌設定 =====
logpriority=${LOG_LEVEL:warn}
logfile=${LOG_FILE:logs/bms-production.log}
logsize=${LOG_MAX_SIZE:20480}

# ===== 本地化設定 =====
language=${SYSTEM_LANGUAGE:zh}
defaultLocale=${SYSTEM_LOCALE:zh_TW}
requestEncoding=${REQUEST_ENCODING:UTF-8}
encoding=${SYSTEM_ENCODING:UTF-8}
defaultDateFormat=${DATE_FORMAT:yyyy-MM-dd}
defaultBooleanFormat=${BOOLEAN_FORMAT:true;false}

# ===== 效能設定 =====
cache.enabled=${CACHE_ENABLED:true}
cache.size=${CACHE_SIZE:2000}
session.timeout=${SESSION_TIMEOUT:30}
upload.max.size=${UPLOAD_MAX_SIZE:10485760}

# ===== 檔案路徑設定 =====
model.folder=${MODEL_FOLDER:}
designs.folder=${DESIGNS_FOLDER:Designs}
upload.folder=${UPLOAD_FOLDER:uploads}
temp.folder=${TEMP_FOLDER:temp}

# ===== 樣式與設計設定 =====
useDynamicStyles=${USE_DYNAMIC_STYLES:false}
defaultStyle=${DEFAULT_STYLE:Austere}
useDynamicDesigns=${USE_DYNAMIC_DESIGNS:false}
defaultDesign=${DEFAULT_DESIGN:Light}
useI18nFeatures=${USE_I18N_FEATURES:false}
isXHTMLUsed=${IS_XHTML_USED:false}
usedWarFile=${USED_WAR_FILE:true}
useAmp=${USE_AMP:true}

# ===== 生產環境專用設定 =====
test.data.enabled=false
mock.third.party.services=false
sql.debug.enabled=false
performance.monitoring.enabled=true

# ===== 安全性加強設定 =====
ssl.enabled=true
ssl.required=true
csrf.protection.enabled=true
xss.protection.enabled=true
content.security.policy.enabled=true
secure.headers.enabled=true

# ===== 監控設定 =====
health.check.enabled=true
metrics.enabled=true
audit.logging.enabled=true
error.notification.enabled=true
performance.alerts.enabled=true

# ===== 備份與災難恢復設定 =====
automated.backup.enabled=true
backup.schedule=${BACKUP_SCHEDULE:0 2 * * *}
disaster.recovery.enabled=true
data.retention.days=${DATA_RETENTION_DAYS:2555}

# ===== 負載平衡設定 =====
load.balancer.enabled=${LOAD_BALANCER_ENABLED:false}
sticky.session.enabled=${STICKY_SESSION_ENABLED:true}
session.replication.enabled=${SESSION_REPLICATION_ENABLED:false}

# ===== 資料庫進階設定 =====
connection.pool.validation.enabled=true
connection.leak.detection.enabled=true
slow.query.logging.enabled=true
database.health.check.enabled=true

# ===== 第三方服務設定 =====
third.party.timeout=${THIRD_PARTY_TIMEOUT:30}
third.party.retry.enabled=${THIRD_PARTY_RETRY_ENABLED:true}
third.party.circuit.breaker.enabled=${THIRD_PARTY_CIRCUIT_BREAKER_ENABLED:true}

# ===== 法規遵循設定 =====
gdpr.compliance.enabled=${GDPR_COMPLIANCE_ENABLED:false}
data.encryption.enabled=${DATA_ENCRYPTION_ENABLED:true}
access.logging.enabled=${ACCESS_LOGGING_ENABLED:true}
privacy.protection.enabled=${PRIVACY_PROTECTION_ENABLED:true}