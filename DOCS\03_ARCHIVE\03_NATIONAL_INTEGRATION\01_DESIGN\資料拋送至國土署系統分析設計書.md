# 違章建築資料拋送至國土署系統分析設計書

## 一、系統概述

### 1.1 專案背景
新北市違章建築管理系統需要將案件資料定期拋送至國土署，以符合中央政府資料整合要求。本文件描述如何設計一個簡潔、可靠的資料拋送服務。

### 1.2 系統目標
- 自動化案件資料拋送流程
- 確保資料完整性與一致性
- 提供完整的稽核軌跡
- 支援錯誤處理與資料回滾

### 1.3 技術選型
- **服務型態**：Windows Service
- **開發框架**：ASP.NET Core Console (.NET 8)
- **資料庫**：PostgreSQL
- **ORM**：Dapper (輕量、高效能)
- **序列化**：System.Text.Json
- **排程**：Hangfire 或 Quartz.NET

## 二、系統架構

### 2.1 分層架構
```
┌─────────────────────────────────────────┐
│         Windows Service Host            │
├─────────────────────────────────────────┤
│          Business Service               │
│   ┌──────────┐  ┌──────────────────┐  │
│   │ SyncJob  │  │ NationalAPIClient│  │
│   └──────────┘  └──────────────────┘  │
├─────────────────────────────────────────┤
│          Data Repository                │
│   ┌──────────┐  ┌──────────────────┐  │
│   │CaseRepo  │  │ SyncQueueRepo    │  │
│   └──────────┘  └──────────────────┘  │
├─────────────────────────────────────────┤
│            PostgreSQL                   │
└─────────────────────────────────────────┘
```

### 2.2 核心元件說明

#### 2.2.1 Windows Service Host
- 負責服務生命週期管理
- 載入組態設定
- 啟動排程作業

#### 2.2.2 Business Service Layer
- **SyncJob**：資料同步排程作業
- **NationalAPIClient**：國土署API客戶端
- **DataMapper**：資料格式轉換

#### 2.2.3 Data Repository Layer
- **CaseRepository**：違建案件資料存取
- **SyncQueueRepository**：同步佇列管理
- **AuditRepository**：稽核記錄管理

## 三、資料庫設計

### 3.1 核心表格結構

#### 3.1.1 違建案件主檔 (violation_cases)
```sql
CREATE TABLE violation_cases (
    id SERIAL PRIMARY KEY,
    case_no VARCHAR(50) UNIQUE NOT NULL,
    case_type VARCHAR(20) NOT NULL CHECK (case_type IN ('NORMAL', 'ADVERT', 'SEWER')),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    current_stage VARCHAR(3) NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) NOT NULL,
    updated_by VARCHAR(50) NOT NULL
);

-- 索引優化
CREATE INDEX idx_cases_status ON violation_cases(status);
CREATE INDEX idx_cases_stage ON violation_cases(current_stage);
CREATE INDEX idx_cases_type ON violation_cases(case_type);
CREATE INDEX idx_cases_created ON violation_cases(created_at);
```

#### 3.1.2 案件階段資料 (case_stages)
```sql
CREATE TABLE case_stages (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL REFERENCES violation_cases(id),
    stage_type VARCHAR(50) NOT NULL,
    stage_code VARCHAR(3) NOT NULL,
    stage_data JSONB NOT NULL,
    is_current BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) NOT NULL
);

-- 確保每個案件只有一個當前階段
CREATE UNIQUE INDEX idx_current_stage ON case_stages(case_id) WHERE is_current = TRUE;
CREATE INDEX idx_stages_case ON case_stages(case_id);
CREATE INDEX idx_stages_type ON case_stages(stage_type);
```

#### 3.1.3 同步佇列 (sync_queue)
```sql
CREATE TABLE sync_queue (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL REFERENCES violation_cases(id),
    stage_id INTEGER REFERENCES case_stages(id),
    operation VARCHAR(20) NOT NULL CHECK (operation IN ('CREATE', 'UPDATE', 'DELETE')),
    priority INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    retry_count INTEGER DEFAULT 0,
    max_retry INTEGER DEFAULT 3,
    next_retry TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    error_message TEXT
);

-- 佇列處理優化
CREATE INDEX idx_queue_status ON sync_queue(status, priority DESC, created_at);
CREATE INDEX idx_queue_retry ON sync_queue(next_retry) WHERE status = 'failed' AND retry_count < max_retry;
```

#### 3.1.4 同步歷程記錄 (sync_history)
```sql
CREATE TABLE sync_history (
    id SERIAL PRIMARY KEY,
    queue_id INTEGER REFERENCES sync_queue(id),
    case_id INTEGER NOT NULL REFERENCES violation_cases(id),
    api_endpoint VARCHAR(200) NOT NULL,
    request_data JSONB NOT NULL,
    response_data JSONB,
    http_status INTEGER,
    execution_time INTEGER, -- milliseconds
    result VARCHAR(20) NOT NULL CHECK (result IN ('success', 'failed', 'timeout')),
    error_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 查詢優化
CREATE INDEX idx_history_case ON sync_history(case_id);
CREATE INDEX idx_history_result ON sync_history(result, created_at);
CREATE INDEX idx_history_date ON sync_history(created_at);
```

#### 3.1.5 稽核記錄 (audit_log)
```sql
CREATE TABLE audit_log (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id INTEGER NOT NULL,
    action VARCHAR(10) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_data JSONB,
    new_data JSONB,
    changed_fields TEXT[], -- 變更欄位清單
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(50) NOT NULL,
    change_source VARCHAR(50) DEFAULT 'SYSTEM', -- SYSTEM, API, MANUAL
    session_id VARCHAR(100)
);

-- 稽核查詢優化
CREATE INDEX idx_audit_table_record ON audit_log(table_name, record_id, changed_at DESC);
CREATE INDEX idx_audit_date ON audit_log(changed_at);
CREATE INDEX idx_audit_user ON audit_log(changed_by);
```

### 3.2 稽核觸發器設計

#### 3.2.1 通用稽核觸發器函數
```sql
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
DECLARE
    changed_fields TEXT[];
    old_jsonb JSONB;
    new_jsonb JSONB;
BEGIN
    -- 轉換為JSONB以便比較
    IF TG_OP = 'DELETE' THEN
        old_jsonb := row_to_json(OLD)::JSONB;
        new_jsonb := NULL;
    ELSIF TG_OP = 'INSERT' THEN
        old_jsonb := NULL;
        new_jsonb := row_to_json(NEW)::JSONB;
    ELSE -- UPDATE
        old_jsonb := row_to_json(OLD)::JSONB;
        new_jsonb := row_to_json(NEW)::JSONB;
        
        -- 找出變更的欄位
        SELECT ARRAY_AGG(key) INTO changed_fields
        FROM (
            SELECT key 
            FROM jsonb_each(old_jsonb) o
            FULL OUTER JOIN jsonb_each(new_jsonb) n USING (key)
            WHERE o.value IS DISTINCT FROM n.value
        ) changes;
    END IF;
    
    -- 寫入稽核記錄
    INSERT INTO audit_log (
        table_name, 
        record_id, 
        action, 
        old_data, 
        new_data, 
        changed_fields,
        changed_by,
        change_source,
        session_id
    )
    VALUES (
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        old_jsonb,
        new_jsonb,
        changed_fields,
        COALESCE(NEW.updated_by, NEW.created_by, OLD.updated_by, current_setting('app.current_user', TRUE), 'SYSTEM'),
        current_setting('app.change_source', TRUE),
        current_setting('app.session_id', TRUE)
    );
    
    -- 自動更新 updated_at
    IF TG_OP = 'UPDATE' AND TG_TABLE_NAME IN ('violation_cases', 'case_stages') THEN
        NEW.updated_at := CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 套用到需要稽核的表格
CREATE TRIGGER audit_violation_cases
    AFTER INSERT OR UPDATE OR DELETE ON violation_cases
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_case_stages
    AFTER INSERT OR UPDATE OR DELETE ON case_stages
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_sync_queue
    AFTER INSERT OR UPDATE OR DELETE ON sync_queue
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

### 3.3 資料視圖設計

#### 3.3.1 案件完整資訊視圖
```sql
CREATE OR REPLACE VIEW v_case_full_info AS
SELECT 
    vc.id,
    vc.case_no,
    vc.case_type,
    vc.current_stage,
    vc.status,
    vc.data AS case_data,
    cs.stage_data AS current_stage_data,
    (
        SELECT jsonb_agg(
            jsonb_build_object(
                'stage_type', stage_type,
                'stage_code', stage_code,
                'stage_data', stage_data,
                'created_at', created_at
            ) ORDER BY created_at
        )
        FROM case_stages
        WHERE case_id = vc.id
    ) AS stage_history,
    vc.created_at,
    vc.updated_at
FROM violation_cases vc
LEFT JOIN case_stages cs ON cs.case_id = vc.id AND cs.is_current = TRUE;
```

#### 3.3.2 待同步案件視圖
```sql
CREATE OR REPLACE VIEW v_pending_sync AS
SELECT 
    sq.id AS queue_id,
    sq.priority,
    sq.retry_count,
    vc.case_no,
    vc.case_type,
    cs.stage_type,
    sq.operation,
    sq.created_at AS queued_at,
    sq.next_retry
FROM sync_queue sq
JOIN violation_cases vc ON vc.id = sq.case_id
LEFT JOIN case_stages cs ON cs.id = sq.stage_id
WHERE sq.status IN ('pending', 'failed')
  AND (sq.next_retry IS NULL OR sq.next_retry <= CURRENT_TIMESTAMP)
  AND sq.retry_count < sq.max_retry
ORDER BY sq.priority DESC, sq.created_at;
```

## 四、資料流程設計

### 4.1 資料寫入流程
```
使用者/系統 → API/Service → Repository → Database → Trigger → Audit Log
                                    ↓
                            Sync Queue (自動加入)
```

### 4.2 資料同步流程
```
Scheduler → SyncJob → Queue Reader → Data Mapper → API Client → National System
                ↓                           ↓
         Sync History               Error Handler
                                          ↓
                                    Retry Logic
```

### 4.3 錯誤處理與重試機制

#### 4.3.1 重試策略
```csharp
public class RetryPolicy
{
    public int MaxRetryCount { get; set; } = 3;
    public int BaseDelaySeconds { get; set; } = 60;
    
    public TimeSpan GetDelay(int retryCount)
    {
        // 指數退避：60s, 120s, 240s
        return TimeSpan.FromSeconds(BaseDelaySeconds * Math.Pow(2, retryCount - 1));
    }
}
```

#### 4.3.2 錯誤分類處理
- **暫時性錯誤**：網路逾時、服務暫時不可用 → 自動重試
- **資料錯誤**：驗證失敗、格式錯誤 → 標記需人工處理
- **系統錯誤**：認證失敗、服務異常 → 通知管理員

## 五、關鍵程式碼實作

### 5.1 Repository 介面定義

#### 5.1.1 案件資料存取介面
```csharp
public interface ICaseRepository
{
    Task<ViolationCase> GetByIdAsync(int id);
    Task<ViolationCase> GetByCaseNoAsync(string caseNo);
    Task<int> CreateAsync(ViolationCase violationCase);
    Task UpdateAsync(ViolationCase violationCase);
    Task<IEnumerable<ViolationCase>> GetPendingSyncAsync(int limit = 100);
}

public class ViolationCase
{
    public int Id { get; set; }
    public string CaseNo { get; set; }
    public string CaseType { get; set; }
    public string Status { get; set; }
    public string CurrentStage { get; set; }
    public JsonDocument Data { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string CreatedBy { get; set; }
    public string UpdatedBy { get; set; }
}
```

#### 5.1.2 同步佇列管理介面
```csharp
public interface ISyncQueueRepository  
{
    Task<int> EnqueueAsync(SyncQueueItem item);
    Task<IEnumerable<SyncQueueItem>> DequeueBatchAsync(int batchSize = 10);
    Task UpdateStatusAsync(int queueId, string status, string errorMessage = null);
    Task IncrementRetryAsync(int queueId, DateTime nextRetry);
}

public class SyncQueueItem
{
    public int Id { get; set; }
    public int CaseId { get; set; }
    public int? StageId { get; set; }
    public string Operation { get; set; }
    public int Priority { get; set; }
    public string Status { get; set; }
    public int RetryCount { get; set; }
    public DateTime? NextRetry { get; set; }
    public string ErrorMessage { get; set; }
}
```

### 5.2 資料對應邏輯

#### 5.2.1 資料轉換器
```csharp
public class DataMapper
{
    public NationalApiRequest MapToApiRequest(ViolationCase violationCase, CaseStage stage)
    {
        var caseData = violationCase.Data.RootElement;
        var stageData = stage?.StageData?.RootElement;
        
        return new NationalApiRequest
        {
            CaseNo = violationCase.CaseNo,
            CaseType = MapCaseType(violationCase.CaseType),
            StageCode = stage?.StageCode ?? violationCase.CurrentStage,
            
            // 基本資料對應
            ReportDate = GetDateValue(caseData, "rpt_day"),
            ReporterName = GetStringValue(caseData, "rep_name"),
            ViolatorName = GetStringValue(caseData, "tbvio_name"),
            ViolatorId = GetStringValue(caseData, "tbvio_unid"),
            Address = GetStringValue(caseData, "address1"),
            BuildingType = GetStringValue(caseData, "build_type"),
            
            // 階段資料對應
            StageData = stage != null ? MapStageData(stage.StageType, stageData) : null
        };
    }
    
    private object MapStageData(string stageType, JsonElement stageData)
    {
        return stageType switch
        {
            "REGISTER" => new RegisterStageData
            {
                RegisterDate = GetDateValue(stageData, "reg_date"),
                AssignedOfficer = GetStringValue(stageData, "s_empno")
            },
            "INSPECTION" => new InspectionStageData
            {
                InspectionDate = GetDateValue(stageData, "chk_day"),
                OccupiedArea = GetDecimalValue(stageData, "area_occupy"),
                ViolationDesc = GetStringValue(stageData, "violation_desc"),
                Photos = GetArrayValue(stageData, "photo_files")
            },
            "DETERMINATION" => new DeterminationStageData
            {
                DeterminationDate = GetDateValue(stageData, "conf_day"),
                DeterminationType = GetStringValue(stageData, "determination_type"),
                LegalBasis = GetStringValue(stageData, "legal_basis")
            },
            _ => stageData
        };
    }
    
    private string MapCaseType(string internalType)
    {
        return internalType switch
        {
            "NORMAL" => "01",
            "ADVERT" => "02", 
            "SEWER" => "03",
            _ => "00"
        };
    }
}
```

### 5.3 回滾實作範例

#### 5.3.1 回滾服務
```csharp
public class RollbackService
{
    private readonly IAuditRepository _auditRepo;
    private readonly IDbConnection _connection;
    
    public async Task<RollbackResult> RollbackToDateAsync(
        string tableName, 
        int recordId, 
        DateTime targetDate)
    {
        using var transaction = _connection.BeginTransaction();
        
        try
        {
            // 取得目標時間點的資料
            var targetAudit = await _auditRepo.GetAuditAtDateAsync(
                tableName, recordId, targetDate);
                
            if (targetAudit == null)
            {
                return new RollbackResult 
                { 
                    Success = false, 
                    Message = "找不到指定時間點的資料" 
                };
            }
            
            // 產生回滾SQL
            var rollbackSql = GenerateRollbackSql(
                tableName, 
                recordId, 
                targetAudit.OldData ?? targetAudit.NewData);
            
            // 執行回滾
            await _connection.ExecuteAsync(rollbackSql, transaction: transaction);
            
            // 記錄回滾操作
            await _auditRepo.LogRollbackAsync(new RollbackLog
            {
                TableName = tableName,
                RecordId = recordId,
                TargetDate = targetDate,
                RollbackFrom = DateTime.Now,
                RollbackBy = GetCurrentUser()
            });
            
            transaction.Commit();
            
            return new RollbackResult 
            { 
                Success = true, 
                Message = "回滾成功" 
            };
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            return new RollbackResult 
            { 
                Success = false, 
                Message = $"回滾失敗：{ex.Message}" 
            };
        }
    }
    
    private string GenerateRollbackSql(string tableName, int recordId, JsonElement data)
    {
        var columns = new List<string>();
        var values = new List<string>();
        
        foreach (var property in data.EnumerateObject())
        {
            if (property.Name != "id") // 排除主鍵
            {
                columns.Add(property.Name);
                values.Add(FormatValue(property.Value));
            }
        }
        
        return $@"
            UPDATE {tableName} 
            SET {string.Join(", ", columns.Select((c, i) => $"{c} = {values[i]}"))}
            WHERE id = {recordId}";
    }
}
```

## 六、部署與監控

### 6.1 Windows Service 配置

#### 6.1.1 服務設定檔 (appsettings.json)
```json
{
  "ServiceConfig": {
    "ServiceName": "ViolationDataSyncService",
    "DisplayName": "違章建築資料同步服務",
    "Description": "負責將違章建築案件資料同步至國土署系統"
  },
  "ConnectionStrings": {
    "PostgreSQL": "Host=localhost;Port=5432;Database=bms_sync;Username=postgres;Password=***"
  },
  "SyncSettings": {
    "BatchSize": 50,
    "IntervalMinutes": 30,
    "MaxConcurrentTasks": 5,
    "TimeoutSeconds": 300
  },
  "NationalAPI": {
    "BaseUrl": "https://api.nlsc.gov.tw/violation",
    "ApiKey": "YOUR_API_KEY",
    "RetryPolicy": {
      "MaxRetryCount": 3,
      "BaseDelaySeconds": 60
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "System": "Warning"
    }
  }
}
```

#### 6.1.2 服務安裝指令
```powershell
# 建立服務
sc create ViolationDataSyncService binPath="C:\Services\ViolationSync\ViolationDataSyncService.exe"

# 設定服務屬性
sc config ViolationDataSyncService start=auto
sc description ViolationDataSyncService "違章建築資料同步服務"

# 啟動服務
sc start ViolationDataSyncService
```

### 6.2 監控與告警

#### 6.2.1 健康檢查端點
```csharp
public class HealthCheckService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var listener = new HttpListener();
        listener.Prefixes.Add("http://localhost:8080/health/");
        listener.Start();
        
        while (!stoppingToken.IsCancellationRequested)
        {
            var context = await listener.GetContextAsync();
            var response = context.Response;
            
            var health = await CheckHealthAsync();
            var json = JsonSerializer.Serialize(health);
            
            response.ContentType = "application/json";
            response.StatusCode = health.IsHealthy ? 200 : 503;
            
            using var writer = new StreamWriter(response.OutputStream);
            await writer.WriteAsync(json);
        }
    }
    
    private async Task<HealthStatus> CheckHealthAsync()
    {
        return new HealthStatus
        {
            IsHealthy = true,
            Database = await CheckDatabaseAsync(),
            Queue = await CheckQueueAsync(),
            API = await CheckAPIAsync(),
            LastSync = await GetLastSyncTimeAsync()
        };
    }
}
```

#### 6.2.2 效能指標收集
```csharp
public class MetricsCollector
{
    private readonly ILogger<MetricsCollector> _logger;
    
    public void RecordSyncMetrics(SyncResult result)
    {
        _logger.LogInformation("同步完成: " +
            $"成功={result.SuccessCount}, " +
            $"失敗={result.FailedCount}, " +
            $"耗時={result.ElapsedMilliseconds}ms");
            
        // 寫入效能資料表
        var metrics = new SyncMetrics
        {
            Timestamp = DateTime.Now,
            SuccessCount = result.SuccessCount,
            FailedCount = result.FailedCount,
            AverageResponseTime = result.AverageResponseTime,
            TotalExecutionTime = result.ElapsedMilliseconds
        };
        
        SaveMetrics(metrics);
    }
}
```

## 七、安全性考量

### 7.1 資料加密
- API金鑰使用 Windows Data Protection API 加密儲存
- 敏感資料欄位（如身分證字號）在資料庫中加密
- 使用 TLS 1.2+ 進行 API 通訊

### 7.2 存取控制
- 服務帳號最小權限原則
- 資料庫連線使用專用帳號
- API 存取需要 IP 白名單

### 7.3 稽核追蹤
- 所有資料異動自動記錄
- API 呼叫記錄請求與回應
- 定期稽核報表產生

## 八、維運指南

### 8.1 日常維護
- 每日檢查同步狀態
- 每週檢視錯誤記錄
- 每月效能調校評估

### 8.2 故障排除
- 檢查服務狀態與日誌
- 驗證網路連線與 API 可用性
- 確認資料庫連線與權限

### 8.3 備份與復原
- 每日備份 audit_log 表
- 保留 30 天同步歷程
- 制定資料復原 SOP

## 九、總結

本設計採用簡潔的三層架構，搭配 PostgreSQL 的 JSONB 功能實現彈性的資料儲存。透過觸發器自動化稽核追蹤，使用佇列機制確保資料不遺失，並提供完整的錯誤處理與回滾能力。整體設計遵循 KISS 原則，易於維護與擴充。