# 新北市違章建築管理系統 - 認定完成通知流程分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.4.1
- **分析範圍**: 認定完成通知流程 (狀態碼239/259等)
- **工時**: 4小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署6個專責Agent同時進行：
- Agent 1: 認定完成通知相關JSP檔案搜尋
- Agent 2: 通知書產生與列印機制分析
- Agent 3: 分案作業處理機制分析
- Agent 4: 號碼登錄管理機制分析
- Agent 5: 送達管理機制分析
- Agent 6: JasperReports報表引擎分析

## 1. 認定完成通知流程概述

### 1.1 業務定義
認定完成通知是違章建築管理系統認定階段的結束環節，負責產生正式認定通知書、辦理號碼登錄、執行分案作業，並啟動送達程序，為後續排拆階段奠定法律基礎。

### 1.2 通知目標
- **法定告知** - 依法通知違建人認定結果
- **程序完備** - 確保行政程序完整性
- **證據保全** - 建立完整法律文件紀錄
- **流程銜接** - 啟動後續排拆作業流程

## 2. 認定完成通知系統檔案架構

### 2.1 核心通知產生模組 (im10401系列)

#### 通知書列印管理
```
im10401_lis.jsp              // 認定通知書列印主介面
im10401_lisHandlers.jsp      // 認定通知列印Handler邏輯
im10401_man.jsp              // 通知書管理頁面
im10401_manHandlers.jsp      // 通知書管理Handler
```

#### 通知列印核心邏輯
**檔案位置**: `im10401_lisHandlers.jsp`

```java
// 認定通知書列印選擇邏輯
String ib_prcs = Utils.convertToString(record.get("IB_PRCS"));
String caseId = Utils.convertToString(record.get("CASE_ID"));

// 依據業務類型決定通知書範本
if ("A".equals(ib_prcs)) {
    // 一般違建認定通知書 (認定科處理)
    reportTemplate = "general_violation_notice.jasper";
} else if ("B".equals(ib_prcs)) {
    // 廣告違建認定通知書 (廣告科處理)
    reportTemplate = "advertisement_violation_notice.jasper";
} else if ("C".equals(ib_prcs)) {
    // 下水道違建認定通知書 (勞安科處理)
    reportTemplate = "drainage_violation_notice.jasper";
}

// 產生通知書
generateNotice(caseId, reportTemplate);
```

### 2.2 通知書產生引擎 (im10101系列)

#### 報表產生核心檔案
```
im10101_prt.jsp              // 通知書產生引擎頁面
im10101_prtHandlers.jsp      // 通知書產生Handler
/WEB-INF/java/com/ezek/report/IM10101.java  // JasperReports報表類別
```

#### JasperReports報表引擎
**檔案位置**: `/WEB-INF/java/com/ezek/report/IM10101.java`

```java
public class IM10101 extends Report {
    
    // 報表產生類型定義
    public enum ReportType {
        NOTICE("Notice", "認定通知書"),
        SURVEY_SHEET("SurveySheet", "勘查紀錄表"),
        ALL("ALL", "全部產生");
        
        private String code;
        private String description;
    }
    
    // 主要產生方法
    public void generateReport(String dataNeedType, String caseId) {
        switch(dataNeedType) {
            case "Notice":
                generateNoticeOnly(caseId);
                break;
            case "SurveySheet":
                generateSurveySheetOnly(caseId);
                break;
            case "ALL":
                generateAllDocuments(caseId);
                break;
            default:
                throw new IllegalArgumentException("無效的報表類型");
        }
    }
    
    // 認定通知書產生
    private void generateNoticeOnly(String caseId) {
        try {
            // 取得案件資料
            CaseData caseData = getCaseData(caseId);
            
            // 設定報表參數
            Map<String, Object> parameters = prepareNoticeParameters(caseData);
            
            // 產生PDF
            JasperPrint jasperPrint = JasperFillManager.fillReport(
                getNoticeTemplate(caseData.getBusinessType()),
                parameters,
                getDataSource(caseId)
            );
            
            // 輸出檔案
            exportToPDF(jasperPrint, generateFileName(caseId, "NOTICE"));
            
        } catch (Exception e) {
            logger.error("認定通知書產生失敗", e);
            throw new ReportGenerationException(e);
        }
    }
    
    // 第二聯專用產生方法
    public void produceSecondCopyOnly(String caseId) {
        // 產生移交科室用的第二聯
        generateSpecificCopy(caseId, 2, "科室移交用");
    }
}
```

### 2.3 號碼登錄管理模組 (im20601系列)

#### 號碼登錄檔案
```
im20601_man.jsp              // 號碼登錄管理頁面
im20601_manHandlers.jsp      // 號碼登錄Handler邏輯
```

#### 號碼登錄核心邏輯
**檔案位置**: `im20601_manHandlers.jsp`

```java
// 號碼登錄狀態轉換邏輯
String ib_prcs = Utils.convertToString(record.get("IB_PRCS"));
String ACC_RLT;

if ("A".equals(ib_prcs)) {
    ACC_RLT = "23f";  // 一般認定號碼登錄
    description = "一般違建認定號碼登錄";
} else if ("C".equals(ib_prcs)) {
    ACC_RLT = "25f";  // 下水道認定號碼登錄
    description = "下水道違建認定號碼登錄";
} else if ("B".equals(ib_prcs)) {
    ACC_RLT = "24f";  // 廣告認定號碼登錄
    description = "廣告違建認定號碼登錄";
} else {
    throw new IllegalStateException("無效的業務處理類型: " + ib_prcs);
}

// 更新案件狀態
String UPDATE_SQL = "UPDATE IBMSTS SET ACC_RLT=?, ACC_DATE=?, ACC_TIME=?, ACC_JOB=? WHERE CASE_ID=?";
DBTools.executeUpdate(UPDATE_SQL, ACC_RLT, getCurrentYMD(), getCurrentTime(), getCurrentJobTitle(), caseId);

// 更新登錄時間
String UPDATE_TIME_SQL = "UPDATE IBMCASE SET IDNTFY_REC_TIME=? WHERE CASE_ID=?";
DBTools.executeUpdate(UPDATE_TIME_SQL, new Timestamp(System.currentTimeMillis()), caseId);

// 建立歷程記錄
String INSERT_HISTORY_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE)"
                          + " VALUES(?, ?, ?, ?, ?, ?, ?)";
DBTools.executeUpdate(INSERT_HISTORY_SQL, caseId, getCurrentYMD(), getCurrentTime(), 
                     getCurrentJobTitle(), ACC_RLT, getCurrentUser(), getCurrentYMD());
```

### 2.4 分案作業管理模組 (im20201系列)

#### 分案管理檔案
```
im20201_man.jsp              // 案件管理主介面
im20201_manHandlers.jsp      // 分案作業Handler邏輯
im20201_lis.jsp              // 分案案件清單
```

#### 分案作業邏輯
```java
// 分案完成狀態轉換
public void completeAssignment(String caseId) {
    String currentStatus = getCurrentStatus(caseId);
    String businessType = currentStatus.substring(0, 2);
    String newStatus;
    
    // 決定分案完成狀態
    switch(businessType) {
        case "23": // 一般違建
            newStatus = "230"; // 一般認定分案完成
            break;
        case "24": // 廣告違建
            newStatus = "240"; // 廣告認定分案完成
            break;
        case "25": // 下水道違建
            newStatus = "250"; // 下水道認定分案完成
            break;
        default:
            throw new IllegalStateException("無效的業務類型");
    }
    
    // 更新狀態並記錄歷程
    updateCaseStatusWithHistory(caseId, newStatus, "分案完成", getCurrentUser());
    
    // 通知排拆階段承辦人員
    notifyDemolitionProcessor(caseId, newStatus);
}
```

## 3. 認定完成通知業務流程

### 3.1 完整流程圖

```mermaid
flowchart TD
    A[認定審核簽准] --> B[狀態更新: 239/249/259]
    B --> C[號碼登錄作業]
    C --> D[狀態更新: 23f/24f/25f]
    D --> E[通知書產生]
    E --> F[多聯式通知書]
    
    F --> G[第一聯: 寄違建人]
    F --> H[第二聯: 移交科室]  
    F --> I[第三聯: 郵寄公所]
    
    G --> J[送達作業]
    H --> K[科室移交]
    I --> L[公所通知]
    
    J --> M[送達狀態追蹤]
    K --> N[分案作業]
    L --> O[公所備查]
    
    N --> P[狀態更新: 230/240/250]
    P --> Q[進入排拆階段]
    
    M --> R{送達結果}
    R -->|成功| S[送達完成]
    R -->|失敗| T[重新送達/公示送達]
    S --> U[通知程序完成]
    T --> U
```

### 3.2 狀態轉換詳細分析

#### 認定簽准階段 (239/249/259)
```java
// 認定簽准狀態確認
public boolean isApprovalCompleted(String caseId) {
    String currentStatus = getCurrentStatus(caseId);
    
    // 檢查是否為已簽准狀態
    return "239".equals(currentStatus) || // 一般認定已簽准
           "249".equals(currentStatus) || // 廣告認定已簽准
           "259".equals(currentStatus);   // 下水道認定已簽准
}

// 觸發通知流程
public void initiateNotificationProcess(String caseId) {
    if (!isApprovalCompleted(caseId)) {
        throw new IllegalStateException("案件尚未完成認定簽准");
    }
    
    // 啟動號碼登錄流程
    initiateNumberRegistration(caseId);
}
```

#### 號碼登錄階段 (23f/24f/25f)
```java
// 號碼登錄處理
public void registerNotificationNumber(String caseId) {
    try {
        conn.setAutoCommit(false);
        
        // 1. 生成認定號碼
        String recognitionNumber = generateRecognitionNumber(caseId);
        
        // 2. 更新案件主檔
        updateCaseWithRecognitionNumber(caseId, recognitionNumber);
        
        // 3. 更新狀態為號碼登錄
        String newStatus = determineRegistrationStatus(caseId);
        updateCaseStatus(caseId, newStatus);
        
        // 4. 記錄登錄時間
        updateRegistrationTime(caseId);
        
        conn.commit();
        
        // 5. 觸發通知書產生
        triggerNoticeGeneration(caseId);
        
    } catch (Exception e) {
        conn.rollback();
        throw new NumberRegistrationException("號碼登錄失敗", e);
    }
}

// 認定號碼生成邏輯
private String generateRecognitionNumber(String caseId) {
    CaseData caseData = getCaseData(caseId);
    String businessType = caseData.getBusinessType();
    int currentYear = Calendar.getInstance().get(Calendar.YEAR) - 1911; // 民國年
    
    // 取得當年度序號
    int sequenceNumber = getNextSequenceNumber(businessType, currentYear);
    
    // 組成認定號碼格式：{業務別}{民國年}{序號}
    return String.format("%s%03d%04d", businessType, currentYear, sequenceNumber);
}
```

#### 分案完成階段 (230/240/250)
```java
// 分案作業處理
public void processAssignmentCompletion(String caseId, String assignedOfficer) {
    // 確認號碼登錄已完成
    if (!isNumberRegistrationCompleted(caseId)) {
        throw new IllegalStateException("尚未完成號碼登錄");
    }
    
    // 確認通知書已產生
    if (!isNoticeGenerated(caseId)) {
        throw new IllegalStateException("尚未產生通知書");
    }
    
    // 執行分案
    executeAssignment(caseId, assignedOfficer);
    
    // 更新為分案完成狀態
    String completionStatus = determineCompletionStatus(caseId);
    updateCaseStatus(caseId, completionStatus);
    
    // 移交至排拆階段
    transferToDemolitionStage(caseId);
}
```

## 4. 通知書產生機制深度分析

### 4.1 多聯式通知書系統

#### 通知書分聯用途
```java
// 通知書分聯定義
public enum NoticeCopy {
    FIRST_COPY(1, "第一聯", "寄違建人", "WHITE"),
    SECOND_COPY(2, "第二聯", "移交科室", "YELLOW"), 
    THIRD_COPY(3, "第三聯", "郵寄公所", "PINK");
    
    private int copyNumber;
    private String copyName;
    private String purpose;
    private String paperColor;
    
    NoticeCopy(int copyNumber, String copyName, String purpose, String paperColor) {
        this.copyNumber = copyNumber;
        this.copyName = copyName;
        this.purpose = purpose;
        this.paperColor = paperColor;
    }
}

// 分聯產生邏輯
public void generateMultipleCopies(String caseId) {
    NoticeData noticeData = prepareNoticeData(caseId);
    
    for (NoticeCopy copy : NoticeCopy.values()) {
        // 為每一聯設定特定參數
        Map<String, Object> parameters = prepareParameters(noticeData, copy);
        
        // 產生PDF
        JasperPrint jasperPrint = fillReport(parameters);
        
        // 設定檔名
        String filename = String.format("%s_%s_%s.pdf", 
                                       caseId, copy.getCopyName(), 
                                       new SimpleDateFormat("yyyyMMdd").format(new Date()));
        
        // 輸出檔案
        exportToPDF(jasperPrint, filename);
        
        // 記錄產生歷程
        recordNoticeGeneration(caseId, copy, filename);
    }
}
```

### 4.2 通知書範本管理

#### 業務類型範本對應
```java
// 通知書範本選擇邏輯
public String selectNoticeTemplate(String businessType, String violationType) {
    String templatePath = "/reports/templates/";
    
    switch(businessType) {
        case "A": // 一般違建
            if ("STRUCTURAL".equals(violationType)) {
                return templatePath + "general_structural_notice.jasper";
            } else if ("USAGE".equals(violationType)) {
                return templatePath + "general_usage_notice.jasper";
            }
            return templatePath + "general_notice.jasper";
            
        case "B": // 廣告違建
            return templatePath + "advertisement_notice.jasper";
            
        case "C": // 下水道違建
            return templatePath + "drainage_notice.jasper";
            
        default:
            throw new IllegalArgumentException("無效的業務類型: " + businessType);
    }
}
```

#### 通知書內容組成
```java
// 通知書資料準備
public NoticeData prepareNoticeData(String caseId) {
    NoticeData data = new NoticeData();
    
    // 基本案件資訊
    CaseInfo caseInfo = getCaseInfo(caseId);
    data.setCaseId(caseInfo.getCaseId());
    data.setViolatorName(caseInfo.getViolatorName());
    data.setViolationAddress(caseInfo.getViolationAddress());
    data.setRecognitionNumber(caseInfo.getRecognitionNumber());
    
    // 認定結果資訊
    RecognitionResult result = getRecognitionResult(caseId);
    data.setViolationFacts(result.getViolationFacts());
    data.setLegalBasis(result.getLegalBasis());
    data.setProcessingMethod(result.getProcessingMethod());
    data.setDeadline(result.getComplianceDeadline());
    
    // 法定權利告知
    LegalRights rights = getLegalRights();
    data.setAppealRights(rights.getAppealRights());
    data.setAppealDeadline(rights.getAppealDeadline());
    data.setAppealAuthority(rights.getAppealAuthority());
    
    // 承辦資訊
    OfficerInfo officer = getOfficerInfo(caseId);
    data.setProcessingUnit(officer.getUnit());
    data.setOfficerName(officer.getName());
    data.setContactPhone(officer.getPhone());
    data.setNoticeDate(new Date());
    
    return data;
}
```

### 4.3 報表產生最佳化

#### 批次產生機制
```java
// 批次通知書產生
public class BatchNoticeGenerator {
    private static final int BATCH_SIZE = 20;
    
    public BatchGenerationResult generateBatchNotices(List<String> caseIds) {
        BatchGenerationResult result = new BatchGenerationResult();
        
        // 分批處理
        List<List<String>> batches = partitionList(caseIds, BATCH_SIZE);
        
        for (List<String> batch : batches) {
            try {
                processBatch(batch, result);
            } catch (Exception e) {
                result.addBatchError(batch, e.getMessage());
                logger.error("批次處理失敗", e);
            }
        }
        
        return result;
    }
    
    private void processBatch(List<String> batch, BatchGenerationResult result) {
        // 預先載入所有案件資料
        Map<String, NoticeData> batchData = preloadNoticeData(batch);
        
        // 並行產生通知書
        batch.parallelStream().forEach(caseId -> {
            try {
                generateNotice(caseId, batchData.get(caseId));
                result.addSuccess(caseId);
            } catch (Exception e) {
                result.addFailure(caseId, e.getMessage());
            }
        });
    }
}
```

#### 檔案壓縮與管理
```java
// 通知書檔案管理
public class NoticeFileManager {
    
    public void compressAndArchive(String caseId, List<String> generatedFiles) {
        try {
            // 建立壓縮檔
            String zipFileName = String.format("%s_notices_%s.zip", 
                                             caseId, 
                                             new SimpleDateFormat("yyyyMMdd").format(new Date()));
            
            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFileName));
            
            for (String filePath : generatedFiles) {
                addFileToZip(zipOut, filePath);
            }
            
            zipOut.close();
            
            // 移動至歸檔目錄
            moveToArchive(zipFileName);
            
            // 清理臨時檔案
            cleanupTempFiles(generatedFiles);
            
        } catch (Exception e) {
            logger.error("檔案壓縮歸檔失敗", e);
            throw new FileManagementException(e);
        }
    }
}
```

## 5. 送達管理機制

### 5.1 送達狀態管理

#### 送達狀態定義
```java
// 送達狀態枚舉
public enum DeliveryStatus {
    PENDING("PENDING", "待送達"),
    IN_TRANSIT("IN_TRANSIT", "送達中"),
    DELIVERED("DELIVERED", "送達成功"),
    FAILED("FAILED", "送達失敗"),
    RETURNED("RETURNED", "退回"),
    PUBLIC_NOTICE("PUBLIC_NOTICE", "公示送達");
    
    private String code;
    private String description;
    
    DeliveryStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
}

// 送達記錄表結構
public class DeliveryRecord {
    private String caseId;
    private String noticeId;
    private DeliveryStatus status;
    private Date deliveryDate;
    private String deliveryMethod;
    private String recipient;
    private String deliveryAddress;
    private String failureReason;
    private String evidence;
    private Date statusUpdateTime;
}
```

#### 送達處理流程
```java
// 送達處理邏輯
public void processDelivery(String caseId, String deliveryMethod) {
    DeliveryRecord record = createDeliveryRecord(caseId);
    
    try {
        switch(deliveryMethod) {
            case "REGISTERED_MAIL":
                processRegisteredMail(record);
                break;
            case "PERSONAL_SERVICE":
                processPersonalService(record);
                break;
            case "SUBSTITUTE_SERVICE":
                processSubstituteService(record);
                break;
            case "PUBLIC_NOTICE":
                processPublicNotice(record);
                break;
            default:
                throw new IllegalArgumentException("無效的送達方式");
        }
        
        // 更新送達狀態
        updateDeliveryStatus(record);
        
        // 通知承辦人員
        notifyOfficer(record);
        
    } catch (Exception e) {
        handleDeliveryFailure(record, e);
    }
}
```

### 5.2 送達結果追蹤

#### 送達回執處理
```java
// 送達回執處理
public void processDeliveryReceipt(String caseId, DeliveryReceipt receipt) {
    DeliveryRecord record = getDeliveryRecord(caseId);
    
    if (receipt.isSuccessful()) {
        // 送達成功
        record.setStatus(DeliveryStatus.DELIVERED);
        record.setDeliveryDate(receipt.getDeliveryDate());
        record.setRecipient(receipt.getRecipient());
        record.setEvidence(receipt.getEvidenceId());
        
        // 觸發後續流程
        triggerPostDeliveryProcess(caseId);
        
    } else {
        // 送達失敗
        record.setStatus(DeliveryStatus.FAILED);
        record.setFailureReason(receipt.getFailureReason());
        
        // 決定後續處理方式
        determineNextDeliveryMethod(record);
    }
    
    updateDeliveryRecord(record);
}

// 後續送達方式決定
private void determineNextDeliveryMethod(DeliveryRecord record) {
    switch(record.getStatus()) {
        case FAILED:
            if (record.getAttemptCount() < MAX_DELIVERY_ATTEMPTS) {
                // 重新嘗試送達
                scheduleRetryDelivery(record);
            } else {
                // 改為公示送達
                initiatePublicNotice(record);
            }
            break;
        case RETURNED:
            // 地址查證後重新送達
            initiateAddressVerification(record);
            break;
    }
}
```

## 6. 品質控制與監控

### 6.1 通知書品質檢查

#### 自動品質檢查
```java
// 通知書品質檢查器
public class NoticeQualityChecker {
    
    public QualityCheckResult performQualityCheck(String caseId, String noticeFilePath) {
        QualityCheckResult result = new QualityCheckResult();
        
        try {
            // 1. 檔案完整性檢查
            result.addCheck("檔案完整性", checkFileIntegrity(noticeFilePath));
            
            // 2. 內容完整性檢查
            result.addCheck("內容完整性", checkContentCompleteness(caseId, noticeFilePath));
            
            // 3. 法定要件檢查
            result.addCheck("法定要件", checkLegalRequirements(noticeFilePath));
            
            // 4. 格式規範檢查
            result.addCheck("格式規範", checkFormatCompliance(noticeFilePath));
            
            // 5. 資料一致性檢查
            result.addCheck("資料一致性", checkDataConsistency(caseId, noticeFilePath));
            
        } catch (Exception e) {
            result.addError("品質檢查過程發生錯誤: " + e.getMessage());
        }
        
        return result;
    }
    
    private boolean checkLegalRequirements(String noticeFilePath) {
        // 檢查法定必要內容
        List<String> requiredContents = Arrays.asList(
            "違章建築認定書",
            "案件編號",
            "違建人姓名",
            "違建地址",
            "違建事實",
            "法律依據",
            "處理方式",
            "申訴權利",
            "申訴期限",
            "承辦單位",
            "通知日期"
        );
        
        String noticeContent = extractTextFromPDF(noticeFilePath);
        
        for (String required : requiredContents) {
            if (!noticeContent.contains(required)) {
                return false;
            }
        }
        
        return true;
    }
}
```

### 6.2 流程監控機制

#### 處理時效監控
```java
// 處理時效監控
public class ProcessingTimeMonitor {
    
    public void monitorNotificationProcess() {
        List<String> overdueCases = findOverdueCases();
        
        for (String caseId : overdueCases) {
            ProcessingStatus status = analyzeProcessingStatus(caseId);
            
            if (status.isOverdue()) {
                // 發送逾期警告
                sendOverdueAlert(caseId, status);
                
                // 自動派工
                if (status.getDaysOverdue() > CRITICAL_OVERDUE_DAYS) {
                    autoAssignToSupervisor(caseId);
                }
            }
        }
    }
    
    private List<String> findOverdueCases() {
        String sql = """
            SELECT CASE_ID FROM IBMSTS 
            WHERE ACC_RLT IN ('239', '249', '259') 
            AND DATEDIFF(CURRENT_DATE, ACC_DATE) > ?
            """;
        
        return DBTools.queryList(sql, NOTIFICATION_DEADLINE_DAYS);
    }
}
```

### 6.3 統計報表機制

#### 通知處理統計
```sql
-- 認定通知處理統計查詢
SELECT 
    CASE 
        WHEN acc_rlt LIKE '23%' THEN '一般違建'
        WHEN acc_rlt LIKE '24%' THEN '廣告違建'  
        WHEN acc_rlt LIKE '25%' THEN '下水道違建'
    END AS business_type,
    
    COUNT(CASE WHEN acc_rlt IN ('239', '249', '259') THEN 1 END) AS approved_count,
    COUNT(CASE WHEN acc_rlt IN ('23f', '24f', '25f') THEN 1 END) AS registered_count,
    COUNT(CASE WHEN acc_rlt IN ('230', '240', '250') THEN 1 END) AS assigned_count,
    
    AVG(CASE 
        WHEN acc_rlt IN ('23f', '24f', '25f') THEN 
            DATEDIFF((SELECT MIN(acc_date) FROM ibmfym f2 
                      WHERE f2.case_id = f1.case_id AND f2.acc_rlt = f1.acc_rlt),
                     (SELECT MAX(acc_date) FROM ibmfym f3 
                      WHERE f3.case_id = f1.case_id AND f3.acc_rlt IN ('239', '249', '259')))
    END) AS avg_registration_days,
    
    AVG(CASE 
        WHEN acc_rlt IN ('230', '240', '250') THEN 
            DATEDIFF((SELECT MIN(acc_date) FROM ibmfym f2 
                      WHERE f2.case_id = f1.case_id AND f2.acc_rlt = f1.acc_rlt),
                     (SELECT MAX(acc_date) FROM ibmfym f3 
                      WHERE f3.case_id = f1.case_id AND f3.acc_rlt IN ('23f', '24f', '25f')))
    END) AS avg_assignment_days

FROM ibmfym f1
WHERE f1.acc_date >= DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH)
  AND f1.acc_rlt IN ('239', '249', '259', '23f', '24f', '25f', '230', '240', '250')
GROUP BY business_type
ORDER BY business_type;
```

## 7. 系統整合與擴展

### 7.1 外部系統整合

#### 郵政系統整合
```java
// 郵政系統API整合
public class PostalServiceIntegration {
    
    public TrackingResult sendRegisteredMail(NoticeMailInfo mailInfo) {
        try {
            // 建立郵件資料
            PostalMailRequest request = new PostalMailRequest();
            request.setRecipientName(mailInfo.getRecipientName());
            request.setRecipientAddress(mailInfo.getRecipientAddress());
            request.setMailContent(mailInfo.getNoticeContent());
            request.setServiceType("REGISTERED_MAIL");
            
            // 呼叫郵政API
            PostalServiceResponse response = postalServiceAPI.sendMail(request);
            
            // 處理回應
            if (response.isSuccess()) {
                return new TrackingResult(response.getTrackingNumber(), 
                                        response.getExpectedDeliveryDate());
            } else {
                throw new PostalServiceException(response.getErrorMessage());
            }
            
        } catch (Exception e) {
            logger.error("郵政系統整合失敗", e);
            throw new IntegrationException("郵政系統整合失敗", e);
        }
    }
    
    public DeliveryStatus queryDeliveryStatus(String trackingNumber) {
        // 查詢郵件配送狀態
        PostalTrackingResponse response = postalServiceAPI.trackMail(trackingNumber);
        
        // 轉換狀態
        return convertPostalStatus(response.getStatus());
    }
}
```

#### 電子化政府服務整合
```java
// 電子化政府服務整合
public class EGovernmentIntegration {
    
    public void sendElectronicNotice(String caseId, String citizenId) {
        try {
            // 檢查市民是否有電子化服務帳號
            if (hasEGovernmentAccount(citizenId)) {
                // 發送電子通知
                ElectronicNotice eNotice = prepareElectronicNotice(caseId);
                eGovernmentAPI.sendNotification(citizenId, eNotice);
                
                // 記錄電子送達
                recordElectronicDelivery(caseId, citizenId);
            }
            
        } catch (Exception e) {
            logger.warn("電子化通知發送失敗，將採用紙本通知", e);
            // 降級為傳統紙本通知
            fallbackToPaperNotice(caseId);
        }
    }
}
```

### 7.2 行動化支援

#### 行動端通知確認
```java
// 行動端通知確認
public class MobileNotificationSupport {
    
    public void sendMobileNotification(String caseId, String mobileNumber) {
        if (isValidMobileNumber(mobileNumber)) {
            // 發送SMS通知
            SMSMessage sms = new SMSMessage();
            sms.setRecipient(mobileNumber);
            sms.setContent(generateSMSContent(caseId));
            
            smsService.sendMessage(sms);
            
            // 發送APP推播通知
            if (hasAppInstalled(mobileNumber)) {
                PushNotification push = new PushNotification();
                push.setRecipient(mobileNumber);
                push.setTitle("違章建築認定通知");
                push.setContent("您的案件已完成認定，請查看詳細內容");
                push.setData(Map.of("caseId", caseId, "type", "RECOGNITION_NOTICE"));
                
                pushNotificationService.sendNotification(push);
            }
        }
    }
}
```

## 8. 認定完成通知SOP標準作業程序

### 8.1 號碼登錄作業SOP

#### 標準作業流程
```markdown
## 號碼登錄作業標準程序

### 前置條件確認 (5分鐘)
- [ ] 確認認定審核已簽准 (狀態239/249/259)
- [ ] 檢查認定內容無誤
- [ ] 確認相關文件齊備
- [ ] 驗證承辦人員權限

### 號碼登錄執行 (10分鐘)
- [ ] 登入im20601號碼登錄系統
- [ ] 選取待登錄案件
- [ ] 確認案件資料正確
- [ ] 執行號碼登錄程序
- [ ] 確認登錄成功 (狀態23f/24f/25f)

### 後續處理 (5分鐘)
- [ ] 檢查認定號碼格式正確
- [ ] 確認登錄時間記錄
- [ ] 通知通知書產生人員
- [ ] 更新案件處理紀錄
```

### 8.2 通知書產生作業SOP

#### 產生作業流程
```markdown
## 通知書產生作業標準程序

### 產生前檢查 (10分鐘)
- [ ] 確認號碼登錄已完成
- [ ] 檢查案件資料完整性
- [ ] 確認報表範本正確
- [ ] 驗證列印權限

### 通知書產生 (15分鐘)
- [ ] 登入im10401通知書列印系統
- [ ] 選擇案件和產生類型
- [ ] 設定產生參數
- [ ] 執行通知書產生
- [ ] 檢查產生結果

### 品質檢查 (10分鐘)
- [ ] 檢查PDF檔案完整性
- [ ] 驗證內容正確性
- [ ] 確認法定要件齊備
- [ ] 檢查格式規範

### 分聯處理 (15分鐘)
- [ ] 列印第一聯 (寄違建人)
- [ ] 列印第二聯 (移交科室)
- [ ] 列印第三聯 (郵寄公所)
- [ ] 分類歸檔各聯
```

### 8.3 送達作業SOP

#### 送達處理流程
```markdown
## 送達作業標準程序

### 送達準備 (20分鐘)
- [ ] 檢查通知書第一聯完整
- [ ] 確認收件人地址正確
- [ ] 準備掛號郵件信封
- [ ] 填寫送達清單

### 郵寄執行 (30分鐘)
- [ ] 至郵局辦理掛號郵寄
- [ ] 取得郵寄回執
- [ ] 記錄追蹤號碼
- [ ] 更新送達狀態為「送達中」

### 送達追蹤 (持續追蹤)
- [ ] 定期查詢郵寄狀態
- [ ] 接收送達回執
- [ ] 更新送達結果
- [ ] 處理異常情況

### 送達完成處理 (10分鐘)
- [ ] 確認送達成功
- [ ] 歸檔送達證明
- [ ] 更新案件狀態
- [ ] 通知承辦人員
```

### 8.4 分案作業SOP

#### 分案處理流程
```markdown
## 分案作業標準程序

### 分案前準備 (10分鐘)
- [ ] 確認通知書已產生
- [ ] 檢查送達狀態
- [ ] 評估案件複雜度
- [ ] 確認人員可用性

### 承辦人員指派 (15分鐘)
- [ ] 登入im20201分案管理系統
- [ ] 選取待分案案件
- [ ] 分析案件特性
- [ ] 指派適當承辦人員
- [ ] 設定處理期限

### 分案完成確認 (10分鐘)
- [ ] 確認指派成功
- [ ] 更新狀態為分案完成 (230/240/250)
- [ ] 通知承辦人員
- [ ] 移交相關文件

### 移交排拆階段 (5分鐘)
- [ ] 準備移交清單
- [ ] 交付案件檔案
- [ ] 更新系統狀態
- [ ] 建立追蹤機制
```

## 9. 效能最佳化與監控

### 9.1 批次處理最佳化

#### 大量通知書產生
```java
// 大量通知書批次產生最佳化
public class OptimizedBatchNoticeGenerator {
    
    private static final int OPTIMAL_BATCH_SIZE = 50;
    private final ExecutorService executorService;
    
    public CompletableFuture<BatchResult> generateBatchNoticesAsync(List<String> caseIds) {
        return CompletableFuture.supplyAsync(() -> {
            // 分割成最佳批次大小
            List<List<String>> batches = partitionList(caseIds, OPTIMAL_BATCH_SIZE);
            
            // 並行處理各批次
            List<CompletableFuture<BatchResult>> futures = batches.stream()
                .map(batch -> CompletableFuture.supplyAsync(() -> processBatch(batch), executorService))
                .collect(Collectors.toList());
            
            // 等待所有批次完成並合併結果
            return futures.stream()
                .map(CompletableFuture::join)
                .reduce(new BatchResult(), BatchResult::merge);
        });
    }
    
    private BatchResult processBatch(List<String> batch) {
        BatchResult result = new BatchResult();
        
        try {
            // 預先載入案件資料 (減少資料庫查詢)
            Map<String, CaseData> caseDataMap = preloadCaseData(batch);
            
            // 並行產生通知書
            batch.parallelStream().forEach(caseId -> {
                try {
                    generateNotice(caseId, caseDataMap.get(caseId));
                    result.addSuccess(caseId);
                } catch (Exception e) {
                    result.addFailure(caseId, e.getMessage());
                }
            });
            
        } catch (Exception e) {
            batch.forEach(caseId -> result.addFailure(caseId, e.getMessage()));
        }
        
        return result;
    }
}
```

### 9.2 快取機制

#### 範本快取
```java
// 報表範本快取管理
@Component
public class TemplateCache {
    
    private final LoadingCache<String, JasperReport> templateCache;
    
    public TemplateCache() {
        this.templateCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(Duration.ofHours(1))
            .refreshAfterWrite(Duration.ofMinutes(30))
            .build(this::loadTemplate);
    }
    
    public JasperReport getTemplate(String templateName) {
        try {
            return templateCache.get(templateName);
        } catch (Exception e) {
            logger.error("範本載入失敗: " + templateName, e);
            throw new TemplateLoadException("範本載入失敗", e);
        }
    }
    
    private JasperReport loadTemplate(String templateName) throws Exception {
        String templatePath = "/reports/templates/" + templateName;
        InputStream templateStream = getClass().getResourceAsStream(templatePath);
        
        if (templateStream == null) {
            throw new FileNotFoundException("找不到範本檔案: " + templatePath);
        }
        
        return JasperCompileManager.compileReport(templateStream);
    }
}
```

### 9.3 監控指標

#### 關鍵績效指標 (KPI)
```java
// 通知處理績效監控
@Component
public class NotificationPerformanceMonitor {
    
    @Scheduled(fixedRate = 300000) // 每5分鐘執行
    public void collectMetrics() {
        try {
            // 收集處理時效指標
            ProcessingTimeMetrics timeMetrics = calculateProcessingTime();
            meterRegistry.gauge("notification.avg_processing_time", timeMetrics.getAverageTime());
            meterRegistry.gauge("notification.max_processing_time", timeMetrics.getMaxTime());
            
            // 收集成功率指標
            SuccessRateMetrics successMetrics = calculateSuccessRate();
            meterRegistry.gauge("notification.success_rate", successMetrics.getSuccessRate());
            meterRegistry.gauge("notification.failure_rate", successMetrics.getFailureRate());
            
            // 收集送達狀態指標
            DeliveryStatusMetrics deliveryMetrics = calculateDeliveryStatus();
            meterRegistry.gauge("delivery.pending_count", deliveryMetrics.getPendingCount());
            meterRegistry.gauge("delivery.success_count", deliveryMetrics.getSuccessCount());
            meterRegistry.gauge("delivery.failed_count", deliveryMetrics.getFailedCount());
            
        } catch (Exception e) {
            logger.error("績效指標收集失敗", e);
        }
    }
}
```

## 總結

### 認定完成通知流程特點

#### 系統優勢
1. **完整的文件產生** - JasperReports專業報表引擎
2. **多聯式通知書** - 滿足不同用途的分聯需求
3. **自動化流程** - 號碼登錄到分案的自動化處理
4. **完善的追蹤機制** - 送達狀態全程追蹤

#### 技術特色
1. **模組化設計** - 清楚分離的功能模組
2. **批次處理能力** - 支援大量案件批次處理
3. **品質控制機制** - 多層級品質檢查
4. **外部系統整合** - 郵政系統與電子化服務整合

#### 業務價值
1. **法定程序完備** - 確保行政程序合法性
2. **處理效率提升** - 自動化減少人工作業
3. **追蹤透明化** - 完整的處理歷程記錄
4. **服務品質保證** - 標準化作業流程

#### 改進建議
1. **數位化通知** - 整合電子化政府服務
2. **智能化分案** - AI輔助最佳分案決策
3. **即時狀態更新** - 建立即時狀態推播機制
4. **多元送達方式** - 整合更多送達管道

新北市違章建築管理系統的認定完成通知流程展現了完整的行政程序與文件管理能力，透過號碼登錄、通知書產生、分案作業、送達管理等環節，確保認定結果的正式通知與後續處理的順利銜接。系統具備良好的可擴展性，為未來數位化轉型提供了堅實基礎。

---

**文件狀態**: ✅ 已完成  
**下一步**: 執行 T2.5.1 拆除通知流程分析