# 新北市違章建築管理系統 - 查報資料驗證機制分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.1.3
- **分析範圍**: 查報資料驗證機制
- **工時**: 3小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署4個專責Agent同時進行：
- Agent 1: JavaScript客戶端驗證分析
- Agent 2: XML配置驗證規則分析
- Agent 3: Handler伺服器端驗證分析
- Agent 4: 重複檢查機制分析

## 1. 查報資料驗證機制概述

### 1.1 驗證架構概要
新北市違章建築管理系統採用**CodeCharge Studio三層驗證架構**，確保資料品質和業務規則正確性：

1. **前端即時驗證** - JavaScript客戶端驗證
2. **配置式驗證** - XML配置檔案定義的規則
3. **後端業務驗證** - Handler中的Java業務邏輯驗證

### 1.2 驗證目標
- **資料完整性** - 確保必要資料完整填寫
- **格式正確性** - 驗證資料格式符合規範
- **業務邏輯性** - 確保符合業務規則
- **唯一性檢查** - 避免重複案件登記
- **安全性控制** - 防止惡意資料注入

## 2. 三層驗證架構詳析

### 2.1 驗證執行流程

```mermaid
flowchart TD
    A[使用者輸入資料] --> B[JavaScript即時驗證]
    B -->|通過| C[表單提交]
    B -->|失敗| D[顯示錯誤訊息]
    D --> A
    
    C --> E[XML配置驗證]
    E -->|通過| F[Handler處理]
    E -->|失敗| G[CodeCharge錯誤處理]
    G --> D
    
    F --> H[Java業務邏輯驗證]
    H -->|通過| I[重複檢查API]
    H -->|失敗| J[業務錯誤處理]
    J --> D
    
    I -->|無重複| K[資料庫寫入]
    I -->|有重複| L[重複警告]
    L --> D
    
    K --> M[驗證完成]
```

### 2.2 驗證層級職責

| 驗證層級 | 執行位置 | 主要職責 | 優點 | 缺點 |
|----------|----------|----------|------|------|
| **JavaScript** | 客戶端瀏覽器 | 即時驗證、格式檢查 | 快速反應、使用者體驗佳 | 可被繞過、安全性低 |
| **XML配置** | CodeCharge框架 | 自動生成驗證規則 | 配置靈活、統一管理 | 功能有限、自訂困難 |
| **Handler Java** | 伺服器端 | 業務邏輯驗證 | 安全可靠、邏輯複雜 | 反應較慢、開發成本高 |

## 3. JavaScript客戶端驗證機制

### 3.1 核心驗證函數庫

#### Functions.js - 基礎驗證框架
**檔案位置**: `/mnt/d/apache-tomcat-9.0.98/webapps/src/Functions.js`

```javascript
// 主要驗證函數
function validate_control(control) {
    // 必填欄位驗證
    if (typeof(control.ccsRequired) == "boolean" && control.ccsRequired) {
        if (control.value == "") {
            return ccsShowError(control, "The value in field {0} is required.");
        }
    }
    
    // 格式驗證 (正規表達式)
    if (typeof(control.ccsRegExp) == "string") {
        if (control.value != "" && (control.value.search(new RegExp(control.ccsRegExp, "i")) == -1)) {
            return ccsShowError(control, "The value in field {0} is not valid.");
        }
    }
    
    // 日期格式驗證
    if (typeof(control.ccsDateFormat) == "string") {
        if (control.value != "" && !checkDate(control.value, parseDateFormat(control.ccsDateFormat).join(""))) {
            return ccsShowError(control, "The value in field {0} is not valid.");
        }
    }
    
    return true;
}

// 表單整體驗證
function validate_form(form) {
    for (var i=0; i < form.elements.length; i++) {
        if (!validate_control(form.elements[i])) {
            return false;
        }
    }
    return true;
}
```

#### functions_synct_im.js - 擴展驗證函數
**檔案位置**: `/mnt/d/apache-tomcat-9.0.98/webapps/src/functions_synct_im.js`

```javascript
// 日期格式驗證與美化
function ezek_validateDates() {
    // 驗證日期格式 (民國年/西元年)
    var datePattern = /^\d{1,3}\/\d{1,2}\/\d{1,2}$/;
    // 實作細節...
}

// 日期區間驗證
function ezek_validateBetweenDates(startDate, endDate) {
    if (startDate > endDate) {
        alert("起始日期不可晚於結束日期");
        return false;
    }
    return true;
}

// 數值範圍驗證
function ezek_validateNumericFields() {
    // 驗證數值欄位範圍
    // 實作細節...
}
```

### 3.2 客戶端驗證規則

#### 必填欄位驗證
```javascript
// 自動檢查具有 ccsRequired 屬性的欄位
<input type="text" name="DIS_B_ADDRD" ccsRequired="true" 
       onblur="validate_control(this)">
```

#### 格式驗證範例
```javascript
// 電話號碼驗證
ccsRegExp = "^[0-9()-]+$"

// 電子郵件驗證  
ccsRegExp = "^[\\w\\.-]+@[\\w\\.-]+\\.\\w+$"

// 身分證字號驗證
ccsRegExp = "^[A-Z][12][0-9]{8}$"

// 台灣手機號碼驗證
ccsRegExp = "^09[0-9]{8}$"
```

#### 錯誤處理機制
```javascript
function ccsShowError(control, msg) {
    // 替換訊息中的欄位名稱
    if (typeof(control.ccsCaption) == "string") {
        msg = msg.replace("{0}", control.ccsCaption);
    }
    
    // 顯示錯誤訊息
    alert(msg);
    
    // 聚焦到錯誤欄位
    if (control.focus) control.focus();
    
    return false;
}
```

## 4. XML配置驗證規則

### 4.1 XML驗證屬性結構

#### 主要驗證屬性
**檔案範例**: `im10101_man.xml`

```xml
<Record name="IBMCASE" connection="DBConn" allowInsert="True" allowUpdate="True">
    <!-- 文字欄位驗證 -->
    <TextBox name="DIS_B_ADDRD" caption="違建物路段" dataType="Text"
             required="True" unique="False" maxLength="50"
             verificationRule="^[\u4e00-\u9fa50-9\s]+$" 
             errorControl="DIS_B_ADDRD_Error">
    </TextBox>
    
    <!-- 日期欄位驗證 -->
    <DatePicker name="REG_DATE" caption="登記日期" 
                required="True" format="yyyy/mm/dd"
                verificationRule="ValidDate">
    </DatePicker>
    
    <!-- 下拉選單驗證 -->
    <ListBox name="DIS_B_ADDZON" caption="違建物行政區" 
             required="True" dataType="Text">
        <Select query="SELECT * FROM ibmcode WHERE code_type = 'ZON' ORDER BY code_seq"/>
    </ListBox>
</Record>
```

#### 驗證屬性說明

| 屬性名稱 | 資料類型 | 功能說明 | 範例值 |
|----------|----------|----------|--------|
| **required** | Boolean | 必填欄位檢查 | `True/False` |
| **unique** | Boolean | 唯一性檢查 | `True/False` |
| **maxLength** | Integer | 最大長度限制 | `50` |
| **minLength** | Integer | 最小長度限制 | `5` |
| **verificationRule** | String | 自訂驗證規則 | 正規表達式 |
| **format** | String | 顯示格式 | `yyyy/mm/dd` |
| **dbFormat** | String | 資料庫格式 | `YYYY-MM-DD` |
| **errorControl** | String | 錯誤訊息控制項 | `FieldName_Error` |

### 4.2 常用驗證規則配置

#### 台灣地址驗證
```xml
<TextBox name="DIS_B_ADDRD" caption="違建物路段"
         verificationRule="^[\u4e00-\u9fa50-9\s\-()]+$">
</TextBox>
```

#### 數值範圍驗證
```xml
<TextBox name="DIS_AREA" caption="違建面積"
         verificationRule="^[0-9]+(\.[0-9]{1,2})?$"
         dataType="Number">
</TextBox>
```

#### 複合欄位驗證
```xml
<!-- 地號驗證 -->
<TextBox name="LAN_BNID" caption="地號段別"
         verificationRule="^[0-9]{1,4}$">
</TextBox>
<TextBox name="LAN_LNID" caption="地號地號"  
         verificationRule="^[0-9]{1,4}(-[0-9]{1,4})?$">
</TextBox>
```

## 5. Handler伺服器端驗證

### 5.1 業務邏輯驗證

#### 檔案位置與結構
**主要Handler檔案**: `im10101_manHandlers.jsp`

```java
public class im10101_manHandlers {
    
    // 權限驗證
    public void validateUserPermission() {
        String loginPass = SessionStorage.getInstance(e.getPage().getRequest())
                          .getAttributeAsString("LoginPass");
        if (!"True".equals(loginPass)) {
            e.getPage().setRedirectString("timeout_err.jsp");
            return;
        }
    }
    
    // 資料完整性驗證
    public boolean validateCaseData(Record record) {
        String dis_b_addzon = Utils.convertToString(record.get("DIS_B_ADDZON"));
        String dis_b_addrd = Utils.convertToString(record.get("DIS_B_ADDRD"));
        
        // 必填欄位檢查
        if (StringUtils.isEmpty(dis_b_addzon)) {
            addValidationError("違建物行政區為必填欄位");
            return false;
        }
        
        if (StringUtils.isEmpty(dis_b_addrd)) {
            addValidationError("違建物路段為必填欄位");
            return false;
        }
        
        return true;
    }
    
    // 業務規則驗證
    public boolean validateBusinessRules(Record record) {
        Date regDate = (Date) record.get("REG_DATE");
        Date repDate = (Date) record.get("REP_DATE");
        
        // 檢舉日期不可晚於登記日期
        if (repDate != null && regDate != null) {
            if (repDate.after(regDate)) {
                addValidationError("檢舉日期不可晚於登記日期");
                return false;
            }
        }
        
        return true;
    }
}
```

### 5.2 資料庫約束驗證

#### 外鍵約束檢查
```java
public boolean validateForeignKeys(Record record) {
    try {
        // 檢查行政區代碼是否存在
        String dis_b_addzon = Utils.convertToString(record.get("DIS_B_ADDZON"));
        String checkSQL = "SELECT COUNT(*) FROM ibmcode WHERE code_type='ZON' AND code_seq=?";
        int count = Integer.parseInt(DBTools.dLookUp("COUNT(*)", "ibmcode", 
                                   "code_type='ZON' AND code_seq='" + dis_b_addzon + "'", "DBConn"));
        
        if (count == 0) {
            addValidationError("行政區代碼不存在");
            return false;
        }
        
        return true;
    } catch (Exception e) {
        addValidationError("驗證過程發生錯誤：" + e.getMessage());
        return false;
    }
}
```

#### 狀態轉換驗證
```java
public boolean validateStatusTransition(String currentStatus, String newStatus) {
    // 狀態轉換規則矩陣
    Map<String, List<String>> validTransitions = Map.of(
        "01", Arrays.asList("02", "03"),           // 掛號 → 勘查/認定
        "02", Arrays.asList("03", "04"),           // 勘查 → 認定/通知
        "03", Arrays.asList("04", "05"),           // 認定 → 通知/排拆
        "04", Arrays.asList("05"),                 // 通知 → 排拆
        "05", Arrays.asList("99")                  // 排拆 → 結案
    );
    
    List<String> allowedNextStates = validTransitions.get(currentStatus);
    if (allowedNextStates == null || !allowedNextStates.contains(newStatus)) {
        addValidationError("不允許的狀態轉換：" + currentStatus + " → " + newStatus);
        return false;
    }
    
    return true;
}
```

## 6. 重複檢查機制

### 6.1 地址重複檢查

#### 檔案位置與API
**檔案**: `im10101_man_checkAddr.jsp`

```java
<%
// 接收參數
String case_id = Utils.convertToString(request.getParameter("CASE_ID"));
String dis_b_addzon = Utils.convertToString(request.getParameter("DIS_B_ADDZON"));
String dis_b_addrd = Utils.convertToString(request.getParameter("DIS_B_ADDRD"));
String dis_b_addln = Utils.convertToString(request.getParameter("DIS_B_ADDLN"));
String dis_b_add1 = Utils.convertToString(request.getParameter("DIS_B_ADD1"));
String dis_b_add2 = Utils.convertToString(request.getParameter("DIS_B_ADD2"));

// 建構查詢條件
String whereSql = "CASE_ID <> '" + case_id + "'";

if (!StringUtils.isEmpty(dis_b_addzon)) {
    whereSql += " AND DIS_B_ADDZON = '" + dis_b_addzon + "'";
}
if (!StringUtils.isEmpty(dis_b_addrd)) {
    whereSql += " AND DIS_B_ADDRD = '" + dis_b_addrd + "'";
}
if (!StringUtils.isEmpty(dis_b_addln)) {
    whereSql += " AND DIS_B_ADDLN = '" + dis_b_addln + "'";
}
if (!StringUtils.isEmpty(dis_b_add1)) {
    whereSql += " AND DIS_B_ADD1 = '" + dis_b_add1 + "'";
}
if (!StringUtils.isEmpty(dis_b_add2)) {
    whereSql += " AND DIS_B_ADD2 = '" + dis_b_add2 + "'";
}

// 執行查詢
String resultCount = Utils.convertToString(DBTools.dLookUp("count(*)", "IBMCASE", whereSql, "DBConn"));

// 回傳結果
out.print(resultCount);
%>
```

#### 前端AJAX調用
```javascript
function checkAddressDuplicate() {
    var params = {
        'CASE_ID': document.forms[0].CASE_ID.value,
        'DIS_B_ADDZON': document.forms[0].DIS_B_ADDZON.value,
        'DIS_B_ADDRD': document.forms[0].DIS_B_ADDRD.value,
        'DIS_B_ADDLN': document.forms[0].DIS_B_ADDLN.value,
        'DIS_B_ADD1': document.forms[0].DIS_B_ADD1.value,
        'DIS_B_ADD2': document.forms[0].DIS_B_ADD2.value
    };
    
    $.post('im10101_man_checkAddr.jsp', params, function(data) {
        var count = parseInt(data);
        if (count > 0) {
            alert('發現 ' + count + ' 筆相同地址的案件，請確認是否重複登記');
        }
    });
}
```

### 6.2 地號重複檢查

#### 檔案位置與API
**檔案**: `im10101_man_checkCslan.jsp`

```java
<%
// 接收地號參數
String case_id = Utils.convertToString(request.getParameter("CASE_ID"));
String lan_dist = Utils.convertToString(request.getParameter("LAN_DIST"));
String lan_bnid = Utils.convertToString(request.getParameter("LAN_BNID"));
String lan_lnid = Utils.convertToString(request.getParameter("LAN_LNID"));
String lan_snid = Utils.convertToString(request.getParameter("LAN_SNID"));

// 建構地號查詢條件
String whereSql = "CASE_ID <> '" + case_id + "'";

if (!StringUtils.isEmpty(lan_dist)) {
    whereSql += " AND LAN_DIST = '" + lan_dist + "'";
}
if (!StringUtils.isEmpty(lan_bnid)) {
    whereSql += " AND LAN_BNID = '" + lan_bnid + "'";
}
if (!StringUtils.isEmpty(lan_lnid)) {
    whereSql += " AND LAN_LNID = '" + lan_lnid + "'";
}
if (!StringUtils.isEmpty(lan_snid)) {
    whereSql += " AND LAN_SNID = '" + lan_snid + "'";
}

// 從地號檔查詢重複
String resultCount = Utils.convertToString(DBTools.dLookUp("count(*)", "IBMCSLAN", whereSql, "DBConn"));

// 回傳結果
out.print(resultCount);
%>
```

### 6.3 重複檢查觸發時機

#### 即時檢查觸發點
```javascript
// 地址欄位失去焦點時檢查
document.forms[0].DIS_B_ADDRD.onblur = function() {
    checkAddressDuplicate();
};

// 地號欄位變更時檢查
document.forms[0].LAN_LNID.onchange = function() {
    checkLandNumberDuplicate();
};

// 表單提交前最終檢查
function validateForm() {
    return checkAddressDuplicate() && checkLandNumberDuplicate();
}
```

## 7. 錯誤處理與訊息機制

### 7.1 多層級錯誤處理

#### 客戶端錯誤處理
```javascript
// 即時錯誤顯示
function showValidationError(message, fieldName) {
    // 顯示錯誤訊息
    alert(message);
    
    // 聚焦到錯誤欄位
    var field = document.forms[0][fieldName];
    if (field && field.focus) {
        field.focus();
    }
    
    // 標記錯誤狀態
    if (field && field.style) {
        field.style.backgroundColor = '#ffcccc';
    }
}

// 清除錯誤狀態
function clearValidationError(fieldName) {
    var field = document.forms[0][fieldName];
    if (field && field.style) {
        field.style.backgroundColor = '';
    }
}
```

#### 伺服器端錯誤處理
```java
public class ValidationErrorHandler {
    private List<String> errors = new ArrayList<>();
    
    public void addValidationError(String message) {
        errors.add(message);
    }
    
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    public String getErrorsAsJson() {
        return "{\"errors\":" + JSON.stringify(errors) + "}";
    }
    
    public void handleValidationFailure() {
        if (hasErrors()) {
            // 記錄錯誤日誌
            logger.warn("Validation failed: " + String.join(", ", errors));
            
            // 設定錯誤訊息到Session
            SessionStorage.getInstance(request).setAttribute("ValidationErrors", errors);
            
            // 重新導向回原頁面
            response.sendRedirect(request.getHeader("Referer"));
        }
    }
}
```

### 7.2 錯誤訊息國際化

#### 錯誤訊息對照表
```java
public class ValidationMessages {
    private static final Map<String, String> messages = Map.of(
        "REQUIRED_FIELD", "此欄位為必填項目",
        "INVALID_FORMAT", "資料格式不正確",
        "DUPLICATE_ADDRESS", "地址重複，請確認是否重複登記",
        "DUPLICATE_LAND", "地號重複，請確認是否重複登記",
        "INVALID_DATE_RANGE", "日期區間不正確",
        "PERMISSION_DENIED", "您沒有權限執行此操作",
        "DATABASE_ERROR", "資料庫操作失敗，請聯絡系統管理員"
    );
    
    public static String getMessage(String key, Object... params) {
        String template = messages.getOrDefault(key, key);
        return MessageFormat.format(template, params);
    }
}
```

## 8. 驗證規則配置管理

### 8.1 驗證規則中央管理

#### 建議的驗證配置檔案
```json
{
  "validationRules": {
    "address": {
      "pattern": "^[\u4e00-\u9fa50-9\s\-()]+$",
      "message": "地址格式不正確，僅允許中文、數字、空格、括號和連字號"
    },
    "phoneNumber": {
      "pattern": "^(0[0-9]-?)?[0-9]{7,8}$|^09[0-9]{8}$",
      "message": "電話號碼格式不正確"
    },
    "taiwanId": {
      "pattern": "^[A-Z][12][0-9]{8}$",
      "message": "身分證字號格式不正確"
    },
    "landNumber": {
      "pattern": "^[0-9]{1,4}(-[0-9]{1,4})?$",
      "message": "地號格式不正確"
    }
  }
}
```

### 8.2 自訂驗證規則擴展

#### 新增自訂驗證器
```javascript
// 自訂驗證器註冊
ValidationEngine.addCustomValidator('taiwanBusinessId', function(value) {
    // 台灣統一編號驗證邏輯
    if (!value || value.length !== 8) return false;
    
    var weights = [1, 2, 1, 2, 1, 2, 4, 1];
    var sum = 0;
    
    for (var i = 0; i < 8; i++) {
        var digit = parseInt(value[i]);
        var product = digit * weights[i];
        sum += Math.floor(product / 10) + (product % 10);
    }
    
    return sum % 10 === 0;
});

// 使用自訂驗證器
<TextBox name="BUSINESS_ID" verificationRule="taiwanBusinessId" />
```

## 9. 效能最佳化建議

### 9.1 驗證效能優化

#### 非同步驗證最佳化
```javascript
// 防抖動機制，減少重複檢查
var checkAddressTimeout;
function debouncedAddressCheck() {
    clearTimeout(checkAddressTimeout);
    checkAddressTimeout = setTimeout(function() {
        checkAddressDuplicate();
    }, 500); // 500ms 延遲
}

// 快取機制，避免重複查詢
var addressCheckCache = {};
function cachedAddressCheck(addressKey) {
    if (addressCheckCache[addressKey]) {
        return Promise.resolve(addressCheckCache[addressKey]);
    }
    
    return checkAddressDuplicate().then(function(result) {
        addressCheckCache[addressKey] = result;
        return result;
    });
}
```

#### 批次驗證處理
```java
public class BatchValidator {
    public ValidationResult validateBatch(List<Record> records) {
        List<ValidationError> errors = new ArrayList<>();
        
        // 批次檢查重複地址
        Set<String> addresses = records.stream()
            .map(this::buildAddressKey)
            .collect(Collectors.toSet());
            
        Map<String, Integer> duplicateCounts = checkDuplicateAddresses(addresses);
        
        // 批次檢查業務規則
        for (Record record : records) {
            errors.addAll(validateSingleRecord(record, duplicateCounts));
        }
        
        return new ValidationResult(errors);
    }
}
```

### 9.2 資料庫查詢最佳化

#### 重複檢查索引最佳化
```sql
-- 地址重複檢查索引
CREATE INDEX idx_ibmcase_address_duplicate ON IBMCASE 
(DIS_B_ADDZON, DIS_B_ADDRD, DIS_B_ADDLN, DIS_B_ADD1, DIS_B_ADD2);

-- 地號重複檢查索引
CREATE INDEX idx_ibmcslan_land_duplicate ON IBMCSLAN 
(LAN_DIST, LAN_BNID, LAN_LNID, LAN_SNID);

-- 複合查詢最佳化
SELECT CASE_ID, DIS_B_ADDZON, DIS_B_ADDRD 
FROM IBMCASE 
WHERE (DIS_B_ADDZON, DIS_B_ADDRD) IN (
    SELECT DISTINCT DIS_B_ADDZON, DIS_B_ADDRD 
    FROM IBMCASE 
    GROUP BY DIS_B_ADDZON, DIS_B_ADDRD 
    HAVING COUNT(*) > 1
);
```

## 10. 安全性考量

### 10.1 輸入驗證安全

#### SQL注入防護
```java
public boolean isSecureInput(String input) {
    // 檢查SQL注入關鍵字
    String[] sqlKeywords = {"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "UNION", "SCRIPT"};
    String upperInput = input.toUpperCase();
    
    for (String keyword : sqlKeywords) {
        if (upperInput.contains(keyword)) {
            return false;
        }
    }
    
    // 檢查特殊字符
    return !input.matches(".*[<>\"';&|*].*");
}

// 使用參數化查詢
PreparedStatement ps = connection.prepareStatement(
    "SELECT COUNT(*) FROM IBMCASE WHERE DIS_B_ADDZON = ? AND DIS_B_ADDRD = ?"
);
ps.setString(1, dis_b_addzon);
ps.setString(2, dis_b_addrd);
```

#### XSS攻擊防護
```java
public String sanitizeInput(String input) {
    if (input == null) return "";
    
    return input
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace("\"", "&quot;")
        .replace("'", "&#x27;")
        .replace("/", "&#x2F;");
}
```

### 10.2 驗證規則安全性

#### 正規表達式安全
```javascript
// 避免ReDoS攻擊的安全正規表達式
var safePatterns = {
    // 原始：^(a+)+$  (危險)
    // 安全：^a+$     (安全)
    repeatedChar: /^a+$/,
    
    // 原始：^([a-zA-Z]+)*$  (危險)  
    // 安全：^[a-zA-Z]*$     (安全)
    alphabets: /^[a-zA-Z]*$/,
    
    // 台灣地址安全模式
    taiwanAddress: /^[\u4e00-\u9fa5\d\s\-()]{1,100}$/
};
```

## 11. 測試與品質保證

### 11.1 驗證測試策略

#### 單元測試範例
```java
@Test
public void testAddressValidation() {
    // 測試有效地址
    assertTrue(validateAddress("新北市板橋區中山路100號"));
    
    // 測試無效地址
    assertFalse(validateAddress(""));
    assertFalse(validateAddress("Invalid<script>alert('xss')</script>"));
    
    // 測試邊界條件
    assertFalse(validateAddress("A".repeat(101))); // 超過長度限制
}

@Test  
public void testDuplicateCheck() {
    // 模擬重複地址
    when(mockDB.checkDuplicate(any())).thenReturn(2);
    
    ValidationResult result = validator.checkAddressDuplicate(testAddress);
    assertTrue(result.hasWarnings());
    assertEquals("發現重複地址", result.getWarningMessage());
}
```

#### 整合測試
```javascript
describe('表單驗證整合測試', function() {
    beforeEach(function() {
        // 設定測試環境
        setupTestForm();
    });
    
    it('應該在提交時執行完整驗證流程', function() {
        // 填入測試資料
        fillTestData();
        
        // 提交表單
        submitForm();
        
        // 驗證結果
        expect(getValidationErrors()).toEqual([]);
    });
    
    it('應該正確處理重複檢查', function() {
        // 模擬重複資料
        mockDuplicateResponse(2);
        
        // 觸發重複檢查
        triggerDuplicateCheck();
        
        // 驗證警告訊息
        expect(getWarningMessage()).toContain('發現重複');
    });
});
```

## 總結

### 系統驗證機制評估

#### 優點
1. **完整的三層驗證架構** - 客戶端、配置、伺服器端全覆蓋
2. **即時重複檢查機制** - 有效避免重複登記
3. **靈活的配置驗證** - XML配置支援快速調整規則
4. **完整的錯誤處理** - 多層級錯誤捕獲和處理

#### 改進空間
1. **驗證規則分散** - 規則散布在多個檔案中，維護困難
2. **錯誤訊息不友善** - 多為英文錯誤訊息，使用者體驗不佳
3. **效能優化需求** - 重複檢查缺乏快取機制
4. **安全性加強** - 需要更嚴格的輸入過濾和XSS防護

#### 建議改進方向
1. **建立統一驗證管理** - 集中管理所有驗證規則
2. **改善使用者體驗** - 提供中文化、更友善的錯誤訊息
3. **效能最佳化** - 實作快取機制和批次驗證
4. **安全性強化** - 加強輸入過濾和安全性檢查

新北市違章建築管理系統的查報資料驗證機制雖然功能完整，但在現代化需求下仍有改進空間。建議在未來的系統升級中，優先考慮統一驗證管理和使用者體驗改善。

---

**文件狀態**: ✅ 已完成  
**下一步**: 執行 T2.2.1 現場勘查流程分析