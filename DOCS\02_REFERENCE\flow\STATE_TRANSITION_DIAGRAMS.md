# 狀態碼轉換視覺化圖表

## 📋 文件資訊
- **文件編號**: T2.8.2-附件
- **文件目的**: 提供狀態碼轉換的視覺化圖表
- **建立日期**: 2025-07-05
- **負責單位**: 【D】Claude Code - DevOps與技術架構任務組

---

## 🔄 一般違建狀態流程圖

```mermaid
graph TD
    Start[開始] --> A231[231<br/>一般認定辦理中]
    
    A231 -->|submit| A232[232<br/>一般認定陳核中]
    A231 -->|synergy| A234[234<br/>認定送協同作業]
    A231 -->|delete| Delete1[刪除案件]
    
    A232 -->|approve| A239[239<br/>一般認定已簽准]
    A232 -->|withdraw| A231
    
    A234 -->|complete| A23b[23b<br/>協同作業完成]
    A23b --> A239
    A23b -->|delete| Delete2[刪除案件]
    
    A239 -->|assign dept| A230[230<br/>一般認定分案完成]
    A239 -->|to demolition| A321[321<br/>一般排拆分案完成]
    
    A321 --> A362[362<br/>一般排拆陳核中]
    A362 -->|approve| A364[364<br/>一般排拆辦理中]
    A362 -->|withdraw| A364
    
    A364 --> A369[369<br/>一般排拆已簽准]
    
    A369 -->|to closure| A461[461<br/>一般結案辦理中]
    
    A461 --> A462[462<br/>一般結案陳核中]
    A462 -->|approve| A469[469<br/>一般結案已簽准]
    A462 -->|withdraw| A461
    
    A469 -->|finalize| A460[460<br/>一般結案]
    A460 --> End[結束]
    
    style A231 fill:#f9f,stroke:#333,stroke-width:2px
    style A321 fill:#9ff,stroke:#333,stroke-width:2px
    style A460 fill:#9f9,stroke:#333,stroke-width:2px
```

---

## 🔄 廣告違建狀態流程圖

```mermaid
graph TD
    Start[開始] --> B241[241<br/>廣告認定辦理中]
    
    B241 -->|synergy| B244[244<br/>認定送協同作業]
    
    B244 -->|complete| B24b[24b<br/>協同作業完成]
    
    B24b --> B240[240<br/>廣告認定日期登錄]
    
    B240 -->|register| B24e[24e<br/>廣告送達日期登錄]
    B240 -->|to demolition| B342[342<br/>廣告認定/排拆陳核中]
    
    B24e --> B24f[24f<br/>廣告認定號碼登錄]
    
    B342 -->|approve| B344[344<br/>廣告排拆辦理中]
    B342 -->|withdraw| B344
    B342 -->|final approve| B349[349<br/>廣告認定/排拆已簽准]
    
    B344 -->|resubmit| B342
    
    B349 -->|assign dept| B240
    B349 -->|to closure| B441[441<br/>廣告結案辦理中]
    
    B441 --> B442[442<br/>廣告結案陳核中]
    B442 -->|approve| B449[449<br/>廣告結案已簽准]
    B442 -->|withdraw| B441
    
    B449 -->|finalize| B440[440<br/>廣告結案]
    B440 --> End[結束]
    
    style B241 fill:#f9f,stroke:#333,stroke-width:2px
    style B342 fill:#9ff,stroke:#333,stroke-width:2px
    style B440 fill:#9f9,stroke:#333,stroke-width:2px
```

---

## 🔄 下水道違建狀態流程圖

```mermaid
graph TD
    Start[開始] --> C251[251<br/>下水道認定辦理中]
    
    C251 -->|submit| C252[252<br/>下水道認定陳核中]
    
    C252 -->|synergy| C254[254<br/>認定送協同作業]
    C252 -->|withdraw| C251
    
    C254 -->|complete| C25b[25b<br/>協同作業完成]
    
    C25b --> C259[259<br/>下水道認定已簽准]
    
    C259 -->|assign dept| C250[250<br/>下水道認定分案完成]
    C259 -->|register| C25e[25e<br/>下水道送達日期登錄]
    C259 -->|to demolition| C352[352<br/>下水道排拆陳核中]
    
    C25e --> C25f[25f<br/>下水道認定號碼登錄]
    
    C352 -->|approve| C354[354<br/>下水道排拆辦理中]
    C352 -->|withdraw| C354
    
    C354 --> C359[359<br/>下水道排拆已簽准]
    
    C359 -->|to closure| C451[451<br/>下水道結案辦理中]
    
    C451 --> C452[452<br/>下水道結案陳核中]
    C452 -->|approve| C459[459<br/>下水道結案已簽准]
    C452 -->|withdraw| C451
    
    C459 -->|finalize| C450[450<br/>下水道結案]
    C450 --> End[結束]
    
    style C251 fill:#f9f,stroke:#333,stroke-width:2px
    style C352 fill:#9ff,stroke:#333,stroke-width:2px
    style C450 fill:#9f9,stroke:#333,stroke-width:2px
```

---

## 🔀 跨部門協同流程

```mermaid
graph LR
    subgraph 認定階段
        D231[231 一般違建]
        D241[241 廣告違建]
        D251[251 下水道違建]
    end
    
    subgraph 協同作業
        E234[234 一般協同]
        E244[244 廣告協同]
        E254[254 下水道協同]
    end
    
    subgraph 處理部門
        F01[認定科]
        F04[廣告科]
        F03[勞安科]
    end
    
    D231 -->|送協同| E234
    D241 -->|送協同| E244
    D251 -->|送協同| E254
    
    E234 -.->|處理| F01
    E244 -.->|處理| F04
    E254 -.->|處理| F03
    
    style E234 fill:#ffd,stroke:#333,stroke-width:2px
    style E244 fill:#ffd,stroke:#333,stroke-width:2px
    style E254 fill:#ffd,stroke:#333,stroke-width:2px
```

---

## 📊 狀態碼結構解析

```mermaid
graph TD
    A[狀態碼 XYZ]
    
    A --> B[X: 階段代碼]
    A --> C[Y: 部門/類型]
    A --> D[Z: 子狀態]
    
    B --> B1[2: 認定階段]
    B --> B2[3: 排拆階段]
    B --> B3[4: 結案階段]
    B --> B4[9: 品質控制]
    
    C --> C1[3/6: 一般違建]
    C --> C2[4: 廣告違建]
    C --> C3[5: 下水道違建]
    
    D --> D1[0: 完成]
    D --> D2[1: 開始/辦理中]
    D --> D3[2: 陳核中]
    D --> D4[4: 協同/辦理中]
    D --> D5[9: 已簽准]
    D --> D6[b: 協同完成]
    D --> D7[c: 繕校]
    D --> D8[d: 撤銷]
    D --> D9[e: 送達登錄]
    D --> D10[f: 號碼登錄]
```

---

## 🚫 無效轉換示例

```mermaid
graph TD
    subgraph 無效的跨階段轉換
        X231[231 認定階段] -.->|❌| X460[460 結案階段]
        X251[251 認定階段] -.->|❌| X359[359 排拆階段]
    end
    
    subgraph 無效的跨類型轉換
        Y231[231 一般違建] -.->|❌| Y244[244 廣告協同]
        Y251[251 下水道] -.->|❌| Y349[349 廣告排拆]
    end
    
    subgraph 無效的逆向轉換
        Z239[239 已簽准] -.->|❌| Z231[231 辦理中]
        Z460[460 結案] -.->|❌| Z321[321 排拆分案]
    end
    
    style X231 fill:#fcc,stroke:#f00,stroke-width:2px
    style Y231 fill:#fcc,stroke:#f00,stroke-width:2px
    style Z239 fill:#fcc,stroke:#f00,stroke-width:2px
```

---

## 📝 圖例說明

- **粉色節點**：認定階段起始點
- **藍色節點**：排拆階段關鍵點
- **綠色節點**：結案完成狀態
- **黃色節點**：協同作業狀態
- **實線箭頭**：正常流程轉換
- **虛線箭頭**：部門處理關聯
- **紅色虛線**：無效/禁止的轉換

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*配合 STATUS_CODE_TRANSITION_MATRIX.md 使用*
*完成日期：2025-07-05*