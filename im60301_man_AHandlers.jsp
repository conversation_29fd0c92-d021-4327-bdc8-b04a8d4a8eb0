<%@page pageEncoding="utf-8"%><%@page import="com.ezek.utils.EzekUtils"%>

<%--== Handlers ==--%> <%--im60301_man_A Opening Initialization directive @1-A0E75F14--%><%!

// //Workaround for JRun 3.1 @1-F81417CB

private final String CONNECTION_NAME = "DBConn";
				
private final HashMap<String, String> TEAR_DOWN_SITUATIONS = new HashMap<String, String>() {{
	// 一般
	put("A-01", "上列違章建築，業經本隊於 民國[b_finish_date] 依法拆除完畢，同意銷案。");
	put("A-02", "上列違章建築，業經本隊於 民國[end_chk_date] 派員勘查，已達不堪使用標準，同意銷案。");
	put("A-03", "上列違章建築，業經本隊於 民國[end_chk_date] 派員勘查，已符合「新北市合法建物增設一定規模以下構造物處理要點」之規定，同意銷案。");
	put("A-04", "上列違章建築，業經本隊於 民國[end_chk_date] 派員勘查，現場已不存在，同意銷案。");
	put("A-05", "上列違章建築，已依法申請補辦建造執照手續，並核發建造執照在案，同意銷案。");
	put("A-06", "上列違章建築，業經本隊以 民國[revoke_date] [revoke_word]字第 [revoke_num] 號函撤銷違建認定處分，同意銷案。");
	put("A-07", "上列違章建築，業經本隊於 民國[end_chk_date] 勘查確認符合專案解列標準，全案移拍照建檔列管。");
	// 廣告
	put("B-01", "上列違章建築，業經本大隊於 民國[b_finish_date] 依法拆除完畢，同意銷案。");
	put("B-02", "上列違章建築，業經本大隊於 民國[end_chk_date] 派員勘查 ，已([end_way_memo])，同意銷案。");
	put("B-03", "上列違章建築，業經本大隊於 民國[end_chk_date] 派員勘查，現場已不存在（恢復原狀），同意銷案。");
	put("B-04", "上列違章建築，已依法申請審查許可，並核發 [licence_word]字第 [licence_no] 號（[licence_kind]）在案，同意銷案。");
	put("B-05", "上列違章建築，業經本大隊以 民國[revoke_date] [revoke_word]字第 [revoke_num] 號函撤銷違建認定處分，同意銷案。");
	// 下水道
	put("C-01", "上列違章建築，業經本大隊於 民國[b_finish_date] 依法拆除完畢，同意銷案。");
	put("C-02", "上列違章建築，已依法申請補辦建造執照手續，並核發 [end_lic_word]字第 [end_lic_num] 號建造執照在案，同意銷案。");
	put("C-03", "上列違章建築，業經本大隊以 民國[revoke_date] [revoke_word]字第 [revoke_num] 號函撤銷違建認定處分，同意銷案。");
	put("C-04", "上列違章建築，業經本大隊會同工程主辦機關及監造(施工)單位共同確認，本案污水下水道用戶接管工程所需施作空間已足夠。");
	put("C-05", "上列違章建築，業經本大隊會同工程主辦機關及監造(施工)單位共同確認，該污水下水道用戶接管工程已完工或完成驗收(計價)程序。");
	put("C-06", "上列違章建築，業經水利局設施維護單位確認，拆除後已無影響污水下水道用戶排水維護之困難。");
	put("C-07", "其他。");
}};
private final HashMap<String, String> END_WAY_MEMO = new HashMap<String, String>() {{
	put("01", "改善至雜項執照規模以下");
	put("02", "自行改善");
	put("03", "標的變異，另行處分");
}};
private final HashMap<String, String> LICENCE_KIND = new HashMap<String, String>() {{
	put("01", "建造執照");
	put("02", "雜項執照");
	put("03", "許可證");
}};

private String getTearDownSituation(String ib_prcs, String b_end_item, String b_finish_date, String end_chk_date, String revoke_date, String revoke_word, String revoke_num, String end_way_memo, String licence_word, String licence_no, String licence_kind, String end_lic_word, String end_lic_num) {
	String keyForTearDownSituation = ib_prcs + "-" + b_end_item;
	String tearDownSituation = TEAR_DOWN_SITUATIONS.get(keyForTearDownSituation);
	
	if (!StringUtils.isEmpty(tearDownSituation)) {
		if ("A".equals(ib_prcs)) { // 一般
			if ("01".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[b_finish_date]", EzekUtils.formatDate(b_finish_date, "YYYMMDD", "MANDARIN"));
			} else if ("06".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_date]", EzekUtils.formatDate(revoke_date, "YYYMMDD", "MANDARIN"));
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_word]", revoke_word);
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_num]", revoke_num);
			} else if ("02,03,04,07".indexOf(b_end_item) > -1) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_chk_date]", EzekUtils.formatDate(end_chk_date, "YYYMMDD", "MANDARIN"));
			}
		} else if ("B".equals(ib_prcs)) { // 廣告
			if ("01".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[b_finish_date]", EzekUtils.formatDate(b_finish_date, "YYYMMDD", "MANDARIN"));
			} else if ("02".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_chk_date]", EzekUtils.formatDate(end_chk_date, "YYYMMDD", "MANDARIN"));
				
				String end_way_memo_desc = END_WAY_MEMO.get(end_way_memo);
				if (StringUtils.isEmpty(end_way_memo_desc)) {
					end_way_memo_desc = "";
				}
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_way_memo]", end_way_memo_desc);
			} else if ("03".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_chk_date]", EzekUtils.formatDate(end_chk_date, "YYYMMDD", "MANDARIN"));
			} else if ("04".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[licence_word]", licence_word);
				tearDownSituation = StringUtils.replace(tearDownSituation, "[licence_no]", licence_no);
				
				String licence_kind_desc = LICENCE_KIND.get(licence_kind);
				if (StringUtils.isEmpty(licence_kind_desc)) {
					licence_kind_desc = "";
				}
				tearDownSituation = StringUtils.replace(tearDownSituation, "[licence_kind]", licence_kind_desc);
			} else if ("05".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_date]", EzekUtils.formatDate(revoke_date, "YYYMMDD", "MANDARIN"));
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_word]", revoke_word);
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_num]", revoke_num);
			}
		} else if ("C".equals(ib_prcs)) { // 下水道
			if ("01".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[b_finish_date]", EzekUtils.formatDate(b_finish_date, "YYYMMDD", "MANDARIN"));
			} else if ("02".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_lic_word]", end_lic_word);
				tearDownSituation = StringUtils.replace(tearDownSituation, "[end_lic_num]", end_lic_num);
			} else if ("03".equals(b_end_item)) {
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_date]", EzekUtils.formatDate(revoke_date, "YYYMMDD", "MANDARIN"));
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_word]", revoke_word);
				tearDownSituation = StringUtils.replace(tearDownSituation, "[revoke_num]", revoke_num);
			}
		}
	} // End of IF: (!StringUtils.isEmpty(tearDownSituation))
	
	return tearDownSituation;
}

private String getViolatorInfo(String case_id) {
	String result = "無";
	
	JDBCConnection jdbcConn = null;
	Enumeration dataRows = null;
	DbRow dataRow = null;
	
	String sql = "";
	String usr_knd = "", ib_user = "", usr_id = "", usr_add = "";
	String usr_knd_name = "";
	StringBuffer html = null;
	
	try {
		// Obtain a database connection from the pool
		jdbcConn = JDBCConnectionFactory.getJDBCConnection(CONNECTION_NAME);
		
		html = new StringBuffer();
		
		sql = "SELECT usr_knd, ib_user, usr_id, usr_add";
		sql += " FROM ibmdisnm";
		sql += " WHERE case_id = '" + case_id + "'";
		sql += " ORDER BY case_seq";
		
		dataRows = jdbcConn.getRows(sql);
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			usr_knd = Utils.convertToString(dataRow.get("usr_knd"));
			ib_user = Utils.convertToString(dataRow.get("ib_user"));
			usr_id = Utils.convertToString(dataRow.get("usr_id"));
			usr_add = Utils.convertToString(dataRow.get("usr_add"));
			
			if ("1".equals(usr_knd)) {
				usr_knd_name = "自然人";
			} else if ("2".equals(usr_knd)) {
				usr_knd_name = "法人";
			}
			ib_user = (StringUtils.isEmpty(ib_user) ? "" : ib_user);
			usr_id = (StringUtils.isEmpty(usr_id) ? "" : usr_id);
			usr_add = (StringUtils.isEmpty(usr_add) ? "" : usr_add);
			
			// Row 1
			html.append("<tr class=\"Controls\">");
			html.append("<td class=\"th_violatorTable\"><label>違建人身分</label>&nbsp;</td>");
			html.append("<td colspan=\"5\"><label>").append(usr_knd_name).append("</label></td>");
			html.append("</tr>");
			// Row 2
			html.append("<tr class=\"Controls\">");
			html.append("<td class=\"th_violatorTable th_topCntnt col-1\"><label>違建人姓名</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt col-2\"><label>").append(ib_user).append("</label></td>");
			html.append("<td class=\"th_violatorTable th_topCntnt col-3\"><label>違建人身分號碼</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt col-4\"><label>").append(usr_id).append("</label></td>");
			html.append("<td class=\"th_violatorTable th_topCntnt col-5\" ><label>違建人地址</label>&nbsp;</td>");
			html.append("<td class=\"th_topCntnt\"><label>").append(usr_add).append("</label></td>");
			html.append("</tr>");
			
			// Reset for the next iteration
			usr_knd_name = "";
		}
	} catch (Exception e) {
		System.err.println("im60301_man_CHandlers ::: getViolatorsInfo ::: Exception");
		e.printStackTrace();
	} finally {
		// Release the database connection back to the pool
		if (jdbcConn != null) {
			jdbcConn.closeConnection();
		}
		
		if (html.length() > 0) {
			result = "<a class=\"link-switch\" id=\"violator_table_switch\" data-toggle=\"collapse\" href=\"#violator_content\" aria-expanded=\"false\">瀏覽</a>";
			result += "<div id=\"violator_content\" class=\"collapse violator-content blend-in-bg\">";
			result += "<table class=\"table violatorTable\" cellspacing=\"0\" cellpadding=\"0\">" + html.toString() + "</table>";
			result += "</div>";
		}
		
		// Release the memory resource immediately
		html = null;
	}
	
	return result;
}

private HashMap<String, String> getFiles(String case_id) {
	HashMap<String, String> result = null;
	
	JDBCConnection jdbcConn = null;
	Enumeration dataRows = null;
	DbRow dataRow = null;
	
	String sql = "";
	String pic_kind = "", pic_seq = "", picname = "", filename = "", showname = "";
	String extract_picname = "", fileExtention = "";
	boolean isImage = false;
	
	StringBuffer case_photo = null, case_attm = null, result_photo = null, result_attm = null;
	
	try {
		result = new HashMap<String, String>();
		// 案件基本資料
		result.put("case_photo", "無");
		result.put("case_attm", "無");
		// 拆除結果
		result.put("result_photo", "無");
		result.put("result_attm", "無");
		
		case_photo = new StringBuffer();
		case_attm = new StringBuffer();
		result_photo = new StringBuffer();
		result_attm = new StringBuffer();
		
		// Obtain a database connection from the pool
		jdbcConn = JDBCConnectionFactory.getJDBCConnection(CONNECTION_NAME);
		
		sql = "SELECT pic_kind, pic_seq, picname, filename, showname";
		sql += " FROM ibmlist";
		sql += " WHERE systid = 'IBM'";
		sql += " AND case_id = '" + case_id + "'";
		sql += " ORDER BY pic_kind, pic_seq";
		
		dataRows = jdbcConn.getRows(sql);
		while (dataRows != null && dataRows.hasMoreElements()) {
			dataRow = (DbRow)dataRows.nextElement();
			
			pic_kind = Utils.convertToString(dataRow.get("pic_kind"));
			pic_seq = Utils.convertToString(dataRow.get("pic_seq"));
			picname = Utils.convertToString(dataRow.get("picname"));
			filename = Utils.convertToString(dataRow.get("filename"));
			showname = Utils.convertToString(dataRow.get("showname"));
			
			// Extract the name after "_"
			extract_picname = picname.substring(picname.lastIndexOf("_") + 1);
			// Find the file extention
			fileExtention = filename.substring(filename.lastIndexOf(".") + 1);
			if ("jpg,png".indexOf(fileExtention.toLowerCase()) > -1) {
				isImage = true;
			}
			
			if ("GEO".indexOf(pic_kind) > -1) {
				case_photo.append( composeHTML_II(case_id, pic_kind, pic_seq, isImage, extract_picname) );
			} else if ("ANO,ANO_TG".indexOf(pic_kind) > -1) {
				System.err.println("im60301_man_AHandlers ::: getFiles ::: " + pic_kind);
				case_attm.append( composeHTML(case_id, pic_kind, pic_seq, isImage, showname) );
			} else if (pic_kind.equals("NOW_END")) {
				result_photo.append( composeHTML_II(case_id, pic_kind, pic_seq, isImage, extract_picname) );
			} else if (pic_kind.equals("ANO_END")) {
				result_attm.append( composeHTML(case_id, pic_kind, pic_seq, isImage, showname) );
			}
			
			// Reset for the next iteration
			extract_picname = "";
			fileExtention = "";
			isImage = false;
		}
	} catch (Exception e) {
		System.err.println("im60301_man_CHandlers ::: getFiles ::: Exception");
		e.printStackTrace();
	} finally {
		// Release the database connection back to the pool
		if (jdbcConn != null) {
			jdbcConn.closeConnection();
		}
		
		if (case_photo.length() > 0) {
			result.put("case_photo", "<a class=\"link-switch\" id=\"case_photo_switch\" data-toggle=\"collapse\" href=\"#case_photo_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"case_photo_content\" class=\"collapse ul-file\">" + case_photo.toString() + "</ul>");
		}
		if (case_attm.length() > 0) {
			result.put("case_attm", "<a class=\"link-switch\" id=\"case_attm_switch\" data-toggle=\"collapse\" href=\"#case_attm_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"case_attm_content\" class=\"collapse ul-file\">" + case_attm.toString() + "</ul>");
		}
		if (result_photo.length() > 0) {
			result.put("result_photo", "<a class=\"link-switch\" id=\"result_photo_switch\" data-toggle=\"collapse\" href=\"#result_photo_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"result_photo_content\" class=\"collapse ul-file\">" + result_photo.toString() + "</ul>");
		}
		if (result_attm.length() > 0) {
			result.put("result_attm", "<a class=\"link-switch\" id=\"result_attm_switch\" data-toggle=\"collapse\" href=\"#result_attm_content\" aria-expanded=\"false\">瀏覽</a><ul id=\"result_attm_content\" class=\"collapse ul-file\">" + result_attm.toString() + "</ul>");
		}
		
		// Release the memory resource immediately
		case_photo = null;
		case_attm = null;
		result_photo = null;
		result_attm = null;
	}
	
	return result;
}

private String composeHTML(String case_id, String pic_kind, String pic_seq, boolean isImage, String name) {
	String html = "";
	String divOnclick = "window.open('in10101_getImage.jsp?EXP_NO=" + case_id + "&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis() + "');";
	String imgSrc = "img/PDF.jpg";
	
	if (isImage) {
		divOnclick = "showPopup('" + case_id + "', '" + pic_kind + "', '" + pic_seq + "');";
		imgSrc = "in10101_getImage.jsp?EXP_NO=" + case_id + "&Zzip=Y&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis();
	}
	
	html = "<div class=\"col-xs-6 col-sm-3 col-md-3 col-lg-2 text-center div-frame\">";
	html += "<li>";
	html += "<div class=\"vertical-container ht-200px\" role=\"button\" onclick=\"[onclick]\">";
	html += "<img class=\"img-150x200\" src=\"[src]\" alt=\"" + name + "\">";
	html += "</div>";
	html += "<div><label>" + name + "</label></div>";
	html += "</li>";
	html += "</div>";
	
	html = StringUtils.replace(html, "[onclick]", divOnclick);
	html = StringUtils.replace(html, "[src]", imgSrc);
	
	return html;
}

private String composeHTML_II(String case_id, String pic_kind, String pic_seq, boolean isImage, String name) {
	String html = "";
	String imgSrc = "img/PDF.jpg";
	
	if (isImage) {
		imgSrc = "in10101_getImage.jsp?EXP_NO=" + case_id + "&Zzip=Y&Img_kind=" + pic_kind + "&Img_index=" + pic_seq + "&t=" + System.currentTimeMillis();
	}
	
	html = "<div class=\"col-xs-6 col-sm-3 col-md-3 col-lg-2 text-center div-frame\">";
	html += "<li>";
	html += "<div class=\"vertical-container ht-200px\" role=\"button\" onclick=\"[onclick]\">";
	html += "<img class=\"img-150x200\" data-original=\"[src]\" src=\"[src]\" alt=\"" + name + "\">";
	html += "</div>";
	html += "<div><label>" + name + "</label></div>";
	html += "</li>";
	html += "</div>";
	
	html = StringUtils.replace(html, "[src]", imgSrc);
	
	return html;
}
private String styleDate(String rawStr) {
	String dateStyle = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		dateStyle = EzekUtils.formatDate(rawStr, "YYYMMDD", "/");
	}
	
	return dateStyle;
}
private String styleDateMain(String rawStr) {
	String dateStyle = "";
	
	if (!StringUtils.isEmpty(rawStr)) {
		dateStyle = EzekUtils.formatDate(rawStr, "YYYMMDD", "MANDARIN");
	}
	
	return dateStyle;
}
private String composeOfficialDocumentPrefix(String ib_prcs) {
	String officialDocumentPrefix = "";
	
	if (!StringUtils.isEmpty(ib_prcs)) {
		if (ib_prcs.startsWith("A")) { // 拆除一科, 二科
			officialDocumentPrefix = "新北拆拆一;新北拆拆一;新北拆拆二;新北拆拆二";
		} else if (ib_prcs.startsWith("C")) { // 勞安科
			officialDocumentPrefix = "新北拆勞;新北拆勞";
		} else if (ib_prcs.startsWith("B")) { // 廣告科
			officialDocumentPrefix = "新北拆廣;新北拆廣";
		}
	}
	
	return officialDocumentPrefix;
}

//Feature checker Head @1-A996CE9E
    public class im60301_man_AServiceChecker implements com.codecharge.feature.IServiceChecker {
//End Feature checker Head

//feature binding @1-6DADF1A6
        public boolean check ( HttpServletRequest request, HttpServletResponse response, ServletContext context) {
            String attr = "" + request.getParameter("callbackControl");
            return false;
        }
//End feature binding

//Feature checker Tail @1-FCB6E20C
    }
//End Feature checker Tail

//im60301_man_A Page Handler Head @1-BBE8E716
    public class im60301_man_APageHandler implements PageListener {
//End im60301_man_A Page Handler Head

//im60301_man_A BeforeInitialize Method Head @1-4C73EADA
        public void beforeInitialize(Event e) {
//End im60301_man_A BeforeInitialize Method Head

//im60301_man_A BeforeInitialize Method Tail @1-FCB6E20C
        }
//End im60301_man_A BeforeInitialize Method Tail

//im60301_man_A AfterInitialize Method Head @1-89E84600
        public void afterInitialize(Event e) {
//End im60301_man_A AfterInitialize Method Head

//Event AfterInitialize Action Validate onTimeout_Synct @269-4B8A4259
          if (SessionStorage.getInstance(e.getPage().getRequest()).getAttributeAsString("LoginPass") != "True")
            e.getPage().setRedirectString("timeout_err.jsp");
//End Event AfterInitialize Action Validate onTimeout_Synct

//Event AfterInitialize Action Custom Code @277-44795B7A




			String JOB_TITLE = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("JOB_TITLE"));
			String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
			String case_id = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("case_id"));

			long ibmfym_MAX_SEQ = Utils.convertToLong(DBTools.dLookUp("COUNT(*)", "IBMFYM", " case_id = '"+case_id+"' and acc_rlt = '92c'  ", "DBConn")).longValue();
			long ano_chk = Utils.convertToLong(DBTools.dLookUp(" COALESCE(max(PIC_SEQ), 1) ", "IBMLIST", " case_id = '"+case_id+"' and  PIC_KIND='ANO_UPD' ", "DBConn")).longValue();
			
			
			String SQL_T = "" , ACC_RLT  =""; ;
			
			
			JDBCConnection jdbcConn = null;
			DbRow singleRowData = null;
			DbRow dataRow = null;
			Enumeration dataRows = null; 
			
			
			//System.err.println(" im60301_man_C  AfterInitialize  ck1=" + ano_chk + " ibmfym_MAX_SEQ" + ibmfym_MAX_SEQ);
			if(!StringUtils.isEmpty(case_id) && ano_chk > ibmfym_MAX_SEQ ){
				try{
					jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");

					SQL_T = "DELETE FROM ibmlist where case_id='"+case_id+"' and PIC_KIND='ANO_UPD' and PIC_SEQ="+ano_chk+" ";
					SQL_T += "";
					jdbcConn.executeUpdate(SQL_T);

				}catch (Exception localException){
					System.err.println("im60301_man_C : error is " + localException.toString());
					
				}finally{
					if( jdbcConn != null )jdbcConn.closeConnection();
				}	
			}
			
			

//End Event AfterInitialize Action Custom Code

//im60301_man_A AfterInitialize Method Tail @1-FCB6E20C
        }
//End im60301_man_A AfterInitialize Method Tail

//im60301_man_A OnInitializeView Method Head @1-E3C15E0F
        public void onInitializeView(Event e) {
//End im60301_man_A OnInitializeView Method Head

//im60301_man_A OnInitializeView Method Tail @1-FCB6E20C
        }
//End im60301_man_A OnInitializeView Method Tail

//im60301_man_A BeforeShow Method Head @1-46046458
        public void beforeShow(Event e) {
//End im60301_man_A BeforeShow Method Head

//im60301_man_A BeforeShow Method Tail @1-FCB6E20C
        }
//End im60301_man_A BeforeShow Method Tail

//im60301_man_A BeforeOutput Method Head @1-BE3571C7
        public void beforeOutput(Event e) {
//End im60301_man_A BeforeOutput Method Head

//im60301_man_A BeforeOutput Method Tail @1-FCB6E20C
        }
//End im60301_man_A BeforeOutput Method Tail

//im60301_man_A BeforeUnload Method Head @1-1DDBA584
        public void beforeUnload(Event e) {
//End im60301_man_A BeforeUnload Method Head

//im60301_man_A BeforeUnload Method Tail @1-FCB6E20C
        }
//End im60301_man_A BeforeUnload Method Tail

//im60301_man_A onCache Method Head @1-7A88A4B8
        public void onCache(CacheEvent e) {
//End im60301_man_A onCache Method Head

//get cachedItem @1-F7EFE9F6
            if (e.getCacheOperation() == ICache.OPERATION_GET) {
//End get cachedItem

//custom code before get cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before get cachedItem

//put cachedItem @1-FD2D76DE
            } else if (e.getCacheOperation() == ICache.OPERATION_PUT) {
//End put cachedItem

//custom code before put cachedItem @1-E3CE2760
                /* Write your own code here */
//End custom code before put cachedItem

//if tail @1-FCB6E20C
            }
//End if tail

//im60301_man_A onCache Method Tail @1-FCB6E20C
        }
//End im60301_man_A onCache Method Tail

//im60301_man_A Page Handler Tail @1-FCB6E20C
    }
//End im60301_man_A Page Handler Tail

//ibmcase Record Handler Head @2-FF6C959A
    public class im60301_man_AibmcaseRecordHandler implements RecordListener, RecordDataObjectListener {
//End ibmcase Record Handler Head

//ibmcase afterInitialize Method Head @2-89E84600
        public void afterInitialize(Event e) {
//End ibmcase afterInitialize Method Head

//ibmcase afterInitialize Method Tail @2-FCB6E20C
        }
//End ibmcase afterInitialize Method Tail

//ibmcase OnSetDataSource Method Head @2-9B7FBFCF
        public void onSetDataSource(DataObjectEvent e) {
//End ibmcase OnSetDataSource Method Head

//ibmcase OnSetDataSource Method Tail @2-FCB6E20C
        }
//End ibmcase OnSetDataSource Method Tail

//ibmcase BeforeShow Method Head @2-46046458
        public void beforeShow(Event e) {
//End ibmcase BeforeShow Method Head

//Event BeforeShow Action Custom Code @27-44795B7A

			//String UNIT_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UNIT_ID"));
			String UserID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("UserID"));
			String acc_rlt_now  ="", prcsTyp = "";
			String case_id = Utils.convertToString(e.getRecord().getControl("case_id").getValue());
			String reg_emp = Utils.convertToString(e.getRecord().getControl("reg_emp").getValue());
			String reg_yy = Utils.convertToString(e.getRecord().getControl("edit_reg_yy").getValue());
			String reg_no = Utils.convertToString(e.getRecord().getControl("edit_reg_no").getValue());
			String EMPNAME = Utils.convertToString(DBTools.dLookUp("EMPNAME", "IBMUSER", "EMPNO = '"+reg_emp+"'", "DBConn"));
			String reg_date = Utils.convertToString(e.getRecord().getControl("reg_date").getValue());
			String sd_date = Utils.convertToString(e.getRecord().getControl("sd_date").getValue());
			String rvldate = Utils.convertToString(e.getRecord().getControl("rvldate").getValue());
			String ib_prcs = Utils.convertToString(e.getRecord().getControl("ib_prcs").getValue());
			String dis_type = Utils.convertToString(e.getRecord().getControl("dis_type").getValue());
			String dis_sort = Utils.convertToString(e.getRecord().getControl("dis_sort").getValue());
			String pre_dis_date = Utils.convertToString(e.getRecord().getControl("pre_dis_date").getValue());
			String dis_notice_date = Utils.convertToString(e.getRecord().getControl("dis_notice_date").getValue());
			String dis_reg_yy = Utils.convertToString(e.getRecord().getControl("dis_reg_yy").getValue());
			String dis_reg_no = Utils.convertToString(e.getRecord().getControl("dis_reg_no").getValue());
			String b_notice_date = Utils.convertToString(e.getRecord().getControl("b_notice_date").getValue());
			// Start 拆除情形 fields
			String b_end_item = Utils.convertToString(e.getRecord().getControl("b_end_item").getValue());
			String b_finish_date = Utils.convertToString(e.getRecord().getControl("b_finish_date").getValue());
			String end_chk_date = Utils.convertToString(e.getRecord().getControl("end_chk_date").getValue());
			String revoke_date = Utils.convertToString(e.getRecord().getControl("revoke_date").getValue());
			String revoke_word = Utils.convertToString(e.getRecord().getControl("revoke_word").getValue());
			String revoke_num = Utils.convertToString(e.getRecord().getControl("revoke_num").getValue());
			String end_way_memo = Utils.convertToString(e.getRecord().getControl("end_way_memo").getValue());
			String licence_word = Utils.convertToString(e.getRecord().getControl("licence_word").getValue());
			String licence_no = Utils.convertToString(e.getRecord().getControl("licence_no").getValue());
			String licence_kind = Utils.convertToString(e.getRecord().getControl("licence_kind").getValue());
			String end_lic_word = Utils.convertToString(e.getRecord().getControl("end_lic_word").getValue());
			String end_lic_num = Utils.convertToString(e.getRecord().getControl("end_lic_num").getValue());
			String b_company_name = Utils.convertToString(e.getRecord().getControl("b_company_name").getValue());
			String case_ori_name = Utils.convertToString(e.getRecord().getControl("case_ori_name").getValue());
			String edit_case_ori_num = Utils.convertToString(e.getRecord().getControl("edit_case_ori_num").getValue());
			
			String edit_end_reg_yy = Utils.convertToString(e.getRecord().getControl("edit_end_reg_yy").getValue());
			String edit_end_reg_no = Utils.convertToString(e.getRecord().getControl("edit_end_reg_no").getValue());
			String edit_end_date = Utils.convertToString(e.getRecord().getControl("edit_end_date").getValue());
			
			// 處理 先敲簽核的資訊
			String tmp_acc_date = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("tmp_acc_date"));
			String tmp_choice_acc_rlt = Utils.convertToString(e.getPage().getHttpGetParams().getParameter("tmp_choice_acc_rlt"));
			
			
			
			e.getRecord().getControl("user_id_show").setValue( Utils.convertToString(DBTools.dLookUp(" empname ", "ibmuser", " empno = '"+UserID+"'", "DBConn")) );
			
			
			if( !StringUtils.isEmpty(reg_yy) && !StringUtils.isEmpty(reg_no) ){
				e.getRecord().getControl("tittle_regno").setValue( reg_yy + reg_no );
				e.getRecord().getControl("show_regno").setValue( reg_yy + reg_no );
				e.getRecord().getControl("regnum").setValue( reg_yy + reg_no);
			}else{
				e.getRecord().getControl("tittle_regno").setValue( "( 尚未取號 )" );
				e.getRecord().getControl("show_regno").setValue( "( 尚未取號 )" );
			}
			
			if (!StringUtils.isEmpty(edit_case_ori_num)) {
				e.getRecord().getControl("case_ori_num_show").setValue( "&nbsp;(" +  edit_case_ori_num +")" );
				
				}
			if (!StringUtils.isEmpty(b_company_name)) {
				e.getRecord().getControl("b_company_name").setValue( "&nbsp;(" +  b_company_name +")" );
			}
			e.getRecord().getList("edit_dis_notice_word").setListOfValues( composeOfficialDocumentPrefix(ib_prcs) );
			// 案件來源-文號
		
			if (!StringUtils.isEmpty(EMPNAME)) {
				e.getRecord().getControl("reg_emp").setValue( EMPNAME  );
			}
		
			String ad_typ = Utils.convertToString(e.getRecord().getControl("ad_typ").getValue());
			String ib_prcs_name = "";
			if("A".equals(ib_prcs)){
				ib_prcs_name = "一般違建";
			}else if("B".equals(ib_prcs)){
				if("A".equals(ad_typ)){
					ib_prcs_name = "大型帆布廣告";
				}else if("B".equals(ad_typ)){
					ib_prcs_name = "危險招牌";	
				}
				else if("C".equals(ad_typ)){
					ib_prcs_name = "高風險廣告物";	
				}
			}else if("C".equals(ib_prcs)){
				ib_prcs_name = "下水道違建";
			}
			e.getRecord().getControl("ib_prcs_name").setValue( ib_prcs_name );
			
			JDBCConnection jdbcConn = null;
			DbRow dataRow = null, singleRowData = null;
			Enumeration dataRows = null; 
			try{
				jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");

				
				String SQL = "select building_coat,building_height,building_area,building_length";
				SQL += " ,b.acc_rlt, b.acc_job";
				SQL += " ,dis_b_way ,dis_b_fl, dis_b_add9, ibm_item, ibm_item_memo ";
				SQL += " ,d.empname as reg_emp_nm , e.empname as dmltn_emp_nm, f.empname as b_notice_emp_nm ";
				SQL += " , to_number(to_char(current_date , 'yyyy'), '9999') -1911 || '/' || to_char(current_date , 'mm/dd') as acc_date ";
				SQL += " from ibmcase as a  ";
				SQL += " left join ibmsts as b on a.case_id = b.case_id";
				SQL += " left join ibmcode as c on c.code_type = 'ZON' and a.DIS_B_ADDZON = c.code_seq";
				// 認定承辦
				SQL += " left join ibmuser as d on a.reg_emp = d.empno";
				// 排拆承辦
				SQL += " left join ibmuser as e on a.dmltn_emp = e.empno";
				// 拆除承辦
				SQL += " left join ibmuser as f on a.b_notice_emp = f.empno";
				SQL += " where a.case_id = '"+case_id+"' ";
			

				singleRowData = jdbcConn.getOneRow( SQL );
		
				if (singleRowData != null) {
					
					//String show_addr = Utils.convertToString(singleRowData.get("show_addr")== null ? "" :singleRowData.get("show_addr"));	
					String dis_b_way = Utils.convertToString(singleRowData.get("dis_b_way")==null ? "" : singleRowData.get("dis_b_way") );
					String dis_b_fl = Utils.convertToString(singleRowData.get("dis_b_fl")==null ? "" : singleRowData.get("dis_b_fl") );
					// 違建物-地址備註
					String dis_b_add9 = Utils.convertToString(singleRowData.get("dis_b_add9")==null ? "" : singleRowData.get("dis_b_add9") );
					String acc_date = Utils.convertToString(singleRowData.get("acc_date")==null ? "" : singleRowData.get("acc_date") );
					acc_rlt_now = Utils.convertToString(singleRowData.get("acc_rlt")==null ? "" : singleRowData.get("acc_rlt") );
					prcsTyp = acc_rlt_now.substring(0,1);
					
					String reg_emp_nm = Utils.convertToString(singleRowData.get("reg_emp_nm")==null ? "" : singleRowData.get("reg_emp_nm") );
					String dmltn_emp_nm = Utils.convertToString(singleRowData.get("dmltn_emp_nm")==null ? "" : singleRowData.get("dmltn_emp_nm") );
					String b_notice_emp_nm= Utils.convertToString(singleRowData.get("b_notice_emp_nm")==null ? "" : singleRowData.get("b_notice_emp_nm") );
					String ibm_item = Utils.convertToString(singleRowData.get("ibm_item")==null ? "" : singleRowData.get("ibm_item") );
					String ibm_item_memo = Utils.convertToString(singleRowData.get("ibm_item_memo")==null ? "" : singleRowData.get("ibm_item_memo") );

					if("A".equals(ibm_item) ){
						ibm_item = "實質違建";
					}else if( "B".equals(ibm_item)){
						ibm_item = "程序違建";
					}else if( "Z".equals(ibm_item)){
						ibm_item = "其他：" + ibm_item_memo;
					}
					
					// 陳核承辦
					String up_empname = "";
					
					
					if( !StringUtils.isEmpty(reg_emp_nm) ){
						up_empname += "(認定)" + reg_emp_nm + "  ";
					}
					if( !StringUtils.isEmpty(dmltn_emp_nm) ){
						up_empname += "(排拆)" + dmltn_emp_nm + "  ";
					}
					if( !StringUtils.isEmpty(b_notice_emp_nm) ){
						up_empname += "(結案)" + b_notice_emp_nm;
					}
					
					
					//e.getRecord().getControl("up_empname").setValue(up_empname);
					e.getRecord().getControl("dmltn_emp").setValue(dmltn_emp_nm);
					
					
					// 110/10/08 增加 退回認定釐清功能 
					// 釐清功能僅呈現認定科可以改的部分
					String PROGRAM_ID = Utils.convertToString(SessionStorage.getInstance(e.getPage().getRequest()).getAttribute("PROGRAM_ID")); 
					if("im60302".equals(PROGRAM_ID)){
						e.getRecord().getControl("acc_rlt_now").setValue("269");
					}else{
						e.getRecord().getControl("acc_rlt_now").setValue(acc_rlt_now);
					}
					
					e.getRecord().getControl("acc_rlt").setValue(acc_rlt_now);
					// tmp_acc_date
					if(!StringUtils.isEmpty( tmp_acc_date) && tmp_acc_date.length() > 6){
						acc_date = styleDate(tmp_acc_date);
						}
					e.getRecord().getControl("acc_date").setValue(acc_date);
	

				}
				
	
				//-------------------
				// show 下載連結
				// 認定通知單
				// 
				// 拆除時間通知單
				// im40101_prt.jsp?case_id=" + case_id_chk + "&out_ext=PDF
				// 結案時間通知單
				// im40201_prt.jsp?case_id=" + case_id_chk + "&out_ext=PDF&out_null=N
				//------------------
				String show_download = "" ,tmp_url = "", link_word="";
				// 認定通知單
				String prcs_2 = Utils.convertToString(DBTools.dLookUp(" count(*)", "ibmfym", " acc_rlt in ('232', '249', '252') and case_id = '"+case_id+"'", "DBConn"));
				//排拆通知單
				String prcs_3 = Utils.convertToString(DBTools.dLookUp(" count(*)", "ibmfym", " acc_rlt in ('342','344', '352', '362') and case_id = '"+case_id+"'", "DBConn"));
				//結案通知單
				String prcs_4 = Utils.convertToString(DBTools.dLookUp(" count(*)", "ibmfym", " acc_rlt in ('442', '452', '462') and case_id = '"+case_id+"'", "DBConn"));
				link_word = "下載瀏覽";
				if( !"0".equals(prcs_2) ){
					tmp_url = "im10101_prt.jsp?case_id=" + case_id + "&out_ext=PDF&data_need_type=SurveySheet";
					show_download = "<label class='download-label pointer' alt='"+link_word+"' title='"+link_word+"' att-name='"+link_word+"'  onclick=\"gogoDownload('"+tmp_url+"')\">"+link_word+"</label>";
					e.getRecord().getControl("print_1").setValue( show_download );
					
					tmp_url = "im10101_prt.jsp?case_id=" + case_id + "&out_ext=PDF&data_need_type=Notice";
					show_download = "<label class='download-label pointer' alt='"+link_word+"' title='"+link_word+"' att-name='"+link_word+"'  onclick=\"gogoDownload('"+tmp_url+"')\">"+link_word+"</label>";
					e.getRecord().getControl("print_2").setValue( show_download );
				}else{
					e.getRecord().getControl("print_1").setValue( "(無)" );
					e.getRecord().getControl("print_2").setValue( "(無)" );
				}
				
				if (!"0".equals(prcs_3)){
					tmp_url = "im40101_prt.jsp?case_id=" + case_id + "&out_ext=PDF";
					show_download = "<label class='download-label pointer' alt='"+link_word+"' title='"+link_word+"' att-name='"+link_word+"'  onclick=\"gogoDownload('"+tmp_url+"')\">"+link_word+"</label>";
					e.getRecord().getControl("print_3").setValue( show_download );
				}else{
					e.getRecord().getControl("print_3").setValue( "(無)" );
				}
				
				if (!"0".equals(prcs_4)){
					tmp_url = "im40201_prt.jsp?case_id=" + case_id + "&out_ext=PDF&out_null=N";
					//show_download = "<label>"+link_word+"</label>&nbsp;<img src='img/icon_map_on.png' alt='下載"+link_word+"' title='下載"+link_word+"' att-name='"+link_word+"' style= 'width:22px;margin-right:20px;' onclick=\"gogoDownload('"+tmp_url+"')\">";
					show_download = "<label class='download-label pointer' alt='"+link_word+"' title='"+link_word+"' att-name='"+link_word+"'  onclick=\"gogoDownload('"+tmp_url+"')\">"+link_word+"</label>";
					e.getRecord().getControl("print_4").setValue( show_download );
				}else{
					e.getRecord().getControl("print_4").setValue( "(無)" );
				}

				
				//-------------------
				//show 專案   prjnm
				//------------------
				
				String prjlist_html = "";
				SQL = "select * from IBMCSPRJ where CASE_ID = '"+case_id+"' order by PRJ_CODE";
				dataRows = jdbcConn.getRows(SQL);
				
				while (dataRows != null && dataRows.hasMoreElements()) {
					dataRow = (DbRow)dataRows.nextElement();
					
					String PRJ_CODE = Utils.convertToString(dataRow.get("PRJ_CODE"));
					String PRJ_YY = Utils.convertToString(dataRow.get("PRJ_YY"));
					String PRJ_NM = Utils.convertToString(dataRow.get("PRJ_NM"));
					
					if(PRJ_YY == "000"){
						PRJ_YY = "通用";
					}
					
					prjlist_html += "<div class='PROJECT_DATA' prj_code='"+PRJ_CODE+"' prj_yy='"+PRJ_YY+"' prj_nm='"+PRJ_NM+"'>" + PRJ_NM +  "(" + PRJ_YY + ")&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='PROJECT_DELETE' onclick=\"delProject('"+PRJ_CODE+"');\"></div>"; 
				}
				e.getRecord().getControl("PRJLIST").setValue(prjlist_html);
		

				//-------------------
				//show 地號
				//------------------
				SQL = " select LAND_SEQ, DIST_DESC || SECTION_NM ||  ROAD_NO1 ||'-' || ROAD_NO2 as LD from IBMCSLAN";
				SQL += " where case_id ='" + case_id + "'  ";
				dataRows = jdbcConn.getRows(SQL);
				String landBR = "<br><span style='visibility: hidden;'>地號：</span>";
				String show_land = "";
				while (dataRows != null && dataRows.hasMoreElements()) {
							
					dataRow = (DbRow)dataRows.nextElement();
					String LD = Utils.convertToString(dataRow.get("LD")== null ? "" :dataRow.get("LD"));	
					String LAND_SEQ = Utils.convertToString(dataRow.get("LAND_SEQ")== null ? "" :dataRow.get("LAND_SEQ"));	
					if( !"1".equals(LAND_SEQ) )show_land += landBR;
					show_land += LD;
				}
				e.getRecord().getControl("show_land").setValue(show_land);
				
				//地段
				int landindex = 0;
				SQL = "select * from IBMCSLAN where CASE_ID = '"+case_id+"' order by LAND_SEQ";
				dataRows = jdbcConn.getRows(SQL);
				String zon_area_list_html = "";
				while (dataRows != null && dataRows.hasMoreElements()) {
					dataRow = (DbRow)dataRows.nextElement();
					
					landindex++;
					String DIST = Utils.convertToString(dataRow.get("DIST"));
					String DIST_DESC = Utils.convertToString(dataRow.get("DIST_DESC"));
					String SECTION = Utils.convertToString(dataRow.get("SECTION"));
					String SECTION_NM = Utils.convertToString(dataRow.get("SECTION_NM"));
					String ROAD_NO1 = Utils.convertToString(dataRow.get("ROAD_NO1"));
					String ROAD_NO2 = Utils.convertToString(dataRow.get("ROAD_NO2"));
					
					zon_area_list_html += "<div class='ZONE_AREA_LIST' landindex='"+landindex+"' dist='"+DIST+"' dist_desc='"+DIST_DESC+"' section='"+SECTION+"' section_nm='"+SECTION_NM+"' road_no1='"+ROAD_NO1+"' road_no2='"+ROAD_NO2+"'>" + DIST_DESC + SECTION_NM + ROAD_NO1 + "-" +ROAD_NO2 + "&nbsp;<img src='img/DeleteQ.png' class= 'img_add' id='LAND_DELETE' onclick=\"delLan('"+landindex+"');\"></div>";
				}
				e.getRecord().getControl("ZONE_AREA_LIST").setValue(zon_area_list_html);
		
			
			}catch (Exception localException){
				System.err.println("im60301_man_C:BeforeShow error is " + localException.toString());		
			}finally{
				if( jdbcConn != null )jdbcConn.closeConnection();
			}
			
			
			
			HashMap<String, String> files = getFiles(case_id);
			// Set the value to the field
			e.getRecord().getControl("reg_date").setValue(styleDate(reg_date));
			e.getRecord().getControl("edit_reg_date").setValue(styleDate(reg_date));
			e.getRecord().getControl("sd_date").setValue(styleDate(sd_date));
			e.getRecord().getControl("edit_sd_date").setValue(styleDate(sd_date));
			e.getRecord().getControl("rvldate").setValue(styleDate(rvldate));
			e.getRecord().getControl("edit_rvldate").setValue(styleDate(rvldate));
			
		
			//e.getRecord().getControl("case_violator").setValue(getViolatorInfo(case_id));
			e.getRecord().getControl("case_photo").setValue(files.get("case_photo"));
			e.getRecord().getControl("case_attm").setValue(files.get("case_attm"));
			e.getRecord().getControl("pre_dis_date").setValue(styleDate(pre_dis_date));
			e.getRecord().getControl("edit_pre_dis_date").setValue(styleDate(pre_dis_date));
			e.getRecord().getControl("dis_notice_date").setValue(styleDateMain(dis_notice_date));
			e.getRecord().getControl("edit_dis_notice_date").setValue(styleDate(dis_notice_date));
			e.getRecord().getControl("b_notice_date").setValue(styleDate(b_notice_date));
			e.getRecord().getControl("edit_b_notice_date").setValue(styleDate(b_notice_date));
			e.getRecord().getControl("tearDownSituation").setValue(getTearDownSituation(ib_prcs, b_end_item, b_finish_date, end_chk_date, revoke_date, revoke_word, revoke_num, end_way_memo, licence_word, licence_no, licence_kind, end_lic_word, end_lic_num));
			e.getRecord().getControl("result_photo").setValue(files.get("result_photo"));
			e.getRecord().getControl("result_attm").setValue(files.get("result_attm"));
			e.getRecord().getControl("input_dis_regnum").setValue( dis_reg_yy+dis_reg_no );
			e.getRecord().getControl("input_end_regnum").setValue( edit_end_reg_yy + edit_end_reg_no );
			e.getRecord().getControl("edit_end_date").setValue(styleDate(edit_end_date));
			

			
			// 廣拆科 確認排拆次數
			String cnt_349 = Utils.convertToString(DBTools.dLookUp("count(*)", "IBMFYM", "case_id = '"+case_id+"' and acc_rlt = '349' ", "DBConn"));
			e.getRecord().getControl("cnt_349").setValue(cnt_349);
			
			// 判斷資料編輯次數
			String ibmfym_MAX_SEQ = Utils.convertToString(DBTools.dLookUp(" count(*) +1  ", "IBMFYM", " case_id = '"+case_id+"' and acc_rlt = '92c' ", "DBConn"));
			e.getRecord().getControl("upd_seq").setValue(ibmfym_MAX_SEQ);
			// 判斷是否有
			String ano_chk = Utils.convertToString(DBTools.dLookUp(" count(*)  ", "IBMLIST", " case_id = '"+case_id+"' and PIC_SEQ = '"+ibmfym_MAX_SEQ+"' and  PIC_KIND='ANO_UPD' ", "DBConn"));
			e.getRecord().getControl("ano_chk").setValue(ano_chk);
			
			
			
			
			//違建人
		
			try{
				jdbcConn = JDBCConnectionFactory.getJDBCConnection("DBConn");
				
				//-------------------------
				// 組 違建人
				//---------------------------
				String showDisnmDiv = "";
				String SQL = "select IB_USER,USR_ID,USR_ADD,USR_KND, USR_BRTH, USR_SEX, CASE_SEQ from IBMDISNM where CASE_ID = '"+case_id+"' order by CASE_SEQ";
				dataRows = jdbcConn.getRows(SQL);
				int t_seq = 1;
				while (dataRows != null && dataRows.hasMoreElements()) {
							
					dataRow = (DbRow)dataRows.nextElement();		
					
					String USR_KND = Utils.convertToString(dataRow.get("USR_KND")== null ? "" :dataRow.get("USR_KND"));	
					String USR_SEX = Utils.convertToString(dataRow.get("USR_SEX")== null ? "" :dataRow.get("USR_SEX"));	
					String IB_USER = Utils.convertToString(dataRow.get("IB_USER")== null ? "" :dataRow.get("IB_USER"));	
					String USR_ID = Utils.convertToString(dataRow.get("USR_ID")== null ? "" :dataRow.get("USR_ID"));	
					String USR_ADD = Utils.convertToString(dataRow.get("USR_ADD")== null ? "" :dataRow.get("USR_ADD"));	
					
					
					String USR_BRTH = Utils.convertToString(dataRow.get("USR_BRTH")== null ? "" :dataRow.get("USR_BRTH"));	
					
					String kindName = "ID_KIND" + t_seq;
					
					showDisnmDiv += "<div class=\"IBMDISNM\" data-seq="+t_seq+">";
					showDisnmDiv += "<div class=\"row\"><div class=\"col-xs-10\">";
					if( "1".equals(USR_KND) ){
						showDisnmDiv += "身　　　分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\" checked >&nbsp;自然人</label>　<label><input type=\"radio\" value=\"2\" name=\""+kindName+"\">&nbsp;法人</label><br>";
					}else if( "2".equals(USR_KND)){
						showDisnmDiv += "身　　　分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\">&nbsp;自然人</label><label>　<input type=\"radio\" value=\"2\" name=\""+kindName+"\" checked>&nbsp;法人</label><br>";
					}else{
						showDisnmDiv += "身　　　分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\""+kindName+"\">&nbsp;自然人</label><label>　<input type=\"radio\" value=\"2\" name=\""+kindName+"\">&nbsp;法人</label><br>";
					}
					showDisnmDiv += "</div><div class=\"col-xs-2 text-right\"><a href=\"#gia\" class=\"del-link\" onclick=\"delIbmdisnm('del', this)\">刪除</a></div></div>";
					
					kindName = "ID_SEX" + t_seq;
					if( "M".equals(USR_SEX) ){
						showDisnmDiv += "性　　　別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\" checked>&nbsp;男性　</label>　<label><input type=\"radio\" value=\"F\" name=\""+kindName+"\">&nbsp;女性</label><br>";
					}else if( "F".equals(USR_SEX)){
						showDisnmDiv += "性　　　別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\">&nbsp;男性　</label><label>　<input type=\"radio\" value=\"F\" name=\""+kindName+"\" checked>&nbsp;女性</label><br>";
					}else{
						showDisnmDiv += "性　　　別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\""+kindName+"\">&nbsp;男性　</label><label>　<input type=\"radio\" value=\"F\" name=\""+kindName+"\">&nbsp;女性</label><br>";
					}
					
					showDisnmDiv += "身分證字號:&nbsp;<input id=\"BMSDISOBEY_DISTID_NUM\" name=\"ID_NUM\" maxlength=\"10\" size=\"10\" value=\""+USR_ID+"\">&nbsp;(法人請填統編)<br>";
					showDisnmDiv += "姓　　　名:&nbsp;<input name=\"DIS_U_NAME\" maxlength=\"35\" size=\"40\" placeholder=\"姓名\" class=\"DIS_U_NAME\" value=\""+IB_USER+"\" ><br>";
					showDisnmDiv += "地　　　址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"120\" size=\"40\" placeholder=\"地址\" value=\""+USR_ADD+"\">";
					showDisnmDiv += "</div>";
					t_seq++;

				}
				String oriDisnmDiv = "<div class=\"IBMDISNM\" data-seq=" + t_seq + ">";
				oriDisnmDiv+="<div class=\"row\"><div class=\"col-xs-10\">";
				oriDisnmDiv+="身　　　分:&nbsp;<label><input type=\"radio\" value=\"1\" name=\"ID_KIND" + t_seq + "\">&nbsp;自然人</label>　<label><input type=\"radio\" value=\"2\" name=\"ID_KIND" + t_seq + "\">&nbsp;法人</label>";
				oriDisnmDiv+="</div><div class=\"col-xs-2 text-right\">";
				oriDisnmDiv+="<a href=\"#gia\" class=\"del-link addMod\" onclick=\"delIbmdisnm('del', this)\">刪除</a>";
				oriDisnmDiv+="</div></div>";
				oriDisnmDiv+="性　　　別:&nbsp;<label><input type=\"radio\" value=\"M\" name=\"ID_SEX" + t_seq + "\">&nbsp;男性　</label>　<label><input type=\"radio\" value=\"F\" name=\"ID_SEX" + t_seq + "\">&nbsp;女性</label><br>";
				oriDisnmDiv+="身分證字號:&nbsp;<input id=\"BMSDISOBEY_DISTID_NUM\" name=\"ID_NUM\" maxlength=\"10\" size=\"10\">&nbsp;(法人請填統編)<br>";
				oriDisnmDiv+="姓　　　名:&nbsp;<input name=\"DIS_U_NAME\" maxlength=\"35\" size=\"40\" placeholder=\"姓名\"class=\"DIS_U_NAME\"><br>";
				oriDisnmDiv+="地　　　址:&nbsp;<input name=\"DIS_U_ADDR\" maxlength=\"30\" size=\"40\" placeholder=\"地址\">";
				oriDisnmDiv+="</div>";
				
				// Be UI friendly by providing at least 1 empty slot of 違建人 for input when there is no 違建人 record
				if (t_seq == 1) showDisnmDiv += oriDisnmDiv;
				e.getRecord().getControl("show_ibmdisnm").setValue(showDisnmDiv);
			}catch (Exception localException){
				System.err.println("im60301_man_A : error is " + localException.toString());
				
			}finally{
				if( jdbcConn != null )jdbcConn.closeConnection();
			}
			
			
			
//End Event BeforeShow Action Custom Code

//ibmcase BeforeShow Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeShow Method Tail

//ibmcase OnValidate Method Head @2-5F430F8E
        public void onValidate(Event e) {
        }
//End ibmcase OnValidate Method Tail

//ibmcase BeforeSelect Method Head @2-E5EC9AD3
        public void beforeSelect(Event e) {
//End ibmcase BeforeSelect Method Head

//ibmcase BeforeSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeSelect Method Tail

//ibmcase BeforeBuildSelect Method Head @2-3041BA14
        public void beforeBuildSelect(DataObjectEvent e) {
//End ibmcase BeforeBuildSelect Method Head

//ibmcase Default Values for Select Query (SQL) @2-0C806E53
            try {
                ((SqlParameter)e.getParameter("case_id")).setDefaultValue("");
            } catch(java.text.ParseException ignore) {}
//End ibmcase Default Values for Select Query (SQL)

//ibmcase BeforeBuildSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeBuildSelect Method Tail

//ibmcase BeforeExecuteSelect Method Head @2-8391D9D6
        public void beforeExecuteSelect(DataObjectEvent e) {
//End ibmcase BeforeExecuteSelect Method Head

//ibmcase BeforeExecuteSelect Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeExecuteSelect Method Tail

//ibmcase AfterExecuteSelect Method Head @2-0972E7FA
        public void afterExecuteSelect(DataObjectEvent e) {
//End ibmcase AfterExecuteSelect Method Head

//ibmcase AfterExecuteSelect Method Tail @2-FCB6E20C
        }
//End ibmcase AfterExecuteSelect Method Tail

//ibmcase BeforeInsert Method Head @2-75B62B83
        public void beforeInsert(Event e) {
//End ibmcase BeforeInsert Method Head

//ibmcase BeforeInsert Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeInsert Method Tail

//ibmcase BeforeBuildInsert Method Head @2-FD6471B0
        public void beforeBuildInsert(DataObjectEvent e) {
//End ibmcase BeforeBuildInsert Method Head

//ibmcase BeforeBuildInsert Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeBuildInsert Method Tail

//ibmcase BeforeExecuteInsert Method Head @2-4EB41272
        public void beforeExecuteInsert(DataObjectEvent e) {
//End ibmcase BeforeExecuteInsert Method Head

//ibmcase BeforeExecuteInsert Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeExecuteInsert Method Tail

//ibmcase AfterExecuteInsert Method Head @2-C4572C5E
        public void afterExecuteInsert(DataObjectEvent e) {
//End ibmcase AfterExecuteInsert Method Head

//ibmcase AfterExecuteInsert Method Tail @2-FCB6E20C
        }
//End ibmcase AfterExecuteInsert Method Tail

//ibmcase AfterInsert Method Head @2-767A9165
        public void afterInsert(Event e) {
//End ibmcase AfterInsert Method Head

//ibmcase AfterInsert Method Tail @2-FCB6E20C
        }
//End ibmcase AfterInsert Method Tail

//ibmcase BeforeUpdate Method Head @2-33A3CFAC
        public void beforeUpdate(Event e) {
//End ibmcase BeforeUpdate Method Head

//ibmcase BeforeUpdate Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeUpdate Method Tail

//ibmcase BeforeBuildUpdate Method Head @2-37688606
        public void beforeBuildUpdate(DataObjectEvent e) {
//End ibmcase BeforeBuildUpdate Method Head

//ibmcase BeforeBuildUpdate Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeBuildUpdate Method Tail

//ibmcase BeforeExecuteUpdate Method Head @2-84B8E5C4
        public void beforeExecuteUpdate(DataObjectEvent e) {
//End ibmcase BeforeExecuteUpdate Method Head

//ibmcase BeforeExecuteUpdate Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeExecuteUpdate Method Tail

//ibmcase AfterExecuteUpdate Method Head @2-0E5BDBE8
        public void afterExecuteUpdate(DataObjectEvent e) {
//End ibmcase AfterExecuteUpdate Method Head

//ibmcase AfterExecuteUpdate Method Tail @2-FCB6E20C
        }
//End ibmcase AfterExecuteUpdate Method Tail

//ibmcase AfterUpdate Method Head @2-306F754A
        public void afterUpdate(Event e) {
//End ibmcase AfterUpdate Method Head

//ibmcase AfterUpdate Method Tail @2-FCB6E20C
        }
//End ibmcase AfterUpdate Method Tail

//ibmcase BeforeDelete Method Head @2-752E3118
        public void beforeDelete(Event e) {
//End ibmcase BeforeDelete Method Head

//ibmcase BeforeDelete Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeDelete Method Tail

//ibmcase BeforeBuildDelete Method Head @2-01A46505
        public void beforeBuildDelete(DataObjectEvent e) {
//End ibmcase BeforeBuildDelete Method Head

//ibmcase BeforeBuildDelete Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeBuildDelete Method Tail

//ibmcase BeforeExecuteDelete Method Head @2-B27406C7
        public void beforeExecuteDelete(DataObjectEvent e) {
//End ibmcase BeforeExecuteDelete Method Head

//ibmcase BeforeExecuteDelete Method Tail @2-FCB6E20C
        }
//End ibmcase BeforeExecuteDelete Method Tail

//ibmcase AfterExecuteDelete Method Head @2-389738EB
        public void afterExecuteDelete(DataObjectEvent e) {
//End ibmcase AfterExecuteDelete Method Head

//ibmcase AfterExecuteDelete Method Tail @2-FCB6E20C
        }
//End ibmcase AfterExecuteDelete Method Tail

//ibmcase AfterDelete Method Head @2-76E28BFE
        public void afterDelete(Event e) {
//End ibmcase AfterDelete Method Head

//ibmcase AfterDelete Method Tail @2-FCB6E20C
        }
//End ibmcase AfterDelete Method Tail

//ibmcase Record Handler Tail @2-FCB6E20C
    }
//End ibmcase Record Handler Tail

//Button_Update Button Handler Head @4-94AE9007
    public class ibmcaseButton_UpdateButtonHandler implements ButtonListener {
//End Button_Update Button Handler Head

//Button_Update OnClick Method Head @4-A9885EEC
        public void onClick(Event e) {
//End Button_Update OnClick Method Head

//Button_Update OnClick Method Tail @4-FCB6E20C
        }
//End Button_Update OnClick Method Tail

//Button_Update BeforeShow Method Head @4-46046458
        public void beforeShow(Event e) {
//End Button_Update BeforeShow Method Head

//Button_Update BeforeShow Method Tail @4-FCB6E20C
        }
//End Button_Update BeforeShow Method Tail

//Button_Update Button Handler Tail @4-FCB6E20C
    }
//End Button_Update Button Handler Tail

//Button_Cancel Button Handler Head @5-DD72D0D7
    public class ibmcaseButton_CancelButtonHandler implements ButtonListener {
//End Button_Cancel Button Handler Head

//Button_Cancel OnClick Method Head @5-A9885EEC
        public void onClick(Event e) {
//End Button_Cancel OnClick Method Head

//Button_Cancel OnClick Method Tail @5-FCB6E20C
        }
//End Button_Cancel OnClick Method Tail

//Button_Cancel BeforeShow Method Head @5-46046458
        public void beforeShow(Event e) {
//End Button_Cancel BeforeShow Method Head

//Button_Cancel BeforeShow Method Tail @5-FCB6E20C
        }
//End Button_Cancel BeforeShow Method Tail

//Button_Cancel Button Handler Tail @5-FCB6E20C
    }
//End Button_Cancel Button Handler Tail

//Comment workaround @1-A0AAE532
%> <%
//End Comment workaround

//Processing @1-DD38C17F
    Page im60301_man_AModel = (Page)request.getAttribute("im60301_man_A_page");
    Page im60301_man_AParent = (Page)request.getAttribute("im60301_man_AParent");
    if (im60301_man_AModel == null) {
        PageController im60301_man_ACntr = new PageController(request, response, application, "/im60301_man_A.xml" );
        im60301_man_AModel = im60301_man_ACntr.getPage();
        im60301_man_AModel.setRelativePath("./");
        //if (im60301_man_AParent != null) {
            //if (!im60301_man_AParent.getChild(im60301_man_AModel.getName()).isVisible()) return;
        //}
        im60301_man_AModel.addPageListener(new im60301_man_APageHandler());
        ((Record)im60301_man_AModel.getChild("ibmcase")).addRecordListener(new im60301_man_AibmcaseRecordHandler());
        im60301_man_ACntr.process();
%>
<%
        if (im60301_man_AParent == null) {
            im60301_man_AModel.setCookies();
            if (im60301_man_AModel.redirect()) return;
        } else {
            im60301_man_AModel.redirect();
        }
    }
//End Processing

%>
<%!
	public class owner{
		public String convertDate(String dateOld) 
		{
			String date = "";

			if(!StringUtils.isEmpty(dateOld))
			{

				if(dateOld.length() == 7)
				{
					date = dateOld.substring(0,3)+"/"+dateOld.substring(3,5)+"/"+dateOld.substring(5,7);
				}
				else if(dateOld.length() == 6)
				{
					date = dateOld.substring(0,2)+"/"+dateOld.substring(2,4)+"/"+dateOld.substring(4,6);
				}
			}

			return date;
		}
	}
%>