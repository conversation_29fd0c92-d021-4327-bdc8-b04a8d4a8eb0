# 資料庫預存程序清單

> **任務編號**: T1.2.2  
> **文件建立**: 2025-07-05  
> **狀態**: 完成  
> **總工時**: 4小時

## 摘要

本文件列出新北市違章建築管理系統中所有的預存程序（儲存程序/函數），包括用途分析和呼叫關係。共計 114 個程序。

## 程序分類

### 1. 地址處理函數 (5個)
地址相關的字串處理和格式化函數。

| 程序名稱 | 參數 | 回傳類型 | 用途 |
|----------|------|----------|------|
| `comb_addr` | 10個地址欄位 | text | 組合完整地址字串 |
| `comb_addr_ntpc` | case_id | text | 新北市地址格式化 |
| `comb_addr_ntpc2` | case_id | text | 新北市地址格式化（版本2） |
| `comb_addr_ntpc_firecase` | case_id | text | 消防案件地址格式化 |
| `comb_addr_ntpc_ibmlaborlaw` | case_id | text | 勞工法案件地址格式化 |

### 2. 資料轉換函數 (4個)
資料格式轉換和處理。

| 程序名稱 | 參數 | 回傳類型 | 用途 |
|----------|------|----------|------|
| `convert` | iregyear | record | 年度轉換 |
| `ibmtt` | text | text | 文字處理 |
| `my_function` | 無 | record | 自訂功能 |
| `wgs84_to_twd97` | case_id | text | 座標系統轉換 |

### 3. UUID 生成函數 (8個)
UUID 相關的函數，用於唯一識別碼生成。

| 程序名稱 | 參數 | 回傳類型 | 用途 |
|----------|------|----------|------|
| `uuid_generate_v1` | 無 | uuid | UUID v1 生成 |
| `uuid_generate_v1mc` | 無 | uuid | UUID v1 多播版本 |
| `uuid_generate_v3` | namespace, name | uuid | UUID v3 生成 |
| `uuid_generate_v4` | 無 | uuid | UUID v4 隨機生成 |
| `uuid_generate_v5` | namespace, name | uuid | UUID v5 生成 |
| `uuid_nil` | 無 | uuid | 空 UUID |
| `uuid_ns_dns` | 無 | uuid | DNS 命名空間 |
| `uuid_ns_oid` | 無 | uuid | OID 命名空間 |
| `uuid_ns_url` | 無 | uuid | URL 命名空間 |
| `uuid_ns_x500` | 無 | uuid | X500 命名空間 |

### 4. 效能監控函數 (3個)
PostgreSQL 效能統計相關函數。

| 程序名稱 | 參數 | 回傳類型 | 用途 |
|----------|------|----------|------|
| `pg_stat_statements` | showtext | record | 查詢統計 |
| `pg_stat_statements_info` | 無 | record | 統計資訊 |
| `pg_stat_statements_reset` | userid, dbid, queryid | record | 重置統計 |
| `get_tuple_stats_over_time` | interval_minutes | record | 時間統計 |

### 5. 觸發器函數 (94個)
資料表觸發器相關的函數，分為以下類型：

#### 5.1 BEFORE INSERT 觸發器 (17個)
用於插入前的資料處理和 ID 生成。

| 程序名稱 | 關聯表 | 功能 |
|----------|--------|------|
| `ibmcase_bef_ins` | ibmcase | 案件插入前處理 |
| `ibmcslan_bef_ins` | ibmcslan | 土地案件插入前處理 |
| `ibmdisnm_bef_ins` | ibmdisnm | 災害名稱插入前處理 |
| `ibmfirecase_bef_ins` | ibmfirecase | 消防案件 ID 生成 |
| `ibmfirecase_cslan_bef_ins` | ibmfirecase_cslan | 消防土地案件處理 |
| `ibmfym_bef_ins` | ibmfym | FYM 資料插入前處理 |
| `ibmifap_bef_ins` | ibmifap | IFAP 資料插入前處理 |
| `ibmlaborlaw_bef_ins` | ibmlaborlaw | 勞工法案件 ID 生成 |
| `ibmlaborlaw_cslan_bef_ins` | ibmlaborlaw_cslan | 勞工法土地案件處理 |
| `ibmlawfee_bef_ins` | ibmlawfee | 法務收費案件 ID 生成 |
| `ibmrpfr_bef_ins` | ibmrpfr | 報表處理插入前 |
| `ibmrplan_bef_ins` | ibmrplan | 報表計畫插入前 |
| `ibmrplb_bef_ins` | ibmrplb | 報表標籤插入前 |
| `ibmrpli_bef_ins` | ibmrpli | 報表項目插入前 |
| `ibmrpsm_bef_ins` | ibmrpsm | 報表摘要插入前 |
| `ibmrpuc_bef_ins` | ibmrpuc | 報表用戶插入前 |
| `ibmviolation_land_bef_ins` | ibmviolation_land | 違規土地插入前 |

#### 5.2 記錄日誌觸發器 (4個)
用於記錄資料異動日誌。

| 程序名稱 | 關聯表 | 功能 |
|----------|--------|------|
| `ibmfirecase_log_ins` | ibmfirecase → log_ibmfirecase | 消防案件異動記錄 |
| `ibmlaborlaw_log_ins` | ibmlaborlaw → log_ibmlaborlaw | 勞工法案件異動記錄 |
| `ibmlawfee_log_ins` | ibmlawfee → log_ibmlawfee | 法務收費異動記錄 |
| `uavdcp_log_bef_ins` | uavdcp_log | UAV DCP 記錄 |
| `uavdom_log_bef_ins` | uavdom_log | UAV DOM 記錄 |

#### 5.3 CRUD 操作觸發器 (71個)
標準的增刪改查觸發器，每個主要資料表都有對應的 _if_ins、_if_upd、_if_del 函數。

**主要資料表觸發器**:
- `ibmcase_if_*`: 主案件表 CRUD
- `ibmcode_if_*`: 代碼表 CRUD  
- `ibmcprp_if_*`: 案件屬性 CRUD
- `ibmcslan_if_*`: 案件土地 CRUD
- `ibmcsprj_if_*`: 案件專案 CRUD
- `ibmdisnm_if_*`: 災害名稱 CRUD
- `ibmfym_if_*`: FYM 資料 CRUD
- `ibmifap_if_*`: IFAP 資料 CRUD
- `ibmlist_if_*`: 清單資料 CRUD
- `ibmmenu_if_*`: 選單資料 CRUD
- `ibmmnrp_if_*`: 月報表 CRUD
- `ibmparam_if_*`: 參數表 CRUD
- `ibmperm_if_*`: 權限表 CRUD
- `ibmrole_if_*`: 角色表 CRUD
- `ibmrpfr_if_*`: 報表框架 CRUD
- `ibmrplb_if_*`: 報表標籤 CRUD
- `ibmrpli_if_*`: 報表項目 CRUD
- `ibmrpsm_if_*`: 報表摘要 CRUD
- `ibmrpuc_if_*`: 報表用戶 CRUD
- `ibmsts_if_*`: 狀態表 CRUD
- `ibmuser_if_*`: 用戶表 CRUD

**檔案管理觸發器**:
- `ibmfirecase_file_if_*`: 消防案件檔案
- `ibmlaborlaw_file_if_*`: 勞工法案件檔案

**UAV 相關觸發器**:
- `uavdcp_bef_ins`: UAV DCP 插入前處理
- `uavdom_bef_ins`: UAV DOM 插入前處理

#### 5.4 外鍵約束觸發器 (2個)
系統自動產生的外鍵約束觸發器。

| 程序名稱 | 關聯表 | 功能 |
|----------|--------|------|
| `RI_FKey_check_ins` | im52101_excel_caseid | 外鍵檢查（插入） |
| `RI_FKey_check_upd` | im52101_excel_caseid | 外鍵檢查（更新） |
| `RI_FKey_noaction_del` | im52101_excel_imports | 外鍵無動作（刪除） |
| `RI_FKey_noaction_upd` | im52101_excel_imports | 外鍵無動作（更新） |

## 關鍵程序詳細說明

### 1. 地址組合函數 (comb_addr)
```sql
-- 功能: 將 10 個地址欄位組合成完整地址
-- 參數: iad1~iad8 (村里鄰、路街段、巷、弄、號、樓、室等)
-- 回傳: 格式化的完整地址字串
-- 使用: 顯示和列印時的地址格式化
```

### 2. 案件 ID 自動生成
```sql
-- ibmcase_bef_ins, ibmfirecase_bef_ins, ibmlaborlaw_bef_ins, ibmlawfee_bef_ins
-- 功能: 基於年月和流水號自動產生案件編號
-- 格式: YYMMMXXXXX (年月 + 5位流水號)
-- 規則: 年月 = 當前年月 - 191100
```

### 3. 異動記錄機制
```sql
-- ibmfirecase_log_ins, ibmlaborlaw_log_ins, ibmlawfee_log_ins
-- 功能: 自動將資料異動記錄到對應的 log_ 表中
-- 觸發時機: BEFORE INSERT OR UPDATE
-- 記錄內容: 完整的欄位資料 + 時間戳 + UUID
```

## 呼叫關係分析

### 1. 地址處理鏈
```
JSP 頁面 → comb_addr_ntpc(case_id) → comb_addr(10 parameters) → 格式化地址
```

### 2. 案件建立流程
```
Insert 操作 → ibmcase_bef_ins → 產生 case_id → ibmcase_if_ins → 記錄異動
```

### 3. 記錄異動流程
```
Update 操作 → table_log_ins → UUID 生成 → log_table 插入
```

## 效能考量

### 1. 高頻使用函數
- `comb_addr_ntpc`: 每次地址顯示都會調用
- `uuid_generate_v4`: 每次記錄異動都會調用
- `*_bef_ins`: 每次新增記錄都會調用

### 2. 優化建議
- 考慮將地址組合邏輯移至應用層
- 使用資料庫內建的 UUID 生成器
- 定期清理舊的 log_ 表記錄

## 維護建議

### 1. 定期檢查
- 監控觸發器執行效能
- 檢查 log_ 表的儲存空間使用
- 驗證 ID 生成函數的唯一性

### 2. 備份策略
- 包含函數定義的完整備份
- 測試環境的函數同步
- 函數變更的版本控制

### 3. 安全性
- 檢查函數的執行權限
- 避免 SQL 注入風險
- 敏感函數的存取控制

---

*此文件由【B】Claude Code - 後端開發任務組產出*