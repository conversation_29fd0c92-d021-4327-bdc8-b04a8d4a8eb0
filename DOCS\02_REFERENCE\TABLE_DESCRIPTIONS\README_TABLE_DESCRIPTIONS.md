# 新北市違章建築管理系統 - 資料表描述說明表使用指南

## 📋 概述

本文件說明如何使用透過10個Agent並行分析生成的完整資料表描述說明表系統。此系統整合了程式碼、頁面、文件等所有來源的資訊，提供統一的資料表和欄位描述管理。

## 🗂️ 檔案結構

```
/src/
├── TABLE_DESCRIPTIONS_MASTER.md          # 主要描述文件（425行）
├── VALIDATION_REPORT.md                  # 完整性驗證報告
├── CREATE_TABLE_DESCRIPTIONS_SQL.sql     # 資料庫建立腳本
├── README_TABLE_DESCRIPTIONS.md          # 本使用指南
└── [Agent分析結果檔案]                    # 各Agent產生的中間檔案
```

## 🚀 快速開始

### 1. 查閱資料表描述

**查看主要文件**：
```bash
# 開啟主要描述文件
open TABLE_DESCRIPTIONS_MASTER.md
```

**搜尋特定表格**：
```bash
# 搜尋案件主表
grep -A 20 "ibmcase（案件主表）" TABLE_DESCRIPTIONS_MASTER.md

# 搜尋流程記錄表
grep -A 15 "tbflow（流程記錄表）" TABLE_DESCRIPTIONS_MASTER.md
```

**搜尋特定欄位**：
```bash
# 搜尋case_no欄位
grep "case_no" TABLE_DESCRIPTIONS_MASTER.md

# 搜尋所有員工編號欄位
grep "empno\|_emp" TABLE_DESCRIPTIONS_MASTER.md
```

### 2. 建立資料庫描述表

**執行SQL腳本**：
```bash
# 連接資料庫並執行腳本
PGPASSWORD='S!@h@202203' psql -h localhost -p 5432 -U postgres -d bms -f CREATE_TABLE_DESCRIPTIONS_SQL.sql
```

**驗證建立結果**：
```sql
-- 檢查建立的表格
SELECT table_name, table_description, record_count 
FROM table_descriptions 
ORDER BY business_priority DESC;

-- 檢查欄位描述
SELECT table_name, field_name, page_description, business_importance 
FROM field_descriptions 
WHERE table_name = 'ibmcase'
ORDER BY business_importance DESC;
```

### 3. 使用查詢視圖

**完整欄位描述視圖**：
```sql
-- 查看所有表格的完整欄位描述
SELECT * FROM v_complete_field_descriptions 
WHERE table_name = 'ibmcase';

-- 查看高重要性欄位
SELECT table_name, field_name, page_description, importance_stars
FROM v_complete_field_descriptions 
WHERE business_importance >= 4
ORDER BY business_importance DESC;
```

**狀態碼完整視圖**：
```sql
-- 查看所有RLT狀態碼
SELECT * FROM v_complete_status_codes 
WHERE code_type = 'RLT'
ORDER BY stage_order;

-- 查看協同退回相關狀態
SELECT * FROM v_complete_status_codes 
WHERE code_description LIKE '%協同退回%';
```

**頁面對應統計視圖**：
```sql
-- 查看頁面使用統計
SELECT * FROM v_page_table_statistics 
ORDER BY max_frequency_level DESC, page_count DESC;

-- 查看特定表格的頁面對應
SELECT * FROM v_page_table_statistics 
WHERE table_name = 'ibmcase';
```

## 📊 主要功能使用

### 1. 開發人員使用

**新功能開發時**：
```sql
-- 1. 查看相關表格結構
SELECT * FROM generate_table_report('ibmcase');

-- 2. 了解欄位業務意義
SELECT field_name, page_description, document_description, field_notes
FROM field_descriptions 
WHERE table_name = 'ibmcase' 
  AND field_name LIKE '%協同%';

-- 3. 檢查外鍵關係
SELECT table_name, field_name, foreign_reference
FROM field_descriptions 
WHERE foreign_reference IS NOT NULL;
```

**Bug修復時**：
```bash
# 1. 搜尋相關欄位
grep -n "caseopened\|flow_status" TABLE_DESCRIPTIONS_MASTER.md

# 2. 檢查狀態碼意義
grep -A 5 -B 5 "235\|245\|255" TABLE_DESCRIPTIONS_MASTER.md
```

### 2. 系統維護使用

**資料庫優化**：
```sql
-- 找出高重要性且資料量大的表格
SELECT table_name, table_description, record_count, business_priority
FROM table_descriptions 
WHERE business_priority >= 4 AND record_count > 100000
ORDER BY record_count DESC;

-- 檢查需要索引的欄位
SELECT table_name, field_name, page_description
FROM field_descriptions 
WHERE is_foreign_key = TRUE OR business_importance >= 4;
```

**效能監控**：
```sql
-- 檢查快速成長的表格
SELECT table_name, table_description, growth_rate, record_count
FROM table_descriptions 
WHERE growth_rate = 'fast'
ORDER BY record_count DESC;
```

### 3. 新人訓練使用

**系統架構理解**：
```bash
# 查看核心業務表
grep -E "^### [0-9]+\." TABLE_DESCRIPTIONS_MASTER.md | head -10

# 了解業務流程
grep -A 10 "業務流程" TABLE_DESCRIPTIONS_MASTER.md
```

**狀態碼學習**：
```sql
-- 學習三階段流程
SELECT code_value, code_description, business_scope, stage_order
FROM status_descriptions 
WHERE code_type = 'RLT' 
  AND business_scope = '一般違建'
ORDER BY stage_order;

-- 了解協同機制
SELECT code_value, code_description, business_scope
FROM status_descriptions 
WHERE code_description LIKE '%協同%'
ORDER BY business_scope, code_value;
```

## 🔧 維護與更新

### 1. 新增欄位描述

**使用便利函數**：
```sql
-- 新增欄位描述
SELECT add_field_description(
    'ibmcase',                    -- 表名
    'new_field',                  -- 欄位名
    'VARCHAR(50)',                -- 資料型別
    '新欄位名稱',                  -- 頁面描述
    '新欄位的業務用途說明',          -- 文件描述
    4,                           -- 重要性（1-5）
    '這是新增的欄位備註'            -- 備註
);
```

**手動SQL插入**：
```sql
INSERT INTO field_descriptions (
    table_name, field_name, data_type, page_description, 
    document_description, business_importance, field_notes
) VALUES (
    'ibmcase', 'new_field', 'VARCHAR(50)', '新欄位名稱',
    '新欄位的業務用途說明', 4, '這是新增的欄位備註'
);
```

### 2. 批次更新重要性

```sql
-- 將所有主鍵欄位設為最高重要性
SELECT update_importance_by_pattern('%', '%_id', 5);
SELECT update_importance_by_pattern('%', '%_no', 5);

-- 將日期欄位設為高重要性
SELECT update_importance_by_pattern('%', '%_date', 4);
```

### 3. 新增狀態碼

```sql
INSERT INTO status_descriptions (
    code_type, code_value, code_description, business_scope, 
    stage_order, is_active, moi_mapping, usage_pages
) VALUES (
    'RLT', '236', '[一般]認定新狀態', '一般違建', 
    7, TRUE, '02', 'im10101_man.jsp'
);
```

### 4. 文件同步更新

```bash
# 重新產生Markdown文件（需要自訂腳本）
psql -h localhost -p 5432 -U postgres -d bms -c "
SELECT '| ' || field_name || ' | ' || data_type || ' | ' || 
       page_description || ' | ' || document_description || ' | ' ||
       CASE business_importance 
           WHEN 5 THEN '⭐⭐⭐⭐⭐'
           WHEN 4 THEN '⭐⭐⭐⭐'
           WHEN 3 THEN '⭐⭐⭐'
           WHEN 2 THEN '⭐⭐'
           ELSE '⭐'
       END || ' | ' || COALESCE(field_notes, '') || ' |'
FROM field_descriptions 
WHERE table_name = 'ibmcase'
ORDER BY business_importance DESC;
" > ibmcase_fields.txt
```

## 📋 常用查詢範例

### 1. 業務分析查詢

**找出協同作業相關欄位**：
```sql
SELECT table_name, field_name, page_description, field_notes
FROM field_descriptions 
WHERE page_description LIKE '%協同%' 
   OR field_notes LIKE '%協同%'
   OR field_name LIKE '%con_%';
```

**分析外鍵關係**：
```sql
SELECT fd.table_name, fd.field_name, fd.foreign_reference,
       td.table_description
FROM field_descriptions fd
JOIN table_descriptions td ON fd.table_name = td.table_name
WHERE fd.is_foreign_key = TRUE
ORDER BY fd.table_name, fd.field_name;
```

**檢查代碼參照**：
```sql
SELECT table_name, field_name, code_reference, page_description
FROM field_descriptions 
WHERE code_reference IS NOT NULL
ORDER BY code_reference, table_name;
```

### 2. 系統架構分析

**核心表格依賴關係**：
```sql
WITH table_relationships AS (
    SELECT 
        fd.table_name as child_table,
        SPLIT_PART(fd.foreign_reference, '.', 1) as parent_table,
        COUNT(*) as fk_count
    FROM field_descriptions fd
    WHERE fd.foreign_reference IS NOT NULL
    GROUP BY fd.table_name, SPLIT_PART(fd.foreign_reference, '.', 1)
)
SELECT parent_table, child_table, fk_count
FROM table_relationships
ORDER BY parent_table, fk_count DESC;
```

**頁面複雜度分析**：
```sql
SELECT 
    page_name,
    COUNT(table_name) as table_count,
    STRING_AGG(operation_type, ', ') as operations,
    MAX(CASE WHEN usage_frequency = '高' THEN 3 
             WHEN usage_frequency = '中' THEN 2 
             ELSE 1 END) as complexity_score
FROM page_table_mapping
GROUP BY page_name
ORDER BY complexity_score DESC, table_count DESC;
```

### 3. 資料品質檢查

**檢查缺少描述的欄位**：
```sql
SELECT table_name, field_name, data_type
FROM field_descriptions 
WHERE page_description IS NULL 
   OR page_description = ''
   OR document_description IS NULL 
   OR document_description = '';
```

**檢查重要性未設定的欄位**：
```sql
SELECT table_name, field_name, business_importance
FROM field_descriptions 
WHERE business_importance IS NULL 
   OR business_importance = 0
ORDER BY table_name, field_name;
```

## 🔍 疑難排解

### 1. 常見問題

**Q: 找不到某個表格的描述**
```sql
-- 檢查表格是否存在
SELECT table_name, table_description 
FROM table_descriptions 
WHERE table_name LIKE '%關鍵字%';

-- 檢查是否有欄位資料
SELECT COUNT(*) as field_count
FROM field_descriptions 
WHERE table_name = '表格名稱';
```

**Q: 狀態碼對應不清楚**
```sql
-- 查看完整狀態碼系統
SELECT code_type, COUNT(*) as code_count,
       STRING_AGG(DISTINCT business_scope, ', ') as scopes
FROM status_descriptions 
GROUP BY code_type
ORDER BY code_count DESC;
```

**Q: 頁面對應關係錯誤**
```sql
-- 檢查頁面對應
SELECT page_name, table_name, operation_type, usage_frequency
FROM page_table_mapping 
WHERE page_name = '頁面名稱'
ORDER BY usage_frequency DESC;
```

### 2. 效能問題

**查詢速度慢**：
```sql
-- 檢查索引狀態
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename IN ('table_descriptions', 'field_descriptions', 
                   'status_descriptions', 'page_table_mapping');

-- 重建索引（如需要）
REINDEX TABLE field_descriptions;
```

**記憶體使用過高**：
```sql
-- 分批查詢大量資料
SELECT * FROM v_complete_field_descriptions 
WHERE table_name = '特定表格'
LIMIT 100;
```

## 📚 進階使用

### 1. 自訂報表產生

**產生表格清單報表**：
```sql
SELECT 
    td.table_name as "表格名稱",
    td.table_description as "用途說明",
    td.record_count as "資料筆數",
    CASE td.business_priority 
        WHEN 5 THEN '⭐⭐⭐⭐⭐'
        WHEN 4 THEN '⭐⭐⭐⭐'
        WHEN 3 THEN '⭐⭐⭐'
        WHEN 2 THEN '⭐⭐'
        ELSE '⭐'
    END as "重要性",
    td.growth_rate as "成長速度"
FROM table_descriptions td
ORDER BY td.business_priority DESC, td.record_count DESC;
```

**產生欄位詳細報表**：
```sql
SELECT 
    fd.field_name as "欄位名稱",
    fd.data_type as "資料型別",
    fd.page_description as "頁面描述",
    CASE fd.business_importance 
        WHEN 5 THEN '⭐⭐⭐⭐⭐'
        WHEN 4 THEN '⭐⭐⭐⭐'
        WHEN 3 THEN '⭐⭐⭐'
        WHEN 2 THEN '⭐⭐'
        ELSE '⭐'
    END as "重要性",
    CASE 
        WHEN fd.is_primary_key THEN 'PK'
        WHEN fd.is_foreign_key THEN 'FK'
        ELSE ''
    END as "鍵值",
    fd.field_notes as "備註"
FROM field_descriptions fd
WHERE fd.table_name = 'ibmcase'
ORDER BY fd.business_importance DESC, fd.field_name;
```

### 2. 資料匯出

**匯出到CSV**：
```bash
# 匯出表格描述
psql -h localhost -p 5432 -U postgres -d bms -c "
COPY (
    SELECT table_name, table_description, record_count, business_priority
    FROM table_descriptions 
    ORDER BY business_priority DESC
) TO STDOUT WITH CSV HEADER;" > table_descriptions.csv

# 匯出欄位描述
psql -h localhost -p 5432 -U postgres -d bms -c "
COPY (
    SELECT table_name, field_name, data_type, page_description, 
           business_importance, field_notes
    FROM field_descriptions 
    ORDER BY table_name, business_importance DESC
) TO STDOUT WITH CSV HEADER;" > field_descriptions.csv
```

**匯出到JSON**：
```sql
-- 匯出表格結構為JSON
SELECT json_agg(json_build_object(
    'table_name', table_name,
    'description', table_description,
    'priority', business_priority,
    'record_count', record_count,
    'fields', (
        SELECT json_agg(json_build_object(
            'field_name', field_name,
            'data_type', data_type,
            'page_description', page_description,
            'importance', business_importance
        ))
        FROM field_descriptions fd 
        WHERE fd.table_name = td.table_name
    )
)) as table_structure
FROM table_descriptions td
WHERE td.table_name = 'ibmcase';
```

## 🎯 最佳實務

### 1. 維護原則
- **定期更新**：每季度檢視並更新描述內容
- **版本控制**：重要變更要記錄變更歷史
- **一致性**：使用統一的描述格式和術語
- **完整性**：新增表格或欄位時同步更新描述

### 2. 使用建議
- **開發前**：先查閱相關表格和欄位描述
- **Code Review**：檢查是否符合既有的命名規範
- **文件更新**：程式碼變更時同步更新描述
- **知識分享**：定期進行系統架構說明會

### 3. 效能建議
- **索引維護**：定期檢查和重建索引
- **查詢優化**：使用預定義的視圖進行複雜查詢
- **分批處理**：大量資料操作時使用分批處理
- **快取機制**：常用查詢結果可考慮快取

## 📞 支援與聯絡

- **技術問題**：參考 CLAUDE.md 文件
- **業務問題**：聯絡系統管理員
- **更新建議**：透過系統回饋機制提交

---

**文件版本**：v1.0  
**最後更新**：2025-01-09  
**維護團隊**：系統開發組  
**適用系統**：新北市違章建築管理系統