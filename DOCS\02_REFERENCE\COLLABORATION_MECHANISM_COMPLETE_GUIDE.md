# 協同作業機制完整指南

## 目錄
1. [系統概述](#系統概述)
2. [現有協同機制分析](#現有協同機制分析)
3. [設計演進歷程](#設計演進歷程)
4. [最終設計方案](#最終設計方案)
5. [系統架構說明](#系統架構說明)
6. [業務流程詳解](#業務流程詳解)
7. [權限控制機制](#權限控制機制)
8. [注意事項](#注意事項)

## 系統概述

新北市違章建築管理系統的協同作業機制是一個跨部門、跨人員的案件處理協作系統。本文檔整合了協同機制的完整分析、設計演進過程，以及最終採用的解決方案。

### 核心功能
- 支援案件在不同承辦人之間的協同處理
- 提供完整的權限控制與狀態追蹤
- 包含協同退回補正機制，處理誤送協同情況

### 適用範圍
- 一般違建案件（2xx系列狀態碼）
- 廣告違建案件（3xx系列狀態碼）
- 施工中違建案件（4xx系列狀態碼）

## 現有協同機制分析

### 協同狀態碼定義

| 狀態碼 | 名稱 | 說明 | 業務類型 |
|--------|------|------|----------|
| 234 | 一般協同 | 一般違建案件協同處理中 | 一般違建 |
| 244 | 廣告協同 | 廣告違建案件協同處理中 | 廣告違建 |
| 254 | 施工中協同 | 施工中違建案件協同處理中 | 施工中違建 |

### 現有機制特點

1. **單向流程**
   - 案件進入協同狀態後，原承辦人失去編輯權限
   - 協同承辦人獲得案件完整處理權限
   - 缺少退回機制，造成誤送協同時的處理困難

2. **權限控制**
   ```sql
   -- 協同權限檢查邏輯
   SELECT COUNT(*) FROM tbflow 
   WHERE case_no = :case_no 
   AND case_state IN ('234', '244', '254')
   AND s_empno = :current_user
   ```

3. **資料追蹤**
   - 使用 `tbflow` 表記錄協同歷程
   - `case_con_user` 欄位記錄協同承辦人
   - `case_con_date` 記錄協同開始時間

### 問題識別

1. **無法退回**：一旦進入協同狀態，即使誤送也無法退回
2. **流程僵化**：必須完成協同流程才能繼續，影響效率
3. **權限限制**：原承辦人完全失去控制權

## 設計演進歷程

### 第一版設計（狀態碼23c/24c/25c方案）

**設計理念**：新增專門的「協同退回」狀態碼

```
234 (協同中) → 23c (協同退回) → 231 (重新開始)
```

**優點**：
- 狀態清晰，易於追蹤
- 符合現有狀態碼編碼規則

**缺點**：
- 增加系統複雜度
- 需要修改多處狀態判斷邏輯

### 第二版設計（直接退回方案）

**設計理念**：簡化流程，直接退回協同前狀態

```
234 (協同中) → 231 (協同前狀態)
```

**優點**：
- 流程簡單直接
- 不需新增狀態碼

**缺點**：
- 無法區分正常完成與退回
- 歷程追蹤困難

### 最終方案（狀態碼235/245/255方案）

**設計理念**：平衡清晰度與實用性

```
234 (協同中) → 235 (協同退回補正) → 231 (重新開始)
```

**採用原因**：
1. 保留退回歷程記錄
2. 明確區分退回與正常流程
3. 符合3位數狀態碼規範
4. 便於後續稽核與追蹤

## 最終設計方案

### 新增狀態碼

| 狀態碼 | 名稱 | 說明 | 下一步狀態 |
|--------|------|------|------------|
| 235 | 協同退回補正 | 一般違建協同退回進行補正 | 231 |
| 245 | 協同退回補正 | 廣告違建協同退回進行補正 | 241 |
| 255 | 協同退回補正 | 施工中違建協同退回進行補正 | 251 |

### 狀態轉換邏輯

```mermaid
graph LR
    A[231/241/251 辦理中] -->|協同| B[234/244/254 協同中]
    B -->|正常完成| C[下一階段]
    B -->|退回補正| D[235/245/255 補正中]
    D -->|補正完成| A
```

### 權限設計

1. **退回權限**
   - 只有協同承辦人可執行退回
   - 原承辦人無退回權限（防止干預）

2. **補正權限**
   - 案件退回後，原承辦人重新獲得編輯權
   - 協同承辦人失去案件存取權

## 系統架構說明

### 資料表結構

#### 1. tbflow（流程記錄表）
```sql
CREATE TABLE tbflow (
    case_no VARCHAR(20),      -- 案件編號
    case_state VARCHAR(3),    -- 狀態碼
    s_empno VARCHAR(10),      -- 承辦人員工編號
    flow_sdate TIMESTAMP,     -- 開始時間
    flow_edate TIMESTAMP,     -- 結束時間
    PRIMARY KEY (case_no, case_state, flow_sdate)
);
```

#### 2. buildcase（案件主表）
```sql
-- 相關欄位
case_con_user VARCHAR(10),   -- 協同承辦人
case_con_date TIMESTAMP,     -- 協同開始時間
caseopened VARCHAR(3),       -- 當前狀態
```

### 程式架構

```
/webapps/
├── im10201_man.jsp         # 案件管理主頁面（含退回按鈕）
├── case_collaboration_return.jsp  # 協同退回API
└── WEB-INF/
    └── handlers/
        └── im10201_Handlers.jsp  # 業務邏輯處理
```

## 業務流程詳解

### 1. 進入協同流程

```java
// 原承辦人發起協同
if (userCanInitiateCollaboration(case_no, current_user)) {
    updateCaseState(case_no, "234"); // 或244/254
    setCaseCollaborationUser(case_no, target_user);
    createFlowRecord(case_no, "234", target_user);
}
```

### 2. 協同處理中

- 協同承辦人可查看、編輯案件
- 原承辦人僅可查看，無編輯權限
- 系統記錄所有操作歷程

### 3. 協同退回流程

```java
// 協同承辦人執行退回
if (userCanReturnCollaboration(case_no, current_user)) {
    // 1. 更新狀態為補正中
    updateCaseState(case_no, "235"); // 或245/255
    
    // 2. 清除協同資訊
    clearCollaborationInfo(case_no);
    
    // 3. 記錄退回原因
    logReturnReason(case_no, return_reason);
    
    // 4. 通知原承辦人
    notifyOriginalHandler(case_no);
}
```

### 4. 補正完成流程

- 原承辦人修正問題
- 重新提交或繼續處理
- 可再次發起協同（如需要）

## 權限控制機制

### 權限矩陣

| 角色 | 狀態 | 查看 | 編輯 | 協同 | 退回 |
|------|------|------|------|------|------|
| 原承辦人 | 234/244/254 | ✓ | ✗ | ✗ | ✗ |
| 協同承辦人 | 234/244/254 | ✓ | ✓ | ✗ | ✓ |
| 原承辦人 | 235/245/255 | ✓ | ✓ | ✓ | ✗ |
| 協同承辦人 | 235/245/255 | ✗ | ✗ | ✗ | ✗ |

### 權限檢查實作

```java
public boolean checkCollaborationPermission(String caseNo, String userId, String action) {
    // 取得案件當前狀態與協同資訊
    CaseInfo caseInfo = getCaseInfo(caseNo);
    
    switch(action) {
        case "RETURN":
            // 只有協同承辦人可退回
            return caseInfo.isInCollaboration() && 
                   caseInfo.getCollaborationUser().equals(userId);
                   
        case "EDIT":
            // 協同中：只有協同承辦人可編輯
            // 補正中：只有原承辦人可編輯
            if (caseInfo.isInCollaboration()) {
                return caseInfo.getCollaborationUser().equals(userId);
            } else if (caseInfo.isInCorrection()) {
                return caseInfo.getOriginalHandler().equals(userId);
            }
            return caseInfo.getCurrentHandler().equals(userId);
            
        default:
            return false;
    }
}
```

## 注意事項

### 1. 資料完整性
- 所有狀態變更必須在事務中執行
- 確保 tbflow 記錄與 buildcase 狀態同步
- 退回時需記錄完整的退回原因

### 2. 系統相容性
- 新增狀態碼不影響現有流程
- 舊案件可正常處理
- 報表系統需更新以識別新狀態

### 3. 使用者教育
- 協同承辦人需了解退回功能
- 原承辦人需了解補正流程
- 建立標準作業程序（SOP）

### 4. 監控與稽核
- 定期檢查協同退回使用情況
- 分析退回原因，改善作業流程
- 建立異常處理機制

## 結論

協同作業機制的完善，特別是退回補正功能的加入，大幅提升了系統的彈性與實用性。透過清晰的狀態管理、完善的權限控制，以及詳細的歷程記錄，確保了跨部門協作的順暢與可追溯性。

本指南將持續更新，以反映系統的最新發展與最佳實踐。