# 業務資料字典

> **任務編號**: T1.2.4  
> **文件建立**: 2025-07-05  
> **狀態**: 完成  
> **總工時**: 4小時

## 摘要

本文件建立新北市違章建築管理系統關鍵資料表的欄位業務意義對照表，包括核心業務邏輯、代碼對照和欄位使用說明。

## 核心業務表欄位字典

### 1. ibmcase (主案件表) - 違章建築案件核心資料

#### 1.1 案件基本資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| case_id | 案件編號 | varchar(10) | 系統唯一案件識別碼 | 自動產生 | 1130900001 |
| reg_yy | 認定年度 | varchar(3) | 認定通知書年度 | 民國年後2位+年度 | 113 |
| reg_no | 認定流水號 | varchar(7) | 認定通知書流水號 | 7位數字 | 3207674 |
| caseno | 舊案件編號 | varchar(20) | 舊系統案件編號 | 兼容性欄位 | - |

#### 1.2 案件來源與分類
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| case_ori | 案件來源 | varchar(4) | 案件通報來源 | 參考 IBMCODE.COR | - |
| case_ori_num | 來源字號 | varchar(100) | 原始通報單位字號 | - | - |
| examine_kind | 勘查類別 | varchar(3) | 勘查作業類型 | 參考 IBMCODE | - |
| ibm_item | 違規項目 | varchar(1) | 違建類型代碼 | A:一般 B:廣告 C:下水道 | A |
| ibm_item_memo | 違規項目說明 | varchar(500) | 違規項目詳細說明 | - | - |

#### 1.3 違建物地址資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| dis_b_addzon | 郵遞區號 | varchar(3) | 違建物郵遞區號 | 參考 IBMCODE.ZON | 220 |
| dis_b_add1 | 村里鄰 | varchar(20) | 違建物村里鄰 | - | 永和里5鄰 |
| dis_b_add2 | 街路大道 | varchar(20) | 違建物街路名稱 | - | 中正路 |
| dis_b_add2_1 | 段 | varchar(20) | 路段名稱 | - | 一段 |
| dis_b_add3 | 巷 | varchar(20) | 巷號 | - | 123 |
| dis_b_add4 | 弄 | varchar(20) | 弄號 | - | 45 |
| dis_b_add5 | 號 | varchar(7) | 門牌號碼 | - | 67 |
| dis_b_add6 | 之號 | varchar(4) | 門牌之號 | - | 8 |
| dis_b_add6_1 | 號之 | varchar(4) | 號之編號 | - | 1 |
| dis_b_add7 | 樓 | varchar(4) | 樓層 | B:地下樓層 | 3 |
| dis_b_add7_1 | 樓之 | varchar(4) | 樓之編號 | - | 1 |
| dis_b_add8 | 室 | varchar(30) | 室號 | - | A室 |
| dis_b_add9 | 地址備註 | varchar(300) | 地址補充說明 | - | - |
| dis_b_way | 違建方向 | varchar(20) | 違建物方位 | - | 後方 |
| dis_b_fl | 頂加樓層 | varchar(3) | 頂樓加蓋樓層 | - | 4 |

#### 1.4 違建物詳細資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| dis_type | 違建類別代碼 | varchar(3) | 違建物類型 | 參考 IBMCODE.VTP | - |
| dis_type_desc | 違建類別名稱 | varchar(100) | 違建類型說明 | - | - |
| dis_sort | 違建組別代碼 | varchar(2) | 違建分組 | 參考 IBMCODE.VST | - |
| dis_sort_item | 違建組別名稱 | varchar(100) | 違建分組說明 | - | - |
| building_kind | 違章材料 | varchar(500) | 違建物建材 | - | 鐵皮 |
| building_coat | 違建地上層數 | numeric | 地上樓層數 | 數值 | 2 |
| building_height | 違章高度 | numeric | 違建物高度(公尺) | 數值 | 3.5 |
| building_area | 違章面積 | numeric | 違建物面積(平方公尺) | 數值 | 25.6 |
| building_length | 違章長度 | numeric | 違建物長度(公尺) | 數值 | 8.0 |
| building_memo | 違建情形備註 | varchar(500) | 違建詳細描述 | - | - |
| blduse | 建物用途 | varchar(200) | 違建物使用用途 | - | 住家用 |

#### 1.5 廣告物特殊欄位
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| ad_typ | 廣告物違建類別 | varchar(1) | 廣告物類型 | - | - |
| ad_kind | 廣告物型式 | varchar(30) | 廣告物形式 | - | 招牌 |
| ad_kind_ct | 廣告物型式內容 | varchar(10) | 廣告內容分類 | - | - |
| ad_kind_memo | 廣告物型式說明 | varchar(100) | 廣告型式其他說明 | - | - |
| ad_name | 廣告名稱及內容 | varchar(200) | 廣告具體內容 | - | - |
| ad_chk_law | 涉及違反法條 | varchar(10) | 違反法規條文 | - | - |
| ad_chk_law_mm | 法條其他說明 | varchar(500) | 違法說明 | - | - |
| ad_chk_rslt | 稽查結論 | varchar(500) | 廣告物稽查結果 | - | - |
| ad_deadline | 廣告期限 | varchar(3) | 廣告物期限 | - | - |
| ad_height_up | 廣告高度超標 | varchar(1) | 是否超過高度限制 | Y/N | N |

#### 1.6 勘查與認定資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| rvldate | 勘查日期 | numeric(8) | 現場勘查日期 | YYYYMMDD | 20231201 |
| reg_emp | 認定承辦人 | varchar(10) | 認定階段承辦人員 | 員工編號 | - |
| reg_emp_nm | 認定承辦人姓名 | varchar(20) | 承辦人中文姓名 | - | - |
| reg_rsult | 認定結果 | varchar(2) | 認定處理結果 | 參考 IBMCODE.RLT | 23 |
| reg_rsult_memo | 認定結果說明 | varchar(200) | 認定結果其他說明 | - | - |
| reg_ann | 是否為公告 | varchar(1) | 認定結果是否公告 | Y/N | N |
| reg_date | 認定通知書發文日期 | numeric(8) | 認定書發文日期 | YYYYMMDD | - |
| reg_rec_date | 認定紀錄日期 | numeric(8) | 認定資料記錄日期 | YYYYMMDD | - |

#### 1.7 狀態與流程控制
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| status | 處理狀態 | varchar(2) | 目前案件狀態 | 參考 IBMCODE.RLT | 23b |
| ib_prcs | 處理階段 | varchar(1) | 業務處理階段 | 1-11 | 3 |
| reg_unit | 認定單位 | varchar(3) | 負責認定的單位 | 參考 IBMCODE.UNT | - |
| dis_unit | 排拆單位 | varchar(3) | 負責排拆的單位 | 參考 IBMCODE.UNT | - |
| finish_state | 完成狀態 | varchar(1) | 案件完成狀態 | - | - |
| verify | 驗證狀態 | varchar(1) | 資料驗證狀態 | - | - |

#### 1.8 排拆相關資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| dmltn_emp | 排拆人員 | varchar(10) | 排拆階段承辦人員 | 員工編號 | - |
| dmltn_emp_old | 排拆人員(舊) | varchar(100) | 歷史排拆人員記錄 | - | - |
| pre_dis_date | 預定拆除日期 | numeric(8) | 計畫拆除日期 | YYYYMMDD | - |
| dis_notice_date | 拆除通知發文日期 | numeric(8) | 拆除通知書發文日期 | YYYYMMDD | - |
| dis_notice_word | 拆除通知發文字號 | varchar(20) | 拆除通知書字號 | - | - |
| dis_notice_crd | 拆除通知記錄日期 | numeric(8) | 拆除通知資料記錄日期 | YYYYMMDD | - |
| dis_reg_yy | 拆除通知年度 | varchar(3) | 拆除通知書年度 | 民國年 | - |
| dis_reg_no | 拆除通知流水號 | varchar(7) | 拆除通知書流水號 | - | - |
| dmltn_memo | 最新修改說明 | varchar(500) | 排拆相關說明 | - | - |
| dis_schdl | 拆除排程 | varchar(1) | 拆除排程狀態 | - | - |

#### 1.9 拆除執行資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| b_notice_emp | 拆除執行人員 | varchar(10) | 現場拆除人員 | 員工編號 | - |
| b_notice_emp_nm | 拆除執行人姓名 | varchar(20) | 現場拆除人員姓名 | - | - |
| b_notice_emp_old | 拆除執行人(舊) | varchar(100) | 歷史拆除人員 | - | - |
| b_notice_date | 至拆除現場日期 | numeric(8) | 實際到現場日期 | YYYYMMDD | - |
| b_notice_result | 拆除結果 | varchar(3) | 拆除執行結果 | 參考 IBMCODE.DRS | - |
| b_notice_way | 拆除方式 | varchar(2) | 拆除執行方式 | 參考 IBMCODE.DWY | - |
| b_notice_mmo | 拆除結果備註 | varchar(500) | 拆除執行說明 | - | - |
| b_finish_date | 拆除完成日期 | numeric(8) | 拆除作業完成日期 | YYYYMMDD | - |
| b_end_item | 拆除結果項目 | varchar(30) | 拆除結果分類 | - | - |
| b_end_item_desc | 拆除結果說明 | varchar(500) | 拆除結果詳細說明 | - | - |
| b_company | 委外廠商 | varchar(100) | 委託拆除廠商 | - | - |
| need_plc | 警力配合 | varchar(2) | 是否需要警力協助 | Y/N | - |
| plc_unit | 警局名稱 | varchar(3) | 協助的警察單位 | 參考 IBMCODE.PLC | - |
| b_monitor | 監督人員 | varchar(10) | 拆除監督人員 | 員工編號 | - |

#### 1.10 結案相關資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| end_way | 結案方式 | varchar(2) | 案件結案方式 | 參考 IBMCODE.ENW | - |
| end_way_memo | 結案方式說明 | varchar(2) | 結案方式備註 | - | - |
| end_date | 結案通知發文日期 | numeric(8) | 結案通知書發文日期 | YYYYMMDD | - |
| end_word | 結案通知發文字號 | varchar(20) | 結案通知書字號 | - | - |
| end_reg_yy | 結案通知年度 | varchar(3) | 結案通知書年度 | 民國年 | - |
| end_reg_no | 結案通知流水號 | varchar(7) | 結案通知書流水號 | - | - |
| end_chk_date | 派員勘查日期 | numeric(8) | 結案前勘查日期 | YYYYMMDD | - |
| rsult_emp | 結案承辦人員 | varchar(30) | 結案階段承辦人 | 員工編號 | - |
| rsult_emp_old | 結案承辦人(舊) | varchar(100) | 歷史結案承辦人 | - | - |
| rsult_rec_time | 結案記錄時間 | numeric(14) | 結案資料記錄時間 | YYYYMMDDHHMMSS | - |
| close_yy | 銷案年度 | varchar(3) | 銷案年度 | 民國年 | - |
| close_no | 銷案流水號 | varchar(7) | 銷案流水號 | - | - |
| close_rec_emp | 銷案記錄人員 | varchar(10) | 銷案記錄人員 | 員工編號 | - |
| is_closed | 是否銷案 | varchar(1) | 銷案標記 | Y/N | - |
| close_illustrate | 銷案說明 | varchar(2000) | 銷案原因說明 | - | - |

#### 1.11 補照與撤銷資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| licence_yy | 建造執照年度 | varchar(30) | 建造執照年度 | - | - |
| licence_word | 建造執照字軌 | varchar(30) | 建造執照字軌 | - | - |
| licence_no | 建造執照號碼 | varchar(30) | 建造執照號碼 | - | - |
| licence_kind | 執照類型 | varchar(2) | 執照類型（廣告用） | - | - |
| end_lic_word | 申請補照字號 | varchar(30) | 補照申請字號 | - | - |
| end_lic_num | 申請補照文號 | varchar(10) | 補照申請文號 | - | - |
| revoke_date | 撤銷違建日期 | numeric(8) | 撤銷違建日期 | YYYYMMDD | - |
| revoke_word | 撤銷違建字號 | varchar(30) | 撤銷違建字號 | - | - |
| revoke_num | 撤銷違建文號 | varchar(20) | 撤銷違建文號 | - | - |

#### 1.12 其他業務欄位
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| need_pay | 是否繳費 | varchar(1) | 是否需要繳費 | Y/N | N |
| prjnm | 專案名稱 | varchar(100) | 所屬專案名稱 | - | - |
| prjyy | 專案年度 | varchar(3) | 專案年度 | 民國年 | - |
| prjshow | 專案顯示 | varchar(1) | 專案是否顯示 | Y/N | - |
| prjfee_desc | 專案收費項目 | varchar(200) | 專案收費項目名稱 | - | - |
| bid_name | 下水道標案名稱 | varchar(250) | 地下水道標案名稱 | - | - |
| tube_prj_1 | 下水道專案1 | varchar(3) | 下水道專案代碼1 | - | - |
| tube_prj_2 | 下水道專案2 | varchar(3) | 下水道專案代碼2 | - | - |
| slf_dis_date | 自拆切結書日期 | varchar(7) | 自行拆除切結日期 | YYYYMMDD | - |
| nb_reason | 未拆原因 | varchar(1000) | 未拆除原因說明 | - | - |
| violation_land | 違規土地 | varchar(200) | 違規土地資訊 | - | - |
| stop_work_record | 停工記錄 | char(1) | 停工記錄標記 | Y/N | N |
| stop_work_record_desc | 停工記錄說明 | varchar(500) | 停工記錄說明 | - | - |

### 2. ibmcode (系統代碼表) - 系統參數代碼

| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| code_type | 代碼類型 | varchar(25) | 代碼分類 | 代碼類型清單 | RLT |
| code_seq | 代碼序號 | varchar(10) | 代碼值 | - | 23b |
| code_desc | 代碼說明 | varchar(200) | 代碼中文說明 | - | 認定完成 |
| sub_seq | 子序號 | varchar(6) | 子分類序號 | - | - |
| sub_seq1 | 子序號1 | varchar(6) | 子分類序號1 | - | - |
| sub_seq2 | 子序號2 | varchar(10) | 子分類序號2 | - | - |
| mark | 備註 | varchar(200) | 代碼備註說明 | - | - |
| is_del | 是否刪除 | varchar(1) | 邏輯刪除標記 | Y/N | N |
| is_open | 是否開放修改 | varchar(1) | 是否允許修改 | Y/N | N |
| order_by_seq | 排序序號 | numeric | 顯示排序 | 數值 | 1 |
| no_use | 專案收費項目 | varchar(1) | 是否為收費項目 | Y/N | - |
| valid_start_date | 有效開始日期 | numeric(8) | 代碼有效起始日 | YYYYMMDD | 0 |
| valid_end_date | 有效結束日期 | numeric(8) | 代碼有效截止日 | YYYYMMDD | 9999999 |

### 3. ibmlawfee (法務收費表) - 違章建築收費管理

#### 3.1 基本收費資訊
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| case_id | 收費案件編號 | varchar(10) | 法務收費案件編號 | 自動產生 | 1130900001 |
| reg_yy | 認定年度 | varchar(3) | 認定號碼年度 | 民國年 | 113 |
| reg_no | 認定流水號 | varchar(7) | 認定號碼流水號 | - | 3207674 |
| construction_date | 工期 | varchar(5) | 施工期間 | - | - |
| toll_name | 收費對象 | varchar(10) | 被收費人姓名 | - | 劉芝卉 |
| toll_type | 收費類型 | varchar(3) | 收費類型代碼 | 參考 IBMCODE | 001 |
| toll_type_note | 收費類型說明 | varchar(200) | 收費類型說明 | - | - |

#### 3.2 繳款書相關
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| paymentbook_date | 繳款書開立日期 | numeric(8) | 繳款書發行日期 | YYYYMMDD | - |
| paymentbook_no | 繳款書流水號 | varchar(50) | 繳款書編號 | - | - |
| reimburse_no | 繳款書銷帳編號 | varchar(50) | 銷帳作業編號 | - | - |

#### 3.3 處分書相關
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| punishment_date | 處分書開立日期 | numeric(8) | 處分書發行日期 | YYYYMMDD | - |
| punishment_no | 處分書文號 | varchar(50) | 處分書正式文號 | - | - |
| punishment_sent_date | 處分書送達日期 | numeric(8) | 處分書送達日期 | YYYYMMDD | - |

#### 3.4 繳款管理
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| final_payment_date | 最後繳款期限 | numeric(8) | 繳款截止日期 | YYYYMMDD | - |
| payment_date | 繳款日期 | numeric(8) | 實際繳款日期 | YYYYMMDD | - |
| payable_amount | 應繳款金額 | integer | 應繳總金額 | 新台幣元 | 24732 |
| pay_amount | 已繳款金額 | integer | 已繳納金額 | 新台幣元 | 24732 |
| unpay_amount | 未付款金額 | integer | 尚未繳納金額 | 新台幣元 | 0 |
| payment_type | 繳款情形 | varchar(1) | 繳款狀態 | 1:已繳清 | 1 |
| payment_type_note | 繳款情形說明 | varchar(200) | 繳款狀態說明 | - | - |
| is_installment | 是否分期付款 | varchar(1) | 分期付款標記 | Y/N | - |

#### 3.5 強制執行相關
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| check_property_date | 函查財產日期 | numeric(8) | 查詢財產日期 | YYYYMMDD | - |
| transfer_date | 移送行政執行日期 | numeric(8) | 移送強制執行日期 | YYYYMMDD | - |
| received_date | 行政執行署收款日期 | numeric(8) | 執行署收到案件日期 | YYYYMMDD | - |

### 4. caseopened & casesubmitsts (雙表機制) - 案件狀態控制

#### 4.1 caseopened (案件開啟表)
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| case_id | 案件編號 | varchar(10) | 案件唯一識別碼 | - | 1130900001 |
| empno | 承辦人員編號 | varchar(10) | 目前承辦人員 | 員工編號 | - |
| rec_time | 記錄時間 | timestamp | 開啟時間戳 | 系統時間 | 2023-12-01 10:30:00 |

#### 4.2 casesubmitsts (案件提交狀態表)
| 欄位名稱 | 中文名稱 | 資料型別 | 業務意義 | 代碼對照 | 範例值 |
|----------|----------|----------|----------|----------|--------|
| case_id | 案件編號 | varchar(10) | 案件唯一識別碼 | - | 1130900001 |
| acc_rlt | 狀態結果 | varchar(6) | 目前處理狀態 | 參考 IBMCODE.RLT | 23b |
| rec_time | 記錄時間 | timestamp | 狀態更新時間 | 系統時間 | 2023-12-01 15:45:00 |

## 重要代碼對照表

### 1. RLT (狀態碼) - 案件處理狀態
| 代碼 | 說明 | 階段 | 業務意義 |
|------|------|------|----------|
| 231 | 一般違建-掛號通報 | 01 | 案件初始通報 |
| 23b | 一般違建-認定完成 | 03 | 認定程序完成 |
| 321 | 一般違建-排拆分案完成 | 05 | 進入排拆階段 |
| 349 | 一般違建-排拆已簽准 | 06 | 排拆核准執行 |
| 440 | 一般違建-結案 | 11 | 案件正式結案 |

### 2. UNT (單位代碼) - 承辦單位
| 代碼 | 說明 | 職責範圍 |
|------|------|----------|
| 001 | 違建管理科 | 一般違建認定 |
| 002 | 使用管理科 | 使用許可管理 |
| 003 | 建築管理科 | 建築許可管理 |

### 3. ZON (郵遞區號) - 行政區域
| 代碼 | 說明 | 行政區 |
|------|------|--------|
| 220 | 板橋區 | 新北市板橋區 |
| 221 | 汐止區 | 新北市汐止區 |
| 222 | 深坑區 | 新北市深坑區 |

### 4. 違建項目代碼 (ibm_item)
| 代碼 | 說明 | 處理流程 |
|------|------|----------|
| A | 一般違建 | 標準認定流程 |
| B | 廣告物違建 | 廣告物專用流程 |
| C | 下水道違建 | 下水道專用流程 |

## 業務邏輯驗證規則

### 1. 資料完整性規則
```sql
-- 1. 金額邏輯一致性
payable_amount = pay_amount + unpay_amount

-- 2. 日期邏輯順序
payment_date >= paymentbook_date (如果已繳款)
punishment_sent_date >= punishment_date (如果已送達)

-- 3. 狀態與階段一致性
status 與 ib_prcs 階段對應關係要正確

-- 4. 分期付款邏輯
如果 is_installment = 'Y'，則 ibmlawfee_installment 應有對應記錄
```

### 2. 業務流程驗證
```sql
-- 1. 案件狀態流轉驗證
狀態變更必須符合：231 → 23b → 321 → 349 → 440 順序

-- 2. 雙表機制驗證
caseopened.case_id 必須對應 casesubmitsts.case_id
casesubmitsts.acc_rlt 必須對應有效的 IBMCODE.RLT

-- 3. 地址完整性驗證
dis_b_addzon + dis_b_add1 + dis_b_add2 不可全為空
```

### 3. 資料品質檢查
```sql
-- 1. 必要欄位檢查
case_id, reg_yy, reg_no 不可為空
status 必須對應 IBMCODE.RLT 有效值

-- 2. 格式驗證
reg_yy 必須為3位數字
reg_no 必須為7位數字
日期欄位必須為8位數字格式 YYYYMMDD

-- 3. 邏輯驗證
已結案的案件不可再修改主要欄位
```

## 維護建議

### 1. 定期檢查項目
- 檢查 IBMCODE 代碼完整性
- 驗證雙表機制資料一致性
- 檢查金額計算邏輯正確性
- 確認日期欄位格式統一

### 2. 資料清理建議
- 定期清理測試資料
- 歸檔歷史結案資料
- 統一日期格式標準
- 驗證地址資料完整性

### 3. 效能優化
- 為常用查詢欄位建立索引
- 定期更新統計資訊
- 監控大型查詢效能
- 優化報表查詢邏輯

---

*此文件由【B】Claude Code - 後端開發任務組產出*