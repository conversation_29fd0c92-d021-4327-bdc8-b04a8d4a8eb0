# 協同作業退回機制實施方案（修正版）

## 📋 文件資訊
- **文件編號**: DEV-2025-001-R1
- **文件目的**: 根據實際需求修正協同作業退回機制
- **修正日期**: 2025-07-05
- **修正原因**: 退回至協同前的狀態，而非特定的退回補正狀態

---

## 🎯 需求確認

根據系統截圖顯示：
- 因同仁有時送協同會有誤線問題
- 需要退回機制可退回承辦人
- 退回到**協同之前的狀態**

---

## 📊 狀態碼轉換邏輯

### 協同狀態退回規則

| 當前狀態 | 狀態說明 | 退回後狀態 | 說明 |
|---------|---------|-----------|------|
| 234 | [一般]認定送協同作業 | 231 | 退回至[一般]認定初建 |
| 244 | [廣告物]認定送協同作業 | 241 | 退回至[廣告物]認定初建 |
| 254 | [下水道]認定送協同作業 | 251 | 退回至[下水道]認定初建 |

### 實施邏輯

```java
/**
 * 協同作業退回狀態碼轉換
 * 退回至協同前的狀態（初建狀態）
 */
private String getCollaborationReturnStatus(String currentStatus) {
    switch(currentStatus) {
        case "234": return "231"; // 一般協同 → 一般認定初建
        case "244": return "241"; // 廣告協同 → 廣告認定初建
        case "254": return "251"; // 下水道協同 → 下水道認定初建
        default: return currentStatus;
    }
}
```

---

## 🔧 實施要點

### 1. 退回按鈕位置
- 在 im10201_man.jsp（協同作業詳細頁面）新增「退回承辦」按鈕
- 只有協同承辦人可見此按鈕

### 2. 退回流程
1. 協同承辦人點擊「退回承辦」
2. 輸入退回原因（必填）
3. 系統更新狀態為初建狀態（231/241/251）
4. 案件返回原承辦人
5. 記錄退回歷程

### 3. 資料更新
```sql
-- 1. 更新當前狀態
UPDATE ibmsts SET acc_rlt = '231' WHERE case_id = ? AND acc_rlt = '234';

-- 2. 新增歷程記錄
INSERT INTO ibmfym (case_id, acc_seq, acc_rlt, acc_memo, op_user, acc_date, acc_time)
VALUES (?, ?, '231', '協同退回：[退回原因]', ?, ?, ?);

-- 3. 清理協同記錄
DELETE FROM caseopened WHERE case_id = ?;

-- 4. 記錄操作日誌
INSERT INTO record (uuid, case_id, rec_type, org_rec, new_rec, empno)
VALUES (?, ?, '協同退回', '234', '231', ?);
```

### 4. 權限控制
- 只有當前協同承辦人可執行退回
- 原承辦人無法看到退回按鈕
- 已完成協同（23b/24b/25b）不可退回

---

## 📝 與原方案的差異

| 項目 | 原方案 | 修正方案 |
|-----|-------|---------|
| 退回目標 | 新的退回補正狀態 | 協同前的初建狀態 |
| 狀態碼 | 需要新增狀態碼 | 使用現有狀態碼 |
| 業務流程 | 進入退回補正流程 | 直接回到原承辦人 |

---

## ✅ 優點

1. **簡單直接**：不需要新增狀態碼
2. **符合需求**：解決誤送協同的問題
3. **流程清晰**：案件直接回到原承辦人手上
4. **易於實施**：使用現有的狀態轉換機制

---

*文件撰寫：【D】Claude Code - DevOps與技術架構任務組*
*修正日期：2025-07-05*