# 新北市違章建築管理系統 - 業務流程完整指南

## 目錄
1. [業務概述](#業務概述)
2. [流程架構](#流程架構)
3. [認定階段流程](#認定階段流程)
4. [排拆階段流程](#排拆階段流程)
5. [結案階段流程](#結案階段流程)
6. [協同作業機制](#協同作業機制)
7. [異常處理流程](#異常處理流程)
8. [狀態轉換規則](#狀態轉換規則)
9. [品質控制機制](#品質控制機制)
10. [流程優化建議](#流程優化建議)

## 業務概述

### 系統定位
新北市違章建築管理系統是一個處理違章建築從發現到拆除完成的全生命週期管理系統，涵蓋三種違建類型的專業化處理。

### 業務範圍
- **一般違建**：由拆除科負責，處理一般建築物違建
- **廣告違建**：由廣告科負責，處理廣告招牌類違建
- **下水道違建**：由勞安科負責，處理影響下水道的違建

### 核心價值
- 標準化作業流程，確保處理一致性
- 多部門協同作業，整合專業意見
- 完整證據保全，支援法律程序
- 透明化進度追蹤，提升行政效率

## 流程架構

### 三階段流程體系

```mermaid
graph LR
    A[掛號通報] --> B[認定階段]
    B --> C[排拆階段]
    C --> D[結案階段]
    
    B --> B1[現場勘查]
    B1 --> B2[違建認定]
    B2 --> B3[審核簽准]
    
    C --> C1[拆除通知]
    C1 --> C2[自行拆除]
    C1 --> C3[強制拆除]
    
    D --> D1[結案審核]
    D1 --> D2[案件歸檔]
```

### 狀態碼體系設計

```
狀態碼格式：XYZ
┌─┬─┬─┐
│X│Y│Z│
└─┴─┴─┘
 │ │ └── 細部狀態 (0-f)
 │ └──── 處理階段 (0-9)
 └────── 業務類型 (2=一般, 3=廣告, 5=下水道)
```

### 處理量統計

| 階段 | 一般違建 | 廣告違建 | 下水道違建 | 總計 |
|------|----------|----------|------------|------|
| 認定階段 | 174,073 | 85,342 | 42,156 | 301,571 |
| 排拆階段 | 14,986 | 17,775 | 8,234 | 40,995 |
| 結案階段 | 12,543 | 15,221 | 6,542 | 34,306 |

## 認定階段流程

### 1. 掛號通報（初始化）

#### 流程說明
```mermaid
graph TD
    A[民眾檢舉/巡查發現] --> B{來源判斷}
    B -->|民眾檢舉| C[線上通報]
    B -->|巡查發現| D[現場登錄]
    C --> E[案件編號產生]
    D --> E
    E --> F[基本資料建檔]
    F --> G[指派承辦人]
    G --> H[狀態: 231/241/251]
```

#### 關鍵處理
- **案件編號規則**：民國年(3碼) + 流水號(6碼)
- **自動分案機制**：依行政區與承辦人工作量分配
- **查報人管理**：記錄檢舉人資訊（可匿名）

#### 資料驗證
```javascript
// 前端驗證邏輯
function validateReportData() {
    // 必填欄位檢查
    if (!formData.address) {
        showError("違建地址為必填欄位");
        return false;
    }
    
    // 地址格式驗證
    if (!isValidAddress(formData.address)) {
        showError("請輸入有效的地址格式");
        return false;
    }
    
    // 日期邏輯檢查
    if (formData.reportDate > today) {
        showError("查報日期不可大於今日");
        return false;
    }
    
    return true;
}
```

### 2. 現場勘查（調查）

#### 流程說明
```mermaid
graph TD
    A[接收勘查任務] --> B[排定勘查時程]
    B --> C[現場實地勘查]
    C --> D{違建事實確認}
    D -->|是| E[拍照存證]
    D -->|否| F[結案處理]
    E --> G[測量記錄]
    G --> H[勘查報告]
    H --> I[狀態: 232/242/252]
```

#### 證據收集要求
1. **照片規範**
   - 最少4張不同角度照片
   - 包含GPS定位資訊
   - 自動加入浮水印（日期、地點）
   - 解析度不低於1920x1080

2. **測量記錄**
   - 違建面積計算
   - 高度測量
   - 佔用情形記錄
   - 影響範圍評估

3. **勘查報告內容**
   - 違建類型判定
   - 使用情形描述
   - 安全性評估
   - 處理建議

### 3. 認定審核（判定）

#### 流程說明
```mermaid
graph TD
    A[勘查報告完成] --> B[承辦人初審]
    B --> C{需要協同?}
    C -->|是| D[發起協同作業]
    C -->|否| E[股長審核]
    D --> F[協同處理]
    F --> E
    E --> G[科長核定]
    G --> H{審核結果}
    H -->|通過| I[認定完成 239/249/259]
    H -->|退回| J[補正處理 237/247/257]
```

#### 審核層級

| 層級 | 角色 | 權限範圍 | 處理時限 |
|------|------|----------|----------|
| 第一層 | 承辦人 | 初步認定、資料準備 | 3工作日 |
| 第二層 | 股長 | 審查認定、退回補正 | 2工作日 |
| 第三層 | 科長 | 最終核定、特殊處理 | 1工作日 |

#### 協同作業觸發條件
- 跨部門權責案件
- 爭議性案件
- 需專業技術評估
- 涉及其他單位業務

## 排拆階段流程

### 1. 拆除通知（告知）

#### 流程說明
```mermaid
graph TD
    A[認定完成] --> B[製作拆除通知書]
    B --> C[通知書發送]
    C --> D{送達方式}
    D -->|郵寄| E[掛號送達]
    D -->|公示| F[現場張貼]
    E --> G[送達證明]
    F --> G
    G --> H[自拆期限設定]
    H --> I[狀態: 331/341/351]
```

#### 通知書內容要求
1. **基本資訊**
   - 案件編號、違建地址
   - 違建人資訊
   - 違建事實描述
   - 法令依據

2. **處理說明**
   - 自行拆除期限（通常30天）
   - 拆除範圍說明
   - 申訴管道資訊
   - 聯絡窗口

3. **法律效果**
   - 逾期未拆除之後果
   - 強制執行說明
   - 費用求償告知

### 2. 排拆執行（處置）

#### 流程說明
```mermaid
graph TD
    A[自拆期限屆滿] --> B{檢查拆除情況}
    B -->|已自拆| C[現場確認]
    B -->|未拆除| D[排定強拆]
    C --> E[拍照存證]
    D --> F[拆除準備]
    F --> G[現場執行]
    G --> H[執行記錄]
    E --> I[狀態: 339/349/359]
    H --> I
```

#### 強制拆除作業
1. **前置準備**
   - 拆除人員調度
   - 機具設備安排
   - 警力支援申請
   - 交通管制規劃

2. **現場執行**
   - 安全圍籬設置
   - 拆除過程錄影
   - 廢棄物清運
   - 現場復原

3. **執行記錄**
   - 執行人員名單
   - 使用機具清單
   - 拆除過程描述
   - 完成照片存證

#### UAV（無人機）輔助作業
- 高空違建勘查
- 拆除前後對比
- 安全監控
- 證據保全

## 結案階段流程

### 1. 結案準備（檢核）

#### 流程說明
```mermaid
graph TD
    A[拆除完成] --> B[結案條件檢查]
    B --> C{檢查項目}
    C --> D[拆除完成確認]
    C --> E[文件齊全確認]
    C --> F[費用結清確認]
    D --> G{全部通過?}
    E --> G
    F --> G
    G -->|是| H[提送結案]
    G -->|否| I[補正處理]
```

#### 結案檢查清單
- [ ] 拆除完成照片（至少4張）
- [ ] 現場復原確認
- [ ] 相關公文歸檔
- [ ] 規費繳納證明
- [ ] 無待辦事項
- [ ] 無申訴案件

### 2. 結案審核（核定）

#### 流程說明
```mermaid
graph TD
    A[提送結案] --> B[承辦人整理]
    B --> C[股長審查]
    C --> D[科長核定]
    D --> E{審核結果}
    E -->|通過| F[正式結案]
    E -->|退回| G[補件處理]
    F --> H[歸檔封存]
    H --> I[狀態: 449/459/469]
```

#### 歸檔要求
1. **實體歸檔**
   - 案件卷宗裝訂
   - 檔案編號建立
   - 歸檔位置登記

2. **電子歸檔**
   - 掃描建檔
   - 電子簽章
   - 長期保存

3. **查詢索引**
   - 多維度檢索
   - 關鍵字標註
   - 快速調閱

## 協同作業機制

### 協同作業類型

#### 1. 一般協同（234/244/254）
```mermaid
graph LR
    A[原承辦人] -->|發起協同| B[協同承辦人]
    B -->|處理完成| C[返回原承辦人]
    C --> D[繼續處理]
```

#### 2. 完成協同（23b/24b/25b）
```mermaid
graph LR
    A[協同承辦人] -->|完成處理| B[協同結束]
    B --> C[原承辦人確認]
    C --> D[進入下一階段]
```

#### 3. 退回補正（235/245/255）
```mermaid
graph LR
    A[協同承辦人] -->|發現問題| B[退回補正]
    B --> C[原承辦人修正]
    C --> D[重新協同/繼續]
```

### 協同權限矩陣

| 角色 | 發起協同 | 處理協同 | 退回協同 | 結束協同 |
|------|----------|----------|----------|----------|
| 原承辦人 | ✓ | ✗ | ✗ | ✓ |
| 協同承辦人 | ✗ | ✓ | ✓ | ✓ |
| 主管 | ✓ | ✓ | ✓ | ✓ |

## 異常處理流程

### 1. 資料異常處理

#### 異常類型與處理
```mermaid
graph TD
    A[資料異常] --> B{異常類型}
    B -->|必填欄位空白| C[前端驗證攔截]
    B -->|格式錯誤| D[格式化處理]
    B -->|邏輯錯誤| E[業務規則檢查]
    C --> F[提示修正]
    D --> F
    E --> G[退回重填]
```

### 2. 流程異常處理

#### 異常情況處理矩陣

| 異常情況 | 處理機制 | 狀態碼 | 後續動作 |
|----------|----------|--------|----------|
| 誤送協同 | 協同退回 | 235/245/255 | 返回原承辦人 |
| 資料錯誤 | 退回補正 | 237/247/257 | 修正後重審 |
| 重複案件 | 併案處理 | - | 主案繼續 |
| 管轄錯誤 | 移轉處理 | - | 轉正確單位 |

### 3. 系統異常處理

```java
// 異常處理框架
try {
    // 業務邏輯執行
    processCase(caseNo);
    
} catch (BusinessException e) {
    // 業務異常：顯示友善錯誤訊息
    logger.warn("業務異常: " + e.getMessage());
    showUserMessage(e.getUserMessage());
    
} catch (DatabaseException e) {
    // 資料庫異常：回滾事務
    logger.error("資料庫異常: " + e.getMessage(), e);
    rollbackTransaction();
    showSystemError();
    
} catch (Exception e) {
    // 未預期異常：記錄並通知管理員
    logger.fatal("系統異常: " + e.getMessage(), e);
    notifyAdministrator(e);
    showMaintenancePage();
}
```

## 狀態轉換規則

### 狀態轉換決策表

基於56個預定義轉換規則的決策邏輯：

#### 認定階段轉換規則
| 起始狀態 | 目標狀態 | 條件 | 動作 |
|----------|----------|------|------|
| 231 | 232 | 勘查完成 | 更新勘查結果 |
| 232 | 234 | 需要協同 | 發起協同作業 |
| 234 | 23b | 協同完成 | 結束協同 |
| 23b | 239 | 審核通過 | 認定完成 |

#### 排拆階段轉換規則
| 起始狀態 | 目標狀態 | 條件 | 動作 |
|----------|----------|------|------|
| 239 | 331 | 發出通知 | 記錄通知資訊 |
| 331 | 334 | 期限屆滿 | 排定強拆 |
| 334 | 339 | 拆除完成 | 更新完成資訊 |

#### 結案階段轉換規則
| 起始狀態 | 目標狀態 | 條件 | 動作 |
|----------|----------|------|------|
| 339 | 441 | 提送結案 | 檢查結案條件 |
| 441 | 442 | 審核中 | 主管審核 |
| 442 | 449 | 核定通過 | 正式結案 |
| 449 | 460 | 歸檔完成 | 封存案件 |

### 狀態轉換驗證

```sql
-- 狀態轉換合法性檢查函數
CREATE FUNCTION validate_state_transition(
    p_from_state VARCHAR(3),
    p_to_state VARCHAR(3)
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM state_transition_rules
        WHERE from_state = p_from_state
        AND to_state = p_to_state
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql;
```

## 品質控制機制

### 1. 案件資料繕校（92c機制）

#### 品質檢查項目
```mermaid
graph TD
    A[92c品質控制] --> B[資料完整性]
    A --> C[邏輯一致性]
    A --> D[證據充分性]
    B --> E[必填欄位檢查]
    B --> F[附件完整檢查]
    C --> G[日期邏輯檢查]
    C --> H[狀態邏輯檢查]
    D --> I[照片數量檢查]
    D --> J[文件齊全檢查]
```

### 2. 多層審核機制

#### 審核層級與重點

| 審核層級 | 審核重點 | 通過標準 | 駁回處理 |
|----------|----------|----------|----------|
| 承辦人自審 | 資料正確性 | 無明顯錯誤 | 立即修正 |
| 股長複審 | 程序合規性 | 符合作業規定 | 退回補正 |
| 科長決審 | 決策適當性 | 處理得當 | 指示修正 |

### 3. 定期稽核檢查

#### 稽核項目清單
- [ ] 案件處理時效
- [ ] 狀態轉換合理性
- [ ] 協同作業必要性
- [ ] 證據保全完整性
- [ ] 文書品質
- [ ] 系統操作合規性

## 流程優化建議

### 短期優化（立即可行）

#### 1. 自動化提醒機制
```javascript
// 期限提醒功能
function checkDeadlines() {
    const overdueCase = getCasesNearDeadline(3); // 3天內到期
    
    overdueCase.forEach(case => {
        // 發送郵件提醒
        sendEmailReminder(case.handler, case);
        
        // 系統內通知
        createSystemNotification(case.handler, 
            `案件 ${case.caseNo} 即將到期`);
        
        // 主管副本
        if (case.daysUntilDeadline <= 1) {
            notifySupervisor(case);
        }
    });
}
```

#### 2. 批次處理功能
- 批次狀態更新
- 批次文書產生
- 批次通知發送

#### 3. 範本管理
- 常用文書範本
- 標準作業程序
- 檢查清單範本

### 中期優化（3-6個月）

#### 1. 工作流引擎導入
- 視覺化流程設計
- 彈性流程調整
- 自動化規則引擎

#### 2. 智慧分案系統
- AI輔助分案建議
- 工作負載平衡
- 專長匹配機制

#### 3. 行動化作業
- 現場勘查APP
- 離線作業支援
- 即時同步機制

### 長期優化（6-12個月）

#### 1. 智慧化違建偵測
- 衛星影像分析
- 無人機自動巡檢
- AI違建識別

#### 2. 預測性分析
- 違建熱點預測
- 處理時間預估
- 資源需求預測

#### 3. 民眾服務優化
- 線上進度查詢
- 主動式通知
- 智慧客服機器人

## 結論

新北市違章建築管理系統的業務流程展現了政府部門在城市管理上的專業與嚴謹：

### 流程優勢
- ✅ **全生命週期管理**：從發現到結案的完整追蹤
- ✅ **標準化作業**：61個狀態碼確保處理一致性
- ✅ **多部門協同**：打破部門藩籬的整合機制
- ✅ **證據保全完整**：支援法律程序的嚴謹要求
- ✅ **品質控制嚴密**：多層審核確保決策品質

### 持續改進方向
- 🎯 **自動化程度提升**：減少人工作業負擔
- 🎯 **智慧化功能導入**：運用新技術提升效能
- 🎯 **使用者體驗優化**：提供更友善的操作介面
- 🎯 **資料加值應用**：深化資料分析與決策支援

透過持續的流程優化與技術升級，系統將能更有效地支援新北市的違建管理工作，為市民創造更優質的居住環境。