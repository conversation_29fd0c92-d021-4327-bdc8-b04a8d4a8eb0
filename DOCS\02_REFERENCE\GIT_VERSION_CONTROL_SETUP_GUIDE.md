# Git 版本控制設定指南

## 📋 文件資訊
- **文件編號**: T1.3.1
- **文件目的**: 提供新北市違章建築管理系統 Git 版本控制設定的完整指南
- **適用對象**: 開發團隊、DevOps 工程師
- **建立日期**: 2025-07-05
- **版本**: 1.0

---

## 🎯 設定目標

本指南旨在協助團隊：
1. 建立適合 Legacy 系統的版本控制架構
2. 保護敏感資訊不被提交
3. 建立清晰的分支管理策略
4. 制定程式碼提交規範

---

## 📁 專案結構與 Git 初始化

### 1. 初始化 Git Repository

```bash
# 在專案根目錄執行
cd /D/apache-tomcat-9.0.98/webapps/src
git init

# 設定使用者資訊
git config user.name "開發者名稱"
git config user.email "<EMAIL>"

# 建立初始提交
git add README.md
git commit -m "Initial commit: 新北市違章建築管理系統"
```

### 2. 遠端儲存庫設定

```bash
# 新增遠端儲存庫（以 GitLab 為例）
git remote add origin https://gitlab.ntpc.gov.tw/building/violation-management-system.git

# 推送到遠端
git push -u origin main
```

---

## 🚫 .gitignore 配置

### 重要：必須排除的檔案類型

創建 `.gitignore` 檔案，內容如下：

```gitignore
# ===== 敏感資訊 =====
# 絕對不可提交的檔案
WEB-INF/site.properties
*.properties
!*.properties.template
config/prod/*
config/test/*
!config/dev/*

# 資料庫連線設定
**/DBConnection.java
**/DBConn*.java
connection.xml
datasource.xml

# 密碼和金鑰檔案
*.key
*.pem
*.p12
*.jks
*.keystore
secrets/
credentials/

# ===== 編譯和暫存檔案 =====
# Java 編譯檔案
*.class
*.jar
*.war
*.ear
target/
build/
out/

# JSP 編譯快取
work/
temp/
*.jsp.java

# ===== IDE 設定檔案 =====
# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr

# Eclipse
.classpath
.project
.settings/
bin/

# Visual Studio Code
.vscode/
*.code-workspace

# ===== 系統和暫存檔案 =====
# 作業系統
.DS_Store
Thumbs.db
desktop.ini

# 暫存檔案
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# ===== 上傳和報表檔案 =====
# 使用者上傳檔案
uploads/
uploaded_files/
user_uploads/

# 產生的報表
reports/generated/
export/
*.pdf
*.xls
*.xlsx

# ===== 備份檔案 =====
*.bak
*.backup
*.old
backup/
backups/

# ===== 測試資料 =====
test-data/
mock-data/
*.test.json
*.mock.xml

# ===== 特定於專案的排除 =====
# Quartz 排程器暫存
quartz/
.quartz/

# ArcGIS 快取
arcgis_cache/
gis_temp/

# JasperReports 暫存
jasper_temp/
*.jasper
!templates/*.jasper
```

### 創建配置模板檔案

為了讓其他開發者知道如何設定，創建模板檔案：

```bash
# 創建 site.properties 模板
cp WEB-INF/site.properties WEB-INF/site.properties.template

# 編輯模板，將敏感資訊替換為佔位符
# 例如：
# DBConn.password=S!@h@202203 改為 DBConn.password=YOUR_DB_PASSWORD_HERE
```

---

## 🌿 分支管理策略

### 主要分支結構

```
main (或 master)
├── develop
├── feature/*
├── bugfix/*
├── hotfix/*
└── release/*
```

### 分支說明

1. **main/master**: 生產環境程式碼
   - 只接受來自 release 和 hotfix 的合併
   - 每次合併都需要打標籤（tag）

2. **develop**: 開發整合分支
   - 所有功能開發的基礎分支
   - 定期合併到 release 分支

3. **feature/***: 功能開發分支
   - 命名範例：`feature/jsp-modernization`
   - 從 develop 分支建立，完成後合併回 develop

4. **bugfix/***: 錯誤修復分支
   - 命名範例：`bugfix/fix-login-validation`
   - 從 develop 分支建立

5. **hotfix/***: 緊急修復分支
   - 命名範例：`hotfix/security-patch-001`
   - 從 main 分支建立，修復後同時合併到 main 和 develop

6. **release/***: 發布準備分支
   - 命名範例：`release/v1.2.0`
   - 從 develop 分支建立，只允許修復錯誤

### 分支操作指令

```bash
# 創建功能分支
git checkout -b feature/user-authentication develop

# 創建錯誤修復分支
git checkout -b bugfix/fix-status-code-logic develop

# 創建熱修復分支
git checkout -b hotfix/emergency-security-fix main

# 合併分支（使用 --no-ff 保留分支歷史）
git checkout develop
git merge --no-ff feature/user-authentication
```

---

## 📝 提交訊息規範

### 提交訊息格式

```
<類型>(<範圍>): <主題>

<詳細描述>

<頁腳>
```

### 類型（Type）

- **feat**: 新功能
- **fix**: 錯誤修復
- **docs**: 文件更新
- **style**: 程式碼格式調整（不影響功能）
- **refactor**: 重構（不新增功能或修復錯誤）
- **perf**: 效能優化
- **test**: 測試相關
- **chore**: 建構過程或輔助工具的變動
- **security**: 安全性修復

### 範例

```bash
# 新功能
git commit -m "feat(認定): 新增案件自動分案功能

- 實作基於地理位置的自動分案邏輯
- 新增分案規則設定介面
- 支援手動調整分案結果

Closes #123"

# 錯誤修復
git commit -m "fix(狀態碼): 修正 3xx 系列狀態碼轉換錯誤

修正從認定階段轉換到排拆階段時，狀態碼設定錯誤的問題。
原本誤將 234 轉換為 444，應轉換為 344。

Fixes #456"

# 安全性修復
git commit -m "security(配置): 移除硬編碼的資料庫密碼

- 將 site.properties 中的密碼移至環境變數
- 新增 EnvironmentConfig 類別處理環境變數
- 更新部署文件說明環境變數設定

Security: CVE-2024-XXXX"
```

---

## 🔄 工作流程

### 1. 日常開發流程

```bash
# 1. 從最新的 develop 分支創建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-report-module

# 2. 進行開發並定期提交
git add .
git commit -m "feat(報表): 實作違建統計報表基礎架構"

# 3. 推送到遠端
git push origin feature/new-report-module

# 4. 創建 Pull Request/Merge Request
# 在 GitLab/GitHub 上操作

# 5. 程式碼審查通過後合併
git checkout develop
git merge --no-ff feature/new-report-module
git push origin develop
```

### 2. 發布流程

```bash
# 1. 從 develop 創建 release 分支
git checkout -b release/v2.1.0 develop

# 2. 進行發布前的最後調整
# - 更新版本號
# - 最後的錯誤修復
# - 更新 CHANGELOG

# 3. 合併到 main 並打標籤
git checkout main
git merge --no-ff release/v2.1.0
git tag -a v2.1.0 -m "Release version 2.1.0"
git push origin main --tags

# 4. 合併回 develop
git checkout develop
git merge --no-ff release/v2.1.0
git push origin develop
```

---

## 🛡️ 安全性最佳實踐

### 1. 敏感資訊處理

```bash
# 如果不小心提交了敏感資訊，立即執行：
git filter-branch --force --index-filter \
  "git rm --cached --ignore-unmatch path/to/sensitive-file" \
  --prune-empty --tag-name-filter cat -- --all

# 或使用 BFG Repo-Cleaner（更簡單）
bfg --delete-files site.properties
```

### 2. 提交前檢查

創建 `.gitmessage` 檔案作為提交訊息模板：

```
# <類型>(<範圍>): <主題>

# 為什麼要進行這個變更？

# 這個變更做了什麼？

# 是否有任何副作用或其他需要注意的地方？
```

設定為預設模板：
```bash
git config commit.template .gitmessage
```

### 3. Pre-commit Hook

創建 `.git/hooks/pre-commit` 檔案：

```bash
#!/bin/sh
# 檢查是否有敏感檔案被提交

FILES_PATTERN='\.properties$|\.key$|\.pem$|password|secret|credential'
FORBIDDEN_FILES=$(git diff --cached --name-only | grep -E "$FILES_PATTERN")

if [ -n "$FORBIDDEN_FILES" ]; then
    echo "錯誤：偵測到可能包含敏感資訊的檔案："
    echo "$FORBIDDEN_FILES"
    echo "請確認這些檔案是否應該被提交。"
    exit 1
fi
```

---

## 📚 團隊協作指南

### 1. Pull Request 檢查清單

在提交 PR 前，請確認：

- [ ] 程式碼符合團隊編碼規範
- [ ] 所有測試都通過
- [ ] 沒有包含敏感資訊
- [ ] 提交訊息清晰明確
- [ ] 更新了相關文件
- [ ] 沒有不必要的 console.log 或 System.out.println

### 2. 程式碼審查重點

- 安全性：是否有 SQL 注入風險？
- 效能：是否有不必要的資料庫查詢？
- 可維護性：程式碼是否易於理解？
- 錯誤處理：是否有適當的異常處理？

### 3. 衝突解決

```bash
# 當發生合併衝突時
git status  # 查看衝突檔案
# 手動編輯衝突檔案
git add <resolved-file>
git commit -m "fix: 解決合併衝突"
```

---

## 🚀 快速開始檢查清單

新成員加入專案時的設定步驟：

1. [ ] Clone 專案儲存庫
2. [ ] 設定 git 使用者資訊
3. [ ] 複製 `*.properties.template` 檔案並設定環境變數
4. [ ] 確認 `.gitignore` 正常運作
5. [ ] 了解分支管理策略
6. [ ] 閱讀提交訊息規範
7. [ ] 設定 pre-commit hook

---

## 📞 支援與問題

如有任何問題，請聯繫：
- DevOps 團隊：<EMAIL>
- 專案負責人：<EMAIL>

---

*本文件由【D】Claude Code - DevOps與技術架構任務組撰寫*
*任務編號：T1.3.1*
*完成日期：2025-07-05*