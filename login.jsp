<%--JSP Page Init @1-09C5D62D--%>
<%@page import="com.codecharge.*,com.codecharge.components.*,com.codecharge.util.*,com.codecharge.events.*,com.codecharge.feature.*,com.codecharge.db.*,com.codecharge.validation.*,java.util.*,java.io.*,com.codecharge.util.cache.CacheEvent,com.codecharge.util.cache.ICache,com.codecharge.template.*"%>
<%if ((new loginServiceChecker()).check(request, response, getServletContext())) return;%>
<%@page contentType="text/html; charset=UTF-8"%>
<%@taglib uri="/ccstags" prefix="ccs"%>
<%--End JSP Page Init--%>

<%--Page Body @1-A421775C--%>
<%@include file="loginHandlers.jsp"%>
<%
    if (!loginModel.isVisible()) return;
    if (loginParent != null) {
        if (!loginParent.getChild(loginModel.getName()).isVisible()) return;
    }
    pageContext.setAttribute("parent", loginModel);
    pageContext.setAttribute("page", loginModel);
    loginModel.fireOnInitializeViewEvent(new Event());
    loginModel.fireBeforeShowEvent(new Event());

    Page curPage = (Page) loginModel;
    String pathToRoot = curPage.getAttribute(Page.PAGE_ATTRIBUTE_PATH_TO_ROOT).toString();

    // Include once for client scripts
    String scripts = "";//"|js/jquery/jquery.js|js/jquery/event-manager.js|js/jquery/selectors.js|";
    request.setAttribute("parentPathToRoot", pathToRoot);
    request.setAttribute("scriptIncludes", scripts);
    ((ModelAttribute) curPage.getAttribute("scriptIncludes"))
            .setValue("<!--[scriptIncludes][begin]--><!--[scriptIncludes][end]-->");

    if (!loginModel.isVisible()) return;
    
%>
<%--End Page Body--%>

<%--JSP Page Content @1-27A3D717--%>
<html lang="zh-Hant">
<head>
<ccs:meta header="Content-Type"/>
<title>新北市違章建築管理系統</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<!--
<link rel="stylesheet" type="text/css" href="//fonts.googleapis.com/earlyaccess/cwtexhei.css">
-->
<link rel="stylesheet" href="javascript/bootstrap.5.3.3/css/bootstrap.min.css?1140716" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<%-- <link rel="stylesheet" href="javascript/bootstrap-3.3.7-dist/css/bootstrap-theme.min.css"> --%>
<link rel="stylesheet" href="font-awesome/css/font-awesome.css?1140716">
<link rel="stylesheet" href="css/animate.css">

<!-- jQuery and dependencies - moved before usage -->
<script src="javascript/jquery-3.7.1.min.js?1140716" integrity="sha384-1H217gwSVyLSIfaLxHbE7dRb3v4mYCKbpQvzx0cegeju1MVsGrX5xXxAvs/HgeFs" crossorigin="anonymous"></script>
<script src="javascript/jquery.blockUI.min.js?1140716"></script>
<script src="javascript/md5.js?1140716"></script>
<script language="JavaScript" src="sit.js?1100624" type="text/javascript"></script>
<script language="JavaScript" src="login_fgtpw.js?110120804" type="text/javascript"></script>

<!-- login CSS files -->
<link rel="shortcut icon" type="image/x-icon" href="img/logo.ico">
<link rel="stylesheet" href="css/login.css?110120801">
<script language="JavaScript" type="text/javascript">
//Begin CCS script
//Include Common JSFunctions @1-A7198D31
</script>
<!-- 【安全修復】移除不存在的 ClientI18N.jsp 資源 -->
<script type="text/javascript">
//End Include Common JSFunctions

//Include User Scripts @1-92024ECE
</script>
<ccs:attribute owner='page' name='scriptIncludes'/>
<script type="text/javascript">
//End Include User Scripts

//Common Script Start @1-8BFA436B
jQuery(function ($) {
    var features = { };
    var actions = { };
    var params = { };
//End Common Script Start

//bsccodeOnSubmit Event Start @-BBFC0DAD
    actions["bsccodeOnSubmit"] = function (eventType, parameters) {
        var result = true;
//End bsccodeOnSubmit Event Start

//Custom Code @22-2A29BDB7
                 
if (is_old_broser) {
    document.getElementById("Button1").disabled = true;
    alert("您目前使用的網頁瀏覽器未支援本系統所有功能, 請改用Chrome, Edge或Safari網頁瀏覽器.");
    result =false;
    return false;
}

//End Custom Code

//bsccodeOnSubmit Event End @-A5B9ECB8
        return result;
    };
//End bsccodeOnSubmit Event End

//Event Binding @1-90010274
    $('#bsccode').on("submit", actions["bsccodeOnSubmit"]);
//End Event Binding

//Common Script End @1-562554DE
});
//End Common Script End

//End CCS script
</script>
<link rel="stylesheet" type="text/css" href="Styles/<ccs:page property='CCS_Style'/>/Style_doctype.css?1091017">
<script language="JavaScript" type="text/javascript">
    var is_old_broser = true;
    //$(document).ready(function () { 
    window.onload = function() { 
        //瀏覽器檢核
        var BrowserDetect = {
            init: function () {
                this.browser = this.searchString(this.dataBrowser) || "Other";
                this.version = this.searchVersion(navigator.userAgent) || this.searchVersion(navigator.appVersion) || "Unknown";
            },
            searchString: function (data) {
                for (var i = 0; i < data.length; i++) {
                    var dataString = data[i].string;
                    this.versionSearchString = data[i].subString;

                    if (dataString.indexOf(data[i].subString) !== -1) {
                        return data[i].identity;
                    }
                }
            },
            searchVersion: function (dataString) {
                var index = dataString.indexOf(this.versionSearchString);
                if (index === -1) {
                    return;
                }

                var rv = dataString.indexOf("rv:");
                if (this.versionSearchString === "Trident" && rv !== -1) {
                    return parseFloat(dataString.substring(rv + 3));
                } else {
                    return parseFloat(dataString.substring(index + this.versionSearchString.length + 1));
                }
            },

            dataBrowser: [
                { string: navigator.userAgent, subString: "Chrome", identity: "Chrome" },
                { string: navigator.userAgent, subString: "MSIE", identity: "Explorer" },
                { string: navigator.userAgent, subString: "Trident", identity: "Explorer" },
                { string: navigator.userAgent, subString: "Firefox", identity: "Firefox" },
                { string: navigator.userAgent, subString: "Safari", identity: "Safari" },
                { string: navigator.userAgent, subString: "Opera", identity: "Opera" }
            ]

        };

        BrowserDetect.init();

        var ua = navigator.userAgent.toLowerCase();
        var browser_url = "";
        var is_old_version = false;
        if (BrowserDetect.browser == "Chrome") {
                is_old_broser = false;
            if (BrowserDetect.version <= "73"){
                is_old_version = true;
                          }
    
        } else if (BrowserDetect.browser == "Explorer") {
            /*
            if (BrowserDetect.version <= "10") {
                is_old_broser = true;  
            }
            
            is_old_broser = true;
            */
        } else if (BrowserDetect.browser == "Firefox") {
                /*
            if (BrowserDetect.version <= "25"){
                is_old_broser = true;
            }
            
            is_old_broser = true;.
            */
        } else if (BrowserDetect.browser == "Safari") {
             is_old_broser = false;
            if ( BrowserDetect.version <= "600"){
                is_old_broser = true;
            }
            
            
        }
         
        if (is_old_broser) {
            //$("div .login_wrap").hide();
            //$(".BrowserErr").show();
            document.getElementById("Button1").disabled = true;
            //alert("您使用的瀏覽器太舊, 無法使用本系統");
            alert("您目前使用的網頁瀏覽器未支援本系統所有功能, 請改用Chrome, Edge或Safari網頁瀏覽器.");
            return;
        }else if(is_old_version){
                document.getElementById("Button1").disabled = true;
                alert("您使用的瀏覽器太舊, 無法使用本系統." +BrowserDetect.browser+BrowserDetect.version);
                return;
        }


        $('#acnt').on('change invalid', function () {
            var textfield = $(this).get(0);
            textfield.setCustomValidity('');

            if (!textfield.validity.valid) {
                textfield.setCustomValidity('請輸入帳號');
            }
        });

        $('#psd').on('change invalid', function () {
            var textfield = $(this).get(0);
            textfield.setCustomValidity('');

            if (!textfield.validity.valid) {
                textfield.setCustomValidity('請輸入密碼');
            }
        });
 
                var imgs =["loginBackground_1.jpg", "loginBackground_2.jpg", "loginBackground_3.jpg", "loginBackground_4.jpg"
    , "loginBackground_5.jpg", "loginBackground_6.jpg", "loginBackground_7.jpg", "loginBackground_8.jpg", "loginBackground_9.jpg"];   
                var x = 0;
    
            $(".img-dot").on("click", function() {
                var indx = $(this).attr("img-vale");
                 $(".img-dot").removeClass("img-dot-white").addClass("img-dot-gray");
                 $(this).removeClass("img-dot-gray").addClass("img-dot-white");
                 
                 document.getElementById("ezekloginbody").style.background="url(./img/loginBackground/"+imgs[indx]+"?1101116) center center/cover no-repeat";
                 //document.getElementById("ezekloginbody").style.backgroundSize="cover";
                });
    
    
    }// END window.onload
    
    function spd(_this){
        var ptp = $('#psd').attr("type");
        if(ptp=="password"){
                $('#psd').attr("type", "text");
                $(_this).addClass("fa-eye").removeClass("fa-eye-slash");
        }else{
                $('#psd').attr("type", "password");
                $(_this).addClass("fa-eye-slash").removeClass("fa-eye");
        }
    }
</script>
<style>
.img-dot-div{
bottom: 0px;
    position: absolute;
    margin-left: auto;
    width: 100%;
    text-align: center;
}
.img-dot{

margin-right: 20px;
border-radius: 20px;
cursor:pointer;
}
.img-dot-white{
width: 14px;
height: 14px;
background-color: white;
}
.img-dot-gray{
width: 12px;
height: 12px;
background-color: #ddd;
}
.forget-pwd{
color: #1a73e8;
cursor:pointer;
font-size: 14px;
font-weight: 500;
}
.container_barckground{
   padding:20px;

    border-radius: 10px;
    background-color: rgba(221, 221, 221, 0.6);
}
</style>
</head>
<body id="ezekloginbody" class="header">
<div class="container text-center loginscreen animate__animated animate__fadeInDown login_container">
  <div class="justify-content-center align-items-center">
    <div class="login_wrap">
      <h3 class="title">新北市政府違章建築拆除大隊</h3>
      <div style="width: 100%; border-bottom: 1px double #474747;">
      </div>
      <h2 class="title">違章建築管理系統</h2>
      <ccs:record name='bsccode'>
      <form id="bsccode" method="post" name="<ccs:form_name/>" action="<ccs:form_action/>">
        <div class="mb-3">
          <div class="input-group">
            <span id="errorMessage" style="font-size: 16px; color: red;">
            <ccs:error_block><ccs:error_text/></ccs:error_block></span>
          </div>
        </div>
        <div class="mb-3">
          <div class="input-group">
            <span class="input-group-text"><i class="fa fa-user"></i></span>
            <input type="text" id="acnt" class="form-control" style="font-size: 16px;" value="<ccs:control name='acnt'/>" name="<ccs:control name='acnt' property='name'/>" required="" placeholder="請輸入帳號">
          </div>
        </div>
        <div class="mb-3">
          <div class="input-group">
            <span class="input-group-text"><i class="fa fa-key"></i></span>
            <input type="password" id="psd" class="form-control" style="font-size: 16px;" value="<ccs:control name='psd'/>" name="<ccs:control name='psd' property='name'/>" required="" placeholder="請輸入密碼" autocomplete="off">
            <span class="input-group-text" style="background: none; color: black;">
              <i onclick="spd(this);" class="fa fa-eye-slash"></i>
            </span>
          </div>
        </div>
        <div class="mb-3">
          <div class="input-group">
            <input type="text" id="bsccodecheckcode" class="form-control" style="font-size: 16px; width: 60%; display: inline-block;" maxlength="4" value="<ccs:control name='checkcode'/>" name="<ccs:control name='checkcode' property='name'/>" placeholder="圖形驗證碼" autocomplete="off">
            <img id="checkimage" style="height: 32px; width: 37%; padding-left: 10%; margin-left: 2%; display: inline-block; padding-right: 10%; background-color: #333; border-radius: 4px;" alt="圖形驗證碼" src="loginCaptcha.jsp">
          </div>
        </div>
        <ccs:button name='Button1'>
          <input type="submit" onclick="" id="Button1" class="btn btn-primary-login  w-100 mb-3" style="font-size: 18px;" value="登入" name="<ccs:control name='Button1' property='name'/>">
        </ccs:button>
        <label class="forget-pwd" onclick="goFgtSetting()">忘記密碼？</label>
        <h5 class="loginFooter">
        <%-- 維護廠商：系統上線　02-87713258<br> --%>
        <span style="font-size: 8px;">[說明]請使用Chrome, Edge或Safari網頁瀏覽器操作本系統</span></h5>
      </form>
      </ccs:record>
    </div>
</div>
<div class="img-dot-div" style="display:none;">
  <label img-vale="0" class="img-dot img-dot-white">&nbsp;</label><label img-vale="1" class="img-dot img-dot-gray">&nbsp;</label><label img-vale="2" class="img-dot img-dot-gray">&nbsp;</label>
</div>
</body>
</html>

<%--End JSP Page Content--%>

<%--JSP Page BeforeOutput @1-C68DA25C--%>
<%loginModel.fireBeforeOutputEvent();%>
<%--End JSP Page BeforeOutput--%>

<%--JSP Page Unload @1-29FB5CA2--%>
<%loginModel.fireBeforeUnloadEvent();%>
<%--End JSP Page Unload--%>

