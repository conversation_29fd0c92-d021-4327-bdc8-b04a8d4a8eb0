# Backup and Disaster Recovery Monitoring & Alerting System
# Author: System Administrator
# Purpose: Monitor backup jobs, storage, and system health with alerting

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("monitor", "alert", "report", "health_check")]
    [string]$Operation = "monitor",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigFile = ".\config\monitoring_config.json",
    
    [Parameter(Mandatory=$false)]
    [switch]$ContinuousMode = $false,
    
    [Parameter(Mandatory=$false)]
    [int]$IntervalMinutes = 5,
    
    [Parameter(Mandatory=$false)]
    [switch]$SendAlerts = $true
)

# Import required modules
Import-Module -Name ".\modules\BackupLogger.psm1" -Force
Import-Module -Name ".\modules\BackupMetrics.psm1" -Force
Import-Module -Name ".\modules\BackupNotification.psm1" -Force

# Global variables
$script:MonitoringStartTime = Get-Date
$script:Logger = $null
$script:MonitoringMetrics = @{}
$script:AlertHistory = @()
$script:HealthStatus = @{}

# Alert severity levels
$script:AlertSeverity = @{
    "Critical" = 1
    "Warning" = 2
    "Info" = 3
    "Success" = 4
}

# Initialize logging
function Initialize-MonitoringLogging {
    param(
        [string]$LogPath = ".\logs\monitoring_$(Get-Date -Format 'yyyyMMdd').log"
    )
    
    $script:Logger = New-BackupLogger -LogPath $LogPath -LogLevel "INFO"
    $script:Logger.Info("Monitoring & Alerting system started - Operation: $Operation")
}

# Load monitoring configuration
function Get-MonitoringConfiguration {
    param(
        [string]$ConfigFile
    )
    
    if (!(Test-Path $ConfigFile)) {
        throw "Monitoring configuration file not found: $ConfigFile"
    }
    
    try {
        $config = Get-Content $ConfigFile -Raw | ConvertFrom-Json
        $script:Logger.Info("Monitoring configuration loaded successfully")
        return $config
    }
    catch {
        throw "Failed to load monitoring configuration: $_"
    }
}

# Monitor backup jobs
function Monitor-BackupJobs {
    param(
        [hashtable]$Config
    )
    
    $script:Logger.Info("Monitoring backup jobs...")
    
    $backupStatus = @{
        "postgresql" = @{}
        "sqlserver" = @{}
        "application" = @{}
        "overall_health" = "unknown"
    }
    
    try {
        # Monitor PostgreSQL backups
        $backupStatus.postgresql = Monitor-PostgreSQLBackups -Config $Config
        
        # Monitor SQL Server backups
        $backupStatus.sqlserver = Monitor-SqlServerBackups -Config $Config
        
        # Monitor application backups
        $backupStatus.application = Monitor-ApplicationBackups -Config $Config
        
        # Determine overall backup health
        $backupStatus.overall_health = Determine-BackupHealth -BackupStatus $backupStatus
        
        # Store monitoring metrics
        $script:MonitoringMetrics["backup_jobs"] = $backupStatus
        
        # Generate alerts if needed
        if ($SendAlerts) {
            Generate-BackupAlerts -BackupStatus $backupStatus -Config $Config
        }
        
        $script:Logger.Info("Backup job monitoring completed - Overall health: $($backupStatus.overall_health)")
        
        return $backupStatus
    }
    catch {
        $script:Logger.Error("Backup job monitoring failed: $_")
        throw
    }
}

# Monitor PostgreSQL backups
function Monitor-PostgreSQLBackups {
    param(
        [hashtable]$Config
    )
    
    $pgStatus = @{
        "last_full_backup" = @{}
        "last_incremental_backup" = @{}
        "backup_success_rate" = 0
        "storage_usage" = @{}
        "health" = "unknown"
    }
    
    try {
        # Check latest full backup
        $fullBackupPath = "$($Config.backup.base_path)\full"
        if (Test-Path $fullBackupPath) {
            $latestFullBackup = Get-ChildItem -Path $fullBackupPath -Filter "*.sql*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestFullBackup) {
                $backupAge = (Get-Date) - $latestFullBackup.LastWriteTime
                $pgStatus.last_full_backup = @{
                    "file_name" = $latestFullBackup.Name
                    "file_size_mb" = [math]::Round($latestFullBackup.Length / 1MB, 2)
                    "backup_time" = $latestFullBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.postgresql.full_backup_max_age_hours
                }
            }
        }
        
        # Check latest incremental backup
        $incBackupPath = "$($Config.backup.base_path)\wal"
        if (Test-Path $incBackupPath) {
            $latestIncBackup = Get-ChildItem -Path $incBackupPath -Recurse -Filter "*.wal" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestIncBackup) {
                $backupAge = (Get-Date) - $latestIncBackup.LastWriteTime
                $pgStatus.last_incremental_backup = @{
                    "file_name" = $latestIncBackup.Name
                    "file_size_mb" = [math]::Round($latestIncBackup.Length / 1MB, 2)
                    "backup_time" = $latestIncBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.postgresql.incremental_backup_max_age_hours
                }
            }
        }
        
        # Calculate backup success rate
        $pgStatus.backup_success_rate = Get-BackupSuccessRate -BackupType "postgresql" -Config $Config
        
        # Check storage usage
        $pgStatus.storage_usage = Get-BackupStorageUsage -BackupPath $fullBackupPath
        
        # Determine PostgreSQL backup health
        $pgStatus.health = Determine-PostgreSQLBackupHealth -Status $pgStatus -Config $Config
        
        return $pgStatus
    }
    catch {
        $script:Logger.Error("PostgreSQL backup monitoring failed: $_")
        return @{
            "health" = "error"
            "error" = $_.Exception.Message
        }
    }
}

# Monitor SQL Server backups
function Monitor-SqlServerBackups {
    param(
        [hashtable]$Config
    )
    
    $sqlStatus = @{
        "last_full_backup" = @{}
        "last_differential_backup" = @{}
        "last_log_backup" = @{}
        "backup_success_rate" = 0
        "storage_usage" = @{}
        "health" = "unknown"
    }
    
    try {
        # Check latest full backup
        $fullBackupPath = "$($Config.backup.base_path)\sqlserver\full"
        if (Test-Path $fullBackupPath) {
            $latestFullBackup = Get-ChildItem -Path $fullBackupPath -Filter "*.bak*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestFullBackup) {
                $backupAge = (Get-Date) - $latestFullBackup.LastWriteTime
                $sqlStatus.last_full_backup = @{
                    "file_name" = $latestFullBackup.Name
                    "file_size_mb" = [math]::Round($latestFullBackup.Length / 1MB, 2)
                    "backup_time" = $latestFullBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.sqlserver.full_backup_max_age_hours
                }
            }
        }
        
        # Check latest differential backup
        $diffBackupPath = "$($Config.backup.base_path)\sqlserver\differential"
        if (Test-Path $diffBackupPath) {
            $latestDiffBackup = Get-ChildItem -Path $diffBackupPath -Filter "*.bak*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestDiffBackup) {
                $backupAge = (Get-Date) - $latestDiffBackup.LastWriteTime
                $sqlStatus.last_differential_backup = @{
                    "file_name" = $latestDiffBackup.Name
                    "file_size_mb" = [math]::Round($latestDiffBackup.Length / 1MB, 2)
                    "backup_time" = $latestDiffBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.sqlserver.differential_backup_max_age_hours
                }
            }
        }
        
        # Check latest log backup
        $logBackupPath = "$($Config.backup.base_path)\sqlserver\log"
        if (Test-Path $logBackupPath) {
            $latestLogBackup = Get-ChildItem -Path $logBackupPath -Filter "*.trn*" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestLogBackup) {
                $backupAge = (Get-Date) - $latestLogBackup.LastWriteTime
                $sqlStatus.last_log_backup = @{
                    "file_name" = $latestLogBackup.Name
                    "file_size_mb" = [math]::Round($latestLogBackup.Length / 1MB, 2)
                    "backup_time" = $latestLogBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.sqlserver.log_backup_max_age_hours
                }
            }
        }
        
        # Calculate backup success rate
        $sqlStatus.backup_success_rate = Get-BackupSuccessRate -BackupType "sqlserver" -Config $Config
        
        # Check storage usage
        $sqlStatus.storage_usage = Get-BackupStorageUsage -BackupPath $fullBackupPath
        
        # Determine SQL Server backup health
        $sqlStatus.health = Determine-SqlServerBackupHealth -Status $sqlStatus -Config $Config
        
        return $sqlStatus
    }
    catch {
        $script:Logger.Error("SQL Server backup monitoring failed: $_")
        return @{
            "health" = "error"
            "error" = $_.Exception.Message
        }
    }
}

# Monitor application backups
function Monitor-ApplicationBackups {
    param(
        [hashtable]$Config
    )
    
    $appStatus = @{
        "last_full_backup" = @{}
        "last_incremental_backup" = @{}
        "last_config_backup" = @{}
        "backup_success_rate" = 0
        "storage_usage" = @{}
        "health" = "unknown"
    }
    
    try {
        # Check latest full backup
        $fullBackupPath = "$($Config.backup.base_path)\application\full"
        if (Test-Path $fullBackupPath) {
            $latestFullBackup = Get-ChildItem -Path $fullBackupPath -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestFullBackup) {
                $backupAge = (Get-Date) - $latestFullBackup.LastWriteTime
                $appStatus.last_full_backup = @{
                    "file_name" = $latestFullBackup.Name
                    "file_size_mb" = [math]::Round($latestFullBackup.Length / 1MB, 2)
                    "backup_time" = $latestFullBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.application.full_backup_max_age_hours
                }
            }
        }
        
        # Check latest incremental backup
        $incBackupPath = "$($Config.backup.base_path)\application\incremental"
        if (Test-Path $incBackupPath) {
            $latestIncBackup = Get-ChildItem -Path $incBackupPath -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestIncBackup) {
                $backupAge = (Get-Date) - $latestIncBackup.LastWriteTime
                $appStatus.last_incremental_backup = @{
                    "file_name" = $latestIncBackup.Name
                    "file_size_mb" = [math]::Round($latestIncBackup.Length / 1MB, 2)
                    "backup_time" = $latestIncBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.application.incremental_backup_max_age_hours
                }
            }
        }
        
        # Check latest configuration backup
        $configBackupPath = "$($Config.backup.base_path)\application\configuration"
        if (Test-Path $configBackupPath) {
            $latestConfigBackup = Get-ChildItem -Path $configBackupPath -Filter "*.zip" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
            if ($latestConfigBackup) {
                $backupAge = (Get-Date) - $latestConfigBackup.LastWriteTime
                $appStatus.last_config_backup = @{
                    "file_name" = $latestConfigBackup.Name
                    "file_size_mb" = [math]::Round($latestConfigBackup.Length / 1MB, 2)
                    "backup_time" = $latestConfigBackup.LastWriteTime
                    "age_hours" = [math]::Round($backupAge.TotalHours, 2)
                    "within_sla" = $backupAge.TotalHours -le $Config.sla.application.config_backup_max_age_hours
                }
            }
        }
        
        # Calculate backup success rate
        $appStatus.backup_success_rate = Get-BackupSuccessRate -BackupType "application" -Config $Config
        
        # Check storage usage
        $appStatus.storage_usage = Get-BackupStorageUsage -BackupPath $fullBackupPath
        
        # Determine application backup health
        $appStatus.health = Determine-ApplicationBackupHealth -Status $appStatus -Config $Config
        
        return $appStatus
    }
    catch {
        $script:Logger.Error("Application backup monitoring failed: $_")
        return @{
            "health" = "error"
            "error" = $_.Exception.Message
        }
    }
}

# Get backup success rate
function Get-BackupSuccessRate {
    param(
        [string]$BackupType,
        [hashtable]$Config
    )
    
    try {
        $metadataPath = "$($Config.backup.base_path)\metadata"
        $lookbackHours = 24
        
        if (Test-Path $metadataPath) {
            $metadataFiles = Get-ChildItem -Path $metadataPath -Filter "*$BackupType*report*.json" | Where-Object {
                $_.LastWriteTime -gt (Get-Date).AddHours(-$lookbackHours)
            }
            
            if ($metadataFiles.Count -gt 0) {
                $successCount = 0
                $totalCount = $metadataFiles.Count
                
                foreach ($file in $metadataFiles) {
                    try {
                        $report = Get-Content $file.FullName -Raw | ConvertFrom-Json
                        if ($report.backup_summary.overall_success -eq $true) {
                            $successCount++
                        }
                    }
                    catch {
                        # Skip invalid report files
                    }
                }
                
                return [math]::Round(($successCount / $totalCount) * 100, 2)
            }
        }
        
        return 0
    }
    catch {
        return 0
    }
}

# Get backup storage usage
function Get-BackupStorageUsage {
    param(
        [string]$BackupPath
    )
    
    try {
        if (Test-Path $BackupPath) {
            $files = Get-ChildItem -Path $BackupPath -Recurse -File
            $totalSize = ($files | Measure-Object -Property Length -Sum).Sum
            $fileCount = $files.Count
            
            # Get drive information
            $drive = (Get-Item $BackupPath).PSDrive
            $driveInfo = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "$($drive.Name):" }
            
            return @{
                "total_files" = $fileCount
                "total_size_mb" = [math]::Round($totalSize / 1MB, 2)
                "total_size_gb" = [math]::Round($totalSize / 1GB, 2)
                "drive_free_space_gb" = [math]::Round($driveInfo.FreeSpace / 1GB, 2)
                "drive_total_space_gb" = [math]::Round($driveInfo.Size / 1GB, 2)
                "drive_used_percent" = [math]::Round((($driveInfo.Size - $driveInfo.FreeSpace) / $driveInfo.Size) * 100, 2)
                "storage_health" = if ($driveInfo.FreeSpace / $driveInfo.Size -gt 0.1) { "healthy" } else { "warning" }
            }
        } else {
            return @{
                "error" = "Backup path not found"
                "storage_health" = "error"
            }
        }
    }
    catch {
        return @{
            "error" = $_.Exception.Message
            "storage_health" = "error"
        }
    }
}

# Determine backup health
function Determine-BackupHealth {
    param(
        [hashtable]$BackupStatus
    )
    
    $healthScores = @()
    
    foreach ($backupType in $BackupStatus.Keys) {
        if ($backupType -ne "overall_health") {
            $typeHealth = $BackupStatus[$backupType].health
            
            switch ($typeHealth) {
                "healthy" { $healthScores += 100 }
                "warning" { $healthScores += 70 }
                "critical" { $healthScores += 30 }
                "error" { $healthScores += 0 }
                default { $healthScores += 50 }
            }
        }
    }
    
    if ($healthScores.Count -gt 0) {
        $averageHealth = ($healthScores | Measure-Object -Average).Average
        
        if ($averageHealth -ge 90) {
            return "healthy"
        } elseif ($averageHealth -ge 70) {
            return "warning"
        } elseif ($averageHealth -ge 30) {
            return "critical"
        } else {
            return "error"
        }
    } else {
        return "unknown"
    }
}

# Determine PostgreSQL backup health
function Determine-PostgreSQLBackupHealth {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $healthFactors = @()
    
    # Check full backup recency
    if ($Status.last_full_backup.within_sla -eq $true) {
        $healthFactors += "full_backup_recent"
    } else {
        $healthFactors += "full_backup_old"
    }
    
    # Check incremental backup recency
    if ($Status.last_incremental_backup.within_sla -eq $true) {
        $healthFactors += "incremental_backup_recent"
    } else {
        $healthFactors += "incremental_backup_old"
    }
    
    # Check backup success rate
    if ($Status.backup_success_rate -ge 95) {
        $healthFactors += "high_success_rate"
    } elseif ($Status.backup_success_rate -ge 80) {
        $healthFactors += "medium_success_rate"
    } else {
        $healthFactors += "low_success_rate"
    }
    
    # Check storage health
    if ($Status.storage_usage.storage_health -eq "healthy") {
        $healthFactors += "storage_healthy"
    } else {
        $healthFactors += "storage_warning"
    }
    
    # Determine overall health
    $criticalIssues = $healthFactors | Where-Object { $_ -match "old|low|error" }
    $warningIssues = $healthFactors | Where-Object { $_ -match "medium|warning" }
    
    if ($criticalIssues.Count -gt 0) {
        return "critical"
    } elseif ($warningIssues.Count -gt 0) {
        return "warning"
    } else {
        return "healthy"
    }
}

# Determine SQL Server backup health
function Determine-SqlServerBackupHealth {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $healthFactors = @()
    
    # Check full backup recency
    if ($Status.last_full_backup.within_sla -eq $true) {
        $healthFactors += "full_backup_recent"
    } else {
        $healthFactors += "full_backup_old"
    }
    
    # Check differential backup recency
    if ($Status.last_differential_backup.within_sla -eq $true) {
        $healthFactors += "differential_backup_recent"
    } else {
        $healthFactors += "differential_backup_old"
    }
    
    # Check log backup recency
    if ($Status.last_log_backup.within_sla -eq $true) {
        $healthFactors += "log_backup_recent"
    } else {
        $healthFactors += "log_backup_old"
    }
    
    # Check backup success rate
    if ($Status.backup_success_rate -ge 95) {
        $healthFactors += "high_success_rate"
    } elseif ($Status.backup_success_rate -ge 80) {
        $healthFactors += "medium_success_rate"
    } else {
        $healthFactors += "low_success_rate"
    }
    
    # Check storage health
    if ($Status.storage_usage.storage_health -eq "healthy") {
        $healthFactors += "storage_healthy"
    } else {
        $healthFactors += "storage_warning"
    }
    
    # Determine overall health
    $criticalIssues = $healthFactors | Where-Object { $_ -match "old|low|error" }
    $warningIssues = $healthFactors | Where-Object { $_ -match "medium|warning" }
    
    if ($criticalIssues.Count -gt 0) {
        return "critical"
    } elseif ($warningIssues.Count -gt 0) {
        return "warning"
    } else {
        return "healthy"
    }
}

# Determine application backup health
function Determine-ApplicationBackupHealth {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $healthFactors = @()
    
    # Check full backup recency
    if ($Status.last_full_backup.within_sla -eq $true) {
        $healthFactors += "full_backup_recent"
    } else {
        $healthFactors += "full_backup_old"
    }
    
    # Check incremental backup recency
    if ($Status.last_incremental_backup.within_sla -eq $true) {
        $healthFactors += "incremental_backup_recent"
    } else {
        $healthFactors += "incremental_backup_old"
    }
    
    # Check configuration backup recency
    if ($Status.last_config_backup.within_sla -eq $true) {
        $healthFactors += "config_backup_recent"
    } else {
        $healthFactors += "config_backup_old"
    }
    
    # Check backup success rate
    if ($Status.backup_success_rate -ge 95) {
        $healthFactors += "high_success_rate"
    } elseif ($Status.backup_success_rate -ge 80) {
        $healthFactors += "medium_success_rate"
    } else {
        $healthFactors += "low_success_rate"
    }
    
    # Check storage health
    if ($Status.storage_usage.storage_health -eq "healthy") {
        $healthFactors += "storage_healthy"
    } else {
        $healthFactors += "storage_warning"
    }
    
    # Determine overall health
    $criticalIssues = $healthFactors | Where-Object { $_ -match "old|low|error" }
    $warningIssues = $healthFactors | Where-Object { $_ -match "medium|warning" }
    
    if ($criticalIssues.Count -gt 0) {
        return "critical"
    } elseif ($warningIssues.Count -gt 0) {
        return "warning"
    } else {
        return "healthy"
    }
}

# Generate backup alerts
function Generate-BackupAlerts {
    param(
        [hashtable]$BackupStatus,
        [hashtable]$Config
    )
    
    $script:Logger.Info("Generating backup alerts...")
    
    $alerts = @()
    
    # Check PostgreSQL backup alerts
    $pgAlerts = Generate-PostgreSQLAlerts -Status $BackupStatus.postgresql -Config $Config
    $alerts += $pgAlerts
    
    # Check SQL Server backup alerts
    $sqlAlerts = Generate-SqlServerAlerts -Status $BackupStatus.sqlserver -Config $Config
    $alerts += $sqlAlerts
    
    # Check application backup alerts
    $appAlerts = Generate-ApplicationAlerts -Status $BackupStatus.application -Config $Config
    $alerts += $appAlerts
    
    # Check overall backup health alerts
    $overallAlerts = Generate-OverallHealthAlerts -OverallHealth $BackupStatus.overall_health -Config $Config
    $alerts += $overallAlerts
    
    # Process and send alerts
    foreach ($alert in $alerts) {
        Process-Alert -Alert $alert -Config $Config
    }
    
    $script:Logger.Info("Generated $($alerts.Count) backup alerts")
    
    return $alerts
}

# Generate PostgreSQL alerts
function Generate-PostgreSQLAlerts {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $alerts = @()
    
    # Full backup age alert
    if ($Status.last_full_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Critical"
            "component" = "PostgreSQL"
            "message" = "PostgreSQL full backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_full_backup.age_hours
                "sla_threshold_hours" = $Config.sla.postgresql.full_backup_max_age_hours
                "last_backup_file" = $Status.last_full_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Incremental backup age alert
    if ($Status.last_incremental_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Warning"
            "component" = "PostgreSQL"
            "message" = "PostgreSQL incremental backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_incremental_backup.age_hours
                "sla_threshold_hours" = $Config.sla.postgresql.incremental_backup_max_age_hours
                "last_backup_file" = $Status.last_incremental_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Backup success rate alert
    if ($Status.backup_success_rate -lt 80) {
        $alerts += @{
            "type" = "backup_success_rate"
            "severity" = "Critical"
            "component" = "PostgreSQL"
            "message" = "PostgreSQL backup success rate is below threshold"
            "details" = @{
                "success_rate" = $Status.backup_success_rate
                "threshold" = 80
            }
            "timestamp" = Get-Date
        }
    }
    
    # Storage space alert
    if ($Status.storage_usage.storage_health -eq "warning") {
        $alerts += @{
            "type" = "storage_space"
            "severity" = "Warning"
            "component" = "PostgreSQL"
            "message" = "PostgreSQL backup storage space is running low"
            "details" = @{
                "drive_used_percent" = $Status.storage_usage.drive_used_percent
                "drive_free_space_gb" = $Status.storage_usage.drive_free_space_gb
            }
            "timestamp" = Get-Date
        }
    }
    
    return $alerts
}

# Generate SQL Server alerts
function Generate-SqlServerAlerts {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $alerts = @()
    
    # Full backup age alert
    if ($Status.last_full_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Critical"
            "component" = "SQL Server"
            "message" = "SQL Server full backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_full_backup.age_hours
                "sla_threshold_hours" = $Config.sla.sqlserver.full_backup_max_age_hours
                "last_backup_file" = $Status.last_full_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Differential backup age alert
    if ($Status.last_differential_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Warning"
            "component" = "SQL Server"
            "message" = "SQL Server differential backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_differential_backup.age_hours
                "sla_threshold_hours" = $Config.sla.sqlserver.differential_backup_max_age_hours
                "last_backup_file" = $Status.last_differential_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Log backup age alert
    if ($Status.last_log_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Critical"
            "component" = "SQL Server"
            "message" = "SQL Server log backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_log_backup.age_hours
                "sla_threshold_hours" = $Config.sla.sqlserver.log_backup_max_age_hours
                "last_backup_file" = $Status.last_log_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Backup success rate alert
    if ($Status.backup_success_rate -lt 80) {
        $alerts += @{
            "type" = "backup_success_rate"
            "severity" = "Critical"
            "component" = "SQL Server"
            "message" = "SQL Server backup success rate is below threshold"
            "details" = @{
                "success_rate" = $Status.backup_success_rate
                "threshold" = 80
            }
            "timestamp" = Get-Date
        }
    }
    
    return $alerts
}

# Generate application alerts
function Generate-ApplicationAlerts {
    param(
        [hashtable]$Status,
        [hashtable]$Config
    )
    
    $alerts = @()
    
    # Full backup age alert
    if ($Status.last_full_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Warning"
            "component" = "Application"
            "message" = "Application full backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_full_backup.age_hours
                "sla_threshold_hours" = $Config.sla.application.full_backup_max_age_hours
                "last_backup_file" = $Status.last_full_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    # Configuration backup age alert
    if ($Status.last_config_backup.within_sla -eq $false) {
        $alerts += @{
            "type" = "backup_age"
            "severity" = "Warning"
            "component" = "Application"
            "message" = "Application configuration backup is older than SLA threshold"
            "details" = @{
                "backup_age_hours" = $Status.last_config_backup.age_hours
                "sla_threshold_hours" = $Config.sla.application.config_backup_max_age_hours
                "last_backup_file" = $Status.last_config_backup.file_name
            }
            "timestamp" = Get-Date
        }
    }
    
    return $alerts
}

# Generate overall health alerts
function Generate-OverallHealthAlerts {
    param(
        [string]$OverallHealth,
        [hashtable]$Config
    )
    
    $alerts = @()
    
    switch ($OverallHealth) {
        "critical" {
            $alerts += @{
                "type" = "overall_health"
                "severity" = "Critical"
                "component" = "Backup System"
                "message" = "Overall backup system health is critical"
                "details" = @{
                    "health_status" = $OverallHealth
                }
                "timestamp" = Get-Date
            }
        }
        "error" {
            $alerts += @{
                "type" = "overall_health"
                "severity" = "Critical"
                "component" = "Backup System"
                "message" = "Overall backup system health shows errors"
                "details" = @{
                    "health_status" = $OverallHealth
                }
                "timestamp" = Get-Date
            }
        }
        "warning" {
            $alerts += @{
                "type" = "overall_health"
                "severity" = "Warning"
                "component" = "Backup System"
                "message" = "Overall backup system health shows warnings"
                "details" = @{
                    "health_status" = $OverallHealth
                }
                "timestamp" = Get-Date
            }
        }
    }
    
    return $alerts
}

# Process individual alert
function Process-Alert {
    param(
        [hashtable]$Alert,
        [hashtable]$Config
    )
    
    # Add alert to history
    $script:AlertHistory += $Alert
    
    # Log alert
    $script:Logger.Info("ALERT [$($Alert.severity)] $($Alert.component): $($Alert.message)")
    
    # Send alert notification
    Send-AlertNotification -Alert $Alert -Config $Config
}

# Send alert notification
function Send-AlertNotification {
    param(
        [hashtable]$Alert,
        [hashtable]$Config
    )
    
    try {
        # Email notification
        if ($Config.notifications.email.enabled -eq $true) {
            Send-EmailAlert -Alert $Alert -Config $Config.notifications.email
        }
        
        # Slack notification
        if ($Config.notifications.slack.enabled -eq $true) {
            Send-SlackAlert -Alert $Alert -Config $Config.notifications.slack
        }
        
        # SMS notification (for critical alerts)
        if ($Config.notifications.sms.enabled -eq $true -and $Alert.severity -eq "Critical") {
            Send-SmsAlert -Alert $Alert -Config $Config.notifications.sms
        }
        
        # Windows Event Log
        Write-EventLog -LogName "Application" -Source "BMS Backup Monitor" -EventId 1001 -EntryType $(if ($Alert.severity -eq "Critical") { "Error" } else { "Warning" }) -Message "$($Alert.component): $($Alert.message)"
    }
    catch {
        $script:Logger.Error("Failed to send alert notification: $_")
    }
}

# Send email alert
function Send-EmailAlert {
    param(
        [hashtable]$Alert,
        [hashtable]$Config
    )
    
    try {
        $subject = "BMS Backup Alert - $($Alert.severity): $($Alert.component)"
        $body = @"
Backup System Alert

Severity: $($Alert.severity)
Component: $($Alert.component)
Message: $($Alert.message)
Timestamp: $($Alert.timestamp)

Details:
$($Alert.details | ConvertTo-Json -Depth 3)

Please investigate and take appropriate action.

--
BMS Backup Monitoring System
"@
        
        $smtpClient = New-Object System.Net.Mail.SmtpClient($Config.smtp_server, $Config.smtp_port)
        $smtpClient.EnableSsl = $Config.use_ssl
        $smtpClient.Credentials = New-Object System.Net.NetworkCredential($Config.username, $Config.password)
        
        $mailMessage = New-Object System.Net.Mail.MailMessage
        $mailMessage.From = $Config.from_address
        $mailMessage.To.Add($Config.to_address)
        $mailMessage.Subject = $subject
        $mailMessage.Body = $body
        $mailMessage.IsBodyHtml = $false
        
        $smtpClient.Send($mailMessage)
        
        $script:Logger.Info("Email alert sent successfully")
    }
    catch {
        $script:Logger.Error("Failed to send email alert: $_")
    }
}

# Send Slack alert
function Send-SlackAlert {
    param(
        [hashtable]$Alert,
        [hashtable]$Config
    )
    
    try {
        $color = switch ($Alert.severity) {
            "Critical" { "danger" }
            "Warning" { "warning" }
            "Info" { "good" }
            default { "warning" }
        }
        
        $payload = @{
            "channel" = $Config.channel
            "username" = "BMS Backup Monitor"
            "icon_emoji" = ":warning:"
            "attachments" = @(
                @{
                    "color" = $color
                    "title" = "Backup System Alert"
                    "fields" = @(
                        @{
                            "title" = "Severity"
                            "value" = $Alert.severity
                            "short" = $true
                        },
                        @{
                            "title" = "Component"
                            "value" = $Alert.component
                            "short" = $true
                        },
                        @{
                            "title" = "Message"
                            "value" = $Alert.message
                            "short" = $false
                        }
                    )
                    "timestamp" = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
                }
            )
        }
        
        $jsonPayload = $payload | ConvertTo-Json -Depth 10
        
        Invoke-RestMethod -Uri $Config.webhook_url -Method Post -Body $jsonPayload -ContentType "application/json"
        
        $script:Logger.Info("Slack alert sent successfully")
    }
    catch {
        $script:Logger.Error("Failed to send Slack alert: $_")
    }
}

# Generate monitoring report
function New-MonitoringReport {
    param(
        [string]$ReportPath,
        [hashtable]$Config
    )
    
    $script:Logger.Info("Generating monitoring report...")
    
    $report = @{
        "monitoring_summary" = @{
            "operation" = $Operation
            "start_time" = $script:MonitoringStartTime
            "end_time" = Get-Date
            "duration_minutes" = ((Get-Date) - $script:MonitoringStartTime).TotalMinutes
            "total_alerts" = $script:AlertHistory.Count
            "critical_alerts" = ($script:AlertHistory | Where-Object { $_.severity -eq "Critical" }).Count
            "warning_alerts" = ($script:AlertHistory | Where-Object { $_.severity -eq "Warning" }).Count
        }
        "metrics" = $script:MonitoringMetrics
        "alerts" = $script:AlertHistory
        "system_info" = @{
            "computer_name" = $env:COMPUTERNAME
            "os_version" = (Get-WmiObject -Class Win32_OperatingSystem).Version
            "powershell_version" = $PSVersionTable.PSVersion.ToString()
            "disk_space" = Get-DiskSpace
        }
    }
    
    # Save report as JSON
    $reportJson = $report | ConvertTo-Json -Depth 10
    $reportFile = "$ReportPath\monitoring_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8
    
    $script:Logger.Info("Monitoring report saved: $reportFile")
    
    return $report
}

# Get disk space information
function Get-DiskSpace {
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
    
    $driveInfo = @()
    foreach ($drive in $drives) {
        $driveInfo += @{
            "drive" = $drive.DeviceID
            "total_size_gb" = [math]::Round($drive.Size / 1GB, 2)
            "free_space_gb" = [math]::Round($drive.FreeSpace / 1GB, 2)
            "used_space_gb" = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
            "free_space_percent" = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        }
    }
    
    return $driveInfo
}

# Main execution
try {
    # Initialize logging
    Initialize-MonitoringLogging
    
    # Load configuration
    $config = Get-MonitoringConfiguration -ConfigFile $ConfigFile
    
    # Execute operation
    if ($ContinuousMode) {
        $script:Logger.Info("Starting continuous monitoring mode - Interval: $IntervalMinutes minutes")
        
        while ($true) {
            try {
                # Execute monitoring operation
                switch ($Operation) {
                    "monitor" {
                        $result = Monitor-BackupJobs -Config $config
                        $script:Logger.Info("Monitoring cycle completed - Overall health: $($result.overall_health)")
                    }
                    "health_check" {
                        $result = Invoke-HealthCheck -Config $config
                        $script:Logger.Info("Health check completed")
                    }
                }
                
                # Wait for next interval
                Start-Sleep -Seconds ($IntervalMinutes * 60)
            }
            catch {
                $script:Logger.Error("Monitoring cycle failed: $_")
                Start-Sleep -Seconds 60  # Wait 1 minute before retry
            }
        }
    } else {
        # Execute single operation
        switch ($Operation) {
            "monitor" {
                $result = Monitor-BackupJobs -Config $config
                $script:Logger.Info("Monitoring completed - Overall health: $($result.overall_health)")
            }
            "alert" {
                $result = Generate-BackupAlerts -BackupStatus $result -Config $config
                $script:Logger.Info("Alert generation completed")
            }
            "report" {
                $result = New-MonitoringReport -ReportPath ".\reports" -Config $config
                $script:Logger.Info("Report generation completed")
            }
            "health_check" {
                $result = Invoke-HealthCheck -Config $config
                $script:Logger.Info("Health check completed")
            }
        }
    }
    
    # Generate final report
    $report = New-MonitoringReport -ReportPath ".\reports" -Config $config
    
    $script:Logger.Info("Monitoring operation completed successfully")
    
    exit 0
}
catch {
    $script:Logger.Error("Monitoring operation failed: $_")
    exit 1
}
finally {
    if ($script:Logger) {
        $script:Logger.Close()
    }
}