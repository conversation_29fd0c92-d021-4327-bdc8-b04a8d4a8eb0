# 新北市違章建築資料轉換對應規格書

## 一、文件概述

### 1.1 目的
本文件定義新北市違章建築管理系統（BMS）與國土署系統間的資料轉換規則，確保資料能正確、完整地拋送至國土署。

### 1.2 範圍
- 資料來源：PostgreSQL資料庫（bms）
- 目標系統：國土署違建管理API
- 轉換內容：案件基本資料、各階段處理資料、附件檔案資訊

### 1.3 相關文件
- 資料拋送至國土署系統分析設計書
- DATABASE_COMPLETE_GUIDE.md
- BUSINESS_PROCESS_COMPLETE_GUIDE.md

## 二、資料表對應架構

### 2.1 主要資料來源表

| 來源資料表 | 說明 | 對應API資料區塊 |
|-----------|------|----------------|
| buildcase | 案件主檔 | 案件基本資料、各階段資料 |
| tbflow | 流程記錄 | 狀態變更歷程 |
| casefile | 附件檔案 | 文件附件資訊 |
| ibmcode | 系統代碼 | 代碼值轉換參照 |
| caselawfee | 規費記錄 | 規費收納資訊 |

### 2.2 資料轉換流程圖

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  BMS資料庫  │ --> │  資料轉換器  │ --> │  國土署API  │
└─────────────┘     └─────────────┘     └─────────────┘
      │                    │                    │
      │                    ├── 欄位對應         │
      │                    ├── 格式轉換         │
      │                    ├── 資料驗證         │
      │                    └── 資料清理         │
      │                                        │
      └────────────────────────────────────────┘
```

## 三、詳細欄位對應表

### 3.1 案件基本資料對應

| BMS欄位 | 欄位說明 | API欄位 | 資料型態 | 轉換規則 | 必填 |
|---------|---------|---------|----------|----------|------|
| case_no | 案件編號 | CaseNo | String(20) | 直接對應 | ✓ |
| case_type | 案件類型 | CaseType | String(2) | 轉換代碼（見3.1.1） | ✓ |
| caseopened | 當前狀態 | StageCode | String(3) | 直接對應 | ✓ |
| rpt_day | 查報日期 | ReportDate | Date | YYYY-MM-DD格式 | ✓ |
| rep_name | 查報人姓名 | ReporterName | String(100) | 去除前後空白 | ✓ |
| rep_unid | 查報人身分證 | ReporterId | String(10) | 遮罩處理（見3.1.2） | |
| tbvio_name | 違建人姓名 | ViolatorName | String(100) | 去除前後空白 | ✓ |
| tbvio_unid | 違建人身分證 | ViolatorId | String(10) | 遮罩處理（見3.1.2） | |
| address1 | 違建地址 | Address | String(200) | 地址標準化（見3.1.3） | ✓ |
| build_type | 建物類型 | BuildingType | String(2) | 轉換代碼（見3.1.4） | ✓ |
| area_occupy | 佔用面積 | OccupiedArea | Decimal(10,2) | 數值驗證，單位：平方公尺 | |
| longitude | 經度 | Longitude | Decimal(10,6) | WGS84座標系 | |
| latitude | 緯度 | Latitude | Decimal(10,6) | WGS84座標系 | |
| s_empno | 承辦人員 | AssignedOfficer | String(10) | 員工編號對應 | |
| create_date | 建立時間 | CreatedAt | DateTime | ISO 8601格式 | ✓ |
| update_date | 更新時間 | UpdatedAt | DateTime | ISO 8601格式 | ✓ |

#### 3.1.1 案件類型代碼轉換

| BMS值 | BMS說明 | API值 | API說明 |
|-------|---------|-------|---------|
| NORMAL | 一般違建 | 01 | 一般違建 |
| ADVERT | 廣告違建 | 02 | 廣告物違建 |
| SEWER | 下水道違建 | 03 | 妨礙下水道 |

#### 3.1.2 身分證遮罩規則

```javascript
function maskId(id) {
    if (!id || id.length < 10) return id;
    // 保留前3碼和後3碼，中間以*取代
    return id.substring(0, 3) + '****' + id.substring(7);
}
// 範例：A123456789 → A12****789
```

#### 3.1.3 地址標準化規則

```javascript
function standardizeAddress(address) {
    // 1. 移除多餘空白
    address = address.trim().replace(/\s+/g, '');
    
    // 2. 統一全形轉半形
    address = toHalfWidth(address);
    
    // 3. 補充行政區資訊
    if (!address.startsWith('新北市')) {
        address = '新北市' + address;
    }
    
    // 4. 段號標準化
    address = address.replace(/段(\d+)/g, '$1段');
    
    return address;
}
```

#### 3.1.4 建物類型代碼轉換

| BMS代碼 | BMS說明 | API代碼 | 轉換邏輯 |
|---------|---------|---------|----------|
| B01 | 住宅 | R01 | 住宅類建物 |
| B02 | 商業 | C01 | 商業類建物 |
| B03 | 工業 | I01 | 工業類建物 |
| B04 | 農業 | A01 | 農業類建物 |
| B99 | 其他 | O99 | 其他類建物 |

### 3.2 各階段資料對應

#### 3.2.1 現勘階段（Inspection Stage）

| BMS欄位 | API欄位 | 轉換規則 | 必填 |
|---------|---------|----------|------|
| chk_day | InspectionDate | YYYY-MM-DD格式 | ✓ |
| chk_empno | InspectorId | 員工編號對應 | ✓ |
| area_occupy | MeasuredArea | 數值驗證 | ✓ |
| violation_desc | ViolationDesc | 最大500字元 | ✓ |
| photo_count | PhotoCount | 整數驗證 | |
| gps_checked | HasGpsLocation | Y/N轉Boolean | |

#### 3.2.2 認定階段（Determination Stage）

| BMS欄位 | API欄位 | 轉換規則 | 必填 |
|---------|---------|----------|------|
| conf_day | DeterminationDate | YYYY-MM-DD格式 | ✓ |
| conf_type | DeterminationType | 代碼轉換（見3.2.3） | ✓ |
| legal_basis | LegalBasis | 法規條文對應 | ✓ |
| penalty_amount | PenaltyAmount | 數值驗證，單位：元 | |
| demolish_deadline | DemolishDeadline | YYYY-MM-DD格式 | |

#### 3.2.3 認定類型代碼轉換

| BMS代碼 | API代碼 | 說明 |
|---------|---------|------|
| C01 | DT01 | 即報即拆 |
| C02 | DT02 | 限期改善 |
| C03 | DT03 | 既存違建 |
| C04 | DT04 | 免拆除 |

### 3.3 附件檔案資料對應

| BMS欄位 | API欄位 | 轉換規則 | 必填 |
|---------|---------|----------|------|
| file_id | FileId | 系統唯一識別碼 | ✓ |
| file_type | FileType | 檔案類型代碼轉換 | ✓ |
| file_name | FileName | 檔名安全處理 | ✓ |
| file_size | FileSize | 位元組數 | ✓ |
| upload_date | UploadDate | ISO 8601格式 | ✓ |
| file_hash | FileHash | SHA-256雜湊值 | |

## 四、資料格式轉換規則

### 4.1 日期時間格式轉換

```csharp
public static class DateTimeConverter
{
    // PostgreSQL日期轉ISO 8601格式
    public static string ToApiDateTime(DateTime? pgDate)
    {
        if (!pgDate.HasValue) return null;
        return pgDate.Value.ToString("yyyy-MM-dd'T'HH:mm:ss.fffK");
    }
    
    // 民國年轉西元年
    public static DateTime? FromRocDate(string rocDate)
    {
        if (string.IsNullOrEmpty(rocDate)) return null;
        
        // 格式：1130708
        if (rocDate.Length == 7)
        {
            int rocYear = int.Parse(rocDate.Substring(0, 3));
            int month = int.Parse(rocDate.Substring(3, 2));
            int day = int.Parse(rocDate.Substring(5, 2));
            
            return new DateTime(rocYear + 1911, month, day);
        }
        return null;
    }
}
```

### 4.2 座標系統轉換

```csharp
public static class CoordinateConverter
{
    // TWD97轉WGS84
    public static (double lon, double lat) TWD97ToWGS84(double x, double y)
    {
        // 使用 Proj4Net 或類似函式庫進行轉換
        var source = ProjectionInfo.FromEpsg(3826); // TWD97 / TM2
        var target = ProjectionInfo.FromEpsg(4326); // WGS84
        
        double[] xy = { x, y };
        double[] z = { 0 };
        
        Reproject.ReprojectPoints(xy, z, source, target, 0, 1);
        
        return (xy[0], xy[1]);
    }
}
```

### 4.3 代碼值轉換處理

```csharp
public class CodeMapper
{
    private readonly Dictionary<string, Dictionary<string, string>> _mappings;
    
    public CodeMapper(IDbConnection connection)
    {
        _mappings = LoadMappings(connection);
    }
    
    public string MapCode(string codeType, string bmsCode)
    {
        if (_mappings.ContainsKey(codeType) && 
            _mappings[codeType].ContainsKey(bmsCode))
        {
            return _mappings[codeType][bmsCode];
        }
        
        // 無對應時返回預設值
        return GetDefaultCode(codeType);
    }
    
    private Dictionary<string, Dictionary<string, string>> LoadMappings(IDbConnection conn)
    {
        // 從資料庫載入對應表
        var sql = @"
            SELECT code_type, bms_code, api_code 
            FROM code_mapping 
            WHERE is_active = true";
            
        return conn.Query<CodeMapping>(sql)
            .GroupBy(x => x.CodeType)
            .ToDictionary(
                g => g.Key,
                g => g.ToDictionary(x => x.BmsCode, x => x.ApiCode)
            );
    }
}
```

## 五、資料驗證規則

### 5.1 必填欄位檢核

```csharp
public class RequiredFieldValidator
{
    private readonly List<string> _requiredFields = new List<string>
    {
        "CaseNo", "CaseType", "StageCode", "ReportDate",
        "ReporterName", "ViolatorName", "Address", "BuildingType"
    };
    
    public ValidationResult Validate(ViolationCase data)
    {
        var errors = new List<string>();
        
        foreach (var field in _requiredFields)
        {
            var value = GetFieldValue(data, field);
            if (string.IsNullOrWhiteSpace(value?.ToString()))
            {
                errors.Add($"必填欄位 {field} 不可為空");
            }
        }
        
        return new ValidationResult
        {
            IsValid = errors.Count == 0,
            Errors = errors
        };
    }
}
```

### 5.2 資料格式驗證

```csharp
public static class DataFormatValidator
{
    // 身分證字號驗證
    public static bool ValidateIdNumber(string id)
    {
        if (string.IsNullOrEmpty(id) || id.Length != 10)
            return false;
            
        // 身分證驗證邏輯
        var regex = new Regex(@"^[A-Z][12]\d{8}$");
        if (!regex.IsMatch(id))
            return false;
            
        // 檢查碼驗證
        return ValidateIdChecksum(id);
    }
    
    // 統一編號驗證
    public static bool ValidateCompanyId(string id)
    {
        if (string.IsNullOrEmpty(id) || id.Length != 8)
            return false;
            
        var regex = new Regex(@"^\d{8}$");
        return regex.IsMatch(id) && ValidateCompanyChecksum(id);
    }
    
    // 電話號碼標準化
    public static string StandardizePhone(string phone)
    {
        if (string.IsNullOrEmpty(phone))
            return phone;
            
        // 移除所有非數字字元
        phone = Regex.Replace(phone, @"\D", "");
        
        // 加上區碼
        if (phone.Length == 8 && !phone.StartsWith("02"))
        {
            phone = "02" + phone;
        }
        
        return phone;
    }
}
```

### 5.3 業務邏輯驗證

```csharp
public class BusinessRuleValidator
{
    // 狀態轉換合法性驗證
    public bool ValidateStateTransition(string fromState, string toState)
    {
        var validTransitions = new Dictionary<string, List<string>>
        {
            ["231"] = new List<string> { "232", "400" },
            ["232"] = new List<string> { "233", "234", "400" },
            ["234"] = new List<string> { "235", "23b", "239" },
            ["239"] = new List<string> { "241", "400" }
        };
        
        return validTransitions.ContainsKey(fromState) && 
               validTransitions[fromState].Contains(toState);
    }
    
    // 日期邏輯驗證
    public bool ValidateDateSequence(DateTime? reportDate, 
                                    DateTime? inspectionDate, 
                                    DateTime? determinationDate)
    {
        // 查報日期必須早於勘查日期
        if (reportDate.HasValue && inspectionDate.HasValue)
        {
            if (reportDate.Value > inspectionDate.Value)
                return false;
        }
        
        // 勘查日期必須早於認定日期
        if (inspectionDate.HasValue && determinationDate.HasValue)
        {
            if (inspectionDate.Value > determinationDate.Value)
                return false;
        }
        
        return true;
    }
}
```

## 六、特殊業務邏輯處理

### 6.1 狀態碼轉換邏輯

```csharp
public class StatusCodeConverter
{
    private readonly Dictionary<string, StageInfo> _stageMapping;
    
    public StatusCodeConverter()
    {
        InitializeMapping();
    }
    
    private void InitializeMapping()
    {
        _stageMapping = new Dictionary<string, StageInfo>
        {
            // 查報階段
            ["201"] = new StageInfo { Stage = "REPORT", SubStage = "PENDING" },
            ["202"] = new StageInfo { Stage = "REPORT", SubStage = "REVIEW" },
            
            // 現勘階段
            ["211"] = new StageInfo { Stage = "INSPECTION", SubStage = "SCHEDULED" },
            ["212"] = new StageInfo { Stage = "INSPECTION", SubStage = "IN_PROGRESS" },
            ["219"] = new StageInfo { Stage = "INSPECTION", SubStage = "COMPLETED" },
            
            // 認定階段
            ["231"] = new StageInfo { Stage = "DETERMINATION", SubStage = "PENDING" },
            ["232"] = new StageInfo { Stage = "DETERMINATION", SubStage = "REVIEW" },
            ["239"] = new StageInfo { Stage = "DETERMINATION", SubStage = "COMPLETED" },
            
            // 協同處理
            ["234"] = new StageInfo { Stage = "DETERMINATION", SubStage = "COLLAB" },
            ["235"] = new StageInfo { Stage = "DETERMINATION", SubStage = "COLLAB_REJECT" },
            ["23b"] = new StageInfo { Stage = "DETERMINATION", SubStage = "COLLAB_COMPLETE" }
        };
    }
    
    public StageInfo GetStageInfo(string statusCode)
    {
        return _stageMapping.ContainsKey(statusCode) 
            ? _stageMapping[statusCode] 
            : new StageInfo { Stage = "UNKNOWN", SubStage = statusCode };
    }
}

public class StageInfo
{
    public string Stage { get; set; }
    public string SubStage { get; set; }
}
```

### 6.2 協同處理資料轉換

```csharp
public class CollaborationDataConverter
{
    public CollaborationInfo ConvertCollaboration(BuildCase caseData)
    {
        if (string.IsNullOrEmpty(caseData.CaseConUser))
            return null;
            
        return new CollaborationInfo
        {
            CollaboratorId = caseData.CaseConUser,
            StartDate = caseData.CaseConDate,
            Status = GetCollaborationStatus(caseData.Caseopened),
            Department = GetDepartmentByUser(caseData.CaseConUser)
        };
    }
    
    private string GetCollaborationStatus(string statusCode)
    {
        return statusCode switch
        {
            "234" or "244" or "254" => "IN_PROGRESS",
            "235" or "245" or "255" => "REJECTED",
            "23b" or "24b" or "25b" => "COMPLETED",
            _ => "UNKNOWN"
        };
    }
}
```

### 6.3 規費資料轉換

```csharp
public class FeeDataConverter
{
    public List<FeeInfo> ConvertFees(string caseNo, IDbConnection conn)
    {
        var sql = @"
            SELECT 
                law_name as FeeType,
                amount as Amount,
                pay_date as PaymentDate,
                receipt_no as ReceiptNo,
                pay_method as PaymentMethod
            FROM caselawfee
            WHERE case_no = @caseNo
            AND is_paid = 'Y'
            ORDER BY pay_date";
            
        var fees = conn.Query<FeeInfo>(sql, new { caseNo });
        
        // 轉換付款方式代碼
        foreach (var fee in fees)
        {
            fee.PaymentMethod = ConvertPaymentMethod(fee.PaymentMethod);
        }
        
        return fees.ToList();
    }
    
    private string ConvertPaymentMethod(string bmsMethod)
    {
        return bmsMethod switch
        {
            "CASH" => "01",      // 現金
            "CHECK" => "02",     // 支票
            "TRANSFER" => "03",  // 匯款
            "CREDIT" => "04",    // 信用卡
            _ => "99"            // 其他
        };
    }
}
```

## 七、資料清理與標準化

### 7.1 文字資料清理

```csharp
public static class TextCleaner
{
    // 移除特殊字元
    public static string CleanText(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;
            
        // 移除控制字元
        input = Regex.Replace(input, @"[\x00-\x1F\x7F]", "");
        
        // 標準化空白
        input = Regex.Replace(input, @"\s+", " ").Trim();
        
        // 移除危險字元（防止注入攻擊）
        input = Regex.Replace(input, @"[<>""']", "");
        
        return input;
    }
    
    // HTML編碼處理
    public static string HtmlEncode(string input)
    {
        return System.Web.HttpUtility.HtmlEncode(input);
    }
    
    // 全形轉半形
    public static string ToHalfWidth(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;
            
        var sb = new StringBuilder();
        foreach (char c in input)
        {
            if (c >= '！' && c <= '～')
            {
                sb.Append((char)(c - 0xFEE0));
            }
            else if (c == '　') // 全形空格
            {
                sb.Append(' ');
            }
            else
            {
                sb.Append(c);
            }
        }
        return sb.ToString();
    }
}
```

### 7.2 數值資料標準化

```csharp
public static class NumberNormalizer
{
    // 面積單位轉換
    public static decimal? ConvertArea(decimal? area, string fromUnit, string toUnit = "sqm")
    {
        if (!area.HasValue) return null;
        
        var conversionRates = new Dictionary<string, decimal>
        {
            ["ping"] = 3.305785m,    // 坪轉平方公尺
            ["sqft"] = 0.092903m,    // 平方英尺轉平方公尺
            ["sqm"] = 1m             // 平方公尺
        };
        
        if (!conversionRates.ContainsKey(fromUnit) || 
            !conversionRates.ContainsKey(toUnit))
            return area;
            
        var sqmValue = area.Value * conversionRates[fromUnit];
        return sqmValue / conversionRates[toUnit];
    }
    
    // 金額格式化
    public static decimal RoundAmount(decimal amount)
    {
        // 四捨五入到整數
        return Math.Round(amount, 0, MidpointRounding.AwayFromZero);
    }
}
```

### 7.3 地址資料標準化

```csharp
public class AddressStandardizer
{
    private readonly Dictionary<string, string> _districtMapping;
    
    public AddressStandardizer()
    {
        InitializeDistrictMapping();
    }
    
    private void InitializeDistrictMapping()
    {
        _districtMapping = new Dictionary<string, string>
        {
            ["板橋"] = "板橋區",
            ["新店"] = "新店區",
            ["中和"] = "中和區",
            ["永和"] = "永和區",
            ["土城"] = "土城區",
            ["三峽"] = "三峽區",
            ["樹林"] = "樹林區",
            ["三重"] = "三重區",
            ["新莊"] = "新莊區",
            ["蘆洲"] = "蘆洲區"
            // ... 其他行政區
        };
    }
    
    public string StandardizeAddress(string address)
    {
        if (string.IsNullOrEmpty(address))
            return address;
            
        // 1. 基本清理
        address = TextCleaner.CleanText(address);
        address = TextCleaner.ToHalfWidth(address);
        
        // 2. 補充縣市
        if (!address.StartsWith("新北市"))
        {
            address = "新北市" + address;
        }
        
        // 3. 標準化行政區
        foreach (var district in _districtMapping)
        {
            if (address.Contains(district.Key) && !address.Contains(district.Value))
            {
                address = address.Replace(district.Key, district.Value);
            }
        }
        
        // 4. 統一路街巷弄格式
        address = Regex.Replace(address, @"(\d+)\s*鄰", "$1鄰");
        address = Regex.Replace(address, @"(\d+)\s*號", "$1號");
        address = Regex.Replace(address, @"(\d+)\s*樓", "$1樓");
        
        return address;
    }
}
```

## 八、錯誤處理與回滾機制

### 8.1 轉換錯誤處理

```csharp
public class ConversionErrorHandler
{
    private readonly ILogger _logger;
    private readonly List<ConversionError> _errors;
    
    public ConversionErrorHandler(ILogger logger)
    {
        _logger = logger;
        _errors = new List<ConversionError>();
    }
    
    public T SafeConvert<T>(Func<T> conversionFunc, string fieldName, string caseNo)
    {
        try
        {
            return conversionFunc();
        }
        catch (Exception ex)
        {
            var error = new ConversionError
            {
                CaseNo = caseNo,
                FieldName = fieldName,
                ErrorMessage = ex.Message,
                ErrorType = GetErrorType(ex),
                OccurredAt = DateTime.Now
            };
            
            _errors.Add(error);
            _logger.LogError(ex, "轉換錯誤: {CaseNo} - {Field}", caseNo, fieldName);
            
            return default(T);
        }
    }
    
    private string GetErrorType(Exception ex)
    {
        return ex switch
        {
            FormatException => "FORMAT_ERROR",
            InvalidCastException => "TYPE_ERROR",
            ArgumentException => "VALIDATION_ERROR",
            _ => "UNKNOWN_ERROR"
        };
    }
}
```

### 8.2 批次處理事務控制

```csharp
public class BatchConversionService
{
    private readonly IDbConnection _connection;
    private readonly ILogger _logger;
    
    public async Task<BatchResult> ConvertBatch(List<string> caseNos)
    {
        var result = new BatchResult();
        using var transaction = _connection.BeginTransaction();
        
        try
        {
            foreach (var caseNo in caseNos)
            {
                try
                {
                    var convertedData = await ConvertSingleCase(caseNo);
                    await SaveToSyncQueue(convertedData, transaction);
                    result.SuccessCount++;
                }
                catch (Exception ex)
                {
                    result.FailedCases.Add(new FailedCase
                    {
                        CaseNo = caseNo,
                        Error = ex.Message
                    });
                    
                    // 決定是否繼續處理
                    if (IsCriticalError(ex))
                    {
                        throw; // 觸發整批回滾
                    }
                }
            }
            
            transaction.Commit();
        }
        catch (Exception ex)
        {
            transaction.Rollback();
            _logger.LogError(ex, "批次轉換失敗，已回滾");
            throw;
        }
        
        return result;
    }
}
```

## 九、效能優化建議

### 9.1 批次處理策略

```csharp
public class BatchProcessingConfig
{
    public int BatchSize { get; set; } = 100;
    public int MaxConcurrency { get; set; } = 5;
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(5);
    
    public static BatchProcessingConfig GetOptimalConfig(int totalRecords)
    {
        return totalRecords switch
        {
            < 1000 => new BatchProcessingConfig { BatchSize = 50, MaxConcurrency = 2 },
            < 10000 => new BatchProcessingConfig { BatchSize = 100, MaxConcurrency = 5 },
            < 100000 => new BatchProcessingConfig { BatchSize = 500, MaxConcurrency = 10 },
            _ => new BatchProcessingConfig { BatchSize = 1000, MaxConcurrency = 20 }
        };
    }
}
```

### 9.2 快取機制

```csharp
public class ConversionCache
{
    private readonly MemoryCache _cache;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromHours(1);
    
    public ConversionCache()
    {
        _cache = new MemoryCache(new MemoryCacheOptions
        {
            SizeLimit = 1000
        });
    }
    
    public T GetOrAdd<T>(string key, Func<T> factory)
    {
        return _cache.GetOrCreate(key, entry =>
        {
            entry.SetSlidingExpiration(_cacheExpiration);
            entry.SetSize(1);
            return factory();
        });
    }
    
    // 快取代碼對應表
    public Dictionary<string, string> GetCodeMapping(string codeType)
    {
        return GetOrAdd($"CodeMapping_{codeType}", () =>
        {
            // 從資料庫載入代碼對應
            return LoadCodeMappingFromDb(codeType);
        });
    }
}
```

## 十、監控與稽核

### 10.1 轉換過程監控

```csharp
public class ConversionMonitor
{
    private readonly IMetricsCollector _metrics;
    
    public void RecordConversion(string caseNo, TimeSpan duration, bool success)
    {
        _metrics.RecordMetric("conversion.duration", duration.TotalMilliseconds);
        _metrics.IncrementCounter($"conversion.{(success ? "success" : "failure")}");
        
        if (duration.TotalSeconds > 5)
        {
            _metrics.RecordSlowConversion(caseNo, duration);
        }
    }
    
    public ConversionStatistics GetStatistics(DateTime from, DateTime to)
    {
        return new ConversionStatistics
        {
            TotalCount = _metrics.GetCount("conversion.total", from, to),
            SuccessCount = _metrics.GetCount("conversion.success", from, to),
            FailureCount = _metrics.GetCount("conversion.failure", from, to),
            AverageDuration = _metrics.GetAverage("conversion.duration", from, to),
            SuccessRate = CalculateSuccessRate()
        };
    }
}
```

### 10.2 資料品質檢核

```csharp
public class DataQualityChecker
{
    public QualityReport CheckDataQuality(ConvertedData data)
    {
        var report = new QualityReport { CaseNo = data.CaseNo };
        
        // 完整性檢查
        report.CompletenessScore = CalculateCompleteness(data);
        
        // 一致性檢查
        report.ConsistencyIssues = CheckConsistency(data);
        
        // 準確性檢查
        report.AccuracyIssues = CheckAccuracy(data);
        
        // 計算總分
        report.OverallScore = CalculateOverallScore(report);
        
        return report;
    }
    
    private decimal CalculateCompleteness(ConvertedData data)
    {
        var requiredFields = GetRequiredFields(data.StageCode);
        var filledFields = CountFilledFields(data, requiredFields);
        
        return (decimal)filledFields / requiredFields.Count * 100;
    }
}
```

## 十一、實作範例

### 11.1 完整轉換流程範例

```csharp
public class ViolationCaseConverter
{
    private readonly IDbConnection _connection;
    private readonly CodeMapper _codeMapper;
    private readonly AddressStandardizer _addressStandardizer;
    private readonly ILogger _logger;
    
    public async Task<NationalApiRequest> ConvertCase(string caseNo)
    {
        // 1. 載入原始資料
        var caseData = await LoadCaseData(caseNo);
        var stageData = await LoadStageData(caseNo);
        var files = await LoadCaseFiles(caseNo);
        
        // 2. 建立API請求物件
        var request = new NationalApiRequest
        {
            CaseNo = caseData.CaseNo,
            CaseType = _codeMapper.MapCode("CASE_TYPE", caseData.CaseType),
            StageCode = caseData.Caseopened,
            
            // 基本資料轉換
            BasicInfo = new BasicInfo
            {
                ReportDate = DateTimeConverter.ToApiDateTime(caseData.RptDay),
                ReporterName = TextCleaner.CleanText(caseData.RepName),
                ReporterId = DataFormatValidator.MaskId(caseData.RepUnid),
                ViolatorName = TextCleaner.CleanText(caseData.TbvioName),
                ViolatorId = DataFormatValidator.MaskId(caseData.TbvioUnid),
                Address = _addressStandardizer.StandardizeAddress(caseData.Address1),
                BuildingType = _codeMapper.MapCode("BUILD_TYPE", caseData.BuildType),
                OccupiedArea = NumberNormalizer.ConvertArea(caseData.AreaOccupy, "sqm")
            },
            
            // 階段資料轉換
            StageInfo = ConvertStageInfo(stageData),
            
            // 附件資料
            Attachments = ConvertAttachments(files)
        };
        
        // 3. 資料驗證
        var validation = ValidateConvertedData(request);
        if (!validation.IsValid)
        {
            throw new ValidationException(validation.Errors);
        }
        
        return request;
    }
    
    private async Task<BuildCase> LoadCaseData(string caseNo)
    {
        var sql = @"
            SELECT 
                case_no as CaseNo,
                case_type as CaseType,
                caseopened as Caseopened,
                rpt_day as RptDay,
                rep_name as RepName,
                rep_unid as RepUnid,
                tbvio_name as TbvioName,
                tbvio_unid as TbvioUnid,
                address1 as Address1,
                build_type as BuildType,
                area_occupy as AreaOccupy,
                longitude as Longitude,
                latitude as Latitude,
                s_empno as SEmpno,
                create_date as CreateDate,
                update_date as UpdateDate
            FROM buildcase
            WHERE case_no = @caseNo";
            
        return await _connection.QuerySingleOrDefaultAsync<BuildCase>(sql, new { caseNo });
    }
}
```

## 十二、部署與維護

### 12.1 部署檢查清單

- [ ] 資料庫連線設定正確
- [ ] 代碼對應表已更新
- [ ] 轉換規則已測試
- [ ] 錯誤處理機制就緒
- [ ] 監控系統已設定
- [ ] 備份機制已啟用

### 12.2 日常維護作業

1. **每日檢查**
   - 轉換失敗案件數量
   - 平均轉換時間
   - 錯誤類型分布

2. **每週維護**
   - 更新代碼對應表
   - 清理轉換日誌
   - 效能調校評估

3. **每月檢討**
   - 資料品質報告
   - 轉換規則調整
   - 系統容量規劃

## 十三、附錄

### 13.1 常見問題處理

| 問題類型 | 處理方式 | 備註 |
|---------|---------|------|
| 日期格式錯誤 | 使用預設值或當前日期 | 記錄至錯誤日誌 |
| 代碼無對應 | 使用預設代碼 | 通知維護人員更新 |
| 必填欄位空值 | 拒絕轉換 | 退回人工處理 |
| 資料超長 | 截斷處理 | 保留完整資料備查 |

### 13.2 相關資源

- 國土署API文件：[待補充]
- 內部系統操作手冊：/DOCS/01_ACTIVE/
- 技術支援聯絡：[待補充]

---

文件版本：1.0
最後更新：2025-01-08
撰寫人：系統整合小組