# 新北市違章建築管理系統 - 掛號通報流程分析報告

## 文件資訊
- **建立時間**: 2025-07-05
- **任務編號**: T2.1.1 
- **分析範圍**: 掛號通報流程 (狀態碼231/241/251)
- **工時**: 4小時
- **負責人**: 【C】Claude Code - 業務分析任務組

## 分析方法說明
本報告採用多Agent並行分析方法，部署6個專責Agent同時進行：
- Agent 1: JSP檔案搜尋分析
- Agent 2: 狀態碼相關檔案檢索
- Agent 3: Handler業務邏輯分析  
- Agent 4: XML配置檔案分析
- Agent 5: 狀態碼轉換邏輯分析
- Agent 6: 資料驗證規則分析

## 1. 掛號通報流程概述

### 1.1 業務定義
掛號通報是新北市違章建築管理系統的起始流程，負責將來源不同的違章建築案件（民眾檢舉、主動查察、其他機關移送）進行正式登記立案，建立案件編號並設定初始狀態。

### 1.2 流程目標
- 建立案件主檔記錄 (IBMCASE)
- 設定初始狀態碼 (IBMSTS/IBMFYM)
- 進行資料驗證和重複檢查
- 啟動後續認定審核流程

## 2. 核心系統檔案架構

### 2.1 主要JSP檔案群組 (im10101系列)

#### 表單頁面 (三頁式分割設計)
```
im10101_man_A.jsp    // 掛號通報表單A頁 - 基本資料
im10101_man_B.jsp    // 掛號通報表單B頁 - 詳細資料  
im10101_man_C.jsp    // 掛號通報表單C頁 - 附加資料
im10101_lis.jsp      // 掛號通報清單頁 - 查詢列表
```

#### 業務邏輯處理檔案
```
im10101_man_AHandlers.jsp  // A頁業務邏輯 - 新增/更新案件主檔
im10101_man_BHandlers.jsp  // B頁業務邏輯 - 處理詳細資料
im10101_man_CHandlers.jsp  // C頁業務邏輯 - 處理附加資料
im10101_lisHandlers.jsp    // 清單業務邏輯 - 查詢/篩選
```

#### 輔助功能檔案
```
im10101_man_checkAddr.jsp   // 地址重複檢查API
im10101_man_checkCslan.jsp  // 地號重複檢查API  
im10101_man_copyCase.jsp    // 案件複製功能API
case_empty_dis.jsp          // 案件結案API
```

### 2.2 XML配置檔案
```
im10101_man_A.xml   // A頁表單配置 - 欄位定義、驗證規則
im10101_man_B.xml   // B頁表單配置 - 資料來源、下拉選項
im10101_man_C.xml   // C頁表單配置 - 附加欄位設定
im10101_lis.xml     // 清單頁配置 - 查詢條件、顯示欄位
```

## 3. 狀態碼架構與業務分工

### 3.1 掛號通報階段狀態碼 (2xx系列)
基於IBMCODE系統參數表分析，掛號通報使用以下狀態碼：

| 狀態碼 | 業務類型 | 負責科別 | 說明 |
|--------|----------|----------|------|
| **231** | 一般違章建築 | 拆除科 | 一般建築物違規使用或建造 |
| **241** | 廣告違章建築 | 廣告科 | 廣告物設置違規 |
| **251** | 下水道違章建築 | 勞安科 | 影響下水道系統的違建 |

### 3.2 三階段狀態轉換邏輯

#### 第一階段：掛號通報 (2xx系列)
```
231 → 認定階段 → 232/234/235/236/237/239...
241 → 認定階段 → 242/244/245/246/247/249...  
251 → 認定階段 → 252/254/255/256/257/259...
```

#### 第二階段：排拆執行 (3xx系列)
```
認定完成 → 331/341/351 → 排拆階段...
```

#### 第三階段：結案歸檔 (4xx系列)
```
排拆完成 → 441/451/461 → 結案歸檔
```

### 3.3 業務分工對應機制
```java
// case_empty_dis.jsp 中的結案邏輯
String midChar = current_acc_rlt.substring(1, 2);
if ("2,6".indexOf(midChar) > -1) { // 拆除科
    acc_rlt = "461";
} else if ("5".indexOf(midChar) > -1) { // 勞安科(下水道)
    acc_rlt = "451"; 
} else if ("4".indexOf(midChar) > -1) { // 廣告科
    acc_rlt = "441";
}
```

## 4. 掛號通報業務流程

### 4.1 流程步驟詳析

#### Step 1: 案件建立
**檔案位置**: `im10101_man_AHandlers.jsp`
**執行時機**: 表單A頁送出時

```java
if(StringUtils.isEmpty(CASE_ID)){
    // 建立新案件主檔
    String INSERT_SQL = "INSERT INTO IBMCASE(REG_EMP, REG_UNIT, IB_PRCS, STATUS, CR_DATE, OP_DATE, CHK_CASE_DATE)"
                       + " VALUES(?, ?, 'A', '01', ?, ?, ?)" 
                       + " RETURNING CASE_ID";
    
    // 取得新案件編號並儲存
    PreparedStatement ps = DBTools.getPreparedStatement(dbc, INSERT_SQL);
    ResultSet rs = ps.executeQuery();
    rs.next();
    String caseID = rs.getString("CASE_ID");
}
```

**重要欄位說明**:
- `REG_EMP`: 登記承辦人員
- `REG_UNIT`: 登記單位
- `IB_PRCS`: 處理階段 ('A' = 掛號通報階段)
- `STATUS`: 案件狀態 ('01' = 初始狀態)

#### Step 2: 狀態記錄建立
**檔案位置**: `im10101_man_AHandlers.jsp`
**執行時機**: 案件建立後立即執行

```java
// 建立當前狀態記錄 (IBMSTS)
INSERT_SQL = "INSERT INTO IBMSTS(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT)"
           + " VALUES('"+caseID+"', "+current_ymd+", "+currentTime+", '"+acc_job+"', '231')";

// 建立歷程記錄 (IBMFYM)  
INSERT_SQL = "INSERT INTO IBMFYM(CASE_ID, ACC_DATE, ACC_TIME, ACC_JOB, ACC_RLT, OP_USER, CR_DATE)"
           + " VALUES('"+caseID+"', "+current_ymd+", "+currentTime+", '"+acc_job+"', '231', '"+reg_emp+"', "+current_ymd+")";
```

**說明**:
- IBMSTS: 記錄案件當前狀態 (單一記錄)
- IBMFYM: 記錄案件所有狀態變更歷程 (多筆記錄)
- 預設建立狀態碼 231 (一般違章建築掛號)

#### Step 3: 地號資料處理
**檔案位置**: `im10101_man_BHandlers.jsp`
**執行時機**: 表單B頁送出時

```java
// 如果是複製案件，同步複製地號資料
if (!StringUtils.isEmpty(copyFromCaseId)) {
    String copySql = "INSERT INTO IBMCSLAN (CASE_ID, LAN_DIST, LAN_BNID, LAN_LNID, LAN_SNID, CSLAN_ID)"
                   + " SELECT '"+caseID+"', LAN_DIST, LAN_BNID, LAN_LNID, LAN_SNID, NEXTVAL('ibmcslan_cslan_id_seq')"
                   + " FROM IBMCSLAN WHERE CASE_ID = '"+copyFromCaseId+"'";
}
```

### 4.2 資料驗證機制

#### 地址重複檢查
**檔案位置**: `im10101_man_checkAddr.jsp`
**檢查邏輯**:
```java
String whereSql = "CASE_ID <> '"+case_id+"'";
// 逐一檢查地址相關欄位
if (!StringUtils.isEmpty(dis_b_addzon))
    whereSql += " AND DIS_B_ADDZON = '"+ dis_b_addzon +"'";
if (!StringUtils.isEmpty(dis_b_addrd))  
    whereSql += " AND DIS_B_ADDRD = '"+ dis_b_addrd +"'";
if (!StringUtils.isEmpty(dis_b_addln))
    whereSql += " AND DIS_B_ADDLN = '"+ dis_b_addln +"'";
    
// 計算重複筆數
String resultCount = Utils.convertToString(DBTools.dLookUp("count(*)", "IBMCASE", whereSql, "DBConn"));
```

#### 地號重複檢查  
**檔案位置**: `im10101_man_checkCslan.jsp`
**檢查邏輯**:
```java
String whereSql = "CASE_ID <> '"+case_id+"'";
if (!StringUtils.isEmpty(lan_dist))
    whereSql += " AND LAN_DIST = '"+ lan_dist +"'";
if (!StringUtils.isEmpty(lan_bnid))
    whereSql += " AND LAN_BNID = '"+ lan_bnid +"'";
if (!StringUtils.isEmpty(lan_lnid))
    whereSql += " AND LAN_LNID = '"+ lan_lnid +"'";
    
String resultCount = Utils.convertToString(DBTools.dLookUp("count(*)", "IBMCSLAN", whereSql, "DBConn"));
```

## 5. 資料庫結構與關聯

### 5.1 核心資料表

#### IBMCASE (案件主檔)
**用途**: 儲存違章建築案件基本資料
**關鍵欄位**:
```sql
CASE_ID         // 案件編號 (主鍵)
REG_EMP         // 登記承辦人
REG_UNIT        // 登記單位  
IB_PRCS         // 處理階段
STATUS          // 案件狀態
DIS_B_ADDZON    // 違建物行政區
DIS_B_ADDRD     // 違建物路段
DIS_B_ADDLN     // 違建物巷弄
CR_DATE         // 建立日期
OP_DATE         // 操作日期
CHK_CASE_DATE   // 檢查日期
```

#### IBMSTS (案件狀態檔)
**用途**: 記錄案件當前狀態 (單一記錄)
**關鍵欄位**:
```sql
CASE_ID     // 案件編號 (外鍵)
ACC_DATE    // 異動日期
ACC_TIME    // 異動時間  
ACC_JOB     // 承辦人員
ACC_RLT     // 狀態碼 (231/241/251)
```

#### IBMFYM (案件歷程檔)
**用途**: 記錄案件所有狀態變更歷史 (多筆記錄)
**關鍵欄位**:
```sql
CASE_ID     // 案件編號 (外鍵)
ACC_DATE    // 異動日期
ACC_TIME    // 異動時間
ACC_JOB     // 承辦人員
ACC_RLT     // 狀態碼
OP_USER     // 操作使用者
CR_DATE     // 建立日期
```

#### IBMCSLAN (案件地號檔)
**用途**: 記錄案件相關地籍資料
**關鍵欄位**:
```sql
CASE_ID     // 案件編號 (外鍵)
LAN_DIST    // 地號區域
LAN_BNID    // 地號段別
LAN_LNID    // 地號地號
LAN_SNID    // 地號小段
CSLAN_ID    // 地號記錄編號 (主鍵)
```

### 5.2 管制檔案機制

#### CASEOPENED (開啟中案件管制檔)
**用途**: 管制同時編輯案件，避免資料衝突
**關鍵欄位**:
```sql
CASE_ID         // 案件編號
OPENED_BY       // 開啟使用者  
OPENED_DATE     // 開啟日期
OPENED_TIME     // 開啟時間
```

#### CASESUBMITSTS (案件送審狀態檔)
**用途**: 記錄案件送審狀態，控制編輯權限
**關鍵欄位**:
```sql
CASE_ID         // 案件編號
SUBMIT_STATUS   // 送審狀態
SUBMIT_DATE     // 送審日期
SUBMIT_BY       // 送審人員
```

## 6. 掛號通報流程圖

```mermaid
flowchart TD
    A[案件來源] --> B{來源類型}
    B -->|民眾檢舉| C[檢舉案件]
    B -->|主動查察| D[查察案件]  
    B -->|機關移送| E[移送案件]
    
    C --> F[開啟掛號通報表單]
    D --> F
    E --> F
    
    F --> G[填寫基本資料 - A頁]
    G --> H[地址重複檢查]
    H -->|重複| I[顯示重複警告]
    H -->|無重複| J[填寫詳細資料 - B頁]
    
    I --> G
    J --> K[地號重複檢查]
    K -->|重複| L[顯示重複警告]
    K -->|無重複| M[填寫附加資料 - C頁]
    
    L --> J
    M --> N[送出掛號通報]
    
    N --> O[建立案件主檔 IBMCASE]
    O --> P[建立狀態記錄 IBMSTS]
    P --> Q[建立歷程記錄 IBMFYM]
    Q --> R[建立地號記錄 IBMCSLAN]
    
    R --> S{違章類型判定}
    S -->|一般建物| T[設定狀態碼 231]
    S -->|廣告物| U[設定狀態碼 241]
    S -->|下水道| V[設定狀態碼 251]
    
    T --> W[掛號通報完成]
    U --> W
    V --> W
    
    W --> X[進入認定審核階段]
```

## 7. 表單欄位配置分析

### 7.1 A頁欄位群組 (基本資料)
**來源**: im10101_man_A.xml

#### 案件基本資訊
```xml
<TextBox name="CASE_ID" caption="案件編號" readonly="True"/>
<DatePicker name="REG_DATE" caption="登記日期" required="True"/>
<ListBox name="REG_UNIT" caption="登記單位">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'UNT'"/>
</ListBox>
```

#### 違建物地址資訊  
```xml
<ListBox name="DIS_B_ADDZON" caption="違建物行政區" required="True">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'ZON'"/>
</ListBox>
<TextBox name="DIS_B_ADDRD" caption="違建物路段" required="True"/>
<TextBox name="DIS_B_ADDLN" caption="違建物巷弄"/>
```

#### 案件來源資訊
```xml
<ListBox name="REP_TYPE" caption="檢舉類型">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'REP'"/>
</ListBox>
<TextBox name="REP_EMP" caption="檢舉人"/>
<DatePicker name="REP_DATE" caption="檢舉日期"/>
```

### 7.2 B頁欄位群組 (詳細資料)
**來源**: im10101_man_B.xml

#### 地籍資料
```xml
<ListBox name="LAN_DIST" caption="地號區域">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'DST'"/>
</ListBox>
<TextBox name="LAN_BNID" caption="地號段別"/>
<TextBox name="LAN_LNID" caption="地號地號"/>
```

#### 違建描述
```xml
<TextArea name="DIS_CONTENT" caption="違建內容" rows="3"/>
<ListBox name="DIS_TYPE" caption="違建類型">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'TYP'"/>
</ListBox>
<TextBox name="DIS_AREA" caption="違建面積"/>
```

### 7.3 C頁欄位群組 (附加資料)
**來源**: im10101_man_C.xml

#### 勘查資訊
```xml
<DatePicker name="CHK_DATE" caption="預定勘查日期"/>
<TextBox name="CHK_EMP" caption="勘查人員"/>
<TextArea name="CHK_NOTE" caption="勘查備註" rows="3"/>
```

#### 處理記錄
```xml
<TextArea name="PROC_NOTE" caption="處理記錄" rows="4"/>
<ListBox name="URGENT_TYPE" caption="緊急程度">
    <Select query="SELECT * FROM ibmcode WHERE code_type = 'URG'"/>
</ListBox>
```

## 8. 業務規則與驗證機制

### 8.1 必填欄位驗證
**配置位置**: XML檔案中的required屬性
```xml
<TextBox name="DIS_B_ADDRD" required="True" verificationRule="NotEmpty"/>
<ListBox name="DIS_B_ADDZON" required="True" verificationRule="NotEmpty"/>
<DatePicker name="REG_DATE" required="True" verificationRule="NotEmpty"/>
```

### 8.2 格式驗證規則
**配置位置**: XML檔案中的verificationRule屬性
```xml
<TextBox name="REP_PHONE" verificationRule="PhoneNumber"/>
<TextBox name="DIS_AREA" verificationRule="Number"/>  
<DatePicker name="REP_DATE" verificationRule="Date"/>
```

### 8.3 業務邏輯驗證

#### 日期邏輯檢查
```java
// 檢舉日期不可晚於登記日期
if (repDate != null && regDate != null) {
    if (repDate.compareTo(regDate) > 0) {
        throw new ValidationException("檢舉日期不可晚於登記日期");
    }
}
```

#### 地址完整性檢查
```java
// 行政區、路段為必填，巷弄可選
if (StringUtils.isEmpty(dis_b_addzon) || StringUtils.isEmpty(dis_b_addrd)) {
    throw new ValidationException("違建物地址資訊不完整");
}
```

## 9. 系統整合與介面

### 9.1 CodeCharge Studio三層架構
```
┌─ 呈現層 ─┐    ┌─ 配置層 ─┐    ┌─ 邏輯層 ─┐
│ .jsp 檔案 │ ←→ │ .xml 檔案 │ ←→ │ Handlers │
│          │    │          │    │ .jsp     │
│ HTML表單  │    │ 欄位配置  │    │ Java程式 │
│ UI組件   │    │ 驗證規則  │    │ SQL操作  │
│ 顯示邏輯  │    │ 資料來源  │    │ 業務邏輯  │
└─────────┘    └─────────┘    └─────────┘
```

### 9.2 資料庫連接架構
```java
// DBConnectionManager 單例模式
public class DBConnectionManager {
    private static DBConnectionManager instance;
    private DataSource dataSource;
    
    // 主要資料庫連接 (PostgreSQL)
    public Connection getConnection(String connName) {
        if ("DBConn".equals(connName)) {
            return getPostgreSQLConnection();
        }
        // 次要資料庫連接 (SQL Server)  
        else if ("DBConn2".equals(connName)) {
            return getSQLServerConnection();
        }
    }
}
```

### 9.3 狀態同步機制
```java
// 狀態更新時的同步邏輯
public void updateCaseStatus(String caseId, String newStatus, String operator) {
    // 1. 更新當前狀態 (IBMSTS)
    updateCurrentStatus(caseId, newStatus);
    
    // 2. 新增歷程記錄 (IBMFYM)  
    insertHistoryRecord(caseId, newStatus, operator);
    
    // 3. 更新案件主檔 (IBMCASE)
    updateCaseMainFile(caseId, newStatus);
    
    // 4. 同步管制檔案狀態
    syncControlFiles(caseId, newStatus);
}
```

## 10. 效能考量與最佳化

### 10.1 查詢最佳化
**清單頁查詢**: im10101_lisHandlers.jsp
```sql
-- 使用索引欄位進行查詢
SELECT * FROM IBMCASE 
WHERE CR_DATE >= ? AND CR_DATE <= ?    -- 日期範圍索引
  AND DIS_B_ADDZON = ?                  -- 行政區索引
  AND STATUS IN ('01', '02', '03')      -- 狀態索引
ORDER BY CASE_ID DESC
LIMIT 100 OFFSET ?
```

### 10.2 重複檢查最佳化
**地址檢查**: im10101_man_checkAddr.jsp
```sql
-- 組合索引最佳化
CREATE INDEX idx_ibmcase_address ON IBMCASE 
(DIS_B_ADDZON, DIS_B_ADDRD, DIS_B_ADDLN);

-- 只檢查必要欄位
SELECT COUNT(*) FROM IBMCASE 
WHERE DIS_B_ADDZON = ? AND DIS_B_ADDRD = ?
  AND (DIS_B_ADDLN = ? OR DIS_B_ADDLN IS NULL)
  AND CASE_ID <> ?
```

### 10.3 交易管理
**案件建立交易**: im10101_man_AHandlers.jsp
```java
Connection conn = null;
try {
    conn = DBTools.getConnection("DBConn");
    conn.setAutoCommit(false);
    
    // 1. 建立案件主檔
    createCaseRecord(conn, caseData);
    
    // 2. 建立狀態記錄  
    createStatusRecord(conn, caseId, "231");
    
    // 3. 建立歷程記錄
    createHistoryRecord(conn, caseId, "231", operator);
    
    conn.commit();
} catch (Exception e) {
    conn.rollback();
    throw e;
} finally {
    conn.setAutoCommit(true);
}
```

## 11. 異常處理與錯誤管控

### 11.1 輸入驗證異常
```java
// 客戶端驗證 (JavaScript)
function validateForm() {
    if (!document.forms[0].DIS_B_ADDZON.value) {
        alert("請選擇違建物行政區");
        return false;
    }
    if (!document.forms[0].DIS_B_ADDRD.value) {
        alert("請輸入違建物路段");  
        return false;
    }
    return true;
}

// 伺服器端驗證 (Java)
if (StringUtils.isEmpty(dis_b_addzon)) {
    throw new ValidationException("違建物行政區為必填欄位");
}
```

### 11.2 資料庫異常處理
```java
try {
    // 執行資料庫操作
    insertCaseRecord(caseData);
} catch (SQLException e) {
    if (e.getErrorCode() == 23505) { // 唯一約束違反
        throw new BusinessException("案件編號重複，請重新產生");
    } else if (e.getErrorCode() == 23503) { // 外鍵約束違反
        throw new BusinessException("參照資料不存在，請檢查輸入值");
    } else {
        logger.error("資料庫操作異常", e);
        throw new SystemException("系統暫時無法服務，請稍後再試");
    }
}
```

### 11.3 並行處理異常
```java
// 案件開啟檢查
String openedBy = checkCaseOpened(caseId);
if (openedBy != null && !openedBy.equals(currentUser)) {
    throw new BusinessException("案件已由 " + openedBy + " 開啟編輯中");
}

// 版本控制檢查
if (!checkCaseVersion(caseId, clientVersion)) {
    throw new BusinessException("案件資料已被其他使用者修改，請重新載入");
}
```

## 12. 安全性考量

### 12.1 權限控制
```java
// 登記權限檢查
if (!hasPermission(currentUser, "CASE_REGISTER")) {
    throw new SecurityException("使用者無案件登記權限");
}

// 單位權限檢查  
if (!belongsToUnit(currentUser, regUnit)) {
    throw new SecurityException("使用者無法登記其他單位案件");
}
```

### 12.2 資料隱私保護
```java
// 敏感資料遮罩
public String maskPersonalData(String data) {
    if (data != null && data.length() > 4) {
        return data.substring(0, 2) + "***" + data.substring(data.length()-2);
    }
    return data;
}

// 檢舉人資料保護
if (!hasPermission(currentUser, "VIEW_REPORTER_INFO")) {
    caseData.setRepEmp(maskPersonalData(caseData.getRepEmp()));
    caseData.setRepPhone(maskPersonalData(caseData.getRepPhone()));
}
```

### 12.3 操作記錄追蹤
```java
// 操作日誌記錄
public void logOperation(String caseId, String operation, String operator, String details) {
    String logSql = "INSERT INTO OPERATION_LOG(CASE_ID, OPERATION, OPERATOR, DETAILS, LOG_TIME)"
                  + " VALUES(?, ?, ?, ?, NOW())";
    DBTools.executeUpdate(logSql, caseId, operation, operator, details);
}

// 關鍵操作記錄
logOperation(caseId, "CASE_REGISTER", currentUser, "新增案件：" + JSON.stringify(caseData));
```

## 總結

### 掛號通報流程特點
1. **三頁式表單設計**: 將複雜的案件資料分散到三個頁面，提升使用者體驗
2. **完整的資料驗證**: 包含客戶端和伺服器端雙重驗證機制
3. **重複檢查機制**: 透過地址和地號檢查避免重複登記
4. **狀態碼管理**: 使用標準化狀態碼系統管理案件生命週期
5. **業務分工整合**: 依據違章類型自動分配給相應科別處理

### 技術架構評估
1. **優點**: 
   - 清楚的三層架構分離
   - 完整的交易管理機制
   - 豐富的驗證規則配置
   
2. **改進空間**:
   - CodeCharge Studio已停止維護
   - 程式碼可讀性和維護性待提升
   - 現代化使用者介面需求

### 後續流程銜接
掛號通報完成後，案件進入認定審核階段：
- 現場勘查流程 (T2.2.1)
- 認定審核流程 (T2.3.1)  
- 認定完成通知流程 (T2.4.1)

---

**文件狀態**: ✅ 已完成
**下一步**: 執行 T2.1.2 查報人員管理分析